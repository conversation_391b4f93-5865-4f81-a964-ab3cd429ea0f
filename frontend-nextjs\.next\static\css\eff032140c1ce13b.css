/*! tailwindcss v4.0.17 | MIT License | https://tailwindcss.com */body{overscroll-behavior:none;background-color:#0000}:root{--font-sans:var(--font-inter);--header-height:calc(var(--spacing)*12 + 1px)}@media (width>=1024px){.theme-scaled{--radius:.6rem;--text-lg:1.05rem;--text-base:.85rem;--text-sm:.8rem;--spacing:.222222rem}}.theme-scaled [data-slot=card]{--spacing:.16rem}.theme-scaled [data-slot=select-trigger],.theme-scaled [data-slot=toggle-group-item]{--spacing:.222222rem}.theme-default,.theme-default-scaled{--primary:var(--color-neutral-600);--primary-foreground:var(--color-neutral-50)}@media (prefers-color-scheme:dark){:is(.theme-default,.theme-default-scaled){--primary:var(--color-neutral-500);--primary-foreground:var(--color-neutral-50)}}.theme-blue,.theme-blue-scaled{--primary:var(--color-blue-600);--primary-foreground:var(--color-blue-50)}@media (prefers-color-scheme:dark){:is(.theme-blue,.theme-blue-scaled){--primary:var(--color-blue-500);--primary-foreground:var(--color-blue-50)}}.theme-green,.theme-green-scaled{--primary:var(--color-lime-600);--primary-foreground:var(--color-lime-50)}@media (prefers-color-scheme:dark){:is(.theme-green,.theme-green-scaled){--primary:var(--color-lime-600);--primary-foreground:var(--color-lime-50)}}.theme-amber,.theme-amber-scaled{--primary:var(--color-amber-600);--primary-foreground:var(--color-amber-50)}@media (prefers-color-scheme:dark){:is(.theme-amber,.theme-amber-scaled){--primary:var(--color-amber-500);--primary-foreground:var(--color-amber-50)}}.theme-mono,.theme-mono-scaled{--font-sans:var(--font-mono);--primary:var(--color-neutral-600);--primary-foreground:var(--color-neutral-50)}@media (prefers-color-scheme:dark){:is(.theme-mono,.theme-mono-scaled){--primary:var(--color-neutral-500);--primary-foreground:var(--color-neutral-50)}}:is(.theme-mono,.theme-mono-scaled) .rounded-lg,:is(.theme-mono,.theme-mono-scaled) .rounded-md,:is(.theme-mono,.theme-mono-scaled) .rounded-sm,:is(.theme-mono,.theme-mono-scaled) .rounded-xl,:is(.theme-mono,.theme-mono-scaled) .rounded-xs{border-radius:0;border-radius:0!important}:is(.theme-mono,.theme-mono-scaled) .shadow-lg,:is(.theme-mono,.theme-mono-scaled) .shadow-md,:is(.theme-mono,.theme-mono-scaled) .shadow-sm,:is(.theme-mono,.theme-mono-scaled) .shadow-xl,:is(.theme-mono,.theme-mono-scaled) .shadow-xs{--tw-shadow:0 0 #0000!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}:is(.theme-mono,.theme-mono-scaled) [data-slot=toggle-group-item],:is(.theme-mono,.theme-mono-scaled) [data-slot=toggle-group]{--tw-shadow:0 0 #0000!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important;border-radius:0!important}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}