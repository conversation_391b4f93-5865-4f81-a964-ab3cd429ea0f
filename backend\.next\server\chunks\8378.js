"use strict";exports.id=8378,exports.ids=[8378],exports.modules={8378:(e,t,r)=>{r.d(t,{CreateTokenCommand:()=>eM,SSOOIDCClient:()=>eh});var o=r(79083),n=r(49720),i=r(48871),s=r(10921),a=r(40229),c=r(35166),d=r(44716),p=r(38353),l=r(55288),u=r(72734),h=r(72587),g=r(10456);let m=async(e,t,r)=>({operation:(0,g.getSmithyContext)(t).operation,region:await (0,g.normalizeProvider)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),S=e=>{let t=[];if("CreateToken"===e.operation)t.push({schemeId:"smithy.api#noAuth"});else t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})});return t},f=e=>Object.assign((0,h.h)(e),{authSchemePreference:(0,g.normalizeProvider)(e.authSchemePreference??[])}),x=e=>Object.assign(e,{useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"}),y={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};var E=r(11603),I=r(82132),v=r(74088),b=r(63551),w=r(99752),P=r(21612),O=r(13214),_=r(45927),C=r(42184),k=r(86473),T=r(49168),D=r(21678),A=r(60052),N=r(50559),$=r(49163);let R="required",j="argv",z="isSet",U="booleanEquals",F="error",G="endpoint",q="tree",V="PartitionResult",H="getAttr",B={[R]:!1,type:"String"},L={[R]:!0,default:!1,type:"Boolean"},M={ref:"Endpoint"},Y={fn:U,[j]:[{ref:"UseFIPS"},!0]},J={fn:U,[j]:[{ref:"UseDualStack"},!0]},K={},W={fn:H,[j]:[{ref:V},"supportsFIPS"]},Q={ref:V},X={fn:U,[j]:[!0,{fn:H,[j]:[Q,"supportsDualStack"]}]},Z=[Y],ee=[J],et=[{ref:"Region"}],er={version:"1.0",parameters:{Region:B,UseDualStack:L,UseFIPS:L,Endpoint:B},rules:[{conditions:[{fn:z,[j]:[M]}],rules:[{conditions:Z,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:F},{conditions:ee,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:F},{endpoint:{url:M,properties:K,headers:K},type:G}],type:q},{conditions:[{fn:z,[j]:et}],rules:[{conditions:[{fn:"aws.partition",[j]:et,assign:V}],rules:[{conditions:[Y,J],rules:[{conditions:[{fn:U,[j]:[!0,W]},X],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:K,headers:K},type:G}],type:q},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:F}],type:q},{conditions:Z,rules:[{conditions:[{fn:U,[j]:[W,!0]}],rules:[{conditions:[{fn:"stringEquals",[j]:[{fn:H,[j]:[Q,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:K,headers:K},type:G},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:K,headers:K},type:G}],type:q},{error:"FIPS is enabled but this partition does not support FIPS",type:F}],type:q},{conditions:ee,rules:[{conditions:[X],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:K,headers:K},type:G}],type:q},{error:"DualStack is enabled but this partition does not support DualStack",type:F}],type:q},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:K,headers:K},type:G}],type:q}],type:q},{error:"Invalid Configuration: Missing Region",type:F}]},eo=new $.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),en=(e,t={})=>eo.get(e,()=>(0,$.resolveEndpoint)(er,{endpointParams:e,logger:t.logger}));$.customEndpointFunctions.aws=N.awsEndpointFunctions;let ei=e=>({apiVersion:"2019-06-10",base64Decoder:e?.base64Decoder??D.fromBase64,base64Encoder:e?.base64Encoder??D.toBase64,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??en,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??S,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new k.f2},{schemeId:"smithy.api#noAuth",identityProvider:e=>e.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new c.NoAuthSigner}],logger:e?.logger??new u.NoOpLogger,serviceId:e?.serviceId??"SSO OIDC",urlParser:e?.urlParser??T.parseUrl,utf8Decoder:e?.utf8Decoder??A.fromUtf8,utf8Encoder:e?.utf8Encoder??A.toUtf8});var es=r(15807);let ea=e=>{(0,u.emitWarningIfUnsupportedVersion)(process.version);let t=(0,es.I)(e),r=()=>t().then(u.loadConfigsForDefaultMode),o=ei(e);(0,I.I)(process.version);let n={profile:e?.profile,logger:o.logger};return{...o,...e,runtime:"node",defaultsMode:t,authSchemePreference:e?.authSchemePreference??(0,P.loadConfig)(v.$,n),bodyLengthChecker:e?.bodyLengthChecker??_.n,defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,b.pf)({serviceId:o.serviceId,clientVersion:E.rE}),maxAttempts:e?.maxAttempts??(0,P.loadConfig)(l.qs,e),region:e?.region??(0,P.loadConfig)(a.NODE_REGION_CONFIG_OPTIONS,{...a.NODE_REGION_CONFIG_FILE_OPTIONS,...n}),requestHandler:O.NodeHttpHandler.create(e?.requestHandler??r),retryMode:e?.retryMode??(0,P.loadConfig)({...l.kN,default:async()=>(await r()).retryMode||C.DEFAULT_RETRY_MODE},e),sha256:e?.sha256??w.V.bind(null,"sha256"),streamCollector:e?.streamCollector??O.streamCollector,useDualstackEndpoint:e?.useDualstackEndpoint??(0,P.loadConfig)(a.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,n),useFipsEndpoint:e?.useFipsEndpoint??(0,P.loadConfig)(a.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,n),userAgentAppId:e?.userAgentAppId??(0,P.loadConfig)(b.hV,n)}};var ec=r(30208),ed=r(96126);let ep=e=>{let t=e.httpAuthSchemes,r=e.httpAuthSchemeProvider,o=e.credentials;return{setHttpAuthScheme(e){let r=t.findIndex(t=>t.schemeId===e.schemeId);-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){o=e},credentials:()=>o}},el=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),eu=(e,t)=>{let r=Object.assign((0,ec.Rq)(e),(0,u.getDefaultExtensionConfiguration)(e),(0,ed.getHttpHandlerExtensionConfiguration)(e),ep(e));return t.forEach(e=>e.configure(r)),Object.assign(e,(0,ec.$3)(r),(0,u.resolveDefaultRuntimeConfig)(r),(0,ed.resolveHttpHandlerRuntimeConfig)(r),el(r))};class eh extends u.Client{config;constructor(...[e]){let t=ea(e||{});super(t),this.initConfig=t;let r=x(t),u=(0,s.resolveUserAgentConfig)(r),h=(0,l.$z)(u),g=(0,a.resolveRegionConfig)(h),S=(0,o.OV)(g),y=eu(f((0,p.Co)(S)),e?.extensions||[]);this.config=y,this.middlewareStack.use((0,s.getUserAgentPlugin)(this.config)),this.middlewareStack.use((0,l.ey)(this.config)),this.middlewareStack.use((0,d.vK)(this.config)),this.middlewareStack.use((0,o.TC)(this.config)),this.middlewareStack.use((0,n.Y7)(this.config)),this.middlewareStack.use((0,i.n4)(this.config)),this.middlewareStack.use((0,c.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config,{httpAuthSchemeParametersProvider:m,identityProviderConfigProvider:async e=>new c.DefaultIdentityProviderConfig({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,c.getHttpSigningPlugin)(this.config))}destroy(){super.destroy()}}var eg=r(17972);class em extends u.ServiceException{constructor(e){super(e),Object.setPrototypeOf(this,em.prototype)}}class eS extends em{name="AccessDeniedException";$fault="client";error;error_description;constructor(e){super({name:"AccessDeniedException",$fault:"client",...e}),Object.setPrototypeOf(this,eS.prototype),this.error=e.error,this.error_description=e.error_description}}class ef extends em{name="AuthorizationPendingException";$fault="client";error;error_description;constructor(e){super({name:"AuthorizationPendingException",$fault:"client",...e}),Object.setPrototypeOf(this,ef.prototype),this.error=e.error,this.error_description=e.error_description}}let ex=e=>({...e,...e.clientSecret&&{clientSecret:u.SENSITIVE_STRING},...e.refreshToken&&{refreshToken:u.SENSITIVE_STRING},...e.codeVerifier&&{codeVerifier:u.SENSITIVE_STRING}}),ey=e=>({...e,...e.accessToken&&{accessToken:u.SENSITIVE_STRING},...e.refreshToken&&{refreshToken:u.SENSITIVE_STRING},...e.idToken&&{idToken:u.SENSITIVE_STRING}});class eE extends em{name="ExpiredTokenException";$fault="client";error;error_description;constructor(e){super({name:"ExpiredTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,eE.prototype),this.error=e.error,this.error_description=e.error_description}}class eI extends em{name="InternalServerException";$fault="server";error;error_description;constructor(e){super({name:"InternalServerException",$fault:"server",...e}),Object.setPrototypeOf(this,eI.prototype),this.error=e.error,this.error_description=e.error_description}}class ev extends em{name="InvalidClientException";$fault="client";error;error_description;constructor(e){super({name:"InvalidClientException",$fault:"client",...e}),Object.setPrototypeOf(this,ev.prototype),this.error=e.error,this.error_description=e.error_description}}class eb extends em{name="InvalidGrantException";$fault="client";error;error_description;constructor(e){super({name:"InvalidGrantException",$fault:"client",...e}),Object.setPrototypeOf(this,eb.prototype),this.error=e.error,this.error_description=e.error_description}}class ew extends em{name="InvalidRequestException";$fault="client";error;error_description;constructor(e){super({name:"InvalidRequestException",$fault:"client",...e}),Object.setPrototypeOf(this,ew.prototype),this.error=e.error,this.error_description=e.error_description}}class eP extends em{name="InvalidScopeException";$fault="client";error;error_description;constructor(e){super({name:"InvalidScopeException",$fault:"client",...e}),Object.setPrototypeOf(this,eP.prototype),this.error=e.error,this.error_description=e.error_description}}class eO extends em{name="SlowDownException";$fault="client";error;error_description;constructor(e){super({name:"SlowDownException",$fault:"client",...e}),Object.setPrototypeOf(this,eO.prototype),this.error=e.error,this.error_description=e.error_description}}class e_ extends em{name="UnauthorizedClientException";$fault="client";error;error_description;constructor(e){super({name:"UnauthorizedClientException",$fault:"client",...e}),Object.setPrototypeOf(this,e_.prototype),this.error=e.error,this.error_description=e.error_description}}class eC extends em{name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(e){super({name:"UnsupportedGrantTypeException",$fault:"client",...e}),Object.setPrototypeOf(this,eC.prototype),this.error=e.error,this.error_description=e.error_description}}var ek=r(90841);let eT=async(e,t)=>{let r,o=(0,c.requestBuilder)(e,t);return o.bp("/token"),r=JSON.stringify((0,u.take)(e,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:e=>(0,u._json)(e)})),o.m("POST").h({"content-type":"application/json"}).b(r),o.build()},eD=async(e,t)=>{if(200!==e.statusCode&&e.statusCode>=300)return eA(e,t);let r=(0,u.map)({$metadata:eL(e)}),o=(0,u.expectNonNull)((0,u.expectObject)(await (0,ek.Y2)(e.body,t)),"body");return Object.assign(r,(0,u.take)(o,{accessToken:u.expectString,expiresIn:u.expectInt32,idToken:u.expectString,refreshToken:u.expectString,tokenType:u.expectString})),r},eA=async(e,t)=>{let r={...e,body:await (0,ek.CG)(e.body,t)},o=(0,ek.cJ)(e,r.body);switch(o){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await e$(r,t);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await eR(r,t);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await ej(r,t);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await ez(r,t);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await eU(r,t);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await eF(r,t);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await eG(r,t);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await eq(r,t);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await eV(r,t);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await eH(r,t);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await eB(r,t);default:return eN({output:e,parsedBody:r.body,errorCode:o})}},eN=(0,u.withBaseException)(em),e$=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new eS({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},eR=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new ef({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},ej=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new eE({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},ez=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new eI({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},eU=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new ev({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},eF=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new eb({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},eG=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new ew({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},eq=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new eP({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},eV=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new eO({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},eH=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new e_({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},eB=async(e,t)=>{let r=(0,u.map)({}),o=e.body;Object.assign(r,(0,u.take)(o,{error:u.expectString,error_description:u.expectString}));let n=new eC({$metadata:eL(e),...r});return(0,u.decorateServiceException)(n,e.body)},eL=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]});class eM extends u.Command.classBuilder().ep(y).m(function(e,t,r,o){return[(0,eg.getSerdePlugin)(r,this.serialize,this.deserialize),(0,p.rD)(r,e.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(ex,ey).ser(eT).de(eD).build(){}class eY extends eh{}(0,u.createAggregatedClient)({CreateTokenCommand:eM},eY)},11603:e=>{e.exports={rE:"3.840.0"}}};