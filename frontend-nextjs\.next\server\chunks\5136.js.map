{"version": 3, "file": "5136.js", "mappings": "0jBACO,MAAyC,2BAAqB,8CAA+C,YAAU,QAAa,kBAAgB,wBCApJ,EAA6C,2BAAqB,8CAA+C,YAAU,QAAa,kBAAgB,4FEAxJ,MAA+C,2BAAqB,8CAA+C,YAAU,QAAa,kBAAgB", "sources": ["webpack://next-shadcn-dashboard-starter/?6a5e", "webpack://next-shadcn-dashboard-starter/?3213", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js?e053", "webpack://next-shadcn-dashboard-starter/?f441"], "sourcesContent": ["import { createServerReference, callServer, findSourceMapURL } from 'private-next-rsc-action-client-wrapper'\nexport const deleteKeylessAction = /*#__PURE__*/createServerReference(\"7f7b45347fd50452ee6e2850ded1018991a7b086f0\", callServer, undefined, findSourceMapURL, \"deleteKeylessAction\")", "import { createServerReference, callServer, findSourceMapURL } from 'private-next-rsc-action-client-wrapper'\nexport const syncKeylessConfigAction = /*#__PURE__*/createServerReference(\"7f909588461cb83e855875f4939d6f26e4ae81b49e\", callServer, undefined, findSourceMapURL, \"syncKeylessConfigAction\")", "export { deleteKeylessAction } from 'next-flight-server-reference-proxy-loader?id=7f7b45347fd50452ee6e2850ded1018991a7b086f0&name=deleteKeylessAction!'\nexport { syncKeylessConfigAction } from 'next-flight-server-reference-proxy-loader?id=7f909588461cb83e855875f4939d6f26e4ae81b49e&name=syncKeylessConfigAction!'\nexport { createOrReadKeylessAction } from 'next-flight-server-reference-proxy-loader?id=7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261&name=createOrReadKeylessAction!'", "import { createServerReference, callServer, findSourceMapURL } from 'private-next-rsc-action-client-wrapper'\nexport const createOrReadKeylessAction = /*#__PURE__*/createServerReference(\"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261\", callServer, undefined, findSourceMapURL, \"createOrReadKeylessAction\")"], "names": [], "sourceRoot": ""}