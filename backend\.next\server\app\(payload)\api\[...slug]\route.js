(()=>{var e={};e.id=7951,e.ids=[7951],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1065:()=>{},1708:e=>{"use strict";e.exports=require("node:process")},2712:()=>{},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},32785:e=>{"use strict";e.exports=import("prettier")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},35672:e=>{"use strict";e.exports=require("dns/promises")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},37830:e=>{"use strict";e.exports=require("node:stream/web")},38522:e=>{"use strict";e.exports=require("node:zlib")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57669:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{DELETE:()=>n,GET:()=>p,OPTIONS:()=>a,PATCH:()=>x,POST:()=>c,PUT:()=>d});var i=t(68117);t(1065);var u=t(41862),o=e([i]);i=(o.then?(await o)():o)[0];let p=(0,u.fG)(i.A),c=(0,u.LO)(i.A),n=(0,u.SJ)(i.A),x=(0,u.D9)(i.A),d=(0,u.Uc)(i.A),a=(0,u.lw)(i.A);s()}catch(e){s(e)}})},57975:e=>{"use strict";e.exports=require("node:util")},59526:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>n,routeModule:()=>x,serverHooks:()=>q,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>a});var i=t(70293),u=t(32498),o=t(83889),p=t(57669),c=e([p]);p=(c.then?(await c)():c)[0];let x=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/(payload)/api/[...slug]/route",pathname:"/api/[...slug]",filename:"route",bundlePath:"app/(payload)/api/[...slug]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\[...slug]\\route.ts",nextConfigOutput:"",userland:p}),{workAsyncStorage:d,workUnitAsyncStorage:a,serverHooks:q}=x;function n(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:a})}s()}catch(e){s(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83725:e=>{"use strict";e.exports=import("next/dist/compiled/@vercel/og/index.node.js")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96088:()=>{},98995:e=>{"use strict";e.exports=require("node:module")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[3889,2481,7852,8170,8754],()=>t(59526));module.exports=s})();