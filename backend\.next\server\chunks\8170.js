"use strict";exports.id=8170,exports.ids=[8170],exports.modules={41862:(e,t,a)=>{a.d(t,{SJ:()=>I,fG:()=>O,lw:()=>k,D9:()=>H,LO:()=>S,Uc:()=>$});var r=a(98607),s=a(42626),o=a(54546),n=a(94334),i=a(43675),l=a(36720),d=a(10724),c=a(86933),p=a(30713);let h=async({collection:e,config:t,err:a,req:s})=>{if("payloadInitError"in a&&!0===a.payloadInitError)return console.error(a),Response.json({message:"There was an error initializing Payload"},{status:r.h.INTERNAL_SERVER_ERROR});let o=s&&"payload"in s&&s?.payload;if(!o)try{o=await (0,d.nm0)({config:t})}catch(e){return Response.json({message:"There was an error initializing Payload"},{status:r.h.INTERNAL_SERVER_ERROR})}let h=(0,c.v)(a),u=a.status||r.h.INTERNAL_SERVER_ERROR;(0,p.v)({err:a,payload:o}),s.payload=o;let g=(0,n.y)({headers:new Headers,req:s}),{config:f}=o;return f.debug||a.isPublic||u!==r.h.INTERNAL_SERVER_ERROR||(h=(0,c.v)(new l.L("Something went wrong."))),f.debug&&!0===f.debug&&(h.stack=a.stack),e&&await e.config.hooks.afterError?.reduce(async(t,r)=>{await t;let o=await r({collection:e.config,context:s.context,error:a,req:s,result:h});o&&(h=o.response||h,u=o.status||u)},Promise.resolve()),await f.hooks.afterError?.reduce(async(t,r)=>{await t;let o=await r({collection:e?.config,context:s.context,error:a,req:s,result:h});o&&(h=o.response||h,u=o.status||u)},Promise.resolve()),Response.json(h,{headers:s.responseHeaders?(0,i.l)(s.responseHeaders,g):g,status:u})},u=(e,t)=>Response.json({message:`Route not found "${t??new URL(e.url).pathname}"`},{headers:(0,n.y)({headers:new Headers,req:e}),status:r.h.NOT_FOUND}),g=async({basePath:e="",config:t,path:a,request:l})=>{let d,c,p;if("post"===l.method.toLowerCase()&&("GET"===l.headers.get("X-Payload-HTTP-Method-Override")||"GET"===l.headers.get("X-HTTP-Method-Override"))){let r=await l.text(),s=`${l.url}?${new URLSearchParams(r).toString()}`;return await g({basePath:e,config:t,path:a,request:new Request(s,{cache:l.cache,credentials:l.credentials,headers:l.headers,method:"GET",signal:l.signal})})}try{let h;if(c=await (0,o.o)({canSetHeaders:!0,config:t,request:l}),c.method?.toLowerCase()==="options")return Response.json({},{headers:(0,n.y)({headers:new Headers,req:c}),status:200});let{payload:g}=c,{config:f}=g,m=`${e}${a??new URL(c.url).pathname}`;if(!m.startsWith(f.routes.api))return u(c,m);let y=m.replace(f.routes.api,""),w=!1;y.startsWith("/globals")&&(w=!0,y=y.replace("/globals",""));let R=y.split("/");R.shift();let x=R[0];x&&(w?h=g.globals.config.find(e=>e.slug===x):g.collections[x]&&(p=g.collections[x]));let b=f.endpoints;if(p?(b=p.config.endpoints,y=y.replace(`/${p.config.slug}`,"")):h&&(y=y.replace(`/${h.slug}`,""),b=h.endpoints),""===y&&(y="/"),!1===b)return Response.json({message:`Cannot ${c.method?.toUpperCase()} ${c.url}`},{headers:(0,n.y)({headers:new Headers,req:c}),status:r.h.NOT_IMPLEMENTED});let E=b?.find(e=>{if(e.method!==c.method?.toLowerCase())return!1;let t=(0,s.YW)(e.path,{decode:decodeURIComponent})(y);return!!t&&(c.routeParams=t.params,p?c.routeParams.collection=p.config.slug:h&&(c.routeParams.global=h.slug),!0)});if(E&&(d=E.handler),!d)return u(c,m);let L=await d(c);return new Response(L.body,{headers:(0,n.y)({headers:(0,i.l)(c.responseHeaders??new Headers,L.headers),req:c}),status:L.status,statusText:L.statusText})}catch(e){return h({collection:p,config:t,err:e,req:c})}};var f=a(90099),m=a(54642),y=a(79748),w=a(62894),R=a(33873);a(98822);var x=a(79551),b=a(35418);let E=({description:e,Fallback:t,fontFamily:a="Arial, sans-serif",Icon:r,importMap:s,leader:o,title:n})=>{let i=(0,b.f)({clientProps:{fill:"white"},Component:r,Fallback:t,importMap:s});return(0,f.jsxs)("div",{style:{backgroundColor:"#000",color:"#fff",display:"flex",flexDirection:"column",fontFamily:a,height:"100%",justifyContent:"space-between",padding:"100px",width:"100%"},children:[(0,f.jsxs)("div",{style:{display:"flex",flexDirection:"column",flexGrow:1,fontSize:50,height:"100%"},children:[o&&(0,f.jsx)("div",{style:{fontSize:30,marginBottom:10},children:o}),(0,f.jsx)("p",{style:{display:"-webkit-box",fontSize:90,lineHeight:1,marginBottom:0,marginTop:0,textOverflow:"ellipsis",WebkitBoxOrient:"vertical",WebkitLineClamp:2},children:n}),e&&(0,f.jsx)("p",{style:{display:"-webkit-box",flexGrow:1,fontSize:30,lineHeight:1,marginBottom:0,marginTop:40,textOverflow:"ellipsis",WebkitBoxOrient:"vertical",WebkitLineClamp:2},children:e})]}),(0,f.jsx)("div",{style:{alignItems:"flex-end",display:"flex",flexShrink:0,height:"38px",justifyContent:"center",width:"38px"},children:i})]})},L=(0,x.fileURLToPath)("file:///C:/Users/<USER>/Desktop/nord-coast/backend/node_modules/.pnpm/@payloadcms+next@3.46.0_@ty_1db65b0c578414e077f30ed6b7873f19/node_modules/@payloadcms/next/dist/routes/rest/og/index.js"),T=R.dirname(L),v=async e=>{let t=e.payload.config;if("off"===t.admin.meta.defaultOGImageType)return Response.json({error:"Open Graph images are disabled"},{status:400});try{let a,{searchParams:r}=new URL(e.url),s=r.has("title")?r.get("title")?.slice(0,100):"",o=r.has("leader")?r.get("leader")?.slice(0,100).replace("-"," "):"",n=r.has("description")?r.get("description"):"";try{a=y.readFile(R.join(T,"roboto-regular.woff"))}catch(t){e.payload.logger.error(`Error reading font file or not readable: ${t.message}`)}return new w.f((0,f.jsx)(E,{description:n,Fallback:m.Ou,fontFamily:"Roboto, sans-serif",Icon:t.admin?.components?.graphics?.Icon,importMap:e.payload.importMap,leader:o,title:s}),{...a?{fonts:[{name:"Roboto",data:await a,style:"normal",weight:400}]}:{},height:630,width:1200})}catch(t){return e.payload.logger.error(`Error generating Open Graph image: ${t.message}`),Response.json({error:`Internal Server Error: ${t.message}`},{status:500})}},j=!1,P=e=>async(t,a)=>{let r=await e;!1!==j||r.endpoints.some(e=>"/og"===e.path&&"get"===e.method)||r.endpoints.push({handler:v,method:"get",path:"/og"}),j=!0;let s=await a.params;return await g({config:e,path:s?`${r.routes.api}/${s.slug.join("/")}`:void 0,request:t})},k=P,O=P,S=P,I=P,H=P,$=P},54546:(e,t,a)=>{a.d(t,{o:()=>p});var r=a(22373),s=a(90990),o=a(26441),n=a(52345),i=a(10724),l=a(76657),d=a(34922),c=a(38263);let p=async({canSetHeaders:e,config:t,params:a,request:p})=>{let h=(0,c.J)(p.headers),u=await (0,i.nm0)({config:t}),{config:g}=u,f=g.localization,m=new URL(p.url),{pathname:y,searchParams:w}=m,R=!g.graphQL.disable&&y===`${g.routes.api}${g.routes.graphQL}`,x=(0,d.Y)({config:g,cookies:h,headers:p.headers}),b=await (0,r.L)({config:g.i18n,context:"api",language:x}),E=w.get("fallback-locale")||w.get("fallbackLocale"),L=w.get("locale"),T=E,{search:v}=m,j=v?s.q(v,{arrayLimit:1e3,depth:10,ignoreQueryPrefix:!0}):{};if(f){let e=(0,l.T)({fallbackLocale:T,locale:L,localization:f});T=e.fallbackLocale,L=e.locale}let P=Object.assign(p,{context:{},fallbackLocale:T,hash:m.hash,host:m.host,href:m.href,i18n:b,locale:L,origin:m.origin,pathname:m.pathname,payload:u,payloadAPI:R?"GraphQL":"REST",payloadDataLoader:void 0,payloadUploadSizes:{},port:m.port,protocol:m.protocol,query:j,routeParams:a||{},search:m.search,searchParams:m.searchParams,t:b.t,transactionID:void 0,user:null});P.payloadDataLoader=(0,n.Y)(P);let{responseHeaders:k,user:O}=await (0,o.F)({canSetHeaders:e,headers:P.headers,isGraphQL:R,payload:u});return P.user=O,k&&(P.responseHeaders=k),P}},62894:(e,t,a)=>{Object.defineProperty(t,"f",{enumerable:!0,get:function(){return r}});class r extends Response{static #e=this.displayName="ImageResponse";constructor(...e){let t=new ReadableStream({async start(t){let r=new(await Promise.resolve().then(a.bind(a,83725))).ImageResponse(...e);if(!r.body)return t.close();let s=r.body.getReader();for(;;){let{done:e,value:a}=await s.read();if(e)return t.close();t.enqueue(a)}}}),r=e[1]||{},s=new Headers({"content-type":"image/png","cache-control":"public, immutable, no-transform, max-age=31536000"});r.headers&&new Headers(r.headers).forEach((e,t)=>s.set(t,e)),super(t,{headers:s,status:r.status,statusText:r.statusText})}}},70293:(e,t,a)=>{e.exports=a(44870)}};