(()=>{var e={};e.id=639,e.ids=[639],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1317:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{w:()=>n,x:()=>c});var i=r(10724),o=r(68117),u=r(32481),a=e([o]);async function n(e){try{let t=e.headers.get("x-clerk-user-id"),r=e.headers.get("x-user-email");if(!t||!r)return console.log("Missing Clerk user headers"),null;let s=await (0,i.nm0)({config:o.A}),a={id:t,email:r};console.log("Syncing Clerk user with Payload:",a);let n=await (0,u.kw)(s,a);return console.log("Synced Payload user:",n),{payload:s,user:n}}catch(e){return console.error("Payload authentication error:",e),null}}async function c(e,t,r,s={}){let{payload:i,user:o}=e;switch(r){case"find":return await i.find({collection:t,user:o,...s});case"findByID":return await i.findByID({collection:t,user:o,...s});case"create":return await i.create({collection:t,user:o,...s});case"update":return await i.update({collection:t,user:o,...s});case"delete":return await i.delete({collection:t,user:o,...s});default:throw Error(`Unsupported operation: ${r}`)}}o=(a.then?(await a)():a)[0],s()}catch(e){s(e)}})},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},17638:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var i=r(70293),o=r(32498),u=r(83889),a=r(55837),n=e([a]);a=(n.then?(await n)():n)[0];let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/reports/financial/route",pathname:"/api/reports/financial",filename:"route",bundlePath:"app/api/reports/financial/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\financial\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:x}=d;function c(){return(0,u.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},32481:(e,t,r)=>{"use strict";async function s(e,t,r="front-desk"){try{let s=await e.find({collection:"users",where:{clerkId:{equals:t.id}},limit:1});if(!(s.docs.length>0))return await e.create({collection:"users",data:{role:r,clerkId:t.id}});{let t=s.docs[0];return await e.update({collection:"users",id:t.id,data:{}})}}catch(e){throw console.error("Error syncing Clerk user with Payload:",e),Error("Failed to sync user authentication")}}r.d(t,{kw:()=>s})},32785:e=>{"use strict";e.exports=import("prettier")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},34869:()=>{},35672:e=>{"use strict";e.exports=require("dns/promises")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},37830:e=>{"use strict";e.exports=require("node:stream/web")},38522:e=>{"use strict";e.exports=require("node:zlib")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55837:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>a});var i=r(27492),o=r(1317),u=e([o]);async function a(e){try{let t=await (0,o.w)(e);if(!t)return i.NextResponse.json({error:"Authentication required"},{status:401});if(!["admin","front-desk"].includes(t.user.role))return i.NextResponse.json({error:"Insufficient permissions to view financial reports"},{status:403});let r=new URL(e.url),s=r.searchParams.get("startDate"),u=r.searchParams.get("endDate"),a=r.searchParams.get("type")||"summary",n=new Date,c=new Date;c.setDate(c.getDate()-30);let d={createdAt:{greater_than_equal:s||c.toISOString(),less_than_equal:u||n.toISOString()}},p=await (0,o.x)(t,"bills","find",{where:d,limit:1e3,depth:1}),l=await (0,o.x)(t,"payments","find",{where:{...d,paymentStatus:{equals:"completed"}},limit:1e3,depth:1}),x=await (0,o.x)(t,"deposits","find",{where:d,limit:1e3,depth:1}),m=p.docs.reduce((e,t)=>e+t.totalAmount,0),q=p.docs.reduce((e,t)=>e+(t.paidAmount||0),0),h=p.docs.reduce((e,t)=>e+(t.remainingAmount||0),0),y=l.docs.reduce((e,t)=>e+t.amount,0),f=x.docs.reduce((e,t)=>e+t.amount,0),w=x.docs.reduce((e,t)=>e+(t.usedAmount||0),0),g=x.docs.reduce((e,t)=>e+(t.remainingAmount||0),0),k=l.docs.reduce((e,t)=>{let r=t.paymentMethod;return e[r]=(e[r]||0)+t.amount,e},{}),A=p.docs.reduce((e,t)=>{let r=t.status;return e[r]=(e[r]||0)+1,e},{}),v=p.docs.reduce((e,t)=>{let r=t.billType;return e[r]=(e[r]||0)+t.totalAmount,e},{}),D={period:{startDate:s||c.toISOString(),endDate:u||n.toISOString()},summary:{bills:{count:p.totalDocs,totalAmount:m,paidAmount:q,remainingAmount:h,collectionRate:m>0?(q/m*100).toFixed(2):0},payments:{count:l.totalDocs,totalAmount:y,averagePayment:l.totalDocs>0?(y/l.totalDocs).toFixed(2):0},deposits:{count:x.totalDocs,totalAmount:f,usedAmount:w,remainingAmount:g,utilizationRate:f>0?(w/f*100).toFixed(2):0}},breakdowns:{paymentMethods:k,billStatuses:A,billTypes:v},generatedAt:new Date().toISOString(),generatedBy:{firstName:t.user.firstName,lastName:t.user.lastName,email:t.user.email}};return"detailed"===a&&(D.details={bills:p.docs,payments:l.docs,deposits:x.docs}),i.NextResponse.json({success:!0,report:D})}catch(e){return console.error("Error generating financial report:",e),i.NextResponse.json({error:"Failed to generate financial report"},{status:500})}}o=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},93077:()=>{},94735:e=>{"use strict";e.exports=require("events")},98995:e=>{"use strict";e.exports=require("node:module")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[3889,2481,9556,8754],()=>r(17638));module.exports=s})();