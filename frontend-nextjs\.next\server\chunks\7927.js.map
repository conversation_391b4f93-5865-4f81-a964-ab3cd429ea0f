{"version": 3, "file": "7927.js", "mappings": "icACA,qCAA6C,CAC7C,QACA,CAAC,EAAC,OACF,+DAA8E,CAC9E,cACA,eACA,QACA,CACA,CAAC,EAAC,IACF,WASA,KACA,mBACA,SAEA,sDACA,OACA,SACA,EAEA,WACA,eACA,gBAEA,OACA,cACA,EACA,yDACA,eACA,6DACA,iDACA,mBACA,6BAEA,UAQA,OAJA,YACA,GACA,WAEA,CACA,EAzCuD,EAAQ,KAAO,GACtE,SAD8D,EAC9D,GACA,0CACA,kBACA,cACA,qBACA,YACA,EAAK,GACL,CAkCA,OACA,YACA,EAEA,0CAIA,EAAoE,aAWpE,EAXuB,MAA6B,CAWpD,EAXuD,CAAa,EAYpE,sBAkBA,EAjBA,QAmBA,CACA,CA9BA,EACA,IACA,IACA,YACA,EAAM,OACN,cACA,CACA,CAAC,mBClED,4BAA0C,CAC1C,cACA,eACA,QACA,CACA,CAAC,EAAC,IACF,EAAiB,EAAQ,KAAwC,EACjE,EAAkC,EAAQ,KAA2C,CAD7D,CAExB,EAAsC,EAAQ,KAAgD,CADrD,CAEzC,EAA0B,EAAQ,KAAiC,CADtB,CAE7C,EAAiC,EAAQ,KAAmD,CAD3D,CAEjC,EAA+B,EAAQ,KAA4B,CAD3B,CAExC,EAAkD,EAAQ,KAAmD,CADvE,CAGtC,GADmB,EAAQ,KAAqB,EACjC,EAAQ,KAAS,GAChC,SADsB,IAEtB,oCACA,oCACA,MACA,kEACA,2CAA2D,SAAiB,gQAC5E,aACA,cACA,eACA,CAAa,EAEb,iBAIA,SADA,oCAAiF,IAGjF,KACA,qBACA,2CAA+D,SAAiB,iWAChF,aACA,cACA,eACA,CAAiB,OACH,6BACd,2CAA+D,SAAiB,0YAChF,aACA,cACA,eACA,CAAiB,CACjB,CAEA,wBACA,iEAAoG,SAAiB,wOACrH,aACA,cACA,eACA,CAAa,EAEb,KACA,wBAIA,KA2BA,EA3BA,QA2BA,EA3BA,EA4BA,eACA,KACA,SAEA,6DAiFA,OAhFA,WACA,2BACA,QACA,iBACA,4BAAyD,gBAA8B,UACvF,SACA,0DACA,CACA,CAAS,CACT,QACA,iBACA,4BAAyD,gBAA8B,KACvF,SACA,0DACA,CACA,CAAS,CACT,KACA,iBACA,yBAAsD,gBAA8B,KACpF,SACA,0DACA,CACA,CAAS,CACT,KACA,iBACA,yBAAsD,gBAA8B,KACpF,SACA,0DACA,CACA,CAAS,CACT,KACA,iBACA,yBAAsD,gBAA8B,UACpF,SACA,0DACA,CACA,CAAS,CACT,cACA,iBACA,mCACA,SACA,0DACA,CACA,CAAS,CACT,SACA,iBACA,iCACA,SACA,0DACA,CACA,CAAS,CACT,MACA,iBACA,2BACA,SACA,0DACA,CACA,CAAS,CACT,QACA,iBACA,6BACA,SACA,0DACA,CACA,CAAS,CACT,SACA,iBACA,8BACA,SACA,0DACA,CACA,CAAS,CACT,mBACA,iBACA,uCACA,SACA,0DACA,CACA,CACA,CAAK,EACL,CAjHA,KACc,yBAKd,gEACc,6BAKd,sDAKA,0CACA,CAKA,SAJA,yCAIA,QAEA,CACA,kBAyFA,cACA,eACA,KACA,SAEA,yBAqCA,OApCA,WACA,2BACA,QACA,sBACA,CAAS,CACT,QACA,sBACA,CAAS,CACT,KACA,mBACA,CAAS,CACT,KACA,mBACA,CAAS,CACT,KACA,mBACA,CAAS,CACT,cACA,4BACA,CAAS,CACT,SACA,uBACA,CAAS,CACT,MACA,oBACA,CAAS,CACT,QACA,sBACA,CAAS,CACT,SACA,uBACA,CAAS,CACT,mBACA,gCACA,CACA,CAAK,EACL,CACA,CAyFA,cACA,6BAAyC,EAAI,QAC7C,CAYA,2DACA,gBACA,kBAAqC,EAAM,kBAC3C,sCAA8C,EAAO,OAAO,EAAW,gJACvE,aACA,cACA,eACA,CAAK,CACL,mBCxUA,4BAA0C,CAC1C,cACA,eACA,QACA,CACA,CAAC,EAAC,IACF,EAAwB,EAAQ,KAAgD,EAChF,EAAiB,EAAQ,KAA+B,CADzB,CAE/B,EAAkC,EAAQ,KAA2C,CAD7D,CAExB,EAAsC,EAAQ,KAAgD,CADrD,CAEzC,EAA0B,EAAQ,KAAiC,CADtB,CAE7C,EAAiC,EAAQ,KAAmD,CAD3D,CAEjC,EAA+B,EAAQ,KAA4B,CAD3B,CAExC,EAAkD,EAAQ,KAAmD,CADvE,CAGtC,GADmB,EAAQ,KAD8B,EAE1C,EAAQ,KAAS,GADN,SACJ,IAEtB,gBACA,gCACA,oCACA,MACA,kEACA,kCACA,SAAqB,SAAiB,gQACtC,YACA,cACA,eACA,CAAa,EAEb,iBAIA,SA+DA,gEAAgG,KA7DhG,KACA,qBACA,2CAA+D,SAAiB,iWAChF,aACA,cACA,eACA,CAAiB,OACH,6BACd,2CAA+D,SAAiB,0YAChF,aACA,cACA,eACA,CAAiB,CACjB,CAEA,wBACA,iEAAoG,SAAiB,wOACrH,aACA,cACA,eACA,CAAa,EAEb,KACA,wBAIA,KAqCA,EArCA,QAqCA,EArCA,EAsCA,eACA,KACA,SAEA,6DAmGA,OAlGA,WACA,2BACA,mBACA,iBACA,uCACA,SACA,0DACA,CACA,CAAS,CACT,MACA,MACA,yBACA,SACA,0DACA,CACA,CAAS,CACT,KACA,qBACA,EAEA,EADA,oBACA,oBAEA,mBAAoD,gBAA8B,KAElF,aACA,0DACA,CACA,CAAS,CACT,QACA,qBACA,EAEA,EADA,oBACA,uBAEA,sBAAuD,gBAA8B,KAErF,aACA,0DACA,CACA,CAAS,CACT,KACA,qBACA,EAEA,EADA,oBACA,oBAEA,mBAAoD,gBAA8B,KAElF,aACA,0DACA,CACA,CAAS,CACT,KACA,qBACA,EACA,uBACA,0BACkB,CAClB,mBAEA,EADA,EACA,mBAAwD,KAAqB,UAE7E,sBAEA,CACA,aACA,0DACA,CACA,CAAS,CACT,QACA,qBACA,EAEA,EADA,oBACA,uBACkB,oBAClB,sBAAuD,gBAA8B,KAErF,sBAAuD,gBAA8B,UAErF,aACA,0DACA,CACA,CAAS,CACT,OACA,iBACA,4BACA,SACA,0DACA,CACA,CAAS,CACT,UACA,iBACA,+BACA,SACA,0DACA,CACA,CACA,CAAK,EACL,CA7IA,KACc,yBAId,wDACc,6BAId,8CAKA,0CACA,CAEA,8CAYA,EAVA,yCAGA,0BAEA,UAOA,CAIA,kBA2GA,cACA,eACA,KACA,SAEA,yBA0CA,OAzCA,WACA,2BACA,mBACA,oDAIA,SACA,CAAS,CACT,MACA,QACA,OAES,CACT,KACA,mBACA,CAAS,CACT,QACA,sBACA,CAAS,CACT,KACA,mBACA,CAAS,CACT,KACA,mBACA,CAAS,CACT,QACA,sBACA,CAAS,CACT,OACA,MACA,2CAIA,WACA,CAAS,CACT,UACA,wBACA,CACA,CAAK,EACL,CACA,CA4HA,cACA,gEAAyF,OAAS,0BAAmC,EAAI,QACzI,CAYA,2DACA,gBACA,kBAAqC,EAAM,kBAC3C,sCAA8C,EAAO,OAAO,EAAW,gJACvE,aACA,cACA,eACA,CAAK,CACL,CACA,aACA,6BACA,OACA,EACA,UACA,CACA,cACA,2BACA,oBAEA,QACA,mBC9ZA,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAKF,cACA,0CACA,cACA,SACK,CACL,EACA,GACA,0BACA,QACA,CAAK,CACL,gCACA,QACA,CACA,CAAC,EACD,MAAiB,EAAQ,KAAW,CACpC,WADwB,MACxB,MACA,cACA,2GACA,CACA,kBACA,WACA,CACA,CACA,wBACA,eAGA,QACA,0BACA,WAIA,sBACA,mCAEA,sBAIA,8CAEA,cAEA,kCACA,CAAa,CACb,aACA,sBACA,qCAEA,sBAIA,8CAEA,uCACA,CAAa,CACb,SACA,uDACA,sBAIA,qDAEA,YAEA,yBACA,CAAa,CACb,oBACA,kEACA,sBAIA,qDAEA,YAEA,oCACA,CACA,CAAS,CACT,CAIA,eACA,oBACA,WACA,UACA,aACA,aACA,UACA,0BAEA,kCACA,CACA,CACA,CAAS,CACT,CAOA,gBACA,8BACA,CACA,CAMA,sBACA,uBACA,QACA,CACA,YACA,qBACA,oBACA,iBACA,EACA,EACA,CACU,iBACV,UAEA,iBAEA,CACA,UACA,uBAEA,OACA,6BACA,yBACA,IACA,CACA,OACA,gCAEA,SACA,iBACA,CACA,aACA,8BACA,kBAEA,CACA,WACA,wCACA,sBAGA,aACA,OACA,EACA,EACA,CAEA,CACA,QACA,wCACA,qBACA,QACA,CACA,CACA,UACA,wCAGA,iBACA,QACA,CACA,CACA,oBACA,qBACA,CACA,2GEnLA,MAAsC,EAAQ,KAAgD,EAC9F,EAAkC,EAAQ,KAA2C,CADxC,CAE7C,EAA0B,EAAQ,KAAiC,CAD1B,CAEzC,EAAkD,EAAQ,KAAmD,CAD5E,CAEjC,EAAiC,EAAQ,KAAmD,CADnC,CAEzD,EAA4B,EAAQ,KAA8C,CAD1C,CAExC,UADmC,GAGnC,oCACA,oCAIA,OAHA,UACA,kCAJA,aAMA,QACA,cACA,uBACA,aACA,qBAIA,mDACA,KACA,aAIA,iBACA,oBACA,uBAMA,cAEA,SAEA,OADA,CAEA,CACA,CACA,gBACA,IAIA,EAJA,kBACA,IAQA,OAEA,WACA,EACA,CACA,kBACA,cACA,eACA,qBAiBA,OAhBA,qCACA,QACA,YAEA,OACA,qCACA,QACA,YACA,aACA,CAAa,CACb,CAAS,CACT,cACA,eACA,CAAK,EACL,0BACA,4BACA,CACA,CAoCA,QACA,eACA,gBACA,CACA,uBACA,uBACA,yBAIA,SAGA,0BACA,uBACA,uBAEA,CACA,UACA,2BACA,uBACA,wBAEA,CACA,CAYA,wDACA,cACA,kBAAqC,EAAM,kBAC3C,sCAA8C,EAAO,OAAO,EAAW,kJACvE,aACA,cACA,eACA,CAAK,CACL,GACA,cACA,oCACA,oCACA,MAGA,KACA,qBACA,2CAA+D,SAAa,QAAQ,EAAW,+OAC/F,aACA,cACA,eACA,CAAiB,OACH,6BACd,2CAA+D,SAAa,QAAQ,EAAW,wRAC/F,aACA,cACA,eACA,CAAiB,OACH,qBACd,2CAA+D,SAAa,QAAQ,EAAW,kOAC/F,aACA,cACA,eACA,CAAiB,CACjB,CAEA,wBACA,iEAAoG,SAAa,+EAA+E,EAAW,oJAC3M,aACA,cACA,eACA,CAAa,EAEb,KACA,0BAEA,2CAAuE,SAAa,OAAO,GAAY,sJACvG,aACA,cACA,eACA,CAAiB,EACjB,gEACA,EAAc,gCAEd,6DACc,gCAEd,eACA,8DAAsG,SAAa,oDAAoD,EAAW,qGAClL,aACA,cACA,eACA,CAAiB,CAGjB,OAFA,4BACA,4BACA,CACA,CAAoH,CAAtG,CAKd,QALuB,KAA2F,EAAE,ICjOpH,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAWF,cACA,0CACA,cACA,SACK,CACL,EACA,GACA,wCACA,QACA,CAAK,CACL,uCACA,QACA,CAAK,CACL,iCACA,QACA,CAAK,CACL,gCACA,QACA,CAAK,CACL,2CACA,QACA,CAAK,CACL,mCACA,QACA,CAAK,CACL,2CACA,QACA,CAAK,CACL,sCACA,QACA,CACA,CAAC,EACD,MAAiB,EAAQ,KAAY,EACrC,EAAiB,EAAQ,KAAW,CADZ,CAExB,EAAkC,EAAQ,KAAiD,CADnE,CAExB,EAAsC,EAAQ,KAAsD,CAD3D,OAEzC,IAD6C,MAC7C,MACA,cACA,yJACA,CACA,kBACA,WACA,CACA,CACA,QACA,eACA,oBACA,WACA,UACA,YACA,aACA,UACA,0BAEA,kCACA,CACA,CACA,CAAS,CACT,CACA,CACA,yCACA,cACA,kBACA,kCAGA,EAFA,GAIA,gBACA,WACA,gBACA,SAKA,+BACA,aAEA,eACA,SAGA,eACA,SAEA,QACA,CACA,QACA,iBACA,yCACA,wBACA,SAEA,SACA,UACA,OAEA,oCAMA,GALA,GACA,0BAGA,EADA,WACA,yBACA,GACA,SACA,gBACA,yCACA,SACA,oBACA,CACA,IACA,CACA,EACA,eACA,WACA,UAEA,OACA,QAGA,cACA,sBACA,4CACA,IAEA,OADA,eACA,CACA,EAA8B,OAC9B,GACA,CACA,CACA,WACA,sBACA,4CACA,IAEA,OADA,YACA,CACA,EAA8B,OAC9B,GACA,CACA,CACA,SACA,kCACA,CACA,CACA,CAAS,EACT,QACA,CACA,CACA,cACA,mBACA,WACA,UACA,aACA,sBAGA,OAFA,sBACA,eACA,CACA,CACA,WACA,sBAGA,OAFA,mBACA,YACA,CACA,CACA,SACA,kCACA,CACA,CACA,CAAK,EACL,QACA,CACA,cACA,yBAQA,cAEA,MADA,kCAGA,WAEA,CACA,cACA,wCACA,wBACA,SAEA,QACA", "sources": ["webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/create-deduped-by-callsite-server-error-logger.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/request/headers.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/request/cookies.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/web/spec-extension/adapters/headers.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/api/headers.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/request/draft-mode.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"createDedupedByCallsiteServerErrorLoggerDev\", {\n    enumerable: true,\n    get: function() {\n        return createDedupedByCallsiteServerErrorLoggerDev;\n    }\n});\nconst _react = /*#__PURE__*/ _interop_require_wildcard(require(\"react\"));\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {\n        __proto__: null\n    };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nconst errorRef = {\n    current: null\n};\n// React.cache is currently only available in canary/experimental React channels.\nconst cache = typeof _react.cache === 'function' ? _react.cache : (fn)=>fn;\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO ? console.error : console.warn;\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(// eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n(key)=>{\n    try {\n        logErrorOrWarn(errorRef.current);\n    } finally{\n        errorRef.current = null;\n    }\n});\nfunction createDedupedByCallsiteServerErrorLoggerDev(getMessage) {\n    return function logDedupedError(...args) {\n        const message = getMessage(...args);\n        if (process.env.NODE_ENV !== 'production') {\n            var _stack;\n            const callStackFrames = (_stack = new Error().stack) == null ? void 0 : _stack.split('\\n');\n            if (callStackFrames === undefined || callStackFrames.length < 4) {\n                logErrorOrWarn(message);\n            } else {\n                // Error:\n                //   logDedupedError\n                //   asyncApiBeingAccessedSynchronously\n                //   <userland callsite>\n                // TODO: This breaks if sourcemaps with ignore lists are enabled.\n                const key = callStackFrames[4];\n                errorRef.current = message;\n                flushCurrentErrorIfNew(key);\n            }\n        } else {\n            logErrorOrWarn(message);\n        }\n    };\n}\n\n//# sourceMappingURL=create-deduped-by-callsite-server-error-logger.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"headers\", {\n    enumerable: true,\n    get: function() {\n        return headers;\n    }\n});\nconst _headers = require(\"../web/spec-extension/adapters/headers\");\nconst _workasyncstorageexternal = require(\"../app-render/work-async-storage.external\");\nconst _workunitasyncstorageexternal = require(\"../app-render/work-unit-async-storage.external\");\nconst _dynamicrendering = require(\"../app-render/dynamic-rendering\");\nconst _staticgenerationbailout = require(\"../../client/components/static-generation-bailout\");\nconst _dynamicrenderingutils = require(\"../dynamic-rendering-utils\");\nconst _creatededupedbycallsiteservererrorlogger = require(\"../create-deduped-by-callsite-server-error-logger\");\nconst _scheduler = require(\"../../lib/scheduler\");\nconst _utils = require(\"./utils\");\nfunction headers() {\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0, _utils.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E367\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // headers object without tracking\n            const underlyingHeaders = _headers.HeadersAdapter.seal(new Headers({}));\n            return makeUntrackedExoticHeaders(underlyingHeaders);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E304\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E127\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E525\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                // We don't track dynamic access here because access will be tracked when you access\n                // one of the properties of the headers object.\n                return makeDynamicallyTrackedExoticHeaders(workStore.route, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                // We are prerendering with PPR. We need track dynamic access here eagerly\n                // to keep continuity with how headers has worked in PPR without dynamicIO.\n                // TODO consider switching the semantic to throw on property access instead\n                (0, _dynamicrendering.postponeWithTracking)(workStore.route, 'headers', workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // Legacy Prerender\n                // We are in a legacy static generation mode while prerendering\n                // We track dynamic access here so we don't need to wrap the headers in\n                // individual property access tracking.\n                (0, _dynamicrendering.throwToInterruptStaticGeneration)('headers', workStore, workUnitStore);\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using headers inside a cache context\n        (0, _dynamicrendering.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)('headers');\n    if (process.env.NODE_ENV === 'development' && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        return makeUntrackedExoticHeadersWithDevWarnings(requestStore.headers, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticHeaders(requestStore.headers);\n    }\n}\nconst CachedHeaders = new WeakMap();\nfunction makeDynamicallyTrackedExoticHeaders(route, prerenderStore) {\n    const cachedHeaders = CachedHeaders.get(prerenderStore);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`headers()`');\n    CachedHeaders.set(prerenderStore, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``;\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`headers()[Symbol.iterator]()`';\n                const error = createHeadersAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeaders(underlyingHeaders) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = Promise.resolve(underlyingHeaders);\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: underlyingHeaders.append.bind(underlyingHeaders)\n        },\n        delete: {\n            value: underlyingHeaders.delete.bind(underlyingHeaders)\n        },\n        get: {\n            value: underlyingHeaders.get.bind(underlyingHeaders)\n        },\n        has: {\n            value: underlyingHeaders.has.bind(underlyingHeaders)\n        },\n        set: {\n            value: underlyingHeaders.set.bind(underlyingHeaders)\n        },\n        getSetCookie: {\n            value: underlyingHeaders.getSetCookie.bind(underlyingHeaders)\n        },\n        forEach: {\n            value: underlyingHeaders.forEach.bind(underlyingHeaders)\n        },\n        keys: {\n            value: underlyingHeaders.keys.bind(underlyingHeaders)\n        },\n        values: {\n            value: underlyingHeaders.values.bind(underlyingHeaders)\n        },\n        entries: {\n            value: underlyingHeaders.entries.bind(underlyingHeaders)\n        },\n        [Symbol.iterator]: {\n            value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticHeadersWithDevWarnings(underlyingHeaders, route) {\n    const cachedHeaders = CachedHeaders.get(underlyingHeaders);\n    if (cachedHeaders) {\n        return cachedHeaders;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingHeaders)));\n    CachedHeaders.set(underlyingHeaders, promise);\n    Object.defineProperties(promise, {\n        append: {\n            value: function append() {\n                const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.append.apply(underlyingHeaders, arguments);\n            }\n        },\n        delete: {\n            value: function _delete() {\n                const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.delete.apply(underlyingHeaders, arguments);\n            }\n        },\n        get: {\n            value: function get() {\n                const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.get.apply(underlyingHeaders, arguments);\n            }\n        },\n        has: {\n            value: function has() {\n                const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.has.apply(underlyingHeaders, arguments);\n            }\n        },\n        set: {\n            value: function set() {\n                const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``;\n                syncIODev(route, expression);\n                return underlyingHeaders.set.apply(underlyingHeaders, arguments);\n            }\n        },\n        getSetCookie: {\n            value: function getSetCookie() {\n                const expression = '`headers().getSetCookie()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.getSetCookie.apply(underlyingHeaders, arguments);\n            }\n        },\n        forEach: {\n            value: function forEach() {\n                const expression = '`headers().forEach(...)`';\n                syncIODev(route, expression);\n                return underlyingHeaders.forEach.apply(underlyingHeaders, arguments);\n            }\n        },\n        keys: {\n            value: function keys() {\n                const expression = '`headers().keys()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.keys.apply(underlyingHeaders, arguments);\n            }\n        },\n        values: {\n            value: function values() {\n                const expression = '`headers().values()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.values.apply(underlyingHeaders, arguments);\n            }\n        },\n        entries: {\n            value: function entries() {\n                const expression = '`headers().entries()`';\n                syncIODev(route, expression);\n                return underlyingHeaders.entries.apply(underlyingHeaders, arguments);\n            }\n        },\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...headers()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingHeaders[Symbol.iterator].apply(underlyingHeaders, arguments);\n            }\n        }\n    });\n    return promise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createHeadersAccessError);\nfunction createHeadersAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`headers()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E277\",\n        enumerable: false,\n        configurable: true\n    });\n}\n\n//# sourceMappingURL=headers.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"cookies\", {\n    enumerable: true,\n    get: function() {\n        return cookies;\n    }\n});\nconst _requestcookies = require(\"../web/spec-extension/adapters/request-cookies\");\nconst _cookies = require(\"../web/spec-extension/cookies\");\nconst _workasyncstorageexternal = require(\"../app-render/work-async-storage.external\");\nconst _workunitasyncstorageexternal = require(\"../app-render/work-unit-async-storage.external\");\nconst _dynamicrendering = require(\"../app-render/dynamic-rendering\");\nconst _staticgenerationbailout = require(\"../../client/components/static-generation-bailout\");\nconst _dynamicrenderingutils = require(\"../dynamic-rendering-utils\");\nconst _creatededupedbycallsiteservererrorlogger = require(\"../create-deduped-by-callsite-server-error-logger\");\nconst _scheduler = require(\"../../lib/scheduler\");\nconst _utils = require(\"./utils\");\nfunction cookies() {\n    const callingExpression = 'cookies';\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0, _utils.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(// TODO(after): clarify that this only applies to pages?\n            `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E88\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // cookies object without tracking\n            const underlyingCookies = createEmptyCookies();\n            return makeUntrackedExoticCookies(underlyingCookies);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E398\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E157\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E549\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                // We don't track dynamic access here because access will be tracked when you access\n                // one of the properties of the cookies object.\n                return makeDynamicallyTrackedExoticCookies(workStore.route, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                // We are prerendering with PPR. We need track dynamic access here eagerly\n                // to keep continuity with how cookies has worked in PPR without dynamicIO.\n                (0, _dynamicrendering.postponeWithTracking)(workStore.route, callingExpression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // Legacy Prerender\n                // We track dynamic access here so we don't need to wrap the cookies in\n                // individual property access tracking.\n                (0, _dynamicrendering.throwToInterruptStaticGeneration)(callingExpression, workStore, workUnitStore);\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using cookies inside a cache context\n        (0, _dynamicrendering.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    // cookies is being called in a dynamic context\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)(callingExpression);\n    let underlyingCookies;\n    if ((0, _requestcookies.areCookiesMutableInCurrentPhase)(requestStore)) {\n        // We can't conditionally return different types here based on the context.\n        // To avoid confusion, we always return the readonly type here.\n        underlyingCookies = requestStore.userspaceMutableCookies;\n    } else {\n        underlyingCookies = requestStore.cookies;\n    }\n    if (process.env.NODE_ENV === 'development' && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        return makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, workStore == null ? void 0 : workStore.route);\n    } else {\n        return makeUntrackedExoticCookies(underlyingCookies);\n    }\n}\nfunction createEmptyCookies() {\n    return _requestcookies.RequestCookiesAdapter.seal(new _cookies.RequestCookies(new Headers({})));\n}\nconst CachedCookies = new WeakMap();\nfunction makeDynamicallyTrackedExoticCookies(route, prerenderStore) {\n    const cachedPromise = CachedCookies.get(prerenderStore);\n    if (cachedPromise) {\n        return cachedPromise;\n    }\n    const promise = (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`cookies()`');\n    CachedCookies.set(prerenderStore, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`cookies()[Symbol.iterator]()`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        has: {\n            value: function has() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``;\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``;\n                } else {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``;\n                }\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()`';\n                const error = createCookiesAccessError(route, expression);\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(route, expression, error, prerenderStore);\n            }\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookies(underlyingCookies) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = Promise.resolve(underlyingCookies);\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].bind(underlyingCookies) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.bind(underlyingCookies)\n        },\n        size: {\n            get () {\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: underlyingCookies.get.bind(underlyingCookies)\n        },\n        getAll: {\n            value: underlyingCookies.getAll.bind(underlyingCookies)\n        },\n        has: {\n            value: underlyingCookies.has.bind(underlyingCookies)\n        },\n        set: {\n            value: underlyingCookies.set.bind(underlyingCookies)\n        },\n        delete: {\n            value: underlyingCookies.delete.bind(underlyingCookies)\n        },\n        clear: {\n            value: // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.bind(underlyingCookies) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise)\n        },\n        toString: {\n            value: underlyingCookies.toString.bind(underlyingCookies)\n        }\n    });\n    return promise;\n}\nfunction makeUntrackedExoticCookiesWithDevWarnings(underlyingCookies, route) {\n    const cachedCookies = CachedCookies.get(underlyingCookies);\n    if (cachedCookies) {\n        return cachedCookies;\n    }\n    const promise = new Promise((resolve)=>(0, _scheduler.scheduleImmediate)(()=>resolve(underlyingCookies)));\n    CachedCookies.set(underlyingCookies, promise);\n    Object.defineProperties(promise, {\n        [Symbol.iterator]: {\n            value: function() {\n                const expression = '`...cookies()` or similar iteration';\n                syncIODev(route, expression);\n                return underlyingCookies[Symbol.iterator] ? underlyingCookies[Symbol.iterator].apply(underlyingCookies, arguments) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n                // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesIterator.call(underlyingCookies);\n            },\n            writable: false\n        },\n        size: {\n            get () {\n                const expression = '`cookies().size`';\n                syncIODev(route, expression);\n                return underlyingCookies.size;\n            }\n        },\n        get: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().get()`';\n                } else {\n                    expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.get.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        getAll: {\n            value: function getAll() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().getAll()`';\n                } else {\n                    expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.getAll.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        has: {\n            value: function get() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().has()`';\n                } else {\n                    expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.has.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        set: {\n            value: function set() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().set()`';\n                } else {\n                    const arg = arguments[0];\n                    if (arg) {\n                        expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``;\n                    } else {\n                        expression = '`cookies().set(...)`';\n                    }\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.set.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        delete: {\n            value: function() {\n                let expression;\n                if (arguments.length === 0) {\n                    expression = '`cookies().delete()`';\n                } else if (arguments.length === 1) {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``;\n                } else {\n                    expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``;\n                }\n                syncIODev(route, expression);\n                return underlyingCookies.delete.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        },\n        clear: {\n            value: function clear() {\n                const expression = '`cookies().clear()`';\n                syncIODev(route, expression);\n                // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n                return typeof underlyingCookies.clear === 'function' ? underlyingCookies.clear.apply(underlyingCookies, arguments) : // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n                // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n                // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n                // has extra properties not available on RequestCookie instances.\n                polyfilledResponseCookiesClear.call(underlyingCookies, promise);\n            },\n            writable: false\n        },\n        toString: {\n            value: function toString() {\n                const expression = '`cookies().toString()` or implicit casting';\n                syncIODev(route, expression);\n                return underlyingCookies.toString.apply(underlyingCookies, arguments);\n            },\n            writable: false\n        }\n    });\n    return promise;\n}\nfunction describeNameArg(arg) {\n    return typeof arg === 'object' && arg !== null && typeof arg.name === 'string' ? `'${arg.name}'` : typeof arg === 'string' ? `'${arg}'` : '...';\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createCookiesAccessError);\nfunction createCookiesAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`cookies()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E223\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction polyfilledResponseCookiesIterator() {\n    return this.getAll().map((c)=>[\n            c.name,\n            c\n        ]).values();\n}\nfunction polyfilledResponseCookiesClear(returnable) {\n    for (const cookie of this.getAll()){\n        this.delete(cookie.name);\n    }\n    return returnable;\n}\n\n//# sourceMappingURL=cookies.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    HeadersAdapter: null,\n    ReadonlyHeadersError: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    HeadersAdapter: function() {\n        return HeadersAdapter;\n    },\n    ReadonlyHeadersError: function() {\n        return ReadonlyHeadersError;\n    }\n});\nconst _reflect = require(\"./reflect\");\nclass ReadonlyHeadersError extends Error {\n    constructor(){\n        super('Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers');\n    }\n    static callable() {\n        throw new ReadonlyHeadersError();\n    }\n}\nclass HeadersAdapter extends Headers {\n    constructor(headers){\n        // We've already overridden the methods that would be called, so we're just\n        // calling the super constructor to ensure that the instanceof check works.\n        super();\n        this.headers = new Proxy(headers, {\n            get (target, prop, receiver) {\n                // Because this is just an object, we expect that all \"get\" operations\n                // are for properties. If it's a \"get\" for a symbol, we'll just return\n                // the symbol.\n                if (typeof prop === 'symbol') {\n                    return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return undefined.\n                if (typeof original === 'undefined') return;\n                // If the original casing exists, return the value.\n                return _reflect.ReflectAdapter.get(target, original, receiver);\n            },\n            set (target, prop, value, receiver) {\n                if (typeof prop === 'symbol') {\n                    return _reflect.ReflectAdapter.set(target, prop, value, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, use the prop as the key.\n                return _reflect.ReflectAdapter.set(target, original ?? prop, value, receiver);\n            },\n            has (target, prop) {\n                if (typeof prop === 'symbol') return _reflect.ReflectAdapter.has(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return false.\n                if (typeof original === 'undefined') return false;\n                // If the original casing exists, return true.\n                return _reflect.ReflectAdapter.has(target, original);\n            },\n            deleteProperty (target, prop) {\n                if (typeof prop === 'symbol') return _reflect.ReflectAdapter.deleteProperty(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return true.\n                if (typeof original === 'undefined') return true;\n                // If the original casing exists, delete the property.\n                return _reflect.ReflectAdapter.deleteProperty(target, original);\n            }\n        });\n    }\n    /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */ static seal(headers) {\n        return new Proxy(headers, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'append':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyHeadersError.callable;\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n    /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */ merge(value) {\n        if (Array.isArray(value)) return value.join(', ');\n        return value;\n    }\n    /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */ static from(headers) {\n        if (headers instanceof Headers) return headers;\n        return new HeadersAdapter(headers);\n    }\n    append(name, value) {\n        const existing = this.headers[name];\n        if (typeof existing === 'string') {\n            this.headers[name] = [\n                existing,\n                value\n            ];\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            this.headers[name] = value;\n        }\n    }\n    delete(name) {\n        delete this.headers[name];\n    }\n    get(name) {\n        const value = this.headers[name];\n        if (typeof value !== 'undefined') return this.merge(value);\n        return null;\n    }\n    has(name) {\n        return typeof this.headers[name] !== 'undefined';\n    }\n    set(name, value) {\n        this.headers[name] = value;\n    }\n    forEach(callbackfn, thisArg) {\n        for (const [name, value] of this.entries()){\n            callbackfn.call(thisArg, value, name, this);\n        }\n    }\n    *entries() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(name);\n            yield [\n                name,\n                value\n            ];\n        }\n    }\n    *keys() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            yield name;\n        }\n    }\n    *values() {\n        for (const key of Object.keys(this.headers)){\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(key);\n            yield value;\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n}\n\n//# sourceMappingURL=headers.js.map", "export * from '../server/request/cookies';\nexport * from '../server/request/headers';\nexport * from '../server/request/draft-mode';\n\n//# sourceMappingURL=headers.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"draftMode\", {\n    enumerable: true,\n    get: function() {\n        return draftMode;\n    }\n});\nconst _workunitasyncstorageexternal = require(\"../app-render/work-unit-async-storage.external\");\nconst _workasyncstorageexternal = require(\"../app-render/work-async-storage.external\");\nconst _dynamicrendering = require(\"../app-render/dynamic-rendering\");\nconst _creatededupedbycallsiteservererrorlogger = require(\"../create-deduped-by-callsite-server-error-logger\");\nconst _staticgenerationbailout = require(\"../../client/components/static-generation-bailout\");\nconst _hooksservercontext = require(\"../../client/components/hooks-server-context\");\nfunction draftMode() {\n    const callingExpression = 'draftMode';\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (!workStore || !workUnitStore) {\n        (0, _workunitasyncstorageexternal.throwForMissingRequestStore)(callingExpression);\n    }\n    switch(workUnitStore.type){\n        case 'request':\n            return createOrGetCachedExoticDraftMode(workUnitStore.draftMode, workStore);\n        case 'cache':\n        case 'unstable-cache':\n            // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n            // the outmost work unit store is a request store, and if draft mode is\n            // enabled.\n            const draftModeProvider = (0, _workunitasyncstorageexternal.getDraftModeProviderForCacheScope)(workStore, workUnitStore);\n            if (draftModeProvider) {\n                return createOrGetCachedExoticDraftMode(draftModeProvider, workStore);\n            }\n        // Otherwise, we fall through to providing an empty draft mode.\n        // eslint-disable-next-line no-fallthrough\n        case 'prerender':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n            // Return empty draft mode\n            if (process.env.NODE_ENV === 'development' && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n                const route = workStore == null ? void 0 : workStore.route;\n                return createExoticDraftModeWithDevWarnings(null, route);\n            } else {\n                return createExoticDraftMode(null);\n            }\n        default:\n            const _exhaustiveCheck = workUnitStore;\n            return _exhaustiveCheck;\n    }\n}\nfunction createOrGetCachedExoticDraftMode(draftModeProvider, workStore) {\n    const cachedDraftMode = CachedDraftModes.get(draftMode);\n    if (cachedDraftMode) {\n        return cachedDraftMode;\n    }\n    let promise;\n    if (process.env.NODE_ENV === 'development' && !(workStore == null ? void 0 : workStore.isPrefetchRequest)) {\n        const route = workStore == null ? void 0 : workStore.route;\n        promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route);\n    } else {\n        promise = createExoticDraftMode(draftModeProvider);\n    }\n    CachedDraftModes.set(draftModeProvider, promise);\n    return promise;\n}\nconst CachedDraftModes = new WeakMap();\nfunction createExoticDraftMode(underlyingProvider) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            return instance.isEnabled;\n        },\n        set (newValue) {\n            Object.defineProperty(promise, 'isEnabled', {\n                value: newValue,\n                writable: true,\n                enumerable: true\n            });\n        },\n        enumerable: true,\n        configurable: true\n    });\n    promise.enable = instance.enable.bind(instance);\n    promise.disable = instance.disable.bind(instance);\n    return promise;\n}\nfunction createExoticDraftModeWithDevWarnings(underlyingProvider, route) {\n    const instance = new DraftMode(underlyingProvider);\n    const promise = Promise.resolve(instance);\n    Object.defineProperty(promise, 'isEnabled', {\n        get () {\n            const expression = '`draftMode().isEnabled`';\n            syncIODev(route, expression);\n            return instance.isEnabled;\n        },\n        set (newValue) {\n            Object.defineProperty(promise, 'isEnabled', {\n                value: newValue,\n                writable: true,\n                enumerable: true\n            });\n        },\n        enumerable: true,\n        configurable: true\n    });\n    Object.defineProperty(promise, 'enable', {\n        value: function get() {\n            const expression = '`draftMode().enable()`';\n            syncIODev(route, expression);\n            return instance.enable.apply(instance, arguments);\n        }\n    });\n    Object.defineProperty(promise, 'disable', {\n        value: function get() {\n            const expression = '`draftMode().disable()`';\n            syncIODev(route, expression);\n            return instance.disable.apply(instance, arguments);\n        }\n    });\n    return promise;\n}\nclass DraftMode {\n    constructor(provider){\n        this._provider = provider;\n    }\n    get isEnabled() {\n        if (this._provider !== null) {\n            return this._provider.isEnabled;\n        }\n        return false;\n    }\n    enable() {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        trackDynamicDraftMode('draftMode().enable()');\n        if (this._provider !== null) {\n            this._provider.enable();\n        }\n    }\n    disable() {\n        trackDynamicDraftMode('draftMode().disable()');\n        if (this._provider !== null) {\n            this._provider.disable();\n        }\n    }\n}\nfunction syncIODev(route, expression) {\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (workUnitStore && workUnitStore.type === 'request' && workUnitStore.prerenderPhase === true) {\n        // When we're rendering dynamically in dev we need to advance out of the\n        // Prerender environment when we read Request data synchronously\n        const requestStore = workUnitStore;\n        (0, _dynamicrendering.trackSynchronousRequestDataAccessInDev)(requestStore);\n    }\n    // In all cases we warn normally\n    warnForSyncAccess(route, expression);\n}\nconst warnForSyncAccess = (0, _creatededupedbycallsiteservererrorlogger.createDedupedByCallsiteServerErrorLoggerDev)(createDraftModeAccessError);\nfunction createDraftModeAccessError(route, expression) {\n    const prefix = route ? `Route \"${route}\" ` : 'This route ';\n    return Object.defineProperty(new Error(`${prefix}used ${expression}. ` + `\\`draftMode()\\` should be awaited before using its value. ` + `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`), \"__NEXT_ERROR_CODE\", {\n        value: \"E377\",\n        enumerable: false,\n        configurable: true\n    });\n}\nfunction trackDynamicDraftMode(expression) {\n    const store = _workasyncstorageexternal.workAsyncStorage.getStore();\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    if (store) {\n        // We have a store we want to track dynamic data access to ensure we\n        // don't statically generate routes that manipulate draft mode.\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E246\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E259\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.phase === 'after') {\n                throw Object.defineProperty(new Error(`Route ${store.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E348\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (store.dynamicShouldError) {\n            throw Object.defineProperty(new _staticgenerationbailout.StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E553\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                const error = Object.defineProperty(new Error(`Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E126\",\n                    enumerable: false,\n                    configurable: true\n                });\n                (0, _dynamicrendering.abortAndThrowOnSynchronousRequestDataAccess)(store.route, expression, error, workUnitStore);\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender\n                (0, _dynamicrendering.postponeWithTracking)(store.route, expression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // legacy Prerender\n                workUnitStore.revalidate = 0;\n                const err = Object.defineProperty(new _hooksservercontext.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E558\",\n                    enumerable: false,\n                    configurable: true\n                });\n                store.dynamicUsageDescription = expression;\n                store.dynamicUsageStack = err.stack;\n                throw err;\n            } else if (process.env.NODE_ENV === 'development' && workUnitStore && workUnitStore.type === 'request') {\n                workUnitStore.usedDynamic = true;\n            }\n        }\n    }\n}\n\n//# sourceMappingURL=draft-mode.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    MutableRequestCookiesAdapter: null,\n    ReadonlyRequestCookiesError: null,\n    RequestCookiesAdapter: null,\n    appendMutableCookies: null,\n    areCookiesMutableInCurrentPhase: null,\n    getModifiedCookieValues: null,\n    responseCookiesToRequestCookies: null,\n    wrapWithMutableAccessCheck: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    MutableRequestCookiesAdapter: function() {\n        return MutableRequestCookiesAdapter;\n    },\n    ReadonlyRequestCookiesError: function() {\n        return ReadonlyRequestCookiesError;\n    },\n    RequestCookiesAdapter: function() {\n        return RequestCookiesAdapter;\n    },\n    appendMutableCookies: function() {\n        return appendMutableCookies;\n    },\n    areCookiesMutableInCurrentPhase: function() {\n        return areCookiesMutableInCurrentPhase;\n    },\n    getModifiedCookieValues: function() {\n        return getModifiedCookieValues;\n    },\n    responseCookiesToRequestCookies: function() {\n        return responseCookiesToRequestCookies;\n    },\n    wrapWithMutableAccessCheck: function() {\n        return wrapWithMutableAccessCheck;\n    }\n});\nconst _cookies = require(\"../cookies\");\nconst _reflect = require(\"./reflect\");\nconst _workasyncstorageexternal = require(\"../../../app-render/work-async-storage.external\");\nconst _workunitasyncstorageexternal = require(\"../../../app-render/work-unit-async-storage.external\");\nclass ReadonlyRequestCookiesError extends Error {\n    constructor(){\n        super('Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options');\n    }\n    static callable() {\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nclass RequestCookiesAdapter {\n    static seal(cookies) {\n        return new Proxy(cookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'clear':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyRequestCookiesError.callable;\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n}\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies');\nfunction getModifiedCookieValues(cookies) {\n    const modified = cookies[SYMBOL_MODIFY_COOKIE_VALUES];\n    if (!modified || !Array.isArray(modified) || modified.length === 0) {\n        return [];\n    }\n    return modified;\n}\nfunction appendMutableCookies(headers, mutableCookies) {\n    const modifiedCookieValues = getModifiedCookieValues(mutableCookies);\n    if (modifiedCookieValues.length === 0) {\n        return false;\n    }\n    // Return a new response that extends the response with\n    // the modified cookies as fallbacks. `res` cookies\n    // will still take precedence.\n    const resCookies = new _cookies.ResponseCookies(headers);\n    const returnedCookies = resCookies.getAll();\n    // Set the modified cookies as fallbacks.\n    for (const cookie of modifiedCookieValues){\n        resCookies.set(cookie);\n    }\n    // Set the original cookies as the final values.\n    for (const cookie of returnedCookies){\n        resCookies.set(cookie);\n    }\n    return true;\n}\nclass MutableRequestCookiesAdapter {\n    static wrap(cookies, onUpdateCookies) {\n        const responseCookies = new _cookies.ResponseCookies(new Headers());\n        for (const cookie of cookies.getAll()){\n            responseCookies.set(cookie);\n        }\n        let modifiedValues = [];\n        const modifiedCookies = new Set();\n        const updateResponseCookies = ()=>{\n            // TODO-APP: change method of getting workStore\n            const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n            if (workStore) {\n                workStore.pathWasRevalidated = true;\n            }\n            const allCookies = responseCookies.getAll();\n            modifiedValues = allCookies.filter((c)=>modifiedCookies.has(c.name));\n            if (onUpdateCookies) {\n                const serializedCookies = [];\n                for (const cookie of modifiedValues){\n                    const tempCookies = new _cookies.ResponseCookies(new Headers());\n                    tempCookies.set(cookie);\n                    serializedCookies.push(tempCookies.toString());\n                }\n                onUpdateCookies(serializedCookies);\n            }\n        };\n        const wrappedCookies = new Proxy(responseCookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    // A special symbol to get the modified cookie values\n                    case SYMBOL_MODIFY_COOKIE_VALUES:\n                        return modifiedValues;\n                    // TODO: Throw error if trying to set a cookie after the response\n                    // headers have been set.\n                    case 'delete':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.delete(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    case 'set':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.set(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    default:\n                        return _reflect.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n        return wrappedCookies;\n    }\n}\nfunction wrapWithMutableAccessCheck(responseCookies) {\n    const wrappedCookies = new Proxy(responseCookies, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case 'delete':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().delete');\n                        target.delete(...args);\n                        return wrappedCookies;\n                    };\n                case 'set':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().set');\n                        target.set(...args);\n                        return wrappedCookies;\n                    };\n                default:\n                    return _reflect.ReflectAdapter.get(target, prop, receiver);\n            }\n        }\n    });\n    return wrappedCookies;\n}\nfunction areCookiesMutableInCurrentPhase(requestStore) {\n    return requestStore.phase === 'action';\n}\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */ function ensureCookiesAreStillMutable(callingExpression) {\n    const requestStore = (0, _workunitasyncstorageexternal.getExpectedRequestStore)(callingExpression);\n    if (!areCookiesMutableInCurrentPhase(requestStore)) {\n        // TODO: maybe we can give a more precise error message based on callingExpression?\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nfunction responseCookiesToRequestCookies(responseCookies) {\n    const requestCookies = new _cookies.RequestCookies(new Headers());\n    for (const cookie of responseCookies.getAll()){\n        requestCookies.set(cookie);\n    }\n    return requestCookies;\n}\n\n//# sourceMappingURL=request-cookies.js.map"], "names": [], "sourceRoot": ""}