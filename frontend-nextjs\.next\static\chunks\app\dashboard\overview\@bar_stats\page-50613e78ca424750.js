try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="21add467-bfe5-4429-b38d-dfdb181143cb",e._sentryDebugIdIdentifier="sentry-dbid-21add467-bfe5-4429-b38d-dfdb181143cb")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7411],{17520:(e,t,a)=>{Promise.resolve().then(a.bind(a,32117))},32117:(e,t,a)=>{"use strict";a.d(t,{BarGraph:()=>h});var r=a(52880),o=a(99004),s=a(84925),d=a(66644),l=a(50487),n=a(51076),i=a(98165),c=(0,s.gu)({chartName:"BarChart",GraphicalChild:d.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:l.W},{axisType:"yAxis",AxisComp:n.h}],formatAxisMap:i.pr}),m=a(68412),p=a(86540),u=a(47882);let b=[{date:"2024-04-01",desktop:222,mobile:150},{date:"2024-04-02",desktop:97,mobile:180},{date:"2024-04-03",desktop:167,mobile:120},{date:"2024-04-04",desktop:242,mobile:260},{date:"2024-04-05",desktop:373,mobile:290},{date:"2024-04-06",desktop:301,mobile:340},{date:"2024-04-07",desktop:245,mobile:180},{date:"2024-04-08",desktop:409,mobile:320},{date:"2024-04-09",desktop:59,mobile:110},{date:"2024-04-10",desktop:261,mobile:190},{date:"2024-04-11",desktop:327,mobile:350},{date:"2024-04-12",desktop:292,mobile:210},{date:"2024-04-13",desktop:342,mobile:380},{date:"2024-04-14",desktop:137,mobile:220},{date:"2024-04-15",desktop:120,mobile:170},{date:"2024-04-16",desktop:138,mobile:190},{date:"2024-04-17",desktop:446,mobile:360},{date:"2024-04-18",desktop:364,mobile:410},{date:"2024-04-19",desktop:243,mobile:180},{date:"2024-04-20",desktop:89,mobile:150},{date:"2024-04-21",desktop:137,mobile:200},{date:"2024-04-22",desktop:224,mobile:170},{date:"2024-04-23",desktop:138,mobile:230},{date:"2024-04-24",desktop:387,mobile:290},{date:"2024-04-25",desktop:215,mobile:250},{date:"2024-04-26",desktop:75,mobile:130},{date:"2024-04-27",desktop:383,mobile:420},{date:"2024-04-28",desktop:122,mobile:180},{date:"2024-04-29",desktop:315,mobile:240},{date:"2024-04-30",desktop:454,mobile:380},{date:"2024-05-01",desktop:165,mobile:220},{date:"2024-05-02",desktop:293,mobile:310},{date:"2024-05-03",desktop:247,mobile:190},{date:"2024-05-04",desktop:385,mobile:420},{date:"2024-05-05",desktop:481,mobile:390},{date:"2024-05-06",desktop:498,mobile:520},{date:"2024-05-07",desktop:388,mobile:300},{date:"2024-05-08",desktop:149,mobile:210},{date:"2024-05-09",desktop:227,mobile:180},{date:"2024-05-10",desktop:293,mobile:330},{date:"2024-05-11",desktop:335,mobile:270},{date:"2024-05-12",desktop:197,mobile:240},{date:"2024-05-13",desktop:197,mobile:160},{date:"2024-05-14",desktop:448,mobile:490},{date:"2024-05-15",desktop:473,mobile:380},{date:"2024-05-16",desktop:338,mobile:400},{date:"2024-05-17",desktop:499,mobile:420},{date:"2024-05-18",desktop:315,mobile:350},{date:"2024-05-19",desktop:235,mobile:180},{date:"2024-05-20",desktop:177,mobile:230},{date:"2024-05-21",desktop:82,mobile:140},{date:"2024-05-22",desktop:81,mobile:120},{date:"2024-05-23",desktop:252,mobile:290},{date:"2024-05-24",desktop:294,mobile:220},{date:"2024-05-25",desktop:201,mobile:250},{date:"2024-05-26",desktop:213,mobile:170},{date:"2024-05-27",desktop:420,mobile:460},{date:"2024-05-28",desktop:233,mobile:190},{date:"2024-05-29",desktop:78,mobile:130},{date:"2024-05-30",desktop:340,mobile:280},{date:"2024-05-31",desktop:178,mobile:230},{date:"2024-06-01",desktop:178,mobile:200},{date:"2024-06-02",desktop:470,mobile:410},{date:"2024-06-03",desktop:103,mobile:160},{date:"2024-06-04",desktop:439,mobile:380},{date:"2024-06-05",desktop:88,mobile:140},{date:"2024-06-06",desktop:294,mobile:250},{date:"2024-06-07",desktop:323,mobile:370},{date:"2024-06-08",desktop:385,mobile:320},{date:"2024-06-09",desktop:438,mobile:480},{date:"2024-06-10",desktop:155,mobile:200},{date:"2024-06-11",desktop:92,mobile:150},{date:"2024-06-12",desktop:492,mobile:420},{date:"2024-06-13",desktop:81,mobile:130},{date:"2024-06-14",desktop:426,mobile:380},{date:"2024-06-15",desktop:307,mobile:350},{date:"2024-06-16",desktop:371,mobile:310},{date:"2024-06-17",desktop:475,mobile:520},{date:"2024-06-18",desktop:107,mobile:170},{date:"2024-06-19",desktop:341,mobile:290},{date:"2024-06-20",desktop:408,mobile:450},{date:"2024-06-21",desktop:169,mobile:210},{date:"2024-06-22",desktop:317,mobile:270},{date:"2024-06-23",desktop:480,mobile:530},{date:"2024-06-24",desktop:132,mobile:180},{date:"2024-06-25",desktop:141,mobile:190},{date:"2024-06-26",desktop:434,mobile:380},{date:"2024-06-27",desktop:448,mobile:490},{date:"2024-06-28",desktop:149,mobile:200},{date:"2024-06-29",desktop:103,mobile:160},{date:"2024-06-30",desktop:446,mobile:400}],f={views:{label:"Page Views"},desktop:{label:"Desktop",color:"var(--primary)"},mobile:{label:"Mobile",color:"var(--primary)"},error:{label:"Error",color:"var(--primary)"}};function h(){let[e,t]=o.useState("desktop"),a=o.useMemo(()=>({desktop:b.reduce((e,t)=>e+t.desktop,0),mobile:b.reduce((e,t)=>e+t.mobile,0)}),[]),[s,n]=o.useState(!1);return(o.useEffect(()=>{n(!0)},[]),o.useEffect(()=>{if("error"===e)throw Error("Mocking Error")},[e]),s)?(0,r.jsxs)(p.Zp,{className:"@container/card !pt-3","data-sentry-element":"Card","data-sentry-component":"BarGraph","data-sentry-source-file":"bar-graph.tsx",children:[(0,r.jsxs)(p.aR,{className:"flex flex-col items-stretch space-y-0 border-b !p-0 sm:flex-row","data-sentry-element":"CardHeader","data-sentry-source-file":"bar-graph.tsx",children:[(0,r.jsxs)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 !py-0",children:[(0,r.jsx)(p.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"bar-graph.tsx",children:"Bar Chart - Interactive"}),(0,r.jsxs)(p.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"bar-graph.tsx",children:[(0,r.jsx)("span",{className:"hidden @[540px]/card:block",children:"Total for the last 3 months"}),(0,r.jsx)("span",{className:"@[540px]/card:hidden",children:"Last 3 months"})]})]}),(0,r.jsx)("div",{className:"flex",children:["desktop","mobile","error"].map(o=>{var s;return o&&0!==a[o]?(0,r.jsxs)("button",{"data-active":e===o,className:"data-[active=true]:bg-primary/5 hover:bg-primary/5 relative flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left transition-colors duration-200 even:border-l sm:border-t-0 sm:border-l sm:px-8 sm:py-6",onClick:()=>t(o),children:[(0,r.jsx)("span",{className:"text-muted-foreground text-xs",children:f[o].label}),(0,r.jsx)("span",{className:"text-lg leading-none font-bold sm:text-3xl",children:null==(s=a[o])?void 0:s.toLocaleString()})]},o):null})})]}),(0,r.jsx)(p.Wu,{className:"px-2 pt-4 sm:px-6 sm:pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"bar-graph.tsx",children:(0,r.jsx)(u.at,{config:f,className:"aspect-auto h-[250px] w-full","data-sentry-element":"ChartContainer","data-sentry-source-file":"bar-graph.tsx",children:(0,r.jsxs)(c,{data:b,margin:{left:12,right:12},"data-sentry-element":"BarChart","data-sentry-source-file":"bar-graph.tsx",children:[(0,r.jsx)("defs",{"data-sentry-element":"defs","data-sentry-source-file":"bar-graph.tsx",children:(0,r.jsxs)("linearGradient",{id:"fillBar",x1:"0",y1:"0",x2:"0",y2:"1","data-sentry-element":"linearGradient","data-sentry-source-file":"bar-graph.tsx",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"var(--primary)",stopOpacity:.8,"data-sentry-element":"stop","data-sentry-source-file":"bar-graph.tsx"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"var(--primary)",stopOpacity:.2,"data-sentry-element":"stop","data-sentry-source-file":"bar-graph.tsx"})]})}),(0,r.jsx)(m.d,{vertical:!1,"data-sentry-element":"CartesianGrid","data-sentry-source-file":"bar-graph.tsx"}),(0,r.jsx)(l.W,{dataKey:"date",tickLine:!1,axisLine:!1,tickMargin:8,minTickGap:32,tickFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"}),"data-sentry-element":"XAxis","data-sentry-source-file":"bar-graph.tsx"}),(0,r.jsx)(u.II,{cursor:{fill:"var(--primary)",opacity:.1},content:(0,r.jsx)(u.Nt,{className:"w-[150px]",nameKey:"views",labelFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}),"data-sentry-element":"ChartTooltip","data-sentry-source-file":"bar-graph.tsx"}),(0,r.jsx)(d.y,{dataKey:e,fill:"url(#fillBar)",radius:[4,4,0,0],"data-sentry-element":"Bar","data-sentry-source-file":"bar-graph.tsx"})]})})})]}):null}},47882:(e,t,a)=>{"use strict";a.d(t,{II:()=>u,Nt:()=>b,at:()=>m});var r=a(52880),o=a(99004),s=a(15998),d=a(67892),l=a(92942),n=a(54651);let i={light:"",dark:".dark"},c=o.createContext(null);function m(e){let{id:t,className:a,children:d,config:l,...i}=e,m=o.useId(),u="chart-".concat(t||m.replace(/:/g,""));return(0,r.jsx)(c.Provider,{value:{config:l},"data-sentry-element":"ChartContext.Provider","data-sentry-component":"ChartContainer","data-sentry-source-file":"chart.tsx",children:(0,r.jsxs)("div",{"data-slot":"chart","data-chart":u,className:(0,n.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",a),...i,children:[(0,r.jsx)(p,{id:u,config:l,"data-sentry-element":"ChartStyle","data-sentry-source-file":"chart.tsx"}),(0,r.jsx)(s.u,{debounce:2e3,"data-sentry-element":"RechartsPrimitive.ResponsiveContainer","data-sentry-source-file":"chart.tsx",children:d})]})})}let p=e=>{let{id:t,config:a}=e,o=Object.entries(a).filter(e=>{let[,t]=e;return t.theme||t.color});return o.length?(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(i).map(e=>{let[a,r]=e;return"\n".concat(r," [data-chart=").concat(t,"] {\n").concat(o.map(e=>{var t;let[r,o]=e,s=(null==(t=o.theme)?void 0:t[a])||o.color;return s?"  --color-".concat(r,": ").concat(s,";"):null}).join("\n"),"\n}\n")}).join("\n")},"data-sentry-component":"ChartStyle","data-sentry-source-file":"chart.tsx"}):null},u=d.m;function b(e){let{active:t,payload:a,className:s,indicator:d="dot",hideLabel:l=!1,hideIndicator:i=!1,label:m,labelFormatter:p,labelClassName:u,formatter:b,color:h,nameKey:x,labelKey:y}=e,{config:k}=function(){let e=o.useContext(c);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),g=o.useMemo(()=>{var e;if(l||!(null==a?void 0:a.length))return null;let[t]=a,o="".concat(y||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),s=f(k,t,o),d=y||"string"!=typeof m?null==s?void 0:s.label:(null==(e=k[m])?void 0:e.label)||m;return p?(0,r.jsx)("div",{className:(0,n.cn)("font-medium",u),children:p(d,a)}):d?(0,r.jsx)("div",{className:(0,n.cn)("font-medium",u),children:d}):null},[m,p,a,l,u,k,y]);if(!t||!(null==a?void 0:a.length))return null;let v=1===a.length&&"dot"!==d;return(0,r.jsxs)("div",{className:(0,n.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",s),"data-sentry-component":"ChartTooltipContent","data-sentry-source-file":"chart.tsx",children:[v?null:g,(0,r.jsx)("div",{className:"grid gap-1.5",children:a.map((e,t)=>{let a="".concat(x||e.name||e.dataKey||"value"),o=f(k,e,a),s=h||e.payload.fill||e.color;return(0,r.jsx)("div",{className:(0,n.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===d&&"items-center"),children:b&&(null==e?void 0:e.value)!==void 0&&e.name?b(e.value,e.name,e,t,e.payload):(0,r.jsxs)(r.Fragment,{children:[(null==o?void 0:o.icon)?(0,r.jsx)(o.icon,{}):!i&&(0,r.jsx)("div",{className:(0,n.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===d,"w-1":"line"===d,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===d,"my-0.5":v&&"dashed"===d}),style:{"--color-bg":s,"--color-border":s}}),(0,r.jsxs)("div",{className:(0,n.cn)("flex flex-1 justify-between leading-none",v?"items-end":"items-center"),children:[(0,r.jsxs)("div",{className:"grid gap-1.5",children:[v?g:null,(0,r.jsx)("span",{className:"text-muted-foreground",children:(null==o?void 0:o.label)||e.name})]}),e.value&&(0,r.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function f(e,t,a){if("object"!=typeof t||null===t)return;let r="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,o=a;return a in t&&"string"==typeof t[a]?o=t[a]:r&&a in r&&"string"==typeof r[a]&&(o=r[a]),o in e?e[o]:e[a]}l.s},54651:(e,t,a)=>{"use strict";a.d(t,{Jv:()=>i,cn:()=>s,fw:()=>n,r6:()=>l,z3:()=>d});var r=a(97921),o=a(56309);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,o.QP)((0,r.$)(t))}function d(e){var t,a;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:o=0,sizeType:s="normal"}=r;if(0===e)return"0 Byte";let d=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,d)).toFixed(o)," ").concat("accurate"===s?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][d])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][d])?a:"Bytes")}function l(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function n(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let a=Math.floor(t/60);if(a<60)return"".concat(a,"分钟前");let r=Math.floor(a/60);if(r<24)return"".concat(r,"小时前");let o=Math.floor(r/24);if(o<7)return"".concat(o,"天前");let s=Math.floor(o/7);if(s<4)return"".concat(s,"周前");let d=Math.floor(o/30);if(d<12)return"".concat(d,"个月前");let l=Math.floor(o/365);return"".concat(l,"年前")}function i(e){return("string"==typeof e?new Date(e):e)<new Date}},86540:(e,t,a)=>{"use strict";a.d(t,{BT:()=>n,Wu:()=>c,X9:()=>i,ZB:()=>l,Zp:()=>s,aR:()=>d,wL:()=>m});var r=a(52880);a(99004);var o=a(54651);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,o.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...a,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function m(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,o.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,2826,2530,9535,9442,4579,9253,7358],()=>t(17520)),_N_E=e.O()}]);