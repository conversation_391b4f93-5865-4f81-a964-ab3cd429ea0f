try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f1ec62d9-3cd6-4184-bba6-ec884f33e541",e._sentryDebugIdIdentifier="sentry-dbid-f1ec62d9-3cd6-4184-bba6-ec884f33e541")}catch(e){}exports.id=8968,exports.ids=[8968],exports.modules={2708:(e,t,r)=>{Promise.resolve().then(r.bind(r,78267))},4916:(e,t,r)=>{Promise.resolve().then(r.bind(r,49045))},7760:()=>{},10849:(e,t,r)=>{Promise.resolve().then(r.bind(r,60901))},10883:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var a=r(78869),s=r(19557);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",e),...t,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}},20361:(e,t,r)=>{"use strict";r.d(t,{Avatar:()=>s,AvatarFallback:()=>o,AvatarImage:()=>n});var a=r(91611);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\avatar.tsx","Avatar"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call AvatarImage() from the server but AvatarImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\avatar.tsx","AvatarImage"),o=(0,a.registerClientReference)(function(){throw Error("Attempted to call AvatarFallback() from the server but AvatarFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\avatar.tsx","AvatarFallback")},20619:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(24443),s=r(91476),n=r(33284),o=r(32218),l=r(51092),d=r(34769),i=r(60222);function c({error:e,reset:t}){let r=(0,d.useRouter)(),[c,m]=(0,i.useTransition)(),p=()=>{m(()=>{r.refresh(),t()})};return(0,a.jsxs)(o.Zp,{className:"border-red-500","data-sentry-element":"Card","data-sentry-component":"StatsError","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(o.aR,{className:"flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row","data-sentry-element":"CardHeader","data-sentry-source-file":"error.tsx",children:(0,a.jsx)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6",children:(0,a.jsxs)(s.Fc,{variant:"destructive",className:"border-none","data-sentry-element":"Alert","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(l.A,{className:"h-4 w-4","data-sentry-element":"IconAlertCircle","data-sentry-source-file":"error.tsx"}),(0,a.jsx)(s.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,a.jsxs)(s.TN,{className:"mt-2","data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load statistics: ",e.message]})]})})}),(0,a.jsx)(o.Wu,{className:"flex h-[316px] items-center justify-center p-6","data-sentry-element":"CardContent","data-sentry-source-file":"error.tsx",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-muted-foreground mb-4 text-sm",children:"Unable to display statistics at this time"}),(0,a.jsx)(n.$,{onClick:()=>p(),variant:"outline",className:"min-w-[120px]",disabled:c,"data-sentry-element":"Button","data-sentry-source-file":"error.tsx",children:"Try again"})]})})]})}r(89011)},22295:(e,t,r)=>{"use strict";r.d(t,{BarGraph:()=>s});var a=r(91611);(0,a.registerClientReference)(function(){throw Error("Attempted to call description() from the server but description is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\bar-graph.tsx","description");let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call BarGraph() from the server but BarGraph is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\bar-graph.tsx","BarGraph")},28016:(e,t,r)=>{Promise.resolve().then(r.bind(r,69900))},30766:(e,t,r)=>{Promise.resolve().then(r.bind(r,89371))},30815:(e,t,r)=>{Promise.resolve().then(r.bind(r,35691))},32218:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,X9:()=>i,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>m});var a=r(24443);r(60222);var s=r(72595);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},35691:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\overview\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\error.tsx","default")},36226:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>g,generateImageMetadata:()=>f,generateMetadata:()=>y,generateViewport:()=>h});var s=r(63033),n=r(78869),o=r(47923),l=r(20361),d=r(96756);let i=[{name:"Olivia Martin",email:"<EMAIL>",avatar:"https://api.slingacademy.com/public/sample-users/1.png",fallback:"OM",amount:"+$1,999.00"},{name:"Jackson Lee",email:"<EMAIL>",avatar:"https://api.slingacademy.com/public/sample-users/2.png",fallback:"JL",amount:"+$39.00"},{name:"Isabella Nguyen",email:"<EMAIL>",avatar:"https://api.slingacademy.com/public/sample-users/3.png",fallback:"IN",amount:"+$299.00"},{name:"William Kim",email:"<EMAIL>",avatar:"https://api.slingacademy.com/public/sample-users/4.png",fallback:"WK",amount:"+$99.00"},{name:"Sofia Davis",email:"<EMAIL>",avatar:"https://api.slingacademy.com/public/sample-users/5.png",fallback:"SD",amount:"+$39.00"}];function c(){return(0,n.jsxs)(d.Zp,{className:"h-full","data-sentry-element":"Card","data-sentry-component":"RecentSales","data-sentry-source-file":"recent-sales.tsx",children:[(0,n.jsxs)(d.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"recent-sales.tsx",children:[(0,n.jsx)(d.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"recent-sales.tsx",children:"Recent Sales"}),(0,n.jsx)(d.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"recent-sales.tsx",children:"You made 265 sales this month."})]}),(0,n.jsx)(d.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"recent-sales.tsx",children:(0,n.jsx)("div",{className:"space-y-8",children:i.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsxs)(l.Avatar,{className:"h-9 w-9",children:[(0,n.jsx)(l.AvatarImage,{src:e.avatar,alt:"Avatar"}),(0,n.jsx)(l.AvatarFallback,{children:e.fallback})]}),(0,n.jsxs)("div",{className:"ml-4 space-y-1",children:[(0,n.jsx)("p",{className:"text-sm leading-none font-medium",children:e.name}),(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:e.email})]}),(0,n.jsx)("div",{className:"ml-auto font-medium",children:e.amount})]},t))})})]})}var m=r(19761);async function p(){return await (0,o.c)(3e3),(0,n.jsx)(c,{"data-sentry-element":"RecentSales","data-sentry-component":"Sales","data-sentry-source-file":"page.tsx"})}let u={...s},x="workUnitAsyncStorage"in u?u.workUnitAsyncStorage:"requestAsyncStorage"in u?u.requestAsyncStorage:void 0;a=new Proxy(p,{apply:(e,t,r)=>{let a,s,n;try{let e=x?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return m.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/overview/@sales",componentType:"Page",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let y=void 0,f=void 0,h=void 0,g=a},37235:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>y,generateImageMetadata:()=>u,generateMetadata:()=>p,generateViewport:()=>x});var s=r(63033),n=r(78869),o=r(96756),l=r(10883);function d(){return(0,n.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-component":"AreaGraphSkeleton","data-sentry-source-file":"area-graph-skeleton.tsx",children:[(0,n.jsx)(o.aR,{className:"flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row","data-sentry-element":"CardHeader","data-sentry-source-file":"area-graph-skeleton.tsx",children:(0,n.jsxs)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6",children:[(0,n.jsx)(l.E,{className:"h-6 w-[180px]","data-sentry-element":"Skeleton","data-sentry-source-file":"area-graph-skeleton.tsx"}),(0,n.jsx)(l.E,{className:"h-4 w-[250px]","data-sentry-element":"Skeleton","data-sentry-source-file":"area-graph-skeleton.tsx"})]})}),(0,n.jsx)(o.Wu,{className:"px-2 sm:p-6","data-sentry-element":"CardContent","data-sentry-source-file":"area-graph-skeleton.tsx",children:(0,n.jsxs)("div",{className:"relative aspect-auto h-[280px] w-full",children:[(0,n.jsx)("div",{className:"from-primary/5 to-primary/20 absolute inset-0 rounded-lg bg-linear-to-t"}),(0,n.jsx)(l.E,{className:"absolute right-0 bottom-0 left-0 h-[1px]","data-sentry-element":"Skeleton","data-sentry-source-file":"area-graph-skeleton.tsx"})," ",(0,n.jsx)(l.E,{className:"absolute top-0 bottom-0 left-0 w-[1px]","data-sentry-element":"Skeleton","data-sentry-source-file":"area-graph-skeleton.tsx"})," "]})})]})}var i=r(19761);let c={...s},m="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;a=new Proxy(function(){return(0,n.jsx)(d,{"data-sentry-element":"AreaGraphSkeleton","data-sentry-component":"Loading","data-sentry-source-file":"loading.tsx"})},{apply:(e,t,r)=>{let a,s,n;try{let e=m?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/overview/@area_stats",componentType:"Loading",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let p=void 0,u=void 0,x=void 0,y=a},39406:(e,t,r)=>{Promise.resolve().then(r.bind(r,9343))},41005:(e,t,r)=>{Promise.resolve().then(r.bind(r,20619))},44589:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>y,generateImageMetadata:()=>u,generateMetadata:()=>p,generateViewport:()=>x});var s=r(63033),n=r(78869),o=r(10883),l=r(96756);function d(){return(0,n.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-component":"PieGraphSkeleton","data-sentry-source-file":"pie-graph-skeleton.tsx",children:[(0,n.jsx)(l.aR,{className:"flex flex-col items-stretch space-y-0 border-b p-0","data-sentry-element":"CardHeader","data-sentry-source-file":"pie-graph-skeleton.tsx",children:(0,n.jsxs)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6",children:[(0,n.jsx)(o.E,{className:"h-6 w-[180px]","data-sentry-element":"Skeleton","data-sentry-source-file":"pie-graph-skeleton.tsx"}),(0,n.jsx)(o.E,{className:"h-4 w-[250px]","data-sentry-element":"Skeleton","data-sentry-source-file":"pie-graph-skeleton.tsx"})]})}),(0,n.jsx)(l.Wu,{className:"p-6","data-sentry-element":"CardContent","data-sentry-source-file":"pie-graph-skeleton.tsx",children:(0,n.jsx)("div",{className:"flex h-[280px] items-center justify-center",children:(0,n.jsx)(o.E,{className:"h-[300px] w-[300px] rounded-full","data-sentry-element":"Skeleton","data-sentry-source-file":"pie-graph-skeleton.tsx"})})})]})}var i=r(19761);let c={...s},m="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;a=new Proxy(function(){return(0,n.jsx)(d,{"data-sentry-element":"PieGraphSkeleton","data-sentry-component":"Loading","data-sentry-source-file":"loading.tsx"})},{apply:(e,t,r)=>{let a,s,n;try{let e=m?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/overview/@pie_stats",componentType:"Loading",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let p=void 0,u=void 0,x=void 0,y=a},47297:(e,t,r)=>{Promise.resolve().then(r.bind(r,83391))},47386:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(24443),s=r(91476),n=r(51092);function o({error:e}){return(0,a.jsxs)(s.Fc,{variant:"destructive","data-sentry-element":"Alert","data-sentry-component":"PieStatsError","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(n.A,{className:"h-4 w-4","data-sentry-element":"IconAlertCircle","data-sentry-source-file":"error.tsx"}),(0,a.jsx)(s.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,a.jsxs)(s.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load pie statistics: ",e.message]})]})}},47923:(e,t,r)=>{"use strict";r.d(t,{c:()=>n,g:()=>o});var a=r(36637),s=r(3944);let n=e=>new Promise(t=>setTimeout(t,e)),o={records:[],initialize(){let e=[];for(let r=1;r<=20;r++){var t;e.push({id:t=r,name:a.a.commerce.productName(),description:a.a.commerce.productDescription(),created_at:a.a.date.between({from:"2022-01-01",to:"2023-12-31"}).toISOString(),price:parseFloat(a.a.commerce.price({min:5,max:500,dec:2})),photo_url:`https://api.slingacademy.com/public/sample-products/${t}.png`,category:a.a.helpers.arrayElement(["Electronics","Furniture","Clothing","Toys","Groceries","Books","Jewelry","Beauty Products"]),updated_at:a.a.date.recent().toISOString()})}this.records=e},async getAll({categories:e=[],search:t}){let r=[...this.records];return e.length>0&&(r=r.filter(t=>e.includes(t.category))),t&&(r=(0,s.Ht)(r,t,{keys:["name","description","category"]})),r},async getProducts({page:e=1,limit:t=10,categories:r,search:a}){await n(1e3);let s=r?r.split("."):[],o=await this.getAll({categories:s,search:a}),l=o.length,d=(e-1)*t,i=o.slice(d,d+t);return{success:!0,time:new Date().toISOString(),message:"Sample data for testing and learning purposes",total_products:l,offset:d,limit:t,products:i}},async getProductById(e){await n(1e3);let t=this.records.find(t=>t.id===e);return t?{success:!0,time:new Date().toISOString(),message:`Product with ID ${e} found`,product:t}:{success:!1,message:`Product with ID ${e} not found`}}};o.initialize()},48662:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\overview\\\\@sales\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\error.tsx","default")},49045:(e,t,r)=>{"use strict";r.d(t,{AreaGraph:()=>u});var a=r(24443),s=r(58246),n=r(27085),o=r(72698),l=r(55041),d=r(97803),i=r(32218),c=r(63726);let m=[{month:"January",desktop:186,mobile:80},{month:"February",desktop:305,mobile:200},{month:"March",desktop:237,mobile:120},{month:"April",desktop:73,mobile:190},{month:"May",desktop:209,mobile:130},{month:"June",desktop:214,mobile:140}],p={visitors:{label:"Visitors"},desktop:{label:"Desktop",color:"var(--primary)"},mobile:{label:"Mobile",color:"var(--primary)"}};function u(){return(0,a.jsxs)(i.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-component":"AreaGraph","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"area-graph.tsx",children:"Area Chart - Stacked"}),(0,a.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"area-graph.tsx",children:"Showing total visitors for the last 6 months"})]}),(0,a.jsx)(i.Wu,{className:"px-2 pt-4 sm:px-6 sm:pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"area-graph.tsx",children:(0,a.jsx)(c.at,{config:p,className:"aspect-auto h-[250px] w-full","data-sentry-element":"ChartContainer","data-sentry-source-file":"area-graph.tsx",children:(0,a.jsxs)(n.Q,{data:m,margin:{left:12,right:12},"data-sentry-element":"AreaChart","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsxs)("defs",{"data-sentry-element":"defs","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsxs)("linearGradient",{id:"fillDesktop",x1:"0",y1:"0",x2:"0",y2:"1","data-sentry-element":"linearGradient","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsx)("stop",{offset:"5%",stopColor:"var(--color-desktop)",stopOpacity:1,"data-sentry-element":"stop","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)("stop",{offset:"95%",stopColor:"var(--color-desktop)",stopOpacity:.1,"data-sentry-element":"stop","data-sentry-source-file":"area-graph.tsx"})]}),(0,a.jsxs)("linearGradient",{id:"fillMobile",x1:"0",y1:"0",x2:"0",y2:"1","data-sentry-element":"linearGradient","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsx)("stop",{offset:"5%",stopColor:"var(--color-mobile)",stopOpacity:.8,"data-sentry-element":"stop","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)("stop",{offset:"95%",stopColor:"var(--color-mobile)",stopOpacity:.1,"data-sentry-element":"stop","data-sentry-source-file":"area-graph.tsx"})]})]}),(0,a.jsx)(o.d,{vertical:!1,"data-sentry-element":"CartesianGrid","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)(l.W,{dataKey:"month",tickLine:!1,axisLine:!1,tickMargin:8,minTickGap:32,tickFormatter:e=>e.slice(0,3),"data-sentry-element":"XAxis","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)(c.II,{cursor:!1,content:(0,a.jsx)(c.Nt,{indicator:"dot"}),"data-sentry-element":"ChartTooltip","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)(d.G,{dataKey:"mobile",type:"natural",fill:"url(#fillMobile)",stroke:"var(--color-mobile)",stackId:"a","data-sentry-element":"Area","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)(d.G,{dataKey:"desktop",type:"natural",fill:"url(#fillDesktop)",stroke:"var(--color-desktop)",stackId:"a","data-sentry-element":"Area","data-sentry-source-file":"area-graph.tsx"})]})})}),(0,a.jsx)(i.wL,{"data-sentry-element":"CardFooter","data-sentry-source-file":"area-graph.tsx",children:(0,a.jsx)("div",{className:"flex w-full items-start gap-2 text-sm",children:(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 leading-none font-medium",children:["Trending up by 5.2% this month"," ",(0,a.jsx)(s.A,{className:"h-4 w-4","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"area-graph.tsx"})]}),(0,a.jsx)("div",{className:"text-muted-foreground flex items-center gap-2 leading-none",children:"January - June 2024"})]})})})]})}},49616:()=>{},52433:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(24443),s=r(91476),n=r(51092);function o({error:e}){return(0,a.jsxs)(s.Fc,{variant:"destructive","data-sentry-element":"Alert","data-sentry-component":"OverviewError","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(n.A,{className:"h-4 w-4","data-sentry-element":"IconAlertCircle","data-sentry-source-file":"error.tsx"}),(0,a.jsx)(s.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,a.jsxs)(s.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load statistics: ",e.message]})]})}},53218:(e,t,r)=>{Promise.resolve().then(r.bind(r,81893))},53544:(e,t,r)=>{Promise.resolve().then(r.bind(r,77777))},55334:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>y,generateImageMetadata:()=>u,generateMetadata:()=>p,generateViewport:()=>x});var s=r(63033),n=r(78869),o=r(47923),l=r(78267),d=r(19761);async function i(){return await await (0,o.c)(2e3),(0,n.jsx)(l.AreaGraph,{"data-sentry-element":"AreaGraph","data-sentry-component":"AreaStats","data-sentry-source-file":"page.tsx"})}let c={...s},m="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;a=new Proxy(i,{apply:(e,t,r)=>{let a,s,n;try{let e=m?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return d.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/overview/@area_stats",componentType:"Page",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let p=void 0,u=void 0,x=void 0,y=a},59565:(e,t,r)=>{Promise.resolve().then(r.bind(r,97057))},59792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\overview\\\\@pie_stats\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\error.tsx","default")},60901:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\overview\\\\@area_stats\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\error.tsx","default")},62160:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>y,generateImageMetadata:()=>u,generateMetadata:()=>p,generateViewport:()=>x});var s=r(63033),n=r(78869),o=r(96756),l=r(10883);function d(){return(0,n.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-component":"BarGraphSkeleton","data-sentry-source-file":"bar-graph-skeleton.tsx",children:[(0,n.jsxs)(o.aR,{className:"flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row","data-sentry-element":"CardHeader","data-sentry-source-file":"bar-graph-skeleton.tsx",children:[(0,n.jsxs)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6",children:[(0,n.jsx)(l.E,{className:"h-6 w-[180px]","data-sentry-element":"Skeleton","data-sentry-source-file":"bar-graph-skeleton.tsx"}),(0,n.jsx)(l.E,{className:"h-4 w-[250px]","data-sentry-element":"Skeleton","data-sentry-source-file":"bar-graph-skeleton.tsx"})]}),(0,n.jsx)("div",{className:"flex",children:[1,2].map(e=>(0,n.jsxs)("div",{className:"relative flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left even:border-l sm:border-t-0 sm:border-l sm:px-8 sm:py-6",children:[(0,n.jsx)(l.E,{className:"h-3 w-[80px]"}),(0,n.jsx)(l.E,{className:"h-8 w-[100px] sm:h-10"})]},e))})]}),(0,n.jsx)(o.Wu,{className:"px-2 sm:p-6","data-sentry-element":"CardContent","data-sentry-source-file":"bar-graph-skeleton.tsx",children:(0,n.jsx)("div",{className:"flex aspect-auto h-[280px] w-full items-end justify-around gap-2 pt-8",children:Array.from({length:12}).map((e,t)=>(0,n.jsx)(l.E,{className:"w-full",style:{height:`${Math.max(20,100*Math.random())}%`}},t))})})]})}var i=r(19761);let c={...s},m="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;a=new Proxy(function(){return(0,n.jsx)(d,{"data-sentry-element":"BarGraphSkeleton","data-sentry-component":"Loading","data-sentry-source-file":"loading.tsx"})},{apply:(e,t,r)=>{let a,s,n;try{let e=m?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/overview/@bar_stats",componentType:"Loading",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let p=void 0,u=void 0,x=void 0,y=a},63726:(e,t,r)=>{"use strict";r.d(t,{II:()=>u,Nt:()=>x,at:()=>m});var a=r(24443),s=r(60222),n=r(45908),o=r(5398),l=r(91110),d=r(72595);let i={light:"",dark:".dark"},c=s.createContext(null);function m({id:e,className:t,children:r,config:o,...l}){let i=s.useId(),m=`chart-${e||i.replace(/:/g,"")}`;return(0,a.jsx)(c.Provider,{value:{config:o},"data-sentry-element":"ChartContext.Provider","data-sentry-component":"ChartContainer","data-sentry-source-file":"chart.tsx",children:(0,a.jsxs)("div",{"data-slot":"chart","data-chart":m,className:(0,d.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",t),...l,children:[(0,a.jsx)(p,{id:m,config:o,"data-sentry-element":"ChartStyle","data-sentry-source-file":"chart.tsx"}),(0,a.jsx)(n.u,{debounce:2e3,"data-sentry-element":"RechartsPrimitive.ResponsiveContainer","data-sentry-source-file":"chart.tsx",children:r})]})})}let p=({id:e,config:t})=>{let r=Object.entries(t).filter(([,e])=>e.theme||e.color);return r.length?(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(i).map(([t,a])=>`
${a} [data-chart=${e}] {
${r.map(([e,r])=>{let a=r.theme?.[t]||r.color;return a?`  --color-${e}: ${a};`:null}).join("\n")}
}
`).join("\n")},"data-sentry-component":"ChartStyle","data-sentry-source-file":"chart.tsx"}):null},u=o.m;function x({active:e,payload:t,className:r,indicator:n="dot",hideLabel:o=!1,hideIndicator:l=!1,label:i,labelFormatter:m,labelClassName:p,formatter:u,color:x,nameKey:f,labelKey:h}){let{config:g}=function(){let e=s.useContext(c);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),v=s.useMemo(()=>{if(o||!t?.length)return null;let[e]=t,r=`${h||e?.dataKey||e?.name||"value"}`,s=y(g,e,r),n=h||"string"!=typeof i?s?.label:g[i]?.label||i;return m?(0,a.jsx)("div",{className:(0,d.cn)("font-medium",p),children:m(n,t)}):n?(0,a.jsx)("div",{className:(0,d.cn)("font-medium",p),children:n}):null},[i,m,t,o,p,g,h]);if(!e||!t?.length)return null;let b=1===t.length&&"dot"!==n;return(0,a.jsxs)("div",{className:(0,d.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",r),"data-sentry-component":"ChartTooltipContent","data-sentry-source-file":"chart.tsx",children:[b?null:v,(0,a.jsx)("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let r=`${f||e.name||e.dataKey||"value"}`,s=y(g,e,r),o=x||e.payload.fill||e.color;return(0,a.jsx)("div",{className:(0,d.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===n&&"items-center"),children:u&&e?.value!==void 0&&e.name?u(e.value,e.name,e,t,e.payload):(0,a.jsxs)(a.Fragment,{children:[s?.icon?(0,a.jsx)(s.icon,{}):!l&&(0,a.jsx)("div",{className:(0,d.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":b&&"dashed"===n}),style:{"--color-bg":o,"--color-border":o}}),(0,a.jsxs)("div",{className:(0,d.cn)("flex flex-1 justify-between leading-none",b?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[b?v:null,(0,a.jsx)("span",{className:"text-muted-foreground",children:s?.label||e.name})]}),e.value&&(0,a.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function y(e,t,r){if("object"!=typeof t||null===t)return;let a="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,s=r;return r in t&&"string"==typeof t[r]?s=t[r]:a&&r in a&&"string"==typeof a[r]&&(s=a[r]),s in e?e[s]:e[r]}l.s},63971:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>y,generateImageMetadata:()=>u,generateMetadata:()=>p,generateViewport:()=>x});var s=r(63033),n=r(78869),o=r(10883),l=r(96756);function d(){return(0,n.jsxs)(l.Zp,{className:"h-full","data-sentry-element":"Card","data-sentry-component":"RecentSalesSkeleton","data-sentry-source-file":"recent-sales-skeleton.tsx",children:[(0,n.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"recent-sales-skeleton.tsx",children:[(0,n.jsx)(o.E,{className:"h-6 w-[140px]","data-sentry-element":"Skeleton","data-sentry-source-file":"recent-sales-skeleton.tsx"})," ",(0,n.jsx)(o.E,{className:"h-4 w-[180px]","data-sentry-element":"Skeleton","data-sentry-source-file":"recent-sales-skeleton.tsx"})," "]}),(0,n.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"recent-sales-skeleton.tsx",children:(0,n.jsx)("div",{className:"space-y-8",children:Array.from({length:5}).map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(o.E,{className:"h-9 w-9 rounded-full"})," ",(0,n.jsxs)("div",{className:"ml-4 space-y-1",children:[(0,n.jsx)(o.E,{className:"h-4 w-[120px]"})," ",(0,n.jsx)(o.E,{className:"h-4 w-[160px]"})," "]}),(0,n.jsx)(o.E,{className:"ml-auto h-4 w-[80px]"})," "]},t))})})]})}r(22576);var i=r(19761);let c={...s},m="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;a=new Proxy(function(){return(0,n.jsx)(d,{"data-sentry-element":"RecentSalesSkeleton","data-sentry-component":"Loading","data-sentry-source-file":"loading.tsx"})},{apply:(e,t,r)=>{let a,s,n;try{let e=m?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/overview/@sales",componentType:"Loading",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let p=void 0,u=void 0,x=void 0,y=a},66696:(e,t,r)=>{Promise.resolve().then(r.bind(r,22295))},67529:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,ScrollArea:()=>o});var a=r(24443);r(60222);var s=r(54889),n=r(72595);function o({className:e,children:t,...r}){return(0,a.jsxs)(s.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",e),...r,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,a.jsx)(s.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:t}),(0,a.jsx)(l,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,a.jsx)(s.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function l({className:e,orientation:t="vertical",...r}){return(0,a.jsx)(s.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...r,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,a.jsx)(s.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},67767:(e,t,r)=>{Promise.resolve().then(r.bind(r,52433))},68829:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f22efd92a3b59d43d3d12fe480e87910640e1db9e":()=>s.y,"7f7b45347fd50452ee6e2850ded1018991a7b086f0":()=>a.at,"7f909588461cb83e855875f4939d6f26e4ae81b49e":()=>a.ot,"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261":()=>a.ai});var a=r(27235),s=r(41372)},69872:(e,t,r)=>{Promise.resolve().then(r.bind(r,48662))},69900:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(24443),s=r(91476),n=r(51092);function o({error:e}){return(0,a.jsxs)(s.Fc,{variant:"destructive","data-sentry-element":"Alert","data-sentry-component":"SalesError","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(n.A,{className:"h-4 w-4","data-sentry-element":"IconAlertCircle","data-sentry-source-file":"error.tsx"}),(0,a.jsx)(s.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,a.jsxs)(s.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load sales data: ",e.message]})]})}},71626:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>y,generateImageMetadata:()=>u,generateMetadata:()=>p,generateViewport:()=>x});var s=r(63033),n=r(78869),o=r(47923),l=r(22295),d=r(19761);async function i(){return await await (0,o.c)(1e3),(0,n.jsx)(l.BarGraph,{"data-sentry-element":"BarGraph","data-sentry-component":"BarStats","data-sentry-source-file":"page.tsx"})}let c={...s},m="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;a=new Proxy(i,{apply:(e,t,r)=>{let a,s,n;try{let e=m?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return d.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/overview/@bar_stats",componentType:"Page",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let p=void 0,u=void 0,x=void 0,y=a},72921:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>y,generateImageMetadata:()=>u,generateMetadata:()=>p,generateViewport:()=>x});var s=r(63033),n=r(78869),o=r(47923),l=r(81893),d=r(19761);async function i(){return await (0,o.c)(1e3),(0,n.jsx)(l.PieGraph,{"data-sentry-element":"PieGraph","data-sentry-component":"Stats","data-sentry-source-file":"page.tsx"})}let c={...s},m="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;a=new Proxy(i,{apply:(e,t,r)=>{let a,s,n;try{let e=m?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return d.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/overview/@pie_stats",componentType:"Page",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let p=void 0,u=void 0,x=void 0,y=a},77777:(e,t,r)=>{"use strict";r.d(t,{BarGraph:()=>u});var a=r(24443),s=r(60222),n=r(46567),o=r(72698),l=r(55041),d=r(33612),i=r(32218),c=r(63726);let m=[{date:"2024-04-01",desktop:222,mobile:150},{date:"2024-04-02",desktop:97,mobile:180},{date:"2024-04-03",desktop:167,mobile:120},{date:"2024-04-04",desktop:242,mobile:260},{date:"2024-04-05",desktop:373,mobile:290},{date:"2024-04-06",desktop:301,mobile:340},{date:"2024-04-07",desktop:245,mobile:180},{date:"2024-04-08",desktop:409,mobile:320},{date:"2024-04-09",desktop:59,mobile:110},{date:"2024-04-10",desktop:261,mobile:190},{date:"2024-04-11",desktop:327,mobile:350},{date:"2024-04-12",desktop:292,mobile:210},{date:"2024-04-13",desktop:342,mobile:380},{date:"2024-04-14",desktop:137,mobile:220},{date:"2024-04-15",desktop:120,mobile:170},{date:"2024-04-16",desktop:138,mobile:190},{date:"2024-04-17",desktop:446,mobile:360},{date:"2024-04-18",desktop:364,mobile:410},{date:"2024-04-19",desktop:243,mobile:180},{date:"2024-04-20",desktop:89,mobile:150},{date:"2024-04-21",desktop:137,mobile:200},{date:"2024-04-22",desktop:224,mobile:170},{date:"2024-04-23",desktop:138,mobile:230},{date:"2024-04-24",desktop:387,mobile:290},{date:"2024-04-25",desktop:215,mobile:250},{date:"2024-04-26",desktop:75,mobile:130},{date:"2024-04-27",desktop:383,mobile:420},{date:"2024-04-28",desktop:122,mobile:180},{date:"2024-04-29",desktop:315,mobile:240},{date:"2024-04-30",desktop:454,mobile:380},{date:"2024-05-01",desktop:165,mobile:220},{date:"2024-05-02",desktop:293,mobile:310},{date:"2024-05-03",desktop:247,mobile:190},{date:"2024-05-04",desktop:385,mobile:420},{date:"2024-05-05",desktop:481,mobile:390},{date:"2024-05-06",desktop:498,mobile:520},{date:"2024-05-07",desktop:388,mobile:300},{date:"2024-05-08",desktop:149,mobile:210},{date:"2024-05-09",desktop:227,mobile:180},{date:"2024-05-10",desktop:293,mobile:330},{date:"2024-05-11",desktop:335,mobile:270},{date:"2024-05-12",desktop:197,mobile:240},{date:"2024-05-13",desktop:197,mobile:160},{date:"2024-05-14",desktop:448,mobile:490},{date:"2024-05-15",desktop:473,mobile:380},{date:"2024-05-16",desktop:338,mobile:400},{date:"2024-05-17",desktop:499,mobile:420},{date:"2024-05-18",desktop:315,mobile:350},{date:"2024-05-19",desktop:235,mobile:180},{date:"2024-05-20",desktop:177,mobile:230},{date:"2024-05-21",desktop:82,mobile:140},{date:"2024-05-22",desktop:81,mobile:120},{date:"2024-05-23",desktop:252,mobile:290},{date:"2024-05-24",desktop:294,mobile:220},{date:"2024-05-25",desktop:201,mobile:250},{date:"2024-05-26",desktop:213,mobile:170},{date:"2024-05-27",desktop:420,mobile:460},{date:"2024-05-28",desktop:233,mobile:190},{date:"2024-05-29",desktop:78,mobile:130},{date:"2024-05-30",desktop:340,mobile:280},{date:"2024-05-31",desktop:178,mobile:230},{date:"2024-06-01",desktop:178,mobile:200},{date:"2024-06-02",desktop:470,mobile:410},{date:"2024-06-03",desktop:103,mobile:160},{date:"2024-06-04",desktop:439,mobile:380},{date:"2024-06-05",desktop:88,mobile:140},{date:"2024-06-06",desktop:294,mobile:250},{date:"2024-06-07",desktop:323,mobile:370},{date:"2024-06-08",desktop:385,mobile:320},{date:"2024-06-09",desktop:438,mobile:480},{date:"2024-06-10",desktop:155,mobile:200},{date:"2024-06-11",desktop:92,mobile:150},{date:"2024-06-12",desktop:492,mobile:420},{date:"2024-06-13",desktop:81,mobile:130},{date:"2024-06-14",desktop:426,mobile:380},{date:"2024-06-15",desktop:307,mobile:350},{date:"2024-06-16",desktop:371,mobile:310},{date:"2024-06-17",desktop:475,mobile:520},{date:"2024-06-18",desktop:107,mobile:170},{date:"2024-06-19",desktop:341,mobile:290},{date:"2024-06-20",desktop:408,mobile:450},{date:"2024-06-21",desktop:169,mobile:210},{date:"2024-06-22",desktop:317,mobile:270},{date:"2024-06-23",desktop:480,mobile:530},{date:"2024-06-24",desktop:132,mobile:180},{date:"2024-06-25",desktop:141,mobile:190},{date:"2024-06-26",desktop:434,mobile:380},{date:"2024-06-27",desktop:448,mobile:490},{date:"2024-06-28",desktop:149,mobile:200},{date:"2024-06-29",desktop:103,mobile:160},{date:"2024-06-30",desktop:446,mobile:400}],p={views:{label:"Page Views"},desktop:{label:"Desktop",color:"var(--primary)"},mobile:{label:"Mobile",color:"var(--primary)"},error:{label:"Error",color:"var(--primary)"}};function u(){let[e,t]=s.useState("desktop"),r=s.useMemo(()=>({desktop:m.reduce((e,t)=>e+t.desktop,0),mobile:m.reduce((e,t)=>e+t.mobile,0)}),[]),[u,x]=s.useState(!1);return(s.useEffect(()=>{x(!0)},[]),s.useEffect(()=>{if("error"===e)throw Error("Mocking Error")},[e]),u)?(0,a.jsxs)(i.Zp,{className:"@container/card !pt-3","data-sentry-element":"Card","data-sentry-component":"BarGraph","data-sentry-source-file":"bar-graph.tsx",children:[(0,a.jsxs)(i.aR,{className:"flex flex-col items-stretch space-y-0 border-b !p-0 sm:flex-row","data-sentry-element":"CardHeader","data-sentry-source-file":"bar-graph.tsx",children:[(0,a.jsxs)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 !py-0",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"bar-graph.tsx",children:"Bar Chart - Interactive"}),(0,a.jsxs)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"bar-graph.tsx",children:[(0,a.jsx)("span",{className:"hidden @[540px]/card:block",children:"Total for the last 3 months"}),(0,a.jsx)("span",{className:"@[540px]/card:hidden",children:"Last 3 months"})]})]}),(0,a.jsx)("div",{className:"flex",children:["desktop","mobile","error"].map(s=>s&&0!==r[s]?(0,a.jsxs)("button",{"data-active":e===s,className:"data-[active=true]:bg-primary/5 hover:bg-primary/5 relative flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left transition-colors duration-200 even:border-l sm:border-t-0 sm:border-l sm:px-8 sm:py-6",onClick:()=>t(s),children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:p[s].label}),(0,a.jsx)("span",{className:"text-lg leading-none font-bold sm:text-3xl",children:r[s]?.toLocaleString()})]},s):null)})]}),(0,a.jsx)(i.Wu,{className:"px-2 pt-4 sm:px-6 sm:pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"bar-graph.tsx",children:(0,a.jsx)(c.at,{config:p,className:"aspect-auto h-[250px] w-full","data-sentry-element":"ChartContainer","data-sentry-source-file":"bar-graph.tsx",children:(0,a.jsxs)(n.E,{data:m,margin:{left:12,right:12},"data-sentry-element":"BarChart","data-sentry-source-file":"bar-graph.tsx",children:[(0,a.jsx)("defs",{"data-sentry-element":"defs","data-sentry-source-file":"bar-graph.tsx",children:(0,a.jsxs)("linearGradient",{id:"fillBar",x1:"0",y1:"0",x2:"0",y2:"1","data-sentry-element":"linearGradient","data-sentry-source-file":"bar-graph.tsx",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"var(--primary)",stopOpacity:.8,"data-sentry-element":"stop","data-sentry-source-file":"bar-graph.tsx"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"var(--primary)",stopOpacity:.2,"data-sentry-element":"stop","data-sentry-source-file":"bar-graph.tsx"})]})}),(0,a.jsx)(o.d,{vertical:!1,"data-sentry-element":"CartesianGrid","data-sentry-source-file":"bar-graph.tsx"}),(0,a.jsx)(l.W,{dataKey:"date",tickLine:!1,axisLine:!1,tickMargin:8,minTickGap:32,tickFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric"}),"data-sentry-element":"XAxis","data-sentry-source-file":"bar-graph.tsx"}),(0,a.jsx)(c.II,{cursor:{fill:"var(--primary)",opacity:.1},content:(0,a.jsx)(c.Nt,{className:"w-[150px]",nameKey:"views",labelFormatter:e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}),"data-sentry-element":"ChartTooltip","data-sentry-source-file":"bar-graph.tsx"}),(0,a.jsx)(d.y,{dataKey:e,fill:"url(#fillBar)",radius:[4,4,0,0],"data-sentry-element":"Bar","data-sentry-source-file":"bar-graph.tsx"})]})})})]}):null}},78267:(e,t,r)=>{"use strict";r.d(t,{AreaGraph:()=>a});let a=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call AreaGraph() from the server but AreaGraph is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\area-graph.tsx","AreaGraph")},81893:(e,t,r)=>{"use strict";r.d(t,{PieGraph:()=>a});let a=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call PieGraph() from the server but PieGraph is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\pie-graph.tsx","PieGraph")},83391:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(24443),s=r(91476),n=r(51092);function o({error:e}){return(0,a.jsxs)(s.Fc,{variant:"destructive","data-sentry-element":"Alert","data-sentry-component":"AreaStatsError","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(n.A,{className:"h-4 w-4","data-sentry-element":"IconAlertCircle","data-sentry-source-file":"error.tsx"}),(0,a.jsx)(s.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,a.jsxs)(s.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load area statistics: ",e.message]})]})}},83829:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(78869);r(22576);var s=r(89371);function n({children:e,scrollable:t=!0}){return(0,a.jsx)(a.Fragment,{children:t?(0,a.jsx)(s.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,a.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):(0,a.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}},87722:(e,t,r)=>{Promise.resolve().then(r.bind(r,97116))},88862:(e,t,r)=>{Promise.resolve().then(r.bind(r,47386))},89371:(e,t,r)=>{"use strict";r.d(t,{ScrollArea:()=>s});var a=r(91611);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call ScrollArea() from the server but ScrollArea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx","ScrollArea");(0,a.registerClientReference)(function(){throw Error("Attempted to call ScrollBar() from the server but ScrollBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx","ScrollBar")},91476:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>i,XL:()=>d});var a=r(24443);r(60222);var s=r(29693),n=r(72595);let o=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(o({variant:t}),e),...r,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},93590:(e,t,r)=>{Promise.resolve().then(r.bind(r,59792))},93814:(e,t,r)=>{Promise.resolve().then(r.bind(r,67529))},93888:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>j,generateImageMetadata:()=>v,generateMetadata:()=>g,generateViewport:()=>b});var s=r(63033),n=r(78869),o=r(83829);r(22576);var l=r(41488),d=r(31963),i=r(19557);let c=(0,d.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function m({className:e,variant:t,asChild:r=!1,...a}){let s=r?l.DX:"span";return(0,n.jsx)(s,{"data-slot":"badge",className:(0,i.cn)(c({variant:t}),e),...a,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}var p=r(96756),u=r(11468),x=r(85487),y=r(19761);let f={...s},h="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;a=new Proxy(function({sales:e,pie_stats:t,bar_stats:r,area_stats:a}){return(0,n.jsx)(o.A,{"data-sentry-element":"PageContainer","data-sentry-component":"OverViewLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)("div",{className:"flex flex-1 flex-col space-y-2",children:[(0,n.jsx)("div",{className:"flex items-center justify-between space-y-2",children:(0,n.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"Hi, Welcome back \uD83D\uDC4B"})}),(0,n.jsxs)("div",{className:"*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4",children:[(0,n.jsxs)(p.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-source-file":"layout.tsx",children:[(0,n.jsxs)(p.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(p.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"layout.tsx",children:"Total Revenue"}),(0,n.jsx)(p.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl","data-sentry-element":"CardTitle","data-sentry-source-file":"layout.tsx",children:"$1,250.00"}),(0,n.jsx)(p.X9,{"data-sentry-element":"CardAction","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(m,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(u.A,{"data-sentry-element":"IconTrendingUp","data-sentry-source-file":"layout.tsx"}),"+12.5%"]})})]}),(0,n.jsxs)(p.wL,{className:"flex-col items-start gap-1.5 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"layout.tsx",children:[(0,n.jsxs)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Trending up this month ",(0,n.jsx)(u.A,{className:"size-4","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"layout.tsx"})]}),(0,n.jsx)("div",{className:"text-muted-foreground",children:"Visitors for the last 6 months"})]})]}),(0,n.jsxs)(p.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-source-file":"layout.tsx",children:[(0,n.jsxs)(p.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(p.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"layout.tsx",children:"New Customers"}),(0,n.jsx)(p.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl","data-sentry-element":"CardTitle","data-sentry-source-file":"layout.tsx",children:"1,234"}),(0,n.jsx)(p.X9,{"data-sentry-element":"CardAction","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(m,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(x.A,{"data-sentry-element":"IconTrendingDown","data-sentry-source-file":"layout.tsx"}),"-20%"]})})]}),(0,n.jsxs)(p.wL,{className:"flex-col items-start gap-1.5 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"layout.tsx",children:[(0,n.jsxs)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Down 20% this period ",(0,n.jsx)(x.A,{className:"size-4","data-sentry-element":"IconTrendingDown","data-sentry-source-file":"layout.tsx"})]}),(0,n.jsx)("div",{className:"text-muted-foreground",children:"Acquisition needs attention"})]})]}),(0,n.jsxs)(p.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-source-file":"layout.tsx",children:[(0,n.jsxs)(p.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(p.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"layout.tsx",children:"Active Accounts"}),(0,n.jsx)(p.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl","data-sentry-element":"CardTitle","data-sentry-source-file":"layout.tsx",children:"45,678"}),(0,n.jsx)(p.X9,{"data-sentry-element":"CardAction","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(m,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(u.A,{"data-sentry-element":"IconTrendingUp","data-sentry-source-file":"layout.tsx"}),"+12.5%"]})})]}),(0,n.jsxs)(p.wL,{className:"flex-col items-start gap-1.5 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"layout.tsx",children:[(0,n.jsxs)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Strong user retention ",(0,n.jsx)(u.A,{className:"size-4","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"layout.tsx"})]}),(0,n.jsx)("div",{className:"text-muted-foreground",children:"Engagement exceed targets"})]})]}),(0,n.jsxs)(p.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-source-file":"layout.tsx",children:[(0,n.jsxs)(p.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(p.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"layout.tsx",children:"Growth Rate"}),(0,n.jsx)(p.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl","data-sentry-element":"CardTitle","data-sentry-source-file":"layout.tsx",children:"4.5%"}),(0,n.jsx)(p.X9,{"data-sentry-element":"CardAction","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(m,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(u.A,{"data-sentry-element":"IconTrendingUp","data-sentry-source-file":"layout.tsx"}),"+4.5%"]})})]}),(0,n.jsxs)(p.wL,{className:"flex-col items-start gap-1.5 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"layout.tsx",children:[(0,n.jsxs)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Steady performance increase"," ",(0,n.jsx)(u.A,{className:"size-4","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"layout.tsx"})]}),(0,n.jsx)("div",{className:"text-muted-foreground",children:"Meets growth projections"})]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7",children:[(0,n.jsx)("div",{className:"col-span-4",children:r}),(0,n.jsx)("div",{className:"col-span-4 md:col-span-3",children:e}),(0,n.jsx)("div",{className:"col-span-4",children:a}),(0,n.jsx)("div",{className:"col-span-4 md:col-span-3",children:t})]})]})})},{apply:(e,t,r)=>{let a,s,n;try{let e=h?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return y.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/overview",componentType:"Layout",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let g=void 0,v=void 0,b=void 0,j=a},96756:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,X9:()=>i,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>m});var a=r(78869);r(22576);var s=r(19557);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},97057:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\overview\\\\@bar_stats\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\error.tsx","default")},97116:(e,t,r)=>{"use strict";r.d(t,{PieGraph:()=>u});var a=r(24443),s=r(60222),n=r(58246),o=r(35035),l=r(1024),d=r(31347),i=r(32218),c=r(63726);let m=[{browser:"chrome",visitors:275,fill:"var(--primary)"},{browser:"safari",visitors:200,fill:"var(--primary-light)"},{browser:"firefox",visitors:287,fill:"var(--primary-lighter)"},{browser:"edge",visitors:173,fill:"var(--primary-dark)"},{browser:"other",visitors:190,fill:"var(--primary-darker)"}],p={visitors:{label:"Visitors"},chrome:{label:"Chrome",color:"var(--primary)"},safari:{label:"Safari",color:"var(--primary)"},firefox:{label:"Firefox",color:"var(--primary)"},edge:{label:"Edge",color:"var(--primary)"},other:{label:"Other",color:"var(--primary)"}};function u(){let e=s.useMemo(()=>m.reduce((e,t)=>e+t.visitors,0),[]);return(0,a.jsxs)(i.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-component":"PieGraph","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"pie-graph.tsx",children:"Pie Chart - Donut with Text"}),(0,a.jsxs)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsx)("span",{className:"hidden @[540px]/card:block",children:"Total visitors by browser for the last 6 months"}),(0,a.jsx)("span",{className:"@[540px]/card:hidden",children:"Browser distribution"})]})]}),(0,a.jsx)(i.Wu,{className:"px-2 pt-4 sm:px-6 sm:pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"pie-graph.tsx",children:(0,a.jsx)(c.at,{config:p,className:"mx-auto aspect-square h-[250px]","data-sentry-element":"ChartContainer","data-sentry-source-file":"pie-graph.tsx",children:(0,a.jsxs)(o.r,{"data-sentry-element":"PieChart","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsx)("defs",{"data-sentry-element":"defs","data-sentry-source-file":"pie-graph.tsx",children:["chrome","safari","firefox","edge","other"].map((e,t)=>(0,a.jsxs)("linearGradient",{id:`fill${e}`,x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"var(--primary)",stopOpacity:1-.15*t}),(0,a.jsx)("stop",{offset:"100%",stopColor:"var(--primary)",stopOpacity:.8-.15*t})]},e))}),(0,a.jsx)(c.II,{cursor:!1,content:(0,a.jsx)(c.Nt,{hideLabel:!0}),"data-sentry-element":"ChartTooltip","data-sentry-source-file":"pie-graph.tsx"}),(0,a.jsx)(l.F,{data:m.map(e=>({...e,fill:`url(#fill${e.browser})`})),dataKey:"visitors",nameKey:"browser",innerRadius:60,strokeWidth:2,stroke:"var(--background)","data-sentry-element":"Pie","data-sentry-source-file":"pie-graph.tsx",children:(0,a.jsx)(d.J,{content:({viewBox:t})=>{if(t&&"cx"in t&&"cy"in t)return(0,a.jsxs)("text",{x:t.cx,y:t.cy,textAnchor:"middle",dominantBaseline:"middle",children:[(0,a.jsx)("tspan",{x:t.cx,y:t.cy,className:"fill-foreground text-3xl font-bold",children:e.toLocaleString()}),(0,a.jsx)("tspan",{x:t.cx,y:(t.cy||0)+24,className:"fill-muted-foreground text-sm",children:"Total Visitors"})]})},"data-sentry-element":"Label","data-sentry-source-file":"pie-graph.tsx"})})]})})}),(0,a.jsxs)(i.wL,{className:"flex-col gap-2 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 leading-none font-medium",children:["Chrome leads with"," ",(m[0].visitors/e*100).toFixed(1),"%"," ",(0,a.jsx)(n.A,{className:"h-4 w-4","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"pie-graph.tsx"})]}),(0,a.jsx)("div",{className:"text-muted-foreground leading-none",children:"Based on data from January - June 2024"})]})]})}},98873:(e,t,r)=>{Promise.resolve().then(r.bind(r,20361))}};
//# sourceMappingURL=8968.js.map