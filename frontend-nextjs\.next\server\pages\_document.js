try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="74c448c9-7331-43c9-848b-6b3d16e10069",e._sentryDebugIdIdentifier="sentry-dbid-74c448c9-7331-43c9-848b-6b3d16e10069")}catch(e){}"use strict";(()=>{var e={};e.id=3220,e.ids=[3220],e.modules={8732:e=>{e.exports=require("react/jsx-runtime")},33873:e=>{e.exports=require("path")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},56472:e=>{e.exports=require("@opentelemetry/api")},82015:e=>{e.exports=require("react")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2665],()=>t(52665));module.exports=s})();
//# sourceMappingURL=_document.js.map