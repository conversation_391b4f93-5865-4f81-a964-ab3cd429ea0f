{"version": 3, "file": "../pages/_document.js", "mappings": "ubAAA,kDCAA,qCCAA,qFCAA,mDCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"react/jsx-runtime\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/pages.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"@opentelemetry/api\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"react\""], "sourcesContent": ["module.exports = require(\"react/jsx-runtime\");", "module.exports = require(\"path\");", "module.exports = require(\"next/dist/compiled/next-server/pages.runtime.prod.js\");", "module.exports = require(\"@opentelemetry/api\");", "module.exports = require(\"react\");"], "names": [], "sourceRoot": ""}