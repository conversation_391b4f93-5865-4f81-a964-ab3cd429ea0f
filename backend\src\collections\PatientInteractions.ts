import type { CollectionConfig } from 'payload'

export const PatientInteractions: CollectionConfig = {
  slug: 'patient-interactions',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'patient', 'interactionType', 'staffMember', 'timestamp', 'status'],
    group: 'CRM',
  },
  access: {
    // Read: All authenticated users can read interaction data based on role
    read: ({ req: { user } }) => {
      if (!user) return false;

      // Admin can read all interactions
      if (user.role === 'admin') return true;

      // Doctor can read interactions for their patients and medical-related interactions
      if (user.role === 'doctor') {
        return {
          or: [
            {
              staffMember: {
                equals: user.id,
              },
            },
            {
              interactionType: {
                in: ['consultation-note', 'treatment-discussion', 'in-person-visit'],
              },
            },
          ],
        } as any;
      }

      // Front-desk can read scheduling and billing related interactions
      if (user.role === 'front-desk') {
        return {
          interactionType: {
            in: ['phone-call', 'email', 'billing-inquiry'],
          },
        } as any;
      }
      
      return false;
    },

    // Create: Admin and staff can create interactions
    create: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'doctor' || user.role === 'front-desk';
    },

    // Update: Users can update their own interactions, Admin can update all
    update: ({ req: { user } }) => {
      if (!user) return false;
      
      if (user.role === 'admin') return true;
      
      // Staff can only update their own interactions
      return {
        staffMember: {
          equals: user.id,
        },
      } as any;
    },

    // Delete: Only Admin can delete interactions
    delete: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin';
    },
  },
  fields: [
    // 关联患者
    {
      name: 'patient',
      type: 'relationship',
      relationTo: 'patients',
      required: true,
      label: '患者',
      admin: {
        description: '选择相关患者',
      },
    },

    // 互动类型
    {
      name: 'interactionType',
      type: 'select',
      required: true,
      options: [
        {
          label: '电话通话',
          value: 'phone-call',
        },
        {
          label: '邮件沟通',
          value: 'email',
        },
        {
          label: '咨询记录',
          value: 'consultation-note',
        },
        {
          label: '到院就诊',
          value: 'in-person-visit',
        },
        {
          label: '治疗讨论',
          value: 'treatment-discussion',
        },
        {
          label: '账单咨询',
          value: 'billing-inquiry',
        },
      ],
      label: '互动类型',
      admin: {
        description: '选择互动的类型',
      },
    },

    // 负责工作人员
    {
      name: 'staffMember',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      label: '负责工作人员',
      admin: {
        description: '处理此次互动的工作人员',
      },
    },

    // 互动时间
    {
      name: 'timestamp',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
      label: '互动时间',
      admin: {
        description: '互动发生的时间',
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },

    // 互动标题
    {
      name: 'title',
      type: 'text',
      required: true,
      maxLength: 200,
      label: '互动标题',
      admin: {
        description: '简要描述此次互动的主题',
      },
    },

    // 详细记录
    {
      name: 'notes',
      type: 'richText',
      required: true,
      label: '详细记录',
      admin: {
        description: '详细记录互动内容、讨论要点等',
      },
    },

    // 互动结果
    {
      name: 'outcome',
      type: 'text',
      maxLength: 500,
      label: '互动结果',
      admin: {
        description: '记录互动的结果或解决方案',
      },
    },

    // 是否需要跟进
    {
      name: 'followUpRequired',
      type: 'checkbox',
      defaultValue: false,
      label: '需要跟进',
      admin: {
        description: '此次互动是否需要后续跟进',
      },
    },

    // 跟进日期
    {
      name: 'followUpDate',
      type: 'date',
      label: '跟进日期',
      admin: {
        description: '计划跟进的日期',
        condition: (data) => data.followUpRequired === true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },

    // 优先级
    {
      name: 'priority',
      type: 'select',
      required: true,
      options: [
        {
          label: '低',
          value: 'low',
        },
        {
          label: '中',
          value: 'medium',
        },
        {
          label: '高',
          value: 'high',
        },
      ],
      defaultValue: 'medium',
      label: '优先级',
      admin: {
        description: '此次互动的优先级',
      },
    },

    // 状态
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        {
          label: '开放',
          value: 'open',
        },
        {
          label: '进行中',
          value: 'in-progress',
        },
        {
          label: '已解决',
          value: 'resolved',
        },
        {
          label: '已关闭',
          value: 'closed',
        },
      ],
      defaultValue: 'open',
      label: '状态',
      admin: {
        description: '互动的当前状态',
      },
    },

    // 关联预约（可选）
    {
      name: 'relatedAppointment',
      type: 'relationship',
      relationTo: 'appointments',
      label: '关联预约',
      admin: {
        description: '如果此互动与特定预约相关，请选择',
      },
    },

    // 关联账单（可选）
    {
      name: 'relatedBill',
      type: 'relationship',
      relationTo: 'bills',
      label: '关联账单',
      admin: {
        description: '如果此互动与特定账单相关，请选择',
      },
    },
  ],
  hooks: {
    // 创建互动后自动创建跟进任务
    afterChange: [
      async ({ doc, operation, req }) => {
        // 只在创建新互动且需要跟进时执行
        if (operation === 'create' && doc.followUpRequired && doc.followUpDate) {
          try {
            // 创建跟进任务
            await req.payload.create({
              collection: 'patient-tasks',
              data: {
                patient: doc.patient,
                title: `跟进：${doc.title}`,
                description: `基于互动记录的跟进任务：${doc.title}`,
                taskType: 'follow-up-call',
                assignedTo: doc.staffMember,
                createdBy: doc.staffMember,
                dueDate: doc.followUpDate,
                priority: doc.priority,
                status: 'pending',
                relatedInteraction: doc.id,
              },
            });
          } catch (error) {
            console.error('Error creating follow-up task:', error);
          }
        }
      },
    ],
  },
}
