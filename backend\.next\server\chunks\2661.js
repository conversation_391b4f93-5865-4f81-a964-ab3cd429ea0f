"use strict";exports.id=2661,exports.ids=[2661],exports.modules={22584:(t,e,a)=>{function n(t){return(e,a={})=>{let n,r=a.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],o=e.match(i);if(!o)return null;let d=o[0],l=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],u=Array.isArray(l)?function(t,e){for(let a=0;a<t.length;a++)if(e(t[a]))return a}(l,t=>t.test(d)):function(t,e){for(let a in t)if(Object.prototype.hasOwnProperty.call(t,a)&&e(t[a]))return a}(l,t=>t.test(d));return n=t.valueCallback?t.valueCallback(u):u,{value:n=a.valueCallback?a.valueCallback(n):n,rest:e.slice(d.length)}}}a.d(e,{A:()=>n})},30804:(t,e,a)=>{a.d(e,{w:()=>r});var n=a(78904);function r(t,e){return"function"==typeof t?t(e):t&&"object"==typeof t&&n._P in t?t[n._P](e):t instanceof Date?new t.constructor(e):new Date(e)}},31456:(t,e,a)=>{a.d(e,{x:()=>r});var n=a(30804);function r(t,...e){let a=n.w.bind(null,t||e.find(t=>"object"==typeof t));return e.map(a)}},37549:(t,e,a)=>{a.d(e,{q:()=>r});let n={};function r(){return n}},42661:(t,e,a)=>{a.r(e),a.d(e,{default:()=>c,zhCN:()=>m});let n={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}};var r=a(89500);let i={date:(0,r.k)({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:(0,r.k)({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:(0,r.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var o=a(43079);function d(t,e,a){let n="eeee p";return(0,o.R)(t,e,a)?n:t.getTime()>e.getTime()?"'下个'"+n:"'上个'"+n}let l={lastWeek:d,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:d,other:"PP p"};var u=a(84246);let s={ordinalNumber:(t,e)=>{let a=Number(t);switch(e?.unit){case"date":return a.toString()+"日";case"hour":return a.toString()+"时";case"minute":return a.toString()+"分";case"second":return a.toString()+"秒";default:return"第 "+a.toString()}},era:(0,u.o)({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:(0,u.o)({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:(0,u.o)({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:(0,u.o)({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:(0,u.o)({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})};var h=a(22584);let m={code:"zh-CN",formatDistance:(t,e,a)=>{let r,i=n[t];if(r="string"==typeof i?i:1===e?i.one:i.other.replace("{{count}}",String(e)),a?.addSuffix)if(a.comparison&&a.comparison>0)return r+"内";else return r+"前";return r},formatLong:i,formatRelative:(t,e,a,n)=>{let r=l[t];return"function"==typeof r?r(e,a,n):r},localize:s,match:{ordinalNumber:(0,a(43416).K)({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:(0,h.A)({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:(0,h.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:(0,h.A)({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:(0,h.A)({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:(0,h.A)({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}},c=m},43079:(t,e,a)=>{a.d(e,{R:()=>i});var n=a(31456),r=a(91520);function i(t,e,a){let[i,o]=(0,n.x)(a?.in,t,e);return+(0,r.k)(i,a)==+(0,r.k)(o,a)}},43416:(t,e,a)=>{a.d(e,{K:()=>n});function n(t){return(e,a={})=>{let n=e.match(t.matchPattern);if(!n)return null;let r=n[0],i=e.match(t.parsePattern);if(!i)return null;let o=t.valueCallback?t.valueCallback(i[0]):i[0];return{value:o=a.valueCallback?a.valueCallback(o):o,rest:e.slice(r.length)}}}},61200:(t,e,a)=>{a.d(e,{a:()=>r});var n=a(30804);function r(t,e){return(0,n.w)(e||t,t)}},78904:(t,e,a)=>{a.d(e,{_P:()=>i,my:()=>n,w4:()=>r});let n=6048e5,r=864e5,i=Symbol.for("constructDateFrom")},84246:(t,e,a)=>{a.d(e,{o:()=>n});function n(t){return(e,a)=>{let n;if("formatting"===(a?.context?String(a.context):"standalone")&&t.formattingValues){let e=t.defaultFormattingWidth||t.defaultWidth,r=a?.width?String(a.width):e;n=t.formattingValues[r]||t.formattingValues[e]}else{let e=t.defaultWidth,r=a?.width?String(a.width):t.defaultWidth;n=t.values[r]||t.values[e]}return n[t.argumentCallback?t.argumentCallback(e):e]}}},89500:(t,e,a)=>{a.d(e,{k:()=>n});function n(t){return (e={})=>{let a=e.width?String(e.width):t.defaultWidth;return t.formats[a]||t.formats[t.defaultWidth]}}},91520:(t,e,a)=>{a.d(e,{k:()=>i});var n=a(37549),r=a(61200);function i(t,e){let a=(0,n.q)(),i=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??a.weekStartsOn??a.locale?.options?.weekStartsOn??0,o=(0,r.a)(t,e?.in),d=o.getDay();return o.setDate(o.getDate()-(7*(d<i)+d-i)),o.setHours(0,0,0,0),o}}};