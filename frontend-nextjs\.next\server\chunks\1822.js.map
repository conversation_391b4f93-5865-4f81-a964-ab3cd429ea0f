{"version": 3, "file": "1822.js", "mappings": "+bAIA,SAASA,EAAc,CAKM,MA8EzBC,EAnFmB,WACrBA,CAAM,KACNC,CAAG,OACHC,CAAK,CACLC,SAAO,CACoB,CALN,EAiFfC,EACJD,GAAAA,CACgB,OADhBA,EACAH,EAAOK,SAAAA,EAAS,OAAhBL,EAAkBM,MAAM,CAAC,CAACC,EAAMC,IAC9BC,KAAKC,GAAG,CAACF,MAAMG,KAAkBD,GAAG,CAACH,MAAoBC,CAAbG,CAAmBJ,EAAAA,CAAAA,EAtFnD,GAwFdI,OAEQX,EAAOY,IAAI,CAAC,QAAOC,mBAAmBZ,GAAK,MAAKC,EAAM,MAAKE,GACnEH,CAAAA,CAAIa,UAAU,CAAC,wBAEX,GAFsCC,KAA8B,GACnE,CAAqC,2EAS9C,0CAFAhB,EAAciB,kBAAkB,CAAG,OAEnC,EAAejB,qFEtGf,uDAAyF,uJCD5EkB,aAAa,mBAAbA,GAiIAC,kBAAkB,mBAAlBA,KAjIN,IAAMD,EAAgB,CAC3B,UACA,QACA,aACA,SACA,SACD,CA2HYC,EAA0C,CACrDC,YAAa,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,CAC1DC,WAAY,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC/CR,KAAM,eACNS,OAAQ,UACRC,WAAY,GACZC,QAAS,EAAE,CACXC,qBAAqB,EACrBC,gBAAiB,GACjBC,QAAS,CAAC,aAAa,CACvBC,qBAAqB,EACrBC,sBAAwB,gDACxBC,uBAAwB,aACxBC,mBAAeC,EACfC,eAAgB,EAAE,CAClB3B,eAAW0B,EACXE,aAAa,CACf,0GC+GgBC,qCAAAA,OAjQS,eACO,WACG,OAkF7BC,EAAiC,CACrC,eACA,OACA,OACA,kBACAJ,EACD,CA4BD,SAASK,EACPnC,CAAoC,EAEpC,YAA0C8B,IAAlC9B,EAAsBoC,OAAO,CAwBvC,SAASC,EAAOC,CAAU,SACpB,KAAa,IAANA,EACFA,EAEQ,KAHa,KAG1B,OAAOA,EACFC,OAAOC,QAAQ,CAACF,GAAKA,EAAIG,IAEjB,UAAb,OAAOH,GAAkB,WAAWI,IAAI,CAACJ,GACpCK,CADwC,QAC/BL,EAAG,IAEdG,GACT,CAqGO,SAASR,EACd,CAyBa,CACbW,CAKC,MAkBmBC,IAjDpB,IA0CI9C,EAqEA+C,EACAC,EAhHJ,KACE/C,CAAG,OACHgD,CAAK,aACLhB,GAAc,CAAK,UACnBiB,GAAW,CAAK,SAChBC,CAAO,WACPC,CAAS,SACTjD,CAAO,OACPD,CAAK,QACLmD,CAAM,MACNC,GAAO,CAAK,OACZC,CAAK,aACLC,CAAW,QACXC,CAAM,mBACNC,CAAiB,aACjBC,EAAc,OAAO,aACrBC,CAAW,eACXC,CAAa,UACbC,EAAW,OAAO,QAClBC,CAAM,WACNC,CAAS,gBACTC,CAAc,cACdC,CAAY,UACZC,CAAQ,CACR,GAAGC,EACQ,CAzBb,EAyCM,SAAEC,CAAO,CAAEC,aAAW,cAAEC,CAAY,eAAExE,CAAa,CAAE,CAAG8C,EAE1DC,EAAIuB,GAAWnD,EAAAA,kBAAkB,CACrC,GAAI,aAAc4B,EAChB9C,CADmB,CACV8C,MACJ,CACL,IAAM0B,EAAW,IAAI1B,EAAE3B,WAAW,IAAK2B,EAAE1B,UAAU,CAAC,CAACqD,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClExD,EAAc2B,EAAE3B,WAAW,CAACsD,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAC/CtE,EAAAA,OAAYyC,EAAAA,EAAEzC,SAAAA,EAAS,OAAXyC,EAAa2B,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClD3E,EAAS,CAAE,GAAG8C,CAAC,CAAE0B,uBAAUrD,YAAad,CAAU,CACpD,CAEA,GAAI,KAAyB,IAAlBN,EACT,MAAM,CADkC,MAClC,cAEL,CAFK,MACJ,yIADI,+DAEN,GAEF,IAAIsB,EAAgC+C,EAAK/C,MAAM,EAAItB,CAGnD,QAAOqE,EAAK/C,MAAM,CAClB,OAAQ+C,EAAaQ,MAAM,CAI3B,IAAMC,EAAkB,uBAAwBxD,EAEhD,GAAIwD,GACF,GAAsB,UAAU,CADb,EACRxD,MAAM,CACf,MAAM,qBAGL,CAHK,yBACepB,EAAlB,IAAsB,gCACpB,kEAFC,+DAGN,EACF,KACK,CAIL,IAAM6E,EAAoBzD,EAC1BA,EAAS,IACP,GAAM,CAAErB,OAAQ+E,CAAC,CAAE,GAAGC,EAAM,CAAGC,EAC/B,OAAOH,EAAkBE,EAC3B,CACF,CAEA,GAAIjB,EAAQ,CACK,QAAQ,CAAnBA,IACFT,GAAO,GAUT,IAAM4B,EARoE,CACxEC,UAAW,CAAEC,SAAU,OAAQ/B,OAAQ,MAAO,EAC9CgC,WAAY,CAAEnF,MAAO,OAAQmD,OAAQ,MAAO,CAC9C,CAKiC,CAACU,EAAO,KAEvCR,EAAQ,CAAE,GAAGA,CAAK,CADH,GACQ2B,CAAW,CAAC,EAErC,IAAMI,EAAcC,CAPlBF,WAAY,QACZ/B,KAAM,OACR,CAKiC,CAACS,EAAO,CACrCuB,GAAe,CAACrC,IAClBA,EAAQqC,CADiB,CAG7B,CAEA,IAAIE,EAAY,GACZC,EAAWnD,EAAOpC,GAClBwF,EAAYpD,EAAOe,GAGvB,GA/OE,CAFoBpD,CAElBA,CA+OeA,CAjP6B,GAG/B,UAAf,EACCmC,KADMnC,IACNmC,EAAgBnC,QACf0F,CARoC5D,IAQlB9B,EARUA,GAQVA,CAAAA,CAAmB,CA6OvC,IAAM2F,EAAkBxD,EAAgBnC,GAAOA,EAAIoC,OAAO,CAAGpC,EAE7D,GAAI,CAAC2F,EAAgB3F,GAAG,CACtB,CADwB,KAClB,qBAIL,CAJK,MACH,8IAA6I4F,KAAKC,SAAS,CAC1JF,IAFE,+DAIN,GAEF,GAAI,CAACA,EAAgBvC,MAAM,EAAI,CAACuC,EAAgB1F,KAAK,CACnD,CADqD,KAC/C,qBAIL,CAJK,MACH,2JAA0J2F,KAAKC,SAAS,CACvKF,IAFE,8DAIN,GAQF,GALA7C,EAAY6C,EAAgB7C,SAAS,CACrCC,EAAa4C,EAAgB5C,UAAU,CACvCY,EAAcA,GAAegC,EAAgBhC,WAAW,CACxD4B,EAAYI,EAAgB3F,GAAG,CAE3B,CAACqD,EACH,GAAI,CADK,EACSoC,GAGX,GAAID,GAAY,CAACC,CAHK,CAGM,CACjC,IAAMK,EAAQN,EAAWG,EAAgB1F,KAAK,CAC9CwF,EAAYjF,KAAKuF,KAAK,CAACJ,EAAgBvC,MAAM,CAAG0C,EAClD,MAAO,GAAI,CAACN,GAAYC,EAAW,CACjC,IAAMK,EAAQL,EAAYE,EAAgBvC,MAAM,CAChDoC,EAAWhF,KAAKuF,KAAK,CAACJ,EAAgB1F,KAAK,CAAG6F,GAChD,MAREN,EAAWG,EAAgB1F,KAAK,CAChCwF,EAAYE,EAAgBvC,MAAM,CAYxC,IAAI4C,EACF,CAAC/C,IAAyB,QAAZC,CAAAA,GAAsB,KAAmB,IAAZA,CAAY,EAAU,CAC/D,CAAClD,CAJLA,EAAM,iBAAOA,EAAmBA,EAAMuF,CAAAA,GAI1BvF,EAAIa,UAAU,CAAC,UAAYb,EAAIa,UAAU,CAAC,WAAU,CAE9DmB,GAAc,EACdgE,GAAS,GAEPjG,EAAOiC,WAAW,EAAE,CACtBA,GAAc,GAGd4C,GACA,CAAC7E,EAAO2B,mBAAmB,EAC3B1B,EAAIiG,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAC9B,CAGAlE,GAAc,GAGhB,IAAMmE,EAAa9D,EAAOnC,GAyMpBkG,EAAWC,OAAOC,MAAM,CAC5BjD,EACI,CACEkD,SAAU,WACVnD,OAAQ,OACRnD,MAAO,OACPuG,KAAM,EACNC,IAAK,EACLC,MAAO,EACPC,OAAQ,YACR5C,iBACAC,CACF,EACA,CAAC,EACLK,EAAc,CAAC,EAAI,CAAEuC,MAAO,aAAc,EAC1CtD,GAGIuD,EACJ,GAAiC,UAAhBnD,EAWb,KAVgB,SAAhBA,EACG,yCAAwCoD,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,UACvDtB,YACAC,YACA3C,aACAC,EACAY,YAAaA,GAAe,GAC5BI,UAAWqC,EAASrC,SAAS,GAC5B,KACF,QAAOL,EAAY,KAAI,EAGT,EAAgCqD,QAAQ,CAC7DX,EAASrC,QAJ4C,CAInC,EAGO,SAAvBqC,EAASrC,SAAS,CAChB,YAAY,QAFdqC,EAASrC,SAAS,CAKlBiD,EAAqCH,EACrC,gBACEI,EACAC,CANuD,kBAMnCd,EAASpC,cAAc,EAAI,UAC/CmD,iBAAkB,4BAClBN,CACF,EACA,CAAC,EAeCO,EA3dR,SAASC,CAQS,EARQ,WACxBtH,CAAM,KACNC,CAAG,aACHgC,CAAW,OACX/B,CAAK,SACLC,CAAO,OACP8C,CAAK,CACL5B,QAAM,CACU,CARQ,EASxB,GAAIY,EACF,MAAO,KAAEhC,EAAK2E,YAAQ7C,EAAWkB,WAAOlB,CAAU,EAGpD,GAAM,QAAEwF,CAAM,MAAEC,CAAI,CAAE,CAAGC,SAxElBA,CAC+B,CACtCvH,CAAyB,CACzB+C,CAAyB,EAFzB,gBAAE9B,CAAW,UAAEqD,CAAQ,CAAe,CAAtC,EAIA,GAAIvB,EAAO,CAET,IAAMyE,EAAkB,qBAClBC,EAAe,EAAE,CACvB,IAAK,IAAIC,EAAQA,EAAQF,EAAgBG,IAAI,CAAC5E,IAC5C0E,EAAAA,EADqDC,EACpC,CAAChF,GAD0C,MACjCgF,CAAK,CAAC,EAAE,GAErC,GAAID,EAAaG,MAAM,CAAE,CACvB,IAAMC,EAA4C,IAA5BtH,KAAKuH,GAAG,IAAIL,GAClC,MAAO,CACLJ,OAAQ/C,EAASyD,MAAM,CAAC,GAAOC,GAAK/G,CAAW,CAAC,EAAE,CAAG4G,GACrDP,KAAM,GACR,CACF,CACA,MAAO,CAAED,OAAQ/C,EAAUgD,KAAM,GAAI,CACvC,OACA,UAAI,OAAOtH,EACF,CAAEqH,OAAQpG,EAAaqG,KAAM,GAAI,EAkBnC,CAAED,OAfM,IACV,IAAIY,IASL,CAACjI,EAAe,EAARA,EAA0B,CAACkI,GAAG,CACpC,GAAO5D,EAAS6D,CADa,GACT,CAAC,GAAOC,GAAKC,IAAM/D,CAAQ,CAACA,EAASsD,MAAM,CAAG,EAAE,GAGzE,CACgBN,KAAM,GAAI,CAC7B,EA+BqCxH,EAAQE,EAAO+C,GAC5CuF,EAAOjB,EAAOO,MAAM,CAAG,EAE7B,MAAO,CACL7E,MAAO,GAAmB,MAATuE,EAAyBvE,EAAV,QAChC2B,OAAQ2C,EACLa,GAAG,CACF,CAACG,EAAGE,IACCpH,EAAO,QAAErB,MAAQC,UAAKE,EAASD,MAAOqI,CAAE,GAAG,IAC5Cf,CAAAA,CAAAA,KAAAA,EAAee,EAAIE,EAAAA,CAAAA,CAAI,CACtBjB,GAENkB,IAAI,CAAC,MAQRzI,IAAKoB,EAAO,QAAErB,MAAQC,UAAKE,EAASD,MAAOqH,CAAM,CAACiB,EAAK,EACzD,CACF,EAwbyC,QACrCxI,EACAC,kBACAgC,EACA/B,MAAOuF,EACPtF,QAASiG,QACTnD,SACA5B,CACF,GA4BA,MAAO,CAAEsH,MAde,CACtB,GAAGvE,CAAI,CACPjB,QAAS8C,EAAS,OAAS9C,gBAC3BU,EACA3D,MAAOuF,EACPpC,OAAQqC,WACR5B,YACAV,EACAG,MAAO,CAAE,GAAG8C,CAAQ,CAAE,GAAGY,CAAgB,EACzChE,MAAOoE,EAAcpE,KAAK,CAC1B2B,OAAQyC,EAAczC,MAAM,CAC5B3E,IAAKuD,GAAe6D,EAAcpH,GAAG,EAGvB2I,KADH,CAAE3G,uBAAaiB,cAAUS,OAAaL,CAAK,CACnC,CACvB,oGC/WauF,qCAAAA,iDA/VN,gBACc,gBACJ,YACW,WAYO,WACA,SACV,eACK,cAGJ,YACG,OAGvBC,EAAY/H,CAAAA,YAAAA,CAAAA,IAAAA,IAAAA,IAAAA,KAAAA,KAAAA,KAAAA,KAAAA,KAAAA,CAAAA,WAAAA,CAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,IAAAA,IAAAA,IAAAA,CAAAA,KAAAA,eAAAA,OAAAA,UAAAA,oBAAAA,CAAAA,EAAAA,YAAAA,CAAAA,CAAAA,CAA6B,CAyB/C,SAASgI,EACPC,CAA2B,CAC3BrF,CAA6B,CAC7BsF,CAAqD,CACrDC,CAA2E,CAC3EC,CAAqC,CACrClH,CAAoB,CACpBmH,CAA8B,EAE9B,IAAMnJ,EAAM+I,MAAAA,EAAAA,KAAAA,EAAAA,EAAK/I,GAAG,CACf+I,GAAOA,CAAG,CAAC,kBAAkB,GAAK/I,IAGvC+I,CAH4C,CAGxC,kBAAkB,CAAG/I,EAEzBqI,CADU,WAAYU,EAAMA,EAAIK,MAAM,GAAKC,QAAQC,OAAO,IACxDC,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC,KACrB,GAAI,EAAKC,aAAa,EAAKV,EAAD,WAAgB,EAAE,GAQxB,SAAS,CAAzBrF,GACFwF,GAAgB,GAEdF,MAAAA,EAAAA,KAAAA,EAAAA,EAAWU,OAAO,CAAE,CAItB,IAAMC,EAAQ,IAAIC,MAAM,QACxBvD,OAAOwD,cAAc,CAACF,EAAO,SAAU,CAAEG,UAAU,EAAOC,MAAOhB,CAAI,GACrE,IAAIiB,GAAY,EACZC,GAAU,EACdjB,EAAUU,OAAO,CAAC,CAChB,GAAGC,CAAK,CACRO,YAAaP,EACbQ,cAAepB,EACfqB,OAAQrB,EACRsB,mBAAoB,IAAML,EAC1BM,qBAAsB,IAAML,EAC5BM,QAAS,KAAO,EAChBC,eAAgB,KACdR,GAAY,EACZL,EAAMa,cAAc,EACtB,EACAC,gBAAiB,KACfR,GAAU,EACVN,EAAMc,eAAe,EACvB,CACF,EACF,EACIxB,MAAAA,EAAAA,KAAAA,EAAAA,EAAsBS,OAAAA,EAAS,CACjCT,EAAqBS,OAAO,CAACX,GAkDjC,GACF,CAEA,SAAS2B,EACP9G,CAAsB,SAEtB,EAAY+G,EAARC,CAAW,CAIN,CAAEhH,CAJO,cAIO,EAIlB,CAAEiH,cAAejH,CAAc,CACxC,CA7IIkH,WAAmBC,qBAAqB,EAAG,EA+I/C,IAAMC,EAAeC,CAAAA,EAAAA,EAAAA,QAAfD,EAAeC,EACnB,GAwBEC,IAzBEF,IAEF,KACEhL,CAAG,QACH2E,CAAM,OACN3B,CAAK,QACLI,CAAM,OACNnD,CAAK,UACL4D,CAAQ,WACRV,CAAS,OACTG,CAAK,CACLM,eAAa,aACbF,CAAW,SACXR,CAAO,aACPlB,CAAW,MACXqB,CAAI,CACJ2F,WAAS,CACTC,sBAAoB,iBACpBC,CAAe,gBACfiC,CAAc,YACdhC,CAAU,QACV3F,CAAM,SACN4H,CAAO,CACP,GAAGjH,EACJ,GAGKkH,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,EACb,IACOvC,IAGDqC,CAHM,GAQRrC,EAAI/I,GALO,CAKD+I,EAAI/I,GAAAA,EAYZ+I,EAAIwC,QAAQ,EAAE,EAEdxC,EACArF,EACAsF,EACAC,EACAC,EACAlH,EACAmH,GAGN,EACA,CACEnJ,EACA0D,EACAsF,EACAC,EACAC,EACAkC,EACApJ,EACAmH,EACD,EAGGqC,EAAMC,CAAAA,EAAAA,EAAAA,YAAAA,EAAaP,EAAcG,GAEvC,MACE,UAACtC,MAAAA,CACE,GAAG5E,CAAI,CACP,GAAGuG,EAAgB9G,EAAc,CAIlCV,QAASA,EACTjD,MAAOA,EACPmD,OAAQA,EACRS,SAAUA,EACV6H,YAAWrI,EAAO,OAAS,IAC3BF,UAAWA,EACXG,MAAOA,EAOPN,MAAOA,EACP2B,OAAQA,EACR3E,IAAKA,EACLwL,IAAKA,EACLhI,OAAQ,IAENsF,EADYa,EAAMQ,UAEhBpB,GAF6B,CAG7BrF,EACAsF,EACAC,EACAC,EACAlH,EACAmH,EAEJ,EACAiC,QAAS,IAEPD,GAAe,GACK,SAAS,CAAzBzH,GAEFwF,GAAgB,GAEdkC,GACFA,EAAQzB,EAEZ,EAHe,CAMrB,GAGF,SAASgC,EAAa,CAMrB,EANqB,gBACpBC,CAAW,CACXxE,eAAa,CAId,CANqB,EAOdrC,EAAO,CACX8G,GAAI,QACJC,YAAa1E,EAAczC,MAAM,CACjCxD,WAAYiG,EAAcpE,KAAK,CAC/B+I,YAAa3E,EAAc2E,WAAW,CACtCC,eAAgB5E,EAAc4E,cAAc,CAC5C,GAAGtB,EAAgBtD,EAAcxD,aAAa,CAAC,SAGjD,GAAmBqI,EAAAA,OAAQ,CAACC,OAAO,EAAE,EAEnCD,OAAQ,CAACC,OAAO,CACd9E,EAAcpH,GAAG,CACjB,GAGK,MAIP,UAACmM,EAAAA,OAAI,WACH,UAACC,OAAAA,CAOCC,IAAI,UAMJC,KAAMlF,EAAczC,MAAM,MAAG7C,EAAYsF,EAAcpH,GAAG,CACzD,GAAG+E,CAAI,EAZN,UACAqC,EAAcpH,GAAG,CACjBoH,EAAczC,MAAM,CACpByC,EAAcpE,KAAK,GAa7B,CAOO,IAAM4F,EAAQqC,CAAAA,EAAAA,EAAAA,CAAAA,SAAAA,EAARrC,CACVF,EAAOwC,KACN,IAAMqB,EAAcC,CAAAA,EAAAA,EAAAA,UAAAA,EAAWC,EAAAA,aAAa,EAItCC,EAAgBF,CAAAA,EAAAA,EAAAA,UAAAA,EAAWG,EAAAA,kBAAkB,EAC7C5M,EAAS6M,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,SAIH/J,EAHlB,IAAMA,EAAIgG,GAAa6D,GAAiBzL,EAAAA,kBAAkB,CACpDsD,EAAW,IAAI1B,EAAE3B,WAAW,IAAK2B,EAAE1B,UAAU,CAAC,CAACqD,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClExD,EAAc2B,EAAE3B,WAAW,CAACsD,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAC/CtE,EAAYyC,MAAAA,CAAAA,EAAAA,EAAEzC,SAAAA,EAAS,OAAXyC,EAAa2B,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClD,MAAO,CAAE,GAAG7B,CAAC,UAAE0B,cAAUrD,YAAad,CAAU,CAClD,EAAG,CAACsM,EAAc,EAEZ,QAAElJ,CAAM,mBAAEC,CAAiB,CAAE,CAAGiF,EAChCM,EAAY6D,CAAAA,EAAAA,EAAAA,MAAAA,EAAOrJ,GAEzBsJ,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR9D,EAAUU,OAAO,CAAGlG,CACtB,EAAG,CAACA,EAAO,EAEX,IAAMyF,EAAuB4D,CAAAA,EAAAA,EAAAA,MAAAA,EAAOpJ,GAEpCqJ,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR7D,EAAqBS,OAAO,CAAGjG,CACjC,EAAG,CAACA,EAAkB,EAEtB,GAAM,CAACa,EAAc4E,EAAgB,CAAG6D,CAAAA,EAAAA,EAAAA,QAAAA,GAAS,GAC3C,CAAC1I,EAAa8G,EAAe,CAAG4B,CAAAA,EAAAA,EAAAA,QAAAA,GAAS,GAEzC,CAAErE,MAAOtB,CAAa,CAAEuB,KAAMqE,CAAO,CAAE,CAAG/K,CAAAA,EAAAA,EAAAA,WAAAA,EAAYyG,EAAO,CACjE5I,cAAAA,EAAAA,OAAa,CACbsE,QAASrE,eACTuE,cACAD,CACF,GAEA,MACE,iCAEI,UAAC2G,EAAAA,CACE,GAAG5D,CAAa,CACjBpF,YAAagL,EAAQhL,WAAW,CAChC0B,YAAasJ,EAAQtJ,WAAW,CAChCL,KAAM2J,EAAQ3J,IAAI,CAClB2F,UAAWA,EACXC,qBAAsBA,EACtBC,gBAAiBA,EACjBiC,eAAgBA,EAChBhC,WAAYT,EAAM1F,KAAK,CACvBwI,IAAKN,IAGR8B,EAAQ/J,QAAQ,CACf,UAAC0I,EAAAA,CACCC,YApDY,CAoDCA,EACbxE,cAAeA,IAEf,OAGV,yOCraK,SAASN,EAAgB,CAc/B,EAd+B,aAC9BtB,CAAQ,WACRC,CAAS,WACT3C,CAAS,YACTC,CAAU,aACVY,CAAW,WACXI,CAAS,CAQV,CAd+B,EAgBxBkJ,EAAWnK,EAAYA,KAAiB0C,EACxC0H,EAAYnK,EAAaA,KAAkB0C,EAE3C0H,EACJF,GAAYC,EAAa,gBAAeD,EAAS,IAAGC,EAAU,IAAK,GASrE,mDAAoDC,EAA5C,QAAoD,8FAA2FC,MAAI,oQAAiQA,MAAI,qEARpYD,EACxB,OACc,YAAdpJ,EACE,IAKufsJ,OAJze,UAAdtJ,EACE,iBACA,QAEygB,sCAAqCJ,EAAY,iBACpkB,4FA9BgBmD,qCAAAA,6JC8BhB,OAAoB,mBAApB,GAjBgBwG,aAAa,mBAAbA,uBAbY,WACN,eAGI,QASnB,SAASA,EAAcC,CAAoB,EAChD,GAAM,OAAE7E,CAAK,CAAE,CAAGzG,CAAAA,EAAAA,EAAAA,WAAAA,EAAYsL,EAAU,CACtCzN,cAAAA,EAAAA,OAAa,CAEbsE,QAAStD,CAAAA,YAAAA,CAAAA,IAAAA,IAAAA,IAAAA,KAAAA,KAAAA,KAAAA,KAAAA,KAAAA,CAAAA,WAAAA,CAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,IAAAA,IAAAA,IAAAA,CAAAA,KAAAA,eAAAA,OAAAA,UAAAA,oBAAAA,CAAAA,EAAAA,YAAAA,CAAAA,CAAAA,CAA6B,GAKxC,IAAK,GAAM,CAAC0M,EAAKzD,EAAM,GAAI1D,OAAOoH,OAAO,CAAC/E,OAAQ,CAClC5G,IAAViI,GACF,IADuB,GAChBrB,CAAK,CAAC8E,EAA0B,CAG3C,MAAO,OAAE9E,CAAM,CACjB,KAEA,EAAeE,EAAAA,KAAK", "sources": ["webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/image-loader.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/api/image.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/route-modules/app-page/vendored/contexts/image-config-context.js", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/image-config.ts", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/get-img-props.ts", "webpack://next-shadcn-dashboard-starter/../../src/client/image-component.tsx", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/image-blur-svg.ts", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/image-external.tsx"], "sourcesContent": ["import type { ImageLoaderPropsWithConfig } from './image-config'\n\nconst DEFAULT_Q = 75\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasLocalMatch } = require('./match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasRemoteMatch } = require('./match-remote-pattern')\n        if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n\n    if (quality && config.qualities && !config.qualities.includes(quality)) {\n      throw new Error(\n        `Invalid quality prop (${quality}) on \\`next/image\\` does not match \\`images.qualities\\` configured in your \\`next.config.js\\`\\n` +\n          `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n  }\n\n  const q =\n    quality ||\n    config.qualities?.reduce((prev, cur) =>\n      Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev\n    ) ||\n    DEFAULT_Q\n\n  return `${config.path}?url=${encodeURIComponent(src)}&w=${width}&q=${q}${\n    src.startsWith('/_next/static/media/') && process.env.NEXT_DEPLOYMENT_ID\n      ? `&dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n      : ''\n  }`\n}\n\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true\n\nexport default defaultLoader\n", "export { default } from '../shared/lib/image-external';\nexport * from '../shared/lib/image-external';\n\n//# sourceMappingURL=image.js.map", "\"use strict\";\nmodule.exports = require('../../module.compiled').vendored['contexts'].ImageConfigContext;\n\n//# sourceMappingURL=image-config-context.js.map", "export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentSecurityPolicy: string\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: Array<URL | RemotePattern>\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n", "import { warnOnce } from './utils/warn-once'\nimport { getImageBlurSvg } from './image-blur-svg'\nimport { imageConfigDefault } from './image-config'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n  ImageLoaderPropsWithConfig,\n} from './image-config'\n\nimport type { CSSProperties, JSX } from 'react'\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n  blurWidth?: number\n  blurHeight?: number\n}\n\nexport interface StaticRequire {\n  default: StaticImageData\n}\n\nexport type StaticImport = StaticRequire | StaticImageData\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'alt' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  alt: string\n  width?: number | `${number}`\n  height?: number | `${number}`\n  fill?: boolean\n  loader?: ImageLoader\n  quality?: number | `${number}`\n  priority?: boolean\n  loading?: LoadingValue\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  overrideSrc?: string\n  /**\n   * @deprecated Use `onLoad` instead.\n   * @see https://nextjs.org/docs/app/api-reference/components/image#onload\n   */\n  onLoadingComplete?: OnLoadingComplete\n  /**\n   * @deprecated Use `fill` prop instead of `layout=\"fill\"` or change import to `next/legacy/image`.\n   * @see https://nextjs.org/docs/api-reference/next/legacy/image\n   */\n  layout?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectFit?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectPosition?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyBoundary?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyRoot?: string\n}\n\nexport type ImgProps = Omit<ImageProps, 'src' | 'loader'> & {\n  loading: LoadingValue\n  width: number | undefined\n  height: number | undefined\n  style: NonNullable<JSX.IntrinsicElements['img']['style']>\n  sizes: string | undefined\n  srcSet: string | undefined\n  src: string\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\n\n// Object-fit values that are not valid background-size values\nconst INVALID_BACKGROUND_SIZE_VALUES = [\n  '-moz-initial',\n  'fill',\n  'none',\n  'scale-down',\n  undefined,\n]\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & {\n  allSizes: number[]\n  output?: 'standalone' | 'export'\n}\n\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (p: ImageLoaderPropsWithConfig) => string\n\nexport type PlaceholderValue = 'blur' | 'empty' | `data:image/${string}`\nexport type OnLoad = React.ReactEventHandler<HTMLImageElement> | undefined\nexport type OnLoadingComplete = (img: HTMLImageElement) => void\n\nexport type PlaceholderStyle = Partial<\n  Pick<\n    CSSProperties,\n    | 'backgroundSize'\n    | 'backgroundPosition'\n    | 'backgroundRepeat'\n    | 'backgroundImage'\n  >\n>\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    !!src &&\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: PlaceholderValue }\n>()\nlet perfObserver: PerformanceObserver | undefined\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'undefined') {\n    return x\n  }\n  if (typeof x === 'number') {\n    return Number.isFinite(x) ? x : NaN\n  }\n  if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n    return parseInt(x, 10)\n  }\n  return NaN\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (typeof width !== 'number') {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\n/**\n * A shared function, used on both client and server, to generate the props for <img>.\n */\nexport function getImgProps(\n  {\n    src,\n    sizes,\n    unoptimized = false,\n    priority = false,\n    loading,\n    className,\n    quality,\n    width,\n    height,\n    fill = false,\n    style,\n    overrideSrc,\n    onLoad,\n    onLoadingComplete,\n    placeholder = 'empty',\n    blurDataURL,\n    fetchPriority,\n    decoding = 'async',\n    layout,\n    objectFit,\n    objectPosition,\n    lazyBoundary,\n    lazyRoot,\n    ...rest\n  }: ImageProps,\n  _state: {\n    defaultLoader: ImageLoaderWithConfig\n    imgConf: ImageConfigComplete\n    showAltText?: boolean\n    blurComplete?: boolean\n  }\n): {\n  props: ImgProps\n  meta: {\n    unoptimized: boolean\n    priority: boolean\n    placeholder: NonNullable<ImageProps['placeholder']>\n    fill: boolean\n  }\n} {\n  const { imgConf, showAltText, blurComplete, defaultLoader } = _state\n  let config: ImageConfig\n  let c = imgConf || imageConfigDefault\n  if ('allSizes' in c) {\n    config = c as ImageConfig\n  } else {\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    const qualities = c.qualities?.sort((a, b) => a - b)\n    config = { ...c, allSizes, deviceSizes, qualities }\n  }\n\n  if (typeof defaultLoader === 'undefined') {\n    throw new Error(\n      'images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config'\n    )\n  }\n  let loader: ImageLoaderWithConfig = rest.loader || defaultLoader\n\n  // Remove property so it's not spread on <img> element\n  delete rest.loader\n  delete (rest as any).srcSet\n\n  // This special value indicates that the user\n  // didn't define a \"loader\" prop or \"loader\" config.\n  const isDefaultLoader = '__next_img_default' in loader\n\n  if (isDefaultLoader) {\n    if (config.loader === 'custom') {\n      throw new Error(\n        `Image with src \"${src}\" is missing \"loader\" prop.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n      )\n    }\n  } else {\n    // The user defined a \"loader\" prop or config.\n    // Since the config object is internal only, we\n    // must not pass it to the user-defined \"loader\".\n    const customImageLoader = loader as ImageLoader\n    loader = (obj) => {\n      const { config: _, ...opts } = obj\n      return customImageLoader(opts)\n    }\n  }\n\n  if (layout) {\n    if (layout === 'fill') {\n      fill = true\n    }\n    const layoutToStyle: Record<string, Record<string, string> | undefined> = {\n      intrinsic: { maxWidth: '100%', height: 'auto' },\n      responsive: { width: '100%', height: 'auto' },\n    }\n    const layoutToSizes: Record<string, string | undefined> = {\n      responsive: '100vw',\n      fill: '100vw',\n    }\n    const layoutStyle = layoutToStyle[layout]\n    if (layoutStyle) {\n      style = { ...style, ...layoutStyle }\n    }\n    const layoutSizes = layoutToSizes[layout]\n    if (layoutSizes && !sizes) {\n      sizes = layoutSizes\n    }\n  }\n\n  let staticSrc = ''\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  let blurWidth: number | undefined\n  let blurHeight: number | undefined\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    if (!staticImageData.height || !staticImageData.width) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n\n    blurWidth = staticImageData.blurWidth\n    blurHeight = staticImageData.blurHeight\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n\n    if (!fill) {\n      if (!widthInt && !heightInt) {\n        widthInt = staticImageData.width\n        heightInt = staticImageData.height\n      } else if (widthInt && !heightInt) {\n        const ratio = widthInt / staticImageData.width\n        heightInt = Math.round(staticImageData.height * ratio)\n      } else if (!widthInt && heightInt) {\n        const ratio = heightInt / staticImageData.height\n        widthInt = Math.round(staticImageData.width * ratio)\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n  if (\n    isDefaultLoader &&\n    !config.dangerouslyAllowSVG &&\n    src.split('?', 1)[0].endsWith('.svg')\n  ) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    unoptimized = true\n  }\n\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n      throw new Error(\n        `Image Optimization using the default loader is not compatible with \\`{ output: 'export' }\\`.\n  Possible solutions:\n    - Remove \\`{ output: 'export' }\\` and run \"next start\" to run server mode including the Image Optimization API.\n    - Configure \\`{ images: { unoptimized: true } }\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      unoptimized = true\n    } else {\n      if (fill) {\n        if (width) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"width\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (height) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"height\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (style?.position && style.position !== 'absolute') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.`\n          )\n        }\n        if (style?.width && style.width !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.`\n          )\n        }\n        if (style?.height && style.height !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.`\n          )\n        }\n      } else {\n        if (typeof widthInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"width\" property.`\n          )\n        } else if (isNaN(widthInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"width\" property. Expected a numeric value in pixels but received \"${width}\".`\n          )\n        }\n        if (typeof heightInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"height\" property.`\n          )\n        } else if (isNaN(heightInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"height\" property. Expected a numeric value in pixels but received \"${height}\".`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/^[\\x00-\\x20]/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/[\\x00-\\x20]$/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n      }\n    }\n    if (!VALID_LOADING_VALUES.includes(loading)) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n          String\n        ).join(',')}.`\n      )\n    }\n    if (priority && loading === 'lazy') {\n      throw new Error(\n        `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n      )\n    }\n    if (\n      placeholder !== 'empty' &&\n      placeholder !== 'blur' &&\n      !placeholder.startsWith('data:image/')\n    ) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"placeholder\" property \"${placeholder}\".`\n      )\n    }\n    if (placeholder !== 'empty') {\n      if (widthInt && heightInt && widthInt * heightInt < 1600) {\n        warnOnce(\n          `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.`\n        )\n      }\n    }\n    if (placeholder === 'blur' && !blurDataURL) {\n      const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n      throw new Error(\n        `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n        Possible solutions:\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n          - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n            ','\n          )} (animated images not supported)\n          - Remove the \"placeholder\" property, effectively no blur effect\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n      )\n    }\n    if ('ref' in rest) {\n      warnOnce(\n        `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.`\n      )\n    }\n\n    if (!unoptimized && !isDefaultLoader) {\n      const urlStr = loader({\n        config,\n        src,\n        width: widthInt || 400,\n        quality: qualityInt || 75,\n      })\n      let url: URL | undefined\n      try {\n        url = new URL(urlStr)\n      } catch (err) {}\n      if (urlStr === src || (url && url.pathname === src && !url.search)) {\n        warnOnce(\n          `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n        )\n      }\n    }\n\n    if (onLoadingComplete) {\n      warnOnce(\n        `Image with src \"${src}\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.`\n      )\n    }\n\n    for (const [legacyKey, legacyValue] of Object.entries({\n      layout,\n      objectFit,\n      objectPosition,\n      lazyBoundary,\n      lazyRoot,\n    })) {\n      if (legacyValue) {\n        warnOnce(\n          `Image with src \"${src}\" has legacy prop \"${legacyKey}\". Did you forget to run the codemod?` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13`\n        )\n      }\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      !perfObserver &&\n      window.PerformanceObserver\n    ) {\n      perfObserver = new PerformanceObserver((entryList) => {\n        for (const entry of entryList.getEntries()) {\n          // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n          const imgSrc = entry?.element?.src || ''\n          const lcpImage = allImgs.get(imgSrc)\n          if (\n            lcpImage &&\n            !lcpImage.priority &&\n            lcpImage.placeholder === 'empty' &&\n            !lcpImage.src.startsWith('data:') &&\n            !lcpImage.src.startsWith('blob:')\n          ) {\n            // https://web.dev/lcp/#measure-lcp-in-javascript\n            warnOnce(\n              `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                `\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority`\n            )\n          }\n        }\n      })\n      try {\n        perfObserver.observe({\n          type: 'largest-contentful-paint',\n          buffered: true,\n        })\n      } catch (err) {\n        // Log error but don't crash the app\n        console.error(err)\n      }\n    }\n  }\n  const imgStyle = Object.assign(\n    fill\n      ? {\n          position: 'absolute',\n          height: '100%',\n          width: '100%',\n          left: 0,\n          top: 0,\n          right: 0,\n          bottom: 0,\n          objectFit,\n          objectPosition,\n        }\n      : {},\n    showAltText ? {} : { color: 'transparent' },\n    style\n  )\n\n  const backgroundImage =\n    !blurComplete && placeholder !== 'empty'\n      ? placeholder === 'blur'\n        ? `url(\"data:image/svg+xml;charset=utf-8,${getImageBlurSvg({\n            widthInt,\n            heightInt,\n            blurWidth,\n            blurHeight,\n            blurDataURL: blurDataURL || '', // assume not undefined\n            objectFit: imgStyle.objectFit,\n          })}\")`\n        : `url(\"${placeholder}\")` // assume `data:image/`\n      : null\n\n  const backgroundSize = !INVALID_BACKGROUND_SIZE_VALUES.includes(\n    imgStyle.objectFit\n  )\n    ? imgStyle.objectFit\n    : imgStyle.objectFit === 'fill'\n      ? '100% 100%' // the background-size equivalent of `fill`\n      : 'cover'\n\n  let placeholderStyle: PlaceholderStyle = backgroundImage\n    ? {\n        backgroundSize,\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage,\n      }\n    : {}\n\n  if (process.env.NODE_ENV === 'development') {\n    if (\n      placeholderStyle.backgroundImage &&\n      placeholder === 'blur' &&\n      blurDataURL?.startsWith('/')\n    ) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      placeholderStyle.backgroundImage = `url(\"${blurDataURL}\")`\n    }\n  }\n\n  const imgAttributes = generateImgAttrs({\n    config,\n    src,\n    unoptimized,\n    width: widthInt,\n    quality: qualityInt,\n    sizes,\n    loader,\n  })\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const props: ImgProps = {\n    ...rest,\n    loading: isLazy ? 'lazy' : loading,\n    fetchPriority,\n    width: widthInt,\n    height: heightInt,\n    decoding,\n    className,\n    style: { ...imgStyle, ...placeholderStyle },\n    sizes: imgAttributes.sizes,\n    srcSet: imgAttributes.srcSet,\n    src: overrideSrc || imgAttributes.src,\n  }\n  const meta = { unoptimized, priority, placeholder, fill }\n  return { props, meta }\n}\n", "'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  forwardRef,\n  use,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport Head from '../shared/lib/head'\nimport { getImgProps } from '../shared/lib/get-img-props'\nimport type {\n  ImageProps,\n  ImgProps,\n  OnLoad,\n  OnLoadingComplete,\n  PlaceholderValue,\n} from '../shared/lib/get-img-props'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n} from '../shared/lib/image-config'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../shared/lib/utils/warn-once'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\nimport { useMergedRef } from './use-merged-ref'\n\n// This is replaced by webpack define plugin\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nexport type { ImageLoaderProps }\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\ntype ImageElementProps = ImgProps & {\n  unoptimized: boolean\n  placeholder: PlaceholderValue\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setShowAltText: (b: boolean) => void\n  sizesInput: string | undefined\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  placeholder: PlaceholderValue,\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void,\n  unoptimized: boolean,\n  sizesInput: string | undefined\n) {\n  const src = img?.src\n  if (!img || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentElement || !img.isConnected) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    if (placeholder !== 'empty') {\n      setBlurComplete(true)\n    }\n    if (onLoadRef?.current) {\n      // Since we don't have the SyntheticEvent here,\n      // we must create one with the same shape.\n      // See https://reactjs.org/docs/events.html\n      const event = new Event('load')\n      Object.defineProperty(event, 'target', { writable: false, value: img })\n      let prevented = false\n      let stopped = false\n      onLoadRef.current({\n        ...event,\n        nativeEvent: event,\n        currentTarget: img,\n        target: img,\n        isDefaultPrevented: () => prevented,\n        isPropagationStopped: () => stopped,\n        persist: () => {},\n        preventDefault: () => {\n          prevented = true\n          event.preventDefault()\n        },\n        stopPropagation: () => {\n          stopped = true\n          event.stopPropagation()\n        },\n      })\n    }\n    if (onLoadingCompleteRef?.current) {\n      onLoadingCompleteRef.current(img)\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const origSrc = new URL(src, 'http://n').searchParams.get('url') || src\n      if (img.getAttribute('data-nimg') === 'fill') {\n        if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n          let widthViewportRatio =\n            img.getBoundingClientRect().width / window.innerWidth\n          if (widthViewportRatio < 0.6) {\n            if (sizesInput === '100vw') {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            } else {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            }\n          }\n        }\n        if (img.parentElement) {\n          const { position } = window.getComputedStyle(img.parentElement)\n          const valid = ['absolute', 'fixed', 'relative']\n          if (!valid.includes(position)) {\n            warnOnce(\n              `Image with src \"${origSrc}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid\n                .map(String)\n                .join(',')}.`\n            )\n          }\n        }\n        if (img.height === 0) {\n          warnOnce(\n            `Image with src \"${origSrc}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`\n          )\n        }\n      }\n\n      const heightModified =\n        img.height.toString() !== img.getAttribute('height')\n      const widthModified = img.width.toString() !== img.getAttribute('width')\n      if (\n        (heightModified && !widthModified) ||\n        (!heightModified && widthModified)\n      ) {\n        warnOnce(\n          `Image with src \"${origSrc}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`\n        )\n      }\n    }\n  })\n}\n\nfunction getDynamicProps(\n  fetchPriority?: string\n): Record<string, string | undefined> {\n  if (Boolean(use)) {\n    // In React 19.0.0 or newer, we must use camelCase\n    // prop to avoid \"Warning: Invalid DOM property\".\n    // See https://github.com/facebook/react/pull/25927\n    return { fetchPriority }\n  }\n  // In React 18.2.0 or older, we must use lowercase prop\n  // to avoid \"Warning: Invalid DOM property\".\n  return { fetchpriority: fetchPriority }\n}\n\nconst ImageElement = forwardRef<HTMLImageElement | null, ImageElementProps>(\n  (\n    {\n      src,\n      srcSet,\n      sizes,\n      height,\n      width,\n      decoding,\n      className,\n      style,\n      fetchPriority,\n      placeholder,\n      loading,\n      unoptimized,\n      fill,\n      onLoadRef,\n      onLoadingCompleteRef,\n      setBlurComplete,\n      setShowAltText,\n      sizesInput,\n      onLoad,\n      onError,\n      ...rest\n    },\n    forwardedRef\n  ) => {\n    const ownRef = useCallback(\n      (img: ImgElementWithDataProp | null) => {\n        if (!img) {\n          return\n        }\n        if (onError) {\n          // If the image has an error before react hydrates, then the error is lost.\n          // The workaround is to wait until the image is mounted which is after hydration,\n          // then we set the src again to trigger the error handler (if there was an error).\n          // eslint-disable-next-line no-self-assign\n          img.src = img.src\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          if (!src) {\n            console.error(`Image is missing required \"src\" property:`, img)\n          }\n          if (img.getAttribute('alt') === null) {\n            console.error(\n              `Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`\n            )\n          }\n        }\n        if (img.complete) {\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }\n      },\n      [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput,\n      ]\n    )\n\n    const ref = useMergedRef(forwardedRef, ownRef)\n\n    return (\n      <img\n        {...rest}\n        {...getDynamicProps(fetchPriority)}\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading={loading}\n        width={width}\n        height={height}\n        decoding={decoding}\n        data-nimg={fill ? 'fill' : '1'}\n        className={className}\n        style={style}\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes={sizes}\n        srcSet={srcSet}\n        src={src}\n        ref={ref}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }}\n        onError={(event) => {\n          // if the real image fails to load, this will ensure \"alt\" is visible\n          setShowAltText(true)\n          if (placeholder !== 'empty') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n    )\n  }\n)\n\nfunction ImagePreload({\n  isAppRouter,\n  imgAttributes,\n}: {\n  isAppRouter: boolean\n  imgAttributes: ImgProps\n}) {\n  const opts = {\n    as: 'image',\n    imageSrcSet: imgAttributes.srcSet,\n    imageSizes: imgAttributes.sizes,\n    crossOrigin: imgAttributes.crossOrigin,\n    referrerPolicy: imgAttributes.referrerPolicy,\n    ...getDynamicProps(imgAttributes.fetchPriority),\n  }\n\n  if (isAppRouter && ReactDOM.preload) {\n    // See https://github.com/facebook/react/pull/26940\n    ReactDOM.preload(\n      imgAttributes.src,\n      // @ts-expect-error TODO: upgrade to `@types/react-dom@18.3.x`\n      opts\n    )\n    return null\n  }\n\n  return (\n    <Head>\n      <link\n        key={\n          '__nimg-' +\n          imgAttributes.src +\n          imgAttributes.srcSet +\n          imgAttributes.sizes\n        }\n        rel=\"preload\"\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n        {...opts}\n      />\n    </Head>\n  )\n}\n\n/**\n * The `Image` component is used to optimize images.\n *\n * Read more: [Next.js docs: `Image`](https://nextjs.org/docs/app/api-reference/components/image)\n */\nexport const Image = forwardRef<HTMLImageElement | null, ImageProps>(\n  (props, forwardedRef) => {\n    const pagesRouter = useContext(RouterContext)\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter\n\n    const configContext = useContext(ImageConfigContext)\n    const config = useMemo(() => {\n      const c = configEnv || configContext || imageConfigDefault\n      const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n      const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n      const qualities = c.qualities?.sort((a, b) => a - b)\n      return { ...c, allSizes, deviceSizes, qualities }\n    }, [configContext])\n\n    const { onLoad, onLoadingComplete } = props\n    const onLoadRef = useRef(onLoad)\n\n    useEffect(() => {\n      onLoadRef.current = onLoad\n    }, [onLoad])\n\n    const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n    useEffect(() => {\n      onLoadingCompleteRef.current = onLoadingComplete\n    }, [onLoadingComplete])\n\n    const [blurComplete, setBlurComplete] = useState(false)\n    const [showAltText, setShowAltText] = useState(false)\n\n    const { props: imgAttributes, meta: imgMeta } = getImgProps(props, {\n      defaultLoader,\n      imgConf: config,\n      blurComplete,\n      showAltText,\n    })\n\n    return (\n      <>\n        {\n          <ImageElement\n            {...imgAttributes}\n            unoptimized={imgMeta.unoptimized}\n            placeholder={imgMeta.placeholder}\n            fill={imgMeta.fill}\n            onLoadRef={onLoadRef}\n            onLoadingCompleteRef={onLoadingCompleteRef}\n            setBlurComplete={setBlurComplete}\n            setShowAltText={setShowAltText}\n            sizesInput={props.sizes}\n            ref={forwardedRef}\n          />\n        }\n        {imgMeta.priority ? (\n          <ImagePreload\n            isAppRouter={isAppRouter}\n            imgAttributes={imgAttributes}\n          />\n        ) : null}\n      </>\n    )\n  }\n)\n", "/**\n * A shared function, used on both client and server, to generate a SVG blur placeholder.\n */\nexport function getImageBlurSvg({\n  widthInt,\n  heightInt,\n  blurWidth,\n  blurHeight,\n  blurDataURL,\n  objectFit,\n}: {\n  widthInt?: number\n  heightInt?: number\n  blurWidth?: number\n  blurHeight?: number\n  blurDataURL: string\n  objectFit?: string\n}): string {\n  const std = 20\n  const svgWidth = blurWidth ? blurWidth * 40 : widthInt\n  const svgHeight = blurHeight ? blurHeight * 40 : heightInt\n\n  const viewBox =\n    svgWidth && svgHeight ? `viewBox='0 0 ${svgWidth} ${svgHeight}'` : ''\n  const preserveAspectRatio = viewBox\n    ? 'none'\n    : objectFit === 'contain'\n      ? 'xMidYMid'\n      : objectFit === 'cover'\n        ? 'xMidYMid slice'\n        : 'none'\n\n  return `%3Csvg xmlns='http://www.w3.org/2000/svg' ${viewBox}%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='${preserveAspectRatio}' style='filter: url(%23b);' href='${blurDataURL}'/%3E%3C/svg%3E`\n}\n", "import type { ImageConfigComplete, ImageLoaderProps } from './image-config'\nimport type { ImageProps, ImageLoader, StaticImageData } from './get-img-props'\n\nimport { getImgProps } from './get-img-props'\nimport { Image } from '../../client/image-component'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\n\n/**\n * For more advanced use cases, you can call `getImageProps()`\n * to get the props that would be passed to the underlying `<img>` element,\n * and instead pass to them to another component, style, canvas, etc.\n *\n * Read more: [Next.js docs: `getImageProps`](https://nextjs.org/docs/app/api-reference/components/image#getimageprops)\n */\nexport function getImageProps(imgProps: ImageProps) {\n  const { props } = getImgProps(imgProps, {\n    defaultLoader,\n    // This is replaced by webpack define plugin\n    imgConf: process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete,\n  })\n  // Normally we don't care about undefined props because we pass to JSX,\n  // but this exported function could be used by the end user for anything\n  // so we delete undefined props to clean it up a little.\n  for (const [key, value] of Object.entries(props)) {\n    if (value === undefined) {\n      delete props[key as keyof typeof props]\n    }\n  }\n  return { props }\n}\n\nexport default Image\n\nexport type { ImageProps, ImageLoaderProps, ImageLoader, StaticImageData }\n"], "names": ["defaultLoader", "config", "src", "width", "quality", "q", "qualities", "reduce", "prev", "cur", "Math", "abs", "DEFAULT_Q", "path", "encodeURIComponent", "startsWith", "process", "__next_img_default", "VALID_LOADERS", "imageConfigDefault", "deviceSizes", "imageSizes", "loader", "loaderFile", "domains", "disableStaticImages", "minimumCacheTTL", "formats", "dangerouslyAllowSVG", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "undefined", "remotePatterns", "unoptimized", "getImgProps", "INVALID_BACKGROUND_SIZE_VALUES", "isStaticRequire", "default", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "_state", "c", "blur<PERSON>idth", "blurHeight", "sizes", "priority", "loading", "className", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "blurDataURL", "fetchPriority", "decoding", "layout", "objectFit", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "allSizes", "sort", "a", "b", "srcSet", "isDefaultLoader", "customImageLoader", "_", "opts", "obj", "layoutStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutSizes", "layoutToSizes", "staticSrc", "widthInt", "heightInt", "isStaticImageData", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "split", "endsWith", "qualityInt", "imgStyle", "Object", "assign", "position", "left", "top", "right", "bottom", "color", "backgroundImage", "getImageBlurSvg", "includes", "placeholder<PERSON><PERSON><PERSON>", "backgroundSize", "backgroundPosition", "backgroundRepeat", "imgAttributes", "generateImgAttrs", "widths", "kind", "getWidths", "viewportWidthRe", "percentSizes", "match", "exec", "length", "smallestRatio", "min", "filter", "s", "Set", "map", "find", "p", "w", "last", "i", "join", "props", "meta", "Image", "configEnv", "handleLoading", "img", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "sizesInput", "decode", "Promise", "resolve", "catch", "then", "parentElement", "current", "event", "Event", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "getDynamicProps", "use", "Boolean", "fetchpriority", "globalThis", "__NEXT_IMAGE_IMPORTED", "ImageElement", "forwardRef", "forwardedRef", "setShowAltText", "onError", "ownRef", "useCallback", "complete", "ref", "useMergedRef", "data-nimg", "ImagePreload", "isAppRouter", "as", "imageSrcSet", "crossOrigin", "referrerPolicy", "ReactDOM", "preload", "Head", "link", "rel", "href", "pagesRouter", "useContext", "RouterContext", "configContext", "ImageConfigContext", "useMemo", "useRef", "useEffect", "useState", "imgMeta", "svgWidth", "svgHeight", "viewBox", "std", "preserveAspectRatio", "getImageProps", "imgProps", "key", "entries"], "sourceRoot": ""}