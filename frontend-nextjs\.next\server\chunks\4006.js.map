{"version": 3, "file": "4006.js", "mappings": "ohBA2CA,SACA;AACA;AACA;AACA,qDAAqD;AACrD,EASA,aArDA,GAAO,QAAwB,GAqC/B,OAlCA,wCACA,yCACA,gBACA,uBAAsB,QAAe,EACrC,MACA,0FAGA,eACA,oBACA,qBAfA,GAgBA,CAAS,EACT,CAAO,CACP,+BAAsC,mBAlBtC,GAkBsC,gBAlBtC,GAkBsC,CAAsF,EAC5H,QACA,2FACA,mDAEA,IACA,gBAKA,OAJA,oBACA,uBACA,MACA,CAAW,EACX,CACA,CAAU,SAIV,MAHA,oBACA,sBACA,CAAW,EACX,CACA,CACA,CACA,GAEA,0CACA", "sources": ["webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/keyless-log-cache.js"], "sourcesContent": ["import \"../chunk-BUSYA2B4.js\";\nimport { isDevelopmentEnvironment } from \"@clerk/shared/utils\";\nconst THROTTLE_DURATION_MS = 10 * 60 * 1e3;\nfunction createClerkDevCache() {\n  if (!isDevelopmentEnvironment()) {\n    return;\n  }\n  if (!global.__clerk_internal_keyless_logger) {\n    global.__clerk_internal_keyless_logger = {\n      __cache: /* @__PURE__ */ new Map(),\n      log: function({ cacheKey, msg }) {\n        var _a;\n        if (this.__cache.has(cacheKey) && Date.now() < (((_a = this.__cache.get(cacheKey)) == null ? void 0 : _a.expiresAt) || 0)) {\n          return;\n        }\n        console.log(msg);\n        this.__cache.set(cacheKey, {\n          expiresAt: Date.now() + THROTTLE_DURATION_MS\n        });\n      },\n      run: async function(callback, { cacheKey, onSuccessStale = THROTTLE_DURATION_MS, onErrorStale = THROTTLE_DURATION_MS }) {\n        var _a, _b;\n        if (this.__cache.has(cacheKey) && Date.now() < (((_a = this.__cache.get(cacheKey)) == null ? void 0 : _a.expiresAt) || 0)) {\n          return (_b = this.__cache.get(cacheKey)) == null ? void 0 : _b.data;\n        }\n        try {\n          const result = await callback();\n          this.__cache.set(cacheKey, {\n            expiresAt: Date.now() + onSuccessStale,\n            data: result\n          });\n          return result;\n        } catch (e) {\n          this.__cache.set(cacheKey, {\n            expiresAt: Date.now() + onErrorStale\n          });\n          throw e;\n        }\n      }\n    };\n  }\n  return globalThis.__clerk_internal_keyless_logger;\n}\nconst createKeylessModeMessage = (keys) => {\n  return `\n\\x1B[35m\n[Clerk]:\\x1B[0m You are running in keyless mode.\nYou can \\x1B[35mclaim your keys\\x1B[0m by visiting ${keys.claimUrl}\n`;\n};\nconst createConfirmationMessage = () => {\n  return `\n\\x1B[35m\n[Clerk]:\\x1B[0m Your application is running with your claimed keys.\nYou can safely remove the \\x1B[35m.clerk/\\x1B[0m from your project.\n`;\n};\nconst clerkDevelopmentCache = createClerkDevCache();\nexport {\n  clerkDevelopmentCache,\n  createConfirmationMessage,\n  createKeylessModeMessage\n};\n//# sourceMappingURL=keyless-log-cache.js.map"], "names": [], "sourceRoot": ""}