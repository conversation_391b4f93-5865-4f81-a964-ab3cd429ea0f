(()=>{var e={};e.id=7654,e.ids=[7654],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1317:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{w:()=>a,x:()=>c});var i=r(10724),o=r(68117),u=r(32481),n=e([o]);async function a(e){try{let t=e.headers.get("x-clerk-user-id"),r=e.headers.get("x-user-email");if(!t||!r)return console.log("Missing Clerk user headers"),null;let s=await (0,i.nm0)({config:o.A}),n={id:t,email:r};console.log("Syncing Clerk user with Payload:",n);let a=await (0,u.kw)(s,n);return console.log("Synced Payload user:",a),{payload:s,user:a}}catch(e){return console.error("Payload authentication error:",e),null}}async function c(e,t,r,s={}){let{payload:i,user:o}=e;switch(r){case"find":return await i.find({collection:t,user:o,...s});case"findByID":return await i.findByID({collection:t,user:o,...s});case"create":return await i.create({collection:t,user:o,...s});case"update":return await i.update({collection:t,user:o,...s});case"delete":return await i.delete({collection:t,user:o,...s});default:throw Error(`Unsupported operation: ${r}`)}}o=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},32481:(e,t,r)=>{"use strict";async function s(e,t,r="front-desk"){try{let s=await e.find({collection:"users",where:{clerkId:{equals:t.id}},limit:1});if(!(s.docs.length>0))return await e.create({collection:"users",data:{role:r,clerkId:t.id}});{let t=s.docs[0];return await e.update({collection:"users",id:t.id,data:{}})}}catch(e){throw console.error("Error syncing Clerk user with Payload:",e),Error("Failed to sync user authentication")}}r.d(t,{kw:()=>s})},32785:e=>{"use strict";e.exports=import("prettier")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},34869:()=>{},35672:e=>{"use strict";e.exports=require("dns/promises")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},37830:e=>{"use strict";e.exports=require("node:stream/web")},38522:e=>{"use strict";e.exports=require("node:zlib")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},50652:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var i=r(70293),o=r(32498),u=r(83889),n=r(91338),a=e([n]);n=(a.then?(await a)():a)[0];let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/patients/convert-to-patient/route",pathname:"/api/patients/convert-to-patient",filename:"route",bundlePath:"app/api/patients/convert-to-patient/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\convert-to-patient\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=p;function c(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},51455:e=>{"use strict";e.exports=require("node:fs/promises")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91338:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{POST:()=>n});var i=r(27492),o=r(1317),u=e([o]);async function n(e){try{let t=await (0,o.w)(e);if(!t)return i.NextResponse.json({error:"Authentication required"},{status:401});let{patientId:r,additionalData:s}=await e.json();if(!r)return i.NextResponse.json({error:"Patient ID is required"},{status:400});let u=await (0,o.x)(t,"patients","findByID",{id:r});if(!u)return i.NextResponse.json({error:"Patient not found"},{status:404});if("patient"===u.userType)return i.NextResponse.json({error:"User is already a patient"},{status:400});let n={userType:"patient",status:"converted",convertedAt:new Date().toISOString(),...s},a=await (0,o.x)(t,"patients","update",{id:r,data:n});return i.NextResponse.json({message:"Successfully converted consultation user to patient",patient:a})}catch(e){return console.error("Error converting consultation user to patient:",e),i.NextResponse.json({error:"Failed to convert consultation user to patient"},{status:500})}}o=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},91645:e=>{"use strict";e.exports=require("net")},93077:()=>{},94735:e=>{"use strict";e.exports=require("events")},98995:e=>{"use strict";e.exports=require("node:module")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[3889,2481,9556,8754],()=>r(50652));module.exports=s})();