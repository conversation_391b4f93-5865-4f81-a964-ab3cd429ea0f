try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="0b79f14c-d1e9-4765-89c7-54afb2704bc6",e._sentryDebugIdIdentifier="sentry-dbid-0b79f14c-d1e9-4765-89c7-54afb2704bc6")}catch(e){}"use strict";exports.id=1950,exports.ids=[1950],exports.modules={2499:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(12848).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6881:(e,t,r)=>{r.d(t,{OV:()=>c,S7:()=>u,VK:()=>d,jq:()=>h});var n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,o=e=>{throw TypeError(e)},l=(e,t,r)=>t.has(e)||o("Cannot "+r),u=(e,t,r)=>(l(e,t,"read from private field"),r?r.call(e):t.get(e)),d=(e,t,r)=>t.has(e)?o("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),c=(e,t,r,n)=>(l(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),h=(e,t,r)=>(l(e,t,"access private method"),r)},8003:(e,t,r)=>{r.d(t,{FW:()=>u,HG:()=>l,Vc:()=>o,gE:()=>i,iM:()=>n,mG:()=>s,ub:()=>a});var n=[".lcl.dev",".lclstage.dev",".lclclerk.com"],i=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],s=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],a=[".accountsstage.dev"],o="https://api.lclclerk.com",l="https://api.clerkstage.dev",u="https://api.clerk.com"},8403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(41618).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10238:(e,t,r)=>{r.d(t,{TD:()=>eY,AA:()=>N,Bs:()=>tk,y3:()=>e$,tl:()=>e2,sb:()=>ea,q5:()=>d.q5,Z5:()=>eG,wI:()=>eV,nr:()=>ti});var n=r(94087),i=r(81315);r(6881);var s={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!0,jitter:!0},a=async e=>new Promise(t=>setTimeout(t,e)),o=(e,t)=>t?e*(1+Math.random()):e,l=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=o(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await a(r()),t++}},u=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:u,factor:d,retryImmediately:c,jitter:h}={...s,...t},f=l({initialDelay:i,maxDelayBetweenRetries:u,factor:d,jitter:h});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;c&&1===r?await a(o(100,h)):await f()}},d=r(45731),c=r(42753),h=new Set,f=(e,t,r)=>{let n=(0,c.MC)()||(0,c.Fj)(),i=r??e;h.has(i)||n||(h.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},p=r(48712),m=r(58496),g={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},y=new Set(["first_factor","second_factor","multi_factor"]),k=new Set(["strict_mfa","strict","moderate","lax"]),_=e=>"number"==typeof e&&e>0,b=e=>y.has(e),v=e=>k.has(e),w=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(e.permission):e.role?n===e.role:null:null},S=e=>{if(!e)return!1;let t="string"==typeof e&&v(e),r="object"==typeof e&&b(e.level)&&_(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?g[e]:e).bind(null,e)},T=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=S(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[s,a]=t,o=-1!==s?i>s:null,l=-1!==a?i>a:null;switch(n){case"first_factor":return o;case"second_factor":return -1!==a?l:o;case"multi_factor":return -1===a?o:o&&l}},E=e=>t=>{if(!e.userId)return!1;let r=w(t,e),n=T(t,e);return[r,n].some(e=>null===e)?[r,n].some(e=>!0===e):[r,n].every(e=>!0===e)},C=r(86199);function I(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function A(e){return e&&e.sensitive?"":"i"}function O(e,t){try{var r,n,i,s,a,o;return r=[],n=function e(t,r,n){var i;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,i=r.exec(e.source);i;)t.push({name:i[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(t,r):Array.isArray(t)?(i=t.map(function(t){return e(t,r,n).source}),new RegExp("(?:".concat(i.join("|"),")"),A(n))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,s=r.start,a=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,u=r.delimiter,d=r.endsWith,c="[".concat(I(void 0===d?"":d),"]|$"),h="[".concat(I(void 0===u?"/#?":u),"]"),f=void 0===s||s?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)f+=I(l(m));else{var g=I(l(m.prefix)),y=I(l(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var k="*"===m.modifier?"?":"";f+="(?:".concat(g,"((?:").concat(m.pattern,")(?:").concat(y).concat(g,"(?:").concat(m.pattern,"))*)").concat(y,")").concat(k)}else f+="(?:".concat(g,"(").concat(m.pattern,")").concat(y,")").concat(m.modifier);else{if("+"===m.modifier||"*"===m.modifier)throw TypeError('Can not repeat "'.concat(m.name,'" without a prefix and suffix'));f+="(".concat(m.pattern,")").concat(m.modifier)}else f+="(?:".concat(g).concat(y,")").concat(m.modifier)}}if(void 0===a||a)i||(f+="".concat(h,"?")),f+=r.endsWith?"(?=".concat(c,")"):"$";else{var _=e[e.length-1],b="string"==typeof _?h.indexOf(_[_.length-1])>-1:void 0===_;i||(f+="(?:".concat(h,"(?=").concat(c,"))?")),b||(f+="(?=".concat(h,"|").concat(c,")"))}return new RegExp(f,A(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[s++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=s;continue}if("("===n){var o=1,l="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '.concat(s));for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at ".concat(s));l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,s=t.delimiter,a=void 0===s?"/#?":s,o=[],l=0,u=0,d="",c=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=c(e);if(void 0!==t)return t;var n=r[u],i=n.type,s=n.index;throw TypeError("Unexpected ".concat(i," at ").concat(s,", expected ").concat(e))},f=function(){for(var e,t="";e=c("CHAR")||c("ESCAPED_CHAR");)t+=e;return t},p=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},m=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||p(r)?"[^".concat(I(a),"]+?"):"(?:(?!".concat(I(r),")[^").concat(I(a),"])+?")};u<r.length;){var g=c("CHAR"),y=c("NAME"),k=c("PATTERN");if(y||k){var _=g||"";-1===i.indexOf(_)&&(d+=_,_=""),d&&(o.push(d),d=""),o.push({name:y||l++,prefix:_,suffix:"",pattern:k||m(_),modifier:c("MODIFIER")||""});continue}var b=g||c("ESCAPED_CHAR");if(b){d+=b;continue}if(d&&(o.push(d),d=""),c("OPEN")){var _=f(),v=c("NAME")||"",w=c("PATTERN")||"",S=f();h("CLOSE"),o.push({name:v||(w?l++:""),pattern:v&&!w?m(_):w,prefix:_,suffix:S,modifier:c("MODIFIER")||""});continue}h("END")}return o}(t,n),r,n)}(e,r,t),i=r,s=t,void 0===s&&(s={}),a=s.decode,o=void 0===a?function(e){return e}:a,function(e){var t=n.exec(e);if(!t)return!1;for(var r=t[0],s=t.index,a=Object.create(null),l=1;l<t.length;l++)!function(e){if(void 0!==t[e]){var r=i[e-1];"*"===r.modifier||"+"===r.modifier?a[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return o(e,r)}):a[r.name]=o(t[e],r)}}(l);return{path:r,index:s,params:a}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}var P="https://api.clerk.com",x="@clerk/backend@1.25.8",R="2024-10-01",U={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count"},q={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:U.DevBrowser,Handshake:U.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason"},N={Attributes:{AuthToken:"__clerkAuthToken",AuthSignature:"__clerkAuthSignature",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage",ClerkUrl:"__clerkUrl"},Cookies:U,Headers:{AuthToken:"x-clerk-auth-token",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthReason:"x-clerk-auth-reason",AuthMessage:"x-clerk-auth-message",ClerkUrl:"x-clerk-clerk-url",EnableDebug:"x-clerk-debug",ClerkRequestData:"x-clerk-request-data",ClerkRedirectTo:"x-clerk-redirect-to",CloudFrontForwardedProto:"cloudfront-forwarded-proto",Authorization:"authorization",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",ForwardedHost:"x-forwarded-host",Accept:"accept",Referrer:"referer",UserAgent:"user-agent",Origin:"origin",Host:"host",ContentType:"content-type",SecFetchDest:"sec-fetch-dest",Location:"location",CacheControl:"cache-control"},ContentTypes:{Json:"application/json"},QueryParameters:q},j=RegExp("(?<!:)/{1,}","g");function z(...e){return e.filter(e=>e).join("/").replace(j,"/")}var M=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},J="/accountless_applications",F=class extends M{async createAccountlessApplication(){return this.request({method:"POST",path:J})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:z(J,"complete")})}},L="/allowlist_identifiers",H=class extends M{async getAllowlistIdentifierList(){return this.request({method:"GET",path:L,queryParams:{paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:L,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:z(L,e)})}},D="/clients",K=class extends M{async getClientList(e={}){return this.request({method:"GET",path:D,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:z(D,e)})}verifyClient(e){return this.request({method:"POST",path:z(D,"verify"),bodyParams:{token:e}})}},W=class extends M{async deleteDomain(e){return this.request({method:"DELETE",path:z("/domains",e)})}},$="/email_addresses",B=class extends M{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:z($,e)})}async createEmailAddress(e){return this.request({method:"POST",path:$,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z($,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:z($,e)})}},G="/invitations",V=class extends M{async getInvitationList(e={}){return this.request({method:"GET",path:G,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:G,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:z(G,e,"revoke")})}},Q="/organizations",Y=class extends M{async getOrganizationList(e){return this.request({method:"GET",path:Q,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:Q,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:z(Q,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:z(Q,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new n.fA.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:z(Q,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:z(Q,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:z(Q,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:z(Q,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(Q,t,"memberships"),queryParams:r})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z(Q,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...n}=e;return this.requireId(t),this.request({method:"PATCH",path:z(Q,t,"memberships",r),bodyParams:n})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...n}=e;return this.request({method:"PATCH",path:z(Q,t,"memberships",r,"metadata"),bodyParams:n})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:z(Q,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(Q,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z(Q,t,"invitations"),bodyParams:r})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:z(Q,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...n}=e;return this.requireId(t),this.request({method:"POST",path:z(Q,t,"invitations",r,"revoke"),bodyParams:n})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(Q,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z(Q,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...n}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:z(Q,t,"domains",r),bodyParams:n})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:z(Q,t,"domains",r)})}},Z="/phone_numbers",X=class extends M{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:z(Z,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:Z,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(Z,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:z(Z,e)})}},ee="/redirect_urls",et=class extends M{async getRedirectUrlList(){return this.request({method:"GET",path:ee,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:z(ee,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:ee,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:z(ee,e)})}},er="/sessions",en=class extends M{async getSessionList(e={}){return this.request({method:"GET",path:er,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:z(er,e)})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:z(er,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:z(er,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),this.request({method:"POST",path:z(er,e,"tokens",t||"")})}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...n}=t;return this.request({method:"POST",path:z(er,e,"refresh"),bodyParams:n,queryParams:{suffixed_cookies:r}})}},ei="/sign_in_tokens",es=class extends M{async createSignInToken(e){return this.request({method:"POST",path:ei,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:z(ei,e,"revoke")})}},ea=(0,p._r)({packageName:"@clerk/backend"}),{isDevOrStagingUrl:eo}=(0,d.RZ)(),el="/users",eu=class extends M{async getUserList(e={}){let{limit:t,offset:r,orderBy:n,...i}=e,[s,a]=await Promise.all([this.request({method:"GET",path:el,queryParams:e}),this.getCount(i)]);return{data:s,totalCount:a}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:z(el,e)})}async createUser(e){return this.request({method:"POST",path:el,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(el,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new n.fA.FormData;return r.append("file",t?.file),this.request({method:"POST",path:z(el,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:z(el,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:z(el,e)})}async getCount(e={}){return this.request({method:"GET",path:z(el,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),n=r?t:`oauth_${t}`;return r&&f("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:z(el,e,"oauth_access_tokens",n),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:z(el,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:z(el,t,"organization_memberships"),queryParams:{limit:r,offset:n}})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:z(el,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:z(el,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:z(el,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:z(el,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:z(el,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:z(el,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:z(el,e,"profile_image")})}},ed="/saml_connections",ec=class extends M{async getSamlConnectionList(e={}){return this.request({method:"GET",path:ed,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:ed,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:z(ed,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(ed,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:z(ed,e)})}},eh=class extends M{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}};function ef(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var ep=class e{constructor(e,t,r,n){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=n}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},em=class e{constructor(e,t,r,n,i){this.id=e,this.identifier=t,this.createdAt=r,this.updatedAt=n,this.invitationId=i}static fromJSON(t){return new e(t.id,t.identifier,t.created_at,t.updated_at,t.invitation_id)}},eg=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=n,this.country=i,this.browserVersion=s,this.browserName=a,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},ey=class e{constructor(e,t,r,n,i,s,a,o,l,u,d,c=null){this.id=e,this.clientId=t,this.userId=r,this.status=n,this.lastActiveAt=i,this.expireAt=s,this.abandonAt=a,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=u,this.latestActivity=d,this.actor=c}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&eg.fromJSON(t.latest_activity),t.actor)}},ek=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=n,this.signUpId=i,this.lastActiveSessionId=s,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>ey.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},e_=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},eb=class e{constructor(e,t,r,n){this.object=e,this.id=t,this.slug=r,this.deleted=n}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},ev=class e{constructor(e,t,r,n,i,s,a,o,l,u,d){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=n,this.subject=i,this.body=s,this.bodyPlain=a,this.status=o,this.slug=l,this.data=u,this.deliveredByClerk=d}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},ew=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},eS=class e{constructor(e,t,r=null,n=null,i=null,s=null,a=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=n,this.expireAt=i,this.nonce=s,this.message=a}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},eT=class e{constructor(e,t,r,n){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&eS.fromJSON(t.verification),t.linked_to.map(e=>ew.fromJSON(e)))}},eE=class e{constructor(e,t,r,n,i,s,a,o,l,u,d={},c,h){this.id=e,this.provider=t,this.identificationId=r,this.externalId=n,this.approvedScopes=i,this.emailAddress=s,this.firstName=a,this.lastName=o,this.imageUrl=l,this.username=u,this.publicMetadata=d,this.label=c,this.verification=h}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.public_metadata,t.label,t.verification&&eS.fromJSON(t.verification))}},eC=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=n,this.updatedAt=i,this.status=s,this.url=a,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},eI={AccountlessApplication:"accountless_application",AllowlistIdentifier:"allowlist_identifier",Client:"client",Cookies:"cookies",Email:"email",EmailAddress:"email_address",Invitation:"invitation",OauthAccessToken:"oauth_access_token",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",PhoneNumber:"phone_number",RedirectUrl:"redirect_url",Session:"session",SignInToken:"sign_in_token",SmsMessage:"sms_message",User:"user",Token:"token",TotalCount:"total_count"},eA=class e{constructor(e,t,r,n={},i,s,a){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=n,this.label=i,this.scopes=s,this.tokenSecret=a}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret)}},eO=class e{constructor(e,t,r,n,i,s,a,o={},l={},u,d,c,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=n,this.hasImage=i,this.createdAt=s,this.updatedAt=a,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=u,this.adminDeleteEnabled=d,this.membersCount=c,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},eP=class e{constructor(e,t,r,n,i,s,a,o={},l={}){this.id=e,this.emailAddress=t,this.role=r,this.organizationId=n,this.createdAt=i,this.updatedAt=s,this.status=a,this.publicMetadata=o,this.privateMetadata=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.organization_id,t.created_at,t.updated_at,t.status,t.public_metadata,t.private_metadata);return r._raw=t,r}},ex=class e{constructor(e,t,r,n={},i={},s,a,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=n,this.privateMetadata=i,this.createdAt=s,this.updatedAt=a,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,eO.fromJSON(t.organization),eR.fromJSON(t.public_user_data));return r._raw=t,r}},eR=class e{constructor(e,t,r,n,i,s){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=n,this.hasImage=i,this.userId=s}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},eU=class e{constructor(e,t,r,n,i,s){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=n,this.verification=i,this.linkedTo=s}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&eS.fromJSON(t.verification),t.linked_to.map(e=>ew.fromJSON(e)))}},eq=class e{constructor(e,t,r,n){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},eN=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.userId=t,this.token=r,this.status=n,this.url=i,this.createdAt=s,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},ej=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=n,this.status=i,this.phoneNumberId=s,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},ez=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},eM=class e{constructor(e,t,r,n,i,s,a,o,l,u){this.id=e,this.name=t,this.domain=r,this.active=n,this.provider=i,this.syncUserAttributes=s,this.allowSubdomains=a,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=u}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},eJ=class e{constructor(e,t,r,n,i,s,a,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=n,this.emailAddress=i,this.firstName=s,this.lastName=a,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&eS.fromJSON(t.verification),t.saml_connection&&eM.fromJSON(t.saml_connection))}},eF=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&eS.fromJSON(t.verification))}},eL=class e{constructor(e,t,r,n,i,s,a,o,l,u,d,c,h,f,p,m,g,y,k,_={},b={},v={},w=[],S=[],T=[],E=[],C=[],I,A,O=null,P,x){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=n,this.twoFactorEnabled=i,this.banned=s,this.locked=a,this.createdAt=o,this.updatedAt=l,this.imageUrl=u,this.hasImage=d,this.primaryEmailAddressId=c,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=f,this.lastSignInAt=p,this.externalId=m,this.username=g,this.firstName=y,this.lastName=k,this.publicMetadata=_,this.privateMetadata=b,this.unsafeMetadata=v,this.emailAddresses=w,this.phoneNumbers=S,this.web3Wallets=T,this.externalAccounts=E,this.samlAccounts=C,this.lastActiveAt=I,this.createOrganizationEnabled=A,this.createOrganizationsLimit=O,this.deleteSelfEnabled=P,this.legalAcceptedAt=x,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>eT.fromJSON(e)),(t.phone_numbers||[]).map(e=>eU.fromJSON(e)),(t.web3_wallets||[]).map(e=>eF.fromJSON(e)),(t.external_accounts||[]).map(e=>eE.fromJSON(e)),(t.saml_accounts||[]).map(e=>eJ.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}};function eH(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return eb.fromJSON(e);switch(e.object){case eI.AccountlessApplication:return ep.fromJSON(e);case eI.AllowlistIdentifier:return em.fromJSON(e);case eI.Client:return ek.fromJSON(e);case eI.Cookies:return e_.fromJSON(e);case eI.EmailAddress:return eT.fromJSON(e);case eI.Email:return ev.fromJSON(e);case eI.Invitation:return eC.fromJSON(e);case eI.OauthAccessToken:return eA.fromJSON(e);case eI.Organization:return eO.fromJSON(e);case eI.OrganizationInvitation:return eP.fromJSON(e);case eI.OrganizationMembership:return ex.fromJSON(e);case eI.PhoneNumber:return eU.fromJSON(e);case eI.RedirectUrl:return eq.fromJSON(e);case eI.SignInToken:return eN.fromJSON(e);case eI.Session:return ey.fromJSON(e);case eI.SmsMessage:return ej.fromJSON(e);case eI.Token:return ez.fromJSON(e);case eI.TotalCount:return e.total_count;case eI.User:return eL.fromJSON(e);default:return e}}function eD(e){var t;return t=async t=>{let r,{secretKey:i,requireSecretKey:s=!0,apiUrl:a=P,apiVersion:o="v1",userAgent:l=x}=e,{path:u,method:d,queryParams:c,headerParams:h,bodyParams:f,formData:p}=t;s&&ef(i);let g=new URL(z(a,o,u));if(c)for(let[e,t]of Object.entries(m({...c})))t&&[t].flat().forEach(t=>g.searchParams.append(e,t));let y={"Clerk-API-Version":R,"User-Agent":l,...h};i&&(y.Authorization=`Bearer ${i}`);try{var k;if(p)r=await n.fA.fetch(g.href,{method:d,headers:y,body:p});else{y["Content-Type"]="application/json";let e="GET"!==d&&f&&Object.keys(f).length>0?{body:JSON.stringify(m(f,{deep:!1}))}:null;r=await n.fA.fetch(g.href,{method:d,headers:y,...e})}let e=r?.headers&&r.headers?.get(N.Headers.ContentType)===N.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:eW(t),status:r?.status,statusText:r?.statusText,clerkTraceId:eK(t,r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>eH(e))}:(k=t)&&"object"==typeof k&&"data"in k&&Array.isArray(k.data)&&void 0!==k.data?{data:t.data.map(e=>eH(e)),totalCount:t.total_count}:{data:eH(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:eK(e,r?.headers)};return{data:null,errors:eW(e),status:r?.status,statusText:r?.statusText,clerkTraceId:eK(e,r?.headers)}}},async(...e)=>{let{data:r,errors:n,totalCount:i,status:s,statusText:a,clerkTraceId:o}=await t(...e);if(n){let e=new p.LR(a||"",{data:[],status:s,clerkTraceId:o});throw e.errors=n,e}return void 0!==i?{data:r,totalCount:i}:r}}function eK(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function eW(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(p.u$):[]}return[]}function e$(e){let t=eD(e);return{__experimental_accountlessApplications:new F(eD({...e,requireSecretKey:!1})),allowlistIdentifiers:new H(t),clients:new K(t),emailAddresses:new B(t),invitations:new V(t),organizations:new Y(t),phoneNumbers:new X(t),redirectUrls:new et(t),sessions:new en(t),signInTokens:new es(t),users:new eu(t),domains:new W(t),samlConnections:new ec(t),testingTokens:new eh(t)}}var eB=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function eG(e,t,r){let{act:n,sid:i,org_id:s,org_role:a,org_slug:o,org_permissions:l,sub:u,fva:d,sts:c}=r,h=e$(e),f=eQ({sessionId:i,sessionToken:t,fetcher:async(...e)=>(await h.sessions.getToken(...e)).jwt}),p=d??null;return{actor:n,sessionClaims:r,sessionId:i,sessionStatus:c??null,userId:u,orgId:s,orgRole:a,orgSlug:o,orgPermissions:l,factorVerificationAge:p,getToken:f,has:E({orgId:s,orgRole:a,orgPermissions:l,userId:u,factorVerificationAge:p}),debug:eB({...e,sessionToken:t})}}function eV(e){return{sessionClaims:null,sessionId:null,sessionStatus:null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:eB(e)}}var eQ=e=>{let{fetcher:t,sessionToken:r,sessionId:n}=e||{};return async(e={})=>n?e.template?t(n,e.template):r:null},eY={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},eZ={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",UnexpectedError:"unexpected-error"};function eX(e,t,r=new Headers,n){let i=eG(e,n,t);return{status:eY.SignedIn,reason:null,message:null,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!0,toAuth:()=>i,headers:r,token:n}}function e0(e,t,r="",n=new Headers){return e1({status:eY.SignedOut,reason:t,message:r,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>eV({...e,status:eY.SignedOut,reason:t,message:r}),token:null})}var e1=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(N.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(N.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(N.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},e8=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},e4=(...e)=>new e8(...e),e5=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(N.Headers.ForwardedProto),n=e.headers.get(N.Headers.ForwardedHost),i=e.headers.get(N.Headers.Host),s=t.protocol,a=this.getFirstValueFromHeader(n)??i,o=this.getFirstValueFromHeader(r)??s?.replace(/[:/]/,""),l=a&&o?`${o}://${a}`:t.origin;return l===t.origin?e4(t):e4(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,C.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},e2=(...e)=>e[0]instanceof e5?e[0]:new e5(...e),e3={},e9=0;function e6(e,t=!0){e3[e.kid]=e,e9=t?Date.now():-1}var e7="local";function te(e){if(!e3[e7]){if(!e)throw new i.zF({action:i.z.SetClerkJWTKey,message:"Missing local JWK.",reason:i.jn.LocalJWKMissing});e6({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return e3[e7]}async function tt({secretKey:e,apiUrl:t=P,apiVersion:r="v1",kid:n,skipJwksCache:s}){if(s||function(){if(-1===e9)return!1;let e=Date.now()-e9>=3e5;return e&&(e3={}),e}()||!e3[n]){if(!e)throw new i.zF({action:i.z.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:i.jn.RemoteJWKFailedToLoad});let{keys:n}=await u(()=>tr(t,e,r));if(!n||!n.length)throw new i.zF({action:i.z.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:i.jn.RemoteJWKFailedToLoad});n.forEach(e=>e6(e))}let a=e3[n];if(!a){let e=Object.values(e3).map(e=>e.kid).sort().join(", ");throw new i.zF({action:`Go to your Dashboard and validate your secret and public keys are correct. ${i.z.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${n}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:i.jn.JWKKidMismatch})}return a}async function tr(e,t,r){if(!t)throw new i.zF({action:i.z.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:i.jn.RemoteJWKFailedToLoad});let s=new URL(e);s.pathname=z(s.pathname,r,"/jwks");let a=await n.fA.fetch(s.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":R,"Content-Type":"application/json","User-Agent":x}});if(!a.ok){let e=await a.json(),t=tn(e?.errors,i.qu.InvalidSecretKey);if(t){let e=i.jn.InvalidSecretKey;throw new i.zF({action:i.z.ContactSupport,message:t.message,reason:e})}throw new i.zF({action:i.z.ContactSupport,message:`Error loading Clerk JWKS from ${s.href} with code=${a.status}`,reason:i.jn.RemoteJWKFailedToLoad})}return a.json()}var tn=(e,t)=>e?e.find(e=>e.code===t):null;async function ti(e,t){let{data:r,errors:s}=(0,n.iU)(e);if(s)return{errors:s};let{header:a}=r,{kid:o}=a;try{let r;if(t.jwtKey)r=te(t.jwtKey);else{if(!t.secretKey)return{errors:[new i.zF({action:i.z.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:i.jn.JWKFailedToResolve})]};r=await tt({...t,kid:o})}return await (0,n.J0)(e,{...t,key:r})}catch(e){return{errors:[e]}}}var ts=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.sessionTokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(N.Cookies.ClientUat),t=this.getCookie(N.Cookies.ClientUat),r=this.getSuffixedCookie(N.Cookies.Session)||"",i=this.getCookie(N.Cookies.Session)||"";if(i&&!this.tokenHasIssuer(i))return!1;if(i&&!this.tokenBelongsToInstance(i))return!0;if(!e&&!r)return!1;let{data:s}=(0,n.iU)(i),a=s?.payload.iat||0,{data:o}=(0,n.iU)(r),l=o?.payload.iat||0;if("0"!==e&&"0"!==t&&a>l||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(o);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}initPublishableKeyValues(e){var t;t=e.publishableKey,(0,d.q5)(t,{fatal:!0}),this.publishableKey=e.publishableKey;let r=(0,d.q5)(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain});this.instanceType=r.instanceType,this.frontendApi=r.frontendApi}initHeaderValues(){this.sessionTokenInHeader=this.parseAuthorizationHeader(this.getHeader(N.Headers.Authorization)),this.origin=this.getHeader(N.Headers.Origin),this.host=this.getHeader(N.Headers.Host),this.forwardedHost=this.getHeader(N.Headers.ForwardedHost),this.forwardedProto=this.getHeader(N.Headers.CloudFrontForwardedProto)||this.getHeader(N.Headers.ForwardedProto),this.referrer=this.getHeader(N.Headers.Referrer),this.userAgent=this.getHeader(N.Headers.UserAgent),this.secFetchDest=this.getHeader(N.Headers.SecFetchDest),this.accept=this.getHeader(N.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(N.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(N.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(N.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(N.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(N.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(N.QueryParameters.Handshake)||this.getCookie(N.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(N.Cookies.RedirectCount))||0}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie((0,d.ky)(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=(0,n.iU)(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=(0,n.iU)(e);if(r)return!1;let i=t.payload.iss.replace(/https?:\/\//gi,"");return this.frontendApi===i}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},ta=async(e,t)=>new ts(t.publishableKey?await (0,d.qS)(t.publishableKey,n.fA.crypto.subtle):"",e,t),to=e=>e.split(";")[0]?.split("=")[0],tl=e=>e.split(";")[0]?.split("=")[1];async function tu(e,{key:t}){let{data:r,errors:s}=(0,n.iU)(e);if(s)throw s[0];let{header:a,payload:o}=r,{typ:l,alg:u}=a;(0,n.qf)(l),(0,n.l3)(u);let{data:d,errors:c}=await (0,n.nk)(r,t);if(c)throw new i.zF({reason:i.jn.TokenVerificationFailed,message:`Error verifying handshake token. ${c[0]}`});if(!d)throw new i.zF({reason:i.jn.TokenInvalidSignature,message:"Handshake signature is invalid."});return o}async function td(e,t){let r,{secretKey:s,apiUrl:a,apiVersion:o,jwksCacheTtlInMs:l,jwtKey:u,skipJwksCache:d}=t,{data:c,errors:h}=(0,n.iU)(e);if(h)throw h[0];let{kid:f}=c.header;if(u)r=te(u);else if(s)r=await tt({secretKey:s,apiUrl:a,apiVersion:o,kid:f,jwksCacheTtlInMs:l,skipJwksCache:d});else throw new i.zF({action:i.z.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:i.jn.JWKFailedToResolve});return await tu(e,{key:r})}var tc={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};async function th(e,t){let r=await ta(e2(e),t);if(ef(r.secretKey),r.isSatellite){var s=r.signInUrl,a=r.secretKey;if(!s&&(0,d.mC)(a))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let o=function(e){let t=null;if(e?.personalAccountPatterns)try{t=O(e.personalAccountPatterns)}catch(t){throw Error(`Invalid personal account pattern "${e.personalAccountPatterns}": "${t}"`)}let r=null;if(e?.organizationPatterns)try{r=O(e.organizationPatterns)}catch(t){throw Error(`Clerk: Invalid organization pattern "${e.organizationPatterns}": "${t}"`)}return{OrganizationMatcher:r,PersonalAccountMatcher:t}}(t.organizationSyncOptions);async function l(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=(await td(r.handshakeToken,r)).handshake,n="";if(t.forEach(t=>{e.append("Set-Cookie",t),to(t).startsWith(N.Cookies.Session)&&(n=tl(t))}),"development"===r.instanceType){let t=new URL(r.clerkUrl);t.searchParams.delete(N.QueryParameters.Handshake),t.searchParams.delete(N.QueryParameters.HandshakeHelp),e.append(N.Headers.Location,t.toString()),e.set(N.Headers.CacheControl,"no-store")}if(""===n)return e0(r,eZ.SessionTokenMissing,"",e);let{data:s,errors:[a]=[]}=await ti(n,r);if(s)return eX(r,s,e,n);if("development"===r.instanceType&&(a?.reason===i.jn.TokenExpired||a?.reason===i.jn.TokenNotActiveYet||a?.reason===i.jn.TokenIatInTheFuture)){a.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${a.getFullMessage()}`);let{data:t,errors:[i]=[]}=await ti(n,{...r,clockSkewInMs:864e5});if(t)return eX(r,t,e,n);throw Error(i?.message||"Clerk: Handshake retry failed.")}throw Error(a?.message||"Clerk: Handshake failed.")}async function u(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:tc.MissingApiClient}}};let{sessionToken:i,refreshTokenInCookie:s}=r;if(!i)return{data:null,error:{message:"Session token must be provided.",cause:{reason:tc.MissingSessionToken}}};if(!s)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:tc.MissingRefreshToken}}};let{data:a,errors:o}=(0,n.iU)(i);if(!a||o)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:tc.ExpiredSessionTokenDecodeFailed,errors:o}}};if(!a?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:tc.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(a.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:i||"",refresh_token:s||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:tc.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:tc.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function c(e){let{data:t,error:r}=await u(e);if(!t||0===t.length)return{data:null,error:r};let n=new Headers,i="";t.forEach(e=>{n.append("Set-Cookie",e),to(e).startsWith(N.Cookies.Session)&&(i=tl(e))});let{data:s,errors:a}=await ti(i,e);return a?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:tc.InvalidSessionToken,errors:a}}}:{data:{jwtPayload:s,sessionToken:i,headers:n},error:null}}function h(e,n,i,s){if(function(e){let{accept:t,secFetchDest:r}=e;return!!("document"===r||"iframe"===r||!r&&t?.startsWith("text/html"))}(e)){let a=s??function({handshakeReason:e}){let n=function(e){let t=new URL(e);return t.searchParams.delete(N.QueryParameters.DevBrowser),t.searchParams.delete(N.QueryParameters.LegacyDevBrowser),t}(r.clerkUrl),i=r.frontendApi.replace(/http(s)?:\/\//,""),s=new URL(`https://${i}/v1/client/handshake`);s.searchParams.append("redirect_url",n?.href||""),s.searchParams.append(N.QueryParameters.SuffixedCookies,r.usesSuffixedCookies().toString()),s.searchParams.append(N.QueryParameters.HandshakeReason,e),"development"===r.instanceType&&r.devBrowserToken&&s.searchParams.append(N.QueryParameters.DevBrowser,r.devBrowserToken);let a=tp(r.clerkUrl,t.organizationSyncOptions,o);return a&&(function(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t})(a).forEach((e,t)=>{s.searchParams.append(t,e)}),new Headers({[N.Headers.Location]:s.href})}({handshakeReason:n});return(a.get(N.Headers.Location)&&a.set(N.Headers.CacheControl,"no-store"),function(e){if(3===r.handshakeRedirectLoopCounter)return!0;let t=r.handshakeRedirectLoopCounter+1,n=N.Cookies.RedirectCount;return e.append("Set-Cookie",`${n}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}(a))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),e0(e,n,i)):function(e,t,r="",n){return e1({status:eY.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>null,token:null})}(e,n,i,a)}return e0(e,n,i)}async function f(){let{sessionTokenInHeader:e}=r;try{let{data:t,errors:n}=await ti(e,r);if(n)throw n[0];return eX(r,t,void 0,e)}catch(e){return m(e,"header")}}async function p(){let e=r.clientUat,s=!!r.sessionTokenInCookie,a=!!r.devBrowserToken;if(r.handshakeToken)try{return await l()}catch(e){if(e instanceof i.zF&&"development"===r.instanceType){if(e.reason===i.jn.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(N.QueryParameters.DevBrowser))return h(r,eZ.DevBrowserSync,"");let u=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&u)return h(r,eZ.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&u&&!r.clerkUrl.searchParams.has(N.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(N.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[N.Headers.Location]:e.toString()});return h(r,eZ.SatelliteCookieNeedsSyncing,"",t)}let d=new URL(r.clerkUrl).searchParams.get(N.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&d){let e=new URL(d);r.devBrowserToken&&e.searchParams.append(N.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(N.QueryParameters.ClerkSynced,"true");let t=new Headers({[N.Headers.Location]:e.toString()});return h(r,eZ.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!a)return h(r,eZ.DevBrowserMissing,"");if(!e&&!s)return e0(r,eZ.SessionTokenAndUATMissing,"");if(!e&&s)return h(r,eZ.SessionTokenWithoutClientUAT,"");if(e&&!s)return h(r,eZ.ClientUATWithoutSessionToken,"");let{data:c,errors:f}=(0,n.iU)(r.sessionTokenInCookie);if(f)return m(f[0],"cookie");if(c.payload.iat<r.clientUat)return h(r,eZ.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:n}=await ti(r.sessionTokenInCookie,r);if(n)throw n[0];let i=eX(r,e,void 0,r.sessionTokenInCookie),s=function(e,r){let n=tp(e.clerkUrl,t.organizationSyncOptions,o);if(!n)return null;let i=!1;if("organization"===n.type&&(n.organizationSlug&&n.organizationSlug!==r.orgSlug&&(i=!0),n.organizationId&&n.organizationId!==r.orgId&&(i=!0)),"personalAccount"===n.type&&r.orgId&&(i=!0),!i)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let s=h(e,eZ.ActiveOrganizationMismatch,"");return"handshake"!==s.status?null:s}(r,i.toAuth());if(s)return s;return i}catch(e){return m(e,"cookie")}}async function m(t,n){let s;if(!(t instanceof i.zF))return e0(r,eZ.UnexpectedError);if(t.reason===i.jn.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await c(r);if(e)return eX(r,e.jwtPayload,e.headers,e.sessionToken);s=t?.cause?.reason?t.cause.reason:tc.UnexpectedSDKError}else s="GET"!==e.method?tc.NonEligibleNonGet:r.refreshTokenInCookie?null:tc.NonEligibleNoCookie;return(t.tokenCarrier=n,[i.jn.TokenExpired,i.jn.TokenNotActiveYet,i.jn.TokenIatInTheFuture].includes(t.reason))?h(r,tm({tokenError:t.reason,refreshError:s}),t.getFullMessage()):e0(r,t.reason,t.getFullMessage())}return r.sessionTokenInHeader?f():p()}var tf=e=>{let{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:s,isSatellite:a,domain:o}=e;return{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:s,isSatellite:a,domain:o}};function tp(e,t,r){if(!t)return null;if(r.OrganizationMatcher){let n;try{n=r.OrganizationMatcher(e.pathname)}catch(e){return console.error(`Clerk: Failed to apply organization pattern "${t.organizationPatterns}" to a path`,e),null}if(n&&"params"in n){let e=n.params;if("id"in e&&"string"==typeof e.id)return{type:"organization",organizationId:e.id};if("slug"in e&&"string"==typeof e.slug)return{type:"organization",organizationSlug:e.slug};console.warn("Clerk: Detected an organization pattern match, but no organization ID or slug was found in the URL. Does the pattern include `:id` or `:slug`?")}}if(r.PersonalAccountMatcher){let n;try{n=r.PersonalAccountMatcher(e.pathname)}catch(e){return console.error(`Failed to apply personal account pattern "${t.personalAccountPatterns}" to a path`,e),null}if(n)return{type:"personalAccount"}}return null}var tm=({tokenError:e,refreshError:t})=>{switch(e){case i.jn.TokenExpired:return`${eZ.SessionTokenExpired}-refresh-${t}`;case i.jn.TokenNotActiveYet:return eZ.SessionTokenNBF;case i.jn.TokenIatInTheFuture:return eZ.SessionTokenIatInTheFuture;default:return eZ.UnexpectedError}};function tg(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}var ty={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};function tk(e){let t=tg(ty,e.options),r=e.apiClient;return{authenticateRequest:(e,n={})=>{let{apiUrl:i,apiVersion:s}=t,a=tg(t,n);return th(e,{...n,...a,apiUrl:i,apiVersion:s,apiClient:r})},debugRequestState:tf}}},14243:(e,t,r)=>{r.d(t,{TD:()=>n.TD,AA:()=>n.AA,tl:()=>n.tl,vH:()=>a,Z5:()=>n.Z5,wI:()=>n.wI});var n=r(10238);r(94087),r(81315),r(6881);var i=(e,t,r,i)=>{if(""===e)return s(t.toString(),r?.toString());let a=new URL(e),o=r?new URL(r,a):void 0,l=new URL(t,a);return o&&l.searchParams.set("redirect_url",o.toString()),i&&a.hostname!==l.hostname&&l.searchParams.set(n.AA.QueryParameters.DevBrowser,i),l.toString()},s=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let n=new URL(t);r=new URL(e,n.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},a=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:s,signUpUrl:a,baseUrl:o}=e,l=(0,n.q5)(t),u=l?.frontendApi,d=l?.instanceType==="development",c=function(e){if(!e)return"";let t=e.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return`https://${t}`}(u);return{redirectToSignUp:({returnBackUrl:t}={})=>{a||c||n.sb.throwMissingPublishableKeyError();let s=`${c}/sign-up`;return r(i(o,a||s,t,d?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{s||c||n.sb.throwMissingPublishableKeyError();let a=`${c}/sign-in`;return r(i(o,s||a,t,d?e.devBrowserToken:null))}}}},14272:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(12848).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17469:(e,t,r)=>{r.d(t,{H$:()=>d,mG:()=>a,V2:()=>c,o7:()=>u,fS:()=>f,ev:()=>_,Rg:()=>h,At:()=>l,tm:()=>g,rB:()=>o,qW:()=>p,sE:()=>m,Mh:()=>k,nN:()=>y});var n=r(95517),i=r(8003);r(6881);var s=r(34595);process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let a=process.env.CLERK_API_VERSION||"v1",o=process.env.CLERK_SECRET_KEY||"",l="pk_test_d2VsY29tZWQtZmVsaW5lLTgyLmNsZXJrLmFjY291bnRzLmRldiQ",u=process.env.CLERK_ENCRYPTION_KEY||"",d=process.env.CLERK_API_URL||(e=>{let t=(0,n.q5)(e)?.frontendApi;return t?.startsWith("clerk.")&&i.iM.some(e=>t?.endsWith(e))?i.FW:i.mG.some(e=>t?.endsWith(e))?i.Vc:i.ub.some(e=>t?.endsWith(e))?i.HG:i.FW})(l),c=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",h=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",f=(0,s.zz)(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,p="/auth/sign-in",m="/auth/sign-up",g={name:"@clerk/nextjs",version:"6.12.12",environment:"production"},y=(0,s.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),k=(0,s.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),_=(0,s.zz)(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1},18120:(e,t,r)=>{r.d(t,{I:()=>s});var n=r(48855),i=r(17469);let s=!r(49788).M&&(0,n.b_)()&&!i.ev},23624:e=>{e.exports={rE:"15.3.2"}},24598:(e,t,r)=>{r.d(t,{zz:()=>i});var n=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function i(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}n(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),n(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},27720:(e,t,r)=>{r.r(t),r.d(t,{snakeCase:()=>l});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function i(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var s=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],a=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=n({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=t.stripRegexp,l=t.transform,u=t.delimiter,d=o(o(e,void 0===r?s:r,"$1\0$2"),void 0===n?a:n,"\0"),c=0,h=d.length;"\0"===d.charAt(c);)c++;for(;"\0"===d.charAt(h-1);)h--;return d.slice(c,h).split("\0").map(void 0===l?i:l).join(void 0===u?" ":u)}(e,n({delimiter:"."},r))}},34595:(e,t,r)=>{r.d(t,{zz:()=>n.zz});var n=r(24598);r(6881)},36218:(e,t,r)=>{r.d(t,{NE:()=>i,Zd:()=>a,_b:()=>s});var n=r(14243);function i(e,t){var r;return((r=n.AA.Attributes[t])in e?e[r]:void 0)||s(e,n.AA.Headers[t])}function s(e,t){var r,n;return function(e){try{let{headers:t,nextUrl:r,cookies:n}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==n?void 0:n.get)}catch{return!1}}(e)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||(null==(n=null==(r=e.socket)?void 0:r._httpMessage)?void 0:n.getHeader(t))}function a(e){return!!i(e,"AuthStatus")}},41618:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,s.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(46100),i=r(29197),s=r(13078),a=r(45226),o=r(27643),l=r(40447);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42753:(e,t,r)=>{r.d(t,{Fj:()=>s,MC:()=>i,b_:()=>n});var n=()=>!1,i=()=>!1,s=()=>{try{return!0}catch{}return!1}},44508:(e,t,r)=>{var n=r(63800);r.o(n,"RedirectType")&&r.d(t,{RedirectType:function(){return n.RedirectType}}),r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},45731:(e,t,r)=>{r.d(t,{RZ:()=>n.RZ,ky:()=>n.ky,mC:()=>n.mC,q5:()=>n.q5,qS:()=>n.qS});var n=r(95517);r(6881)},48712:(e,t,r)=>{r.d(t,{LR:()=>i,_r:()=>a,u$:()=>n});function n(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn}}}var i=class e extends Error{constructor(t,{data:r,status:i,clerkTraceId:s}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=i,this.message=t,this.clerkTraceId=s,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(n):[]}(r)}},s=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function a({packageName:e,customMessages:t}){let r=e,n={...s,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(n.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}r(6881)},48855:(e,t,r)=>{r.d(t,{Fj:()=>n.Fj,b_:()=>n.b_});var n=r(42753);r(6881)},49788:(e,t,r)=>{r.d(t,{M:()=>i});var n=r(23624);let i=n.rE.startsWith("13.")||n.rE.startsWith("14.0")},51644:e=>{let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),n=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),i=(e,t,s,a=new WeakMap)=>{if(s={deep:!1,target:{},...s},a.has(e))return a.get(e);a.set(e,s.target);let{target:o}=s;delete s.target;let l=e=>e.map(e=>n(e)?i(e,t,s,a):e);if(Array.isArray(e))return l(e);for(let[u,d]of Object.entries(e)){let c=t(u,d,e);if(c===r)continue;let[h,f,{shouldRecurse:p=!0}={}]=c;"__proto__"!==h&&(s.deep&&p&&n(f)&&(f=Array.isArray(f)?l(f):i(f,t,s,a)),o[h]=f)}return o};e.exports=(e,r,n)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return i(e,r,n)},e.exports.mapObjectSkip=r},58496:(e,t,r)=>{let n=r(51644),{snakeCase:i}=r(27720),s={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==s))throw Error("obj must be array of plain objects")}else if(e.constructor!==s)throw Error("obj must be an plain object");return n(e,function(e,r){var n,s,a,o,l;return[(n=t.exclude,s=e,n.some(function(e){return"string"==typeof e?e===s:e.test(s)}))?e:i(e,t.parsingOptions),r,(a=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(a,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},63800:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return i.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return s.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(90081),i=r(93618),s=r(94653),a=r(14272),o=r(2499),l=r(8403);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81315:(e,t,r)=>{r.d(t,{jn:()=>i,qu:()=>n,xy:()=>o,z:()=>s,zF:()=>a});var n={InvalidSecretKey:"clerk_key_invalid"},i={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",RemoteJWKInvalid:"jwk-remote-invalid",RemoteJWKMissing:"jwk-remote-missing",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},s={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable.",EnsureClockSync:"Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."},a=class e extends Error{constructor({action:t,message:r,reason:n}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=n,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},o=class extends Error{}},86199:(e,t)=>{t.qg=function(e,t){let a=new r,o=e.length;if(o<2)return a;let l=t?.decode||s,u=0;do{let t=e.indexOf("=",u);if(-1===t)break;let r=e.indexOf(";",u),s=-1===r?o:r;if(t>s){u=e.lastIndexOf(";",t-1)+1;continue}let d=n(e,u,t),c=i(e,t,d),h=e.slice(d,c);if(void 0===a[h]){let r=n(e,t+1,s),o=i(e,s,r),u=l(e.slice(r,o));a[h]=u}u=s+1}while(u<o);return a},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function n(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function i(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function s(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},90081:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return o}});let n=r(7956),i=r(93618),s=r(19121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let s=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",s}function o(e,t){var r;throw null!=t||(t=(null==s||null==(r=s.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),a(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=i.RedirectType.replace),a(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91613:(e,t,r)=>{r.d(t,{y:()=>n});var n=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e},94087:(e,t,r)=>{r.d(t,{l3:()=>k,qf:()=>y,r0:()=>l,iU:()=>C,hJ:()=>p,nk:()=>E,Fh:()=>T,fA:()=>o,J0:()=>I});var n=r(81315),i=r(77598),s=r(91613);r(6881);var a=fetch.bind(globalThis),o={crypto:i.webcrypto,get fetch(){return a},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},l={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let n=e.length;for(;"="===e[n-1];)if(--n,!r.loose&&!((e.length-n)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(n*t.bits/8|0),s=0,a=0,o=0;for(let r=0;r<n;++r){let n=t.codes[e[r]];if(void 0===n)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|n,(s+=t.bits)>=8&&(s-=8,i[o++]=255&a>>s)}if(s>=t.bits||255&a<<8-s)throw SyntaxError("Unexpected end of data");return i})(e,u,t),stringify:(e,t)=>(function(e,t,r={}){let{pad:n=!0}=r,i=(1<<t.bits)-1,s="",a=0,o=0;for(let r=0;r<e.length;++r)for(o=o<<8|255&e[r],a+=8;a>t.bits;)a-=t.bits,s+=t.chars[i&o>>a];if(a&&(s+=t.chars[i&o<<t.bits-a]),n)for(;s.length*t.bits&7;)s+="=";return s})(e,u,t)},u={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},d={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},c="RSASSA-PKCS1-v1_5",h={RS256:c,RS384:c,RS512:c},f=Object.keys(d);function p(e){let t=d[e],r=h[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${f.join(",")}.`);return{hash:{name:d[e]},name:h[e]}}var m=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),g=(e,t)=>{let r=[t].flat().filter(e=>!!e),i=[e].flat().filter(e=>!!e);if(r.length>0&&i.length>0){if("string"==typeof e){if(!r.includes(e))throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(m(e)&&!e.some(e=>r.includes(e)))throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},y=e=>{if(void 0!==e&&"JWT"!==e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},k=e=>{if(!f.includes(e))throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${f}.`})},_=e=>{if("string"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},b=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new n.zF({reason:n.jn.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},v=(e,t)=>{if("number"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()<=r.getTime()-t)throw new n.zF({reason:n.jn.TokenExpired,message:`JWT is expired. Expiry date: ${i.toUTCString()}, Current date: ${r.toUTCString()}.`})},w=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new n.zF({reason:n.jn.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})},S=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new n.zF({reason:n.jn.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})};function T(e,t,r){if("object"==typeof e)return o.crypto.subtle.importKey("jwk",e,t,!1,[r]);let n=function(e){let t=e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,""),r=(0,s.y)(t),n=new Uint8Array(new ArrayBuffer(r.length));for(let e=0,t=r.length;e<t;e++)n[e]=r.charCodeAt(e);return n}(e),i="sign"===r?"pkcs8":"spki";return o.crypto.subtle.importKey(i,n,t,!1,[r])}async function E(e,t){let{header:r,signature:i,raw:s}=e,a=new TextEncoder().encode([s.header,s.payload].join(".")),l=p(r.alg);try{let e=await T(t,l,"verify");return{data:await o.crypto.subtle.verify(l.name,e,i,a)}}catch(e){return{errors:[new n.zF({reason:n.jn.TokenInvalidSignature,message:e?.message})]}}}function C(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new n.zF({reason:n.jn.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,i,s]=t,a=new TextDecoder,o=JSON.parse(a.decode(l.parse(r,{loose:!0}))),u=JSON.parse(a.decode(l.parse(i,{loose:!0})));return{data:{header:o,payload:u,signature:l.parse(s,{loose:!0}),raw:{header:r,payload:i,signature:s,text:e}}}}async function I(e,t){let{audience:r,authorizedParties:i,clockSkewInMs:s,key:a}=t,o=s||5e3,{data:l,errors:u}=C(e);if(u)return{errors:u};let{header:d,payload:c}=l;try{let{typ:e,alg:t}=d;y(e),k(t);let{azp:n,sub:s,aud:a,iat:l,exp:u,nbf:h}=c;_(s),g([a],[r]),b(n,i),v(u,o),w(h,o),S(l,o)}catch(e){return{errors:[e]}}let{data:h,errors:f}=await E(l,a);return f?{errors:[new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Error verifying JWT signature. ${f[0]}`})]}:h?{data:c}:{errors:[new n.zF({reason:n.jn.TokenInvalidSignature,message:"JWT signature is invalid."})]}}},94653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(12848).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95517:(e,t,r)=>{r.d(t,{RZ:()=>u,qS:()=>c,ky:()=>h,mC:()=>d,q5:()=>o});var n=r(91613),i=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,s=r(8003),a="pk_live_";function o(e,t={}){if(!(e=e||"")||!l(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!l(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(a)?"production":"development",i=(0,n.y)(e.split("_")[2]);return i=i.slice(0,-1),t.proxyUrl?i=t.proxyUrl:"development"!==r&&t.domain&&(i=`clerk.${t.domain}`),{instanceType:r,frontendApi:i}}function l(e=""){try{let t=e.startsWith(a)||e.startsWith("pk_test_"),r=(0,n.y)(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function u(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=s.gE.some(e=>r.endsWith(e)),e.set(r,n)),n}}}function d(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function c(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return i(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var h=(e,t)=>`${e}_${t}`}};
//# sourceMappingURL=1950.js.map