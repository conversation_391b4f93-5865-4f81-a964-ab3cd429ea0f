@layer payload-default;@layer payload-default;@layer payload-default{.banner{font-size:1rem;line-height:20px;border:0;vertical-align:middle;background:var(--theme-elevation-100);color:var(--theme-elevation-800);border-radius:4px;padding:10px;margin-bottom:1.5384615385rem}.banner--has-action{cursor:pointer;text-decoration:none}.banner--has-icon{display:flex}.banner--has-icon svg{display:block}.banner--type-default.button--has-action:hover{background:var(--theme-elevation-900)}.banner--type-default.button--has-action:active{background:var(--theme-elevation-950)}.banner--type-error{background:var(--theme-error-100);color:var(--theme-error-600)}.banner--type-error svg .stroke{stroke:var(--theme-error-600);fill:none}.banner--type-error svg .fill{fill:var(--theme-error-600)}.banner--type-error.button--has-action:hover{background:var(--theme-error-200)}.banner--type-error.button--has-action:active{background:var(--theme-error-300)}.banner--type-success{background:var(--theme-success-100);color:var(--theme-success-600)}.banner--type-success.button--has-action:active,.banner--type-success.button--has-action:hover{background:var(--theme-success-200)}.icon--chevron{height:var(--base);width:var(--base)}.icon--chevron .stroke{fill:none;stroke:currentColor;stroke-width:1px;vector-effect:non-scaling-stroke}.icon--chevron.icon--size-large{height:var(--base);width:var(--base)}.icon--chevron.icon--size-small{height:12px;width:12px}.icon--edit{height:1.5384615385rem;width:1.5384615385rem;shape-rendering:auto}.icon--edit .stroke{fill:none;stroke:currentColor}.icon--link{width:1.5384615385rem;height:1.5384615385rem}.icon--link .stroke,.icon--plus .stroke{stroke:currentColor;stroke-width:1px}.icon--swap{height:1.5384615385rem;width:1.5384615385rem}.icon--swap .stroke{fill:none}.icon--swap .stroke,.icon--x .stroke{stroke:currentColor;stroke-width:1px}.popup-button-list__button,.popup-button-list__button button{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body)}.popup-button-list{--list-button-padding:calc(var(--base) * .5);--popup-button-list-gap:3px;display:flex;flex-direction:column;text-align:left;gap:var(--popup-button-list-gap)}[dir=rtl] .popup-button-list__text-align--left{text-align:right}.popup-button-list__text-align--left{text-align:left}.popup-button-list__text-align--center{text-align:center}[dir=rtl] .popup-button-list__text-align--right{text-align:left}.popup-button-list__text-align--right{text-align:right}.popup-button-list__button{padding-left:var(--list-button-padding);padding-right:var(--list-button-padding);padding-top:2px;padding-bottom:2px;cursor:pointer;text-align:inherit;line-height:var(--base);text-decoration:none;border-radius:3px}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default{.popup-button-list__button button:focus-visible{outline:none}.popup-button-list__button:focus-visible,.popup-button-list__button:focus-within,.popup-button-list__button:hover{background-color:var(--popup-button-highlight)}.popup-button-list__button--selected{background-color:var(--theme-elevation-150)}.popup-button-list__disabled{cursor:not-allowed;--popup-button-highlight:transparent;background-color:var(--popup-button-highlight);color:var(--theme-elevation-350)}.popup-button-list__disabled:hover{--popup-button-highlight:var(--theme-elevation-50)}.popup-divider{width:100%;height:1px;background-color:var(--theme-elevation-150);border:none;margin:calc(var(--base) * .75) 0}.popup-list-group-label{color:var(--theme-elevation-500);font-weight:500;line-height:1;margin-top:calc(var(--base) * .25);margin-bottom:calc(var(--base) * .5)}.popup-button{height:100%;color:currentColor;padding:0;font-size:inherit;line-height:inherit;font-family:inherit;border:0;cursor:pointer;display:inline-flex}.popup-button--background{background:transparent}.popup-button--size-xsmall{padding:2px}.popup-button--size-small{padding:4px}.popup-button--size-medium{padding:6px}.popup-button--size-large{padding:8px}.popup-button--disabled{cursor:not-allowed}.popup{--popup-button-highlight:var(--theme-elevation-200);--popup-bg:var(--theme-input-bg);--popup-text:var(--theme-text);--popup-caret-size:10px;--popup-x-padding:calc(var(--base) * .33);--popup-padding:calc(var(--base) * .5);--button-size-offset:-8px;position:relative}.popup__trigger-wrap{display:flex;align-items:stretch;height:100%;cursor:pointer}.popup__content{position:absolute;background:var(--popup-bg);opacity:0;visibility:hidden;pointer-events:none;z-index:var(--z-popup);max-width:calc(100vw - 1.5384615385rem);color:var(--popup-text);border-radius:4px;padding-left:var(--popup-padding);padding-right:var(--popup-padding);min-width:var(--popup-width,auto)}.popup__hide-scrollbar{overflow:hidden}.popup__scroll-container{overflow-y:auto;white-space:nowrap;width:calc(100% + var(--scrollbar-width));padding-top:var(--popup-padding);padding-bottom:var(--popup-padding)}.popup__scroll-content{width:calc(100% - var(--scrollbar-width))}.popup--show-scrollbar .popup__scroll-container,.popup--show-scrollbar .popup__scroll-content{width:100%}.popup:active,.popup:focus{outline:none}.popup--size-xsmall{--popup-width:80px}.popup--size-xsmall .popup__content{box-shadow:0 2px 2px -1px #0000001a}.popup--size-small{--popup-width:100px}.popup--size-small .popup__content{box-shadow:0 4px 8px -3px #0000001a}.popup--size-medium{--popup-width:150px}.popup--size-medium .popup__content{box-shadow:0 -2px 16px -2px #0003}.popup--size-large{--popup-width:200px}.popup--size-large .popup__content{box-shadow:0 -2px 16px -2px #0003}.popup--button-size-xsmall{--button-size-offset:-12px}.popup--button-size-small{--button-size-offset:-8px}.popup--button-size-medium{--button-size-offset:-4px}.popup--button-size-large{--button-size-offset:0px}[dir=rtl] .popup--h-align-left .popup__caret{right:var(--popup-padding);left:unset}.popup--h-align-left .popup__caret{left:var(--popup-padding)}.popup--h-align-center .popup__caret,.popup--h-align-center .popup__content{left:50%;transform:translate(-50%)}[dir=rtl] .popup--h-align-right .popup__content{right:unset;left:0}[dir=rtl] .popup--h-align-right .popup__caret{right:unset;left:var(--popup-padding)}.popup--h-align-right .popup__content{right:var(--button-size-offset)}.popup--h-align-right .popup__caret{right:var(--popup-padding)}.popup__caret{position:absolute;border:var(--popup-caret-size) solid transparent}.popup--v-align-top .popup__content{box-shadow:0 -2px 16px -2px #0003;bottom:calc(100% + var(--popup-caret-size))}.popup--v-align-top .popup__caret{top:calc(100% - 1px);border-top-color:var(--popup-bg)}.popup--v-align-bottom .popup__content{box-shadow:0 2px 16px -2px #0003;top:calc(100% + var(--popup-caret-size))}.popup--v-align-bottom .popup__caret{bottom:calc(100% - 1px);border-bottom-color:var(--popup-bg)}.popup--active .popup__content{opacity:1;visibility:visible;pointer-events:all}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.popup{--popup-padding:calc(var(--base) * .25)}.popup--h-align-center .popup__caret,.popup--h-align-center .popup__content{left:50%;transform:translate(0)}.popup--h-align-right .popup__content{right:0}.popup--h-align-right .popup__caret{right:var(--popup-padding)}.popup--force-h-align-left .popup__content{left:0;right:unset;transform:unset}.popup--force-h-align-left .popup__caret{left:var(--popup-padding);right:unset;transform:unset}.popup--force-h-align-right .popup__content{right:0;left:unset;transform:unset}.popup--force-h-align-right .popup__caret{right:var(--popup-padding);left:unset;transform:unset}}}@layer payload-default;@layer payload-default;@layer payload-default{a.btn{display:inline-block}.btn--withPopup{margin-block:4px}.btn--withPopup .btn{margin:0}.btn{--btn-font-weight:normal}.btn *{pointer-events:none}.btn--style-primary{--color:var(--theme-elevation-0);--bg-color:var(--theme-elevation-800);--box-shadow:none;--hover-bg:var(--theme-elevation-600);--hover-color:var(--color)}.btn--style-primary.btn--disabled{--bg-color:var(--theme-elevation-200);--color:var(--theme-elevation-800);--hover-bg:var(--bg-color);--hover-color:var(--color)}.btn--style-secondary{--color:var(--theme-text);--bg-color:transparent;--box-shadow:inset 0 0 0 1px var(--theme-elevation-800);--hover-color:var(--theme-elevation-600);--hover-box-shadow:inset 0 0 0 1px var(--theme-elevation-400)}.btn--style-secondary.btn--disabled{--color:var(--theme-elevation-200);--box-shadow:inset 0 0 0 1px var(--theme-elevation-200);--hover-box-shadow:inset 0 0 0 1px var(--theme-elevation-200);--hover-color:var(--color)}.btn--style-pill{--bg-color:var(--theme-elevation-150);--color:var(--theme-elevation-800);--hover-color:var(--theme-elevation-800);--hover-bg:var(--theme-elevation-100)}.btn--style-pill.btn--disabled{--color:var(--theme-elevation-600);--hover-bg:var(--bg-color);--hover-color:var(--color)}.btn--style-icon-label,.btn--style-icon-label.btn--icon-position-left,.btn--style-icon-label.btn--icon-position-right{padding:0;font-weight:600;--color:var(--theme-text);--bg-color:transparent;--hover-color:var(--theme-elevation-600)}.btn--style-icon-label.btn--disabled,.btn--style-icon-label.btn--icon-position-left.btn--disabled,.btn--style-icon-label.btn--icon-position-right.btn--disabled{--color:var(--theme-elevation-200);--hover-color:var(--color)}.btn--style-icon-label .btn__content,.btn--style-icon-label.btn--icon-position-left .btn__content,.btn--style-icon-label.btn--icon-position-right .btn__content{--btn-icon-content-gap:calc(var(--base) * .4)}.btn--style-subtle{--color:var(--theme-text);--bg-color:var(--theme-elevation-100);--hover-bg:var(--theme-elevation-150);--box-shadow:inset 0 0 0 1px var(--theme-elevation-200);--hover-box-shadow:inset 0 0 0 1px var(--theme-elevation-250)}.btn--style-subtle.btn--disabled{--color:var(--theme-elevation-450);--hover-box-shadow:var(--box-shadow);--hover-bg:var(--bg-color);--hover-color:var(--color)}.btn--style-tab{--bg-color:transparent;--hover-bg:var(--theme-elevation-50);--color:var(--theme-text);--btn-font-weight:500}.btn--style-tab.btn--disabled{--btn-font-weight:600;--hover-box-shadow:var(--box-shadow);--bg-color:var(--theme-elevation-100);--hover-bg:var(--bg-color);--hover-color:var(--color)}.popup--active .btn{background-color:var(--hover-bg)}.btn--withPopup .popup-button{color:var(--color,inherit);background-color:var(--bg-color);box-shadow:var(--box-shadow);border-radius:4px;align-items:center}html:not([dir=RTL]) .btn--withPopup .popup-button{border-left:1px solid var(--theme-bg);border-top-left-radius:0;border-bottom-left-radius:0}html[dir=RTL] .btn--withPopup .popup-button{border-right:1px solid var(--theme-bg);border-top-right-radius:0;border-bottom-right-radius:0}.btn--withPopup .popup-button:active,.btn--withPopup .popup-button:focus,.btn--withPopup .popup-button:focus-visible,.btn--withPopup .popup-button:hover{background-color:var(--hover-bg);color:var(--hover-color);box-shadow:var(--hover-box-shadow)}.btn--withPopup .btn:active,.btn--withPopup .btn:focus,.btn--withPopup .btn:focus-visible,.btn--withPopup .btn:hover,.btn:active,.btn:focus,.btn:focus-visible,.btn:hover{color:var(--hover-color);box-shadow:var(--hover-box-shadow);background-color:var(--hover-bg)}.btn--disabled,.btn--disabled .btn{cursor:not-allowed}.btn{--btn-padding-block-start:0;--btn-padding-inline-end:0;--btn-padding-block-end:0;--btn-padding-inline-start:0;--btn-icon-size:calc(var(--base) * 1.2);--btn-icon-border-color:currentColor;--btn-icon-padding:0px;--btn-icon-content-gap:calc(var(--base) * .4);--margin-block:calc(var(--base) * 1.2);--btn-line-height:calc(var(--base) * 1.2);border-radius:var(--style-radius-s);font-size:var(--base-body-size);font-family:var(--font-body);font-weight:var(--btn-font-weight,normal);margin-block:var(--margin-block);line-height:var(--btn-line-height);border:0;cursor:pointer;text-decoration:none;transition-property:border,color,box-shadow,background;transition-duration:.1s;transition-timing-function:cubic-bezier(0,.2,.2,1);padding:var(--btn-padding-block-start) var(--btn-padding-inline-end) var(--btn-padding-block-end) var(--btn-padding-inline-start);color:var(--color,inherit);background-color:var(--bg-color,transparent);box-shadow:var(--box-shadow,none)}.btn__icon{width:100%;height:100%}.btn__icon .stroke{stroke:var(--color,currentColor);fill:none}.btn__icon .fill{fill:var(--color,currentColor)}.btn__content,.btn__icon{display:flex;align-items:center;justify-content:center}.btn__icon{width:var(--btn-icon-size);height:var(--btn-icon-size);border:1px solid var(--btn-icon-border-color);border-radius:100%;padding:var(--btn-icon-padding);color:inherit}.btn__icon svg{width:100%;height:100%}.btn__icon.btn--size-small{padding:calc(var(--base) * .2)}.btn--withPopup{display:flex}.btn--has-tooltip{position:relative}.btn--icon .btn__content{gap:var(--btn-icon-content-gap)}.btn--icon-style-none,.btn--icon-style-without-border{--btn-icon-border-color:transparent}.btn--icon-position-left .btn__content{flex-direction:row-reverse}.btn--size-small{--btn-icon-size:calc(var(--base) * .9);--btn-icon-content-gap:calc(var(--base) * .2);--btn-padding-block-start:0;--btn-padding-inline-end:calc(var(--base) * .4);--btn-padding-inline-start:calc(var(--base) * .4);--btn-padding-block-end:0}.btn--size-small.btn--icon-position-left{--btn-padding-inline-start:calc(var(--base) * .3)}.btn--size-small.btn--icon-position-right{--btn-padding-inline-end:calc(var(--base) * .3)}.btn--size-xsmall{--btn-icon-size:calc(var(--base) * .8);--btn-icon-content-gap:calc(var(--base) * .2);--btn-padding-block-start:0;--btn-padding-inline-end:calc(var(--base) * .3);--btn-padding-inline-start:calc(var(--base) * .3);--btn-padding-block-end:0}.btn--size-xsmall.btn--icon-position-left{--btn-padding-inline-start:calc(var(--base) * .2)}.btn--size-xsmall.btn--icon-position-right{--btn-padding-inline-end:calc(var(--base) * .2)}.btn--size-medium{--btn-icon-size:calc(var(--base) * 1.2);--btn-icon-content-gap:calc(var(--base) * .2);--btn-padding-block-start:calc(var(--base) * .2);--btn-padding-inline-end:calc(var(--base) * .6);--btn-padding-block-end:calc(var(--base) * .2);--btn-padding-inline-start:calc(var(--base) * .6)}.btn--size-medium.btn--icon-position-left{--btn-padding-inline-start:calc(var(--base) * .4)}.btn--size-medium.btn--icon-position-right{--btn-padding-inline-end:calc(var(--base) * .4)}.btn--size-large{--btn-icon-size:calc(var(--base) * 1.2);--btn-icon-content-gap:calc(var(--base) * .4);--btn-padding-block-start:calc(var(--base) * .4);--btn-padding-inline-end:calc(var(--base) * .8);--btn-padding-inline-start:calc(var(--base) * .8);--btn-padding-block-end:calc(var(--base) * .4)}.btn--size-large.btn--icon-position-left{--btn-padding-inline-start:calc(var(--base) * .6)}.btn--size-large.btn--icon-position-right{--btn-padding-inline-end:calc(var(--base) * .6)}html:not([dir=RTL]) .btn--withPopup .btn{border-top-right-radius:0;border-bottom-right-radius:0}html[dir=RTL] .btn--withPopup .btn{border-top-left-radius:0;border-bottom-left-radius:0}.btn:focus-visible{outline:var(--accessibility-outline);outline-offset:var(--accessibility-outline-offset)}.btn.btn--disabled{cursor:not-allowed}.btn--style-none{padding:0}.btn--no-margin{--margin-block:0}.tooltip{--caret-size:6px;opacity:0;background-color:var(--theme-elevation-800);position:absolute;z-index:3;left:50%;padding:4px 8px;color:var(--theme-elevation-0);line-height:15px;font-weight:400;white-space:nowrap;border-radius:2px;visibility:hidden}.tooltip:after{content:" ";display:block;position:absolute;transform:translate3d(-50%,100%,0);width:0;height:0;border-left:var(--caret-size) solid transparent;border-right:var(--caret-size) solid transparent}.tooltip--show{visibility:visible;opacity:1;transition:opacity .2s ease-in-out;cursor:default}.tooltip--caret-center:after{left:50%}.tooltip--caret-left:after{left:calc(var(--base) * .5)}.tooltip--caret-right:after{right:calc(var(--base) * .5)}.tooltip--position-top{top:calc(var(--base) * -1.25);transform:translate3d(-50%,calc(var(--caret-size) * -1),0)}.tooltip--position-top:after{bottom:1px;border-top:var(--caret-size) solid var(--theme-elevation-800)}.tooltip--position-bottom{bottom:calc(var(--base) * -1.25);transform:translate3d(-50%,var(--caret-size),0)}.tooltip--position-bottom:after{bottom:calc(100% + var(--caret-size) - 1px);border-bottom:var(--caret-size) solid var(--theme-elevation-800)}.tooltip .tooltip-content{overflow:hidden;text-overflow:ellipsis;width:100%}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.tooltip{display:none}}html[data-theme=light] .tooltip:not(.field-error){background-color:var(--theme-elevation-100);color:var(--theme-elevation-1000)}html[data-theme=light] .tooltip--position-top:not(.field-error):after{border-top-color:var(--theme-elevation-100)}html[data-theme=light] .tooltip--position-bottom:not(.field-error):after{border-bottom-color:var(--theme-elevation-100)}.error-pill{align-self:center;border:0;padding:0 5px;flex-shrink:0;border-radius:var(--style-radius-l);line-height:18px;font-size:11px;text-align:center;font-weight:500;display:flex;align-items:center;justify-content:center;background:var(--theme-error-300);color:var(--theme-error-950)}.error-pill--fixed-width{width:18px;height:18px;border-radius:50%;position:relative}.error-pill__count{letter-spacing:.5px;margin-left:.5px}.field-description{display:flex;color:var(--theme-elevation-400);margin-top:calc(var(--base) / 4)}.field-description--margin-bottom{margin-top:0;margin-bottom:calc(var(--base) / 2)}.field-error.tooltip{font-family:var(--font-body);left:auto;max-width:75%;right:0;transform:translateY(calc(var(--caret-size) * -1));color:var(--theme-error-950);background-color:var(--theme-error-300)}.field-error.tooltip:after{border-top-color:var(--theme-error-300);border-bottom-color:var(--theme-error-300)}.gutter--left{padding-left:var(--gutter-h)}.gutter--right{padding-right:var(--gutter-h)}.gutter--negative-left{margin-left:calc(-1 * var(--gutter-h))}.gutter--negative-right{margin-right:calc(-1 * var(--gutter-h))}.drawer__close{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body)}.drawer{display:flex;overflow:hidden;position:fixed;height:100vh}.drawer__blur-bg{position:absolute;z-index:1;inset:0;opacity:0;transition:all .2s linear}.drawer__blur-bg:after,.drawer__blur-bg:before{content:" ";position:absolute;inset:0}.drawer__blur-bg:before{background:var(--theme-bg);opacity:.75}.drawer__blur-bg:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.drawer__content{opacity:0;transform:translate(calc(var(--base) * 4));position:relative;z-index:2;overflow:hidden;transition:all .2s linear;background-color:var(--theme-bg)}.drawer__content-children{position:relative;z-index:1;overflow:auto;height:100%}.drawer--is-open .drawer__blur-bg,.drawer--is-open .drawer__content{opacity:1}.drawer--is-open .drawer__close{opacity:.1;transition:opacity .2s linear;transition-delay:.1s}.drawer--is-open .drawer__content{transform:translate(0)}.drawer__close{position:relative;z-index:2;flex-shrink:0;text-indent:-9999px;cursor:pointer;opacity:0;will-change:opacity;transition:none;transition-delay:0s;flex-grow:1;background:var(--theme-elevation-800)}.drawer__close:active,.drawer__close:focus{outline:0}.drawer__header{display:flex;align-items:center;margin-top:50px;margin-bottom:20px;width:100%}.drawer__header__title{margin:0;flex-grow:1}.drawer__header__close{border:0;background-color:transparent;padding:0;cursor:pointer;overflow:hidden;direction:ltr;display:flex;align-items:center;justify-content:center;width:24px;height:24px}.drawer__header__close svg{margin:-24px;width:48px;height:48px;position:relative}.drawer__header__close svg .stroke{stroke-width:1px;vector-effect:non-scaling-stroke}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default{@media (max-width:1024px){.drawer__header{margin-top:30px}}html[data-theme=dark] .drawer__close{background:var(--color-base-1000)}html[data-theme=dark] .drawer--is-open .drawer__close{opacity:.25}.confirmation-modal{display:flex;align-items:center;justify-content:center;height:100%}.confirmation-modal:after,.confirmation-modal:before{content:" ";position:absolute;inset:0}.confirmation-modal:before{background:var(--theme-bg);opacity:.75}.confirmation-modal:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.confirmation-modal__wrapper{z-index:1;position:relative;display:flex;flex-direction:column;gap:16px;padding:40px;max-width:720px}.confirmation-modal__content{display:flex;flex-direction:column;gap:8px}.confirmation-modal__content>*{margin:0}.confirmation-modal__controls{display:flex;gap:8px}.confirmation-modal__controls .btn{margin:0}.localized,label.field-label:not(.unstyled){font-size:13px;line-height:20px;font-weight:400;font-family:var(--font-body)}label.field-label{display:flex;align-items:center}label.field-label:not(.unstyled){display:flex;padding-bottom:5px;color:var(--theme-elevation-800);font-family:var(--font-body)}[dir=ltr] label.field-label:not(.unstyled){margin-right:auto}[dir=rtl] label.field-label:not(.unstyled){margin-left:auto}label.field-label:not(.unstyled) .required{color:var(--theme-error-500)}[dir=ltr] label.field-label:not(.unstyled) .required{margin-left:5px}[dir=rtl] label.field-label:not(.unstyled) .required{margin-right:5px}[dir=ltr] .localized{margin-left:5px}[dir=rtl] .localized{margin-right:5px}.icon--check{height:1.5384615385rem;width:1.5384615385rem}.icon--check .stroke{fill:none;stroke:currentColor;stroke-width:2px}.icon--line{width:1.5384615385rem;height:1.5384615385rem}.icon--line .stroke{stroke:currentColor;stroke-width:1px}.checkbox{position:relative;margin-bottom:1.5384615385rem}.checkbox .tooltip:not([aria-hidden=true]){right:auto;position:static;transform:translateY(calc(var(--caret-size) * -1));margin-bottom:.2em;max-width:-moz-fit-content;max-width:fit-content}.checkbox-input{display:inline-flex}.checkbox-input:hover:not(.checkbox-input--read-only) input,.checkbox-input:hover:not(.checkbox-input--read-only) label{cursor:pointer}.checkbox-input label.field-label{padding-bottom:0;padding-left:10px}[dir=rtl] .checkbox-input__input{margin-right:0;margin-left:10px}.checkbox-input__input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1);display:flex;padding:0;line-height:0;position:relative;width:1.5384615385rem;height:1.5384615385rem}.checkbox-input__input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.checkbox-input__input[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{.checkbox-input__input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.checkbox-input__input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.checkbox-input__input:hover{border-color:var(--theme-elevation-250)}.checkbox-input__input:active,.checkbox-input__input:focus,.checkbox-input__input:focus-within{border-color:var(--theme-elevation-400);outline:0}.checkbox-input__input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.checkbox-input__input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.checkbox-input__input input[type=checkbox]{position:absolute;width:calc(100% + 4px);height:calc(100% + 4px);padding:0;margin:-2px 0 0 -2px;opacity:0;border-radius:0;z-index:1}.checkbox-input__icon{position:absolute}.checkbox-input__icon svg{opacity:0}.checkbox-input:not(.checkbox-input--read-only):active .checkbox-input__input,.checkbox-input:not(.checkbox-input--read-only):active input[type=checkbox],.checkbox-input:not(.checkbox-input--read-only):focus .checkbox-input__input,.checkbox-input:not(.checkbox-input--read-only):focus input[type=checkbox],.checkbox-input:not(.checkbox-input--read-only):focus-within .checkbox-input__input,.checkbox-input:not(.checkbox-input--read-only):focus-within input[type=checkbox]{outline:0;box-shadow:0 0 3px 3px var(--theme-success-400)!important;border:1px solid var(--theme-elevation-150)}.checkbox-input:not(.checkbox-input--read-only):hover .checkbox-input__input,.checkbox-input:not(.checkbox-input--read-only):hover input[type=checkbox]{border-color:var(--theme-elevation-250)}.checkbox-input:not(.checkbox-input--read-only):not(.checkbox-input--checked):hover{cursor:pointer}.checkbox-input:not(.checkbox-input--read-only):not(.checkbox-input--checked):hover svg{opacity:.2}.checkbox-input--checked .checkbox-input__icon svg{opacity:1}.checkbox-input .checkbox-input__icon .icon--line{width:1.4rem;height:1.4rem}.checkbox-input .checkbox-input__icon.partial svg{opacity:1}.checkbox-input--read-only .checkbox-input__input{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.checkbox-input--read-only .checkbox-input__input:hover{border-color:var(--theme-elevation-150);box-shadow:none}.checkbox-input--read-only label{color:var(--theme-elevation-400)}html[data-theme=light] .checkbox.error .checkbox-input__input{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .checkbox.error .checkbox-input__input{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .checkbox.error .checkbox-input__input:hover{border-color:var(--theme-error-500)}.icon--copy{height:1.5384615385rem;width:1.5384615385rem}.icon--copy .stroke{fill:none;stroke:currentColor;stroke-width:1px}.icon--more{height:1.5384615385rem;width:1.5384615385rem}.icon--more .fill{fill:var(--theme-elevation-800);stroke:currentColor}.array-actions__button{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body);cursor:pointer;border-radius:100px}.array-actions__button:hover{background:var(--theme-elevation-0)}.array-actions__actions{list-style:none;margin:0;padding:0}.array-actions__action{display:flex;gap:calc(var(--base) / 2);align-items:center}.array-actions__action svg{position:relative}.array-actions__action svg .stroke{stroke-width:1px}.icon--drag-handle{height:1.5384615385rem;width:1.5384615385rem}.icon--drag-handle .fill{stroke:currentColor;stroke-width:1px;fill:var(--theme-elevation-800)}.collapsible__toggle{font-size:13px;line-height:20px;font-weight:400;font-family:var(--font-body);border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor}.collapsible{--toggle-pad-h:15px;--toggle-pad-v:12px;border-radius:4px}.collapsible__toggle-wrap{position:relative;padding:8px 8px 8px 16px;display:flex;align-items:center;justify-content:space-between;background:var(--theme-elevation-50);line-height:24px;gap:4px;border-top-right-radius:4px;border-top-left-radius:4px;width:100%}.collapsible__toggle-wrap:hover{background:var(--theme-elevation-100)}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default{}@layer payload-default{.collapsible__toggle-wrap:has(.collapsible__drag){-webkit-padding-start:8px;padding-inline-start:8px}.collapsible__drag{display:flex;opacity:.5;top:var(--toggle-pad-v);width:24px;height:24px;padding:2px}.collapsible__drag icon,.collapsible__toggle{width:100%;height:100%}.collapsible__toggle{text-align:left;cursor:pointer;border-top-right-radius:4px;border-top-left-radius:4px;color:transparent;position:absolute;top:0;left:0}.collapsible__toggle span{-webkit-user-select:none;-moz-user-select:none;user-select:none}.collapsible--style-default{border:1px solid var(--theme-elevation-200)}.collapsible--style-default:hover{border:1px solid var(--theme-elevation-300)}.collapsible--style-default>.collapsible__toggle-wrap .row-label{color:var(--theme-text)}.collapsible--style-error{border:1px solid var(--theme-error-400)}.collapsible--style-error>.collapsible__toggle-wrap{background-color:var(--theme-error-100)}.collapsible--style-error>.collapsible__toggle-wrap:hover{background:var(--theme-error-150)}.collapsible--style-error>.collapsible__toggle-wrap .row-label{color:var(--theme-error-950)}.collapsible__header-wrap{inset:0 60px 0 0;pointer-events:none;width:100%;overflow:hidden;max-width:100%}.collapsible__header-wrap--has-drag-handle{left:20px}.collapsible--collapsed .collapsible__toggle-wrap{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.collapsible__actions-wrap{pointer-events:none;display:flex;gap:4px;-webkit-margin-end:4px;margin-inline-end:4px}.collapsible__actions{pointer-events:all;display:flex;align-items:center;justify-content:center;width:24px;height:24px}.collapsible__actions.icon{padding:2px}.collapsible__indicator{display:flex;align-items:center;justify-content:center;width:24px;height:24px}.collapsible__indicator.icon{padding:2px}.collapsible__content{background-color:var(--theme-elevation-0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;padding:var(--base)}@media (max-width:768px){.collapsible__content{padding:var(--gutter-h)}}}@layer payload-default{.rah-static{interpolate-size:allow-keywords;height:0}.rah-static--height-auto{height:auto}.shimmer-effect{position:relative;overflow:hidden;background-color:var(--theme-elevation-50)}.shimmer-effect__shine{position:absolute;scale:1.5;width:100%;height:100%;transform:translate(-100%);animation:shimmer 1.75s infinite;opacity:.75;background:linear-gradient(100deg,var(--theme-elevation-50) 0,var(--theme-elevation-50) 15%,var(--theme-elevation-150) 50%,var(--theme-elevation-50) 85%,var(--theme-elevation-50) 100%)}}@layer payload-default{@keyframes shimmer{to{transform:translate3d(100%,0,0)}}}@layer payload-default;@layer payload-default;@layer payload-default{.field-type__wrap{position:relative}.render-fields,.render-fields--margins-small{--spacing-field:var(--base)}.render-fields--margins-none{--spacing-field:0}.render-fields>.field-type{margin-bottom:var(--spacing-field);position:relative}.render-fields>.field-type[type=hidden]{margin-bottom:0}.render-fields>.field-type:first-child{margin-top:0}.render-fields>.field-type:last-of-type{margin-bottom:0}.render-fields:not(.render-fields--margins-small)>.field-type.array-field,.render-fields:not(.render-fields--margins-small)>.field-type.blocks-field,.render-fields:not(.render-fields--margins-small)>.field-type.collapsible-field,.render-fields:not(.render-fields--margins-small)>.field-type.group-field,.render-fields:not(.render-fields--margins-small)>.field-type.rich-text{margin-top:calc(var(--spacing-field) * 2);margin-bottom:calc(var(--spacing-field) * 2)}.render-fields:not(.render-fields--margins-small)>.field-type.array-field:first-child,.render-fields:not(.render-fields--margins-small)>.field-type.blocks-field:first-child,.render-fields:not(.render-fields--margins-small)>.field-type.collapsible-field:first-child,.render-fields:not(.render-fields--margins-small)>.field-type.group-field:first-child,.render-fields:not(.render-fields--margins-small)>.field-type.rich-text:first-child{margin-top:0}.render-fields:not(.render-fields--margins-small)>.field-type.array-field:last-child,.render-fields:not(.render-fields--margins-small)>.field-type.blocks-field:last-child,.render-fields:not(.render-fields--margins-small)>.field-type.collapsible-field:last-child,.render-fields:not(.render-fields--margins-small)>.field-type.group-field:last-child,.render-fields:not(.render-fields--margins-small)>.field-type.rich-text:last-child{margin-bottom:0}@media (max-width:768px){.render-fields{--spacing-field:calc(var(--base) / 2)}}}@layer payload-default;@layer payload-default;@layer payload-default{.pill{--pill-padding-block-start:0px;--pill-padding-inline-start:0px;--pill-padding-block-end:0px;--pill-padding-inline-end:0px;font-size:1rem;line-height:calc(var(--base) * 1.2);display:inline-flex;background:var(--theme-elevation-150);color:var(--theme-elevation-800);border-radius:3px;cursor:inherit;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;border:0;align-items:center;flex-shrink:0;gap:4px;padding:var(--pill-padding-block-start) var(--pill-padding-inline-end) var(--pill-padding-block-end) var(--pill-padding-inline-start);--pill-icon-size:calc(var(--base) * 1.2)}.pill--rounded{border-radius:var(--style-radius-l);font-size:12px}.pill:active,.pill:focus:not(:focus-visible){outline:none}.pill:focus-visible{outline:var(--accessibility-outline);outline-offset:var(--accessibility-outline-offset)}.pill .icon{flex-shrink:0;width:var(--pill-icon-size);height:var(--pill-icon-size)}.pill__label{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.pill--has-action{cursor:pointer;text-decoration:none}.pill--is-dragging{cursor:-webkit-grabbing;cursor:grabbing}.pill--has-icon{gap:0;--pill-padding-inline-start:base(.4);--pill-padding-inline-end:base(.1)}.pill--has-icon svg{display:block}.pill--align-icon-left{flex-direction:row-reverse;-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:8px;padding-inline-end:8px}.pill--style-white{background:var(--theme-elevation-0)}.pill--style-white.pill--has-action:active,.pill--style-white.pill--has-action:hover{background:var(--theme-elevation-100)}.pill--style-always-white{background:var(--theme-elevation-850);color:var(--theme-elevation-0)}.pill--style-always-white.pill--has-action:active,.pill--style-always-white.pill--has-action:hover{background:var(--theme-elevation-750)}.pill--style-light-gray,.pill--style-light.pill--has-action:active,.pill--style-light.pill--has-action:hover{background:var(--theme-elevation-100)}.pill--style-light-gray{color:var(--theme-elevation-800)}.pill--style-warning{background:var(--theme-warning-150);color:var(--theme-warning-800)}.pill--style-success{background:var(--theme-success-150);color:var(--theme-success-800)}.pill--style-error{background:var(--theme-error-150);color:var(--theme-error-800)}.pill--style-dark{background:var(--theme-elevation-800);color:var(--theme-elevation-0)}.pill--style-dark svg .stroke{stroke:var(--theme-elevation-0);fill:none}.pill--style-dark svg .fill{fill:var(--theme-elevation-0)}.pill--style-dark.pill--has-action:hover{background:var(--theme-elevation-750)}.pill--style-dark.pill--has-action:active{background:var(--theme-elevation-700)}.pill--size-medium{--pill-padding-block-start:calc(var(--base) * .2);--pill-padding-inline-end:calc(var(--base) * .6);--pill-padding-block-end:calc(var(--base) * .2);--pill-padding-inline-start:calc(var(--base) * .6)}.pill--size-small{--pill-icon-size:calc(var(--base) * .9);--pill-padding-block-start:0;--pill-padding-inline-end:calc(var(--base) * .4);--pill-padding-inline-start:calc(var(--base) * .4);--pill-padding-block-end:0}html[data-theme=light] .pill--style-always-white{background:var(--theme-elevation-0);color:var(--theme-elevation-800);border:1px solid var(--theme-elevation-100)}html[data-theme=light] .pill--style-always-white.pill--has-action:active,html[data-theme=light] .pill--style-always-white.pill--has-action:hover{background:var(--theme-elevation-100)}.thumbnail-card{border:0;background:none;box-shadow:none;border-radius:0;color:currentColor;font-family:var(--font-body);box-shadow:0 4px 8px -3px #0000001a;width:100%;background:var(--theme-input-bg);border:1px solid var(--theme-border-color);border-radius:var(--style-radius-m);transition:border .1s cubic-bezier(0,.2,.2,1);padding:10px}.thumbnail-card__label{padding:15px 10px 5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:600}.thumbnail-card--has-on-click{cursor:pointer}.thumbnail-card--has-on-click:active,.thumbnail-card--has-on-click:focus,.thumbnail-card--has-on-click:hover{border:1px solid var(--theme-elevation-350)}.thumbnail-card--align-label-center{text-align:center}.thumbnail-card__thumbnail{display:flex;align-items:center;justify-content:center}.blocks-drawer__blocks-wrapper{padding-top:30px}.blocks-drawer__blocks{position:relative;padding:0;list-style:none;display:grid;grid-template-columns:repeat(6,1fr);grid-gap:20px;gap:20px}.blocks-drawer__default-image{display:flex;align-items:center;justify-content:center;width:100%;aspect-ratio:3/2;overflow:hidden}.blocks-drawer__default-image img,.blocks-drawer__default-image svg{width:100%;height:100%;object-fit:cover}.blocks-drawer__block-groups{padding:0;display:flex;flex-direction:column;gap:30px}.blocks-drawer__block-group{list-style:none}.blocks-drawer__block-group-label{padding-bottom:10px}.blocks-drawer__block-group-none{order:1;padding-top:30px;border-top:1px solid var(--theme-border-color)}.blocks-drawer__block-group-none:only-child{padding-top:0;border-top:0}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1440px){.blocks-drawer__blocks{grid-template-columns:repeat(5,1fr)}}@media (max-width:1024px){.blocks-drawer__blocks-wrapper{padding-top:35px}.blocks-drawer__blocks{grid-template-columns:repeat(3,1fr)}.blocks-drawer__block-groups{gap:35px}.blocks-drawer__block-group-none{padding-top:35px}}@media (max-width:768px){.blocks-drawer__blocks-wrapper{padding-top:15px}.blocks-drawer__blocks{grid-template-columns:repeat(2,1fr)}.blocks-drawer__block-groups{gap:15px}.blocks-drawer__block-group-none{padding-top:15px}}}@layer payload-default;@layer payload-default;@layer payload-default{.icon--search{height:1.5384615385rem;width:1.5384615385rem}.icon--search .stroke{stroke:currentColor;stroke-width:1px}.block-search{position:-webkit-sticky;position:sticky;top:0;display:flex;width:100%;align-items:center;z-index:1}.block-search__input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.block-search__input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.block-search__input[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.block-search__input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.block-search__input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.block-search__input:hover{border-color:var(--theme-elevation-250)}.block-search__input:active,.block-search__input:focus,.block-search__input:focus-within{border-color:var(--theme-elevation-400);outline:0}.block-search__input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.block-search__input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.block-search .icon--search{position:absolute;top:50%;transform:translate3d(0,-50%,0);right:0;width:40px;margin:0 5px}.block-search .icon--search .stroke{stroke:var(--theme-elevation-300)}@media (max-width:1024px){.block-search__input{margin-bottom:0}}}@layer payload-default;@layer payload-default;@layer payload-default{.section-title{position:relative;min-width:80px;max-width:100%;pointer-events:all;display:flex;overflow:hidden}.section-title:after{display:block;content:attr(data-value) " ";visibility:hidden;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%}.section-title:after,.section-title__input{font-family:var(--font-body);font-weight:600;font-size:12.5px;padding:0;width:100%}.section-title__input{color:var(--theme-elevation-800);background-color:transparent;border:none;min-width:min-content;width:100%;max-width:100%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;resize:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;position:absolute;inset:0}.section-title__input:hover{box-shadow:inset 0 -2px 0 -1px var(--theme-elevation-150)}.section-title__input:focus,.section-title__input:hover{outline:0}.section-title__input:focus{box-shadow:none}.blocks-field__header-action{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body)}.blocks-field{display:flex;flex-direction:column;gap:calc(var(--base) / 2)}.blocks-field__header h3{margin:0}.blocks-field__header-wrap{display:flex;align-items:flex-end;width:100%;justify-content:space-between}.blocks-field__heading-with-error{display:flex;align-items:center;gap:10px}.blocks-field--has-no-error>.array-field__header .array-field__heading-with-error{color:var(--theme-text)}.blocks-field--has-error>.array-field__header{color:var(--theme-error-500)}.blocks-field__error-pill{align-self:center}.blocks-field__header-actions{list-style:none;margin:0;padding:0;display:flex}.blocks-field__header-action{cursor:pointer;margin-left:10px}}@layer payload-default;@layer payload-default{}@layer payload-default{.blocks-field__header-action:focus-visible,.blocks-field__header-action:hover{text-decoration:underline}.blocks-field__block-header{display:inline-flex;max-width:100%;width:100%;overflow:hidden;gap:7.5px}.blocks-field__block-number{flex-shrink:0}.blocks-field__block-pill{flex-shrink:0;display:block;line-height:unset}.blocks-field__rows{display:flex;flex-direction:column;gap:calc(var(--base) / 2)}.blocks-field__drawer-toggler{background-color:transparent;margin:0;padding:0;border:none;align-self:flex-start}.blocks-field__drawer-toggler .btn{color:var(--theme-elevation-400);margin:0}.blocks-field__drawer-toggler .btn:hover{color:var(--theme-elevation-800)}html[data-theme=light] .blocks-field--has-error .blocks-field__heading-with-error,html[data-theme=light] .blocks-field--has-error .section-title__input{color:var(--theme-error-750)}html[data-theme=dark] .blocks-field--has-error .blocks-field__heading-with-error,html[data-theme=dark] .blocks-field--has-error .section-title__input{color:var(--theme-error-500)}.code-editor{direction:ltr;box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1);height:auto;padding:0}.code-editor:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.code-editor[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.code-editor::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.code-editor::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.code-editor:hover{border-color:var(--theme-elevation-250)}.code-editor:active,.code-editor:focus,.code-editor:focus-within{border-color:var(--theme-elevation-400);outline:0}.code-editor:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.code-editor:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.code-editor .monaco-editor .view-overlays .current-line{max-width:calc(100% - 14px);border-width:0}.code-editor .monaco-editor:focus-within .view-overlays .current-line{border-right:0;border-width:1px}}@layer payload-default;@layer payload-default;@layer payload-default{.code-field{position:relative}.code-field.error textarea{border:1px solid var(--theme-error-500)!important}.code-field.error .code-editor{border-color:var(--theme-error-500)}.code-field.error .margin,.code-field.error .monaco-editor-background{background-color:var(--theme-error-50)}.code-field .read-only .code-editor{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.code-field .read-only .code-editor:hover{border-color:var(--theme-elevation-150);box-shadow:none}.collapsible-field__row-label-wrap{pointer-events:none;display:flex;align-items:center;gap:10px}.icon--calendar{height:1.5384615385rem;width:1.5384615385rem}.icon--calendar .stroke{stroke:currentColor;stroke-width:1px}.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow,.react-datepicker__navigation-icon:before,.react-datepicker__year-read-view--down-arrow{border-color:#ccc;border-style:solid;border-width:3px 3px 0 0;content:"";display:block;height:9px;position:absolute;top:6px;width:9px}.react-datepicker-wrapper{display:inline-block;padding:0;border:0}.react-datepicker{font-family:Helvetica Neue,helvetica,arial,sans-serif;font-size:.8rem;background-color:#fff;color:#000;border:1px solid #aeaeae;border-radius:.3rem;display:inline-block;position:relative;line-height:normal}.react-datepicker--time-only .react-datepicker__time-container{border-left:0}.react-datepicker--time-only .react-datepicker__time,.react-datepicker--time-only .react-datepicker__time-box{border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem}.react-datepicker-popper{z-index:1;line-height:0}.react-datepicker-popper .react-datepicker__triangle{stroke:#aeaeae}.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle{fill:#f0f0f0;color:#f0f0f0}.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle{fill:#fff;color:#fff}.react-datepicker__header{text-align:center;background-color:#f0f0f0;border-bottom:1px solid #aeaeae;border-top-left-radius:.3rem;padding:8px 0;position:relative}.react-datepicker__header--time{padding-bottom:8px;padding-left:5px;padding-right:5px}.react-datepicker__header--time:not(.react-datepicker__header--time--only){border-top-left-radius:0}.react-datepicker__header:not(.react-datepicker__header--has-time-select){border-top-right-radius:.3rem}.react-datepicker__month-dropdown-container--scroll,.react-datepicker__month-dropdown-container--select,.react-datepicker__month-year-dropdown-container--scroll,.react-datepicker__month-year-dropdown-container--select,.react-datepicker__year-dropdown-container--scroll,.react-datepicker__year-dropdown-container--select{display:inline-block;margin:0 15px}.react-datepicker-time__header,.react-datepicker-year-header,.react-datepicker__current-month{margin-top:0;color:#000;font-weight:700;font-size:.944rem}.react-datepicker-time__header{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.react-datepicker__navigation{align-items:center;background:none;display:flex;justify-content:center;text-align:center;cursor:pointer;position:absolute;top:2px;padding:0;border:none;z-index:1;height:32px;width:32px;text-indent:-999em;overflow:hidden}.react-datepicker__navigation--previous{left:2px}.react-datepicker__navigation--next{right:2px}.react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button){right:85px}.react-datepicker__navigation--years{position:relative;top:0;display:block;margin-left:auto;margin-right:auto}.react-datepicker__navigation--years-previous{top:4px}.react-datepicker__navigation--years-upcoming{top:-4px}.react-datepicker__navigation:hover :before{border-color:#a6a6a6}.react-datepicker__navigation-icon{position:relative;top:-1px;font-size:20px;width:0}.react-datepicker__navigation-icon--next{left:-2px}.react-datepicker__navigation-icon--next:before{transform:rotate(45deg);left:-7px}.react-datepicker__navigation-icon--previous{right:-2px}.react-datepicker__navigation-icon--previous:before{transform:rotate(225deg);right:-7px}.react-datepicker__month-container{float:left}.react-datepicker__year{margin:.4rem;text-align:center}.react-datepicker__year-wrapper{display:flex;flex-wrap:wrap;max-width:180px}.react-datepicker__year .react-datepicker__year-text{display:inline-block;width:4rem;margin:2px}.react-datepicker__month{margin:.4rem;text-align:center}.react-datepicker__month .react-datepicker__month-text,.react-datepicker__month .react-datepicker__quarter-text{display:inline-block;width:4rem;margin:2px}.react-datepicker__input-time-container{clear:both;width:100%;float:left;margin:5px 0 10px 15px;text-align:left}.react-datepicker__input-time-container .react-datepicker-time__caption,.react-datepicker__input-time-container .react-datepicker-time__input-container{display:inline-block}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input{display:inline-block;margin-left:10px}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input{width:auto}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-inner-spin-button,.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__input input[type=time]{-moz-appearance:textfield}.react-datepicker__input-time-container .react-datepicker-time__input-container .react-datepicker-time__delimiter{margin-left:5px;display:inline-block}.react-datepicker__time-container{float:right;border-left:1px solid #aeaeae;width:85px}.react-datepicker__time-container--with-today-button{display:inline;border:1px solid #aeaeae;border-radius:.3rem;position:absolute;right:-87px;top:0}.react-datepicker__time-container .react-datepicker__time{position:relative;background:#fff;border-bottom-right-radius:.3rem}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box{width:85px;overflow-x:hidden;margin:0 auto;text-align:center;border-bottom-right-radius:.3rem}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list{list-style:none;margin:0;height:calc(195px + .85rem);overflow-y:scroll;padding-right:0;padding-left:0;width:100%;box-sizing:content-box}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item{height:30px;padding:5px 10px;white-space:nowrap}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover{cursor:pointer;background-color:#f0f0f0}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected{background-color:#216ba5;color:#fff;font-weight:700}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected:hover{background-color:#216ba5}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled{color:#ccc}.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--disabled:hover{cursor:default;background-color:transparent}.react-datepicker__week-number{color:#ccc;display:inline-block;width:1.7rem;line-height:1.7rem;text-align:center;margin:.166rem}.react-datepicker__week-number.react-datepicker__week-number--clickable{cursor:pointer}.react-datepicker__week-number.react-datepicker__week-number--clickable:not(.react-datepicker__week-number--selected,.react-datepicker__week-number--keyboard-selected):hover{border-radius:.3rem;background-color:#f0f0f0}.react-datepicker__week-number--selected{border-radius:.3rem;background-color:#216ba5;color:#fff}.react-datepicker__week-number--selected:hover{background-color:#1d5d90}.react-datepicker__week-number--keyboard-selected{border-radius:.3rem;background-color:#2a87d0;color:#fff}.react-datepicker__week-number--keyboard-selected:hover{background-color:#1d5d90}.react-datepicker__day-names{white-space:nowrap;margin-bottom:-8px}.react-datepicker__week{white-space:nowrap}.react-datepicker__day,.react-datepicker__day-name,.react-datepicker__time-name{color:#000;display:inline-block;width:1.7rem;line-height:1.7rem;text-align:center;margin:.166rem}.react-datepicker__day,.react-datepicker__month-text,.react-datepicker__quarter-text,.react-datepicker__year-text{cursor:pointer}.react-datepicker__day:hover,.react-datepicker__month-text:hover,.react-datepicker__quarter-text:hover,.react-datepicker__year-text:hover{border-radius:.3rem;background-color:#f0f0f0}.react-datepicker__day--today,.react-datepicker__month-text--today,.react-datepicker__quarter-text--today,.react-datepicker__year-text--today{font-weight:700}.react-datepicker__day--highlighted,.react-datepicker__month-text--highlighted,.react-datepicker__quarter-text--highlighted,.react-datepicker__year-text--highlighted{border-radius:.3rem;background-color:#3dcc4a;color:#fff}.react-datepicker__day--highlighted:hover,.react-datepicker__month-text--highlighted:hover,.react-datepicker__quarter-text--highlighted:hover,.react-datepicker__year-text--highlighted:hover{background-color:#32be3f}.react-datepicker__day--highlighted-custom-1,.react-datepicker__month-text--highlighted-custom-1,.react-datepicker__quarter-text--highlighted-custom-1,.react-datepicker__year-text--highlighted-custom-1{color:#f0f}.react-datepicker__day--highlighted-custom-2,.react-datepicker__month-text--highlighted-custom-2,.react-datepicker__quarter-text--highlighted-custom-2,.react-datepicker__year-text--highlighted-custom-2{color:green}.react-datepicker__day--holidays,.react-datepicker__month-text--holidays,.react-datepicker__quarter-text--holidays,.react-datepicker__year-text--holidays{position:relative;border-radius:.3rem;background-color:#ff6803;color:#fff}.react-datepicker__day--holidays .overlay,.react-datepicker__month-text--holidays .overlay,.react-datepicker__quarter-text--holidays .overlay,.react-datepicker__year-text--holidays .overlay{position:absolute;bottom:100%;left:50%;transform:translate(-50%);background-color:#333;color:#fff;padding:4px;border-radius:4px;white-space:nowrap;visibility:hidden;opacity:0;transition:visibility 0s,opacity .3s ease-in-out}.react-datepicker__day--holidays:hover,.react-datepicker__month-text--holidays:hover,.react-datepicker__quarter-text--holidays:hover,.react-datepicker__year-text--holidays:hover{background-color:#cf5300}.react-datepicker__day--holidays:hover .overlay,.react-datepicker__month-text--holidays:hover .overlay,.react-datepicker__quarter-text--holidays:hover .overlay,.react-datepicker__year-text--holidays:hover .overlay{visibility:visible;opacity:1}.react-datepicker__day--in-range,.react-datepicker__day--in-selecting-range,.react-datepicker__day--selected,.react-datepicker__month-text--in-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__month-text--selected,.react-datepicker__quarter-text--in-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__quarter-text--selected,.react-datepicker__year-text--in-range,.react-datepicker__year-text--in-selecting-range,.react-datepicker__year-text--selected{border-radius:.3rem;background-color:#216ba5;color:#fff}.react-datepicker__day--in-range:hover,.react-datepicker__day--in-selecting-range:hover,.react-datepicker__day--selected:hover,.react-datepicker__month-text--in-range:hover,.react-datepicker__month-text--in-selecting-range:hover,.react-datepicker__month-text--selected:hover,.react-datepicker__quarter-text--in-range:hover,.react-datepicker__quarter-text--in-selecting-range:hover,.react-datepicker__quarter-text--selected:hover,.react-datepicker__year-text--in-range:hover,.react-datepicker__year-text--in-selecting-range:hover,.react-datepicker__year-text--selected:hover{background-color:#1d5d90}.react-datepicker__day--keyboard-selected,.react-datepicker__month-text--keyboard-selected,.react-datepicker__quarter-text--keyboard-selected,.react-datepicker__year-text--keyboard-selected{border-radius:.3rem;background-color:#bad9f1;color:#000}.react-datepicker__day--keyboard-selected:hover,.react-datepicker__month-text--keyboard-selected:hover,.react-datepicker__quarter-text--keyboard-selected:hover,.react-datepicker__year-text--keyboard-selected:hover{background-color:#1d5d90}.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__quarter-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range),.react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range,.react-datepicker__month-text--in-range,.react-datepicker__quarter-text--in-range,.react-datepicker__year-text--in-range){background-color:#216ba580}.react-datepicker__month--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__month--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__day--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__month-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__quarter-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range),.react-datepicker__year--selecting-range .react-datepicker__year-text--in-range:not(.react-datepicker__day--in-selecting-range,.react-datepicker__month-text--in-selecting-range,.react-datepicker__quarter-text--in-selecting-range,.react-datepicker__year-text--in-selecting-range){background-color:#f0f0f0;color:#000}.react-datepicker__day--disabled,.react-datepicker__month-text--disabled,.react-datepicker__quarter-text--disabled,.react-datepicker__year-text--disabled{cursor:default;color:#ccc}.react-datepicker__day--disabled:hover,.react-datepicker__month-text--disabled:hover,.react-datepicker__quarter-text--disabled:hover,.react-datepicker__year-text--disabled:hover{background-color:transparent}.react-datepicker__day--disabled .overlay,.react-datepicker__month-text--disabled .overlay,.react-datepicker__quarter-text--disabled .overlay,.react-datepicker__year-text--disabled .overlay{position:absolute;bottom:70%;left:50%;transform:translate(-50%);background-color:#333;color:#fff;padding:4px;border-radius:4px;white-space:nowrap;visibility:hidden;opacity:0;transition:visibility 0s,opacity .3s ease-in-out}.react-datepicker__input-container{position:relative;display:inline-block;width:100%}.react-datepicker__input-container .react-datepicker__calendar-icon{position:absolute;padding:.5rem;box-sizing:content-box}.react-datepicker__view-calendar-icon input{padding:6px 10px 5px 25px}.react-datepicker__month-read-view,.react-datepicker__month-year-read-view,.react-datepicker__year-read-view{border:1px solid transparent;border-radius:.3rem;position:relative}.react-datepicker__month-read-view:hover,.react-datepicker__month-year-read-view:hover,.react-datepicker__year-read-view:hover{cursor:pointer}.react-datepicker__month-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view:hover .react-datepicker__year-read-view--down-arrow,.react-datepicker__year-read-view:hover .react-datepicker__month-read-view--down-arrow,.react-datepicker__year-read-view:hover .react-datepicker__year-read-view--down-arrow{border-top-color:#b3b3b3}.react-datepicker__month-read-view--down-arrow,.react-datepicker__month-year-read-view--down-arrow,.react-datepicker__year-read-view--down-arrow{transform:rotate(135deg);right:-16px;top:0}.react-datepicker__month-dropdown,.react-datepicker__month-year-dropdown,.react-datepicker__year-dropdown{background-color:#f0f0f0;position:absolute;width:50%;left:25%;top:30px;z-index:1;text-align:center;border-radius:.3rem;border:1px solid #aeaeae}.react-datepicker__month-dropdown:hover,.react-datepicker__month-year-dropdown:hover,.react-datepicker__year-dropdown:hover{cursor:pointer}.react-datepicker__month-dropdown--scrollable,.react-datepicker__month-year-dropdown--scrollable,.react-datepicker__year-dropdown--scrollable{height:150px;overflow-y:scroll}.react-datepicker__month-option,.react-datepicker__month-year-option,.react-datepicker__year-option{line-height:20px;width:100%;display:block;margin-left:auto;margin-right:auto}.react-datepicker__month-option:first-of-type,.react-datepicker__month-year-option:first-of-type,.react-datepicker__year-option:first-of-type{border-top-left-radius:.3rem;border-top-right-radius:.3rem}.react-datepicker__month-option:last-of-type,.react-datepicker__month-year-option:last-of-type,.react-datepicker__year-option:last-of-type{-webkit-user-select:none;-moz-user-select:none;user-select:none;border-bottom-left-radius:.3rem;border-bottom-right-radius:.3rem}.react-datepicker__month-option:hover,.react-datepicker__month-year-option:hover,.react-datepicker__year-option:hover{background-color:#ccc}.react-datepicker__month-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-upcoming,.react-datepicker__year-option:hover .react-datepicker__navigation--years-upcoming{border-bottom-color:#b3b3b3}.react-datepicker__month-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__month-year-option:hover .react-datepicker__navigation--years-previous,.react-datepicker__year-option:hover .react-datepicker__navigation--years-previous{border-top-color:#b3b3b3}.react-datepicker__month-option--selected,.react-datepicker__month-year-option--selected,.react-datepicker__year-option--selected{position:absolute;left:15px}.react-datepicker__close-icon{cursor:pointer;background-color:transparent;border:0;outline:0;padding:0 6px 0 0;position:absolute;top:0;right:0;height:100%;display:table-cell;vertical-align:middle}.react-datepicker__close-icon:after{cursor:pointer;background-color:#216ba5;color:#fff;border-radius:50%;height:16px;width:16px;padding:2px;font-size:12px;line-height:1;text-align:center;display:table-cell;vertical-align:middle;content:"\d7"}.react-datepicker__close-icon--disabled{cursor:default}.react-datepicker__close-icon--disabled:after{cursor:default;background-color:#ccc}.react-datepicker__today-button{background:#f0f0f0;border-top:1px solid #aeaeae;cursor:pointer;text-align:center;font-weight:700;padding:5px 0;clear:left}.react-datepicker__portal{position:fixed;width:100vw;height:100vh;background-color:#000c;left:0;top:0;justify-content:center;align-items:center;display:flex;z-index:2147483647}.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__time-name{width:3rem;line-height:3rem}@media (max-height:550px),(max-width:400px){.react-datepicker__portal .react-datepicker__day,.react-datepicker__portal .react-datepicker__day-name,.react-datepicker__portal .react-datepicker__time-name{width:2rem;line-height:2rem}}.react-datepicker__portal .react-datepicker-time__header,.react-datepicker__portal .react-datepicker__current-month{font-size:1.44rem}.react-datepicker__children-container{width:13.8rem;margin:.4rem;padding-right:.2rem;padding-left:.2rem;height:auto}.react-datepicker__aria-live{position:absolute;-webkit-clip-path:circle(0);clip-path:circle(0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;width:1px;white-space:nowrap}.react-datepicker__calendar-icon{width:1em;height:1em;vertical-align:-.125em}[dir=rtl] .date-time-picker .react-datepicker__input-container input{padding-right:68px}.date-time-picker .react-datepicker__time-container,.date-time-picker .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box{width:120px}.date-time-picker__icon-wrap{position:relative;z-index:1}.date-time-picker .icon--calendar,.date-time-picker__clear-button{position:absolute}.date-time-picker .icon--calendar,.date-time-picker .icon--x{height:auto}.date-time-picker .icon--calendar .stroke,.date-time-picker .icon--x .stroke{stroke:var(--theme-elevation-800);fill:none}.date-time-picker .icon--calendar .fill,.date-time-picker .icon--x .fill{fill:var(--theme-elevation-800)}.date-time-picker__clear-button{top:10px;right:40px}.date-time-picker .icon--calendar{top:10px;right:15px;width:18px;pointer-events:none}.date-time-picker .icon--x{width:20px}.date-time-picker__clear-button{-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:transparent;border:none;outline:none;padding:0;cursor:pointer}.date-time-picker__appearance--timeOnly .react-datepicker{width:100%}.date-time-picker__appearance--timeOnly .react-datepicker__month-container,.date-time-picker__appearance--timeOnly .react-datepicker__navigation--next,.date-time-picker__appearance--timeOnly .react-datepicker__navigation--previous{display:none;visibility:hidden}.date-time-picker__appearance--timeOnly .react-datepicker-popper,.date-time-picker__appearance--timeOnly .react-datepicker__time-box,.date-time-picker__appearance--timeOnly .react-datepicker__time-container{width:120px}.date-time-picker__appearance--timeOnly .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box{width:100%}.date-time-picker .react-datepicker-wrapper{display:block}.date-time-picker .react-datepicker-wrapper,.date-time-picker .react-datepicker__input-container{width:100%}.date-time-picker .react-datepicker__input-container input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 33px 8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.date-time-picker .react-datepicker__input-container input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.date-time-picker .react-datepicker__input-container input[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.date-time-picker .react-datepicker__input-container input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.date-time-picker .react-datepicker__input-container input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.date-time-picker .react-datepicker__input-container input:hover{border-color:var(--theme-elevation-250)}.date-time-picker .react-datepicker__input-container input:active,.date-time-picker .react-datepicker__input-container input:focus,.date-time-picker .react-datepicker__input-container input:focus-within{border-color:var(--theme-elevation-400);outline:0}.date-time-picker .react-datepicker__input-container input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.date-time-picker .react-datepicker__input-container input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.date-time-picker--has-error .react-datepicker__input-container input{background-color:var(--theme-error-200)}.date-time-picker .react-datepicker{box-shadow:0 -2px 16px -2px #0003;border:1px solid var(--theme-elevation-100);background:var(--theme-input-bg);display:inline-flex;font-family:var(--font-body);font-weight:100;border-radius:0;color:var(--theme-elevation-800)}.date-time-picker .react-datepicker__header{padding-top:0;text-transform:none;text-align:center;border-radius:0;border:none;background-color:var(--theme-input-bg)}.date-time-picker .react-datepicker__header--time{padding:10px 0;border-bottom:1px solid var(--theme-elevation-150);font-weight:600}.date-time-picker .react-datepicker__navigation{background:none;line-height:1.7rem;text-align:center;cursor:pointer;position:absolute;width:0;padding:0;border:.45rem solid transparent;z-index:1;height:10px;width:10px;text-indent:-999em;overflow:hidden;top:15px}.date-time-picker .react-datepicker__navigation--next{border-left-color:var(--theme-elevation-400)}.date-time-picker .react-datepicker__navigation--next:focus{border-left-color:var(--theme-elevation-500);outline:none}.date-time-picker .react-datepicker__navigation--previous{border-right-color:var(--theme-elevation-400)}.date-time-picker .react-datepicker__navigation--previous:focus{border-right-color:var(--theme-elevation-500);outline:none}.date-time-picker .react-datepicker-time__header,.date-time-picker .react-datepicker-year-header,.date-time-picker .react-datepicker__current-month,.date-time-picker .react-datepicker__day,.date-time-picker .react-datepicker__day-name,.date-time-picker .react-datepicker__header,.date-time-picker .react-datepicker__time-name{color:var(--theme-elevation-1000)}.date-time-picker .react-datepicker__current-month{display:none}.date-time-picker .react-datepicker-year-header,.date-time-picker .react-datepicker__header__dropdown{padding:10px 0;font-weight:700}.date-time-picker .react-datepicker__month-container{border-right:1px solid var(--theme-elevation-150)}.date-time-picker .react-datepicker .react-datepicker__time-container .react-datepicker__time,.date-time-picker .react-datepicker__time,.date-time-picker .react-datepicker__time-container{background:none}.date-time-picker .react-datepicker__time-container{border-left:none}.date-time-picker .react-datepicker__month-text{padding:6px;margin:3px;font-size:11px}.date-time-picker .react-datepicker__month-text:hover{background:var(--theme-elevation-100)}.date-time-picker .react-datepicker__month-select,.date-time-picker .react-datepicker__year-select{min-width:70px;border:none;background:none;outline:none;cursor:pointer}.date-time-picker .react-datepicker__month-select option,.date-time-picker .react-datepicker__year-select option{background-color:var(--theme-elevation-50)}.date-time-picker .react-datepicker__day-names{background-color:var(--theme-elevation-100)}.date-time-picker .react-datepicker__day{box-shadow:inset 0 0 0 1px var(--theme-elevation-150);font-size:11px}.date-time-picker .react-datepicker__day:hover{background:var(--theme-elevation-100)}.date-time-picker .react-datepicker__day:focus{outline:0;background:var(--theme-elevation-400)}.date-time-picker .react-datepicker__day--selected{font-weight:700}.date-time-picker .react-datepicker__day--selected:focus{background-color:var(--theme-elevation-150)}.date-time-picker .react-datepicker__day--keyboard-selected{color:var(--theme-elevation-0);font-weight:700}.date-time-picker .react-datepicker__day--keyboard-selected:focus{background-color:var(--theme-elevation-150);box-shadow:inset 0 0 0 1px var(--theme-elevation-800),0 0 0 1px var(--theme-elevation-800)}.date-time-picker .react-datepicker__day--today{font-weight:700}.date-time-picker .react-datepicker__day,.date-time-picker .react-datepicker__day-name{width:30px;margin:3px;line-height:25px}.date-time-picker .react-datepicker-popper{z-index:10;border:none}.date-time-picker .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list{max-height:100%}.date-time-picker .react-datepicker__day--in-range,.date-time-picker .react-datepicker__day--in-selecting-range,.date-time-picker .react-datepicker__day--keyboard-selected,.date-time-picker .react-datepicker__day--selected,.date-time-picker .react-datepicker__month-text--in-range,.date-time-picker .react-datepicker__month-text--in-selecting-range,.date-time-picker .react-datepicker__month-text--keyboard-selected,.date-time-picker .react-datepicker__month-text--selected,.date-time-picker .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected{box-shadow:none;background-color:var(--theme-elevation-150);font-weight:700;color:var(--theme-elevation-800);border-radius:0}.date-time-picker .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover{background:var(--theme-elevation-100)}.date-time-picker .react-datepicker__day:hover,.date-time-picker .react-datepicker__month-text:hover{border-radius:0}.date-time-picker .react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button){right:130px}.date-time-picker .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item{line-height:20px;font-size:10px}.date-time-picker__appearance--dayOnly .react-datepicker__month-container,.date-time-picker__appearance--monthOnly .react-datepicker__month-container{border-right:none}@media (max-width:768px){.date-time-picker .react-datepicker{flex-direction:column}.date-time-picker .react-datepicker__month-container{border-right:0}.date-time-picker .react-datepicker__time-container{width:auto}.date-time-picker .react-datepicker__header--time{background-color:var(--theme-elevation-100);padding:8px 0;border-bottom:none}.date-time-picker .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box{height:120px;width:unset}.date-time-picker .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box>ul{height:120px}.date-time-picker .react-datepicker__navigation--next--with-time:not(.react-datepicker__navigation--next--with-today-button){right:0}.date-time-picker__input-wrapper .icon{top:calc(50% - 5px)}}}@layer payload-default{.timezone-picker-wrapper{display:flex;gap:calc(var(--base) / 4);margin-top:calc(var(--base) / 4);align-items:center}.timezone-picker-wrapper .field-label{margin-right:unset;color:var(--theme-elevation-400);flex-shrink:0}.timezone-picker-wrapper .timezone-picker{display:inline-block}.timezone-picker-wrapper .timezone-picker .rs__menu{min-width:calc(var(--base) * 14);overflow:hidden;border-radius:calc(var(--base) * .25)}.timezone-picker-wrapper .timezone-picker .rs__value-container{text-align:center}.timezone-picker-wrapper .timezone-picker .rs__control{background:none;border:none;padding:0;min-height:auto!important;position:relative;box-shadow:unset;min-width:var(--base)}.timezone-picker-wrapper .timezone-picker .rs__control:hover{cursor:pointer;box-shadow:unset}.timezone-picker-wrapper .timezone-picker .rs__control.rs__control--menu-is-open:before{display:block}.timezone-picker-wrapper .timezone-picker .rs__indicators{-webkit-margin-start:calc(var(--base) * .25);margin-inline-start:calc(var(--base) * .25)}.clear-indicator{cursor:pointer}}@layer payload-default;@layer payload-default;@layer payload-default{.clear-indicator:focus-visible{outline:var(--accessibility-outline)}}@layer payload-default;@layer payload-default;@layer payload-default{.dropdown-indicator{cursor:pointer;border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body)}.dropdown-indicator:focus-visible{outline:var(--accessibility-outline)}.dropdown-indicator__icon .stroke{stroke-width:1px}.react-select-container{width:100%}.react-select .rs__control{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1);height:auto;padding:7px 12px;flex-wrap:nowrap}.react-select .rs__control:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.react-select .rs__control[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.react-select .rs__control::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.react-select .rs__control::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.react-select .rs__control:hover{border-color:var(--theme-elevation-250)}.react-select .rs__control:active,.react-select .rs__control:focus,.react-select .rs__control:focus-within{border-color:var(--theme-elevation-400);outline:0}.react-select .rs__control:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.react-select .rs__control:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.react-select .rs__menu-notice{padding:10px 12px}.react-select .rs__indicator{padding:0 4px;cursor:pointer}.react-select .rs__indicator-separator{display:none}.react-select .rs__input-container{color:var(--theme-elevation-1000)}.react-select .rs__input{font-family:var(--font-body);width:10px}.react-select .rs__menu{z-index:4;border-radius:0;box-shadow:0 -2px 16px -2px #0003;background:var(--theme-input-bg)}.react-select .rs__group-heading{color:var(--theme-elevation-800);padding-left:10px;margin-top:5px;margin-bottom:5px}.react-select .rs__option{font-family:var(--font-body);font-size:13px;padding:7.5px 15px;color:var(--theme-elevation-800)}.react-select .rs__option--is-focused{background-color:var(--theme-elevation-100)}.react-select .rs__option--is-selected{background-color:var(--theme-elevation-300)}.react-select--error div.rs__control,.react-select--error:focus-within div.rs__control,.react-select--error:hover div.rs__control{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}.react-select--error div.rs__control>div.rs__indicator>button.dropdown-indicator[type=button],.react-select--error:focus-within div.rs__control>div.rs__indicator>button.dropdown-indicator[type=button],.react-select--error:hover div.rs__control>div.rs__indicator>button.dropdown-indicator[type=button]{border:none}.react-select.rs--is-disabled .rs__control{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.react-select.rs--is-disabled .rs__control:hover{border-color:var(--theme-elevation-150);box-shadow:none}.multi-value.rs__multi-value{display:flex;padding:0;border:1px solid var(--theme-border-color);border-radius:var(--style-radius-s);line-height:calc(1.5384615385rem - 2px);margin:5px 10px 5px 0;transition:border .2s cubic-bezier(.2,0,0,1)}.multi-value.rs__multi-value:hover{border:1px solid var(--theme-elevation-250)}.multi-value--is-dragging{z-index:2}html[data-theme=dark] .multi-value.rs__multi-value,html[data-theme=light] .multi-value.rs__multi-value{background:var(--theme-elevation-50)}.multi-value-label{margin:0;font-size:12px;line-height:20px;display:flex;align-items:center;max-width:150px;color:currentColor;padding:0 8px}.multi-value-label__text{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.multi-value-label__text--editable{cursor:text;outline:var(--accessibility-outline)}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default{.multi-value-label:focus-visible{outline:var(--accessibility-outline)}}@layer payload-default;@layer payload-default;@layer payload-default{.multi-value-remove{cursor:pointer;width:20px;display:flex;align-items:center;justify-content:center;position:relative;background-color:transparent;border:none;padding:0;color:inherit}.multi-value-remove:hover{color:var(--theme-elevation-800);background:var(--theme-elevation-150)}.multi-value-remove__icon{width:100%;height:100%}.value-container{flex-grow:1;min-width:0;display:flex;align-items:center;flex-direction:row;gap:calc(var(--base) / 2)}.value-container__label{color:var(--theme-elevation-550)}.value-container .rs__value-container{overflow:visible;padding:2px;gap:2px}.value-container .rs__value-container>*{margin:0;padding-top:0;padding-bottom:0;color:currentColor}.value-container .rs__value-container>* .field-label{padding-bottom:0}.value-container .rs__value-container--is-multi{width:calc(100% + 5px)}.value-container .rs__value-container--is-multi.rs__value-container--has-value{padding:0;-webkit-margin-start:-4px;margin-inline-start:-4px}html[data-theme=light] .date-time-field--has-error .react-datepicker__input-container input{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .date-time-field--has-error .react-datepicker__input-container input{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .date-time-field--has-error .react-datepicker__input-container input:hover{border-color:var(--theme-error-500)}.field-type.email{position:relative}.field-type.email input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.field-type.email input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.field-type.email input[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{.field-type.email input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.email input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.email input:hover{border-color:var(--theme-elevation-250)}.field-type.email input:active,.field-type.email input:focus,.field-type.email input:focus-within{border-color:var(--theme-elevation-400);outline:0}.field-type.email input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.field-type.email input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.field-type.email.error input{background-color:var(--theme-error-200)}html[data-theme=light] .field-type.email.error input{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .field-type.email.error input{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .field-type.email.error input:hover{border-color:var(--theme-error-500)}.group-field{margin-left:calc(var(--gutter-h) * -1);margin-right:calc(var(--gutter-h) * -1);border-bottom:1px solid var(--theme-elevation-100);border-top:1px solid var(--theme-elevation-100)}.group-field--top-level{padding:40px var(--gutter-h)}.group-field--top-level:first-child{padding-top:0;border-top:0}.group-field--within-collapsible{margin-left:calc(var(--base) * -1);margin-right:calc(var(--base) * -1);padding:var(--base)}.group-field--within-collapsible:first-child{border-top:0;padding-top:0;margin-top:0}.group-field--within-collapsible:last-child{padding-bottom:0;border-bottom:0}.group-field--within-group{margin-left:0;margin-right:0;padding:0;border-top:0;border-bottom:0}.group-field--within-row{margin:0;border-top:0;border-bottom:0}.group-field--within-tab:first-child{margin-top:0;border-top:0;padding-top:0}.group-field--within-tab:last-child{margin-bottom:0;border-bottom:0;padding-bottom:0}.group-field--gutter{border-left:1px solid var(--theme-elevation-100);padding:0 0 0 1.5384615385rem}.group-field__header{margin-bottom:calc(var(--base) / 2);display:flex;align-items:center;gap:10px}.group-field__header>header{display:flex;flex-direction:column;gap:calc(var(--base) / 4)}.group-field__title{margin-bottom:0}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:768px){.group-field--top-level{padding:var(--base) var(--gutter-h)}.group-field--top-level:first-child{padding-top:0;border-top:0}.group-field__header{margin-bottom:calc(var(--base) / 2)}.group-field--within-collapsible{margin-left:calc(var(--gutter-h) * -1);margin-right:calc(var(--gutter-h) * -1)}.group-field--within-group{padding:0}.group-field--gutter{padding-left:var(--gutter-h)}}.group-field+.group-field{border-top:0;padding-top:0}.group-field--within-row+.group-field--within-row{margin-top:0}.group-field--within-tab+.group-field--within-row{padding-top:0}html[data-theme=light] .group-field--has-error .group-field__header{color:var(--theme-error-750)}html[data-theme=light] .group-field--has-error .group-field__header:after{background:var(--theme-error-500)}html[data-theme=dark] .group-field--has-error .group-field__header{color:var(--theme-error-500)}html[data-theme=dark] .group-field--has-error .group-field__header:after{background:var(--theme-error-500)}.pill-selector{display:flex;flex-wrap:wrap;background:var(--theme-elevation-50);padding:var(--base);gap:calc(var(--base) / 2)}.pill-selector__pill{background-color:transparent;box-shadow:0 0 0 1px var(--theme-elevation-150)}.pill-selector__pill.pill-selector__pill{cursor:pointer}.pill-selector__pill.pill-selector__pill:hover{background-color:var(--theme-elevation-100)}.pill-selector__pill.pill-selector__pill--selected{background-color:var(--theme-elevation-0);box-shadow:0 0 1px 1px var(--theme-elevation-150),0 2px 4px -2px #0000001a}.pill-selector__pill.pill-selector__pill--selected:hover{background-color:var(--theme-elevation-0);box-shadow:0 0 1px 1px var(--theme-elevation-250),0 3px 4px -1px #0000001a}.pill-selector .pill__icon{padding:0}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:768px){.pill-selector{padding:calc(var(--base) / 2)}}}@layer payload-default;@layer payload-default;@layer payload-default{.loading-overlay{isolation:isolate;height:100%;width:100%;left:0;top:0;bottom:0;position:fixed;display:flex;align-items:center;justify-content:center;flex-direction:column;pointer-events:none;z-index:calc(var(--z-status) + 1);transition-property:left,width;transition:.25s ease}.loading-overlay.loading-overlay--entering{opacity:1;animation:fade-in ease;pointer-events:all}.loading-overlay.loading-overlay--exiting{opacity:0;animation:fade-out ease}.loading-overlay:after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:var(--theme-elevation-0);opacity:.85;z-index:-1}.loading-overlay__bars{display:grid;grid-template-columns:1fr 1fr 1fr 1fr 1fr;grid-gap:7px;gap:7px;align-items:center}.loading-overlay__bar{width:2px;background-color:currentColor;height:15px}.loading-overlay__bar:first-child{transform:translateY(0);animation:animate-bar--odd 1.25s infinite}.loading-overlay__bar:nth-child(2){transform:translateY(-2px);animation:animate-bar--even 1.25s infinite}.loading-overlay__bar:nth-child(3){transform:translateY(0);animation:animate-bar--odd 1.25s infinite}.loading-overlay__bar:nth-child(4){transform:translateY(-2px);animation:animate-bar--even 1.25s infinite}.loading-overlay__bar:nth-child(5){transform:translateY(0);animation:animate-bar--odd 1.25s infinite}.loading-overlay__text{margin-top:15px;text-transform:uppercase;font-family:var(--font-body);font-size:13px;letter-spacing:3px}@keyframes animate-bar--even{0%{transform:translateY(2px)}50%{transform:translateY(-2px)}to{transform:translateY(2px)}}@keyframes animate-bar--odd{0%{transform:translateY(-2px)}50%{transform:translateY(2px)}to{transform:translateY(-2px)}}@keyframes fade-in{0%{opacity:0}to{opacity:1}}@keyframes fade-out{0%{opacity:1}to{opacity:0}}}@layer payload-default;@layer payload-default;@layer payload-default{.doc-drawer__toggler{background:transparent;border:0;margin:0;padding:0;cursor:pointer;color:inherit}.doc-drawer__toggler:focus,.doc-drawer__toggler:focus-within{outline:none}.doc-drawer__toggler:disabled{pointer-events:none}.code-cell{font-size:1rem;line-height:20px;border:0;display:inline-flex;vertical-align:middle;background:var(--theme-elevation-150);border-radius:4px;padding:0 5px;background:var(--theme-elevation-100);color:var(--theme-elevation-800)}[dir=ltr] .code-cell{padding-left:6.75px}[dir=rtl] .code-cell{padding-right:6.75px}.code-cell:hover{text-decoration:inherit}.bool-cell{font-size:1rem;line-height:20px;border:0;display:inline-flex;vertical-align:middle;background:var(--theme-elevation-150);border-radius:3px;padding:0 5px;background:var(--theme-elevation-100);color:var(--theme-elevation-800)}[dir=ltr] .bool-cell{padding-left:6.75px}[dir=rtl] .bool-cell{padding-right:6.75px}.file{display:flex;flex-wrap:nowrap}.file__thumbnail{display:inline-block;max-width:calc(var(--base) * 2);height:calc(var(--base) * 2);border-radius:var(--style-radius-s)}.file__filename{align-self:center}[dir=ltr] .file__filename{margin-left:var(--base)}[dir=rtl] .file__filename{margin-right:var(--base)}.thumbnail{min-height:100%;flex-shrink:0;align-self:stretch;overflow:hidden}.thumbnail img,.thumbnail svg{width:100%;height:100%;object-fit:cover}.thumbnail--size-expand{max-height:100%;width:100%;padding-top:100%;position:relative}.thumbnail--size-expand img,.thumbnail--size-expand svg{position:absolute;top:0}.thumbnail--size-large{max-height:180px;width:180px}.thumbnail--size-medium{max-height:140px;width:140px}.thumbnail--size-small{max-height:100px;width:100px}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1440px){.thumbnail .thumbnail{width:100px}}}@layer payload-default;@layer payload-default;@layer payload-default{.json-cell{font-size:1rem;line-height:20px;border:0;display:inline-flex;vertical-align:middle;background:var(--theme-elevation-150);border-radius:4px;padding:0 5px;background:var(--theme-elevation-100);color:var(--theme-elevation-800)}[dir=ltr] .json-cell{padding-left:6.75px}[dir=rtl] .json-cell{padding-right:6.75px}.default-cell__first-cell{border:0;background-color:transparent;padding:0;cursor:pointer;text-decoration:underline;text-align:left;white-space:nowrap}.drawer-link{display:flex;gap:calc(var(--base) / 2)}.relationship-table{position:relative}.relationship-table__header{display:flex;justify-content:space-between;margin-bottom:var(--base)}.relationship-table__actions{display:flex;align-items:center;gap:var(--base)}.relationship-table__columns-inner{padding-bottom:var(--base)}.relationship-table__add-new-polymorphic-wrapper{display:inline-flex}.relationship-table__add-new-polymorphic .btn__label{display:flex;text-wrap:nowrap;align-items:center}.relationship-table .table table{width:100%;overflow:auto}.relationship-table .table table [class^=cell]>a,.relationship-table .table table [class^=cell]>p,.relationship-table .table table [class^=cell]>span{line-clamp:4;-webkit-box-orient:vertical;-webkit-line-clamp:4;overflow:hidden;display:-webkit-box;max-width:100vw}.relationship-table .table td:first-child,.relationship-table .table th{min-width:0}.clickable-arrow{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body);cursor:pointer;width:40px;height:40px;display:flex;justify-content:center;align-content:center;align-items:center;outline:0;padding:10px;color:var(--theme-elevation-800);line-height:20px}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default{.clickable-arrow:not(.clickable-arrow--is-disabled):focus-visible,.clickable-arrow:not(.clickable-arrow--is-disabled):hover{background:var(--theme-elevation-100)}.clickable-arrow:focus-visible{outline:var(--accessibility-outline)}.clickable-arrow--right .icon{transform:rotate(-90deg)}.clickable-arrow--left .icon{transform:rotate(90deg)}.clickable-arrow--is-disabled{cursor:default}.clickable-arrow--is-disabled .icon .stroke{stroke:var(--theme-elevation-400)}.paginator__page{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body)}.paginator{display:flex;margin-bottom:1.5384615385rem}.paginator__page{cursor:pointer}.paginator__page--is-current{background:var(--theme-elevation-100);color:var(--theme-elevation-400);cursor:default}.paginator__page--is-last-page{margin-right:0}.paginator .clickable-arrow--right{margin-right:5px}.paginator__page{width:40px;height:40px;display:flex;justify-content:center;align-content:center;outline:0;padding:10px;color:var(--theme-elevation-800);line-height:20px}}@layer payload-default;@layer payload-default{}@layer payload-default{.paginator__page:focus-visible{outline:var(--accessibility-outline)}.paginator__page,.paginator__separator{margin-right:5px}.paginator__separator{align-self:center;color:var(--theme-elevation-400)}.json-field{position:relative}.json-field.error .code-editor{border-color:var(--theme-error-500)}.json-field.error .margin,.json-field.error .monaco-editor-background{background-color:var(--theme-error-50)}.field-type.number{position:relative}.field-type.number:not(.has-many) input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.field-type.number:not(.has-many) input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.field-type.number:not(.has-many) input[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{.field-type.number:not(.has-many) input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.number:not(.has-many) input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.number:not(.has-many) input:hover{border-color:var(--theme-elevation-250)}.field-type.number:not(.has-many) input:active,.field-type.number:not(.has-many) input:focus,.field-type.number:not(.has-many) input:focus-within{border-color:var(--theme-elevation-400);outline:0}.field-type.number:not(.has-many) input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.field-type.number:not(.has-many) input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}html[data-theme=light] .field-type.number.error input{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .field-type.number.error input{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .field-type.number.error input:hover{border-color:var(--theme-error-500)}.point,.point .input-wrapper{position:relative}.point__wrap{display:flex;width:calc(100% + 20px);margin:0 -10px;list-style:none;padding:0}.point__wrap li{padding:0 10px;width:50%}.point input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.point input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.point input[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.point input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.point input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.point input:hover{border-color:var(--theme-elevation-250)}.point input:active,.point input:focus,.point input:focus-within{border-color:var(--theme-elevation-400);outline:0}.point input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.point input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}html[data-theme=light] .point.error input{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .point.error input{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .point.error input:hover{border-color:var(--theme-error-500)}.radio-group .tooltip:not([aria-hidden=true]){right:auto;position:static;margin-bottom:.2em;max-width:-moz-fit-content;max-width:fit-content}.radio-group--layout-horizontal ul{display:flex;flex-wrap:wrap}.radio-group--layout-horizontal li{flex-shrink:0}[dir=ltr] .radio-group--layout-horizontal li{padding-right:1.5384615385rem}[dir=rtl] .radio-group--layout-horizontal li{padding-left:1.5384615385rem}.radio-group ul{list-style:none;padding:0;margin:0}.radio-group--read-only .radio-input{cursor:default}.radio-group--read-only .radio-input:hover{border-color:var(--theme-elevation-50)}.radio-group--read-only .radio-input__label{color:var(--theme-elevation-400)}.radio-group--read-only .radio-input--is-selected .radio-input__styled-radio:before{background-color:var(--theme-elevation-250)}.radio-group--read-only .radio-input:not(.radio-input--is-selected):hover .radio-input__styled-radio:before{opacity:0}html[data-theme=light] .radio-group.error .radio-input__styled-radio{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .radio-group.error .radio-input__styled-radio{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .radio-group.error .radio-input__styled-radio:hover{border-color:var(--theme-error-500)}.radio-input{display:flex;align-items:center;cursor:pointer;margin:2px 0;position:relative}.radio-input input[type=radio]{opacity:0;margin:0;position:absolute}.radio-input input[type=radio]:focus+.radio-input__styled-radio{box-shadow:0 0 3px 3px var(--theme-success-400)}.radio-input__styled-radio{border:1px solid var(--theme-border-color);background-color:var(--theme-input-bg);box-shadow:0 2px 2px -1px #0000001a;width:1.5384615385rem;height:1.5384615385rem;position:relative;padding:0;display:inline-block;border-radius:50%}.radio-input__styled-radio:before{content:" ";display:block;border-radius:100%;background-color:var(--theme-elevation-800);width:calc(100% - 8px);height:calc(100% - 8px);border:4px solid var(--theme-elevation-0);opacity:0}.radio-input__styled-radio--disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.radio-input__styled-radio--disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.radio-input__styled-radio--disabled:before{border-color:var(--theme-elevation-100)}[dir=rtl] .radio-input__label{margin-left:0;margin-right:10px}.radio-input__label{margin-left:10px}.radio-input--is-selected .radio-input__styled-radio:before{opacity:1}.radio-input:not(.radio-input--is-selected):hover .radio-input__styled-radio:before{opacity:.2}.relationship-add-new{display:flex;align-items:stretch}.relationship-add-new .popup__trigger-wrap{display:flex;align-items:stretch;height:100%}.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled),.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1);border-top-left-radius:0;border-bottom-left-radius:0;position:relative;height:100%;margin:0 0 0 -1px;padding:0 10px;align-items:center;display:flex;cursor:pointer}.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler:not(:disabled):hover,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled):not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler[data-rtl=true],.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled)[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler::-webkit-input-placeholder,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled)::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler::-moz-placeholder,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled)::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler:hover,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled):hover{border-color:var(--theme-elevation-250)}.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler:active,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler:focus,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler:focus-within,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled):active,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled):focus,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled):focus-within{border-color:var(--theme-elevation-400);outline:0}.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler:disabled,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled):disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled).doc-drawer__toggler:disabled:hover,.relationship-add-new__add-button:not(.relationship-add-new__add-button--unstyled):disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.relationship-add-new__add-button{border-top-left-radius:0;border-bottom-left-radius:0;position:relative;height:100%;margin:0 0 0 -1px;padding:0 10px;align-items:center;display:flex;cursor:pointer}.relationship--multi-value-label__content{margin:0;font-size:12px;line-height:20px}.relationship--multi-value-label{display:flex;-webkit-padding-start:8px;padding-inline-start:8px;gap:4px}.relationship--multi-value-label__content{line-height:22px;max-width:150px;color:currentColor;display:flex;align-items:center}.relationship--multi-value-label__text{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.relationship--multi-value-label__drawer-toggler{border:none;background-color:transparent;padding:0;cursor:pointer;position:relative;display:flex;align-items:center;justify-content:center;margin-left:4px;pointer-events:all}.relationship--multi-value-label__drawer-toggler .icon{width:20px;height:20px;padding:2px}.relationship--multi-value-label__drawer-toggler:hover{background-color:var(--theme-elevation-150)}}@layer payload-default{}@layer payload-default;@layer payload-default{.relationship--multi-value-label__drawer-toggler:focus-visible{outline:var(--accessibility-outline)}}@layer payload-default;@layer payload-default;@layer payload-default{.field-type.relationship{position:relative}.relationship__wrap{display:flex;width:100%}.relationship__wrap div.react-select{width:100%;min-width:0}.relationship__error-loading{border:1px solid var(--theme-error-500);min-height:40px;padding:10px 15px;background-color:var(--theme-error-500);color:var(--theme-elevation-0)}.relationship--allow-create .rs__control{border-top-right-radius:0;border-bottom-right-radius:0}html[data-theme=light] .relationship.error button.relationship-add-new__add-button,html[data-theme=light] .relationship.error>.relationship__wrap .rs__control{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .relationship.error>.relationship__wrap .rs__control{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .relationship.error>.relationship__wrap .rs__control:hover{border-color:var(--theme-error-500)}html[data-theme=dark] .relationship.error button.relationship-add-new__add-button{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .relationship.error button.relationship-add-new__add-button:hover{border-color:var(--theme-error-500)}.relationship--single-value.rs__single-value{overflow:visible;min-width:0}.relationship--single-value__label-text{max-width:unset;display:flex;align-items:center;overflow:visible;width:100%;flex-shrink:1}.relationship--single-value__text{overflow:hidden;text-overflow:ellipsis}.relationship--single-value__drawer-toggler{border:none;background-color:transparent;padding:0;cursor:pointer;position:relative;display:inline-flex;align-items:center;justify-content:center;margin-left:5px;pointer-events:all}.relationship--single-value__drawer-toggler .icon{width:15px;height:15px}}@layer payload-default;@layer payload-default;@layer payload-default{.relationship--single-value__drawer-toggler:focus-visible{outline:var(--accessibility-outline)}.relationship--single-value__drawer-toggler:hover{background-color:var(--theme-elevation-100)}.relationship--single-value__label{flex-grow:1}.field-type.row{margin-bottom:0}.field-type.row .row__fields{display:flex;flex-wrap:wrap;row-gap:calc(var(--base) * .8)}.field-type.row .row__fields>*{flex:0 1 var(--field-width);display:flex;flex-direction:column;justify-content:flex-start}}@layer payload-default;@layer payload-default;@layer payload-default{.field-type.row .row__fields:has(>:first-child){margin-bottom:var(--base)}.field-type.row .row__fields:has(>:nth-child(2)){margin-inline:calc(var(--base) / -4)}.field-type.row .row__fields:has(>:nth-child(2))>*{flex:0 1 calc(var(--field-width) - var(--base) * .5);margin-inline:calc(var(--base) / 4)}@media (max-width:1024px){.field-type.row .row__fields{display:block;margin-left:0;margin-right:0;width:100%}.field-type.row .row__fields>*{margin-left:0;margin-right:0;width:100%!important;padding-left:0;padding-right:0}}}@layer payload-default;@layer payload-default;@layer payload-default{.field-type.select{position:relative}html[data-theme=light] .field-type.select.error .rs__control{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .field-type.select.error .rs__control{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .field-type.select.error .rs__control:hover{border-color:var(--theme-error-500)}.tabs-field__tab-button{font-family:var(--font-body);font-weight:500;margin:0;font-size:16px;line-height:20px;letter-spacing:-.375px;border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;display:flex;padding-bottom:20px;-webkit-margin-end:1.5384615385rem;margin-inline-end:1.5384615385rem;cursor:pointer;opacity:.5;position:relative;white-space:nowrap;flex-shrink:0;gap:10px}.tabs-field__tab-button:last-child{margin:0}.tabs-field__tab-button:after{content:" ";position:absolute;right:0;bottom:-1px;left:0;height:1px;background:var(--theme-elevation-800);opacity:0}.tabs-field__tab-button:hover{opacity:.75}.tabs-field__tab-button:hover:after{opacity:.2}.tabs-field__tab-button--hidden{display:none}.tabs-field__tab-button--active{opacity:1!important}.tabs-field__tab-button--active:after{opacity:1!important;height:2px}.tabs-field__tab-button__description{margin-bottom:calc(var(--base) / 2)}}@layer payload-default{}@layer payload-default{}@layer payload-default{@media (max-width:768px){.tabs-field__tab-button{margin:0 15px 0 0;padding-bottom:10px}.tabs-field__tab-button:last-child{margin:0}}html[data-theme=light] .tabs-field__tab-button--has-error{color:var(--theme-error-750)}html[data-theme=light] .tabs-field__tab-button--has-error:after{background:var(--theme-error-500)}html[data-theme=dark] .tabs-field__tab-button--has-error{color:var(--theme-error-500)}html[data-theme=dark] .tabs-field__tab-button--has-error:after{background:var(--theme-error-500)}.tabs-field{margin-top:40px;margin-left:calc(var(--gutter-h) * -1);margin-right:calc(var(--gutter-h) * -1)}.tabs-field--hidden{display:none}.tabs-field__content-wrap{padding-left:var(--gutter-h);padding-right:var(--gutter-h)}.tabs-field--within-collapsible{margin:0 calc(1.5384615385rem * -1)}.tabs-field--within-collapsible .tabs-field__content-wrap{padding-left:1.5384615385rem;padding-right:1.5384615385rem}.tabs-field--within-collapsible .tabs-field__tabs:after,.tabs-field--within-collapsible .tabs-field__tabs:before{content:" ";display:block;width:1.5384615385rem}.tabs-field__tabs-wrap{overflow-x:auto;overflow-y:hidden;margin-bottom:1.5384615385rem}.tabs-field__tabs{border-bottom:1px solid var(--theme-elevation-100);display:inline-flex;min-width:100%;vertical-align:bottom}.tabs-field__tabs:after,.tabs-field__tabs:before{content:" ";display:block;width:var(--gutter-h);flex-shrink:0}.tabs-field__tab--hidden{display:none}.tabs-field__description{margin-bottom:calc(var(--base) / 2)}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:768px){.tabs-field--within-collapsible{margin-left:calc(var(--gutter-h) * -1);margin-right:calc(var(--gutter-h) * -1)}}}@layer payload-default;@layer payload-default;@layer payload-default{.field-type.text{position:relative}.field-type.text:not(.has-many) input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.field-type.text:not(.has-many) input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.field-type.text:not(.has-many) input[data-rtl=true]{direction:rtl}.field-type.text:not(.has-many) input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.text:not(.has-many) input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.text:not(.has-many) input:hover{border-color:var(--theme-elevation-250)}.field-type.text:not(.has-many) input:active,.field-type.text:not(.has-many) input:focus,.field-type.text:not(.has-many) input:focus-within{border-color:var(--theme-elevation-400);outline:0}.field-type.text:not(.has-many) input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.field-type.text:not(.has-many) input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.has-many .rs__input-container{overflow:hidden}html[data-theme=light] .field-type.text.error input{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .field-type.text.error input{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .field-type.text.error input:hover{border-color:var(--theme-error-500)}.field-type.textarea{position:relative;display:flex;flex-direction:column}.field-type.textarea textarea{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1);overflow-y:auto;resize:vertical;min-height:60px;height:auto;display:flex}.field-type.textarea textarea:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.field-type.textarea textarea[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.field-type.textarea textarea::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.textarea textarea::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.textarea textarea:hover{border-color:var(--theme-elevation-250)}.field-type.textarea textarea:active,.field-type.textarea textarea:focus,.field-type.textarea textarea:focus-within{border-color:var(--theme-elevation-400);outline:0}.field-type.textarea textarea:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.field-type.textarea textarea:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.field-type.textarea textarea:not(:empty){field-sizing:content;min-height:calc(var(--rows) * var(--base) + var(--base) * .8 + 2px)}.field-type.textarea.read-only .textarea-outer{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.field-type.textarea.read-only .textarea-outer:hover{border-color:var(--theme-elevation-150);box-shadow:none}html[data-theme=light] .field-type.textarea.error textarea{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .field-type.textarea.error textarea{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .field-type.textarea.error textarea:hover{border-color:var(--theme-error-500)}.dropzone{position:relative;padding:calc(var(--base) * .9) var(--base);background:transparent;border:1px dotted var(--theme-elevation-400);border-radius:var(--style-radius-s);height:100%;width:100%;box-shadow:0 0 0 0 transparent;transition:all .1s cubic-bezier(0,.2,.2,1)}.dropzone,.dropzone .btn{display:flex;align-items:center}.dropzone .btn{margin:0;justify-content:center}.dropzone.dragging{border-color:var(--theme-success-500);background:var(--theme-success-150);box-shadow:0 4px 8px -3px #0000001a}.dropzone.dragging *{pointer-events:none}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.dropzone{display:block;text-align:center}}.dropzone.dropzoneStyle--none{all:unset}.drawer-close-button{--size:calc(var(--base) * 1.2);border:0;background-color:transparent;padding:0;cursor:pointer;overflow:hidden;direction:ltr;display:flex;align-items:center;justify-content:center;width:var(--size);height:var(--size)}.drawer-close-button svg{margin:calc(-1 * var(--size));width:calc(var(--size) * 2);height:calc(var(--size) * 2);position:relative}.drawer-close-button svg .stroke{stroke-width:1px;vector-effect:non-scaling-stroke}.bulk-upload--drawer-header{display:flex;justify-content:space-between;align-items:center;padding:calc(var(--base) * 2.5) var(--gutter-h);height:48px;border-bottom:1px solid var(--theme-border-color)}.bulk-upload--drawer-header h2{margin:0}.bulk-upload--add-files{height:100%;display:flex;flex-direction:column}.bulk-upload--add-files__dropArea{height:100%;padding:calc(var(--base) * 2) var(--gutter-h)}.bulk-upload--add-files .dropzone{flex-direction:column;justify-content:center;display:flex;gap:var(--base);background-color:var(--theme-elevation-50)}.bulk-upload--add-files .dropzone p{margin:0}.bulk-upload--add-files__dragAndDropText{margin:0;text-transform:lowercase;align-self:center}.field-select{margin-bottom:20px}.edit-many-bulk-uploads__toggle{font-size:1rem;line-height:24px;display:inline-flex;background:var(--theme-elevation-150);color:var(--theme-elevation-800);border-radius:3px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;border:0;padding:0 8px;align-items:center;cursor:pointer;text-decoration:none}.edit-many-bulk-uploads__toggle:active,.edit-many-bulk-uploads__toggle:focus{outline:none}.edit-many-bulk-uploads__toggle:active,.edit-many-bulk-uploads__toggle:hover{background:var(--theme-elevation-100)}.edit-many-bulk-uploads__form{height:100%}.edit-many-bulk-uploads__main{width:calc(100% - 300px);display:flex;flex-direction:column;min-height:100%}.edit-many-bulk-uploads__header{display:flex;margin-top:50px;margin-bottom:20px;width:100%}.edit-many-bulk-uploads__header__title{margin:0;flex-grow:1}.edit-many-bulk-uploads__header__close{border:0;background-color:transparent;padding:0;cursor:pointer;overflow:hidden;width:20px;height:20px}.edit-many-bulk-uploads__header__close svg{width:40px;height:40px;position:relative;inset-inline-start:-10px;top:-10px}.edit-many-bulk-uploads__header__close svg .stroke{stroke-width:2px;vector-effect:non-scaling-stroke}.edit-many-bulk-uploads__edit{padding-top:20px;padding-bottom:40px;flex-grow:1}[dir=rtl] .edit-many-bulk-uploads__sidebar-wrap{left:0;border-right:1px solid var(--theme-elevation-100);right:auto}.edit-many-bulk-uploads__sidebar-wrap{position:fixed;width:300px;height:100%;top:0;right:0;overflow:visible;border-left:1px solid var(--theme-elevation-100)}.edit-many-bulk-uploads__sidebar{width:100%;height:100%;overflow-y:auto}.edit-many-bulk-uploads__sidebar-sticky-wrap{display:flex;flex-direction:column;min-height:100%}[dir=ltr] .edit-many-bulk-uploads__collection-actions,[dir=ltr] .edit-many-bulk-uploads__meta,[dir=ltr] .edit-many-bulk-uploads__sidebar-fields{padding-left:30px}[dir=rtl] .edit-many-bulk-uploads__collection-actions,[dir=rtl] .edit-many-bulk-uploads__meta,[dir=rtl] .edit-many-bulk-uploads__sidebar-fields{padding-right:30px}.edit-many-bulk-uploads__document-actions{padding-right:1.5384615385rem;position:-webkit-sticky;position:sticky;top:0;z-index:var(--z-nav)}.edit-many-bulk-uploads__document-actions>*{position:relative;z-index:1}}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.edit-many-bulk-uploads__document-actions:after,.edit-many-bulk-uploads__document-actions:before{content:" ";position:absolute;inset:0}.edit-many-bulk-uploads__document-actions:before{background:var(--theme-bg);opacity:.75}.edit-many-bulk-uploads__document-actions:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}}.edit-many-bulk-uploads__document-actions{display:flex;flex-wrap:wrap;padding:20px;gap:10px}.edit-many-bulk-uploads__document-actions .form-submit{width:calc(50% - 20px)}@media (max-width:1024px){.edit-many-bulk-uploads__document-actions .form-submit{width:auto;flex-grow:1}}.edit-many-bulk-uploads__document-actions .form-submit .btn{width:100%;padding-left:10px;padding-right:10px;margin-bottom:0}@media (max-width:1024px){.edit-many-bulk-uploads__main{width:100%;min-height:auto}.edit-many-bulk-uploads__sidebar-wrap{position:static;width:100%;height:auto}.edit-many-bulk-uploads__form{display:block}.edit-many-bulk-uploads__edit{padding-top:0;padding-bottom:0}.edit-many-bulk-uploads__document-actions{position:fixed;inset:auto 0 0;z-index:var(--z-nav)}.edit-many-bulk-uploads__document-actions,.edit-many-bulk-uploads__sidebar-fields{padding-left:var(--gutter-h);padding-right:var(--gutter-h)}}}@layer payload-default;@layer payload-default;@layer payload-default{.bulk-upload--actions-bar{display:flex;padding-inline:var(--gutter-h);align-items:center;border-bottom:1px solid var(--theme-border-color);position:-webkit-sticky;position:sticky;z-index:1;top:0;background-color:var(--theme-bg);height:var(--doc-controls-height)}.bulk-upload--actions-bar__navigation{display:flex;gap:var(--base);align-items:center;width:100%}.bulk-upload--actions-bar__locationText{font-feature-settings:"tnum";font-variant-numeric:tabular-nums;margin:0}.bulk-upload--actions-bar__controls{display:flex;gap:calc(var(--base) / 2)}.bulk-upload--actions-bar__controls .btn{background-color:var(--theme-elevation-100);width:calc(var(--base) * 1.2);height:calc(var(--base) * 1.2)}.bulk-upload--actions-bar__controls .btn:hover{background-color:var(--theme-elevation-200)}.bulk-upload--actions-bar__controls .btn__label{display:flex}.bulk-upload--actions-bar__buttons{display:flex;gap:var(--base);margin-left:auto}@media (max-width:1024px){.bulk-upload--actions-bar__navigation{justify-content:space-between}.bulk-upload--actions-bar__saveButtons{display:none}}}@layer payload-default;@layer payload-default;@layer payload-default{.document-fields{width:100%;display:flex;--doc-sidebar-width:325px}.document-fields--has-sidebar .document-fields__main{width:66.66%}[dir=ltr] .document-fields--has-sidebar .document-fields__edit{top:0;right:0;border-right:1px solid var(--theme-elevation-100);padding-right:calc(var(--base) * 2)}[dir=rtl] .document-fields--has-sidebar .document-fields__edit{top:0;left:0;border-left:1px solid var(--theme-elevation-100);padding-left:calc(var(--base) * 2)}.document-fields--has-sidebar .document-fields__fields>.group-field,.document-fields--has-sidebar .document-fields__fields>.tabs-field{margin-right:calc(var(--base) * -2)}.document-fields__main{width:100%;display:flex;flex-direction:column;min-height:100%;flex-grow:1}.document-fields__edit{padding-top:calc(var(--base) * 1.5);padding-bottom:var(--spacing-view-bottom);flex-grow:1}.document-fields__sidebar-wrap{position:-webkit-sticky;position:sticky;top:var(--doc-controls-height);width:33.33%;height:calc(100vh - var(--doc-controls-height));min-width:var(--doc-sidebar-width);flex-shrink:0}.document-fields__sidebar{width:100%;height:100%;overflow-y:auto;display:flex;flex-direction:column;min-height:100%}.document-fields__sidebar-fields{display:flex;flex-direction:column;gap:var(--base);padding:calc(var(--base) * 1.5) var(--gutter-h) var(--spacing-view-bottom) calc(var(--base) * 2)}.document-fields__label{color:var(--theme-elevation-400)}.document-fields--force-sidebar-wrap{display:block}.document-fields--force-sidebar-wrap .document-fields__main{width:100%;min-height:auto}.document-fields--force-sidebar-wrap .document-fields__sidebar-wrap{position:static;width:100%;height:auto;border-left:0}.document-fields--force-sidebar-wrap .document-fields__sidebar{padding-bottom:70px;overflow:visible}.document-fields--force-sidebar-wrap .document-fields__sidebar-fields{padding-top:0;padding-left:var(--gutter-h);padding-bottom:0}@media (max-width:1024px){.document-fields{display:block}.document-fields--has-sidebar .document-fields__main{width:100%}[dir=ltr] .document-fields--has-sidebar .document-fields__edit{border-right:0;padding-right:var(--gutter-h)}[dir=rtl] .document-fields--has-sidebar .document-fields__edit{border-left:0;padding-left:var(--gutter-h)}.document-fields--has-sidebar .document-fields__fields>.group-field,.document-fields--has-sidebar .document-fields__fields>.tabs-field{margin-right:calc(var(--gutter-h) * -1)}.document-fields__main{width:100%;min-height:auto}.document-fields__sidebar-wrap{position:static;width:100%;height:auto;border-left:0}.document-fields__form{display:block}.document-fields__sidebar-fields{padding-top:0;padding-left:var(--gutter-h);padding-right:var(--gutter-h);padding-bottom:0;gap:10px}[dir=ltr] .document-fields__sidebar-fields{padding-right:var(--gutter-h)}[dir=rtl] .document-fields__sidebar-fields{padding-left:var(--gutter-h)}.document-fields__sidebar{padding-bottom:70px;overflow:visible}}@media (max-width:768px){.document-fields__sidebar-wrap{min-width:auto;width:100%}.document-fields__edit{padding-top:calc(var(--base) / 2)}}}@layer payload-default{.icon--folder{height:var(--base);width:var(--base)}form>.form-submit .btn{width:100%}}@layer payload-default{}.drawer-action-header{padding-top:calc(var(--base) * 2);padding-bottom:calc(var(--base) * 1);border-bottom:1px solid var(--theme-elevation-100)}.drawer-action-header__content{margin-left:var(--gutter-h);margin-right:var(--gutter-h);display:flex;justify-content:space-between;align-items:center}.drawer-action-header__title{margin:0}.drawer-action-header__actions{display:flex;margin-left:auto;padding-left:var(--base);gap:var(--base)}.drawer-content-container{padding:calc(var(--base) * 2) var(--gutter-h);display:flex;flex-direction:column;overflow:auto}.no-results{padding:calc(var(--base) * 2) var(--base);display:flex;flex-direction:column;align-items:center;justify-content:center;border:1px dashed var(--theme-border-color);border-radius:var(--style-radius-m);text-align:center}.no-results__actions{display:flex;gap:calc(var(--base) / 2);margin-top:var(--base)}.no-results__actions .btn{margin:0}@layer payload-default{.folderBreadcrumbs__crumb,.folderBreadcrumbs__crumb-item.droppable-button{font-family:var(--font-body);font-weight:500;margin:0;font-size:16px;line-height:20px;letter-spacing:-.375px}.droppable-button{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body);font-weight:600;font-family:inherit}.droppable-button:hover{cursor:pointer}.folderBreadcrumbs{display:flex}.folderBreadcrumbs__crumb,.folderBreadcrumbs__crumb-item.droppable-button{font-weight:600;letter-spacing:unset;display:flex;align-items:center;margin:0}}@layer payload-default;@layer payload-default{.folderBreadcrumbs__crumb-item.droppable-button:has(.icon--folder),.folderBreadcrumbs__crumb:has(.icon--folder){height:calc(var(--base) * 1.6)}.folderBreadcrumbs__crumb-item.droppable-button:has(.icon--folder) .btn__label,.folderBreadcrumbs__crumb:has(.icon--folder) .btn__label{display:flex;align-items:center;height:100%}.folderBreadcrumbs__crumb-item.droppable-button--hover{opacity:.5}.folderBreadcrumbs__crumb-chevron{position:relative;top:1px}.folderBreadcrumbs__crumb-chevron .stroke{stroke:var(--theme-elevation-250)}@media (max-width:1024px){.folderBreadcrumbs__crumb,.folderBreadcrumbs__crumb-item{font-size:var(--base)}}}@layer payload-default{.colored-folder-icon{color:var(--theme-elevation-300)}.move-folder-drawer__body-section{display:grid;grid-template-rows:auto 1fr;grid-gap:var(--base);gap:var(--base)}.move-folder-drawer__breadcrumbs-section{padding:calc(var(--base) * .75) var(--gutter-h);border-bottom:1px solid var(--theme-elevation-100);display:flex;justify-content:space-between}.move-folder-drawer__breadcrumbs-section .move-folder-drawer__add-folder-button{margin-left:var(--base)}.move-folder-drawer__folder-breadcrumbs-root{display:flex;align-items:center;gap:calc(var(--base) / 2)}.move-doc-to-folder{margin:0}.move-doc-to-folder .btn__icon{color:var(--theme-elevation-300)}.move-doc-to-folder .btn__label{font-weight:600}.edit-upload{--edit-upload-cell-spacing:calc(var(--base) * 1.5);--edit-upload-sidebar-width:calc(350px + var(--gutter-h));height:100%;margin-right:calc(var(--gutter-h) * -1);margin-left:calc(var(--gutter-h) * -1)}.edit-upload__header{height:100px;border-bottom:1px solid var(--theme-elevation-150);padding:0 var(--gutter-h);display:flex;justify-content:space-between;align-items:center}.edit-upload__header h2{margin:0;text-wrap:nowrap;overflow:hidden;text-overflow:ellipsis}[dir=rtl] .edit-upload__actions{margin-right:auto;margin-left:unset}.edit-upload__actions{min-width:350px;margin-left:auto;padding:10px 0 10px 30px;justify-content:flex-end;display:flex;gap:20px;text-wrap:nowrap}.edit-upload__toolWrap{display:flex;justify-content:flex-end;height:calc(100% - 100px)}.edit-upload .ReactCrop__selection-addon,.edit-upload__crop-window{height:100%;width:100%}.edit-upload__focal-wrapper{position:relative;display:inline-flex;max-height:100%}.edit-upload__draggable-container{position:absolute;inset:0;pointer-events:none}.edit-upload__draggable-container--dragging{pointer-events:all}.edit-upload__draggable-container--dragging .edit-upload__focalPoint{cursor:-webkit-grabbing;cursor:grabbing}.edit-upload__draggable{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body);position:absolute}.edit-upload__focalPoint{position:absolute;top:50%;left:50%;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:-webkit-grab;cursor:grab;width:50px;height:50px;transform:translate3d(-50%,-50%,0);pointer-events:all}.edit-upload__focalPoint svg{position:absolute;inset:0;background:#00000080;border-radius:100%;width:40px;height:40px;color:#fff}.edit-upload__crop,.edit-upload__focalOnly{padding:30px 30px 30px 0;width:100%;display:flex;justify-content:center}.edit-upload__crop{padding:var(--edit-upload-cell-spacing);padding-left:var(--gutter-h);display:flex;align-items:flex-start;height:100%}.edit-upload__imageWrap{position:relative}.edit-upload__point{cursor:move;position:absolute;background:#00000080;border-radius:100%}.edit-upload__point svg{width:40px;height:40px}.edit-upload__sidebar{border-left:1px solid var(--theme-elevation-150);padding-top:var(--edit-upload-cell-spacing);min-width:var(--edit-upload-sidebar-width)}.edit-upload__sidebar>div:first-child{margin-bottom:20px}.edit-upload__groupWrap{display:flex;flex-direction:column;gap:10px;padding-right:var(--gutter-h);padding-left:var(--edit-upload-cell-spacing);width:100%}.edit-upload__groupWrap+.edit-upload__groupWrap{padding-top:var(--edit-upload-cell-spacing);margin-top:var(--edit-upload-cell-spacing);border-top:1px solid var(--theme-elevation-150)}.edit-upload__inputsWrap,.edit-upload__titleWrap{display:flex;gap:20px}.edit-upload__titleWrap{justify-content:space-between;align-items:center}.edit-upload__titleWrap h3{margin:0}.edit-upload__reset{height:-moz-fit-content;height:fit-content;border-radius:var(--style-radius-s);background-color:var(--theme-elevation-150);padding:0 8px}.edit-upload__input{flex:1 1}.edit-upload__input input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.edit-upload__input input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.edit-upload__input input[data-rtl=true]{direction:rtl}}@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{.edit-upload__input input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.edit-upload__input input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.edit-upload__input input:hover{border-color:var(--theme-elevation-250)}.edit-upload__input input:active,.edit-upload__input input:focus,.edit-upload__input input:focus-within{border-color:var(--theme-elevation-400);outline:0}.edit-upload__input input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.edit-upload__input input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}@media (max-width:1024px){.edit-upload{--edit-upload-cell-spacing:var(--gutter-h)}.edit-upload__sidebar{padding-left:0;border-left:0;width:100%}.edit-upload__toolWrap{flex-direction:column-reverse}}@media (max-width:768px){.edit-upload{flex-direction:column}.edit-upload__focalPoint{border-right:none;padding:20px 0}.edit-upload__inputsWrap{flex-direction:column;gap:20px}.edit-upload__sidebar{min-width:0}}}@layer payload-default;@layer payload-default;@layer payload-default{.file-field{position:relative;margin-bottom:var(--base);background:var(--theme-elevation-50);border-radius:var(--style-radius-s)}.file-field__upload{display:flex}.file-field .tooltip.error-message{z-index:3;bottom:calc(100% - calc(var(--base) * .5))}.file-field__file-selected{display:flex}.file-field__thumbnail-wrap{position:relative;width:150px}.file-field__thumbnail-wrap .thumbnail{position:relative;width:100%;height:100%;object-fit:contain;border-radius:var(--style-radius-s) 0 0 var(--style-radius-s)}.file-field__remove{margin:calc(1.5384615385rem * 1.5) 1.5384615385rem 1.5384615385rem 0;place-self:flex-start}.file-field__file-adjustments,.file-field__remote-file-wrap{padding:1.5384615385rem;width:100%;display:flex;flex-direction:column;gap:calc(var(--base) / 2)}.file-field__filename,.file-field__remote-file{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1);background-color:var(--theme-bg)}.file-field__filename:not(:disabled):hover,.file-field__remote-file:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.file-field__filename[data-rtl=true],.file-field__remote-file[data-rtl=true]{direction:rtl}.file-field__filename::-webkit-input-placeholder,.file-field__remote-file::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.file-field__filename::-moz-placeholder,.file-field__remote-file::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.file-field__filename:hover,.file-field__remote-file:hover{border-color:var(--theme-elevation-250)}.file-field__filename:active,.file-field__filename:focus,.file-field__filename:focus-within,.file-field__remote-file:active,.file-field__remote-file:focus,.file-field__remote-file:focus-within{border-color:var(--theme-elevation-400);outline:0}.file-field__filename:disabled,.file-field__remote-file:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.file-field__filename:disabled:hover,.file-field__remote-file:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.file-field__add-file-wrap,.file-field__upload-actions{display:flex;gap:calc(var(--base) / 2);flex-wrap:wrap}.file-field__upload-actions{margin-top:calc(var(--base) * .5)}.file-field__previewDrawer h2{margin:0 var(--base) 0 0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - var(--base) * 2)}.file-field .dropzone{background-color:transparent;padding-block:calc(var(--base) * 2.25)}.file-field__dropzoneContent{display:flex;flex-wrap:wrap;gap:calc(var(--base) * .4);justify-content:space-between;width:100%}.file-field__dropzoneButtons{display:flex;gap:calc(var(--base) * .5);align-items:center}.file-field__dragAndDropText,.file-field__orText{color:var(--theme-elevation-500);text-transform:lowercase}.file-field__dragAndDropText{flex-shrink:0;margin:0;align-self:center}@media (max-width:768px){.file-field__upload{flex-wrap:wrap;justify-content:space-between}.file-field__remove{margin:1.5384615385rem;order:2}.file-field__file-adjustments{order:3;border-top:2px solid var(--theme-elevation-0);padding:calc(1.5384615385rem * .5);gap:0}.file-field__thumbnail-wrap{order:1;width:50%}.file-field__thumbnail-wrap .thumbnail{width:100%}.file-field__edit{display:none}}}@layer payload-default;@layer payload-default;@layer payload-default{.file-details-draggable{background:var(--theme-elevation-50);border-radius:3px;padding:.7rem .8rem}.file-details-draggable,.file-details-draggable--drag-wrapper{display:flex;gap:.6rem;align-items:center}.file-details-draggable__thumbnail{max-width:1.5rem}.file-details-draggable__actions{flex-grow:2;display:flex;gap:.6rem;align-items:center;justify-content:flex-end}.file-details-draggable__remove.btn--style-icon-label{margin:0}.copy-to-clipboard{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body);position:relative;cursor:pointer;vertical-align:middle;border-radius:100%}.copy-to-clipboard textarea{position:absolute;opacity:0;z-index:-1;height:0;width:0}.copy-to-clipboard:active,.copy-to-clipboard:focus{outline:none}}@layer payload-default;@layer payload-default{}@layer payload-default{.copy-to-clipboard:focus-visible{outline:var(--accessibility-outline)}}@layer payload-default;@layer payload-default;@layer payload-default{.file-meta__url{display:flex;gap:8px}.file-meta__url a{font-weight:600;text-decoration:none}.file-meta__url a:focus-visible,.file-meta__url a:hover{text-decoration:underline}.file-meta__size-type,.file-meta__url a{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.file-meta__edit{position:relative}.file-details{background:var(--theme-elevation-50);border:1px solid var(--theme-border-color);border-radius:var(--style-radius-m);box-shadow:0 2px 2px -1px #0000001a}.file-details:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.file-details header{display:flex;flex-direction:row;flex-wrap:wrap;position:relative}.file-details__remove{position:absolute;margin:0;top:1.5384615385rem;right:1.5384615385rem}.file-details__remove .btn__icon{border:1px solid var(--theme-border-color);background:var(--theme-input-bg);box-shadow:0 2px 2px -1px #0000001a;transition:border .1s cubic-bezier(0,.2,.2,1)}.file-details__remove .btn__icon:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.file-details__remove .btn__icon:hover{border:1px solid var(--theme-elevation-400)}.file-details__main-detail{padding:var(--base) calc(var(--base) * 1.2);width:auto;flex-grow:1;min-width:280px;max-width:100%;display:flex;flex-direction:column;justify-content:space-between;align-self:stretch;gap:calc(var(--base) * .2)}.file-details__toggle-more-info{font-weight:600;text-decoration:none}}@layer payload-default;@layer payload-default;@layer payload-default{.file-details__toggle-more-info:focus-visible,.file-details__toggle-more-info:hover{text-decoration:underline}.file-details__toggle-icon{padding:calc(var(--base) / 4)}.file-details__sizes{margin:0;padding:calc(var(--base) * 1.5) 1.5384615385rem 0;list-style:none;display:flex;flex-wrap:wrap}.file-details__sizes li{width:50%;padding:0 calc(var(--base) * .5);margin-bottom:1.5384615385rem}.file-details__size-label{color:var(--theme-elevation-400)}.file-details__file-mutation{display:flex;margin-top:calc(var(--base) * .25);gap:calc(var(--base) / 2)}.file-details__edit{cursor:pointer;background-color:var(--theme-elevation-150);border:none;border-radius:4px;padding:calc(var(--base) * .25) calc(var(--base) * .5)}.file-details__edit:hover{background-color:var(--theme-elevation-100)}@media (max-width:1440px){.file-details__main-detail{padding:1.5384615385rem}.file-details__sizes{display:block;padding:1.5384615385rem 1.5384615385rem calc(var(--base) * .5)}.file-details__sizes li{padding:0;width:100%}}@media (max-width:1024px){.file-details header{flex-wrap:wrap}.file-details .thumbnail{width:50%;order:1}.file-details__remove{order:2}.file-details__main-detail{order:3;width:100%}}}@layer payload-default;@layer payload-default;@layer payload-default{.preview-sizes{margin:40px calc(var(--gutter-h) * -1) 0 calc(var(--gutter-h) * -1);border-top:1px solid var(--theme-elevation-150);max-height:calc(100vh - 120px);height:100%;display:flex;flex-direction:row}.preview-sizes__imageWrap{min-width:60%;border-right:1px solid var(--theme-elevation-150)}.preview-sizes__preview{max-height:calc(100% - 120px);padding:30px 30px 30px var(--gutter-h);object-fit:contain}.preview-sizes__meta{border-bottom:1px solid var(--theme-elevation-150);padding:20px var(--gutter-h);display:flex;flex-wrap:wrap;column-gap:20px}.preview-sizes__meta .file-meta{display:flex;flex-wrap:wrap;column-gap:20px;text-wrap:wrap;width:100%}.preview-sizes__meta .file-meta__url{width:100%}.preview-sizes .file-meta__size-type,.preview-sizes__sizeName{color:var(--theme-elevation-600)}.preview-sizes__listWrap{padding-right:var(--gutter-h);overflow-y:scroll}.preview-sizes__listWrap::-webkit-scrollbar{width:0}.preview-sizes__listWrap:after{content:"";display:block;position:-webkit-sticky;position:sticky;bottom:0;left:0;height:80px;width:100%;background:linear-gradient(180deg,transparent 0,var(--theme-bg) 100%);pointer-events:none}.preview-sizes__list{list-style:none;display:flex;flex-direction:column;gap:10px;margin:0;padding:30px 0 30px 30px}.preview-sizes__sizeOption{padding:10px;display:flex;gap:20px;cursor:pointer;transition:background-color .2s ease-in-out}.preview-sizes--selected,.preview-sizes__sizeOption:hover{background-color:var(--theme-elevation-100)}.preview-sizes__image{display:flex;width:30%;min-width:30%;align-items:center;justify-content:center}.preview-sizes__sizeMeta{padding:10px 0}.preview-sizes__sizeMeta,.preview-sizes__sizeName{overflow:hidden;text-overflow:ellipsis}@media (max-width:1024px){.preview-sizes{margin-top:20px;max-height:calc(100vh - 80px)}}@media (max-width:768px){.preview-sizes{margin-top:0;max-height:calc(100vh - 70px);flex-direction:column;justify-content:space-between}.preview-sizes__imageWrap{height:60%;border:none}.preview-sizes__list,.preview-sizes__preview{padding:calc(var(--gutter-h) * 2) var(--gutter-h)}.preview-sizes__preview{max-height:calc(100% - 80px)}.preview-sizes__sizeOption{padding:5px}.preview-sizes__listWrap{border-top:1px solid var(--theme-elevation-150);height:40%}}}@layer payload-default;@layer payload-default;@layer payload-default{.collection-edit{width:100%}.collection-edit__form{height:auto}.file-selections{--file-gutter-h:calc(var(--gutter-h) / 4);border-right:1px solid var(--theme-border-color);padding:0;display:flex;flex-direction:column;width:300px;overflow:auto;max-height:100%}.file-selections__header{position:-webkit-sticky;position:sticky;top:0;margin-top:var(--base);z-index:1;display:flex;align-items:center;justify-content:space-between;width:100%;background:var(--theme-bg);flex-wrap:wrap}.file-selections__header p{margin:0}.file-selections__headerTopRow{display:flex;align-items:center;justify-content:space-between;gap:var(--base);width:100%;padding-block:var(--base);padding-inline:var(--file-gutter-h)}.file-selections__header__text{display:flex;flex-direction:column}.file-selections__header__text .error-pill{align-self:flex-start}.file-selections__filesContainer{display:flex;flex-direction:column;gap:calc(var(--base) / 4);margin-top:calc(var(--base) / 2);width:100%;padding-inline:var(--file-gutter-h)}.file-selections__filesContainer .shimmer-effect{border-radius:var(--style-radius-m)}.file-selections__fileRowContainer{--rowPadding:calc(var(--base) / 4);position:relative}.file-selections__fileRowContainer:last-child{margin-bottom:calc(var(--base) / 4)}.file-selections__fileRow{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body);display:flex;padding:var(--rowPadding);align-items:center;gap:calc(var(--base) / 2);border-radius:var(--style-radius-m);max-width:100%;cursor:pointer;width:100%}.file-selections__fileRow:hover{background-color:var(--theme-elevation-100)}.file-selections__fileRow .file-selections__thumbnail,.file-selections__fileRow .file-selections__thumbnail-shimmer{width:calc(var(--base) * 1.2);height:calc(var(--base) * 1.2);border-radius:var(--style-radius-s);flex-shrink:0;object-fit:cover}.file-selections__fileRow p{margin:0}.file-selections__fileDetails{display:flex;flex-direction:column;min-width:0}.file-selections__fileRowContainer--active .file-selections__fileRow{background-color:var(--theme-elevation-100)}.file-selections__fileRowContainer--active .file-selections__remove .icon--x{opacity:1}.file-selections__fileRowContainer--error .file-selections__fileRow{background-color:var(--theme-error-100)}.file-selections__fileRowContainer--error .file-selections__fileRow:hover,.file-selections__fileRowContainer--error.file-selections__fileRowContainer--active .file-selections__fileRow{background-color:var(--theme-error-200)}.file-selections__fileRowContainer--error .file-selections__remove--overlay:hover{background-color:var(--theme-error-50)}.file-selections__fileRowContainer--error .file-selections__remove--overlay:hover .icon--x{opacity:1}.file-selections__errorCount{margin-left:auto;position:absolute;transform:translate(50%,-50%);top:0;right:0}.file-selections__fileName{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.file-selections__fileSize{font-size:calc(var(--base) / 2);color:var(--theme-elvation-400);flex-shrink:0}.file-selections__remove{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body);margin:0 0 0 auto}.file-selections__remove .icon--x{opacity:.75}.file-selections__remove--underlay{pointer-events:none;opacity:0}.file-selections__remove--overlay{position:absolute;transform:translateY(-50%);top:50%;bottom:50%;right:var(--rowPadding);height:20px;border-radius:var(--style-radius-m);cursor:pointer}.file-selections__remove--overlay:hover{background-color:var(--theme-elevation-200)}.file-selections__header__actions{display:flex;align-items:center;gap:var(--base)}.file-selections__header__addFile{height:-moz-fit-content;height:fit-content}.file-selections__toggler{display:none;margin:0;padding-block:0}.file-selections__header__mobileDocActions,.file-selections__toggler__text{display:none}.file-selections__animateWrapper{overflow:auto}.file-selections__mobileBlur{position:fixed;top:0;left:0;width:100%;height:100%;opacity:0;transition:opacity .1s cubic-bezier(0,.2,.2,1)}.file-selections__mobileBlur:after,.file-selections__mobileBlur:before{content:" ";position:absolute;inset:0}.file-selections__mobileBlur:before{background:var(--theme-bg);opacity:.75}.file-selections__mobileBlur:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.file-selections__showingFiles .file-selections__mobileBlur{opacity:1}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.file-selections{--file-gutter-h:var(--gutter-h);flex-direction:column-reverse;width:100%;position:-webkit-sticky;position:sticky;bottom:0;flex-shrink:0}.file-selections__showingFiles{z-index:2}.file-selections__filesContainer:after,.file-selections__filesContainer:before{content:" ";position:absolute;inset:0}.file-selections__filesContainer:before{background:var(--theme-bg);opacity:.75}.file-selections__filesContainer:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.file-selections__fileRowContainer{z-index:1}.file-selections__header{margin-top:0}.file-selections__headerTopRow{border-top:1px solid var(--theme-border-color);padding-block:0 calc(var(--base) * .8) 0}.file-selections__header__text{display:none}.file-selections__header__actions{flex-grow:2;display:flex;justify-content:flex-end}.file-selections__header__mobileDocActions{position:relative;display:flex;width:100%;padding-block:calc(var(--base) * .8);padding-inline:var(--file-gutter-h);border-top:1px solid var(--theme-border-color)}.file-selections__header__mobileDocActions>div{display:flex;justify-content:flex-end;width:100%}.file-selections__header__mobileDocActions>div button{flex:.5 1}.file-selections__toggler{padding-right:0;padding-left:0;padding-top:calc(var(--base) * .8);padding-bottom:calc(var(--base) * .8);display:flex;justify-content:flex-end;flex-grow:1}.file-selections__toggler .btn__label{width:100%;display:flex;align-items:center;justify-content:space-between}.file-selections__toggler__text{display:flex}.file-selections__toggler svg{max-width:1.5rem}.file-selections__toggler .btn__content{width:100%}.file-selections .btn{margin:0}}}@layer payload-default;@layer payload-default;@layer payload-default{.bulk-upload--file-manager{display:flex;height:100%;width:100%;overflow:hidden}.bulk-upload--file-manager__editView{flex-grow:1;height:100%;max-height:100%;overflow:auto}@media (max-width:1024px){.bulk-upload--file-manager{flex-direction:column-reverse}.bulk-upload--file-manager__editView{flex-grow:1}}}@layer payload-default{.upload-relationship-details{display:flex;justify-content:space-between;align-items:center;width:100%;min-width:0}.upload-relationship-details__imageAndDetails{display:flex;gap:calc(var(--base) / 2);align-items:center;min-width:0}.upload-relationship-details__thumbnail{align-self:center;border-radius:var(--style-radius-s)}.upload-relationship-details__details{display:flex;flex-direction:column;gap:0;overflow:hidden;margin-right:calc(var(--base) * 2)}.upload-relationship-details__filename{margin:0;text-wrap:nowrap;text-overflow:ellipsis;overflow:hidden}.upload-relationship-details__filename a{text-decoration:none}.upload-relationship-details__meta{margin:0;color:var(--theme-elevation-500);text-wrap:nowrap;text-overflow:ellipsis;overflow:hidden}.upload-relationship-details__actions{flex-shrink:0;display:flex}.upload-relationship-details .btn{margin:0}.upload-field-card{background:var(--theme-elevation-50);border:1px solid var(--theme-border-color);border-radius:var(--style-radius-s);display:flex;align-items:center;width:100%;gap:calc(var(--base) / 2)}.upload-field-card--size-medium{padding:calc(var(--base) * .5)}.upload-field-card--size-medium .thumbnail{width:40px;height:40px}.upload-field-card--size-small{padding:calc(var(--base) / 3) calc(var(--base) / 2)}.upload-field-card--size-small .thumbnail{width:25px;height:25px}.upload--has-many{position:relative;max-width:100%}.upload--has-many__drag[aria-disabled=true]:hover{cursor:default}.upload--has-many__draggable-rows{display:flex;flex-direction:column;gap:calc(var(--base) / 4)}.upload--has-many__dragItem .icon--drag-handle{color:var(--theme-elevation-400)}.upload--has-many__dragItem .thumbnail{width:26px;height:26px}.upload--has-many__dragItem .uploadDocRelationshipContent__details{line-height:1.2}.upload{position:relative;max-width:100%}.upload__dropzoneAndUpload{display:flex;flex-direction:column;gap:calc(var(--base) / 4)}.upload__dropzoneContent{display:flex;flex-wrap:wrap;gap:8px;justify-content:space-between;width:100%}.upload__dropzoneContent__buttons{display:flex;gap:calc(var(--base) / 2);position:relative;left:-2px}.upload__dropzoneContent__buttons .btn .btn__content{gap:calc(var(--base) / 5)}.upload__dragAndDropText,.upload__dropzoneContent__orText{color:var(--theme-elevation-500);text-transform:lowercase}.upload__dragAndDropText{flex-shrink:0;margin:0;align-self:center}.upload__loadingRows{display:flex;flex-direction:column;gap:calc(var(--base) / 4)}.upload .shimmer-effect{border-radius:var(--style-radius-s);border:1px solid var(--theme-border-color)}}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:768px){.upload__dragAndDropText{display:none}}}@layer payload-default;@layer payload-default{.array-field__header-action{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body)}.array-field,.array-field__header{display:flex;flex-direction:column;gap:calc(var(--base) / 2)}.array-field__header__header-content{display:flex;flex-direction:column;gap:calc(var(--base) / 4)}.array-field--has-no-error>.array-field__header .array-field__header-content{color:var(--theme-text)}.array-field__header-content{display:flex;align-items:center;gap:10px}.array-field__header-wrap{display:flex;align-items:flex-end;width:100%;justify-content:space-between}.array-field__header-actions{list-style:none;margin:0;padding:0;display:flex;color:var(--theme-elevation-800)}.array-field__header-action{cursor:pointer;margin-left:10px}}@layer payload-default{.array-field__header-action:focus-visible,.array-field__header-action:hover{text-decoration:underline;color:var(--theme-elevation-600)}.array-field__row-header{display:flex;align-items:center;gap:10px;pointer-events:none}.array-field__draggable-rows{display:flex;flex-direction:column;gap:calc(var(--base) / 2)}.array-field__title{margin-bottom:0}.array-field__add-row{align-self:flex-start;color:var(--theme-elevation-400);margin:2px 0}.array-field__add-row:hover{color:var(--theme-elevation-800)}html[data-theme=light] .array-field--has-error>.array-field__header .array-field__header-content{color:var(--theme-error-750)}html[data-theme=dark] .array-field--has-error>.array-field__header .array-field__header-content{color:var(--theme-error-500)}.field-type.confirm-password{position:relative}.field-type.confirm-password input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.field-type.confirm-password input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.field-type.confirm-password input[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.field-type.confirm-password input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.confirm-password input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.confirm-password input:hover{border-color:var(--theme-elevation-250)}.field-type.confirm-password input:active,.field-type.confirm-password input:focus,.field-type.confirm-password input:focus-within{border-color:var(--theme-elevation-400);outline:0}.field-type.confirm-password input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.field-type.confirm-password input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}html[data-theme=light] .field-type.field-type.confirm-password.error input{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .field-type.field-type.confirm-password.error input{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .field-type.field-type.confirm-password.error input:hover{border-color:var(--theme-error-500)}.field-type.password{position:relative}.field-type.password input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.field-type.password input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.field-type.password input[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.field-type.password input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.password input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.password input:hover{border-color:var(--theme-elevation-250)}.field-type.password input:active,.field-type.password input:focus,.field-type.password input:focus-within{border-color:var(--theme-elevation-400);outline:0}.field-type.password input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.field-type.password input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}html[data-theme=light] .field-type.password.error input{background-color:var(--theme-error-50);border:1px solid var(--theme-error-500)}html[data-theme=dark] .field-type.password.error input{background-color:var(--theme-error-100);border:1px solid var(--theme-error-400)}html[data-theme=dark] .field-type.password.error input:hover{border-color:var(--theme-error-500)}.icon--sort{height:1.5384615385rem;width:1.5384615385rem}.icon--sort .fill{stroke:currentColor;stroke-width:1px;fill:var(--theme-elevation-800)}.sort-header{display:flex;gap:calc(var(--base) / 2);align-items:center}.sort-header__buttons{display:flex;align-items:center;gap:calc(var(--base) / 4)}.sort-header__button{margin:0;padding:0!important;opacity:.3;padding:calc(var(--base) / 4);display:inline-flex;align-items:center;justify-content:center;background:transparent;border:none;cursor:pointer}.sort-header__button.sort-header--active{opacity:1;visibility:visible}.sort-header__button:hover{opacity:.7}.sort-header:hover .btn{opacity:.4;visibility:visible}.sort-header--appearance-condensed{gap:calc(var(--base) / 4)}.sort-header--appearance-condensed .sort-header__buttons{gap:0}.sort-row{opacity:.3;cursor:not-allowed}.sort-row.active{cursor:-webkit-grab;cursor:grab;opacity:1}.sort-row__icon{height:22px;width:22px;margin-left:-2px;margin-top:-2px;display:block;width:min-content}.table{margin-bottom:1.5384615385rem;overflow:auto;max-width:100%}.table table{min-width:100%}.table thead{color:var(--theme-elevation-400)}.table thead th{font-weight:400;text-align:left;vertical-align:middle}[dir=rtl] .table thead th{text-align:right}.table td,.table th{vertical-align:top;padding:calc(var(--base) * .6);min-width:150px;position:relative;z-index:1}.table td:first-child,.table th:first-child{-webkit-padding-start:16px;padding-inline-start:16px}.table td:last-child,.table th:last-child{-webkit-padding-end:16px;padding-inline-end:16px}.table tbody tr{position:relative}.table tbody tr:nth-child(odd){background:var(--theme-elevation-50);border-radius:var(--style-radius-s)}.table tbody tr:nth-child(odd) td:first-child{border-top-left-radius:inherit;border-bottom-left-radius:inherit}.table tbody tr:nth-child(odd) td:last-child{border-top-right-radius:inherit;border-bottom-right-radius:inherit}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{.table a:focus-visible{outline:var(--accessibility-outline);outline-offset:var(--accessibility-outline-offset)}.table--appearance-condensed{border-radius:var(--style-radius-s)}.table--appearance-condensed table{border-collapse:collapse}.table--appearance-condensed thead{background:var(--theme-elevation-50)}.table--appearance-condensed thead th:first-child{border-top-left-radius:var(--style-radius-s)}.table--appearance-condensed thead th:last-child{border-top-right-radius:var(--style-radius-s)}.table--appearance-condensed tbody tr:nth-child(odd):after{display:none}.table--appearance-condensed tbody tr:last-child td:first-child{border-bottom-left-radius:var(--style-radius-s)}.table--appearance-condensed tbody tr:last-child td:last-child{border-bottom-right-radius:var(--style-radius-s)}.table--appearance-condensed td,.table--appearance-condensed th{padding:6px}.table--appearance-condensed td:first-child,.table--appearance-condensed th:first-child{-webkit-padding-start:12px;padding-inline-start:12px}.table--appearance-condensed td:last-child,.table--appearance-condensed th:last-child{-webkit-padding-end:12px;padding-inline-end:12px}.table--appearance-condensed th{padding:6px}.table--appearance-condensed th,.table--appearance-condensed tr td{border:.5px solid var(--theme-elevation-100)}.table--drag-preview{cursor:-webkit-grabbing;cursor:grabbing;z-index:var(--z-popup)}@media (max-width:1024px){.table td,.table th{max-width:70vw}}}@layer payload-default;@layer payload-default;@layer payload-default{.query-preset-columns-cell{display:flex;flex-wrap:wrap;gap:4px}.query-preset-columns-field .field-label{margin-bottom:calc(var(--base) / 2)}.query-preset-columns-field .value-wrapper{background-color:var(--theme-elevation-50);padding:var(--base);display:flex;flex-wrap:wrap;gap:calc(var(--base) / 2)}.query-preset-where-field .field-label{margin-bottom:calc(var(--base) / 2)}.query-preset-where-field .value-wrapper{background-color:var(--theme-elevation-50);padding:var(--base)}.query-preset-where-field .pill--style-always-white{background:var(--theme-elevation-250);color:var(--theme-elevation-1000)}.document-take-over{display:flex;align-items:center;justify-content:center;height:100%}.document-take-over:after,.document-take-over:before{content:" ";position:absolute;inset:0}.document-take-over:before{background:var(--theme-bg);opacity:.75}.document-take-over:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.document-take-over__wrapper{z-index:1;position:relative;padding:40px}.document-take-over__content,.document-take-over__wrapper{display:flex;flex-direction:column;gap:var(--base)}.document-take-over__content>*{margin:0}.document-take-over__controls{display:flex;gap:var(--base)}.document-take-over__controls .btn{margin:0}.document-locked{display:flex;align-items:center;justify-content:center;height:100%}.document-locked:after,.document-locked:before{content:" ";position:absolute;inset:0}.document-locked:before{background:var(--theme-bg);opacity:.75}.document-locked:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.document-locked__wrapper{z-index:1;position:relative;display:flex;flex-direction:column;gap:var(--base);padding:40px}.document-locked__content{display:flex;flex-direction:column;gap:var(--base);max-width:720px}.document-locked__content>*{margin:0}.document-locked__controls{display:flex;gap:var(--base)}.document-locked__controls .btn{margin:0}.graphic-account{vector-effect:non-scaling-stroke;overflow:visible;position:relative}.graphic-account__bg{fill:var(--theme-elevation-50);stroke:var(--theme-elevation-200);stroke-width:1px}.graphic-account__body,.graphic-account__head{fill:var(--theme-elevation-200)}.graphic-account--active .graphic-account__bg{fill:var(--theme-elevation-500);stroke:var(--theme-text)}.graphic-account--active .graphic-account__body,.graphic-account--active .graphic-account__head{fill:var(--theme-text)}.graphic-account:hover:not(.graphic-account--active) .graphic-account__bg{fill:var(--theme-elevation-200);stroke:var(--theme-elevation-600)}.graphic-account:hover:not(.graphic-account--active) .graphic-account__body,.graphic-account:hover:not(.graphic-account--active) .graphic-account__head{fill:var(--theme-elevation-600)}[data-theme=light] .graphic-account--active .graphic-account__bg{fill:var(--theme-elevation-300);stroke:var(--theme-elevation-600)}[data-theme=light] .graphic-account--active .graphic-account__body,[data-theme=light] .graphic-account--active .graphic-account__head{fill:var(--theme-elevation-600)}.icon--close-menu{height:1.5384615385rem;width:1.5384615385rem}.icon--close-menu .stroke{stroke:currentColor}.icon--menu .stroke{stroke-width:1px;stroke:currentColor}.hamburger{border:0;cursor:pointer;background-color:var(--theme-bg);outline:none;color:var(--theme-text);box-shadow:0 0 0 1px var(--theme-elevation-150);padding:2px;border-radius:3px;position:relative;z-index:1;height:100%;width:100%;transition-property:box-shadow,background-color;transition-duration:.1s;transition-timing-function:cubic-bezier(0,.2,.2,1);--hamburger-size:var(--base)}.hamburger:hover{background-color:var(--theme-elevation-100);box-shadow:0 0 0 1px var(--theme-elevation-500)}.hamburger:focus{outline:none}.hamburger:after{z-index:-1}.hamburger__close-icon,.hamburger__open-icon{width:var(--hamburger-size);height:var(--hamburger-size);display:flex;align-items:center;justify-content:center}.localizer{position:relative;flex-wrap:nowrap}.localizer,.localizer-button{display:flex;align-items:center}.localizer-button{white-space:nowrap;-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:4px;padding-inline-end:4px;background-color:var(--theme-elevation-100);border-radius:var(--style-radius-s)}.localizer-button__label{color:var(--theme-elevation-500)}.localizer-button__chevron .stroke{stroke:currentColor}.localizer-button__current{display:flex;align-items:center}.localizer-button button{color:currentColor;padding:0;font-size:1rem;line-height:20px;background:transparent;border:0;font-weight:600;cursor:pointer}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{.localizer-button button:focus-visible,.localizer-button button:hover{text-decoration:underline}.localizer-button button:active,.localizer-button button:focus{outline:none}@media (max-width:768px){.localizer-button__label{display:none}}}@layer payload-default;@layer payload-default;@layer payload-default{.nav-toggler{position:relative;background:transparent;padding:0;margin:0;border:0;cursor:pointer}.step-nav{display:flex;align-items:center;gap:calc(var(--base) / 2)}.step-nav:after{content:" ";position:-webkit-sticky;position:sticky;top:0;right:0;width:var(--base);background:linear-gradient(to right,transparent,var(--theme-bg))}.step-nav__home{width:18px;height:18px;position:relative}}@layer payload-default;@layer payload-default;@layer payload-default{.step-nav__home:focus-visible{outline:none}.step-nav__home:focus-visible:after{content:"";border:var(--accessibility-outline);position:absolute;inset:0;pointer-events:none}.step-nav *{display:block}.step-nav a{border:0;display:flex;align-items:center;font-weight:600;text-decoration:none}.step-nav a label{cursor:pointer}.step-nav a:focus-visible,.step-nav a:hover{text-decoration:underline}.step-nav span{max-width:160px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}@media (max-width:1024px){.step-nav .step-nav__home{width:16px;height:16px}}@media (max-width:768px){.step-nav{gap:8px}}}@layer payload-default;@layer payload-default;@layer payload-default{.app-header{position:relative;width:100%;height:var(--app-header-height);z-index:var(--z-modal)}.app-header__mobile-nav-toggler{display:none}.app-header__localizer.localizer{position:absolute;top:50%;right:90px;transform:translate3d(0,-50%,0)}[dir=rtl] .app-header__localizer{right:unset;left:90px}.app-header__localizer-spacing{visibility:hidden}.app-header__bg{opacity:0;position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none}.app-header--show-bg{opacity:1}.app-header__content{padding:0 var(--gutter-h);position:relative}.app-header__content,.app-header__wrapper{display:flex;align-items:center;height:100%;flex-grow:1}.app-header__wrapper{gap:calc(var(--base) / 2);justify-content:space-between;width:100%}.app-header__account{position:relative}.app-header__account:focus:not(:focus-visible){opacity:1}.app-header__account:focus-visible{outline:none}.app-header__account:focus-visible:after{content:"";border:var(--accessibility-outline);position:absolute;inset:0;pointer-events:none}.app-header__controls-wrapper{display:flex;align-items:center;flex-grow:1;width:100%}.app-header__step-nav-wrapper{flex-grow:0;overflow:auto;display:flex;width:100%}.app-header__step-nav-wrapper::-webkit-scrollbar{display:none}.app-header__actions-wrapper{position:relative;display:flex;align-items:center;gap:calc(var(--base) / 2);margin-right:var(--base)}.app-header__gradient-placeholder{position:absolute;top:0;right:0;width:var(--base);height:var(--base);background:linear-gradient(to right,transparent,var(--theme-bg))}.app-header__actions{display:flex;align-items:center;gap:calc(var(--base) / 2);flex-shrink:0;max-width:600px;white-space:nowrap}.app-header__actions::-webkit-scrollbar{display:none}.app-header__last-action{margin-right:var(--base)}@media (max-width:1440px){.app-header__actions{max-width:500px}}@media (max-width:1024px){.app-header__gradient-placeholder{right:var(--base)}.app-header__actions{max-width:300px;margin-right:var(--base)}}@media (max-width:768px){.app-header__localizer.localizer{right:40px}.app-header--nav-open .app-header__localizer{display:none}.app-header__mobile-nav-toggler{display:flex;align-items:center}.app-header__mobile-nav-toggler.nav-toggler--is-open{opacity:.5}.app-header__step-header{display:none}.app-header__gradient-placeholder{right:0}.app-header__actions{max-width:150px;margin-right:0}}}@layer payload-default{.card__title{font-family:var(--font-body);font-weight:500;margin:0;font-size:13px;line-height:16px}.card{background:var(--theme-elevation-50);padding:16px;width:100%;min-height:80px;position:relative;border-radius:var(--style-radius-m);border:1px solid var(--theme-border-color);transition-property:border,box-shadow,background;transition-duration:.1s;transition-timing-function:cubic-bezier(0,.2,.2,1);display:flex;justify-content:space-between;align-self:start;gap:16px}.card__title{letter-spacing:0;font-weight:600;line-height:16px;width:100%;margin:2px 0}.card__actions{position:relative;z-index:2;display:inline-flex}.card__actions .btn{margin:0;flex-shrink:0}.card__actions .btn__icon{border:1px solid var(--theme-border-color);transition-property:border,box-shadow,color,background;transition-duration:.1s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.card__actions .btn__icon:hover{border:1px solid var(--theme-elevation-500);background-color:var(--theme-elevation-0);color:currentColor;box-shadow:0 2px 2px -1px #0000001a}.card--has-onclick{cursor:pointer}.card--has-onclick:hover{background:var(--theme-elevation-50);border:1px solid var(--theme-elevation-250);box-shadow:0 4px 8px -2px #0000000d}.card__click{z-index:1;top:0;left:0;width:100%;height:100%;position:absolute;margin:0}}@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;.copy-locale-data__sub-header{padding:0 var(--gutter-h);display:flex;justify-content:space-between;align-items:center;border-bottom:1px solid var(--theme-border-color)}.copy-locale-data__content{padding:calc(var(--base) * 1.5) var(--gutter-h);display:flex;flex-direction:column;gap:var(--base)}.copy-locale-data__content>*{display:flex;flex-direction:column;gap:calc(var(--base) * .25)}@layer payload-default;@layer payload-default;@layer payload-default{.list-selection{display:flex;margin-left:auto;color:var(--theme-elevation-500);gap:.5em;align-items:center}.list-selection__actions{display:flex;gap:calc(var(--base) * .3)}.list-selection__button{color:var(--theme-elevation-800);background:unset;border:none;text-decoration:underline;cursor:pointer;padding:0;font-size:inherit;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}@media (max-width:768px){.list-selection{margin-bottom:10px}}}@layer payload-default;@layer payload-default;@layer payload-default{.autosave{white-space:nowrap}.delete-document__toggle{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body)}.delete-document{display:flex;align-items:center;justify-content:center;height:100%}.delete-document:after,.delete-document:before{content:" ";position:absolute;inset:0}.delete-document:before{background:var(--theme-bg);opacity:.75}.delete-document:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.icon--eye{height:1.5384615385rem;width:1.5384615385rem;shape-rendering:auto;vector-effect:non-scaling-stroke}.icon--eye .stroke{fill:none;stroke:currentColor;stroke-width:.75}.icon--eye .fill{fill:currentColor}.live-preview-toggler{background:none;border:1px solid;border-color:var(--theme-elevation-100);border-radius:var(--style-radius-s);line-height:var(--btn-line-height);font-size:var(--base-body-size);padding:calc(var(--base) * .2) calc(var(--base) * .4);cursor:pointer;transition-property:border,color,background;transition-duration:.1s;transition-timing-function:cubic-bezier(0,.2,.2,1);height:calc(var(--base) * 1.6);width:calc(var(--base) * 1.6);position:relative}.live-preview-toggler .icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.live-preview-toggler .icon .stroke{transition-property:stroke;transition-duration:.1s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.live-preview-toggler:hover{border-color:var(--theme-elevation-300);background-color:var(--theme-elevation-100)}.live-preview-toggler--active{background-color:var(--theme-elevation-100);border-color:var(--theme-elevation-200)}.live-preview-toggler--active:hover{background-color:var(--theme-elevation-200)}.icon--lock .stroke{stroke:currentColor;stroke-width:1px}.locked{position:relative;display:inline-flex;align-items:center;justify-content:center;pointer-events:all}.locked__tooltip{left:0;transform:translate3d(0,calc(var(--caret-size) * -1),0)}.icon--externalLink{height:1.5384615385rem;width:1.5384615385rem;shape-rendering:auto}.icon--externalLink .stroke{fill:none;stroke:currentColor}.preview-btn{background:none;border:1px solid;border-color:var(--theme-elevation-100);border-radius:var(--style-radius-s);line-height:var(--btn-line-height);font-size:var(--base-body-size);padding:calc(var(--base) * .2) calc(var(--base) * .4);cursor:pointer;transition-property:border,color,background;transition-duration:.1s;transition-timing-function:cubic-bezier(0,.2,.2,1);height:calc(var(--base) * 1.6);width:calc(var(--base) * 1.6);position:relative}.preview-btn .icon{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.preview-btn .icon .stroke{transition-property:stroke;transition-duration:.1s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.preview-btn:hover{border-color:var(--theme-elevation-300);background-color:var(--theme-elevation-100)}.schedule-publish__drawer-header{display:flex;justify-content:space-between;align-items:center;padding:calc(var(--base) * 2.5) var(--gutter-h);height:48px;border-bottom:1px solid var(--theme-border-color)}.schedule-publish__drawer-header h2{margin:0}.schedule-publish__scheduler{padding-top:calc(var(--base) * 2);padding-bottom:calc(var(--base) * 2);border-bottom:1px solid var(--theme-border-color)}.schedule-publish__type{list-style:none;margin:0;padding:0;display:flex}.schedule-publish__type li{margin-right:calc(var(--base) * 2)}.schedule-publish__actions button{margin-right:var(--base)}.schedule-publish__upcoming{padding-top:calc(var(--base) * 2);padding-bottom:calc(var(--base) * 2)}.schedule-publish__upcoming h4{margin-bottom:var(--base)}.schedule-publish__delete{margin:0}.status__label{color:var(--theme-elevation-500)}.status__value{font-weight:600}.status__value-wrap{white-space:nowrap}.status__action{text-decoration:underline}.doc-controls{position:-webkit-sticky;position:sticky;top:0;width:100%;z-index:5;display:flex;align-items:center}.doc-controls:after,.doc-controls:before{content:" ";position:absolute;inset:0}.doc-controls:before{background:var(--theme-bg);opacity:.3}.doc-controls:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.doc-controls__divider{content:"";display:block;position:absolute;height:1px;background:var(--theme-elevation-100);width:100%;left:0;top:100%}.doc-controls__wrapper{position:relative;width:100%;display:flex;align-items:space-between;gap:var(--base);padding-bottom:1px;z-index:4;height:var(--doc-controls-height)}.doc-controls__content{display:flex;align-items:center;flex-grow:1;overflow:hidden;padding:16px 0;gap:calc(var(--base) * .5)}.doc-controls__meta-icons{display:flex;align-items:center;gap:calc(var(--base) * .2);flex-shrink:0}.doc-controls__meta{flex-grow:1;display:flex;list-style:none;padding:0;gap:var(--base);margin:0;width:100%}.doc-controls__meta button{margin:0}.doc-controls__locked-controls.locked{position:unset}.doc-controls__locked-controls.locked .tooltip{top:calc(var(--base) * -.5)}.doc-controls__list-item{display:flex;align-items:center;margin:0}.doc-controls__value-wrap{overflow:hidden}.doc-controls__value{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin:0;font-weight:600;line-height:24px}.doc-controls__label{color:var(--theme-elevation-500);white-space:nowrap;margin:0}.doc-controls__controls-wrapper{--controls-gap:calc(var(--base) / 2);--dot-button-width:calc(var(--base) * 2);display:flex;align-items:center;margin:0;gap:var(--controls-gap);position:relative}.doc-controls__controls{display:flex;align-items:center;margin:0;gap:calc(var(--base) / 2)}.doc-controls__controls button{margin:0;white-space:nowrap}.doc-controls__dots{margin:0;display:flex;align-items:center;justify-content:center;flex-direction:column;gap:2px;border:1px solid var(--theme-elevation-100);border-radius:4px;height:calc(var(--base) * 1.6);width:calc(var(--base) * 1.6)}.doc-controls__dots:hover{border:1px solid var(--theme-elevation-500);background-color:var(--theme-elevation-100)}.doc-controls__dots>div{width:3px;height:3px;border-radius:100%;background-color:currentColor}.doc-controls__popup{position:relative}.doc-controls .popup--active .doc-controls__dots{border:1px solid var(--theme-elevation-500);background-color:var(--theme-elevation-100)}.doc-controls .popup__trigger-wrap{display:flex}}@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.doc-controls.gutter{top:-56px;padding-right:0;padding-left:0}.doc-controls__wrapper{flex-direction:column;gap:0;height:unset}.doc-controls__content{width:100%;overflow:auto;padding-inline:40px}.doc-controls__content::-webkit-scrollbar{display:none}.doc-controls__meta{width:auto;gap:calc(var(--base) / 2)}.doc-controls__meta:after{content:"";display:block;position:absolute;right:0;width:16px;height:var(--base);background:linear-gradient(to right,transparent,var(--theme-bg));flex-shrink:0;z-index:1111;pointer-events:none}.doc-controls__controls-wrapper{background-color:var(--theme-bg);width:100%;transform:translateZ(0);padding-right:var(--gutter-h);justify-content:space-between;height:var(--doc-controls-height);border-top:1px solid var(--theme-elevation-100)}.doc-controls__controls{padding-left:var(--gutter-h);overflow:auto}.doc-controls__controls::-webkit-scrollbar{display:none}.doc-controls__controls:after{content:"";display:block;position:-webkit-sticky;position:sticky;right:0;width:calc(var(--base) * 2);height:calc(var(--base) * 1.5);background:linear-gradient(to right,transparent,var(--theme-bg));flex-shrink:0;z-index:1111;pointer-events:none}}@media (max-width:768px){.doc-controls__content{padding-inline:16px}}}@layer payload-default;@layer payload-default;@layer payload-default;@layer payload-default;@layer payload-default{:root{--diff-delete-pill-bg:var(--theme-error-200);--diff-delete-pill-color:var(--theme-error-600);--diff-delete-pill-border:var(--theme-error-400);--diff-delete-parent-bg:var(--theme-error-100);--diff-delete-parent-color:var(--theme-error-800);--diff-delete-link-color:var(--theme-error-600);--diff-create-pill-bg:var(--theme-success-200);--diff-create-pill-color:var(--theme-success-600);--diff-create-pill-border:var(--theme-success-400);--diff-create-parent-bg:var(--theme-success-100);--diff-create-parent-color:var(--theme-success-800);--diff-create-link-color:var(--theme-success-600)}html[data-theme=dark]{--diff-delete-pill-bg:var(--theme-error-200);--diff-delete-pill-color:var(--theme-error-650);--diff-delete-pill-border:var(--theme-error-400);--diff-delete-parent-bg:var(--theme-error-100);--diff-delete-parent-color:var(--theme-error-900);--diff-delete-link-color:var(--theme-error-750);--diff-create-pill-bg:var(--theme-success-200);--diff-create-pill-color:var(--theme-success-650);--diff-create-pill-border:var(--theme-success-400);--diff-create-parent-bg:var(--theme-success-100);--diff-create-parent-color:var(--theme-success-900);--diff-create-link-color:var(--theme-success-750)}.html-diff{font-size:14px;letter-spacing:.02em}.html-diff-no-value{color:var(--theme-elevation-400)}.html-diff pre{margin-top:0;margin-bottom:0}}@layer payload-default{.html-diff blockquote:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h1:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h2:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h3:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h4:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h5:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h6:not([data-enable-match=false]):has([data-match-type=create]),.html-diff p:not([data-enable-match=false]):has([data-match-type=create]),.html-diff pre:not([data-enable-match=false]):has([data-match-type=create]){position:relative;z-index:1}.html-diff blockquote:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h1:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h2:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h3:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h4:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h5:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h6:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff p:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff pre:not([data-enable-match=false]):has([data-match-type=create]):before{content:"";position:absolute;inset:-10px;display:block;background-color:var(--diff-create-parent-bg);color:var(--diff-create-parent-color);z-index:-1}.html-diff blockquote:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h1:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h2:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h3:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h4:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h5:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h6:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff p:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff pre:not([data-enable-match=false]):has([data-match-type=delete]){position:relative;z-index:1}.html-diff blockquote:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h1:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h2:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h3:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h4:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h5:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h6:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff p:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff pre:not([data-enable-match=false]):has([data-match-type=delete]):before{content:"";position:absolute;inset:-10px;display:block;background-color:var(--diff-delete-parent-bg);color:var(--diff-delete-parent-color);z-index:-1}.html-diff li:not([data-enable-match=false]):has([data-match-type=create]){position:relative;z-index:1}.html-diff li:not([data-enable-match=false]):has([data-match-type=create]):before{content:"";position:absolute;inset:0 -10px;display:block;background-color:var(--diff-create-parent-bg);color:var(--diff-create-parent-color);z-index:-1}.html-diff li:not([data-enable-match=false]):has([data-match-type=delete]){position:relative;z-index:1}.html-diff li:not([data-enable-match=false]):has([data-match-type=delete]):before{content:"";position:absolute;inset:0 -10px;display:block;background-color:var(--diff-delete-parent-bg);color:var(--diff-delete-parent-color);z-index:-1}.html-diff li::marker{color:var(--theme-text)}.html-diff [data-match-type=delete]:not([data-enable-match=false]):not(:is([data-enable-match=false] *)){color:var(--diff-delete-pill-color);-webkit-text-decoration-color:var(--diff-delete-pill-color);text-decoration-color:var(--diff-delete-pill-color);-webkit-text-decoration-line:line-through;text-decoration-line:line-through;background-color:var(--diff-delete-pill-bg);border-radius:4px;text-decoration-thickness:1px}.html-diff a[data-match-type=delete] :not([data-enable-match=false]) :not(:is([data-enable-match=false] *)){color:var(--diff-delete-link-color)}.html-diff a[data-match-type=create]:not(img) :not([data-enable-match=false]) :not(:is([data-enable-match=false] *)){color:var(--diff-create-link-color)}.html-diff [data-match-type=create]:not(img):not([data-enable-match=false]):not(:is([data-enable-match=false] *)){background-color:var(--diff-create-pill-bg);color:var(--diff-create-pill-color);border-radius:4px}.html-diff .html-diff-create-inline-wrapper,.html-diff .html-diff-delete-inline-wrapper{display:inline-flex}.html-diff .html-diff-create-block-wrapper,.html-diff .html-diff-delete-block-wrapper{display:flex}.html-diff .html-diff-create-block-wrapper,.html-diff .html-diff-create-inline-wrapper,.html-diff .html-diff-delete-block-wrapper,.html-diff .html-diff-delete-inline-wrapper{position:relative;align-items:center;flex-direction:row}.html-diff .html-diff-create-block-wrapper:after,.html-diff .html-diff-create-inline-wrapper:after,.html-diff .html-diff-delete-block-wrapper:after,.html-diff .html-diff-delete-inline-wrapper:after{position:absolute;top:0;left:0;display:block;width:100%;height:100%;content:""}.edit-many__form{height:100%}.edit-many__main{width:calc(100% - 300px);display:flex;flex-direction:column;min-height:100%}.edit-many__header{display:flex;margin-top:50px;margin-bottom:20px;width:100%}.edit-many__header__title{margin:0;flex-grow:1}.edit-many__header__close{border:0;background-color:transparent;padding:0;cursor:pointer;overflow:hidden;width:20px;height:20px}.edit-many__header__close svg{width:40px;height:40px;position:relative;inset-inline-start:-10px;top:-10px}.edit-many__header__close svg .stroke{stroke-width:2px;vector-effect:non-scaling-stroke}.edit-many__edit{padding-top:20px;padding-bottom:40px;flex-grow:1}[dir=rtl] .edit-many__sidebar-wrap{left:0;border-right:1px solid var(--theme-elevation-100);right:auto}.edit-many__sidebar-wrap{position:fixed;width:300px;height:100%;top:0;right:0;overflow:visible;border-left:1px solid var(--theme-elevation-100)}.edit-many__sidebar{width:100%;height:100%;overflow-y:auto}.edit-many__sidebar-sticky-wrap{display:flex;flex-direction:column;min-height:100%}[dir=ltr] .edit-many__collection-actions,[dir=ltr] .edit-many__meta,[dir=ltr] .edit-many__sidebar-fields{padding-left:30px}[dir=rtl] .edit-many__collection-actions,[dir=rtl] .edit-many__meta,[dir=rtl] .edit-many__sidebar-fields{padding-right:30px}.edit-many__document-actions{padding-right:1.5384615385rem;position:-webkit-sticky;position:sticky;top:0;z-index:var(--z-nav)}.edit-many__document-actions>*{position:relative;z-index:1}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.edit-many__document-actions:after,.edit-many__document-actions:before{content:" ";position:absolute;inset:0}.edit-many__document-actions:before{background:var(--theme-bg);opacity:.75}.edit-many__document-actions:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}}.edit-many__save{width:calc(50% - 20px)}@media (max-width:1024px){.edit-many__save{width:100%}}.edit-many__draft,.edit-many__publish{width:100%}.edit-many__document-actions{display:flex;padding:20px;gap:10px}.edit-many__document-actions .form-submit{width:100%}@media (max-width:1024px){.edit-many__document-actions .form-submit{width:auto;flex-grow:1}}.edit-many__document-actions .form-submit .btn{padding-left:10px;padding-right:10px;margin-bottom:0}@media (max-width:1024px){.edit-many__main{width:100%;min-height:auto}.edit-many__sidebar-wrap{position:static;width:100%;height:auto}.edit-many__form{display:block}.edit-many__edit{padding-top:0;padding-bottom:0}.edit-many__document-actions{position:fixed;inset:auto 0 0;z-index:var(--z-nav)}.edit-many__document-actions,.edit-many__sidebar-fields{padding-left:var(--gutter-h);padding-right:var(--gutter-h)}}}@layer payload-default;@layer payload-default;@layer payload-default{.dots{margin:0;display:flex;align-items:center;justify-content:center;flex-direction:column;gap:2px;background-color:var(--theme-elevation-150);border-radius:4px;height:calc(var(--base) * 1.2);width:calc(var(--base) * 1.2)}.dots:hover{background-color:var(--theme-elevation-250)}.dots--no-background{width:auto;height:auto}.dots--no-background,.dots--no-background:hover{background-color:transparent}.dots--horizontal{flex-direction:row}.dots>div{width:2px;height:2px;border-radius:100%;background-color:currentColor}[dir=rtl] .search-filter svg{right:10px;left:0}[dir=rtl] .search-filter__input{padding-right:40px;padding-left:0}.search-filter{position:relative}.search-filter svg{position:absolute;top:50%;transform:translateY(-50%);left:10px}.search-filter__input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1);height:auto;padding:0;box-shadow:none;background-color:var(--theme-elevation-50);border:none}.search-filter__input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.search-filter__input[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.search-filter__input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.search-filter__input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.search-filter__input:hover{border-color:var(--theme-elevation-250)}.search-filter__input:active,.search-filter__input:focus,.search-filter__input:focus-within{border-color:var(--theme-elevation-400);outline:0}.search-filter__input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.search-filter__input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.search-filter__input:not(:disabled):focus,.search-filter__input:not(:disabled):hover{box-shadow:none}.condition-value-number{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.condition-value-number:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.condition-value-number[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{.condition-value-number::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.condition-value-number::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.condition-value-number:hover{border-color:var(--theme-elevation-250)}.condition-value-number:active,.condition-value-number:focus,.condition-value-number:focus-within{border-color:var(--theme-elevation-400);outline:0}.condition-value-number:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.condition-value-number:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.condition-value-relationship__error-loading{border:1px solid var(--theme-error-600);min-height:40px;padding:10px 15px;background-color:var(--theme-error-100);color:var(--theme-elevation-0)}.condition-value-text{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.condition-value-text:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.condition-value-text[data-rtl=true]{direction:rtl}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{.condition-value-text::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.condition-value-text::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.condition-value-text:hover{border-color:var(--theme-elevation-250)}.condition-value-text:active,.condition-value-text:focus,.condition-value-text:focus-within{border-color:var(--theme-elevation-400);outline:0}.condition-value-text:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.condition-value-text:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}.condition__inputs,.condition__wrap{display:flex;align-items:center;gap:var(--base)}.condition__inputs{flex-grow:1}.condition__inputs>div{flex-basis:100%}.condition__field .field-label{padding-bottom:0}.condition__actions{flex-shrink:0;display:flex;gap:calc(var(--base) / 2);padding:calc(var(--base) / 2) 0}.condition .btn{vertical-align:middle;margin:0}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.condition__wrap{align-items:normal;gap:calc(var(--base) / 2)}.condition__inputs{flex-direction:column;gap:calc(var(--base) / 2);align-items:stretch}.condition__actions{display:flex;flex-direction:column;justify-content:space-between}}}@layer payload-default;@layer payload-default;@layer payload-default{.where-builder{background:var(--theme-elevation-50);padding:var(--base);display:flex;flex-direction:column;gap:calc(var(--base) / 2)}.where-builder .btn{margin:0;align-self:flex-start}.where-builder__and-filters,.where-builder__no-filters,.where-builder__or-filters{display:flex;flex-direction:column;gap:calc(var(--base) / 2)}.where-builder__and-filters,.where-builder__or-filters{list-style:none;margin:0;padding:0}.where-builder__and-filters li,.where-builder__or-filters li{display:flex;flex-direction:column;gap:calc(var(--base) / 2)}@media (max-width:768px){.where-builder{padding:calc(var(--base) / 2)}}}@layer payload-default;@layer payload-default;@layer payload-default{.icon--people .stroke{stroke:currentColor;stroke-width:1px;fill:none}.active-query-preset .pill__label{gap:calc(var(--base) / 4);display:flex;align-items:center}.active-query-preset__label-text-max-width{max-width:100px;overflow:hidden}.active-query-preset__label-text{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.active-query-preset__clear,.active-query-preset__label-and-clear-wrap,.list-controls__wrap{display:flex;align-items:center}.list-controls__wrap{background-color:var(--theme-elevation-50);border-radius:var(--style-radius-m);padding:12px;gap:12px}.list-controls .search-filter{flex-grow:1}.list-controls .search-filter input{margin:0}.list-controls__custom-control{padding:0;border-radius:0}.list-controls__modified{color:var(--theme-elevation-500)}.list-controls__buttons-wrap{display:flex;align-items:center;gap:calc(var(--base) / 4)}.list-controls .pill-selector,.list-controls .sort-complex,.list-controls .where-builder{margin-top:20px}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:768px){.list-controls__wrap{flex-wrap:wrap;background-color:unset;padding:0;position:relative}.list-controls .icon--search{position:absolute;top:8px;inset-inline-start:8px;z-index:1}.list-controls .search-filter{width:100%}.list-controls .search-filter input{padding:8px 40px}[dir=ltr] .list-controls__buttons-wrap{margin-right:0}[dir=rtl] .list-controls__buttons-wrap{margin-left:0}.list-controls__buttons-wrap .pill{padding:4px 4px 4px 8px;justify-content:space-between}.list-controls__buttons{margin:0;width:100%}.list-controls .pill-selector,.list-controls .sort-complex,.list-controls .where-builder{margin-top:calc(var(--base) / 2)}.list-controls__toggle-columns,.list-controls__toggle-sort,.list-controls__toggle-where{flex:1 1}}}.close-modal-button{flex-shrink:0;border:0;background-color:transparent;padding:0;margin:0;cursor:pointer;overflow:hidden;width:var(--base);height:var(--base);align-self:flex-start}.close-modal-button svg{width:calc(var(--base) * 2);height:calc(var(--base) * 2);position:relative;inset-inline-start:calc(var(--base) * -.5);top:calc(var(--base) * -.5)}.close-modal-button svg .stroke{stroke-width:2px;vector-effect:non-scaling-stroke}@layer payload-default{.list-folder-pills{display:flex;gap:calc(var(--base) * .5);margin-left:calc(var(--base) * .5)}.list-header{display:flex;align-items:flex-end;flex-wrap:wrap}.list-header__content{display:grid;grid-template-columns:1fr auto;width:100%}.list-header__title-and-actions{display:flex;flex-wrap:wrap;align-items:flex-end;gap:calc(var(--base) * .5)}.list-header__title{margin:0}.list-header__actions,.list-header__title-actions{margin-bottom:4px;display:flex;gap:calc(var(--base) * .5)}.list-header__actions{flex-wrap:wrap;align-items:center}.list-header__after-header-content{width:100%}.list-header .btn{margin:0}.list-drawer .list-header__title{font-family:var(--font-body);font-weight:500;margin:0;font-size:26px;line-height:32px}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{@media (max-width:768px){.list-drawer .list-header__title{font-size:17px}}}@layer payload-default;@layer payload-default{.list-drawer__header{margin-top:50px;width:100%}@media (max-width:1024px){.list-drawer__header{margin-top:30px}}.list-drawer .doc-drawer__toggler.list-header__create-new-button{background:transparent;border:0;padding:0;cursor:pointer;color:inherit;border-radius:var(--style-radius-s)}.list-drawer .doc-drawer__toggler.list-header__create-new-button:hover .pill{background:var(--theme-elevation-250)}.list-drawer .doc-drawer__toggler.list-header__create-new-button:focus-within:not(:focus-visible),.list-drawer .doc-drawer__toggler.list-header__create-new-button:focus:not(:focus-visible){outline:none}.list-drawer .doc-drawer__toggler.list-header__create-new-button:focus-visible{outline:var(--accessibility-outline);outline-offset:var(--accessibility-outline-offset)}.list-drawer .doc-drawer__toggler.list-header__create-new-button:disabled{pointer-events:none}.list-drawer .doc-drawer__toggler.list-header__create-new-button .pill{cursor:inherit}.list-drawer__select-collection-wrap{margin-top:20px}@media (max-width:1024px){.list-drawer .collection-list__header{margin-bottom:10px}.list-drawer__select-collection-wrap{margin-top:calc(var(--base) / 2)}}}@layer payload-default;@layer payload-default;@layer payload-default{.icon--logout{height:1.5384615385rem;width:1.5384615385rem}.icon--logout .stroke{stroke:currentColor;stroke-width:2px}.nav-group{width:100%;margin-bottom:10px}.nav-group__toggle{cursor:pointer;color:var(--theme-elevation-400);background:transparent;border:0;margin-bottom:5px;width:100%;text-align:left;display:flex;align-items:flex-start;padding:0;gap:10px;justify-content:space-between}.nav-group__toggle svg{flex-shrink:0;margin-top:-4px}}@layer payload-default;@layer payload-default;@layer payload-default{.nav-group__toggle:focus-visible,.nav-group__toggle:hover{color:var(--theme-elevation-1000)}.nav-group__toggle:focus-visible .stroke,.nav-group__toggle:hover .stroke{stroke:var(--theme-elevation-1000)}.nav-group__toggle:focus-visible{outline:none}.nav-group__indicator{position:relative;flex-shrink:0}.nav-group__indicator svg .stroke{stroke:var(--theme-elevation-200)}.nav-group--collapsed .collapsible__toggle{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.per-page__button{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body)}.per-page ul{list-style:none;padding:0;margin:0;display:flex;flex-direction:column;gap:calc(var(--base) / 4)}.per-page__base-button{display:flex;align-items:center;font-weight:700}.per-page__button{cursor:pointer;text-align:left;width:100%;display:flex;align-items:center;color:var(--theme-elevation-500)}}@layer payload-default;@layer payload-default{}@layer payload-default{.per-page__button:focus-visible,.per-page__button:hover{text-decoration:underline}.per-page__button svg .stroke{stroke:currentColor}.per-page__button-active{font-weight:700;color:var(--theme-text)}.browse-by-folder-button{border:1px solid var(--theme-elevation-100);text-decoration:none;padding:calc(var(--base) / 2);display:flex;align-items:center;width:100%;gap:calc(var(--base) * .33);margin-top:calc(var(--base) * .25);margin-bottom:var(--base);border-radius:var(--style-radius-m);color:var(--theme-text)}.browse-by-folder-button.active{background-color:var(--theme-elevation-50);font-weight:600}.browse-by-folder-button.active .icon{color:var(--theme-elevation-400)}.browse-by-folder-button .icon{color:var(--theme-elevation-300)}.icon--document{height:var(--base);width:var(--base)}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}.icon--dots{margin:0;display:flex;align-items:center;justify-content:center;flex-direction:column;gap:2px;height:2rem;width:2rem}.icon--dots>div{width:3px;height:3px;border-radius:100%;background-color:currentColor}@layer payload-default{.draggable-with-click{-webkit-user-select:none;-moz-user-select:none;user-select:none}.folder-file-card{--card-border-color:var(--theme-elevation-150);--card-bg-color:var(--theme-elevation-0);--card-preview-bg-color:var(--theme-elevation-50);--card-icon-dots-bg-color:transparent;--card-icon-dots-color:var(--theme-elevation-600);--card-titlebar-icon-color:var(--theme-elevation-300);--card-label-color:var(--theme-text);--card-preview-icon-color:var(--theme-elevation-400);position:relative;display:grid;grid-template-areas:"details";border-radius:var(--style-radius-m);border:1px solid var(--card-border-color);background-color:var(--card-bg-color);cursor:pointer}.folder-file-card--file{grid-template-rows:1fr auto;grid-template-areas:"preview" "details"}.folder-file-card--over{--card-border-color:var(--theme-elevation-500);--card-bg-color:var(--theme-elevation-150);--card-icon-dots-bg-color:transparent;--card-icon-dots-color:var(--theme-elevation-400);--card-titlebar-icon-color:var(--theme-elevation-250);--card-label-color:var(--theme-text)}.folder-file-card--disabled{--card-bg-color:var(--theme-elevation-50);cursor:not-allowed}.folder-file-card--disabled:after{content:"";position:absolute;background-color:var(--theme-bg);opacity:.5;width:calc(100% + 2px);height:calc(100% + 2px);top:-1px;left:-1px;border-radius:inherit}.folder-file-card--selected{--card-border-color:var(--theme-success-300);--card-bg-color:var(--theme-success-50);--card-preview-bg-color:var(--theme-success-50);--card-icon-dots-bg-color:var(--theme-success-50);--card-icon-dots-color:var(--theme-success-400);--card-titlebar-icon-color:var(--theme-success-800);--card-label-color:var(--theme-success-800);--card-preview-icon-color:var(--theme-success-800);--accessibility-outline:2px solid var(--theme-success-600)}.folder-file-card--selected .popup:hover:not(.popup--active){--card-icon-dots-bg-color:var(--theme-success-100)}}@layer payload-default;@layer payload-default;@layer payload-default{.folder-file-card--selected:has(.popup--active){--card-icon-dots-bg-color:var(--theme-success-150)}.folder-file-card--selected .icon--dots{opacity:1}.folder-file-card--selected .folder-file-card__icon-wrap .icon{opacity:50%}.folder-file-card--selected .folder-file-card__preview-area .icon{opacity:.7}.folder-file-card:not(.folder-file-card--selected) .icon--dots{opacity:0}.folder-file-card:not(.folder-file-card--selected):has(.popup--active),.folder-file-card:not(.folder-file-card--selected):hover{--card-bg-color:var(--theme-elevation-50)}.folder-file-card:not(.folder-file-card--selected):has(.popup--active) .icon--dots,.folder-file-card:not(.folder-file-card--selected):hover .icon--dots{opacity:1}.folder-file-card:not(.folder-file-card--selected) .popup:hover:not(.popup--active){--card-icon-dots-bg-color:var(--theme-elevation-150)}.folder-file-card:not(.folder-file-card--selected):has(.popup--active){--card-icon-dots-bg-color:var(--theme-elevation-200)}.folder-file-card__drag-handle{position:absolute;top:0;width:100%;height:100%;cursor:pointer;background:none;border:none;padding:0;outline-offset:var(--accessibility-outline-offset)}.folder-file-card__drag-handle:focus-visible{outline:var(--accessibility-outline)}.folder-file-card__drop-area{position:absolute;top:0;left:0;width:100%;height:100%;border-radius:inherit;pointer-events:none}.folder-file-card__preview-area{grid-area:preview;aspect-ratio:1/1;background-color:var(--card-preview-bg-color);border-top-left-radius:var(--style-radius-s);border-top-right-radius:var(--style-radius-s);border-bottom:1px solid var(--card-border-color);display:grid;align-items:center;justify-content:center;pointer-events:none;grid-template-columns:auto 50% auto}.folder-file-card__preview-area:has(.thumbnail){grid-template-columns:unset}.folder-file-card__preview-area>.icon{grid-column:2}.folder-file-card__preview-area .icon--document{pointer-events:none;height:50%;width:50%;margin:auto;color:var(--card-preview-icon-color)}.folder-file-card__preview-area .thumbnail{width:100%;height:100%;position:relative;border-radius:inherit}.folder-file-card__preview-area .thumbnail>img{position:absolute;inset:0;width:100%;height:100%;object-fit:cover;border-radius:inherit}.folder-file-card__preview-area:has(.thumbnail){justify-content:stretch}.folder-file-card__preview-area img{height:100%;width:100%;max-width:100%;max-height:100%;object-fit:cover;border-top-left-radius:var(--style-radius-s);border-top-right-radius:var(--style-radius-s)}.folder-file-card__titlebar-area{position:relative;pointer-events:none;grid-area:details;border-radius:inherit;display:grid;grid-template-columns:auto 1fr auto;grid-gap:1rem;gap:1rem;align-items:center;padding:1rem;background-color:var(--card-bg-color)}.folder-file-card__titlebar-area .popup{pointer-events:all}.folder-file-card__name{overflow:hidden;font-weight:700;text-indent:1px;text-wrap:nowrap;text-overflow:ellipsis;line-height:normal;color:var(--card-label-color)}.folder-file-card__icon-wrap .icon{flex-shrink:0;color:var(--card-titlebar-icon-color)}.folder-file-card .icon--dots{rotate:90deg;transition:opacity .2s;color:var(--card-icon-dots-color);border-radius:var(--style-radius-s);background-color:var(--card-icon-dots-bg-color)}.item-card-grid{--gap:var(--base);grid-gap:var(--gap);gap:var(--gap);display:grid;grid-template-columns:repeat(auto-fill,minmax(250px,1fr));margin-bottom:var(--base)}.item-card-grid__title{color:var(--theme-elevation-400);margin-bottom:calc(var(--base) / 2)}.simple-table{margin-bottom:var(--base);overflow:auto;max-width:100%}.simple-table__table{min-width:100%;border-collapse:collapse}.simple-table__thead{color:var(--theme-elevation-400)}.simple-table__th{font-weight:400;text-align:left}[dir=rtl] .simple-table__th{text-align:right}.simple-table__td,.simple-table__th{vertical-align:top;padding:calc(var(--base) * .6);min-width:150px;position:relative}.simple-table__td:first-child,.simple-table__th:first-child{-webkit-padding-start:calc(var(--base) * .8);padding-inline-start:calc(var(--base) * .8)}.simple-table__td:last-child,.simple-table__th:last-child{-webkit-padding-end:calc(var(--base) * .8);padding-inline-end:calc(var(--base) * .8)}.simple-table .simple-table__thead .simple-table__tr:after{background-color:transparent}.simple-table__hidden-cell{position:absolute;padding:0}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.simple-table__td,.simple-table__th{max-width:70vw}}}@layer payload-default{.draggable-table-row{--border-top-left-radius:var(--style-radius-m);--border-top-right-radius:var(--style-radius-m);--border-bottom-right-radius:var(--style-radius-m);--border-bottom-left-radius:var(--style-radius-m);--row-text-color:var(--theme-text);--row-icon-opacity:1;--row-icon-color:var(--theme-elevation-400);--row-bg-color:transparent;--row-opacity:1;--foreground-opacity:0;--row-cursor:pointer;isolation:isolate;opacity:var(--row-opacity);cursor:var(--row-cursor)}.draggable-table-row__first-td{border-top-left-radius:var(--border-top-left-radius);border-bottom-left-radius:var(--border-bottom-left-radius)}.draggable-table-row td.draggable-table-row__last-td{border-top-right-radius:var(--border-top-right-radius);border-bottom-right-radius:var(--border-bottom-right-radius);-webkit-padding-end:calc(var(--base) * .8);padding-inline-end:calc(var(--base) * .8)}.draggable-table-row:not(.draggable-table-row--selected):nth-child(odd){--row-bg-color:var(--theme-elevation-50)}.draggable-table-row:nth-child(odd):after{display:none}.draggable-table-row--focused.draggable-table-row:nth-child(2n),.draggable-table-row--focused.draggable-table-row:nth-child(odd){--row-bg-color:var(--theme-elevation-100)}.draggable-table-row--disabled{--row-cursor:no-drop;--row-opacity:.6}.draggable-table-row--selected{--row-icon-color:var(--theme-success-800);--row-icon-opacity:.6}.draggable-table-row--selected.draggable-table-row:nth-child(2n),.draggable-table-row--selected.draggable-table-row:nth-child(odd){--row-bg-color:var(--theme-success-150)}.draggable-table-row--selected+.draggable-table-row--selected{--border-top-left-radius:0;--border-top-right-radius:0}.draggable-table-row--selected:not(:last-child):has(+.draggable-table-row--selected){--border-bottom-left-radius:0;--border-bottom-right-radius:0}.draggable-table-row--over.draggable-table-row:nth-child(2n),.draggable-table-row--over.draggable-table-row:nth-child(odd){--row-bg-color:var(--theme-elevation-150)}.draggable-table-row__cell-content{position:relative;z-index:1;color:var(--row-text-color);background-color:var(--row-bg-color)}.draggable-table-row__drag-handle{position:absolute;top:0;width:100%;height:100%;left:0;right:0;cursor:var(--row-cursor);background:none;border:none;padding:0;outline-offset:0;z-index:2}.draggable-table-row__drag-handle:focus-visible{box-shadow:inset 0 0 0 2px var(--theme-text);outline:none}.draggable-table-row__drop-area{position:absolute;top:0;width:100%;height:100%;left:0;right:0}.draggable-table-row .simple-table__hidden-cell{position:absolute;padding:0;width:100%;height:100%;left:0;right:0}.draggable-table-row.draggable-table-row{position:relative}.draggable-table-row .icon{color:var(--row-icon-color);opacity:var(--row-icon-opacity)}.folder-file-table__cell-with-icon{display:grid;grid-template-columns:auto 1fr;grid-gap:calc(var(--base) / 2);gap:calc(var(--base) / 2)}.id-label{font-size:16px;line-height:24px;font-weight:400;color:var(--theme-elevation-600);background:var(--theme-elevation-100);padding:4px 8px;border-radius:4px;display:inline-flex}.render-title{display:inline-block}.render-title__id{vertical-align:middle;position:relative}.sort-column{display:flex;gap:calc(var(--base) / 2);align-items:center}.sort-column__label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.sort-column .btn,.sort-column__label{vertical-align:middle;display:inline-block}.sort-column__label{cursor:default}.sort-column__buttons{display:flex;align-items:center;gap:0}.sort-column__button{margin:0;opacity:.3;padding:calc(var(--base) / 4) 0;display:inline-flex;align-items:center;justify-content:center;background:transparent;border:none;cursor:pointer}.sort-column__button.sort-column--active{opacity:1;visibility:visible}.sort-column__button:hover{opacity:.7}.sort-column:hover .btn{opacity:.4;visibility:visible}.sort-column--appearance-condensed{gap:calc(var(--base) / 4)}.sort-column--appearance-condensed .sort-column__buttons{gap:0}.icon--move-folder{height:var(--base);width:var(--base)}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}.icon.icon--grid-view,.icon.icon--list-view{stroke:currentColor}@layer payload-default{.progress-bar{position:fixed;top:0;left:0;width:100%;height:2px;z-index:9999;opacity:1}.progress-bar__progress{height:100%;background-color:var(--theme-elevation-1000);transition:width ease-in var(--transition-duration)}.progress-bar--fade-out{opacity:0;transition:opacity linear var(--transition-duration);transition-delay:var(--transition-duration)}.login-fields{display:flex;flex-direction:column;gap:var(--base)}.select-all__checkbox{display:block}.select-row__checkbox{display:block;width:min-content}.collection-list{width:100%}.collection-list__wrap{padding-bottom:var(--spacing-view-bottom)}.collection-list__wrap>:not(:last-child){margin-bottom:var(--base)}.collection-list .list-header a{text-decoration:none}.collection-list__sub-header{flex-basis:100%}.collection-list .table table{width:100%;overflow:auto}.collection-list .table table [class^=cell]>a,.collection-list .table table [class^=cell]>p,.collection-list .table table [class^=cell]>span{line-clamp:4;-webkit-box-orient:vertical;-webkit-line-clamp:4;overflow:hidden;display:-webkit-box;max-width:100vw}.collection-list .table table #heading-_select,.collection-list .table table .cell-_select{min-width:unset}.collection-list .table table #heading-_dragHandle,.collection-list .table table .cell-_dragHandle{width:20px;min-width:0}.collection-list__no-results{display:flex;flex-direction:column;align-items:flex-start;gap:var(--base)}.collection-list__no-results>*{margin:0}.collection-list__page-controls{width:100%;display:flex;align-items:center}.collection-list .paginator{margin-bottom:0}[dir=ltr] .collection-list__page-info{margin-right:20px;margin-left:auto}[dir=rtl] .collection-list__page-info{margin-left:20px;margin-right:auto}.collection-list__list-selection{position:fixed;bottom:0;z-index:10;padding:16px 0;width:100%;background-color:var(--theme-bg)}.collection-list__list-selection .btn{margin:0 0 0 8px;background-color:var(--theme-elevation-100);cursor:pointer;padding:0 8px;border-radius:3px}.collection-list__list-selection .btn:hover{background-color:var(--theme-elevation-200)}.collection-list__list-selection-actions{display:flex;gap:5px}.collection-list__shimmer{margin-top:35px;width:100%}.collection-list__shimmer>div{margin-top:8px}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.collection-list{margin-top:5px}.collection-list__wrap{padding-top:0;padding-bottom:0}.collection-list__header{gap:10px}.collection-list__sub-header{margin-top:0}.collection-list__search-input{margin:0}.collection-list .table{display:flex;width:calc(100% + var(--gutter-h) * 2);max-width:unset;left:calc(var(--gutter-h) * -1);position:relative;padding-left:var(--gutter-h)}.collection-list .table:after{content:"";height:1px;padding-right:var(--gutter-h)}.collection-list__page-controls{flex-wrap:wrap}.collection-list__page-info{margin-left:0}.collection-list .paginator{width:100%;margin-bottom:1.5384615385rem}}@media (max-width:768px){.collection-list{margin-bottom:48px}}}@layer payload-default{.drag-overlay-selection__cards{display:grid;grid-template-columns:1fr;grid-template-rows:1fr}.drag-overlay-selection__card{position:absolute;width:100%;height:100%;grid-column:1/2;grid-row:1/2}.drag-overlay-selection__card-count{position:absolute;transform:translate(calc(50% - 3px),calc(-50% - 3px));right:0;top:0;border-radius:50%;line-height:1;width:26px;height:26px;display:flex;align-items:center;justify-content:center;color:var(--theme-success-50);background:var(--theme-success-600);font-weight:700;font-feature-settings:"tnum";font-variant-numeric:tabular-nums}.sort-by-pill__order-option{display:flex;gap:calc(var(--base) * .25)}.sort-by-pill__trigger .pill__label{display:flex;align-items:center;gap:4px}.folder-view-toggle-button{padding:0;background-color:transparent}.folder-view-toggle-button--active{background-color:var(--theme-elevation-150)}.folder-view-toggle-button .btn__icon{border:none;padding:0}.search-bar{width:100%;display:flex;align-items:center;background-color:var(--theme-elevation-50);border-radius:var(--style-radius-m);padding:calc(var(--base) * .6);gap:calc(var(--base) * .6)}.search-bar .search-filter,.search-bar .search-filter input{flex-grow:1}.search-bar__actions{display:flex;align-items:center;gap:calc(var(--base) / 4)}.collection-folder-list{width:100%}.collection-folder-list__step-nav-icon-label,.collection-folder-list__step-nav-icon-label .btn__label{margin:0;display:flex;align-items:center;gap:calc(var(--base) * .25)}.collection-folder-list__step-nav-icon-label .btn__label .icon,.collection-folder-list__step-nav-icon-label .icon{height:18px}.collection-folder-list__step-nav-droppable.droppable-button--hover{opacity:.3}.collection-folder-list__wrap{padding-bottom:var(--spacing-view-bottom)}.collection-folder-list__wrap>:not(:last-child){margin-bottom:var(--base)}.collection-folder-list .cell-with-icon{display:flex;align-items:center;gap:calc(var(--base) * .5)}.collection-folder-list .list-header a{text-decoration:none}.collection-folder-list__sub-header{flex-basis:100%}.collection-folder-list .table table{width:100%;overflow:auto}.collection-folder-list .table table [class^=cell]>a,.collection-folder-list .table table [class^=cell]>p,.collection-folder-list .table table [class^=cell]>span{line-clamp:4;-webkit-box-orient:vertical;-webkit-line-clamp:4;overflow:hidden;display:-webkit-box;max-width:100vw}.collection-folder-list__no-results{display:flex;flex-direction:column;align-items:flex-start;gap:var(--base)}.collection-folder-list__no-results>*{margin:0}.collection-folder-list__page-controls{width:100%;display:flex;align-items:center}.collection-folder-list .paginator{margin-bottom:0}[dir=ltr] .collection-folder-list__page-info{margin-right:20px;margin-left:auto}[dir=rtl] .collection-folder-list__page-info{margin-left:20px;margin-right:auto}.collection-folder-list__shimmer{margin-top:35px;width:100%}.collection-folder-list__shimmer>div{margin-top:8px}}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.collection-folder-list{margin-top:5px}.collection-folder-list__wrap{padding-top:0;padding-bottom:0}.collection-folder-list__header{gap:10px}.collection-folder-list__sub-header{margin-top:0}.collection-folder-list__search-input{margin:0}.collection-folder-list .table{display:flex;width:calc(100% + var(--gutter-h) * 2);max-width:unset;left:calc(var(--gutter-h) * -1);position:relative;padding-left:var(--gutter-h)}.collection-folder-list .table:after{content:"";height:1px;padding-right:var(--gutter-h)}.collection-folder-list__page-controls{flex-wrap:wrap}.collection-folder-list__page-info{margin-left:0}.collection-folder-list .paginator{width:100%;margin-bottom:1.5384615385rem}}@media (max-width:768px){.collection-folder-list{margin-bottom:48px}}}@layer payload-default;@layer payload-default;@layer payload-default{.checkbox-popup__options{display:flex;flex-direction:column;gap:calc(var(--base) * .5);padding:0 3px}.checkbox-popup .checkbox-input{align-items:center}.checkbox-popup .checkbox-input label{padding-bottom:0}.collection-type__count{font-weight:600;font-feature-settings:"tnum";font-variant-numeric:tabular-nums;background-color:var(--theme-bg);color:var(--theme-text);padding:0 3px;border-radius:var(--style-radius-s);margin-left:-4px;margin-right:calc(var(--base) * .25)}.folder-list{width:100%}.folder-list__toggle-view-button{padding:0;background-color:transparent .folder-list__toggle-view-button --active;background-color-background-color:var(--theme-elevation-150)}.folder-list__toggle-view-button .btn__icon{border:none;padding:0}.folder-list__step-nav-icon-label,.folder-list__step-nav-icon-label .btn__label{margin:0;display:flex;align-items:center;gap:calc(var(--base) * .25)}.folder-list__step-nav-icon-label .btn__label .icon,.folder-list__step-nav-icon-label .icon{height:18px}.folder-list__step-nav-droppable.droppable-button--hover{opacity:.3}.folder-list__wrap{padding-bottom:var(--spacing-view-bottom)}.folder-list__wrap>:not(:last-child){margin-bottom:var(--base)}.folder-list .cell-with-icon{display:flex;align-items:center;gap:calc(var(--base) * .5)}.folder-list .list-header a{text-decoration:none}.folder-list__sub-header{flex-basis:100%}.folder-list .table table{width:100%;overflow:auto}.folder-list .table table [class^=cell]>a,.folder-list .table table [class^=cell]>p,.folder-list .table table [class^=cell]>span{line-clamp:4;-webkit-box-orient:vertical;-webkit-line-clamp:4;overflow:hidden;display:-webkit-box;max-width:100vw}.folder-list__no-results{display:flex;flex-direction:column;align-items:flex-start;gap:var(--base)}.folder-list__no-results>*{margin:0}.folder-list__page-controls{width:100%;display:flex;align-items:center}.folder-list .paginator{margin-bottom:0}[dir=ltr] .folder-list__page-info{margin-right:20px;margin-left:auto}[dir=rtl] .folder-list__page-info{margin-left:20px;margin-right:auto}.folder-list__shimmer{margin-top:35px;width:100%}.folder-list__shimmer>div{margin-top:8px}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.folder-list{margin-top:5px}.folder-list__wrap{padding-top:0;padding-bottom:0}.folder-list__header{gap:10px}.folder-list__sub-header{margin-top:0}.folder-list__search-input{margin:0}.folder-list .table{display:flex;width:calc(100% + var(--gutter-h) * 2);max-width:unset;left:calc(var(--gutter-h) * -1);position:relative;padding-left:var(--gutter-h)}.folder-list .table:after{content:"";height:1px;padding-right:var(--gutter-h)}.folder-list__page-controls{flex-wrap:wrap}.folder-list__page-info{margin-left:0}.folder-list .paginator{width:100%;margin-bottom:1.5384615385rem}}@media (max-width:768px){.folder-list{margin-bottom:48px}}}@layer payload-default;@layer payload-default;@layer payload-default{.doc-drawer__header{width:100%;margin-top:calc(var(--base) * 2);display:flex;flex-direction:column;gap:calc(var(--base) * .5);align-items:flex-start;border-bottom:1px solid var(--theme-elevation-100);padding-bottom:var(--base)}.doc-drawer__header-content{display:flex;justify-content:space-between;align-items:flex-start;width:100%}.doc-drawer__header-text{margin:0}.doc-drawer__header-toggler{background:transparent;border:0;margin:0;padding:0;cursor:pointer;color:inherit}.doc-drawer__header-toggler:focus,.doc-drawer__header-toggler:focus-within{outline:none}.doc-drawer__header-toggler:disabled{pointer-events:none}.doc-drawer__header-close{border:0;background-color:transparent;padding:0;cursor:pointer;overflow:hidden}.doc-drawer__header-close,.doc-drawer__header-close svg{width:calc(var(--base) * 2);height:calc(var(--base) * 2)}.doc-drawer__header-close svg{position:relative}.doc-drawer__header-close svg .stroke{stroke-width:2px;vector-effect:non-scaling-stroke}.doc-drawer__divider{height:1px;background:var(--theme-elevation-100);width:100%}@media (max-width:1024px){.doc-drawer .doc-drawer__header{margin-top:calc(var(--base) * 1.5);margin-bottom:calc(var(--base) * .5);padding-left:var(--gutter-h);padding-right:var(--gutter-h)}}}@layer payload-default{.live-preview-iframe{background-color:#fff;border:0;width:100%;height:100%;transform-origin:top left}.toolbar-input{width:50px;height:var(--base);display:flex;align-items:center;border:1px solid var(--theme-elevation-200);background:var(--theme-elevation-100);border-radius:2px;font-size:small}.live-preview-toolbar-controls{display:flex;align-items:center;gap:calc(var(--base) / 3)}.live-preview-toolbar-controls__breakpoint{border:none;background:transparent;height:var(--base)}.live-preview-toolbar-controls__breakpoint:focus{outline:none}.live-preview-toolbar-controls__device-size{display:flex;align-items:center}.live-preview-toolbar-controls__size{width:50px;height:var(--base);display:flex;align-items:center;border:1px solid var(--theme-elevation-200);background:var(--theme-elevation-100);border-radius:2px;font-size:small}.live-preview-toolbar-controls__zoom{width:55px;border:none;background:transparent;height:var(--base)}.live-preview-toolbar-controls__zoom:focus{outline:none}.live-preview-toolbar-controls__external{flex-shrink:0;display:flex;width:var(--base);height:var(--base);align-items:center;justify-content:center;padding:6px 0}.live-preview-toolbar,.live-preview-toolbar-controls .popup-button{display:flex;align-items:center}.live-preview-toolbar{background-color:var(--theme-bg);color:var(--theme-text);height:calc(var(--base) * 1.75);flex-shrink:0}.live-preview-toolbar--static{position:relative;width:100%;justify-content:center;border-bottom:1px solid var(--theme-elevation-100)}.live-preview-toolbar--draggable{box-shadow:0 -2px 16px -2px #0003;position:absolute;top:0;left:0;margin:0;border-radius:4px}.live-preview-toolbar__drag-handle{background:transparent;border:0;padding:0;cursor:-webkit-grab;cursor:grab}.live-preview-toolbar__drag-handle .icon--drag-handle .fill{fill:var(--theme-elevation-300)}.live-preview-toolbar__drag-handle:active{cursor:-webkit-grabbing;cursor:grabbing}.live-preview-window{background-color:var(--theme-bg);display:none;width:60%;flex-shrink:0;flex-grow:0;position:-webkit-sticky;position:sticky;top:var(--doc-controls-height);height:calc(100vh - var(--doc-controls-height));overflow:hidden}.live-preview-window--is-live-previewing{display:block}.live-preview-window__wrapper{display:flex;flex-direction:column;height:100%;justify-content:flex-start}.live-preview-window__main{flex-grow:1;height:100%;width:100%}.live-preview-window--has-breakpoint .live-preview-iframe{border:1px solid var(--theme-elevation-100)}.live-preview-window--has-breakpoint .live-preview-window__main{padding:var(--base)}}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.live-preview-window{width:100%}}}@layer payload-default;@layer payload-default;@layer payload-default{.auth-fields{padding:calc(var(--base) * 2);background:var(--theme-elevation-50);display:flex;flex-direction:column;gap:var(--base)}.auth-fields__controls{display:flex;align-items:center;gap:calc(var(--base) / 2);flex-wrap:wrap}.auth-fields__changing-password{display:flex;flex-direction:column;gap:var(--base)}.auth-fields .btn{margin:0}.auth-fields__api-key-label{position:relative}@media (max-width:1024px){.auth-fields{padding:var(--base)}.auth-fields,.auth-fields__changing-password{gap:calc(var(--base) / 2)}}.field-type.api-key{margin-bottom:var(--base)}.field-type.api-key input{box-shadow:0 2px 2px -1px #0000001a;font-family:var(--font-body);width:100%;border:1px solid var(--theme-elevation-150);border-radius:var(--style-radius-s);background:var(--theme-input-bg);color:var(--theme-elevation-800);font-size:1rem;height:40px;line-height:20px;padding:8px 15px;-webkit-appearance:none;transition-property:border,box-shadow,background-color;transition-duration:.1s,.1s,.5s;transition-timing-function:cubic-bezier(0,.2,.2,1)}.field-type.api-key input:not(:disabled):hover{box-shadow:0 2px 2px -1px #0003}.field-type.api-key input[data-rtl=true]{direction:rtl}.field-type.api-key input::-webkit-input-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.api-key input::-moz-placeholder{color:var(--theme-elevation-400);font-weight:400;font-size:1rem}.field-type.api-key input:hover{border-color:var(--theme-elevation-250)}.field-type.api-key input:active,.field-type.api-key input:focus,.field-type.api-key input:focus-within{border-color:var(--theme-elevation-400);outline:0}.field-type.api-key input:disabled{background:var(--theme-elevation-100);color:var(--theme-elevation-400);box-shadow:none}.field-type.api-key input:disabled:hover{border-color:var(--theme-elevation-150);box-shadow:none}@keyframes highlight{0%{background:var(--theme-success-250);border:1px solid var(--theme-success-500)}20%{background:var(--theme-input-bg);border:1px solid var(--theme-elevation-250);color:var(--theme-text)}80%{background:var(--theme-input-bg);border:1px solid var(--theme-elevation-250);color:var(--theme-text)}to{background:var(--theme-elevation-200);border:1px solid transparent;color:var(--theme-elevation-400)}}.highlight{animation:highlight 10s}.collection-edit{--gradient:linear-gradient(270deg,rgba(0,0,0,.04) 0%,transparent)}.collection-edit__main-wrapper{width:100%;display:flex}.collection-edit__main{width:100%;container-type:inline-size}.collection-edit__main--popup-open{width:100%}.collection-edit__main--is-live-previewing{width:40%;position:relative}.collection-edit__main--is-live-previewing:after{content:" ";position:absolute;top:0;right:0;width:calc(var(--base) * 2);height:100%;background:var(--gradient);pointer-events:none;z-index:-1}.collection-edit__form{height:100%;width:100%}.collection-edit__auth{margin-bottom:32px;border-radius:var(--style-radius-s)}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:768px){.collection-edit__auth{margin-top:0;margin-bottom:var(--base)}}html[data-theme=dark] .collection-edit{--gradient:linear-gradient(270deg,rgba(0,0,0,.4) 0%,rgba(0,0,0,0))}.field-diff-label{margin-bottom:calc(var(--base) * .35);font-weight:600;display:flex;flex-direction:row;height:100%;align-items:center;line-height:normal}.field-diff__locale-label{background:var(--theme-elevation-100);border-radius:var(--style-radius-s);padding:calc(var(--base) * .2)}[dir=ltr] .field-diff__locale-label{margin-right:calc(var(--base) * .25)}[dir=rtl] .field-diff__locale-label{margin-left:calc(var(--base) * .25)}.field-diff-container{position:relative}.field-diff-content{display:grid;grid-template-columns:calc(50% - 10px) calc(50% - 10px);grid-gap:20px;gap:20px;background:var(--theme-elevation-50);padding:10px}.folder-edit-field{display:none}.edit-many-bulk-uploads__main .folder-edit-field,.edit-many__main .folder-edit-field{display:initial}h1,h2,h3,h4,h5,h6{font-family:var(--font-body);font-weight:500}h1{margin:0;font-size:32px;line-height:36px}}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default,payload;@layer payload-default{@media (max-width:768px){h1{letter-spacing:-.5px;font-size:25px}}h2{margin:0;font-size:26px;line-height:32px}@media (max-width:768px){h2{font-size:17px}}h3{margin:0;font-size:20px;line-height:24px}@media (max-width:768px){h3{font-size:13px;line-height:1.25}}h4{margin:0;font-size:16px;line-height:20px;letter-spacing:-.375px}h5{font-size:13px}h5,h6{margin:0;line-height:16px}h6{font-size:12px}html{font-size:13px;line-height:20px;font-weight:400}h1,h2,h3,h4,h5,h6,html{font-family:var(--font-body)}h1,h2,h3,h4,h5,h6{font-weight:500}h1{margin:0;font-size:32px;line-height:36px}}@layer payload-default;@layer payload-default{@media (max-width:768px){h1{letter-spacing:-.5px;font-size:25px}}h2{margin:0;font-size:26px;line-height:32px}@media (max-width:768px){h2{font-size:17px}}h3{margin:0;font-size:20px;line-height:24px}@media (max-width:768px){h3{font-size:13px;line-height:1.25}}h4{margin:0;font-size:16px;line-height:20px;letter-spacing:-.375px}h5{font-size:13px}h5,h6{margin:0;line-height:16px}h6{font-size:12px}html{font-size:13px;line-height:20px;font-weight:400;font-family:var(--font-body)}.payload-toast-container{--offset:calc(var(--gutter-h) / 2);padding:0;margin:0}.payload-toast-container .payload-toast-close-button{position:absolute;order:3;left:unset;inset-inline-end:16px;top:50%;transform:translateY(-50%);color:var(--theme-elevation-600);background:unset;border:none}.payload-toast-container .payload-toast-close-button svg{width:16px;height:16px}.payload-toast-container .payload-toast-close-button:hover{color:var(--theme-elevation-250);background:none}[dir=RTL] .payload-toast-container .payload-toast-close-button{right:unset;left:.5rem}.payload-toast-container .toast-title{line-height:20px;margin-right:20px}.payload-toast-container .payload-toast-item{padding:16px;color:var(--theme-elevation-800);font-style:normal;font-weight:600;display:flex;gap:1rem;align-items:center;width:100%;border-radius:4px;border:1px solid var(--theme-border-color);background:var(--theme-input-bg);box-shadow:0 10px 4px -8px #00020405,0 2px 3px #0002040d}.payload-toast-container .payload-toast-item .toast-content{transition:opacity .1s cubic-bezier(.55,.055,.675,.19);width:100%}.payload-toast-container .payload-toast-item[data-front=false] .toast-content{opacity:0}.payload-toast-container .payload-toast-item[data-expanded=true] .toast-content{opacity:1}.payload-toast-container .payload-toast-item .toast-icon{width:16px;height:16px;margin:0;display:flex;align-items:center;justify-content:center}.payload-toast-container .payload-toast-item .toast-icon>*{width:24px;height:24px}.payload-toast-container .payload-toast-item.toast-warning{color:var(--theme-warning-800);border-color:var(--theme-warning-250);background-color:var(--theme-warning-100)}.payload-toast-container .payload-toast-item.toast-warning .payload-toast-close-button{color:var(--theme-warning-600)}.payload-toast-container .payload-toast-item.toast-warning .payload-toast-close-button:hover{color:var(--theme-warning-250)}.payload-toast-container .payload-toast-item.toast-error{color:var(--theme-error-800);border-color:var(--theme-error-250);background-color:var(--theme-error-100)}.payload-toast-container .payload-toast-item.toast-error .payload-toast-close-button{color:var(--theme-error-600)}.payload-toast-container .payload-toast-item.toast-error .payload-toast-close-button:hover{color:var(--theme-error-250)}.payload-toast-container .payload-toast-item.toast-success{color:var(--theme-success-800);border-color:var(--theme-success-250);background-color:var(--theme-success-100)}.payload-toast-container .payload-toast-item.toast-success .payload-toast-close-button{color:var(--theme-success-600)}.payload-toast-container .payload-toast-item.toast-success .payload-toast-close-button:hover{color:var(--theme-success-250)}.payload-toast-container .payload-toast-item.toast-info{color:var(--theme-elevation-800);border-color:var(--theme-elevation-250);background-color:var(--theme-elevation-100)}.payload-toast-container .payload-toast-item.toast-info .payload-toast-close-button{color:var(--theme-elevation-600)}.payload-toast-container .payload-toast-item.toast-info .payload-toast-close-button:hover{color:var(--theme-elevation-250)}:root{--color-base-0:rgb(255,255,255);--color-base-50:rgb(245,245,245);--color-base-100:rgb(235,235,235);--color-base-150:rgb(221,221,221);--color-base-200:rgb(208,208,208);--color-base-250:rgb(195,195,195);--color-base-300:rgb(181,181,181);--color-base-350:rgb(168,168,168);--color-base-400:rgb(154,154,154);--color-base-450:rgb(141,141,141);--color-base-500:rgb(128,128,128);--color-base-550:rgb(114,114,114);--color-base-600:rgb(101,101,101);--color-base-650:rgb(87,87,87);--color-base-700:rgb(74,74,74);--color-base-750:rgb(60,60,60);--color-base-800:rgb(47,47,47);--color-base-850:rgb(34,34,34);--color-base-900:rgb(20,20,20);--color-base-950:rgb(7,7,7);--color-base-1000:rgb(0,0,0);--color-success-50:rgb(237,245,249);--color-success-100:rgb(218,237,248);--color-success-150:rgb(188,225,248);--color-success-200:rgb(156,216,253);--color-success-250:rgb(125,204,248);--color-success-300:rgb(97,190,241);--color-success-350:rgb(65,178,236);--color-success-400:rgb(36,164,223);--color-success-450:rgb(18,148,204);--color-success-500:rgb(21,135,186);--color-success-550:rgb(12,121,168);--color-success-600:rgb(11,110,153);--color-success-650:rgb(11,97,135);--color-success-700:rgb(17,88,121);--color-success-750:rgb(17,76,105);--color-success-800:rgb(18,66,90);--color-success-850:rgb(18,56,76);--color-success-900:rgb(19,44,58);--color-success-950:rgb(22,33,39);--color-error-50:rgb(250,241,240);--color-error-100:rgb(252,229,227);--color-error-150:rgb(247,208,204);--color-error-200:rgb(254,193,188);--color-error-250:rgb(253,177,170);--color-error-300:rgb(253,154,146);--color-error-350:rgb(253,131,123);--color-error-400:rgb(246,109,103);--color-error-450:rgb(234,90,86);--color-error-500:rgb(218,75,72);--color-error-550:rgb(200,62,61);--color-error-600:rgb(182,54,54);--color-error-650:rgb(161,47,47);--color-error-700:rgb(144,44,43);--color-error-750:rgb(123,41,39);--color-error-800:rgb(105,39,37);--color-error-850:rgb(86,36,33);--color-error-900:rgb(64,32,29);--color-error-950:rgb(44,26,24);--color-warning-50:rgb(249,242,237);--color-warning-100:rgb(248,232,219);--color-warning-150:rgb(243,212,186);--color-warning-200:rgb(243,200,162);--color-warning-250:rgb(240,185,136);--color-warning-300:rgb(238,166,98);--color-warning-350:rgb(234,148,58);--color-warning-400:rgb(223,132,17);--color-warning-450:rgb(204,120,15);--color-warning-500:rgb(185,108,13);--color-warning-550:rgb(167,97,10);--color-warning-600:rgb(150,87,11);--color-warning-650:rgb(134,78,11);--color-warning-700:rgb(120,70,13);--color-warning-750:rgb(105,61,13);--color-warning-800:rgb(90,55,19);--color-warning-850:rgb(73,47,21);--color-warning-900:rgb(56,38,20);--color-warning-950:rgb(38,29,21);--color-blue-50:rgb(237,245,249);--color-blue-100:rgb(218,237,248);--color-blue-150:rgb(188,225,248);--color-blue-200:rgb(156,216,253);--color-blue-250:rgb(125,204,248);--color-blue-300:rgb(97,190,241);--color-blue-350:rgb(65,178,236);--color-blue-400:rgb(36,164,223);--color-blue-450:rgb(18,148,204);--color-blue-500:rgb(21,135,186);--color-blue-550:rgb(12,121,168);--color-blue-600:rgb(11,110,153);--color-blue-650:rgb(11,97,135);--color-blue-700:rgb(17,88,121);--color-blue-750:rgb(17,76,105);--color-blue-800:rgb(18,66,90);--color-blue-850:rgb(18,56,76);--color-blue-900:rgb(19,44,58);--color-blue-950:rgb(22,33,39);--theme-border-color:var(--theme-elevation-150);--theme-success-50:var(--color-success-50);--theme-success-100:var(--color-success-100);--theme-success-150:var(--color-success-150);--theme-success-200:var(--color-success-200);--theme-success-250:var(--color-success-250);--theme-success-300:var(--color-success-300);--theme-success-350:var(--color-success-350);--theme-success-400:var(--color-success-400);--theme-success-450:var(--color-success-450);--theme-success-500:var(--color-success-500);--theme-success-550:var(--color-success-550);--theme-success-600:var(--color-success-600);--theme-success-650:var(--color-success-650);--theme-success-700:var(--color-success-700);--theme-success-750:var(--color-success-750);--theme-success-800:var(--color-success-800);--theme-success-850:var(--color-success-850);--theme-success-900:var(--color-success-900);--theme-success-950:var(--color-success-950);--theme-warning-50:var(--color-warning-50);--theme-warning-100:var(--color-warning-100);--theme-warning-150:var(--color-warning-150);--theme-warning-200:var(--color-warning-200);--theme-warning-250:var(--color-warning-250);--theme-warning-300:var(--color-warning-300);--theme-warning-350:var(--color-warning-350);--theme-warning-400:var(--color-warning-400);--theme-warning-450:var(--color-warning-450);--theme-warning-500:var(--color-warning-500);--theme-warning-550:var(--color-warning-550);--theme-warning-600:var(--color-warning-600);--theme-warning-650:var(--color-warning-650);--theme-warning-700:var(--color-warning-700);--theme-warning-750:var(--color-warning-750);--theme-warning-800:var(--color-warning-800);--theme-warning-850:var(--color-warning-850);--theme-warning-900:var(--color-warning-900);--theme-warning-950:var(--color-warning-950);--theme-error-50:var(--color-error-50);--theme-error-100:var(--color-error-100);--theme-error-150:var(--color-error-150);--theme-error-200:var(--color-error-200);--theme-error-250:var(--color-error-250);--theme-error-300:var(--color-error-300);--theme-error-350:var(--color-error-350);--theme-error-400:var(--color-error-400);--theme-error-450:var(--color-error-450);--theme-error-500:var(--color-error-500);--theme-error-550:var(--color-error-550);--theme-error-600:var(--color-error-600);--theme-error-650:var(--color-error-650);--theme-error-700:var(--color-error-700);--theme-error-750:var(--color-error-750);--theme-error-800:var(--color-error-800);--theme-error-850:var(--color-error-850);--theme-error-900:var(--color-error-900);--theme-error-950:var(--color-error-950);--theme-elevation-0:var(--color-base-0);--theme-elevation-50:var(--color-base-50);--theme-elevation-100:var(--color-base-100);--theme-elevation-150:var(--color-base-150);--theme-elevation-200:var(--color-base-200);--theme-elevation-250:var(--color-base-250);--theme-elevation-300:var(--color-base-300);--theme-elevation-350:var(--color-base-350);--theme-elevation-400:var(--color-base-400);--theme-elevation-450:var(--color-base-450);--theme-elevation-500:var(--color-base-500);--theme-elevation-550:var(--color-base-550);--theme-elevation-600:var(--color-base-600);--theme-elevation-650:var(--color-base-650);--theme-elevation-700:var(--color-base-700);--theme-elevation-750:var(--color-base-750);--theme-elevation-800:var(--color-base-800);--theme-elevation-850:var(--color-base-850);--theme-elevation-900:var(--color-base-900);--theme-elevation-950:var(--color-base-950);--theme-elevation-1000:var(--color-base-1000)}html[data-theme=dark]{--theme-border-color:var(--theme-elevation-150);--theme-elevation-0:var(--color-base-900);--theme-elevation-50:var(--color-base-850);--theme-elevation-100:var(--color-base-800);--theme-elevation-150:var(--color-base-750);--theme-elevation-200:var(--color-base-700);--theme-elevation-250:var(--color-base-650);--theme-elevation-300:var(--color-base-600);--theme-elevation-350:var(--color-base-550);--theme-elevation-400:var(--color-base-450);--theme-elevation-450:var(--color-base-400);--theme-elevation-550:var(--color-base-350);--theme-elevation-600:var(--color-base-300);--theme-elevation-650:var(--color-base-250);--theme-elevation-700:var(--color-base-200);--theme-elevation-750:var(--color-base-150);--theme-elevation-800:var(--color-base-100);--theme-elevation-850:var(--color-base-50);--theme-elevation-900:var(--color-base-0);--theme-elevation-950:var(--color-base-0);--theme-elevation-1000:var(--color-base-0);--theme-success-50:var(--color-success-950);--theme-success-100:var(--color-success-900);--theme-success-150:var(--color-success-850);--theme-success-200:var(--color-success-800);--theme-success-250:var(--color-success-750);--theme-success-300:var(--color-success-700);--theme-success-350:var(--color-success-650);--theme-success-400:var(--color-success-600);--theme-success-450:var(--color-success-550);--theme-success-550:var(--color-success-450);--theme-success-600:var(--color-success-400);--theme-success-650:var(--color-success-350);--theme-success-700:var(--color-success-300);--theme-success-750:var(--color-success-250);--theme-success-800:var(--color-success-200);--theme-success-850:var(--color-success-150);--theme-success-900:var(--color-success-100);--theme-success-950:var(--color-success-50);--theme-warning-50:var(--color-warning-950);--theme-warning-100:var(--color-warning-900);--theme-warning-150:var(--color-warning-850);--theme-warning-200:var(--color-warning-800);--theme-warning-250:var(--color-warning-750);--theme-warning-300:var(--color-warning-700);--theme-warning-350:var(--color-warning-650);--theme-warning-400:var(--color-warning-600);--theme-warning-450:var(--color-warning-550);--theme-warning-550:var(--color-warning-450);--theme-warning-600:var(--color-warning-400);--theme-warning-650:var(--color-warning-350);--theme-warning-700:var(--color-warning-300);--theme-warning-750:var(--color-warning-250);--theme-warning-800:var(--color-warning-200);--theme-warning-850:var(--color-warning-150);--theme-warning-900:var(--color-warning-100);--theme-warning-950:var(--color-warning-50);--theme-error-50:var(--color-error-950);--theme-error-100:var(--color-error-900);--theme-error-150:var(--color-error-850);--theme-error-200:var(--color-error-800);--theme-error-250:var(--color-error-750);--theme-error-300:var(--color-error-700);--theme-error-350:var(--color-error-650);--theme-error-400:var(--color-error-600);--theme-error-450:var(--color-error-550);--theme-error-550:var(--color-error-450);--theme-error-600:var(--color-error-400);--theme-error-650:var(--color-error-350);--theme-error-700:var(--color-error-300);--theme-error-750:var(--color-error-250);--theme-error-800:var(--color-error-200);--theme-error-850:var(--color-error-150);--theme-error-900:var(--color-error-100);--theme-error-950:var(--color-error-50)}:root{--base-px:20;--base-body-size:13;--base:calc((var(--base-px) / var(--base-body-size)) * 1rem);--breakpoint-xs-width:400px;--breakpoint-s-width:768px;--breakpoint-m-width:1024px;--breakpoint-l-width:1440px;--scrollbar-width:17px;--theme-bg:var(--theme-elevation-0);--theme-input-bg:var(--theme-elevation-0);--theme-text:var(--theme-elevation-800);--theme-overlay:rgba(5,5,5,.5);--theme-baseline:20px;--theme-baseline-body-size:13px;--font-body:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif;--font-serif:"Georgia","Bitstream Charter","Charis SIL",Utopia,"URW Bookman L",serif;--font-mono:"SF Mono",Menlo,Consolas,Monaco,monospace;--style-radius-s:3px;--style-radius-m:4px;--style-radius-l:8px;--z-popup:10;--z-nav:20;--z-modal:30;--z-status:40;--accessibility-outline:2px solid var(--theme-text);--accessibility-outline-offset:2px;--gutter-h:60px;--spacing-view-bottom:var(--gutter-h);--doc-controls-height:calc(var(--base) * 2.8);--app-header-height:calc(var(--base) * 2.8);--nav-width:275px;--nav-trans-time:.15s}}@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default{@media (max-width:1024px){:root{--gutter-h:40px;--app-header-height:calc(var(--base) * 2.4);--doc-controls-height:calc(var(--base) * 2.4)}}@media (max-width:768px){:root{--gutter-h:16px;--spacing-view-bottom:calc(var(--base) * 2);--nav-width:100vw}}*{box-sizing:border-box}html{background:var(--theme-bg);-webkit-font-smoothing:antialiased}html[data-theme=dark]{--theme-bg:var(--theme-elevation-0);--theme-text:var(--theme-elevation-1000);--theme-input-bg:var(--theme-elevation-50);--theme-overlay:rgba(5,5,5,.75);color-scheme:dark}html[data-theme=dark] ::selection{color:var(--color-base-1000)}html[data-theme=dark] ::-moz-selection{color:var(--color-base-1000)}@media (max-width:1024px){html{font-size:12px}}#app,body,html{height:100%}body{font-family:var(--font-body);font-weight:400;color:var(--theme-text);margin:0;overflow-x:hidden}::selection{background:var(--color-success-250);color:var(--theme-base-800)}::-moz-selection{background:var(--color-success-250);color:var(--theme-base-800)}img{max-width:100%;height:auto;display:block}ol,p,ul{margin:0}ol,ul{padding-left:1.5384615385rem}:focus-visible{outline:var(--accessibility-outline)}a{color:currentColor}a:focus{outline:none}a:focus:not(:focus-visible){opacity:.8}a:active{opacity:.7;outline:none}svg{vertical-align:middle}dialog{width:100%;border:0;padding:0;color:currentColor}.payload__modal-item{min-height:100%;background:transparent}.payload__modal-container--enterDone{overflow:auto}.payload__modal-item--enter,.payload__modal-item--enterDone{z-index:var(--z-modal)}button{font:var(--font-body)}.dashboard{width:100%;--gap:var(--base);--cols:5}.dashboard__wrap{padding-bottom:var(--spacing-view-bottom);display:flex;flex-direction:column;gap:var(--base)}.dashboard__group{display:flex;flex-direction:column;gap:var(--gap)}.dashboard__label{margin:0}.dashboard__card-list{padding:0;margin:0;list-style:none;grid-gap:var(--gap);gap:var(--gap);display:grid;grid-template-columns:repeat(var(--cols),1fr)}.dashboard__card-list .card{height:100%}.dashboard__locked.locked{align-items:unset;justify-content:unset}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1440px){.dashboard{--cols:4}}@media (max-width:1024px){.dashboard{--gap:var(--base);--cols:2}}@media (max-width:768px){.dashboard{--cols:2}.dashboard__wrap{gap:var(--base)}.dashboard__card-list{gap:8px}}@media (max-width:400px){.dashboard{--cols:1}}}@layer payload-default{.login__form__inputWrap{display:flex;flex-direction:column;gap:var(--base);margin-bottom:calc(var(--base) / 4)}.login__brand{display:flex;justify-content:center;width:100%;margin-bottom:calc(var(--base) * 2)}.template-default{background-color:var(--theme-bg);color:var(--theme-text)}[dir=rtl] .template-default__nav-toggler-wrapper{left:unset;right:0}.template-default__nav-toggler-wrapper{position:-webkit-sticky;position:sticky;z-index:var(--z-modal);top:0;left:0;height:0;width:var(--gutter-h);display:flex;justify-content:center}.template-default__nav-toggler-container{height:var(--app-header-height);display:flex;align-items:center}.template-default__nav-toggler{display:flex;align-items:center}.template-default__wrap{min-width:0;width:100%;flex-grow:1;position:relative;background-color:var(--theme-bg)}.template-default__wrap:before{content:"";display:block;position:absolute;inset:0;background-color:inherit;opacity:0;z-index:var(--z-status);visibility:hidden;transition:all var(--nav-trans-time) linear}}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.template-default__nav-toggler-wrapper .hamburger{left:unset}}@media (max-width:768px){.template-default__nav-toggler-wrapper{width:unset;justify-content:unset}.template-default__nav-toggler-wrapper .hamburger{display:none}.template-default .template-default__wrap{min-width:100%}}}@layer payload-default;@layer payload-default;@layer payload-default{.nav{position:-webkit-sticky;position:sticky;top:0;left:0;flex-shrink:0;height:100vh;width:var(--nav-width);border-right:1px solid var(--theme-elevation-100);opacity:0}[dir=rtl] .nav{border-right:none;border-left:1px solid var(--theme-elevation-100)}.nav--nav-animate{transition:opacity var(--nav-trans-time) ease-in-out}.nav--nav-open{opacity:1}.nav{position:-webkit-sticky;position:sticky;top:0;left:0;flex-shrink:0;height:100vh;width:var(--nav-width);border-right:1px solid var(--theme-elevation-100);opacity:0;overflow:hidden}[dir=rtl] .nav{border-right:none;border-left:1px solid var(--theme-elevation-100)}.nav--nav-animate{transition:opacity var(--nav-trans-time) ease-in-out}.nav--nav-open{opacity:1}.nav__header{position:absolute;top:0;width:100vw;height:var(--app-header-height)}.nav__header-content{z-index:1;position:relative;height:100%;width:100%}.nav__mobile-close{display:none;background:none;border:0;outline:0;padding:16px 0}.nav__scroll{height:100%;display:flex;flex-direction:column;padding:var(--app-header-height) 20px 40px 20px;overflow-y:auto}}@layer payload-default;@layer payload-default;@layer payload-default{.nav__scroll::-webkit-scrollbar{display:none}.nav__wrap{width:100%;display:flex;flex-direction:column;align-items:flex-start;flex-grow:1}.nav__label{color:var(--theme-elevation-400)}.nav__controls{margin-top:auto;margin-bottom:0}.nav__controls>*{margin-top:20px}.nav__controls a:focus-visible{outline:var(--accessibility-outline)}.nav__log-out:hover g{transform:translate(-2.5px)}.nav__link{display:flex;align-items:center;position:relative;padding-block:2.5px;-webkit-padding-start:0;padding-inline-start:0;-webkit-padding-end:30px;padding-inline-end:30px;text-decoration:none}.nav__link:focus:not(:focus-visible){box-shadow:none;font-weight:600}.nav__link.active{font-weight:400;padding-left:0;font-weight:600}.nav a.nav__link:focus-visible,.nav a.nav__link:hover{text-decoration:underline}.nav__link:has(.nav__link-indicator){font-weight:600;padding-left:0}.nav__link-indicator{position:absolute;display:block;inset-inline-start:-20px;width:2px;height:16px;border-start-end-radius:2px;border-end-end-radius:2px;background:var(--theme-text)}@media (max-width:1024px){.nav__scroll{padding:var(--app-header-height) 10px 40px}}@media (max-width:768px){.nav__scroll{padding:var(--app-header-height) var(--gutter-h) 40px}.nav__link{font-size:17.5px;line-height:30px}.nav__mobile-close{display:flex;align-items:center}}}@layer payload-default;@layer payload-default;@layer payload-default{.template-default{min-height:100vh;display:grid;position:relative;isolation:isolate}@media (prefers-reduced-motion){.template-default{transition:none}}.template-default--nav-animate{transition:grid-template-columns var(--nav-trans-time) linear}.template-default--nav-open .template-default__nav-overlay{transition:opacity var(--nav-trans-time) linear}@media (min-width:1441px){.template-default{grid-template-columns:0 auto}.template-default--nav-open{grid-template-columns:var(--nav-width) auto}}@media (max-width:1440px){.template-default--nav-hydrated.template-default--nav-open{grid-template-columns:var(--nav-width) auto}.template-default{grid-template-columns:1fr auto}.template-default .nav{display:none}.template-default--nav-hydrated{grid-template-columns:0 auto}.template-default--nav-hydrated .nav{display:unset}}}@layer payload-default;@layer payload-default;@layer payload-default{.template-minimal{display:flex;width:100%;justify-content:center;align-items:center;padding:60px 1.5384615385rem;margin-left:auto;margin-right:auto;min-height:100%;background-color:var(--theme-bg-color);color:var(--theme-text)}.template-minimal--width-normal .template-minimal__wrap{max-width:480px;width:100%}.template-minimal--width-wide .template-minimal__wrap{max-width:960px;width:100%}.doc-tab{display:flex;justify-content:center;align-items:center;white-space:nowrap}.doc-tab:hover .pill-version-count{background-color:var(--theme-elevation-150)}.doc-tab--active .pill-version-count,.doc-tab--active:hover .pill-version-count{background-color:var(--theme-elevation-250)}.doc-tab__label{display:flex;position:relative;align-items:center;gap:4px;width:100%;height:100%;line-height:calc(var(--base) * 1.2)}.pill-version-count{line-height:calc(var(--base) * .8);min-width:calc(var(--base) * .8);text-align:center;background-color:var(--theme-elevation-100);border-radius:var(--style-radius-s)}.doc-tabs{display:flex}.doc-tabs__tabs{display:flex;gap:calc(var(--base) / 2);list-style:none;align-items:center;margin:0;padding-left:0}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.doc-tabs{width:100%;padding:0;overflow:auto}.doc-tabs::-webkit-scrollbar{display:none}.doc-tabs:after{content:"";display:block;position:-webkit-sticky;position:sticky;right:0;width:calc(var(--base) * 2);height:calc(var(--base) * 2);background:linear-gradient(to right,transparent,var(--theme-bg));flex-shrink:0;z-index:1111;pointer-events:none}.doc-tabs__tabs{padding:0}}@media (max-width:768px){.doc-tabs__tabs-container{margin-right:var(--gutter-h)}.doc-tabs__tabs{gap:var(--gutter-h)}}}@layer payload-default;@layer payload-default;@layer payload-default{.doc-header{width:100%;margin-top:8px;padding-bottom:calc(var(--base) * 1.2);align-items:center;position:relative;display:flex;gap:calc(var(--base) / 2)}.doc-header:after{content:"";display:block;position:absolute;height:1px;background:var(--theme-elevation-100);width:100%;left:0;top:calc(100% - 1px)}.doc-header__title{flex-grow:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin:0;padding-bottom:8px;line-height:1;vertical-align:top}@media (max-width:1024px){.doc-header{margin-top:5px;flex-direction:column;gap:calc(var(--base) / 2);padding-bottom:calc(var(--base) / 2)}.doc-header__title{width:100%}}@media (max-width:768px){.doc-header{margin-top:0}}}@layer payload-default;@layer payload-default;@layer payload-default{.payload-settings{position:relative;margin-bottom:calc(var(--base) * 2);margin-top:calc(var(--base) * 3);padding-top:calc(var(--base) * 3);padding-bottom:calc(var(--base) * 3);display:flex;flex-direction:column;gap:var(--base)}.payload-settings h3{margin:0}.payload-settings:after,.payload-settings:before{content:"";display:block;height:1px;background:var(--theme-elevation-100);width:calc(100% + var(--base) * 5);left:calc(var(--gutter-h) * -1);top:0;position:absolute}.payload-settings:after{display:none;bottom:0;top:unset}@media (max-width:1024px){.payload-settings{margin-bottom:var(--base);padding-top:calc(var(--base) * 2);margin-top:calc(var(--base) * 2);padding-bottom:calc(var(--base) * 2)}.payload-settings:after{display:block}}}@layer payload-default;@layer payload-default;@layer payload-default{.create-first-user{display:flex;flex-direction:column;gap:8px}.create-first-user>form>.field-type{margin-bottom:var(--base)}.create-first-user>form>.field-type .form-submit{margin:0}.emailAndUsername{margin-bottom:var(--base)}.not-found{margin-top:var(--base);display:flex}.not-found>:first-child{margin-top:0}.not-found>:last-child{margin-bottom:0}.not-found__wrap{display:flex;flex-direction:column;align-items:flex-start;gap:16px;max-width:720px}.not-found__content{display:flex;flex-direction:column;gap:8px}.not-found__button,.not-found__content>*{margin:0}.not-found--margin-top-large{margin-top:calc(var(--base) * 2)}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1440px){.not-found--margin-top-large{margin-top:var(--base)}}@media (max-width:768px){.not-found,.not-found--margin-top-large{margin-top:calc(var(--base) / 2)}}}@layer payload-default;@layer payload-default;@layer payload-default{.query-inspector{--string-color:var(--color-success-450);--number-color:var(--color-warning-450);display:flex;gap:calc(var(--base) * 2);align-items:flex-start}.query-inspector ul{padding-left:calc(var(--base) * 1)}.query-inspector--fullscreen{padding-left:0}.query-inspector--fullscreen .query-inspector__configuration{display:none}.query-inspector__configuration{margin-top:calc(var(--base) * 2);width:60%;position:-webkit-sticky;position:sticky;top:var(--base)}.query-inspector__api-url{margin-bottom:calc(var(--base) * 1.5)}.query-inspector__api-url a{display:block;overflow:hidden;text-overflow:ellipsis;text-decoration:none}.query-inspector__api-url a:focus-visible,.query-inspector__api-url a:hover{text-decoration:underline}.query-inspector__form-fields{display:flex;flex-direction:column;gap:var(--base)}.query-inspector__label{color:var(--theme-elevation-400)}.query-inspector__filter-query-checkboxes{display:flex;gap:var(--base)}.query-inspector__results-wrapper{font-family:var(--font-mono);width:100%}.query-inspector__results-wrapper ul{margin:0}.query-inspector__results-wrapper li{list-style:none}.query-inspector__toggle-fullscreen-button-container{position:-webkit-sticky;position:sticky;top:0;z-index:1}@media (max-width:1024px){.query-inspector__toggle-fullscreen-button-container{display:none}}.query-inspector__toggle-fullscreen-button{position:absolute;right:calc(var(--base) * .5);top:calc(var(--base) * .5);padding:calc(var(--base) * .25);background-color:var(--theme-elevation-0);cursor:pointer;z-index:1;margin:0;border:0;border-radius:3px;color:var(--theme-elevation-300)}.query-inspector__toggle-fullscreen-button:hover{color:var(--theme-elevation-700)}.query-inspector__results{padding-top:calc(var(--base) * .5);padding-left:calc(var(--base) * .5);padding-bottom:calc(var(--base) * .5);background-color:var(--theme-elevation-50);overflow:auto;min-height:100vh}@media (max-width:1024px){.query-inspector{flex-direction:column;padding-left:0}.query-inspector .query-inspector__configuration{position:relative;width:100%;top:0;-webkit-padding-end:var(--gutter-h);padding-inline-end:var(--gutter-h)}}}@layer payload-default;@layer payload-default;@layer payload-default{.query-inspector__json-children{position:relative}.query-inspector__json-children--nested li{padding-left:16px}.query-inspector__json-children:before{content:"";position:absolute;top:0;width:1px;height:100%;border-left:1px dashed var(--theme-elevation-200)}.query-inspector__list-wrap{position:relative}.query-inspector__list-toggle{all:unset;width:100%;text-align:left;cursor:pointer;border-radius:3px 0 0 3px;position:relative;display:flex;gap:10px;align-items:center;left:-3px;width:calc(100% + 3px)}.query-inspector__list-toggle svg .stroke{stroke:var(--theme-elevation-400)}.query-inspector__list-toggle:hover{background-color:var(--theme-elevation-100)}.query-inspector__list-toggle--empty{cursor:default;pointer-events:none}.query-inspector__toggle-row-icon--open{transform:rotate(0)}.query-inspector__toggle-row-icon--closed{transform:rotate(-90deg)}.query-inspector__value-type--number .query-inspector__value{color:var(--number-color)}.query-inspector__value-type--string .query-inspector__value{color:var(--string-color)}.query-inspector__bracket{position:relative}.query-inspector__bracket--nested{margin-left:16px}.query-inspector__bracket--position-end{left:1px;width:calc(100% - 5px)}.form-header{display:flex;flex-direction:column;gap:calc(var(--base) * .5);margin-bottom:var(--base)}.unauthorized__button.btn{margin:0;margin-block:0}.unauthorized--with-gutter{margin-top:var(--base)}.restore-version__modal__toggle{border:0;background:none;box-shadow:none;border-radius:0;padding:0;color:currentColor;font-family:var(--font-body)}.restore-version{cursor:pointer;display:flex;min-width:max-content}.restore-version .popup-button{display:flex}.restore-version__chevron{background-color:var(--theme-elevation-150);border-top-left-radius:0;border-bottom-left-radius:0;cursor:pointer}.restore-version__chevron .stroke{stroke-width:1px}.restore-version__chevron:hover{background:var(--theme-elevation-100)}.restore-version .btn{margin-block:0}.restore-version__restore-as-draft-button{border-top-right-radius:0;border-bottom-right-radius:0;margin-right:2px}.restore-version__restore-as-draft-button:focus{border-radius:0;outline-offset:0}.restore-version__modal{display:flex;align-items:center;justify-content:center;height:100%}.restore-version__modal:after,.restore-version__modal:before{content:" ";position:absolute;inset:0}.restore-version__modal:before{background:var(--theme-bg);opacity:.75}.restore-version__modal:after{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}.restore-version__wrapper{z-index:1;position:relative;display:flex;flex-direction:column;gap:16px;padding:40px;max-width:720px}.restore-version__content{display:flex;flex-direction:column;gap:8px}.restore-version__content>*{margin:0}.restore-version__controls{display:flex;gap:8px}.restore-version__controls .btn{margin:0}.view-version{width:100%;padding-bottom:var(--spacing-view-bottom)}.view-version__toggle-locales-label{color:var(--theme-elevation-500)}.view-version-controls-top{border-bottom:1px solid var(--theme-elevation-100);padding:16px var(--gutter-h) 16px var(--gutter-h)}.view-version-controls-top__wrapper{display:flex;flex-direction:row;justify-content:space-between;align-items:center}.view-version-controls-top__wrapper-actions{display:flex;flex-direction:row;align-items:center;gap:var(--base)}.view-version-controls-top h2{font-size:18px}.view-version-controls-bottom{border-bottom:1px solid var(--theme-elevation-100);padding:16px var(--gutter-h) 16px var(--gutter-h);position:relative}.view-version-controls-bottom:after{content:"";position:absolute;top:0;bottom:0;left:50%;width:1px;background-color:var(--theme-elevation-100);transform:translate(-50%)}.view-version-controls-bottom__wrapper{display:grid;grid-template-columns:1fr 1fr;grid-gap:var(--base);gap:var(--base)}.view-version__time-elapsed{color:var(--theme-elevation-500)}.view-version__version-from{display:flex;flex-direction:column;gap:5px}.view-version__version-from-labels{display:flex;flex-direction:row;justify-content:space-between}.view-version__version-to{display:flex;flex-direction:column;gap:5px}.view-version__version-to-labels,.view-version__version-to-version{display:flex;flex-direction:row;justify-content:space-between}.view-version__version-to-version{align-items:center;background:var(--theme-elevation-50);padding:8px 12px;gap:calc(var(--base) / 2)}.view-version__version-to-version h2{font-size:13px;font-weight:400}.view-version__restore div{margin-block:0}.view-version__modifiedCheckBox{margin:0 0 0 var(--base);display:flex;align-items:center}.view-version__diff-wrap{padding-top:var(--base);display:flex;flex-direction:column;gap:var(--base);position:relative}.view-version__diff-wrap:after{content:"";position:absolute;top:0;bottom:0;left:50%;width:1px;background-color:var(--theme-elevation-100);transform:translate(-50%);z-index:2}}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.view-version__version-to-version{flex-direction:column;align-items:flex-start}}@media (max-width:768px){.view-version__diff-wrap{padding-top:calc(var(--base) / 2)}.view-version-controls-top__wrapper,.view-version__version-from-labels,.view-version__version-to-labels{flex-direction:column;align-items:flex-start}.view-version-controls-top__wrapper .view-version__modifiedCheckBox{margin-left:0}}}@layer payload-default;@layer payload-default;@layer payload-default{.compare-version-moreVersions{color:var(--theme-elevation-500)}.version-drawer .table{width:100%}.version-drawer .created-at-cell{background:none;border:none;cursor:pointer;padding:0;text-decoration:underline}.diff-collapser__toggle-button{all:unset;cursor:pointer;position:relative;z-index:1;display:flex;align-items:center}.diff-collapser__toggle-button .icon{color:var(--theme-elevation-500)}.diff-collapser__toggle-button:hover:before{content:"";position:absolute;inset:-3px;background-color:var(--theme-elevation-50);border-radius:var(--style-radius-s);z-index:-1}.diff-collapser__toggle-button:hover .iterable-diff__label{background-color:var(--theme-elevation-50);z-index:1}.diff-collapser__label{margin:0 calc(var(--base) * .3) 0 0;display:inline-flex;height:100%}.diff-collapser__field-change-count{font-weight:400;margin-left:calc(var(--base) * .3);padding:calc(var(--base) * .1) calc(var(--base) * .2);background:var(--theme-elevation-100);border-radius:var(--style-radius-s);font-size:.8rem}[dir=ltr] .diff-collapser__content:not(.diff-collapser__content--hide-gutter){border-left:2px solid var(--theme-elevation-100);margin-left:3px;padding-left:calc(var(--base) * .5)}[dir=rtl] .diff-collapser__content:not(.diff-collapser__content--hide-gutter){border-right:2px solid var(--theme-elevation-100);margin-right:3px;padding-right:calc(var(--base) * .5)}.diff-collapser__content--is-collapsed{display:none}.render-field-diffs{display:flex;flex-direction:column;gap:var(--base)}.render-field-diffs [role=banner]{display:none!important}.render-field-diffs__field{overflow-wrap:anywhere;display:flex;flex-direction:column;gap:var(--base)}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:768px){.render-field-diffs{gap:calc(var(--base) / 2)}}}@layer payload-default{.date-diff p [data-match-type=create],.date-diff p [data-match-type=delete]{color:unset!important;background-color:unset!important}.iterable-diff-label-container{position:relative;height:20px;display:flex;flex-direction:row;height:100%}.iterable-diff-label-prefix{background-color:var(--theme-bg);position:relative;width:calc(var(--base) * .5);height:16px;margin-left:calc(var(--base) * -.5 - 5px);margin-right:calc(var(--base) * .5)}.iterable-diff-label-prefix:before{content:"";position:absolute;left:1px;top:8px;transform:translateY(-50%);width:6px;height:6px;background-color:var(--theme-elevation-200);border-radius:50%;margin-right:5px}.iterable-diff__label{font-weight:400;color:var(--theme-elevation-600)}.iterable-diff__locale-label{background:var(--theme-elevation-100);border-radius:var(--style-radius-s);padding:calc(var(--base) * .2)}[dir=ltr] .iterable-diff__locale-label{margin-right:calc(var(--base) * .25)}[dir=rtl] .iterable-diff__locale-label{margin-left:calc(var(--base) * .25)}.iterable-diff__row:not(:first-of-type){margin-top:calc(var(--base) * .5)}.iterable-diff__no-rows{color:var(--theme-elevation-400)}.relationship-diff{font-size:13px;line-height:20px;font-weight:400;font-family:var(--font-body)}.relationship-diff-container .field-diff-content{padding:0;background:unset}.relationship-diff-container--hasOne .relationship-diff{min-width:100%;max-width:-moz-fit-content;max-width:fit-content}.relationship-diff-container--hasMany .field-diff-content{background:var(--theme-elevation-50);padding:10px}.relationship-diff-container--hasMany .field-diff-content .html-diff{display:flex;min-width:0;max-width:max-content;flex-wrap:wrap;gap:calc(var(--base) * .5)}.relationship-diff-container--hasMany .field-diff-content .relationship-diff{padding:calc(var(--base) * .15) calc(var(--base) * .3)}.relationship-diff{display:flex;align-items:center;border-radius:3px;border:1px solid var(--theme-elevation-150);position:relative;font-family:var(--font-body);max-height:calc(var(--base) * 3);padding:calc(var(--base) * .35)}.relationship-diff[data-match-type=create]{border-color:var(--diff-create-pill-border);color:var(--diff-create-parent-color)}.relationship-diff[data-match-type=create] *{color:var(--diff-create-parent-color)}.relationship-diff[data-match-type=delete]{border-color:var(--diff-delete-pill-border);color:var(--diff-delete-parent-color);background-color:var(--diff-delete-pill-bg);-webkit-text-decoration-line:none!important;text-decoration-line:none!important}.relationship-diff[data-match-type=delete] *{color:var(--diff-delete-parent-color);-webkit-text-decoration-line:none;text-decoration-line:none}.relationship-diff[data-match-type=delete] .relationship-diff__info{-webkit-text-decoration-line:line-through;text-decoration-line:line-through}.relationship-diff__info{font-weight:500}.relationship-diff__pill{border-radius:3px;margin:0 calc(var(--base) * .4) 0 calc(var(--base) * .2);padding:0 calc(var(--base) * .1);background-color:var(--theme-elevation-150);color:var(--theme-elevation-750)}.relationship-diff[data-match-type=create] .relationship-diff__pill{background-color:var(--diff-create-parent-bg);color:var(--diff-create-pill-color)}.relationship-diff[data-match-type=delete] .relationship-diff__pill{background-color:var(--diff-delete-parent-bg);color:var(--diff-delete-pill-color)}.tabs-diff__tab-locale:not(:first-of-type),.tabs-diff__tab:not(:first-of-type){margin-top:var(--base)}.upload-diff{font-size:13px;line-height:20px;font-weight:400;font-family:var(--font-body)}.upload-diff-container .field-diff-content{padding:0;background:unset}.upload-diff-hasMany{display:flex;flex-direction:column;gap:calc(var(--base) * .4)}.upload-diff{min-width:100%;max-width:-moz-fit-content;max-width:fit-content;display:flex;align-items:center;background-color:var(--theme-elevation-50);border-radius:3px;border:1px solid var(--theme-elevation-150);position:relative;font-family:var(--font-body);max-height:calc(var(--base) * 3);padding:calc(var(--base) * .1)}.upload-diff[data-match-type=create]{border-color:var(--diff-create-pill-border)}.upload-diff[data-match-type=create],.upload-diff[data-match-type=create] *{color:var(--diff-create-parent-color)}.upload-diff[data-match-type=create] .upload-diff__thumbnail{border-radius:0;border-color:var(--diff-create-pill-border);background-color:none}.upload-diff[data-match-type=delete]{border-color:var(--diff-delete-pill-border);background-color:var(--diff-delete-pill-bg)}.upload-diff[data-match-type=delete],.upload-diff[data-match-type=delete] *{-webkit-text-decoration-line:none;text-decoration-line:none;color:var(--diff-delete-parent-color)}.upload-diff[data-match-type=delete] .upload-diff__thumbnail{border-radius:0;border-color:var(--diff-delete-pill-border);background-color:none}.upload-diff__card{display:flex;flex-direction:row;align-items:center;width:100%}.upload-diff__thumbnail{width:calc(var(--base) * 3 - 32px);height:calc(var(--base) * 3 - 32px);position:relative;overflow:hidden;flex-shrink:0;border-radius:0;border:1px solid var(--theme-elevation-100)}.upload-diff__thumbnail img,.upload-diff__thumbnail svg{position:absolute;object-fit:cover;width:100%;height:100%;border-radius:0}.upload-diff__info{flex-grow:1;display:flex;align-items:flex-start;flex-direction:column;padding:calc(var(--base) * .25) calc(var(--base) * .6);justify-content:space-between;font-weight:400}.upload-diff__info strong{font-weight:500}.upload-diff__pill{border-radius:3px;margin-left:calc(var(--base) * .6);padding:0 calc(var(--base) * .1);background-color:var(--theme-elevation-150);color:var(--theme-elevation-750)}.upload-diff[data-match-type=create] .upload-diff__pill{background-color:var(--diff-create-parent-bg);color:var(--diff-create-pill-color)}.upload-diff[data-match-type=delete] .upload-diff__pill{background-color:var(--diff-delete-parent-bg);color:var(--diff-delete-pill-color)}.version-pill-label{display:flex;align-items:center;gap:calc(var(--base) / 2)}.version-pill-label-text{font-weight:500}.version-pill-label-date{color:var(--theme-elevation-500)}}@layer payload-default;@layer payload-default{}@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default{}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:768px){.version-pill-label{flex-direction:column;align-items:flex-start;gap:0}}}@layer payload-default{.autosave-cell__items{display:flex;align-items:center;gap:calc(var(--base) * .5)}.versions{width:100%;margin-bottom:calc(var(--base) * 2)}.versions__wrap{padding-top:0;padding-bottom:var(--spacing-view-bottom);margin-top:calc(var(--base) * .75)}.versions__header{margin-bottom:var(--base)}.versions__no-versions{margin-top:calc(var(--base) * 1.5)}.versions__parent-doc .banner__content{display:flex}[dir=ltr] .versions__parent-doc-pills{margin-left:auto}[dir=rtl] .versions__parent-doc-pills{margin-right:auto}.versions .table table{width:100%;overflow:auto}.versions__page-controls{width:100%;display:flex;align-items:center}.versions .paginator{margin-bottom:0}[dir=ltr] .versions__page-info{margin-right:var(--base);margin-left:auto}[dir=rtl] .versions__page-info{margin-left:var(--base);margin-right:auto}}@layer payload-default;@layer payload-default;@layer payload-default{@media (max-width:1024px){.versions__wrap{padding-top:0;margin-top:0}.versions .table{display:flex;width:calc(100% + var(--gutter-h) * 2);max-width:unset;left:calc(var(--gutter-h) * -1);position:relative;padding-left:var(--gutter-h)}.versions .table:after{content:"";height:1px;padding-right:var(--gutter-h)}.versions__page-controls{flex-wrap:wrap}[dir=ltr] .versions__page-info{margin-left:0}[dir=rtl] .versions__page-info{margin-right:0}.versions .paginator{width:100%;margin-bottom:var(--base)}}}@layer payload-default;@layer payload-default;@layer payload-default{.logout{align-items:center;flex-wrap:wrap}.logout,.logout__wrap{display:flex;flex-direction:column}.logout__wrap{z-index:1;position:relative;align-items:flex-start;gap:16px;width:100%;max-width:720px}.logout__wrap>*{margin:0}.reset-password__wrap .inputWrap{display:flex;flex-direction:column;gap:16px}.verify{display:flex;align-items:center;text-align:center;flex-wrap:wrap;min-height:100vh}.verify__brand{display:flex;justify-content:center;width:100%;margin-bottom:calc(var(--base) * 2)}}@layer payload-default;@layer payload-default;@layer payload-default{}@layer payload-default{}