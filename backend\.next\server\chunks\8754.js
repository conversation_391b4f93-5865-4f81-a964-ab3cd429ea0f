exports.id=8754,exports.ids=[8754],exports.modules={1022:(e,t,a)=>{"use strict";a.d(t,{y:()=>l});let l={slug:"payments",admin:{useAsTitle:"paymentNumber",defaultColumns:["paymentNumber","bill","patient","amount","paymentMethod","paymentStatus"],listSearchableFields:["paymentNumber","bill.billNumber","patient.fullName"]},access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role||"doctor"===e.role&&{or:[{"bill.appointment.practitioner":{equals:e.id}},{receivedBy:{equals:e.id}}]}),create:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),delete:({req:{user:e}})=>!!e&&"admin"===e.role},hooks:{beforeChange:[async({data:e,req:t,operation:a})=>{if(!e.paymentNumber){let t=new Date,a=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),n=t.getTime().toString().slice(-6);e.paymentNumber=`PAY-${a}${l}${i}-${n}`}if(!e.receiptNumber&&"completed"===e.paymentStatus){let t=new Date,a=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0");e.receiptNumber=`REC-${a}${l}${e.paymentNumber.slice(-6)}`}if("create"===a&&e.bill&&e.amount)try{let a="object"==typeof e.bill?e.bill.id:e.bill,l=await t.payload.findByID({collection:"bills",id:a});if(l){let t=l.remainingAmount||l.totalAmount;if(e.amount>t)throw Error(`Payment amount (${e.amount}) cannot exceed remaining bill balance (${t})`)}}catch(e){if(e.message?.includes("Payment amount"))throw e}return e}],afterChange:[async({doc:e,req:t,operation:a})=>{if(e.bill&&"completed"===e.paymentStatus)try{let a="object"==typeof e.bill?e.bill.id:e.bill,l=new Promise((e,t)=>{setTimeout(()=>t(Error("Hook timeout")),5e3)}),i=(async()=>{let e=await t.payload.findByID({collection:"bills",id:a});if(e){let l=(await t.payload.find({collection:"payments",where:{bill:{equals:a},paymentStatus:{equals:"completed"}},limit:1e3})).docs.reduce((e,t)=>e+t.amount,0),i=e.totalAmount-l,n=e.status;i<=0?n="paid":l>0&&"confirmed"===e.status&&(n="confirmed"),await t.payload.update({collection:"bills",id:a,data:{paidAmount:l,remainingAmount:i,status:n}})}})();await Promise.race([i,l])}catch(e){console.error("Error updating bill after payment:",e)}}]},fields:[{name:"paymentNumber",type:"text",required:!0,unique:!0,label:"支付编号",admin:{description:"系统自动生成，格式：PAY-YYYYMMDD-XXXXXX",readOnly:!0}},{name:"bill",type:"relationship",relationTo:"bills",required:!0,hasMany:!1,label:"关联账单"},{name:"patient",type:"relationship",relationTo:"patients",required:!0,hasMany:!1,label:"患者"},{name:"amount",type:"number",required:!0,label:"支付金额",min:.01,admin:{description:"本次支付的金额"}},{name:"paymentMethod",type:"select",required:!0,options:[{label:"现金",value:"cash"},{label:"银行卡",value:"card"},{label:"微信支付",value:"wechat"},{label:"支付宝",value:"alipay"},{label:"银行转账",value:"transfer"},{label:"分期付款",value:"installment"}],label:"支付方式"},{name:"paymentStatus",type:"select",required:!0,options:[{label:"待处理",value:"pending"},{label:"已完成",value:"completed"},{label:"失败",value:"failed"},{label:"已退款",value:"refunded"}],defaultValue:"pending",label:"支付状态"},{name:"transactionId",type:"text",label:"交易ID",admin:{description:"第三方支付平台的交易ID（如适用）"}},{name:"paymentDate",type:"date",required:!0,label:"支付日期",defaultValue:()=>new Date().toISOString(),admin:{date:{pickerAppearance:"dayAndTime"}}},{name:"receivedBy",type:"relationship",relationTo:"users",required:!0,hasMany:!1,label:"收款人员",admin:{description:"处理此次支付的工作人员"}},{name:"relatedDeposit",type:"relationship",relationTo:"deposits",hasMany:!1,label:"关联押金",admin:{description:"如果此支付来自押金抵扣，请选择对应押金",condition:e=>"deposit"===e.paymentMethod}},{name:"notes",type:"textarea",label:"支付备注",admin:{description:"支付相关的备注信息"}},{name:"receiptNumber",type:"text",label:"收据编号",admin:{description:"收据编号（系统自动生成）",readOnly:!0}}]}},2377:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});let l={slug:"media",access:{read:()=>!0},fields:[{name:"alt",type:"text",required:!0}],upload:!0}},2750:(e,t,a)=>{"use strict";a.d(t,{m:()=>l});let l={slug:"bill-items",admin:{useAsTitle:"itemName",defaultColumns:["bill","itemName","quantity","unitPrice","totalPrice"],listSearchableFields:["itemName","description"]},access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role||"doctor"===e.role&&{or:[{"bill.appointment.practitioner":{equals:e.id}},{"bill.createdBy":{equals:e.id}}]}),create:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),delete:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role)},hooks:{beforeChange:[({data:e})=>{if(void 0!==e.quantity&&e.quantity<=0)throw Error("数量必须大于0");if(void 0!==e.unitPrice&&e.unitPrice<0)throw Error("单价不能为负数");if(void 0!==e.discountRate&&(e.discountRate<0||e.discountRate>100))throw Error("折扣率必须在0-100之间");let t=e.quantity||0,a=e.unitPrice||0,l=e.discountRate||0,i=t*a;return e.totalPrice=i-l/100*i,e}],afterChange:[async({doc:e,req:t,operation:a})=>{if(e.bill&&("create"===a||"update"===a))try{let a=new Promise((e,t)=>{setTimeout(()=>t(Error("Hook timeout")),5e3)}),l=(async()=>{let a="object"==typeof e.bill?e.bill.id:e.bill,l=(await t.payload.find({collection:"bill-items",where:{bill:{equals:a}},limit:1e3})).docs.reduce((e,t)=>e+(t.totalPrice||0),0),i=await t.payload.findByID({collection:"bills",id:a});if(i){let e=i.discountAmount||0,n=i.taxAmount||0,r=l-e+n,o=i.paidAmount||0;await t.payload.update({collection:"bills",id:a,data:{subtotal:l,totalAmount:r,remainingAmount:r-o}})}})();await Promise.race([l,a])}catch(e){console.error("Error in BillItems afterChange hook:",e)}}]},fields:[{name:"bill",type:"relationship",relationTo:"bills",required:!0,hasMany:!1,label:"所属账单"},{name:"itemType",type:"select",required:!0,options:[{label:"治疗项目",value:"treatment"},{label:"咨询服务",value:"consultation"},{label:"材料费用",value:"material"},{label:"其他服务",value:"service"}],defaultValue:"treatment",label:"项目类型"},{name:"itemId",type:"text",label:"项目ID",admin:{description:"关联的治疗或服务的ID（可选）"}},{name:"itemName",type:"text",required:!0,label:"项目名称",admin:{description:"账单项目的名称"}},{name:"description",type:"textarea",label:"项目描述",admin:{description:"项目的详细描述"}},{name:"quantity",type:"number",required:!0,defaultValue:1,label:"数量",min:.01,admin:{description:"项目数量"}},{name:"unitPrice",type:"number",required:!0,label:"单价",min:0,admin:{description:"项目单价"}},{name:"discountRate",type:"number",defaultValue:0,label:"折扣率 (%)",min:0,max:100,admin:{description:"折扣率，0-100之间的数值"}},{name:"totalPrice",type:"number",label:"小计金额",admin:{description:"该项目的总金额（自动计算）",readOnly:!0}}]}},4457:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});let l={slug:"patient-interactions",admin:{useAsTitle:"title",defaultColumns:["title","patient","interactionType","staffMember","timestamp","status"],group:"CRM"},access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||("doctor"===e.role?{or:[{staffMember:{equals:e.id}},{interactionType:{in:["consultation-note","treatment-discussion","in-person-visit"]}}]}:"front-desk"===e.role&&{interactionType:{in:["phone-call","email","billing-inquiry"]}})),create:({req:{user:e}})=>!!e&&("admin"===e.role||"doctor"===e.role||"front-desk"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||{staffMember:{equals:e.id}}),delete:({req:{user:e}})=>!!e&&"admin"===e.role},fields:[{name:"patient",type:"relationship",relationTo:"patients",required:!0,label:"患者",admin:{description:"选择相关患者"}},{name:"interactionType",type:"select",required:!0,options:[{label:"电话通话",value:"phone-call"},{label:"邮件沟通",value:"email"},{label:"咨询记录",value:"consultation-note"},{label:"到院就诊",value:"in-person-visit"},{label:"治疗讨论",value:"treatment-discussion"},{label:"账单咨询",value:"billing-inquiry"}],label:"互动类型",admin:{description:"选择互动的类型"}},{name:"staffMember",type:"relationship",relationTo:"users",required:!0,label:"负责工作人员",admin:{description:"处理此次互动的工作人员"}},{name:"timestamp",type:"date",required:!0,defaultValue:()=>new Date,label:"互动时间",admin:{description:"互动发生的时间",date:{pickerAppearance:"dayAndTime"}}},{name:"title",type:"text",required:!0,maxLength:200,label:"互动标题",admin:{description:"简要描述此次互动的主题"}},{name:"notes",type:"richText",required:!0,label:"详细记录",admin:{description:"详细记录互动内容、讨论要点等"}},{name:"outcome",type:"text",maxLength:500,label:"互动结果",admin:{description:"记录互动的结果或解决方案"}},{name:"followUpRequired",type:"checkbox",defaultValue:!1,label:"需要跟进",admin:{description:"此次互动是否需要后续跟进"}},{name:"followUpDate",type:"date",label:"跟进日期",admin:{description:"计划跟进的日期",condition:e=>!0===e.followUpRequired,date:{pickerAppearance:"dayAndTime"}}},{name:"priority",type:"select",required:!0,options:[{label:"低",value:"low"},{label:"中",value:"medium"},{label:"高",value:"high"}],defaultValue:"medium",label:"优先级",admin:{description:"此次互动的优先级"}},{name:"status",type:"select",required:!0,options:[{label:"开放",value:"open"},{label:"进行中",value:"in-progress"},{label:"已解决",value:"resolved"},{label:"已关闭",value:"closed"}],defaultValue:"open",label:"状态",admin:{description:"互动的当前状态"}},{name:"relatedAppointment",type:"relationship",relationTo:"appointments",label:"关联预约",admin:{description:"如果此互动与特定预约相关，请选择"}},{name:"relatedBill",type:"relationship",relationTo:"bills",label:"关联账单",admin:{description:"如果此互动与特定账单相关，请选择"}}],hooks:{afterChange:[async({doc:e,operation:t,req:a})=>{if("create"===t&&e.followUpRequired&&e.followUpDate)try{await a.payload.create({collection:"patient-tasks",data:{patient:e.patient,title:`跟进：${e.title}`,description:`基于互动记录的跟进任务：${e.title}`,taskType:"follow-up-call",assignedTo:e.staffMember,createdBy:e.staffMember,dueDate:e.followUpDate,priority:e.priority,status:"pending",relatedInteraction:e.id}})}catch(e){console.error("Error creating follow-up task:",e)}}]}}},6708:(e,t,a)=>{"use strict";a.d(t,{u:()=>l});let l={slug:"patient-tasks",admin:{useAsTitle:"title",defaultColumns:["title","patient","taskType","assignedTo","dueDate","priority","status"],group:"CRM"},access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||("doctor"===e.role?{or:[{assignedTo:{equals:e.id}},{createdBy:{equals:e.id}},{taskType:{in:["treatment-reminder","medical-record-update","consultation-follow-up"]}}]}:"front-desk"===e.role&&{or:[{assignedTo:{equals:e.id}},{taskType:{in:["follow-up-call","appointment-scheduling","billing-follow-up"]}}]})),create:({req:{user:e}})=>!!e&&("admin"===e.role||"doctor"===e.role||"front-desk"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||{or:[{assignedTo:{equals:e.id}},{createdBy:{equals:e.id}}]}),delete:({req:{user:e}})=>!!e&&"admin"===e.role},fields:[{name:"patient",type:"relationship",relationTo:"patients",required:!0,label:"患者",admin:{description:"选择相关患者"}},{name:"title",type:"text",required:!0,maxLength:200,label:"任务标题",admin:{description:"简要描述任务内容"}},{name:"description",type:"richText",label:"任务描述",admin:{description:"详细描述任务要求和注意事项"}},{name:"taskType",type:"select",required:!0,options:[{label:"跟进电话",value:"follow-up-call"},{label:"预约安排",value:"appointment-scheduling"},{label:"治疗提醒",value:"treatment-reminder"},{label:"账单跟进",value:"billing-follow-up"},{label:"病历更新",value:"medical-record-update"},{label:"咨询跟进",value:"consultation-follow-up"}],label:"任务类型",admin:{description:"选择任务的类型"}},{name:"assignedTo",type:"relationship",relationTo:"users",required:!0,label:"分配给",admin:{description:"负责执行此任务的工作人员"}},{name:"createdBy",type:"relationship",relationTo:"users",required:!0,label:"创建者",admin:{description:"创建此任务的工作人员",readOnly:!0}},{name:"dueDate",type:"date",required:!0,label:"截止日期",admin:{description:"任务需要完成的日期",date:{pickerAppearance:"dayAndTime"}}},{name:"priority",type:"select",required:!0,options:[{label:"低",value:"low"},{label:"中",value:"medium"},{label:"高",value:"high"},{label:"紧急",value:"urgent"}],defaultValue:"medium",label:"优先级",admin:{description:"任务的优先级"}},{name:"status",type:"select",required:!0,options:[{label:"待处理",value:"pending"},{label:"进行中",value:"in-progress"},{label:"已完成",value:"completed"},{label:"已取消",value:"cancelled"}],defaultValue:"pending",label:"状态",admin:{description:"任务的当前状态"}},{name:"relatedInteraction",type:"relationship",relationTo:"patient-interactions",label:"关联互动",admin:{description:"如果此任务基于特定互动创建，请选择"}},{name:"completedAt",type:"date",label:"完成时间",admin:{description:"任务完成的时间",condition:e=>"completed"===e.status,date:{pickerAppearance:"dayAndTime"}}},{name:"completionNotes",type:"richText",label:"完成备注",admin:{description:"任务完成时的备注和总结",condition:e=>"completed"===e.status}}],hooks:{beforeChange:[async({data:e,operation:t,req:a})=>("create"===t&&a.user&&(e.createdBy=a.user.id),"completed"!==e.status||e.completedAt||(e.completedAt=new Date),"completed"!==e.status&&(e.completedAt=null,e.completionNotes=null),e)]}}},15487:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});let l={slug:"patients",admin:{useAsTitle:"fullName",defaultColumns:["fullName","phone","email","userType","status"]},access:{read:({req:{user:e}})=>!!e,create:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role||"doctor"===e.role),delete:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role)},fields:[{name:"userType",type:"select",required:!0,options:[{label:"咨询用户",value:"consultation"},{label:"正式患者",value:"patient"}],defaultValue:"consultation",label:"用户类型",admin:{description:"区分咨询用户和正式患者"}},{name:"status",type:"select",required:!0,options:[{label:"活跃",value:"active"},{label:"非活跃",value:"inactive"},{label:"已转换",value:"converted"}],defaultValue:"active",label:"用户状态",admin:{description:"用户当前状态"}},{name:"source",type:"select",required:!0,options:[{label:"到店咨询",value:"walk-in"},{label:"转介绍",value:"referral"},{label:"在线咨询",value:"online"},{label:"电话咨询",value:"phone"}],defaultValue:"walk-in",label:"来源渠道",admin:{description:"用户来源追踪"}},{name:"referredBy",type:"text",label:"转介绍人",admin:{description:"如果是转介绍，记录转介绍人信息",condition:e=>"referral"===e.source}},{name:"fullName",type:"text",required:!0,label:"Full Name"},{name:"phone",type:"text",required:!0,unique:!0,label:"Phone Number"},{name:"email",type:"email",label:"Email Address"},{name:"photo",type:"upload",relationTo:"media",label:"Patient Photo"},{name:"convertedAt",type:"date",label:"转换时间",admin:{description:"从咨询用户转为正式患者的时间",condition:e=>"patient"===e.userType&&"converted"===e.status,date:{pickerAppearance:"dayAndTime"}}},{name:"lastVisit",type:"date",label:"最后就诊时间",admin:{description:"最后一次就诊的时间",date:{pickerAppearance:"dayAndTime"}}},{name:"emergencyContact",type:"text",label:"紧急联系人",admin:{description:"紧急联系人信息（正式患者建议填写）",condition:e=>"patient"===e.userType}},{name:"allergies",type:"array",label:"过敏史",fields:[{name:"allergen",type:"text",label:"过敏原",required:!0},{name:"severity",type:"select",label:"严重程度",options:[{label:"轻微",value:"mild"},{label:"中等",value:"moderate"},{label:"严重",value:"severe"}],defaultValue:"mild"}],admin:{description:"患者过敏史记录（正式患者重要信息）",condition:e=>"patient"===e.userType},access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||"doctor"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||"doctor"===e.role)}},{name:"medicalHistory",type:"textarea",label:"病史摘要",admin:{description:"患者主要病史摘要（正式患者重要信息）",condition:e=>"patient"===e.userType},access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||"doctor"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||"doctor"===e.role)}},{name:"medicalNotes",type:"richText",label:"Medical Notes",access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||"doctor"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||"doctor"===e.role)},admin:{description:"Confidential medical information - restricted to medical staff only"}}]}},17547:(e,t,a)=>{"use strict";a.d(t,{F:()=>l});let l={slug:"appointments",admin:{defaultColumns:["appointmentDate","appointmentType","treatment","patient","status"],listSearchableFields:["treatment.name","patient.fullName"]},access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role||"doctor"===e.role&&{practitioner:{equals:e.id}}),create:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role||"doctor"===e.role&&{practitioner:{equals:e.id}}),delete:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role)},fields:[{name:"appointmentType",type:"select",required:!0,options:[{label:"咨询预约",value:"consultation"},{label:"治疗预约",value:"treatment"}],defaultValue:"consultation",label:"预约类型",admin:{description:"区分咨询预约和治疗预约"}},{name:"appointmentDate",type:"date",required:!0,label:"Appointment Date & Time",admin:{date:{pickerAppearance:"dayAndTime"}}},{name:"status",type:"select",required:!0,options:[{label:"已预约",value:"scheduled"},{label:"已确认",value:"confirmed"},{label:"已完成",value:"completed"},{label:"已取消",value:"cancelled"},{label:"未到诊",value:"no-show"}],defaultValue:"scheduled",label:"预约状态"},{name:"treatment",type:"relationship",relationTo:"treatments",required:!1,hasMany:!1,label:"Treatment",admin:{description:"治疗预约必填，咨询预约可选",condition:e=>"treatment"===e.appointmentType}},{name:"price",type:"number",required:!1,label:"Final Price",min:0,admin:{description:"最终价格（咨询预约可能为0）"}},{name:"durationInMinutes",type:"number",required:!0,label:"Duration (minutes)",min:1},{name:"patient",type:"relationship",relationTo:"patients",required:!0,hasMany:!1,label:"Patient"},{name:"practitioner",type:"relationship",relationTo:"users",required:!0,hasMany:!1,label:"Practitioner"},{name:"consultationType",type:"select",options:[{label:"初次咨询",value:"initial"},{label:"复诊咨询",value:"follow-up"},{label:"价格咨询",value:"price-inquiry"}],label:"咨询类型",admin:{description:"仅咨询预约需要填写",condition:e=>"consultation"===e.appointmentType}},{name:"interestedTreatments",type:"relationship",relationTo:"treatments",hasMany:!0,label:"感兴趣的治疗项目",admin:{description:"咨询用户感兴趣的治疗项目",condition:e=>"consultation"===e.appointmentType}},{name:"paymentStatus",type:"select",options:[{label:"待支付",value:"pending"},{label:"部分支付",value:"partial"},{label:"已支付",value:"paid"},{label:"逾期",value:"overdue"}],label:"支付状态",admin:{description:"治疗预约的支付状态",condition:e=>"treatment"===e.appointmentType}},{name:"outcome",type:"select",options:[{label:"已转换",value:"converted"},{label:"已预约治疗",value:"scheduled-treatment"},{label:"无兴趣",value:"no-interest"},{label:"需要后续跟进",value:"follow-up-needed"}],label:"咨询结果",admin:{description:"咨询预约的结果追踪",condition:e=>"consultation"===e.appointmentType&&"completed"===e.status}},{name:"notes",type:"textarea",label:"预约备注",admin:{description:"预约相关的备注信息"}}]}},45590:()=>{},51728:(e,t,a)=>{"use strict";a.d(t,{u:()=>l});let l={slug:"treatments",admin:{useAsTitle:"name"},access:{read:({req:{user:e}})=>!!e,create:({req:{user:e}})=>!!e&&"admin"===e.role,update:({req:{user:e}})=>!!e&&"admin"===e.role,delete:({req:{user:e}})=>!!e&&"admin"===e.role},fields:[{name:"name",type:"text",required:!0,unique:!0,label:"Treatment Name"},{name:"description",type:"richText",label:"Description"},{name:"defaultPrice",type:"number",required:!0,label:"Default Price",min:0},{name:"defaultDurationInMinutes",type:"number",required:!0,label:"Default Duration (minutes)",min:1}]}},52367:()=>{},58997:(e,t,a)=>{"use strict";a.d(t,{z:()=>l});let l={slug:"users",admin:{useAsTitle:"email",defaultColumns:["email","role","clerkId"]},auth:{disableLocalStrategy:!0},access:{read:({req:{user:e}})=>!!e,create:({req:{user:e}})=>!!e&&"admin"===e.role,update:({req:{user:e}})=>!!e&&"admin"===e.role,delete:({req:{user:e}})=>!!e&&"admin"===e.role},fields:[{name:"role",type:"select",required:!0,options:[{label:"Admin",value:"admin"},{label:"Front-desk",value:"front-desk"},{label:"Doctor",value:"doctor"}],defaultValue:"front-desk",access:{update:({req:{user:e}})=>!!e&&"admin"===e.role},admin:{description:"User role determines access permissions - only Admin can modify"}},{name:"clerkId",type:"text",required:!0,unique:!0,label:"Clerk User ID",admin:{description:"Unique identifier from Clerk authentication service",readOnly:!0}},{name:"firstName",type:"text",label:"First Name"},{name:"lastName",type:"text",label:"Last Name"},{name:"lastLogin",type:"date",label:"Last Login",admin:{readOnly:!0,description:"Automatically updated when user logs in"}}]}},68117:(e,t,a)=>{"use strict";a.a(e,async(e,l)=>{try{a.d(t,{A:()=>N});var i=a(30404),n=a(12712),r=a(52396),o=a(33873),d=a.n(o),s=a(56201),m=a(79551),u=a(9288),p=a.n(u),c=a(58997),b=a(2377),y=a(51728),f=a(15487),q=a(17547),v=a(68459),h=a(2750),g=a(1022),A=a(74902),T=a(4457),k=a(6708),w=e([i]);i=(w.then?(await w)():w)[0];let D=(0,m.fileURLToPath)("file:///C:/Users/<USER>/Desktop/nord-coast/backend/src/payload.config.ts"),x=d().dirname(D),N=(0,s.f)({admin:{user:c.z.slug,importMap:{baseDir:d().resolve(x)}},collections:[c.z,b.$,y.u,f.J,q.F,v.B,h.m,g.y,A.I,T.$,k.u],editor:(0,r.qf)(),secret:process.env.PAYLOAD_SECRET||"",typescript:{outputFile:d().resolve(x,"payload-types.ts")},db:(0,i.V8)({pool:{connectionString:process.env.DATABASE_URI||""}}),sharp:p(),plugins:[(0,n.T)()]});l()}catch(e){l(e)}})},68459:(e,t,a)=>{"use strict";a.d(t,{B:()=>l});let l={slug:"bills",admin:{useAsTitle:"billNumber",defaultColumns:["billNumber","patient","billType","status","totalAmount","remainingAmount"],listSearchableFields:["billNumber","patient.fullName","description"]},access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role||"doctor"===e.role&&{or:[{"appointment.practitioner":{equals:e.id}},{createdBy:{equals:e.id}}]}),create:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),delete:({req:{user:e}})=>!!e&&"admin"===e.role},fields:[{name:"billNumber",type:"text",required:!0,unique:!0,label:"账单编号",admin:{description:"系统自动生成，格式：BILL-YYYYMMDD-XXXXXX",readOnly:!0}},{name:"patient",type:"relationship",relationTo:"patients",required:!0,hasMany:!1,label:"患者"},{name:"appointment",type:"relationship",relationTo:"appointments",hasMany:!1,label:"关联预约",admin:{description:"如果账单来源于预约，请选择对应预约"}},{name:"treatment",type:"relationship",relationTo:"treatments",hasMany:!1,label:"关联治疗",admin:{description:"如果是治疗账单，请选择对应治疗项目"}},{name:"billType",type:"select",required:!0,options:[{label:"治疗账单",value:"treatment"},{label:"咨询账单",value:"consultation"},{label:"押金账单",value:"deposit"},{label:"补充账单",value:"additional"}],defaultValue:"treatment",label:"账单类型"},{name:"status",type:"select",required:!0,options:[{label:"草稿",value:"draft"},{label:"已发送",value:"sent"},{label:"已确认",value:"confirmed"},{label:"已支付",value:"paid"},{label:"已取消",value:"cancelled"}],defaultValue:"draft",label:"账单状态"},{name:"subtotal",type:"number",required:!0,label:"小计金额",min:0,admin:{description:"税前金额"}},{name:"discountAmount",type:"number",defaultValue:0,label:"折扣金额",min:0,admin:{description:"折扣金额"}},{name:"taxAmount",type:"number",defaultValue:0,label:"税费",min:0,admin:{description:"税费金额"}},{name:"totalAmount",type:"number",required:!0,label:"总金额",min:0,admin:{description:"最终应付金额 = 小计 + 税费 - 折扣"}},{name:"paidAmount",type:"number",defaultValue:0,label:"已支付金额",min:0,admin:{description:"已支付的金额"}},{name:"remainingAmount",type:"number",label:"剩余金额",admin:{description:"剩余未支付金额（自动计算）",readOnly:!0}},{name:"issueDate",type:"date",required:!0,label:"开票日期",defaultValue:()=>new Date().toISOString(),admin:{date:{pickerAppearance:"dayOnly"}}},{name:"dueDate",type:"date",required:!0,label:"到期日期",admin:{date:{pickerAppearance:"dayOnly"},description:"账单到期日期"}},{name:"paidDate",type:"date",label:"支付完成日期",admin:{date:{pickerAppearance:"dayAndTime"},description:"账单完全支付的日期",condition:e=>"paid"===e.status}},{name:"description",type:"text",required:!0,label:"账单描述",admin:{description:"账单的简要描述"}},{name:"notes",type:"textarea",label:"备注",admin:{description:"账单相关的备注信息"}},{name:"createdBy",type:"relationship",relationTo:"users",required:!0,hasMany:!1,label:"创建人员",admin:{description:"创建此账单的工作人员"}}],hooks:{beforeChange:[async({data:e,req:t,operation:a})=>{if(!e.billNumber){let t=new Date,a=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),n=t.getTime().toString().slice(-6);e.billNumber=`BILL-${a}${l}${i}-${n}`}if(void 0!==e.subtotal&&e.subtotal<0)throw Error("小计金额不能为负数");if(void 0!==e.discountAmount&&e.discountAmount<0)throw Error("折扣金额不能为负数");if(void 0!==e.taxAmount&&e.taxAmount<0)throw Error("税费不能为负数");if(void 0!==e.totalAmount&&e.totalAmount<0)throw Error("总金额不能为负数");if(void 0!==e.paidAmount&&e.paidAmount<0)throw Error("已付金额不能为负数");if(void 0!==e.subtotal){let t=e.subtotal||0,a=e.discountAmount||0,l=e.taxAmount||0;e.totalAmount=t-a+l}if(void 0!==e.totalAmount){let t=e.totalAmount,a=e.paidAmount||0;e.remainingAmount=t-a}if(void 0!==e.paidAmount&&void 0!==e.totalAmount&&e.paidAmount>e.totalAmount)throw Error("已付金额不能超过总金额");return"create"===a&&!e.createdBy&&t.user&&(e.createdBy=t.user.id),"paid"!==e.status||e.paidDate||(e.paidDate=new Date().toISOString()),"paid"!==e.status&&e.paidDate&&(e.paidDate=null),e}],afterChange:[async({doc:e,req:t,operation:a,previousDoc:l})=>{if("create"===a||l&&e.totalAmount!==l.totalAmount)try{let l=new Promise((e,t)=>{setTimeout(()=>t(Error("Hook timeout")),5e3)}),i=(async()=>{if(e.appointment){let a="object"==typeof e.appointment?e.appointment.id:e.appointment,l="pending";"paid"===e.status?l="paid":e.paidAmount>0&&(l="partial"),await t.payload.update({collection:"appointments",id:a,data:{paymentStatus:l}})}console.log(`Bill ${e.billNumber} ${a}d - Total: $${e.totalAmount}, Remaining: $${e.remainingAmount}, Status: ${e.status}`)})();await Promise.race([i,l])}catch(e){console.error("Error in Bills afterChange hook:",e)}}]}}},74902:(e,t,a)=>{"use strict";a.d(t,{I:()=>l});let l={slug:"deposits",admin:{useAsTitle:"depositNumber",defaultColumns:["depositNumber","patient","depositType","amount","remainingAmount","status"],listSearchableFields:["depositNumber","patient.fullName","purpose"]},access:{read:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role||"doctor"===e.role&&{or:[{"appointment.practitioner":{equals:e.id}},{"treatment.practitioner":{equals:e.id}}]}),create:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),update:({req:{user:e}})=>!!e&&("admin"===e.role||"front-desk"===e.role),delete:({req:{user:e}})=>!!e&&"admin"===e.role},hooks:{beforeChange:[({data:e})=>{if(!e.depositNumber){let t=new Date,a=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),n=t.getTime().toString().slice(-6);e.depositNumber=`DEP-${a}${l}${i}-${n}`}if(void 0!==e.amount&&e.amount<0)throw Error("Deposit amount cannot be negative");if(void 0!==e.usedAmount&&e.usedAmount<0)throw Error("Used amount cannot be negative");let t=e.amount||0,a=e.usedAmount||0;if(a>t)throw Error("Used amount cannot exceed total deposit amount");return e.remainingAmount=t-a,e.remainingAmount<=0&&a>0?e.status="used":e.remainingAmount>0&&a>0?e.status="active":0===a&&(e.status="active"),e.expiryDate&&new Date(e.expiryDate)<new Date&&"active"===e.status&&(e.status="expired"),e}]},fields:[{name:"depositNumber",type:"text",required:!0,unique:!0,label:"押金编号",admin:{description:"系统自动生成，格式：DEP-YYYYMMDD-XXXXXX",readOnly:!0}},{name:"patient",type:"relationship",relationTo:"patients",required:!0,hasMany:!1,label:"患者"},{name:"appointment",type:"relationship",relationTo:"appointments",hasMany:!1,label:"关联预约",admin:{description:"如果押金与特定预约相关"}},{name:"treatment",type:"relationship",relationTo:"treatments",hasMany:!1,label:"关联治疗",admin:{description:"如果押金与特定治疗相关"}},{name:"depositType",type:"select",required:!0,options:[{label:"治疗押金",value:"treatment"},{label:"预约押金",value:"appointment"},{label:"材料押金",value:"material"}],defaultValue:"treatment",label:"押金类型"},{name:"amount",type:"number",required:!0,label:"押金金额",min:.01,admin:{description:"押金总金额"}},{name:"status",type:"select",required:!0,options:[{label:"有效",value:"active"},{label:"已使用",value:"used"},{label:"已退还",value:"refunded"},{label:"已过期",value:"expired"}],defaultValue:"active",label:"押金状态"},{name:"usedAmount",type:"number",defaultValue:0,label:"已使用金额",min:0,admin:{description:"已使用的押金金额"}},{name:"remainingAmount",type:"number",label:"剩余金额",admin:{description:"剩余可用押金金额（自动计算）",readOnly:!0}},{name:"depositDate",type:"date",required:!0,label:"收取日期",defaultValue:()=>new Date().toISOString(),admin:{date:{pickerAppearance:"dayAndTime"}}},{name:"expiryDate",type:"date",label:"到期日期",admin:{date:{pickerAppearance:"dayOnly"},description:"押金到期日期（可选）"}},{name:"usedDate",type:"date",label:"使用日期",admin:{date:{pickerAppearance:"dayAndTime"},description:"押金使用日期",condition:e=>"used"===e.status||e.usedAmount>0}},{name:"refundDate",type:"date",label:"退还日期",admin:{date:{pickerAppearance:"dayAndTime"},description:"押金退还日期",condition:e=>"refunded"===e.status}},{name:"purpose",type:"text",required:!0,label:"押金用途",admin:{description:"押金的具体用途说明"}},{name:"notes",type:"textarea",label:"备注",admin:{description:"押金相关的备注信息"}}]}}};