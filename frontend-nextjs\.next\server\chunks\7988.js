try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d2361785-bf96-491f-8f10-e964e5cfb6f9",e._sentryDebugIdIdentifier="sentry-dbid-d2361785-bf96-491f-8f10-e964e5cfb6f9")}catch(e){}"use strict";exports.id=7988,exports.ids=[7988],exports.modules={2946:(e,t,n)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o.registerServerReference}});let o=n(91611)},8074:(e,t,n)=>{n.d(t,{AuthenticateWithRedirectCallback:()=>o.B$,ClerkLoaded:()=>o.z0,ClerkLoading:()=>o.A0,RedirectToCreateOrganization:()=>o.rm,RedirectToOrganizationProfile:()=>o.m2,RedirectToSignIn:()=>o.W5,RedirectToSignUp:()=>o.mO,RedirectToUserProfile:()=>o.eG});var o=n(22371);n(95505)},15349:(e,t,n)=>{n.d(t,{useAuth:()=>r,useClerk:()=>s,useEmailLink:()=>i,useOrganization:()=>a,useOrganizationList:()=>l,useReverification:()=>d,useSession:()=>c,useSessionList:()=>f,useSignIn:()=>u,useSignUp:()=>p,useUser:()=>m});var o=n(91611);(0,o.registerClientReference)(function(){throw Error("Attempted to call EmailLinkErrorCode() from the server but EmailLinkErrorCode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","EmailLinkErrorCode"),(0,o.registerClientReference)(function(){throw Error("Attempted to call EmailLinkErrorCodeStatus() from the server but EmailLinkErrorCodeStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","EmailLinkErrorCodeStatus"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isClerkAPIResponseError() from the server but isClerkAPIResponseError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isClerkAPIResponseError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isClerkRuntimeError() from the server but isClerkRuntimeError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isClerkRuntimeError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isEmailLinkError() from the server but isEmailLinkError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isEmailLinkError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isKnownError() from the server but isKnownError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isKnownError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isMetamaskError() from the server but isMetamaskError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isMetamaskError"),(0,o.registerClientReference)(function(){throw Error("Attempted to call isReverificationCancelledError() from the server but isReverificationCancelledError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","isReverificationCancelledError");let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useAuth"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call useClerk() from the server but useClerk is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useClerk"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call useEmailLink() from the server but useEmailLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useEmailLink"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call useOrganization() from the server but useOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useOrganization"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call useOrganizationList() from the server but useOrganizationList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useOrganizationList"),d=(0,o.registerClientReference)(function(){throw Error("Attempted to call useReverification() from the server but useReverification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useReverification"),c=(0,o.registerClientReference)(function(){throw Error("Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useSession"),f=(0,o.registerClientReference)(function(){throw Error("Attempted to call useSessionList() from the server but useSessionList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useSessionList"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call useSignIn() from the server but useSignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useSignIn"),p=(0,o.registerClientReference)(function(){throw Error("Attempted to call useSignUp() from the server but useSignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useSignUp"),m=(0,o.registerClientReference)(function(){throw Error("Attempted to call useUser() from the server but useUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\hooks.js","useUser")},17893:(e,t,n)=>{n.r(t),n.d(t,{KeylessCookieSync:()=>r});var o=n(34769);function r(e){var t;return null==(t=(0,o.useSelectedLayoutSegments)()[0])||t.startsWith("/_not-found"),e.children}n(60222),n(5054)},18198:(e,t)=>{function n(e){for(let t=0;t<e.length;t++){let n=e[t];if("function"!=typeof n)throw Object.defineProperty(Error(`A "use server" file can only export async functions, found ${typeof n}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`),"__NEXT_ERROR_CODE",{value:"E352",enumerable:!1,configurable:!0})}}Object.defineProperty(t,"D",{enumerable:!0,get:function(){return n}})},21830:(e,t,n)=>{n.r(t),n.d(t,{ClientClerkProvider:()=>o});let o=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call ClientClerkProvider() from the server but ClientClerkProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\ClerkProvider.js","ClientClerkProvider")},27144:(e,t,n)=>{n.d(t,{useAuth:()=>r.d,useClerk:()=>o.ho,useEmailLink:()=>o.ui,useOrganization:()=>o.Z5,useOrganizationList:()=>o.D_,useReverification:()=>o.Wp,useSession:()=>o.wV,useSessionList:()=>o.g7,useSignIn:()=>o.go,useSignUp:()=>o.yC,useUser:()=>o.Jd});var o=n(22371);n(65621);var r=n(79153)},31386:(e,t,n)=>{let o;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{arrayBufferToString:function(){return a},decrypt:function(){return c},encrypt:function(){return d},getActionEncryptionKey:function(){return b},getClientReferenceManifestForRsc:function(){return m},getServerModuleMap:function(){return p},setReferenceManifestsSingleton:function(){return u},stringToUint8Array:function(){return l}});let r=n(63473),s=n(56498),i=n(29294);function a(e){let t=new Uint8Array(e),n=t.byteLength;if(n<65535)return String.fromCharCode.apply(null,t);let o="";for(let e=0;e<n;e++)o+=String.fromCharCode(t[e]);return o}function l(e){let t=e.length,n=new Uint8Array(t);for(let o=0;o<t;o++)n[o]=e.charCodeAt(o);return n}function d(e,t,n){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,n)}function c(e,t,n){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,n)}let f=Symbol.for("next.server.action-manifests");function u({page:e,clientReferenceManifest:t,serverActionsManifest:n,serverModuleMap:o}){var r;let i=null==(r=globalThis[f])?void 0:r.clientReferenceManifestsPerPage;globalThis[f]={clientReferenceManifestsPerPage:{...i,[(0,s.normalizeAppPath)(e)]:t},serverActionsManifest:n,serverModuleMap:o}}function p(){let e=globalThis[f];if(!e)throw Object.defineProperty(new r.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function m(){let e=globalThis[f];if(!e)throw Object.defineProperty(new r.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:t}=e,n=i.workAsyncStorage.getStore();if(!n){var o=t;let e=Object.values(o),n={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let t of e)n.clientModules={...n.clientModules,...t.clientModules},n.edgeRscModuleMapping={...n.edgeRscModuleMapping,...t.edgeRscModuleMapping},n.rscModuleMapping={...n.rscModuleMapping,...t.rscModuleMapping};return n}let s=t[n.route];if(!s)throw Object.defineProperty(new r.InvariantError(`Missing Client Reference Manifest for ${n.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return s}async function b(){if(o)return o;let e=globalThis[f];if(!e)throw Object.defineProperty(new r.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Object.defineProperty(new r.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return o=await crypto.subtle.importKey("raw",l(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},40112:(e,t,n)=>{n.d(t,{AuthenticateWithRedirectCallback:()=>r,ClerkLoaded:()=>s,ClerkLoading:()=>i,RedirectToCreateOrganization:()=>a,RedirectToOrganizationProfile:()=>l,RedirectToSignIn:()=>d,RedirectToSignUp:()=>c,RedirectToUserProfile:()=>f});var o=n(91611);let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthenticateWithRedirectCallback() from the server but AuthenticateWithRedirectCallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","AuthenticateWithRedirectCallback"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call ClerkLoaded() from the server but ClerkLoaded is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","ClerkLoaded"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call ClerkLoading() from the server but ClerkLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","ClerkLoading");(0,o.registerClientReference)(function(){throw Error("Attempted to call MultisessionAppSupport() from the server but MultisessionAppSupport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","MultisessionAppSupport"),(0,o.registerClientReference)(function(){throw Error("Attempted to call Protect() from the server but Protect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","Protect");let a=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToCreateOrganization() from the server but RedirectToCreateOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToCreateOrganization"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToOrganizationProfile() from the server but RedirectToOrganizationProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToOrganizationProfile"),d=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToSignIn() from the server but RedirectToSignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToSignIn"),c=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToSignUp() from the server but RedirectToSignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToSignUp"),f=(0,o.registerClientReference)(function(){throw Error("Attempted to call RedirectToUserProfile() from the server but RedirectToUserProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","RedirectToUserProfile");(0,o.registerClientReference)(function(){throw Error("Attempted to call SignedIn() from the server but SignedIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","SignedIn"),(0,o.registerClientReference)(function(){throw Error("Attempted to call SignedOut() from the server but SignedOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\controlComponents.js","SignedOut")},55439:(e,t,n)=>{n.r(t),n.d(t,{KeylessCookieSync:()=>o});let o=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call KeylessCookieSync() from the server but KeylessCookieSync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\app-router\\client\\keyless-cookie-sync.js","KeylessCookieSync")},64965:(e,t,n)=>{n.d(t,{ai:()=>m,at:()=>b,ot:()=>p});var o=n(2946);n(70570);var r=n(87927),s=n(44508);let i=(0,n(48712)._r)({packageName:"@clerk/nextjs"});var a=n(36218),l=n(18120);let d="__clerk_keys_";async function c(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function f(){let e=process.env.PWD;if(!e)return`${d}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),n=await c(t);return`${d}${n}`}async function u(e){let t;if(!l.I)return;let n=await f();try{n&&(t=JSON.parse(e(n)||"{}"))}catch{t=void 0}return t}async function p(e){let{claimUrl:t,publishableKey:n,secretKey:o,returnUrl:i}=e,l=await (0,r.UL)(),d=new Request("https://placeholder.com",{headers:await (0,r.headers)()}),c=await u(e=>{var t;return null==(t=l.get(e))?void 0:t.value}),p=(null==c?void 0:c.publishableKey)===n,m=(null==c?void 0:c.secretKey)===o;if((!p||!m)&&(l.set(await f(),JSON.stringify({claimUrl:t,publishableKey:n,secretKey:o}),{secure:!0,httpOnly:!0}),(0,a.Zd)(d)))return void(0,s.redirect)(`/clerk-sync-keyless?returnUrl=${i}`,s.RedirectType.replace)}async function m(){if(!l.I)return null;let e=await n.e(1995).then(n.bind(n,41995)).then(e=>e.createOrReadKeyless()).catch(()=>null);if(!e)return i.throwMissingPublishableKeyError(),null;let{clerkDevelopmentCache:t,createKeylessModeMessage:o}=await n.e(4006).then(n.bind(n,94006));null==t||t.log({cacheKey:e.publishableKey,msg:o(e)});let{claimUrl:s,publishableKey:a,secretKey:d,apiKeysUrl:c}=e;return(await (0,r.UL)()).set(await f(),JSON.stringify({claimUrl:s,publishableKey:a,secretKey:d}),{secure:!1,httpOnly:!1}),{claimUrl:s,publishableKey:a,apiKeysUrl:c}}async function b(){l.I&&await n.e(1995).then(n.bind(n,41995)).then(e=>e.removeKeyless()).catch(()=>{})}(0,n(18198).D)([m,b,p]),(0,o.A)(m,"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261",null),(0,o.A)(b,"7f7b45347fd50452ee6e2850ded1018991a7b086f0",null),(0,o.A)(p,"7f909588461cb83e855875f4939d6f26e4ae81b49e",null)},70570:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{decryptActionBoundArgs:function(){return b},encryptActionBoundArgs:function(){return m}}),n(58838);let o=n(91611),r=n(36433),s=n(50639),i=n(31386),a=n(63033),l=n(27643),d=function(e){return e&&e.__esModule?e:{default:e}}(n(22576)),c=new TextEncoder,f=new TextDecoder;async function u(e,t){let n=await (0,i.getActionEncryptionKey)();if(void 0===n)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let o=atob(t),r=o.slice(0,16),s=o.slice(16),a=f.decode(await (0,i.decrypt)(n,(0,i.stringToUint8Array)(r),(0,i.stringToUint8Array)(s)));if(!a.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return a.slice(e.length)}async function p(e,t){let n=await (0,i.getActionEncryptionKey)();if(void 0===n)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let o=new Uint8Array(16);a.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(o));let r=(0,i.arrayBufferToString)(o.buffer),s=await (0,i.encrypt)(n,o,c.encode(e+t));return btoa(r+(0,i.arrayBufferToString)(s))}let m=d.default.cache(async function e(t,...n){let{clientModules:r}=(0,i.getClientReferenceManifestForRsc)(),d=Error();Error.captureStackTrace(d,e);let c=!1,f=a.workUnitAsyncStorage.getStore(),u=(null==f?void 0:f.type)==="prerender"?(0,l.createHangingInputAbortSignal)(f):void 0,m=await (0,s.streamToString)((0,o.renderToReadableStream)(n,r,{signal:u,onError(e){(null==u||!u.aborted)&&(c||(c=!0,d.message=e instanceof Error?e.message:String(e)))}}),u);if(c)throw d;if(!f)return p(t,m);let b=(0,a.getPrerenderResumeDataCache)(f),C=(0,a.getRenderResumeDataCache)(f),h=t+m,g=(null==b?void 0:b.encryptedBoundArgs.get(h))??(null==C?void 0:C.encryptedBoundArgs.get(h));if(g)return g;let k="prerender"===f.type?f.cacheSignal:void 0;null==k||k.beginRead();let y=await p(t,m);return null==k||k.endRead(),null==b||b.encryptedBoundArgs.set(h,y),y});async function b(e,t){let n,o=await t,s=a.workUnitAsyncStorage.getStore();if(s){let t="prerender"===s.type?s.cacheSignal:void 0,r=(0,a.getPrerenderResumeDataCache)(s),i=(0,a.getRenderResumeDataCache)(s);(n=(null==r?void 0:r.decryptedBoundArgs.get(o))??(null==i?void 0:i.decryptedBoundArgs.get(o)))||(null==t||t.beginRead(),n=await u(e,o),null==t||t.endRead(),null==r||r.decryptedBoundArgs.set(o,n))}else n=await u(e,o);let{edgeRscModuleMapping:l,rscModuleMapping:d}=(0,i.getClientReferenceManifestForRsc)();return await (0,r.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(c.encode(n)),(null==s?void 0:s.type)==="prerender"?s.renderSignal.aborted?e.close():s.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:d,serverModuleMap:(0,i.getServerModuleMap)()}})}},79153:(e,t,n)=>{n.d(t,{PromisifiedAuthProvider:()=>d,d:()=>c});var o=n(22371),r=n(95505),s=n(26393),i=n(60222),a=n.n(i);let l=a().createContext(null);function d({authPromise:e,children:t}){return a().createElement(l.Provider,{value:e},t)}function c(){let e=(0,s.useRouter)(),t=a().useContext(l),n=t;return(t&&"then"in t&&(n=a().use(t)),e)?(0,o.As)():(0,r.hP)(n)}},90339:(e,t,n)=>{n.d(t,{PromisifiedAuthProvider:()=>r});var o=n(91611);let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call PromisifiedAuthProvider() from the server but PromisifiedAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\PromisifiedAuthProvider.js","PromisifiedAuthProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call usePromisifiedAuth() from the server but usePromisifiedAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\PromisifiedAuthProvider.js","usePromisifiedAuth")},95614:(e,t,n)=>{n.d(t,{CreateOrganization:()=>o.ul,GoogleOneTap:()=>o.PQ,OrganizationList:()=>o.oE,OrganizationProfile:()=>p,OrganizationSwitcher:()=>o.NC,SignIn:()=>m,SignInButton:()=>o.hZ,SignInWithMetamaskButton:()=>o.M_,SignOutButton:()=>o.ct,SignUp:()=>b,SignUpButton:()=>o.Ny,UserButton:()=>o.uF,UserProfile:()=>u,Waitlist:()=>o.cP});var o=n(22371),r=n(60222),s=n.n(r),i=n(95505),a=n(86756),l=n(57678);let d=(e,t,n,r=!0)=>{let i=s().useRef(0),{pagesRouter:d}=(0,l.r)(),{session:c,isLoaded:f}=(0,o.wV)();(0,a.Fj)()||s().useEffect(()=>{if(!f||n&&"path"!==n||r&&!c)return;let o=new AbortController,s=()=>{let n=d?`${t}/[[...index]].tsx`:`${t}/[[...rest]]/page.tsx`;throw Error(`
Clerk: The <${e}/> component is not configured correctly. The most likely reasons for this error are:

1. The "${t}" route is not a catch-all route.
It is recommended to convert this route to a catch-all route, eg: "${n}". Alternatively, you can update the <${e}/> component to use hash-based routing by setting the "routing" prop to "hash".

2. The <${e}/> component is mounted in a catch-all route, but all routes under "${t}" are protected by the middleware.
To resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the "createRouteMatcher" helper, consider adding "(.*)" to the end of the route pattern, eg: "${t}(.*)". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher
`)};return d?d.pathname.match(/\[\[\.\.\..+]]/)||s():(async()=>{let t;if(i.current++,!(i.current>1)){try{let n=`${window.location.origin}${window.location.pathname}/${e}_clerk_catchall_check_${Date.now()}`;t=await fetch(n,{signal:o.signal})}catch{}(null==t?void 0:t.status)===404&&s()}})(),()=>{i.current>1&&o.abort()}},[f])},c=()=>{let e=s().useRef(),{pagesRouter:t}=(0,l.r)();if(t)if(e.current)return e.current;else return e.current=t.pathname.replace(/\/\[\[\.\.\..*/,""),e.current;let o=n(34769).usePathname,r=n(34769).useParams,i=(o()||"").split("/").filter(Boolean),a=Object.values(r()||{}).filter(e=>Array.isArray(e)).flat(1/0);return e.current||(e.current=`/${i.slice(0,i.length-a.length).join("/")}`),e.current};function f(e,t,n=!0){let o=c(),r=(0,i.yC)(e,t,{path:o});return d(e,o,r.routing,n),r}let u=Object.assign(e=>s().createElement(o.Fv,{...f("UserProfile",e)}),{...o.Fv}),p=Object.assign(e=>s().createElement(o.nC,{...f("OrganizationProfile",e)}),{...o.nC}),m=e=>s().createElement(o.Ls,{...f("SignIn",e,!1)}),b=e=>s().createElement(o.Hx,{...f("SignUp",e,!1)})},98451:(e,t,n)=>{n.d(t,{CreateOrganization:()=>r,GoogleOneTap:()=>s,OrganizationList:()=>i,OrganizationProfile:()=>a,OrganizationSwitcher:()=>l,SignIn:()=>d,SignInButton:()=>c,SignInWithMetamaskButton:()=>f,SignOutButton:()=>u,SignUp:()=>p,SignUpButton:()=>m,UserButton:()=>b,UserProfile:()=>C,Waitlist:()=>h});var o=n(91611);let r=(0,o.registerClientReference)(function(){throw Error("Attempted to call CreateOrganization() from the server but CreateOrganization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","CreateOrganization"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call GoogleOneTap() from the server but GoogleOneTap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","GoogleOneTap"),i=(0,o.registerClientReference)(function(){throw Error("Attempted to call OrganizationList() from the server but OrganizationList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","OrganizationList"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call OrganizationProfile() from the server but OrganizationProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","OrganizationProfile"),l=(0,o.registerClientReference)(function(){throw Error("Attempted to call OrganizationSwitcher() from the server but OrganizationSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","OrganizationSwitcher"),d=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignIn() from the server but SignIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignIn"),c=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignInButton() from the server but SignInButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignInButton"),f=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignInWithMetamaskButton() from the server but SignInWithMetamaskButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignInWithMetamaskButton"),u=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignOutButton() from the server but SignOutButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignOutButton"),p=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignUp() from the server but SignUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignUp"),m=(0,o.registerClientReference)(function(){throw Error("Attempted to call SignUpButton() from the server but SignUpButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","SignUpButton"),b=(0,o.registerClientReference)(function(){throw Error("Attempted to call UserButton() from the server but UserButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","UserButton"),C=(0,o.registerClientReference)(function(){throw Error("Attempted to call UserProfile() from the server but UserProfile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","UserProfile"),h=(0,o.registerClientReference)(function(){throw Error("Attempted to call Waitlist() from the server but Waitlist is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\node_modules\\@clerk\\nextjs\\dist\\esm\\client-boundary\\uiComponents.js","Waitlist")}};
//# sourceMappingURL=7988.js.map