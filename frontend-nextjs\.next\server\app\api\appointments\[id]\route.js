try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="1a6f3f1a-28f0-4c57-9bcc-93f103e2a055",e._sentryDebugIdIdentifier="sentry-dbid-1a6f3f1a-28f0-4c57-9bcc-93f103e2a055")}catch(e){}"use strict";(()=>{var e={};e.id=1074,e.ids=[1074],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},17950:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>E,serverHooks:()=>P,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>b});var o={};t.r(o),t.d(o,{DELETE:()=>v,GET:()=>f,HEAD:()=>A,OPTIONS:()=>k,PATCH:()=>w,POST:()=>m,PUT:()=>g});var n=t(86047),s=t(85544),i=t(36135),p=t(63033),a=t(53547),d=t(54360),u=t(19761);let c=(0,a.ZA)(async(e,r,{params:t})=>{try{let r=(0,d.o)(e),o=await r.getAppointment(t.id);return(0,a.$y)(o)}catch(e){return console.error("Error fetching appointment:",e),(0,a.WX)("Failed to fetch appointment")}}),l=(0,a.ZA)(async(e,r,{params:t})=>{try{let o=(0,d.o)(e),n=await r.json(),s=await o.updateAppointment(t.id,n);return(0,a.$y)(s)}catch(e){return console.error("Error updating appointment:",e),(0,a.WX)("Failed to update appointment")}}),x=(0,a.ZA)(async(e,r,{params:t})=>{try{let r=(0,d.o)(e);return await r.deleteAppointment(t.id),(0,a.$y)(null,204)}catch(e){return console.error("Error deleting appointment:",e),(0,a.WX)("Failed to delete appointment")}}),q={...p},y="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function h(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,o)=>{let n;try{let e=y?.getStore();n=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/appointments/[id]",headers:n}).apply(t,o)}})}let f=h(c,"GET"),m=h(void 0,"POST"),g=h(void 0,"PUT"),w=h(l,"PATCH"),v=h(x,"DELETE"),A=h(void 0,"HEAD"),k=h(void 0,"OPTIONS"),E=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/appointments/[id]/route",pathname:"/api/appointments/[id]",filename:"route",bundlePath:"app/api/appointments/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\appointments\\[id]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:T,workUnitAsyncStorage:b,serverHooks:P}=E;function _(){return(0,i.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:b})}},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[55,3738,1950,5886,9615,125],()=>t(17950));module.exports=o})();
//# sourceMappingURL=route.js.map