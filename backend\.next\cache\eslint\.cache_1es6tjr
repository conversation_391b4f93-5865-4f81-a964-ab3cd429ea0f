[{"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\importMap.js": "3", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx": "4", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql-playground\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\[...slug]\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\[id]\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments-direct\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\[id]\\route.ts": "14", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\generate-from-appointment\\route.ts": "15", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\route.ts": "16", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\db-test\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\apply-to-bill\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\refund\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\[id]\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\convert-to-patient\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\[id]\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\receipt\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\daily-revenue\\route.ts": "29", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\financial\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\monthly-revenue\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\outstanding-balances\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reset-db\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\test\\route.ts": "34", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\route.ts": "35", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\[id]\\route.ts": "36", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\route.ts": "37", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\[id]\\route.ts": "38", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\my-route\\route.ts": "39", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Appointments.ts": "40", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\BillItems.ts": "41", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Bills.ts": "42", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Deposits.ts": "43", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Media.ts": "44", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Patients.ts": "45", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Payments.ts": "46", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Treatments.ts": "47", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Users.ts": "48", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\auth-sync.ts": "49", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\clerk-auth-strategy.ts": "50", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\payload-auth-middleware.ts": "51", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload-types.ts": "52", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload.config.ts": "53", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\bulk-operations\\route.ts": "54", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\process-payment\\route.ts": "55", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\analytics\\route.ts": "56", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\PatientInteractions.ts": "57", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\PatientTasks.ts": "58"}, {"size": 400, "mtime": 1752009631000, "results": "59", "hashOfConfig": "60"}, {"size": 1847, "mtime": 1752048389803, "results": "61", "hashOfConfig": "60"}, {"size": 5898, "mtime": 1752016400380, "results": "62", "hashOfConfig": "63"}, {"size": 731, "mtime": 1752009631000, "results": "64", "hashOfConfig": "60"}, {"size": 715, "mtime": 1752009631000, "results": "65", "hashOfConfig": "60"}, {"size": 315, "mtime": 1752009631000, "results": "66", "hashOfConfig": "60"}, {"size": 305, "mtime": 1752009631000, "results": "67", "hashOfConfig": "60"}, {"size": 550, "mtime": 1752009631000, "results": "68", "hashOfConfig": "60"}, {"size": 810, "mtime": 1752009631000, "results": "69", "hashOfConfig": "60"}, {"size": 2212, "mtime": 1752032650690, "results": "70", "hashOfConfig": "60"}, {"size": 3171, "mtime": 1752037068700, "results": "71", "hashOfConfig": "60"}, {"size": 1346, "mtime": 1752018809145, "results": "72", "hashOfConfig": "60"}, {"size": 3305, "mtime": **********005, "results": "73", "hashOfConfig": "60"}, {"size": 3783, "mtime": 1752042973170, "results": "74", "hashOfConfig": "60"}, {"size": 4926, "mtime": 1752117128123, "results": "75", "hashOfConfig": "60"}, {"size": 3707, "mtime": 1752048176586, "results": "76", "hashOfConfig": "60"}, {"size": 3921, "mtime": 1752042932094, "results": "77", "hashOfConfig": "60"}, {"size": 777, "mtime": 1752018791568, "results": "78", "hashOfConfig": "60"}, {"size": 4765, "mtime": 1752088274625, "results": "79", "hashOfConfig": "60"}, {"size": 5064, "mtime": 1752092473781, "results": "80", "hashOfConfig": "60"}, {"size": 3266, "mtime": 1752048189770, "results": "81", "hashOfConfig": "60"}, {"size": 3767, "mtime": 1752043056882, "results": "82", "hashOfConfig": "60"}, {"size": 2308, "mtime": 1752049995084, "results": "83", "hashOfConfig": "60"}, {"size": 2459, "mtime": 1752032674926, "results": "84", "hashOfConfig": "60"}, {"size": 3000, "mtime": 1752037001759, "results": "85", "hashOfConfig": "60"}, {"size": 3659, "mtime": 1752048214156, "results": "86", "hashOfConfig": "60"}, {"size": 5249, "mtime": 1752092952466, "results": "87", "hashOfConfig": "60"}, {"size": 3559, "mtime": 1752043015028, "results": "88", "hashOfConfig": "60"}, {"size": 3350, "mtime": 1752093175446, "results": "89", "hashOfConfig": "60"}, {"size": 5716, "mtime": 1752094372092, "results": "90", "hashOfConfig": "60"}, {"size": 3974, "mtime": 1752093206347, "results": "91", "hashOfConfig": "60"}, {"size": 4250, "mtime": 1752093216697, "results": "92", "hashOfConfig": "60"}, {"size": 1029, "mtime": 1752018919310, "results": "93", "hashOfConfig": "60"}, {"size": 231, "mtime": 1752018622704, "results": "94", "hashOfConfig": "60"}, {"size": 2022, "mtime": 1752032698666, "results": "95", "hashOfConfig": "60"}, {"size": 3036, "mtime": 1752037033875, "results": "96", "hashOfConfig": "60"}, {"size": 1952, "mtime": 1752032614878, "results": "97", "hashOfConfig": "60"}, {"size": 2946, "mtime": 1752036952764, "results": "98", "hashOfConfig": "60"}, {"size": 289, "mtime": 1752048092114, "results": "99", "hashOfConfig": "60"}, {"size": 6433, "mtime": 1752042577243, "results": "100", "hashOfConfig": "60"}, {"size": 6852, "mtime": 1752098003242, "results": "101", "hashOfConfig": "60"}, {"size": 10831, "mtime": 1752118495865, "results": "102", "hashOfConfig": "60"}, {"size": 7285, "mtime": 1752083936008, "results": "103", "hashOfConfig": "60"}, {"size": 255, "mtime": 1752009631000, "results": "104", "hashOfConfig": "60"}, {"size": 6672, "mtime": 1752042473052, "results": "105", "hashOfConfig": "60"}, {"size": 9283, "mtime": 1752094498002, "results": "106", "hashOfConfig": "60"}, {"size": 1401, "mtime": 1752025370327, "results": "107", "hashOfConfig": "60"}, {"size": 2394, "mtime": 1752025408079, "results": "108", "hashOfConfig": "60"}, {"size": 2856, "mtime": 1752044417674, "results": "109", "hashOfConfig": "60"}, {"size": 951, "mtime": 1752044472492, "results": "110", "hashOfConfig": "60"}, {"size": 2700, "mtime": 1752050933716, "results": "111", "hashOfConfig": "60"}, {"size": 23790, "mtime": 1752107010079, "results": "112", "hashOfConfig": "60"}, {"size": 1626, "mtime": 1752106966966, "results": "113", "hashOfConfig": "60"}, {"size": 9524, "mtime": 1752117553548, "results": "114", "hashOfConfig": "60"}, {"size": 7432, "mtime": 1752118055198, "results": "115", "hashOfConfig": "60"}, {"size": 9213, "mtime": 1752118401258, "results": "116", "hashOfConfig": "60"}, {"size": 7431, "mtime": 1752118584070, "results": "117", "hashOfConfig": "60"}, {"size": 7321, "mtime": 1752106946886, "results": "118", "hashOfConfig": "60"}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e1u0sl", {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pn1qvw", {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 35, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\importMap.js", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql-playground\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\[...slug]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments-direct\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\generate-from-appointment\\route.ts", ["293", "294", "295", "296"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\db-test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\apply-to-bill\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\refund\\route.ts", ["297", "298", "299", "300", "301", "302", "303", "304", "305"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\convert-to-patient\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\receipt\\route.ts", ["306", "307", "308", "309", "310", "311", "312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\daily-revenue\\route.ts", ["330", "331", "332", "333", "334", "335", "336", "337"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\financial\\route.ts", ["338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349", "350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "360", "361", "362", "363", "364", "365", "366", "367", "368", "369", "370", "371", "372"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\monthly-revenue\\route.ts", ["373", "374", "375", "376", "377", "378", "379", "380"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\outstanding-balances\\route.ts", ["381", "382", "383", "384", "385"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reset-db\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\my-route\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Appointments.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\BillItems.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Bills.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Deposits.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Media.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Patients.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Payments.ts", ["386"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Treatments.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Users.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\auth-sync.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\clerk-auth-strategy.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\payload-auth-middleware.ts", ["387", "388", "389", "390"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload-types.ts", [], ["391", "392", "393", "394", "395"], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload.config.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\bulk-operations\\route.ts", ["396", "397", "398", "399", "400", "401", "402", "403"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\process-payment\\route.ts", ["404", "405", "406", "407", "408", "409", "410"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\analytics\\route.ts", ["411", "412", "413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423", "424", "425", "426", "427", "428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438", "439", "440", "441", "442", "443", "444", "445", "446", "447", "448", "449", "450"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\PatientInteractions.ts", ["451", "452", "453"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\PatientTasks.ts", [], [], {"ruleId": "454", "severity": 1, "message": "455", "line": 24, "column": 7, "nodeType": null, "messageId": "456", "endLine": 24, "endColumn": 20}, {"ruleId": "454", "severity": 1, "message": "457", "line": 25, "column": 7, "nodeType": null, "messageId": "456", "endLine": 25, "endColumn": 19}, {"ruleId": "454", "severity": 1, "message": "458", "line": 28, "column": 7, "nodeType": null, "messageId": "456", "endLine": 28, "endColumn": 12}, {"ruleId": "454", "severity": 1, "message": "459", "line": 30, "column": 7, "nodeType": null, "messageId": "456", "endLine": 30, "endColumn": 24}, {"ruleId": "460", "severity": 1, "message": "461", "line": 52, "column": 10, "nodeType": "462", "messageId": "463", "endLine": 52, "endColumn": 13, "suggestions": "464"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 62, "column": 44, "nodeType": "462", "messageId": "463", "endLine": 62, "endColumn": 47, "suggestions": "465"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 62, "column": 81, "nodeType": "462", "messageId": "463", "endLine": 62, "endColumn": 84, "suggestions": "466"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 62, "column": 108, "nodeType": "462", "messageId": "463", "endLine": 62, "endColumn": 111, "suggestions": "467"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 140, "column": 41, "nodeType": "462", "messageId": "463", "endLine": 140, "endColumn": 44, "suggestions": "468"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 141, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 141, "endColumn": 43, "suggestions": "469"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 142, "column": 37, "nodeType": "462", "messageId": "463", "endLine": 142, "endColumn": 40, "suggestions": "470"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 150, "column": 31, "nodeType": "462", "messageId": "463", "endLine": 150, "endColumn": 34, "suggestions": "471"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 151, "column": 42, "nodeType": "462", "messageId": "463", "endLine": 151, "endColumn": 45, "suggestions": "472"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 43, "column": 21, "nodeType": "462", "messageId": "463", "endLine": 43, "endColumn": 24, "suggestions": "473"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 52, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 52, "endColumn": 37, "suggestions": "474"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 53, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 53, "endColumn": 37, "suggestions": "475"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 54, "column": 32, "nodeType": "462", "messageId": "463", "endLine": 54, "endColumn": 35, "suggestions": "476"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 55, "column": 27, "nodeType": "462", "messageId": "463", "endLine": 55, "endColumn": 30, "suggestions": "477"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 56, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 56, "endColumn": 37, "suggestions": "478"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 60, "column": 33, "nodeType": "462", "messageId": "463", "endLine": 60, "endColumn": 36, "suggestions": "479"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 61, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 61, "endColumn": 37, "suggestions": "480"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 62, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 62, "endColumn": 37, "suggestions": "481"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 63, "column": 38, "nodeType": "462", "messageId": "463", "endLine": 63, "endColumn": 41, "suggestions": "482"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 68, "column": 31, "nodeType": "462", "messageId": "463", "endLine": 68, "endColumn": 34, "suggestions": "483"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 69, "column": 28, "nodeType": "462", "messageId": "463", "endLine": 69, "endColumn": 31, "suggestions": "484"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 70, "column": 28, "nodeType": "462", "messageId": "463", "endLine": 70, "endColumn": 31, "suggestions": "485"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 75, "column": 32, "nodeType": "462", "messageId": "463", "endLine": 75, "endColumn": 35, "suggestions": "486"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 76, "column": 31, "nodeType": "462", "messageId": "463", "endLine": 76, "endColumn": 34, "suggestions": "487"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 77, "column": 28, "nodeType": "462", "messageId": "463", "endLine": 77, "endColumn": 31, "suggestions": "488"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 88, "column": 26, "nodeType": "462", "messageId": "463", "endLine": 88, "endColumn": 29, "suggestions": "489"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 89, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 89, "endColumn": 37, "suggestions": "490"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 92, "column": 35, "nodeType": "462", "messageId": "463", "endLine": 92, "endColumn": 38, "suggestions": "491"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 93, "column": 36, "nodeType": "462", "messageId": "463", "endLine": 93, "endColumn": 39, "suggestions": "492"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 94, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 94, "endColumn": 37, "suggestions": "493"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 100, "column": 41, "nodeType": "462", "messageId": "463", "endLine": 100, "endColumn": 44, "suggestions": "494"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 101, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 101, "endColumn": 43, "suggestions": "495"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 102, "column": 37, "nodeType": "462", "messageId": "463", "endLine": 102, "endColumn": 40, "suggestions": "496"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 75, "column": 39, "nodeType": "462", "messageId": "463", "endLine": 75, "endColumn": 42, "suggestions": "497"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 75, "column": 79, "nodeType": "462", "messageId": "463", "endLine": 75, "endColumn": 82, "suggestions": "498"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 76, "column": 39, "nodeType": "462", "messageId": "463", "endLine": 76, "endColumn": 42, "suggestions": "499"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 81, "column": 18, "nodeType": "462", "messageId": "463", "endLine": 81, "endColumn": 21, "suggestions": "500"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 81, "column": 46, "nodeType": "462", "messageId": "463", "endLine": 81, "endColumn": 49, "suggestions": "501"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 97, "column": 41, "nodeType": "462", "messageId": "463", "endLine": 97, "endColumn": 44, "suggestions": "502"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 98, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 98, "endColumn": 43, "suggestions": "503"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 99, "column": 37, "nodeType": "462", "messageId": "463", "endLine": 99, "endColumn": 40, "suggestions": "504"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 83, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 83, "endColumn": 37, "suggestions": "505"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 83, "column": 71, "nodeType": "462", "messageId": "463", "endLine": 83, "endColumn": 74, "suggestions": "506"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 84, "column": 33, "nodeType": "462", "messageId": "463", "endLine": 84, "endColumn": 36, "suggestions": "507"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 84, "column": 70, "nodeType": "462", "messageId": "463", "endLine": 84, "endColumn": 73, "suggestions": "508"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 85, "column": 38, "nodeType": "462", "messageId": "463", "endLine": 85, "endColumn": 41, "suggestions": "509"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 85, "column": 75, "nodeType": "462", "messageId": "463", "endLine": 85, "endColumn": 78, "suggestions": "510"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 87, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 87, "endColumn": 43, "suggestions": "511"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 87, "column": 80, "nodeType": "462", "messageId": "463", "endLine": 87, "endColumn": 83, "suggestions": "512"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 89, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 89, "endColumn": 43, "suggestions": "513"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 89, "column": 80, "nodeType": "462", "messageId": "463", "endLine": 89, "endColumn": 83, "suggestions": "514"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 90, "column": 39, "nodeType": "462", "messageId": "463", "endLine": 90, "endColumn": 42, "suggestions": "515"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 90, "column": 79, "nodeType": "462", "messageId": "463", "endLine": 90, "endColumn": 82, "suggestions": "516"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 91, "column": 44, "nodeType": "462", "messageId": "463", "endLine": 91, "endColumn": 47, "suggestions": "517"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 91, "column": 84, "nodeType": "462", "messageId": "463", "endLine": 91, "endColumn": 87, "suggestions": "518"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 94, "column": 49, "nodeType": "462", "messageId": "463", "endLine": 94, "endColumn": 52, "suggestions": "519"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 94, "column": 72, "nodeType": "462", "messageId": "463", "endLine": 94, "endColumn": 75, "suggestions": "520"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 94, "column": 86, "nodeType": "462", "messageId": "463", "endLine": 94, "endColumn": 89, "suggestions": "521"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 101, "column": 43, "nodeType": "462", "messageId": "463", "endLine": 101, "endColumn": 46, "suggestions": "522"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 101, "column": 66, "nodeType": "462", "messageId": "463", "endLine": 101, "endColumn": 69, "suggestions": "523"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 101, "column": 77, "nodeType": "462", "messageId": "463", "endLine": 101, "endColumn": 80, "suggestions": "524"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 108, "column": 41, "nodeType": "462", "messageId": "463", "endLine": 108, "endColumn": 44, "suggestions": "525"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 108, "column": 64, "nodeType": "462", "messageId": "463", "endLine": 108, "endColumn": 67, "suggestions": "526"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 108, "column": 75, "nodeType": "462", "messageId": "463", "endLine": 108, "endColumn": 78, "suggestions": "527"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 121, "column": 28, "nodeType": "462", "messageId": "463", "endLine": 121, "endColumn": 31, "suggestions": "528"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 128, "column": 31, "nodeType": "462", "messageId": "463", "endLine": 128, "endColumn": 34, "suggestions": "529"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 130, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 130, "endColumn": 43, "suggestions": "530"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 130, "column": 91, "nodeType": "462", "messageId": "463", "endLine": 130, "endColumn": 94, "suggestions": "531"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 133, "column": 31, "nodeType": "462", "messageId": "463", "endLine": 133, "endColumn": 34, "suggestions": "532"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 147, "column": 41, "nodeType": "462", "messageId": "463", "endLine": 147, "endColumn": 44, "suggestions": "533"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 148, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 148, "endColumn": 43, "suggestions": "534"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 149, "column": 37, "nodeType": "462", "messageId": "463", "endLine": 149, "endColumn": 40, "suggestions": "535"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 155, "column": 22, "nodeType": "462", "messageId": "463", "endLine": 155, "endColumn": 25, "suggestions": "536"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 156, "column": 26, "nodeType": "462", "messageId": "463", "endLine": 156, "endColumn": 29, "suggestions": "537"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 157, "column": 32, "nodeType": "462", "messageId": "463", "endLine": 157, "endColumn": 35, "suggestions": "538"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 158, "column": 32, "nodeType": "462", "messageId": "463", "endLine": 158, "endColumn": 35, "suggestions": "539"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 74, "column": 39, "nodeType": "462", "messageId": "463", "endLine": 74, "endColumn": 42, "suggestions": "540"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 74, "column": 79, "nodeType": "462", "messageId": "463", "endLine": 74, "endColumn": 82, "suggestions": "541"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 88, "column": 18, "nodeType": "462", "messageId": "463", "endLine": 88, "endColumn": 21, "suggestions": "542"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 88, "column": 46, "nodeType": "462", "messageId": "463", "endLine": 88, "endColumn": 49, "suggestions": "543"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 108, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 108, "endColumn": 37, "suggestions": "544"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 113, "column": 41, "nodeType": "462", "messageId": "463", "endLine": 113, "endColumn": 44, "suggestions": "545"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 114, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 114, "endColumn": 43, "suggestions": "546"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 115, "column": 37, "nodeType": "462", "messageId": "463", "endLine": 115, "endColumn": 40, "suggestions": "547"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 63, "column": 15, "nodeType": "462", "messageId": "463", "endLine": 63, "endColumn": 18, "suggestions": "548"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 63, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 63, "endColumn": 43, "suggestions": "549"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 118, "column": 41, "nodeType": "462", "messageId": "463", "endLine": 118, "endColumn": 44, "suggestions": "550"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 119, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 119, "endColumn": 43, "suggestions": "551"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 120, "column": 37, "nodeType": "462", "messageId": "463", "endLine": 120, "endColumn": 40, "suggestions": "552"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 91, "column": 27, "nodeType": "462", "messageId": "463", "endLine": 91, "endColumn": 30, "suggestions": "553"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 78, "column": 12, "nodeType": "462", "messageId": "463", "endLine": 78, "endColumn": 15, "suggestions": "554"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 85, "column": 12, "nodeType": "462", "messageId": "463", "endLine": 85, "endColumn": 15, "suggestions": "555"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 92, "column": 12, "nodeType": "462", "messageId": "463", "endLine": 92, "endColumn": 15, "suggestions": "556"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 99, "column": 12, "nodeType": "462", "messageId": "463", "endLine": 99, "endColumn": 15, "suggestions": "557"}, {"ruleId": "558", "severity": 1, "message": "559", "line": 68, "column": 11, "nodeType": "560", "messageId": "561", "endLine": 68, "endColumn": 13, "suggestions": "562", "suppressions": "563"}, {"ruleId": "558", "severity": 1, "message": "559", "line": 85, "column": 21, "nodeType": "560", "messageId": "561", "endLine": 85, "endColumn": 23, "suggestions": "564", "suppressions": "565"}, {"ruleId": "558", "severity": 1, "message": "559", "line": 105, "column": 12, "nodeType": "560", "messageId": "561", "endLine": 105, "endColumn": 14, "suggestions": "566", "suppressions": "567"}, {"ruleId": "558", "severity": 1, "message": "559", "line": 106, "column": 18, "nodeType": "560", "messageId": "561", "endLine": 106, "endColumn": 20, "suggestions": "568", "suppressions": "569"}, {"ruleId": "558", "severity": 1, "message": "570", "line": 1055, "column": 20, "nodeType": "571", "messageId": "572", "endLine": 1055, "endColumn": 34, "suggestions": "573", "suppressions": "574"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 37, "column": 25, "nodeType": "462", "messageId": "463", "endLine": 37, "endColumn": 28, "suggestions": "575"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 38, "column": 21, "nodeType": "462", "messageId": "463", "endLine": 38, "endColumn": 24, "suggestions": "576"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 110, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 110, "endColumn": 43, "suggestions": "577"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 166, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 166, "endColumn": 43, "suggestions": "578"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 211, "column": 72, "nodeType": "462", "messageId": "463", "endLine": 211, "endColumn": 75, "suggestions": "579"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 218, "column": 36, "nodeType": "462", "messageId": "463", "endLine": 218, "endColumn": 39, "suggestions": "580"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 219, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 219, "endColumn": 37, "suggestions": "581"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 220, "column": 37, "nodeType": "462", "messageId": "463", "endLine": 220, "endColumn": 40, "suggestions": "582"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 85, "column": 32, "nodeType": "462", "messageId": "463", "endLine": 85, "endColumn": 35, "suggestions": "583"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 110, "column": 42, "nodeType": "462", "messageId": "463", "endLine": 110, "endColumn": 45, "suggestions": "584"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 140, "column": 17, "nodeType": "462", "messageId": "463", "endLine": 140, "endColumn": 20, "suggestions": "585"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 141, "column": 28, "nodeType": "462", "messageId": "463", "endLine": 141, "endColumn": 31, "suggestions": "586"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 142, "column": 20, "nodeType": "462", "messageId": "463", "endLine": 142, "endColumn": 23, "suggestions": "587"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 143, "column": 17, "nodeType": "462", "messageId": "463", "endLine": 143, "endColumn": 20, "suggestions": "588"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 172, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 172, "endColumn": 43, "suggestions": "589"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 71, "column": 34, "nodeType": "462", "messageId": "463", "endLine": 71, "endColumn": 37, "suggestions": "590"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 72, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 72, "endColumn": 43, "suggestions": "591"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 73, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 73, "endColumn": 43, "suggestions": "592"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 74, "column": 48, "nodeType": "462", "messageId": "463", "endLine": 74, "endColumn": 51, "suggestions": "593"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 75, "column": 40, "nodeType": "462", "messageId": "463", "endLine": 75, "endColumn": 43, "suggestions": "594"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 79, "column": 65, "nodeType": "462", "messageId": "463", "endLine": 79, "endColumn": 68, "suggestions": "595"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 81, "column": 53, "nodeType": "462", "messageId": "463", "endLine": 81, "endColumn": 56, "suggestions": "596"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 82, "column": 58, "nodeType": "462", "messageId": "463", "endLine": 82, "endColumn": 61, "suggestions": "597"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 83, "column": 63, "nodeType": "462", "messageId": "463", "endLine": 83, "endColumn": 66, "suggestions": "598"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 84, "column": 61, "nodeType": "462", "messageId": "463", "endLine": 84, "endColumn": 64, "suggestions": "599"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 85, "column": 54, "nodeType": "462", "messageId": "463", "endLine": 85, "endColumn": 57, "suggestions": "600"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 86, "column": 48, "nodeType": "462", "messageId": "463", "endLine": 86, "endColumn": 51, "suggestions": "601"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 90, "column": 31, "nodeType": "462", "messageId": "463", "endLine": 90, "endColumn": 34, "suggestions": "602"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 91, "column": 37, "nodeType": "462", "messageId": "463", "endLine": 91, "endColumn": 40, "suggestions": "603"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 101, "column": 28, "nodeType": "462", "messageId": "463", "endLine": 101, "endColumn": 31, "suggestions": "604"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 102, "column": 31, "nodeType": "462", "messageId": "463", "endLine": 102, "endColumn": 34, "suggestions": "605"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 112, "column": 27, "nodeType": "462", "messageId": "463", "endLine": 112, "endColumn": 30, "suggestions": "606"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 113, "column": 31, "nodeType": "462", "messageId": "463", "endLine": 113, "endColumn": 34, "suggestions": "607"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 124, "column": 66, "nodeType": "462", "messageId": "463", "endLine": 124, "endColumn": 69, "suggestions": "608"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 125, "column": 70, "nodeType": "462", "messageId": "463", "endLine": 125, "endColumn": 73, "suggestions": "609"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 126, "column": 75, "nodeType": "462", "messageId": "463", "endLine": 126, "endColumn": 78, "suggestions": "610"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 127, "column": 68, "nodeType": "462", "messageId": "463", "endLine": 127, "endColumn": 71, "suggestions": "611"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 128, "column": 54, "nodeType": "462", "messageId": "463", "endLine": 128, "endColumn": 57, "suggestions": "612"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 129, "column": 54, "nodeType": "462", "messageId": "463", "endLine": 129, "endColumn": 57, "suggestions": "613"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 134, "column": 45, "nodeType": "462", "messageId": "463", "endLine": 134, "endColumn": 48, "suggestions": "614"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 135, "column": 51, "nodeType": "462", "messageId": "463", "endLine": 135, "endColumn": 54, "suggestions": "615"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 136, "column": 48, "nodeType": "462", "messageId": "463", "endLine": 136, "endColumn": 51, "suggestions": "616"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 137, "column": 35, "nodeType": "462", "messageId": "463", "endLine": 137, "endColumn": 38, "suggestions": "617"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 138, "column": 35, "nodeType": "462", "messageId": "463", "endLine": 138, "endColumn": 38, "suggestions": "618"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 145, "column": 62, "nodeType": "462", "messageId": "463", "endLine": 145, "endColumn": 65, "suggestions": "619"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 146, "column": 59, "nodeType": "462", "messageId": "463", "endLine": 146, "endColumn": 62, "suggestions": "620"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 147, "column": 59, "nodeType": "462", "messageId": "463", "endLine": 147, "endColumn": 62, "suggestions": "621"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 149, "column": 39, "nodeType": "462", "messageId": "463", "endLine": 149, "endColumn": 42, "suggestions": "622"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 167, "column": 37, "nodeType": "462", "messageId": "463", "endLine": 167, "endColumn": 40, "suggestions": "623"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 175, "column": 31, "nodeType": "462", "messageId": "463", "endLine": 175, "endColumn": 34, "suggestions": "624"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 182, "column": 45, "nodeType": "462", "messageId": "463", "endLine": 182, "endColumn": 48, "suggestions": "625"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 191, "column": 67, "nodeType": "462", "messageId": "463", "endLine": 191, "endColumn": 70, "suggestions": "626"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 191, "column": 95, "nodeType": "462", "messageId": "463", "endLine": 191, "endColumn": 98, "suggestions": "627"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 193, "column": 63, "nodeType": "462", "messageId": "463", "endLine": 193, "endColumn": 66, "suggestions": "628"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 193, "column": 91, "nodeType": "462", "messageId": "463", "endLine": 193, "endColumn": 94, "suggestions": "629"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 33, "column": 14, "nodeType": "462", "messageId": "463", "endLine": 33, "endColumn": 17, "suggestions": "630"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 42, "column": 14, "nodeType": "462", "messageId": "463", "endLine": 42, "endColumn": 17, "suggestions": "631"}, {"ruleId": "460", "severity": 1, "message": "461", "line": 65, "column": 12, "nodeType": "462", "messageId": "463", "endLine": 65, "endColumn": 15, "suggestions": "632"}, "@typescript-eslint/no-unused-vars", "'applyDiscount' is assigned a value but never used. Allowed unused vars must match /^_/u.", "unusedVar", "'discountRate' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'notes' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'autoApplyDeposits' is assigned a value but never used. Allowed unused vars must match /^_/u.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["633", "634"], ["635", "636"], ["637", "638"], ["639", "640"], ["641", "642"], ["643", "644"], ["645", "646"], ["647", "648"], ["649", "650"], ["651", "652"], ["653", "654"], ["655", "656"], ["657", "658"], ["659", "660"], ["661", "662"], ["663", "664"], ["665", "666"], ["667", "668"], ["669", "670"], ["671", "672"], ["673", "674"], ["675", "676"], ["677", "678"], ["679", "680"], ["681", "682"], ["683", "684"], ["685", "686"], ["687", "688"], ["689", "690"], ["691", "692"], ["693", "694"], ["695", "696"], ["697", "698"], ["699", "700"], ["701", "702"], ["703", "704"], ["705", "706"], ["707", "708"], ["709", "710"], ["711", "712"], ["713", "714"], ["715", "716"], ["717", "718"], ["719", "720"], ["721", "722"], ["723", "724"], ["725", "726"], ["727", "728"], ["729", "730"], ["731", "732"], ["733", "734"], ["735", "736"], ["737", "738"], ["739", "740"], ["741", "742"], ["743", "744"], ["745", "746"], ["747", "748"], ["749", "750"], ["751", "752"], ["753", "754"], ["755", "756"], ["757", "758"], ["759", "760"], ["761", "762"], ["763", "764"], ["765", "766"], ["767", "768"], ["769", "770"], ["771", "772"], ["773", "774"], ["775", "776"], ["777", "778"], ["779", "780"], ["781", "782"], ["783", "784"], ["785", "786"], ["787", "788"], ["789", "790"], ["791", "792"], ["793", "794"], ["795", "796"], ["797", "798"], ["799", "800"], ["801", "802"], ["803", "804"], ["805", "806"], ["807", "808"], ["809", "810"], ["811", "812"], ["813", "814"], ["815", "816"], ["817", "818"], ["819", "820"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["821", "822"], ["823"], ["824", "825"], ["826"], ["827", "828"], ["829"], ["830", "831"], ["832"], "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["833"], ["834"], ["835", "836"], ["837", "838"], ["839", "840"], ["841", "842"], ["843", "844"], ["845", "846"], ["847", "848"], ["849", "850"], ["851", "852"], ["853", "854"], ["855", "856"], ["857", "858"], ["859", "860"], ["861", "862"], ["863", "864"], ["865", "866"], ["867", "868"], ["869", "870"], ["871", "872"], ["873", "874"], ["875", "876"], ["877", "878"], ["879", "880"], ["881", "882"], ["883", "884"], ["885", "886"], ["887", "888"], ["889", "890"], ["891", "892"], ["893", "894"], ["895", "896"], ["897", "898"], ["899", "900"], ["901", "902"], ["903", "904"], ["905", "906"], ["907", "908"], ["909", "910"], ["911", "912"], ["913", "914"], ["915", "916"], ["917", "918"], ["919", "920"], ["921", "922"], ["923", "924"], ["925", "926"], ["927", "928"], ["929", "930"], ["931", "932"], ["933", "934"], ["935", "936"], ["937", "938"], ["939", "940"], ["941", "942"], ["943", "944"], ["945", "946"], ["947", "948"], ["949", "950"], {"messageId": "951", "fix": "952", "desc": "953"}, {"messageId": "954", "fix": "955", "desc": "956"}, {"messageId": "951", "fix": "957", "desc": "953"}, {"messageId": "954", "fix": "958", "desc": "956"}, {"messageId": "951", "fix": "959", "desc": "953"}, {"messageId": "954", "fix": "960", "desc": "956"}, {"messageId": "951", "fix": "961", "desc": "953"}, {"messageId": "954", "fix": "962", "desc": "956"}, {"messageId": "951", "fix": "963", "desc": "953"}, {"messageId": "954", "fix": "964", "desc": "956"}, {"messageId": "951", "fix": "965", "desc": "953"}, {"messageId": "954", "fix": "966", "desc": "956"}, {"messageId": "951", "fix": "967", "desc": "953"}, {"messageId": "954", "fix": "968", "desc": "956"}, {"messageId": "951", "fix": "969", "desc": "953"}, {"messageId": "954", "fix": "970", "desc": "956"}, {"messageId": "951", "fix": "971", "desc": "953"}, {"messageId": "954", "fix": "972", "desc": "956"}, {"messageId": "951", "fix": "973", "desc": "953"}, {"messageId": "954", "fix": "974", "desc": "956"}, {"messageId": "951", "fix": "975", "desc": "953"}, {"messageId": "954", "fix": "976", "desc": "956"}, {"messageId": "951", "fix": "977", "desc": "953"}, {"messageId": "954", "fix": "978", "desc": "956"}, {"messageId": "951", "fix": "979", "desc": "953"}, {"messageId": "954", "fix": "980", "desc": "956"}, {"messageId": "951", "fix": "981", "desc": "953"}, {"messageId": "954", "fix": "982", "desc": "956"}, {"messageId": "951", "fix": "983", "desc": "953"}, {"messageId": "954", "fix": "984", "desc": "956"}, {"messageId": "951", "fix": "985", "desc": "953"}, {"messageId": "954", "fix": "986", "desc": "956"}, {"messageId": "951", "fix": "987", "desc": "953"}, {"messageId": "954", "fix": "988", "desc": "956"}, {"messageId": "951", "fix": "989", "desc": "953"}, {"messageId": "954", "fix": "990", "desc": "956"}, {"messageId": "951", "fix": "991", "desc": "953"}, {"messageId": "954", "fix": "992", "desc": "956"}, {"messageId": "951", "fix": "993", "desc": "953"}, {"messageId": "954", "fix": "994", "desc": "956"}, {"messageId": "951", "fix": "995", "desc": "953"}, {"messageId": "954", "fix": "996", "desc": "956"}, {"messageId": "951", "fix": "997", "desc": "953"}, {"messageId": "954", "fix": "998", "desc": "956"}, {"messageId": "951", "fix": "999", "desc": "953"}, {"messageId": "954", "fix": "1000", "desc": "956"}, {"messageId": "951", "fix": "1001", "desc": "953"}, {"messageId": "954", "fix": "1002", "desc": "956"}, {"messageId": "951", "fix": "1003", "desc": "953"}, {"messageId": "954", "fix": "1004", "desc": "956"}, {"messageId": "951", "fix": "1005", "desc": "953"}, {"messageId": "954", "fix": "1006", "desc": "956"}, {"messageId": "951", "fix": "1007", "desc": "953"}, {"messageId": "954", "fix": "1008", "desc": "956"}, {"messageId": "951", "fix": "1009", "desc": "953"}, {"messageId": "954", "fix": "1010", "desc": "956"}, {"messageId": "951", "fix": "1011", "desc": "953"}, {"messageId": "954", "fix": "1012", "desc": "956"}, {"messageId": "951", "fix": "1013", "desc": "953"}, {"messageId": "954", "fix": "1014", "desc": "956"}, {"messageId": "951", "fix": "1015", "desc": "953"}, {"messageId": "954", "fix": "1016", "desc": "956"}, {"messageId": "951", "fix": "1017", "desc": "953"}, {"messageId": "954", "fix": "1018", "desc": "956"}, {"messageId": "951", "fix": "1019", "desc": "953"}, {"messageId": "954", "fix": "1020", "desc": "956"}, {"messageId": "951", "fix": "1021", "desc": "953"}, {"messageId": "954", "fix": "1022", "desc": "956"}, {"messageId": "951", "fix": "1023", "desc": "953"}, {"messageId": "954", "fix": "1024", "desc": "956"}, {"messageId": "951", "fix": "1025", "desc": "953"}, {"messageId": "954", "fix": "1026", "desc": "956"}, {"messageId": "951", "fix": "1027", "desc": "953"}, {"messageId": "954", "fix": "1028", "desc": "956"}, {"messageId": "951", "fix": "1029", "desc": "953"}, {"messageId": "954", "fix": "1030", "desc": "956"}, {"messageId": "951", "fix": "1031", "desc": "953"}, {"messageId": "954", "fix": "1032", "desc": "956"}, {"messageId": "951", "fix": "1033", "desc": "953"}, {"messageId": "954", "fix": "1034", "desc": "956"}, {"messageId": "951", "fix": "1035", "desc": "953"}, {"messageId": "954", "fix": "1036", "desc": "956"}, {"messageId": "951", "fix": "1037", "desc": "953"}, {"messageId": "954", "fix": "1038", "desc": "956"}, {"messageId": "951", "fix": "1039", "desc": "953"}, {"messageId": "954", "fix": "1040", "desc": "956"}, {"messageId": "951", "fix": "1041", "desc": "953"}, {"messageId": "954", "fix": "1042", "desc": "956"}, {"messageId": "951", "fix": "1043", "desc": "953"}, {"messageId": "954", "fix": "1044", "desc": "956"}, {"messageId": "951", "fix": "1045", "desc": "953"}, {"messageId": "954", "fix": "1046", "desc": "956"}, {"messageId": "951", "fix": "1047", "desc": "953"}, {"messageId": "954", "fix": "1048", "desc": "956"}, {"messageId": "951", "fix": "1049", "desc": "953"}, {"messageId": "954", "fix": "1050", "desc": "956"}, {"messageId": "951", "fix": "1051", "desc": "953"}, {"messageId": "954", "fix": "1052", "desc": "956"}, {"messageId": "951", "fix": "1053", "desc": "953"}, {"messageId": "954", "fix": "1054", "desc": "956"}, {"messageId": "951", "fix": "1055", "desc": "953"}, {"messageId": "954", "fix": "1056", "desc": "956"}, {"messageId": "951", "fix": "1057", "desc": "953"}, {"messageId": "954", "fix": "1058", "desc": "956"}, {"messageId": "951", "fix": "1059", "desc": "953"}, {"messageId": "954", "fix": "1060", "desc": "956"}, {"messageId": "951", "fix": "1061", "desc": "953"}, {"messageId": "954", "fix": "1062", "desc": "956"}, {"messageId": "951", "fix": "1063", "desc": "953"}, {"messageId": "954", "fix": "1064", "desc": "956"}, {"messageId": "951", "fix": "1065", "desc": "953"}, {"messageId": "954", "fix": "1066", "desc": "956"}, {"messageId": "951", "fix": "1067", "desc": "953"}, {"messageId": "954", "fix": "1068", "desc": "956"}, {"messageId": "951", "fix": "1069", "desc": "953"}, {"messageId": "954", "fix": "1070", "desc": "956"}, {"messageId": "951", "fix": "1071", "desc": "953"}, {"messageId": "954", "fix": "1072", "desc": "956"}, {"messageId": "951", "fix": "1073", "desc": "953"}, {"messageId": "954", "fix": "1074", "desc": "956"}, {"messageId": "951", "fix": "1075", "desc": "953"}, {"messageId": "954", "fix": "1076", "desc": "956"}, {"messageId": "951", "fix": "1077", "desc": "953"}, {"messageId": "954", "fix": "1078", "desc": "956"}, {"messageId": "951", "fix": "1079", "desc": "953"}, {"messageId": "954", "fix": "1080", "desc": "956"}, {"messageId": "951", "fix": "1081", "desc": "953"}, {"messageId": "954", "fix": "1082", "desc": "956"}, {"messageId": "951", "fix": "1083", "desc": "953"}, {"messageId": "954", "fix": "1084", "desc": "956"}, {"messageId": "951", "fix": "1085", "desc": "953"}, {"messageId": "954", "fix": "1086", "desc": "956"}, {"messageId": "951", "fix": "1087", "desc": "953"}, {"messageId": "954", "fix": "1088", "desc": "956"}, {"messageId": "951", "fix": "1089", "desc": "953"}, {"messageId": "954", "fix": "1090", "desc": "956"}, {"messageId": "951", "fix": "1091", "desc": "953"}, {"messageId": "954", "fix": "1092", "desc": "956"}, {"messageId": "951", "fix": "1093", "desc": "953"}, {"messageId": "954", "fix": "1094", "desc": "956"}, {"messageId": "951", "fix": "1095", "desc": "953"}, {"messageId": "954", "fix": "1096", "desc": "956"}, {"messageId": "951", "fix": "1097", "desc": "953"}, {"messageId": "954", "fix": "1098", "desc": "956"}, {"messageId": "951", "fix": "1099", "desc": "953"}, {"messageId": "954", "fix": "1100", "desc": "956"}, {"messageId": "951", "fix": "1101", "desc": "953"}, {"messageId": "954", "fix": "1102", "desc": "956"}, {"messageId": "951", "fix": "1103", "desc": "953"}, {"messageId": "954", "fix": "1104", "desc": "956"}, {"messageId": "951", "fix": "1105", "desc": "953"}, {"messageId": "954", "fix": "1106", "desc": "956"}, {"messageId": "951", "fix": "1107", "desc": "953"}, {"messageId": "954", "fix": "1108", "desc": "956"}, {"messageId": "951", "fix": "1109", "desc": "953"}, {"messageId": "954", "fix": "1110", "desc": "956"}, {"messageId": "951", "fix": "1111", "desc": "953"}, {"messageId": "954", "fix": "1112", "desc": "956"}, {"messageId": "951", "fix": "1113", "desc": "953"}, {"messageId": "954", "fix": "1114", "desc": "956"}, {"messageId": "951", "fix": "1115", "desc": "953"}, {"messageId": "954", "fix": "1116", "desc": "956"}, {"messageId": "951", "fix": "1117", "desc": "953"}, {"messageId": "954", "fix": "1118", "desc": "956"}, {"messageId": "951", "fix": "1119", "desc": "953"}, {"messageId": "954", "fix": "1120", "desc": "956"}, {"messageId": "951", "fix": "1121", "desc": "953"}, {"messageId": "954", "fix": "1122", "desc": "956"}, {"messageId": "951", "fix": "1123", "desc": "953"}, {"messageId": "954", "fix": "1124", "desc": "956"}, {"messageId": "951", "fix": "1125", "desc": "953"}, {"messageId": "954", "fix": "1126", "desc": "956"}, {"messageId": "951", "fix": "1127", "desc": "953"}, {"messageId": "954", "fix": "1128", "desc": "956"}, {"messageId": "951", "fix": "1129", "desc": "953"}, {"messageId": "954", "fix": "1130", "desc": "956"}, {"messageId": "951", "fix": "1131", "desc": "953"}, {"messageId": "954", "fix": "1132", "desc": "956"}, {"messageId": "951", "fix": "1133", "desc": "953"}, {"messageId": "954", "fix": "1134", "desc": "956"}, {"messageId": "951", "fix": "1135", "desc": "953"}, {"messageId": "954", "fix": "1136", "desc": "956"}, {"messageId": "951", "fix": "1137", "desc": "953"}, {"messageId": "954", "fix": "1138", "desc": "956"}, {"messageId": "951", "fix": "1139", "desc": "953"}, {"messageId": "954", "fix": "1140", "desc": "956"}, {"messageId": "951", "fix": "1141", "desc": "953"}, {"messageId": "954", "fix": "1142", "desc": "956"}, {"messageId": "1143", "data": "1144", "fix": "1145", "desc": "1146"}, {"messageId": "1143", "data": "1147", "fix": "1148", "desc": "1149"}, {"kind": "1150", "justification": "1151"}, {"messageId": "1143", "data": "1152", "fix": "1153", "desc": "1146"}, {"messageId": "1143", "data": "1154", "fix": "1155", "desc": "1149"}, {"kind": "1150", "justification": "1151"}, {"messageId": "1143", "data": "1156", "fix": "1157", "desc": "1146"}, {"messageId": "1143", "data": "1158", "fix": "1159", "desc": "1149"}, {"kind": "1150", "justification": "1151"}, {"messageId": "1143", "data": "1160", "fix": "1161", "desc": "1146"}, {"messageId": "1143", "data": "1162", "fix": "1163", "desc": "1149"}, {"kind": "1150", "justification": "1151"}, {"messageId": "1164", "fix": "1165", "desc": "1166"}, {"kind": "1150", "justification": "1151"}, {"messageId": "951", "fix": "1167", "desc": "953"}, {"messageId": "954", "fix": "1168", "desc": "956"}, {"messageId": "951", "fix": "1169", "desc": "953"}, {"messageId": "954", "fix": "1170", "desc": "956"}, {"messageId": "951", "fix": "1171", "desc": "953"}, {"messageId": "954", "fix": "1172", "desc": "956"}, {"messageId": "951", "fix": "1173", "desc": "953"}, {"messageId": "954", "fix": "1174", "desc": "956"}, {"messageId": "951", "fix": "1175", "desc": "953"}, {"messageId": "954", "fix": "1176", "desc": "956"}, {"messageId": "951", "fix": "1177", "desc": "953"}, {"messageId": "954", "fix": "1178", "desc": "956"}, {"messageId": "951", "fix": "1179", "desc": "953"}, {"messageId": "954", "fix": "1180", "desc": "956"}, {"messageId": "951", "fix": "1181", "desc": "953"}, {"messageId": "954", "fix": "1182", "desc": "956"}, {"messageId": "951", "fix": "1183", "desc": "953"}, {"messageId": "954", "fix": "1184", "desc": "956"}, {"messageId": "951", "fix": "1185", "desc": "953"}, {"messageId": "954", "fix": "1186", "desc": "956"}, {"messageId": "951", "fix": "1187", "desc": "953"}, {"messageId": "954", "fix": "1188", "desc": "956"}, {"messageId": "951", "fix": "1189", "desc": "953"}, {"messageId": "954", "fix": "1190", "desc": "956"}, {"messageId": "951", "fix": "1191", "desc": "953"}, {"messageId": "954", "fix": "1192", "desc": "956"}, {"messageId": "951", "fix": "1193", "desc": "953"}, {"messageId": "954", "fix": "1194", "desc": "956"}, {"messageId": "951", "fix": "1195", "desc": "953"}, {"messageId": "954", "fix": "1196", "desc": "956"}, {"messageId": "951", "fix": "1197", "desc": "953"}, {"messageId": "954", "fix": "1198", "desc": "956"}, {"messageId": "951", "fix": "1199", "desc": "953"}, {"messageId": "954", "fix": "1200", "desc": "956"}, {"messageId": "951", "fix": "1201", "desc": "953"}, {"messageId": "954", "fix": "1202", "desc": "956"}, {"messageId": "951", "fix": "1203", "desc": "953"}, {"messageId": "954", "fix": "1204", "desc": "956"}, {"messageId": "951", "fix": "1205", "desc": "953"}, {"messageId": "954", "fix": "1206", "desc": "956"}, {"messageId": "951", "fix": "1207", "desc": "953"}, {"messageId": "954", "fix": "1208", "desc": "956"}, {"messageId": "951", "fix": "1209", "desc": "953"}, {"messageId": "954", "fix": "1210", "desc": "956"}, {"messageId": "951", "fix": "1211", "desc": "953"}, {"messageId": "954", "fix": "1212", "desc": "956"}, {"messageId": "951", "fix": "1213", "desc": "953"}, {"messageId": "954", "fix": "1214", "desc": "956"}, {"messageId": "951", "fix": "1215", "desc": "953"}, {"messageId": "954", "fix": "1216", "desc": "956"}, {"messageId": "951", "fix": "1217", "desc": "953"}, {"messageId": "954", "fix": "1218", "desc": "956"}, {"messageId": "951", "fix": "1219", "desc": "953"}, {"messageId": "954", "fix": "1220", "desc": "956"}, {"messageId": "951", "fix": "1221", "desc": "953"}, {"messageId": "954", "fix": "1222", "desc": "956"}, {"messageId": "951", "fix": "1223", "desc": "953"}, {"messageId": "954", "fix": "1224", "desc": "956"}, {"messageId": "951", "fix": "1225", "desc": "953"}, {"messageId": "954", "fix": "1226", "desc": "956"}, {"messageId": "951", "fix": "1227", "desc": "953"}, {"messageId": "954", "fix": "1228", "desc": "956"}, {"messageId": "951", "fix": "1229", "desc": "953"}, {"messageId": "954", "fix": "1230", "desc": "956"}, {"messageId": "951", "fix": "1231", "desc": "953"}, {"messageId": "954", "fix": "1232", "desc": "956"}, {"messageId": "951", "fix": "1233", "desc": "953"}, {"messageId": "954", "fix": "1234", "desc": "956"}, {"messageId": "951", "fix": "1235", "desc": "953"}, {"messageId": "954", "fix": "1236", "desc": "956"}, {"messageId": "951", "fix": "1237", "desc": "953"}, {"messageId": "954", "fix": "1238", "desc": "956"}, {"messageId": "951", "fix": "1239", "desc": "953"}, {"messageId": "954", "fix": "1240", "desc": "956"}, {"messageId": "951", "fix": "1241", "desc": "953"}, {"messageId": "954", "fix": "1242", "desc": "956"}, {"messageId": "951", "fix": "1243", "desc": "953"}, {"messageId": "954", "fix": "1244", "desc": "956"}, {"messageId": "951", "fix": "1245", "desc": "953"}, {"messageId": "954", "fix": "1246", "desc": "956"}, {"messageId": "951", "fix": "1247", "desc": "953"}, {"messageId": "954", "fix": "1248", "desc": "956"}, {"messageId": "951", "fix": "1249", "desc": "953"}, {"messageId": "954", "fix": "1250", "desc": "956"}, {"messageId": "951", "fix": "1251", "desc": "953"}, {"messageId": "954", "fix": "1252", "desc": "956"}, {"messageId": "951", "fix": "1253", "desc": "953"}, {"messageId": "954", "fix": "1254", "desc": "956"}, {"messageId": "951", "fix": "1255", "desc": "953"}, {"messageId": "954", "fix": "1256", "desc": "956"}, {"messageId": "951", "fix": "1257", "desc": "953"}, {"messageId": "954", "fix": "1258", "desc": "956"}, {"messageId": "951", "fix": "1259", "desc": "953"}, {"messageId": "954", "fix": "1260", "desc": "956"}, {"messageId": "951", "fix": "1261", "desc": "953"}, {"messageId": "954", "fix": "1262", "desc": "956"}, {"messageId": "951", "fix": "1263", "desc": "953"}, {"messageId": "954", "fix": "1264", "desc": "956"}, {"messageId": "951", "fix": "1265", "desc": "953"}, {"messageId": "954", "fix": "1266", "desc": "956"}, {"messageId": "951", "fix": "1267", "desc": "953"}, {"messageId": "954", "fix": "1268", "desc": "956"}, {"messageId": "951", "fix": "1269", "desc": "953"}, {"messageId": "954", "fix": "1270", "desc": "956"}, {"messageId": "951", "fix": "1271", "desc": "953"}, {"messageId": "954", "fix": "1272", "desc": "956"}, {"messageId": "951", "fix": "1273", "desc": "953"}, {"messageId": "954", "fix": "1274", "desc": "956"}, {"messageId": "951", "fix": "1275", "desc": "953"}, {"messageId": "954", "fix": "1276", "desc": "956"}, {"messageId": "951", "fix": "1277", "desc": "953"}, {"messageId": "954", "fix": "1278", "desc": "956"}, {"messageId": "951", "fix": "1279", "desc": "953"}, {"messageId": "954", "fix": "1280", "desc": "956"}, {"messageId": "951", "fix": "1281", "desc": "953"}, {"messageId": "954", "fix": "1282", "desc": "956"}, "suggestUnknown", {"range": "1283", "text": "1284"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1285", "text": "1286"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1287", "text": "1284"}, {"range": "1288", "text": "1286"}, {"range": "1289", "text": "1284"}, {"range": "1290", "text": "1286"}, {"range": "1291", "text": "1284"}, {"range": "1292", "text": "1286"}, {"range": "1293", "text": "1284"}, {"range": "1294", "text": "1286"}, {"range": "1295", "text": "1284"}, {"range": "1296", "text": "1286"}, {"range": "1297", "text": "1284"}, {"range": "1298", "text": "1286"}, {"range": "1299", "text": "1284"}, {"range": "1300", "text": "1286"}, {"range": "1301", "text": "1284"}, {"range": "1302", "text": "1286"}, {"range": "1303", "text": "1284"}, {"range": "1304", "text": "1286"}, {"range": "1305", "text": "1284"}, {"range": "1306", "text": "1286"}, {"range": "1307", "text": "1284"}, {"range": "1308", "text": "1286"}, {"range": "1309", "text": "1284"}, {"range": "1310", "text": "1286"}, {"range": "1311", "text": "1284"}, {"range": "1312", "text": "1286"}, {"range": "1313", "text": "1284"}, {"range": "1314", "text": "1286"}, {"range": "1315", "text": "1284"}, {"range": "1316", "text": "1286"}, {"range": "1317", "text": "1284"}, {"range": "1318", "text": "1286"}, {"range": "1319", "text": "1284"}, {"range": "1320", "text": "1286"}, {"range": "1321", "text": "1284"}, {"range": "1322", "text": "1286"}, {"range": "1323", "text": "1284"}, {"range": "1324", "text": "1286"}, {"range": "1325", "text": "1284"}, {"range": "1326", "text": "1286"}, {"range": "1327", "text": "1284"}, {"range": "1328", "text": "1286"}, {"range": "1329", "text": "1284"}, {"range": "1330", "text": "1286"}, {"range": "1331", "text": "1284"}, {"range": "1332", "text": "1286"}, {"range": "1333", "text": "1284"}, {"range": "1334", "text": "1286"}, {"range": "1335", "text": "1284"}, {"range": "1336", "text": "1286"}, {"range": "1337", "text": "1284"}, {"range": "1338", "text": "1286"}, {"range": "1339", "text": "1284"}, {"range": "1340", "text": "1286"}, {"range": "1341", "text": "1284"}, {"range": "1342", "text": "1286"}, {"range": "1343", "text": "1284"}, {"range": "1344", "text": "1286"}, {"range": "1345", "text": "1284"}, {"range": "1346", "text": "1286"}, {"range": "1347", "text": "1284"}, {"range": "1348", "text": "1286"}, {"range": "1349", "text": "1284"}, {"range": "1350", "text": "1286"}, {"range": "1351", "text": "1284"}, {"range": "1352", "text": "1286"}, {"range": "1353", "text": "1284"}, {"range": "1354", "text": "1286"}, {"range": "1355", "text": "1284"}, {"range": "1356", "text": "1286"}, {"range": "1357", "text": "1284"}, {"range": "1358", "text": "1286"}, {"range": "1359", "text": "1284"}, {"range": "1360", "text": "1286"}, {"range": "1361", "text": "1284"}, {"range": "1362", "text": "1286"}, {"range": "1363", "text": "1284"}, {"range": "1364", "text": "1286"}, {"range": "1365", "text": "1284"}, {"range": "1366", "text": "1286"}, {"range": "1367", "text": "1284"}, {"range": "1368", "text": "1286"}, {"range": "1369", "text": "1284"}, {"range": "1370", "text": "1286"}, {"range": "1371", "text": "1284"}, {"range": "1372", "text": "1286"}, {"range": "1373", "text": "1284"}, {"range": "1374", "text": "1286"}, {"range": "1375", "text": "1284"}, {"range": "1376", "text": "1286"}, {"range": "1377", "text": "1284"}, {"range": "1378", "text": "1286"}, {"range": "1379", "text": "1284"}, {"range": "1380", "text": "1286"}, {"range": "1381", "text": "1284"}, {"range": "1382", "text": "1286"}, {"range": "1383", "text": "1284"}, {"range": "1384", "text": "1286"}, {"range": "1385", "text": "1284"}, {"range": "1386", "text": "1286"}, {"range": "1387", "text": "1284"}, {"range": "1388", "text": "1286"}, {"range": "1389", "text": "1284"}, {"range": "1390", "text": "1286"}, {"range": "1391", "text": "1284"}, {"range": "1392", "text": "1286"}, {"range": "1393", "text": "1284"}, {"range": "1394", "text": "1286"}, {"range": "1395", "text": "1284"}, {"range": "1396", "text": "1286"}, {"range": "1397", "text": "1284"}, {"range": "1398", "text": "1286"}, {"range": "1399", "text": "1284"}, {"range": "1400", "text": "1286"}, {"range": "1401", "text": "1284"}, {"range": "1402", "text": "1286"}, {"range": "1403", "text": "1284"}, {"range": "1404", "text": "1286"}, {"range": "1405", "text": "1284"}, {"range": "1406", "text": "1286"}, {"range": "1407", "text": "1284"}, {"range": "1408", "text": "1286"}, {"range": "1409", "text": "1284"}, {"range": "1410", "text": "1286"}, {"range": "1411", "text": "1284"}, {"range": "1412", "text": "1286"}, {"range": "1413", "text": "1284"}, {"range": "1414", "text": "1286"}, {"range": "1415", "text": "1284"}, {"range": "1416", "text": "1286"}, {"range": "1417", "text": "1284"}, {"range": "1418", "text": "1286"}, {"range": "1419", "text": "1284"}, {"range": "1420", "text": "1286"}, {"range": "1421", "text": "1284"}, {"range": "1422", "text": "1286"}, {"range": "1423", "text": "1284"}, {"range": "1424", "text": "1286"}, {"range": "1425", "text": "1284"}, {"range": "1426", "text": "1286"}, {"range": "1427", "text": "1284"}, {"range": "1428", "text": "1286"}, {"range": "1429", "text": "1284"}, {"range": "1430", "text": "1286"}, {"range": "1431", "text": "1284"}, {"range": "1432", "text": "1286"}, {"range": "1433", "text": "1284"}, {"range": "1434", "text": "1286"}, {"range": "1435", "text": "1284"}, {"range": "1436", "text": "1286"}, {"range": "1437", "text": "1284"}, {"range": "1438", "text": "1286"}, {"range": "1439", "text": "1284"}, {"range": "1440", "text": "1286"}, {"range": "1441", "text": "1284"}, {"range": "1442", "text": "1286"}, {"range": "1443", "text": "1284"}, {"range": "1444", "text": "1286"}, {"range": "1445", "text": "1284"}, {"range": "1446", "text": "1286"}, {"range": "1447", "text": "1284"}, {"range": "1448", "text": "1286"}, {"range": "1449", "text": "1284"}, {"range": "1450", "text": "1286"}, {"range": "1451", "text": "1284"}, {"range": "1452", "text": "1286"}, {"range": "1453", "text": "1284"}, {"range": "1454", "text": "1286"}, {"range": "1455", "text": "1284"}, {"range": "1456", "text": "1286"}, {"range": "1457", "text": "1284"}, {"range": "1458", "text": "1286"}, {"range": "1459", "text": "1284"}, {"range": "1460", "text": "1286"}, {"range": "1461", "text": "1284"}, {"range": "1462", "text": "1286"}, {"range": "1463", "text": "1284"}, {"range": "1464", "text": "1286"}, {"range": "1465", "text": "1284"}, {"range": "1466", "text": "1286"}, {"range": "1467", "text": "1284"}, {"range": "1468", "text": "1286"}, {"range": "1469", "text": "1284"}, {"range": "1470", "text": "1286"}, {"range": "1471", "text": "1284"}, {"range": "1472", "text": "1286"}, "replaceEmptyObjectType", {"replacement": "1473"}, {"range": "1474", "text": "1473"}, "Replace `{}` with `object`.", {"replacement": "1284"}, {"range": "1475", "text": "1284"}, "Replace `{}` with `unknown`.", "directive", "", {"replacement": "1473"}, {"range": "1476", "text": "1473"}, {"replacement": "1284"}, {"range": "1477", "text": "1284"}, {"replacement": "1473"}, {"range": "1478", "text": "1473"}, {"replacement": "1284"}, {"range": "1479", "text": "1284"}, {"replacement": "1473"}, {"range": "1480", "text": "1473"}, {"replacement": "1284"}, {"range": "1481", "text": "1284"}, "replaceEmptyInterfaceWithSuper", {"range": "1482", "text": "1483"}, "Replace empty interface with a type alias.", {"range": "1484", "text": "1284"}, {"range": "1485", "text": "1286"}, {"range": "1486", "text": "1284"}, {"range": "1487", "text": "1286"}, {"range": "1488", "text": "1284"}, {"range": "1489", "text": "1286"}, {"range": "1490", "text": "1284"}, {"range": "1491", "text": "1286"}, {"range": "1492", "text": "1284"}, {"range": "1493", "text": "1286"}, {"range": "1494", "text": "1284"}, {"range": "1495", "text": "1286"}, {"range": "1496", "text": "1284"}, {"range": "1497", "text": "1286"}, {"range": "1498", "text": "1284"}, {"range": "1499", "text": "1286"}, {"range": "1500", "text": "1284"}, {"range": "1501", "text": "1286"}, {"range": "1502", "text": "1284"}, {"range": "1503", "text": "1286"}, {"range": "1504", "text": "1284"}, {"range": "1505", "text": "1286"}, {"range": "1506", "text": "1284"}, {"range": "1507", "text": "1286"}, {"range": "1508", "text": "1284"}, {"range": "1509", "text": "1286"}, {"range": "1510", "text": "1284"}, {"range": "1511", "text": "1286"}, {"range": "1512", "text": "1284"}, {"range": "1513", "text": "1286"}, {"range": "1514", "text": "1284"}, {"range": "1515", "text": "1286"}, {"range": "1516", "text": "1284"}, {"range": "1517", "text": "1286"}, {"range": "1518", "text": "1284"}, {"range": "1519", "text": "1286"}, {"range": "1520", "text": "1284"}, {"range": "1521", "text": "1286"}, {"range": "1522", "text": "1284"}, {"range": "1523", "text": "1286"}, {"range": "1524", "text": "1284"}, {"range": "1525", "text": "1286"}, {"range": "1526", "text": "1284"}, {"range": "1527", "text": "1286"}, {"range": "1528", "text": "1284"}, {"range": "1529", "text": "1286"}, {"range": "1530", "text": "1284"}, {"range": "1531", "text": "1286"}, {"range": "1532", "text": "1284"}, {"range": "1533", "text": "1286"}, {"range": "1534", "text": "1284"}, {"range": "1535", "text": "1286"}, {"range": "1536", "text": "1284"}, {"range": "1537", "text": "1286"}, {"range": "1538", "text": "1284"}, {"range": "1539", "text": "1286"}, {"range": "1540", "text": "1284"}, {"range": "1541", "text": "1286"}, {"range": "1542", "text": "1284"}, {"range": "1543", "text": "1286"}, {"range": "1544", "text": "1284"}, {"range": "1545", "text": "1286"}, {"range": "1546", "text": "1284"}, {"range": "1547", "text": "1286"}, {"range": "1548", "text": "1284"}, {"range": "1549", "text": "1286"}, {"range": "1550", "text": "1284"}, {"range": "1551", "text": "1286"}, {"range": "1552", "text": "1284"}, {"range": "1553", "text": "1286"}, {"range": "1554", "text": "1284"}, {"range": "1555", "text": "1286"}, {"range": "1556", "text": "1284"}, {"range": "1557", "text": "1286"}, {"range": "1558", "text": "1284"}, {"range": "1559", "text": "1286"}, {"range": "1560", "text": "1284"}, {"range": "1561", "text": "1286"}, {"range": "1562", "text": "1284"}, {"range": "1563", "text": "1286"}, {"range": "1564", "text": "1284"}, {"range": "1565", "text": "1286"}, {"range": "1566", "text": "1284"}, {"range": "1567", "text": "1286"}, {"range": "1568", "text": "1284"}, {"range": "1569", "text": "1286"}, {"range": "1570", "text": "1284"}, {"range": "1571", "text": "1286"}, {"range": "1572", "text": "1284"}, {"range": "1573", "text": "1286"}, {"range": "1574", "text": "1284"}, {"range": "1575", "text": "1286"}, {"range": "1576", "text": "1284"}, {"range": "1577", "text": "1286"}, {"range": "1578", "text": "1284"}, {"range": "1579", "text": "1286"}, {"range": "1580", "text": "1284"}, {"range": "1581", "text": "1286"}, {"range": "1582", "text": "1284"}, {"range": "1583", "text": "1286"}, {"range": "1584", "text": "1284"}, {"range": "1585", "text": "1286"}, {"range": "1586", "text": "1284"}, {"range": "1587", "text": "1286"}, {"range": "1588", "text": "1284"}, {"range": "1589", "text": "1286"}, {"range": "1590", "text": "1284"}, {"range": "1591", "text": "1286"}, {"range": "1592", "text": "1284"}, {"range": "1593", "text": "1286"}, {"range": "1594", "text": "1284"}, {"range": "1595", "text": "1286"}, {"range": "1596", "text": "1284"}, {"range": "1597", "text": "1286"}, {"range": "1598", "text": "1284"}, {"range": "1599", "text": "1286"}, [1476, 1479], "unknown", [1476, 1479], "never", [1687, 1690], [1687, 1690], [1724, 1727], [1724, 1727], [1751, 1754], [1751, 1754], [4289, 4292], [4289, 4292], [4344, 4347], [4344, 4347], [4395, 4398], [4395, 4398], [4578, 4581], [4578, 4581], [4628, 4631], [4628, 4631], [1148, 1151], [1148, 1151], [1421, 1424], [1421, 1424], [1474, 1477], [1474, 1477], [1525, 1528], [1525, 1528], [1569, 1572], [1569, 1572], [1615, 1618], [1615, 1618], [1708, 1711], [1708, 1711], [1764, 1767], [1764, 1767], [1821, 1824], [1821, 1824], [1882, 1885], [1882, 1885], [1996, 1999], [1996, 1999], [2047, 2050], [2047, 2050], [2095, 2098], [2095, 2098], [2204, 2207], [2204, 2207], [2262, 2265], [2262, 2265], [2316, 2319], [2316, 2319], [2576, 2579], [2576, 2579], [2621, 2624], [2621, 2624], [2720, 2723], [2720, 2723], [2779, 2782], [2779, 2782], [2848, 2851], [2848, 2851], [3040, 3043], [3040, 3043], [3095, 3098], [3095, 3098], [3146, 3149], [3146, 3149], [2077, 2080], [2077, 2080], [2117, 2120], [2117, 2120], [2189, 2192], [2189, 2192], [2345, 2348], [2345, 2348], [2373, 2376], [2373, 2376], [2920, 2923], [2920, 2923], [2975, 2978], [2975, 2978], [3026, 3029], [3026, 3029], [2265, 2268], [2265, 2268], [2302, 2305], [2302, 2305], [2370, 2373], [2370, 2373], [2407, 2410], [2407, 2410], [2486, 2489], [2486, 2489], [2523, 2526], [2523, 2526], [2610, 2613], [2610, 2613], [2650, 2653], [2650, 2653], [2724, 2727], [2724, 2727], [2764, 2767], [2764, 2767], [2836, 2839], [2836, 2839], [2876, 2879], [2876, 2879], [2964, 2967], [2964, 2967], [3004, 3007], [3004, 3007], [3135, 3138], [3135, 3138], [3158, 3161], [3158, 3161], [3172, 3175], [3172, 3175], [3385, 3388], [3385, 3388], [3408, 3411], [3408, 3411], [3419, 3422], [3419, 3422], [3605, 3608], [3605, 3608], [3628, 3631], [3628, 3631], [3639, 3642], [3639, 3642], [4002, 4005], [4002, 4005], [4280, 4283], [4280, 4283], [4373, 4376], [4373, 4376], [4424, 4427], [4424, 4427], [4517, 4520], [4517, 4520], [5037, 5040], [5037, 5040], [5092, 5095], [5092, 5095], [5143, 5146], [5143, 5146], [5271, 5274], [5271, 5274], [5313, 5316], [5313, 5316], [5355, 5358], [5355, 5358], [5397, 5400], [5397, 5400], [2147, 2150], [2147, 2150], [2187, 2190], [2187, 2190], [2709, 2712], [2709, 2712], [2737, 2740], [2737, 2740], [3319, 3322], [3319, 3322], [3540, 3543], [3540, 3543], [3595, 3598], [3595, 3598], [3646, 3649], [3646, 3649], [1730, 1733], [1730, 1733], [1755, 1758], [1755, 1758], [3806, 3809], [3806, 3809], [3861, 3864], [3861, 3864], [3912, 3915], [3912, 3915], [3215, 3218], [3215, 3218], [2231, 2234], [2231, 2234], [2358, 2361], [2358, 2361], [2485, 2488], [2485, 2488], [2612, 2615], [2612, 2615], "object", [1506, 1508], [1506, 1508], [1986, 1988], [1986, 1988], [3066, 3068], [3066, 3068], [3087, 3089], [3087, 3089], [22122, 22164], "type GeneratedTypes = Config", [1230, 1233], [1230, 1233], [1256, 1259], [1256, 1259], [3267, 3270], [3267, 3270], [5296, 5299], [5296, 5299], [6912, 6915], [6912, 6915], [7309, 7312], [7309, 7312], [7347, 7350], [7347, 7350], [7388, 7391], [7388, 7391], [2475, 2478], [2475, 2478], [3302, 3305], [3302, 3305], [4244, 4247], [4244, 4247], [4278, 4281], [4278, 4281], [4304, 4307], [4304, 4307], [4325, 4328], [4325, 4328], [5180, 5183], [5180, 5183], [2385, 2388], [2385, 2388], [2429, 2432], [2429, 2432], [2473, 2476], [2473, 2476], [2525, 2528], [2525, 2528], [2569, 2572], [2569, 2572], [2695, 2698], [2695, 2698], [2846, 2849], [2846, 2849], [2971, 2974], [2971, 2974], [3076, 3079], [3076, 3079], [3183, 3186], [3183, 3186], [3284, 3287], [3284, 3287], [3373, 3376], [3373, 3376], [3496, 3499], [3496, 3499], [3542, 3545], [3542, 3545], [3892, 3895], [3892, 3895], [3932, 3935], [3932, 3935], [4264, 4267], [4264, 4267], [4304, 4307], [4304, 4307], [4688, 4691], [4688, 4691], [4798, 4801], [4798, 4801], [4917, 4920], [4917, 4920], [5034, 5037], [5034, 5037], [5133, 5136], [5133, 5136], [5232, 5235], [5232, 5235], [5392, 5395], [5392, 5395], [5484, 5487], [5484, 5487], [5578, 5581], [5578, 5581], [5664, 5667], [5664, 5667], [5741, 5744], [5741, 5744], [6100, 6103], [6100, 6103], [6212, 6215], [6212, 6215], [6321, 6324], [6321, 6324], [6454, 6457], [6454, 6457], [7095, 7098], [7095, 7098], [7369, 7372], [7369, 7372], [7588, 7591], [7588, 7591], [7898, 7901], [7898, 7901], [7926, 7929], [7926, 7929], [8078, 8081], [8078, 8081], [8106, 8109], [8106, 8109], [996, 999], [996, 999], [1255, 1258], [1255, 1258], [1872, 1875], [1872, 1875]]