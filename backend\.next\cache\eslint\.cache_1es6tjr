[{"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\importMap.js": "3", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx": "4", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql-playground\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\[...slug]\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\[id]\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments-direct\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\[id]\\route.ts": "14", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\generate-from-appointment\\route.ts": "15", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\route.ts": "16", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\db-test\\route.ts": "18", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\apply-to-bill\\route.ts": "19", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\refund\\route.ts": "20", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\route.ts": "21", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\[id]\\route.ts": "22", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\convert-to-patient\\route.ts": "23", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\[id]\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\receipt\\route.ts": "27", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\route.ts": "28", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\daily-revenue\\route.ts": "29", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\financial\\route.ts": "30", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\monthly-revenue\\route.ts": "31", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\outstanding-balances\\route.ts": "32", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reset-db\\route.ts": "33", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\test\\route.ts": "34", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\route.ts": "35", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\[id]\\route.ts": "36", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\route.ts": "37", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\[id]\\route.ts": "38", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\my-route\\route.ts": "39", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Appointments.ts": "40", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\BillItems.ts": "41", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Bills.ts": "42", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Deposits.ts": "43", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Media.ts": "44", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Patients.ts": "45", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Payments.ts": "46", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Treatments.ts": "47", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Users.ts": "48", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\auth-sync.ts": "49", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\clerk-auth-strategy.ts": "50", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\payload-auth-middleware.ts": "51", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload-types.ts": "52", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload.config.ts": "53", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\bulk-operations\\route.ts": "54", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\process-payment\\route.ts": "55", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\analytics\\route.ts": "56", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\PatientInteractions.ts": "57", "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\PatientTasks.ts": "58"}, {"size": 400, "mtime": 1752009631000, "results": "59", "hashOfConfig": "60"}, {"size": 1847, "mtime": 1752048389803, "results": "61", "hashOfConfig": "60"}, {"size": 5898, "mtime": 1752016400380, "results": "62", "hashOfConfig": "63"}, {"size": 731, "mtime": 1752009631000, "results": "64", "hashOfConfig": "60"}, {"size": 715, "mtime": 1752009631000, "results": "65", "hashOfConfig": "60"}, {"size": 315, "mtime": 1752009631000, "results": "66", "hashOfConfig": "60"}, {"size": 305, "mtime": 1752009631000, "results": "67", "hashOfConfig": "60"}, {"size": 550, "mtime": 1752009631000, "results": "68", "hashOfConfig": "60"}, {"size": 810, "mtime": 1752009631000, "results": "69", "hashOfConfig": "60"}, {"size": 2212, "mtime": 1752032650690, "results": "70", "hashOfConfig": "60"}, {"size": 3171, "mtime": 1752037068700, "results": "71", "hashOfConfig": "60"}, {"size": 1346, "mtime": 1752018809145, "results": "72", "hashOfConfig": "60"}, {"size": 3305, "mtime": **********005, "results": "73", "hashOfConfig": "60"}, {"size": 3783, "mtime": 1752042973170, "results": "74", "hashOfConfig": "60"}, {"size": 4926, "mtime": 1752117128123, "results": "75", "hashOfConfig": "60"}, {"size": 3707, "mtime": 1752048176586, "results": "76", "hashOfConfig": "60"}, {"size": 3921, "mtime": 1752042932094, "results": "77", "hashOfConfig": "60"}, {"size": 777, "mtime": 1752018791568, "results": "78", "hashOfConfig": "60"}, {"size": 4765, "mtime": 1752088274625, "results": "79", "hashOfConfig": "60"}, {"size": 5064, "mtime": 1752092473781, "results": "80", "hashOfConfig": "60"}, {"size": 3266, "mtime": 1752048189770, "results": "81", "hashOfConfig": "60"}, {"size": 3767, "mtime": 1752043056882, "results": "82", "hashOfConfig": "60"}, {"size": 2308, "mtime": 1752049995084, "results": "83", "hashOfConfig": "60"}, {"size": 2459, "mtime": 1752032674926, "results": "84", "hashOfConfig": "60"}, {"size": 3000, "mtime": 1752037001759, "results": "85", "hashOfConfig": "60"}, {"size": 3659, "mtime": 1752048214156, "results": "86", "hashOfConfig": "60"}, {"size": 5249, "mtime": 1752092952466, "results": "87", "hashOfConfig": "60"}, {"size": 3559, "mtime": 1752043015028, "results": "88", "hashOfConfig": "60"}, {"size": 3350, "mtime": 1752093175446, "results": "89", "hashOfConfig": "60"}, {"size": 5716, "mtime": 1752094372092, "results": "90", "hashOfConfig": "60"}, {"size": 3974, "mtime": 1752093206347, "results": "91", "hashOfConfig": "60"}, {"size": 4250, "mtime": 1752093216697, "results": "92", "hashOfConfig": "60"}, {"size": 1029, "mtime": 1752018919310, "results": "93", "hashOfConfig": "60"}, {"size": 231, "mtime": 1752018622704, "results": "94", "hashOfConfig": "60"}, {"size": 2022, "mtime": 1752032698666, "results": "95", "hashOfConfig": "60"}, {"size": 3036, "mtime": 1752037033875, "results": "96", "hashOfConfig": "60"}, {"size": 1952, "mtime": 1752032614878, "results": "97", "hashOfConfig": "60"}, {"size": 2946, "mtime": 1752036952764, "results": "98", "hashOfConfig": "60"}, {"size": 289, "mtime": 1752048092114, "results": "99", "hashOfConfig": "60"}, {"size": 6433, "mtime": 1752042577243, "results": "100", "hashOfConfig": "60"}, {"size": 6852, "mtime": 1752098003242, "results": "101", "hashOfConfig": "60"}, {"size": 12433, "mtime": 1752097953922, "results": "102", "hashOfConfig": "60"}, {"size": 7285, "mtime": 1752083936008, "results": "103", "hashOfConfig": "60"}, {"size": 255, "mtime": 1752009631000, "results": "104", "hashOfConfig": "60"}, {"size": 6672, "mtime": 1752042473052, "results": "105", "hashOfConfig": "60"}, {"size": 9283, "mtime": 1752094498002, "results": "106", "hashOfConfig": "60"}, {"size": 1401, "mtime": 1752025370327, "results": "107", "hashOfConfig": "60"}, {"size": 2394, "mtime": 1752025408079, "results": "108", "hashOfConfig": "60"}, {"size": 2856, "mtime": 1752044417674, "results": "109", "hashOfConfig": "60"}, {"size": 951, "mtime": 1752044472492, "results": "110", "hashOfConfig": "60"}, {"size": 2700, "mtime": 1752050933716, "results": "111", "hashOfConfig": "60"}, {"size": 23790, "mtime": 1752107010079, "results": "112", "hashOfConfig": "60"}, {"size": 1626, "mtime": 1752106966966, "results": "113", "hashOfConfig": "60"}, {"size": 9524, "mtime": 1752117553548, "results": "114", "hashOfConfig": "60"}, {"size": 7139, "mtime": 1752117597341, "results": "115", "hashOfConfig": "60"}, {"size": 8399, "mtime": 1752098109044, "results": "116", "hashOfConfig": "60"}, {"size": 7428, "mtime": 1752106907537, "results": "117", "hashOfConfig": "60"}, {"size": 7321, "mtime": 1752106946886, "results": "118", "hashOfConfig": "60"}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1e1u0sl", {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pn1qvw", {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 35, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\importMap.js", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql-playground\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\[...slug]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments-direct\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bill-items\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\generate-from-appointment\\route.ts", ["293", "294", "295", "296"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\db-test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\apply-to-bill\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\refund\\route.ts", ["297", "298", "299", "300", "301", "302", "303", "304", "305"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\deposits\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\convert-to-patient\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\patients\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\receipt\\route.ts", ["306", "307", "308", "309", "310", "311", "312", "313", "314", "315", "316", "317", "318", "319", "320", "321", "322", "323", "324", "325", "326", "327", "328", "329"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\daily-revenue\\route.ts", ["330", "331", "332", "333", "334", "335", "336", "337"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\financial\\route.ts", ["338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348", "349", "350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "360", "361", "362", "363", "364", "365", "366", "367", "368", "369", "370", "371", "372"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\monthly-revenue\\route.ts", ["373", "374", "375", "376", "377", "378", "379", "380"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\outstanding-balances\\route.ts", ["381", "382", "383", "384", "385"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reset-db\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\test\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\treatments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\my-route\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Appointments.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\BillItems.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Bills.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Deposits.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Media.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Patients.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Payments.ts", ["386"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Treatments.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\Users.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\auth-sync.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\clerk-auth-strategy.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\lib\\payload-auth-middleware.ts", ["387", "388", "389", "390"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload-types.ts", [], ["391", "392", "393", "394", "395"], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\payload.config.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\bulk-operations\\route.ts", ["396", "397", "398", "399", "400", "401", "402", "403"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\process-payment\\route.ts", ["404"], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\analytics\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\PatientInteractions.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\collections\\PatientTasks.ts", [], [], {"ruleId": "405", "severity": 1, "message": "406", "line": 24, "column": 7, "nodeType": null, "messageId": "407", "endLine": 24, "endColumn": 20}, {"ruleId": "405", "severity": 1, "message": "408", "line": 25, "column": 7, "nodeType": null, "messageId": "407", "endLine": 25, "endColumn": 19}, {"ruleId": "405", "severity": 1, "message": "409", "line": 28, "column": 7, "nodeType": null, "messageId": "407", "endLine": 28, "endColumn": 12}, {"ruleId": "405", "severity": 1, "message": "410", "line": 30, "column": 7, "nodeType": null, "messageId": "407", "endLine": 30, "endColumn": 24}, {"ruleId": "411", "severity": 1, "message": "412", "line": 52, "column": 10, "nodeType": "413", "messageId": "414", "endLine": 52, "endColumn": 13, "suggestions": "415"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 62, "column": 44, "nodeType": "413", "messageId": "414", "endLine": 62, "endColumn": 47, "suggestions": "416"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 62, "column": 81, "nodeType": "413", "messageId": "414", "endLine": 62, "endColumn": 84, "suggestions": "417"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 62, "column": 108, "nodeType": "413", "messageId": "414", "endLine": 62, "endColumn": 111, "suggestions": "418"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 140, "column": 41, "nodeType": "413", "messageId": "414", "endLine": 140, "endColumn": 44, "suggestions": "419"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 141, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 141, "endColumn": 43, "suggestions": "420"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 142, "column": 37, "nodeType": "413", "messageId": "414", "endLine": 142, "endColumn": 40, "suggestions": "421"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 150, "column": 31, "nodeType": "413", "messageId": "414", "endLine": 150, "endColumn": 34, "suggestions": "422"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 151, "column": 42, "nodeType": "413", "messageId": "414", "endLine": 151, "endColumn": 45, "suggestions": "423"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 43, "column": 21, "nodeType": "413", "messageId": "414", "endLine": 43, "endColumn": 24, "suggestions": "424"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 52, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 52, "endColumn": 37, "suggestions": "425"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 53, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 53, "endColumn": 37, "suggestions": "426"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 54, "column": 32, "nodeType": "413", "messageId": "414", "endLine": 54, "endColumn": 35, "suggestions": "427"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 55, "column": 27, "nodeType": "413", "messageId": "414", "endLine": 55, "endColumn": 30, "suggestions": "428"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 56, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 56, "endColumn": 37, "suggestions": "429"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 60, "column": 33, "nodeType": "413", "messageId": "414", "endLine": 60, "endColumn": 36, "suggestions": "430"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 61, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 61, "endColumn": 37, "suggestions": "431"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 62, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 62, "endColumn": 37, "suggestions": "432"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 63, "column": 38, "nodeType": "413", "messageId": "414", "endLine": 63, "endColumn": 41, "suggestions": "433"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 68, "column": 31, "nodeType": "413", "messageId": "414", "endLine": 68, "endColumn": 34, "suggestions": "434"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 69, "column": 28, "nodeType": "413", "messageId": "414", "endLine": 69, "endColumn": 31, "suggestions": "435"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 70, "column": 28, "nodeType": "413", "messageId": "414", "endLine": 70, "endColumn": 31, "suggestions": "436"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 75, "column": 32, "nodeType": "413", "messageId": "414", "endLine": 75, "endColumn": 35, "suggestions": "437"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 76, "column": 31, "nodeType": "413", "messageId": "414", "endLine": 76, "endColumn": 34, "suggestions": "438"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 77, "column": 28, "nodeType": "413", "messageId": "414", "endLine": 77, "endColumn": 31, "suggestions": "439"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 88, "column": 26, "nodeType": "413", "messageId": "414", "endLine": 88, "endColumn": 29, "suggestions": "440"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 89, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 89, "endColumn": 37, "suggestions": "441"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 92, "column": 35, "nodeType": "413", "messageId": "414", "endLine": 92, "endColumn": 38, "suggestions": "442"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 93, "column": 36, "nodeType": "413", "messageId": "414", "endLine": 93, "endColumn": 39, "suggestions": "443"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 94, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 94, "endColumn": 37, "suggestions": "444"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 100, "column": 41, "nodeType": "413", "messageId": "414", "endLine": 100, "endColumn": 44, "suggestions": "445"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 101, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 101, "endColumn": 43, "suggestions": "446"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 102, "column": 37, "nodeType": "413", "messageId": "414", "endLine": 102, "endColumn": 40, "suggestions": "447"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 75, "column": 39, "nodeType": "413", "messageId": "414", "endLine": 75, "endColumn": 42, "suggestions": "448"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 75, "column": 79, "nodeType": "413", "messageId": "414", "endLine": 75, "endColumn": 82, "suggestions": "449"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 76, "column": 39, "nodeType": "413", "messageId": "414", "endLine": 76, "endColumn": 42, "suggestions": "450"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 81, "column": 18, "nodeType": "413", "messageId": "414", "endLine": 81, "endColumn": 21, "suggestions": "451"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 81, "column": 46, "nodeType": "413", "messageId": "414", "endLine": 81, "endColumn": 49, "suggestions": "452"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 97, "column": 41, "nodeType": "413", "messageId": "414", "endLine": 97, "endColumn": 44, "suggestions": "453"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 98, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 98, "endColumn": 43, "suggestions": "454"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 99, "column": 37, "nodeType": "413", "messageId": "414", "endLine": 99, "endColumn": 40, "suggestions": "455"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 83, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 83, "endColumn": 37, "suggestions": "456"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 83, "column": 71, "nodeType": "413", "messageId": "414", "endLine": 83, "endColumn": 74, "suggestions": "457"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 84, "column": 33, "nodeType": "413", "messageId": "414", "endLine": 84, "endColumn": 36, "suggestions": "458"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 84, "column": 70, "nodeType": "413", "messageId": "414", "endLine": 84, "endColumn": 73, "suggestions": "459"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 85, "column": 38, "nodeType": "413", "messageId": "414", "endLine": 85, "endColumn": 41, "suggestions": "460"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 85, "column": 75, "nodeType": "413", "messageId": "414", "endLine": 85, "endColumn": 78, "suggestions": "461"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 87, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 87, "endColumn": 43, "suggestions": "462"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 87, "column": 80, "nodeType": "413", "messageId": "414", "endLine": 87, "endColumn": 83, "suggestions": "463"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 89, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 89, "endColumn": 43, "suggestions": "464"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 89, "column": 80, "nodeType": "413", "messageId": "414", "endLine": 89, "endColumn": 83, "suggestions": "465"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 90, "column": 39, "nodeType": "413", "messageId": "414", "endLine": 90, "endColumn": 42, "suggestions": "466"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 90, "column": 79, "nodeType": "413", "messageId": "414", "endLine": 90, "endColumn": 82, "suggestions": "467"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 91, "column": 44, "nodeType": "413", "messageId": "414", "endLine": 91, "endColumn": 47, "suggestions": "468"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 91, "column": 84, "nodeType": "413", "messageId": "414", "endLine": 91, "endColumn": 87, "suggestions": "469"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 94, "column": 49, "nodeType": "413", "messageId": "414", "endLine": 94, "endColumn": 52, "suggestions": "470"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 94, "column": 72, "nodeType": "413", "messageId": "414", "endLine": 94, "endColumn": 75, "suggestions": "471"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 94, "column": 86, "nodeType": "413", "messageId": "414", "endLine": 94, "endColumn": 89, "suggestions": "472"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 101, "column": 43, "nodeType": "413", "messageId": "414", "endLine": 101, "endColumn": 46, "suggestions": "473"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 101, "column": 66, "nodeType": "413", "messageId": "414", "endLine": 101, "endColumn": 69, "suggestions": "474"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 101, "column": 77, "nodeType": "413", "messageId": "414", "endLine": 101, "endColumn": 80, "suggestions": "475"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 108, "column": 41, "nodeType": "413", "messageId": "414", "endLine": 108, "endColumn": 44, "suggestions": "476"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 108, "column": 64, "nodeType": "413", "messageId": "414", "endLine": 108, "endColumn": 67, "suggestions": "477"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 108, "column": 75, "nodeType": "413", "messageId": "414", "endLine": 108, "endColumn": 78, "suggestions": "478"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 121, "column": 28, "nodeType": "413", "messageId": "414", "endLine": 121, "endColumn": 31, "suggestions": "479"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 128, "column": 31, "nodeType": "413", "messageId": "414", "endLine": 128, "endColumn": 34, "suggestions": "480"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 130, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 130, "endColumn": 43, "suggestions": "481"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 130, "column": 91, "nodeType": "413", "messageId": "414", "endLine": 130, "endColumn": 94, "suggestions": "482"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 133, "column": 31, "nodeType": "413", "messageId": "414", "endLine": 133, "endColumn": 34, "suggestions": "483"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 147, "column": 41, "nodeType": "413", "messageId": "414", "endLine": 147, "endColumn": 44, "suggestions": "484"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 148, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 148, "endColumn": 43, "suggestions": "485"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 149, "column": 37, "nodeType": "413", "messageId": "414", "endLine": 149, "endColumn": 40, "suggestions": "486"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 155, "column": 22, "nodeType": "413", "messageId": "414", "endLine": 155, "endColumn": 25, "suggestions": "487"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 156, "column": 26, "nodeType": "413", "messageId": "414", "endLine": 156, "endColumn": 29, "suggestions": "488"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 157, "column": 32, "nodeType": "413", "messageId": "414", "endLine": 157, "endColumn": 35, "suggestions": "489"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 158, "column": 32, "nodeType": "413", "messageId": "414", "endLine": 158, "endColumn": 35, "suggestions": "490"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 74, "column": 39, "nodeType": "413", "messageId": "414", "endLine": 74, "endColumn": 42, "suggestions": "491"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 74, "column": 79, "nodeType": "413", "messageId": "414", "endLine": 74, "endColumn": 82, "suggestions": "492"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 88, "column": 18, "nodeType": "413", "messageId": "414", "endLine": 88, "endColumn": 21, "suggestions": "493"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 88, "column": 46, "nodeType": "413", "messageId": "414", "endLine": 88, "endColumn": 49, "suggestions": "494"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 108, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 108, "endColumn": 37, "suggestions": "495"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 113, "column": 41, "nodeType": "413", "messageId": "414", "endLine": 113, "endColumn": 44, "suggestions": "496"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 114, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 114, "endColumn": 43, "suggestions": "497"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 115, "column": 37, "nodeType": "413", "messageId": "414", "endLine": 115, "endColumn": 40, "suggestions": "498"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 63, "column": 15, "nodeType": "413", "messageId": "414", "endLine": 63, "endColumn": 18, "suggestions": "499"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 63, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 63, "endColumn": 43, "suggestions": "500"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 118, "column": 41, "nodeType": "413", "messageId": "414", "endLine": 118, "endColumn": 44, "suggestions": "501"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 119, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 119, "endColumn": 43, "suggestions": "502"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 120, "column": 37, "nodeType": "413", "messageId": "414", "endLine": 120, "endColumn": 40, "suggestions": "503"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 91, "column": 27, "nodeType": "413", "messageId": "414", "endLine": 91, "endColumn": 30, "suggestions": "504"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 78, "column": 12, "nodeType": "413", "messageId": "414", "endLine": 78, "endColumn": 15, "suggestions": "505"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 85, "column": 12, "nodeType": "413", "messageId": "414", "endLine": 85, "endColumn": 15, "suggestions": "506"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 92, "column": 12, "nodeType": "413", "messageId": "414", "endLine": 92, "endColumn": 15, "suggestions": "507"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 99, "column": 12, "nodeType": "413", "messageId": "414", "endLine": 99, "endColumn": 15, "suggestions": "508"}, {"ruleId": "509", "severity": 1, "message": "510", "line": 68, "column": 11, "nodeType": "511", "messageId": "512", "endLine": 68, "endColumn": 13, "suggestions": "513", "suppressions": "514"}, {"ruleId": "509", "severity": 1, "message": "510", "line": 85, "column": 21, "nodeType": "511", "messageId": "512", "endLine": 85, "endColumn": 23, "suggestions": "515", "suppressions": "516"}, {"ruleId": "509", "severity": 1, "message": "510", "line": 105, "column": 12, "nodeType": "511", "messageId": "512", "endLine": 105, "endColumn": 14, "suggestions": "517", "suppressions": "518"}, {"ruleId": "509", "severity": 1, "message": "510", "line": 106, "column": 18, "nodeType": "511", "messageId": "512", "endLine": 106, "endColumn": 20, "suggestions": "519", "suppressions": "520"}, {"ruleId": "509", "severity": 1, "message": "521", "line": 1055, "column": 20, "nodeType": "522", "messageId": "523", "endLine": 1055, "endColumn": 34, "suggestions": "524", "suppressions": "525"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 37, "column": 25, "nodeType": "413", "messageId": "414", "endLine": 37, "endColumn": 28, "suggestions": "526"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 38, "column": 21, "nodeType": "413", "messageId": "414", "endLine": 38, "endColumn": 24, "suggestions": "527"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 110, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 110, "endColumn": 43, "suggestions": "528"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 166, "column": 40, "nodeType": "413", "messageId": "414", "endLine": 166, "endColumn": 43, "suggestions": "529"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 211, "column": 72, "nodeType": "413", "messageId": "414", "endLine": 211, "endColumn": 75, "suggestions": "530"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 218, "column": 36, "nodeType": "413", "messageId": "414", "endLine": 218, "endColumn": 39, "suggestions": "531"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 219, "column": 34, "nodeType": "413", "messageId": "414", "endLine": 219, "endColumn": 37, "suggestions": "532"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 220, "column": 37, "nodeType": "413", "messageId": "414", "endLine": 220, "endColumn": 40, "suggestions": "533"}, {"ruleId": "411", "severity": 1, "message": "412", "line": 108, "column": 42, "nodeType": "413", "messageId": "414", "endLine": 108, "endColumn": 45, "suggestions": "534"}, "@typescript-eslint/no-unused-vars", "'applyDiscount' is assigned a value but never used. Allowed unused vars must match /^_/u.", "unusedVar", "'discountRate' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'notes' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'autoApplyDeposits' is assigned a value but never used. Allowed unused vars must match /^_/u.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["535", "536"], ["537", "538"], ["539", "540"], ["541", "542"], ["543", "544"], ["545", "546"], ["547", "548"], ["549", "550"], ["551", "552"], ["553", "554"], ["555", "556"], ["557", "558"], ["559", "560"], ["561", "562"], ["563", "564"], ["565", "566"], ["567", "568"], ["569", "570"], ["571", "572"], ["573", "574"], ["575", "576"], ["577", "578"], ["579", "580"], ["581", "582"], ["583", "584"], ["585", "586"], ["587", "588"], ["589", "590"], ["591", "592"], ["593", "594"], ["595", "596"], ["597", "598"], ["599", "600"], ["601", "602"], ["603", "604"], ["605", "606"], ["607", "608"], ["609", "610"], ["611", "612"], ["613", "614"], ["615", "616"], ["617", "618"], ["619", "620"], ["621", "622"], ["623", "624"], ["625", "626"], ["627", "628"], ["629", "630"], ["631", "632"], ["633", "634"], ["635", "636"], ["637", "638"], ["639", "640"], ["641", "642"], ["643", "644"], ["645", "646"], ["647", "648"], ["649", "650"], ["651", "652"], ["653", "654"], ["655", "656"], ["657", "658"], ["659", "660"], ["661", "662"], ["663", "664"], ["665", "666"], ["667", "668"], ["669", "670"], ["671", "672"], ["673", "674"], ["675", "676"], ["677", "678"], ["679", "680"], ["681", "682"], ["683", "684"], ["685", "686"], ["687", "688"], ["689", "690"], ["691", "692"], ["693", "694"], ["695", "696"], ["697", "698"], ["699", "700"], ["701", "702"], ["703", "704"], ["705", "706"], ["707", "708"], ["709", "710"], ["711", "712"], ["713", "714"], ["715", "716"], ["717", "718"], ["719", "720"], ["721", "722"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["723", "724"], ["725"], ["726", "727"], ["728"], ["729", "730"], ["731"], ["732", "733"], ["734"], "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["735"], ["736"], ["737", "738"], ["739", "740"], ["741", "742"], ["743", "744"], ["745", "746"], ["747", "748"], ["749", "750"], ["751", "752"], ["753", "754"], {"messageId": "755", "fix": "756", "desc": "757"}, {"messageId": "758", "fix": "759", "desc": "760"}, {"messageId": "755", "fix": "761", "desc": "757"}, {"messageId": "758", "fix": "762", "desc": "760"}, {"messageId": "755", "fix": "763", "desc": "757"}, {"messageId": "758", "fix": "764", "desc": "760"}, {"messageId": "755", "fix": "765", "desc": "757"}, {"messageId": "758", "fix": "766", "desc": "760"}, {"messageId": "755", "fix": "767", "desc": "757"}, {"messageId": "758", "fix": "768", "desc": "760"}, {"messageId": "755", "fix": "769", "desc": "757"}, {"messageId": "758", "fix": "770", "desc": "760"}, {"messageId": "755", "fix": "771", "desc": "757"}, {"messageId": "758", "fix": "772", "desc": "760"}, {"messageId": "755", "fix": "773", "desc": "757"}, {"messageId": "758", "fix": "774", "desc": "760"}, {"messageId": "755", "fix": "775", "desc": "757"}, {"messageId": "758", "fix": "776", "desc": "760"}, {"messageId": "755", "fix": "777", "desc": "757"}, {"messageId": "758", "fix": "778", "desc": "760"}, {"messageId": "755", "fix": "779", "desc": "757"}, {"messageId": "758", "fix": "780", "desc": "760"}, {"messageId": "755", "fix": "781", "desc": "757"}, {"messageId": "758", "fix": "782", "desc": "760"}, {"messageId": "755", "fix": "783", "desc": "757"}, {"messageId": "758", "fix": "784", "desc": "760"}, {"messageId": "755", "fix": "785", "desc": "757"}, {"messageId": "758", "fix": "786", "desc": "760"}, {"messageId": "755", "fix": "787", "desc": "757"}, {"messageId": "758", "fix": "788", "desc": "760"}, {"messageId": "755", "fix": "789", "desc": "757"}, {"messageId": "758", "fix": "790", "desc": "760"}, {"messageId": "755", "fix": "791", "desc": "757"}, {"messageId": "758", "fix": "792", "desc": "760"}, {"messageId": "755", "fix": "793", "desc": "757"}, {"messageId": "758", "fix": "794", "desc": "760"}, {"messageId": "755", "fix": "795", "desc": "757"}, {"messageId": "758", "fix": "796", "desc": "760"}, {"messageId": "755", "fix": "797", "desc": "757"}, {"messageId": "758", "fix": "798", "desc": "760"}, {"messageId": "755", "fix": "799", "desc": "757"}, {"messageId": "758", "fix": "800", "desc": "760"}, {"messageId": "755", "fix": "801", "desc": "757"}, {"messageId": "758", "fix": "802", "desc": "760"}, {"messageId": "755", "fix": "803", "desc": "757"}, {"messageId": "758", "fix": "804", "desc": "760"}, {"messageId": "755", "fix": "805", "desc": "757"}, {"messageId": "758", "fix": "806", "desc": "760"}, {"messageId": "755", "fix": "807", "desc": "757"}, {"messageId": "758", "fix": "808", "desc": "760"}, {"messageId": "755", "fix": "809", "desc": "757"}, {"messageId": "758", "fix": "810", "desc": "760"}, {"messageId": "755", "fix": "811", "desc": "757"}, {"messageId": "758", "fix": "812", "desc": "760"}, {"messageId": "755", "fix": "813", "desc": "757"}, {"messageId": "758", "fix": "814", "desc": "760"}, {"messageId": "755", "fix": "815", "desc": "757"}, {"messageId": "758", "fix": "816", "desc": "760"}, {"messageId": "755", "fix": "817", "desc": "757"}, {"messageId": "758", "fix": "818", "desc": "760"}, {"messageId": "755", "fix": "819", "desc": "757"}, {"messageId": "758", "fix": "820", "desc": "760"}, {"messageId": "755", "fix": "821", "desc": "757"}, {"messageId": "758", "fix": "822", "desc": "760"}, {"messageId": "755", "fix": "823", "desc": "757"}, {"messageId": "758", "fix": "824", "desc": "760"}, {"messageId": "755", "fix": "825", "desc": "757"}, {"messageId": "758", "fix": "826", "desc": "760"}, {"messageId": "755", "fix": "827", "desc": "757"}, {"messageId": "758", "fix": "828", "desc": "760"}, {"messageId": "755", "fix": "829", "desc": "757"}, {"messageId": "758", "fix": "830", "desc": "760"}, {"messageId": "755", "fix": "831", "desc": "757"}, {"messageId": "758", "fix": "832", "desc": "760"}, {"messageId": "755", "fix": "833", "desc": "757"}, {"messageId": "758", "fix": "834", "desc": "760"}, {"messageId": "755", "fix": "835", "desc": "757"}, {"messageId": "758", "fix": "836", "desc": "760"}, {"messageId": "755", "fix": "837", "desc": "757"}, {"messageId": "758", "fix": "838", "desc": "760"}, {"messageId": "755", "fix": "839", "desc": "757"}, {"messageId": "758", "fix": "840", "desc": "760"}, {"messageId": "755", "fix": "841", "desc": "757"}, {"messageId": "758", "fix": "842", "desc": "760"}, {"messageId": "755", "fix": "843", "desc": "757"}, {"messageId": "758", "fix": "844", "desc": "760"}, {"messageId": "755", "fix": "845", "desc": "757"}, {"messageId": "758", "fix": "846", "desc": "760"}, {"messageId": "755", "fix": "847", "desc": "757"}, {"messageId": "758", "fix": "848", "desc": "760"}, {"messageId": "755", "fix": "849", "desc": "757"}, {"messageId": "758", "fix": "850", "desc": "760"}, {"messageId": "755", "fix": "851", "desc": "757"}, {"messageId": "758", "fix": "852", "desc": "760"}, {"messageId": "755", "fix": "853", "desc": "757"}, {"messageId": "758", "fix": "854", "desc": "760"}, {"messageId": "755", "fix": "855", "desc": "757"}, {"messageId": "758", "fix": "856", "desc": "760"}, {"messageId": "755", "fix": "857", "desc": "757"}, {"messageId": "758", "fix": "858", "desc": "760"}, {"messageId": "755", "fix": "859", "desc": "757"}, {"messageId": "758", "fix": "860", "desc": "760"}, {"messageId": "755", "fix": "861", "desc": "757"}, {"messageId": "758", "fix": "862", "desc": "760"}, {"messageId": "755", "fix": "863", "desc": "757"}, {"messageId": "758", "fix": "864", "desc": "760"}, {"messageId": "755", "fix": "865", "desc": "757"}, {"messageId": "758", "fix": "866", "desc": "760"}, {"messageId": "755", "fix": "867", "desc": "757"}, {"messageId": "758", "fix": "868", "desc": "760"}, {"messageId": "755", "fix": "869", "desc": "757"}, {"messageId": "758", "fix": "870", "desc": "760"}, {"messageId": "755", "fix": "871", "desc": "757"}, {"messageId": "758", "fix": "872", "desc": "760"}, {"messageId": "755", "fix": "873", "desc": "757"}, {"messageId": "758", "fix": "874", "desc": "760"}, {"messageId": "755", "fix": "875", "desc": "757"}, {"messageId": "758", "fix": "876", "desc": "760"}, {"messageId": "755", "fix": "877", "desc": "757"}, {"messageId": "758", "fix": "878", "desc": "760"}, {"messageId": "755", "fix": "879", "desc": "757"}, {"messageId": "758", "fix": "880", "desc": "760"}, {"messageId": "755", "fix": "881", "desc": "757"}, {"messageId": "758", "fix": "882", "desc": "760"}, {"messageId": "755", "fix": "883", "desc": "757"}, {"messageId": "758", "fix": "884", "desc": "760"}, {"messageId": "755", "fix": "885", "desc": "757"}, {"messageId": "758", "fix": "886", "desc": "760"}, {"messageId": "755", "fix": "887", "desc": "757"}, {"messageId": "758", "fix": "888", "desc": "760"}, {"messageId": "755", "fix": "889", "desc": "757"}, {"messageId": "758", "fix": "890", "desc": "760"}, {"messageId": "755", "fix": "891", "desc": "757"}, {"messageId": "758", "fix": "892", "desc": "760"}, {"messageId": "755", "fix": "893", "desc": "757"}, {"messageId": "758", "fix": "894", "desc": "760"}, {"messageId": "755", "fix": "895", "desc": "757"}, {"messageId": "758", "fix": "896", "desc": "760"}, {"messageId": "755", "fix": "897", "desc": "757"}, {"messageId": "758", "fix": "898", "desc": "760"}, {"messageId": "755", "fix": "899", "desc": "757"}, {"messageId": "758", "fix": "900", "desc": "760"}, {"messageId": "755", "fix": "901", "desc": "757"}, {"messageId": "758", "fix": "902", "desc": "760"}, {"messageId": "755", "fix": "903", "desc": "757"}, {"messageId": "758", "fix": "904", "desc": "760"}, {"messageId": "755", "fix": "905", "desc": "757"}, {"messageId": "758", "fix": "906", "desc": "760"}, {"messageId": "755", "fix": "907", "desc": "757"}, {"messageId": "758", "fix": "908", "desc": "760"}, {"messageId": "755", "fix": "909", "desc": "757"}, {"messageId": "758", "fix": "910", "desc": "760"}, {"messageId": "755", "fix": "911", "desc": "757"}, {"messageId": "758", "fix": "912", "desc": "760"}, {"messageId": "755", "fix": "913", "desc": "757"}, {"messageId": "758", "fix": "914", "desc": "760"}, {"messageId": "755", "fix": "915", "desc": "757"}, {"messageId": "758", "fix": "916", "desc": "760"}, {"messageId": "755", "fix": "917", "desc": "757"}, {"messageId": "758", "fix": "918", "desc": "760"}, {"messageId": "755", "fix": "919", "desc": "757"}, {"messageId": "758", "fix": "920", "desc": "760"}, {"messageId": "755", "fix": "921", "desc": "757"}, {"messageId": "758", "fix": "922", "desc": "760"}, {"messageId": "755", "fix": "923", "desc": "757"}, {"messageId": "758", "fix": "924", "desc": "760"}, {"messageId": "755", "fix": "925", "desc": "757"}, {"messageId": "758", "fix": "926", "desc": "760"}, {"messageId": "755", "fix": "927", "desc": "757"}, {"messageId": "758", "fix": "928", "desc": "760"}, {"messageId": "755", "fix": "929", "desc": "757"}, {"messageId": "758", "fix": "930", "desc": "760"}, {"messageId": "755", "fix": "931", "desc": "757"}, {"messageId": "758", "fix": "932", "desc": "760"}, {"messageId": "755", "fix": "933", "desc": "757"}, {"messageId": "758", "fix": "934", "desc": "760"}, {"messageId": "755", "fix": "935", "desc": "757"}, {"messageId": "758", "fix": "936", "desc": "760"}, {"messageId": "755", "fix": "937", "desc": "757"}, {"messageId": "758", "fix": "938", "desc": "760"}, {"messageId": "755", "fix": "939", "desc": "757"}, {"messageId": "758", "fix": "940", "desc": "760"}, {"messageId": "755", "fix": "941", "desc": "757"}, {"messageId": "758", "fix": "942", "desc": "760"}, {"messageId": "755", "fix": "943", "desc": "757"}, {"messageId": "758", "fix": "944", "desc": "760"}, {"messageId": "755", "fix": "945", "desc": "757"}, {"messageId": "758", "fix": "946", "desc": "760"}, {"messageId": "947", "data": "948", "fix": "949", "desc": "950"}, {"messageId": "947", "data": "951", "fix": "952", "desc": "953"}, {"kind": "954", "justification": "955"}, {"messageId": "947", "data": "956", "fix": "957", "desc": "950"}, {"messageId": "947", "data": "958", "fix": "959", "desc": "953"}, {"kind": "954", "justification": "955"}, {"messageId": "947", "data": "960", "fix": "961", "desc": "950"}, {"messageId": "947", "data": "962", "fix": "963", "desc": "953"}, {"kind": "954", "justification": "955"}, {"messageId": "947", "data": "964", "fix": "965", "desc": "950"}, {"messageId": "947", "data": "966", "fix": "967", "desc": "953"}, {"kind": "954", "justification": "955"}, {"messageId": "968", "fix": "969", "desc": "970"}, {"kind": "954", "justification": "955"}, {"messageId": "755", "fix": "971", "desc": "757"}, {"messageId": "758", "fix": "972", "desc": "760"}, {"messageId": "755", "fix": "973", "desc": "757"}, {"messageId": "758", "fix": "974", "desc": "760"}, {"messageId": "755", "fix": "975", "desc": "757"}, {"messageId": "758", "fix": "976", "desc": "760"}, {"messageId": "755", "fix": "977", "desc": "757"}, {"messageId": "758", "fix": "978", "desc": "760"}, {"messageId": "755", "fix": "979", "desc": "757"}, {"messageId": "758", "fix": "980", "desc": "760"}, {"messageId": "755", "fix": "981", "desc": "757"}, {"messageId": "758", "fix": "982", "desc": "760"}, {"messageId": "755", "fix": "983", "desc": "757"}, {"messageId": "758", "fix": "984", "desc": "760"}, {"messageId": "755", "fix": "985", "desc": "757"}, {"messageId": "758", "fix": "986", "desc": "760"}, {"messageId": "755", "fix": "987", "desc": "757"}, {"messageId": "758", "fix": "988", "desc": "760"}, "suggestUnknown", {"range": "989", "text": "990"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "991", "text": "992"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "993", "text": "990"}, {"range": "994", "text": "992"}, {"range": "995", "text": "990"}, {"range": "996", "text": "992"}, {"range": "997", "text": "990"}, {"range": "998", "text": "992"}, {"range": "999", "text": "990"}, {"range": "1000", "text": "992"}, {"range": "1001", "text": "990"}, {"range": "1002", "text": "992"}, {"range": "1003", "text": "990"}, {"range": "1004", "text": "992"}, {"range": "1005", "text": "990"}, {"range": "1006", "text": "992"}, {"range": "1007", "text": "990"}, {"range": "1008", "text": "992"}, {"range": "1009", "text": "990"}, {"range": "1010", "text": "992"}, {"range": "1011", "text": "990"}, {"range": "1012", "text": "992"}, {"range": "1013", "text": "990"}, {"range": "1014", "text": "992"}, {"range": "1015", "text": "990"}, {"range": "1016", "text": "992"}, {"range": "1017", "text": "990"}, {"range": "1018", "text": "992"}, {"range": "1019", "text": "990"}, {"range": "1020", "text": "992"}, {"range": "1021", "text": "990"}, {"range": "1022", "text": "992"}, {"range": "1023", "text": "990"}, {"range": "1024", "text": "992"}, {"range": "1025", "text": "990"}, {"range": "1026", "text": "992"}, {"range": "1027", "text": "990"}, {"range": "1028", "text": "992"}, {"range": "1029", "text": "990"}, {"range": "1030", "text": "992"}, {"range": "1031", "text": "990"}, {"range": "1032", "text": "992"}, {"range": "1033", "text": "990"}, {"range": "1034", "text": "992"}, {"range": "1035", "text": "990"}, {"range": "1036", "text": "992"}, {"range": "1037", "text": "990"}, {"range": "1038", "text": "992"}, {"range": "1039", "text": "990"}, {"range": "1040", "text": "992"}, {"range": "1041", "text": "990"}, {"range": "1042", "text": "992"}, {"range": "1043", "text": "990"}, {"range": "1044", "text": "992"}, {"range": "1045", "text": "990"}, {"range": "1046", "text": "992"}, {"range": "1047", "text": "990"}, {"range": "1048", "text": "992"}, {"range": "1049", "text": "990"}, {"range": "1050", "text": "992"}, {"range": "1051", "text": "990"}, {"range": "1052", "text": "992"}, {"range": "1053", "text": "990"}, {"range": "1054", "text": "992"}, {"range": "1055", "text": "990"}, {"range": "1056", "text": "992"}, {"range": "1057", "text": "990"}, {"range": "1058", "text": "992"}, {"range": "1059", "text": "990"}, {"range": "1060", "text": "992"}, {"range": "1061", "text": "990"}, {"range": "1062", "text": "992"}, {"range": "1063", "text": "990"}, {"range": "1064", "text": "992"}, {"range": "1065", "text": "990"}, {"range": "1066", "text": "992"}, {"range": "1067", "text": "990"}, {"range": "1068", "text": "992"}, {"range": "1069", "text": "990"}, {"range": "1070", "text": "992"}, {"range": "1071", "text": "990"}, {"range": "1072", "text": "992"}, {"range": "1073", "text": "990"}, {"range": "1074", "text": "992"}, {"range": "1075", "text": "990"}, {"range": "1076", "text": "992"}, {"range": "1077", "text": "990"}, {"range": "1078", "text": "992"}, {"range": "1079", "text": "990"}, {"range": "1080", "text": "992"}, {"range": "1081", "text": "990"}, {"range": "1082", "text": "992"}, {"range": "1083", "text": "990"}, {"range": "1084", "text": "992"}, {"range": "1085", "text": "990"}, {"range": "1086", "text": "992"}, {"range": "1087", "text": "990"}, {"range": "1088", "text": "992"}, {"range": "1089", "text": "990"}, {"range": "1090", "text": "992"}, {"range": "1091", "text": "990"}, {"range": "1092", "text": "992"}, {"range": "1093", "text": "990"}, {"range": "1094", "text": "992"}, {"range": "1095", "text": "990"}, {"range": "1096", "text": "992"}, {"range": "1097", "text": "990"}, {"range": "1098", "text": "992"}, {"range": "1099", "text": "990"}, {"range": "1100", "text": "992"}, {"range": "1101", "text": "990"}, {"range": "1102", "text": "992"}, {"range": "1103", "text": "990"}, {"range": "1104", "text": "992"}, {"range": "1105", "text": "990"}, {"range": "1106", "text": "992"}, {"range": "1107", "text": "990"}, {"range": "1108", "text": "992"}, {"range": "1109", "text": "990"}, {"range": "1110", "text": "992"}, {"range": "1111", "text": "990"}, {"range": "1112", "text": "992"}, {"range": "1113", "text": "990"}, {"range": "1114", "text": "992"}, {"range": "1115", "text": "990"}, {"range": "1116", "text": "992"}, {"range": "1117", "text": "990"}, {"range": "1118", "text": "992"}, {"range": "1119", "text": "990"}, {"range": "1120", "text": "992"}, {"range": "1121", "text": "990"}, {"range": "1122", "text": "992"}, {"range": "1123", "text": "990"}, {"range": "1124", "text": "992"}, {"range": "1125", "text": "990"}, {"range": "1126", "text": "992"}, {"range": "1127", "text": "990"}, {"range": "1128", "text": "992"}, {"range": "1129", "text": "990"}, {"range": "1130", "text": "992"}, {"range": "1131", "text": "990"}, {"range": "1132", "text": "992"}, {"range": "1133", "text": "990"}, {"range": "1134", "text": "992"}, {"range": "1135", "text": "990"}, {"range": "1136", "text": "992"}, {"range": "1137", "text": "990"}, {"range": "1138", "text": "992"}, {"range": "1139", "text": "990"}, {"range": "1140", "text": "992"}, {"range": "1141", "text": "990"}, {"range": "1142", "text": "992"}, {"range": "1143", "text": "990"}, {"range": "1144", "text": "992"}, {"range": "1145", "text": "990"}, {"range": "1146", "text": "992"}, {"range": "1147", "text": "990"}, {"range": "1148", "text": "992"}, {"range": "1149", "text": "990"}, {"range": "1150", "text": "992"}, {"range": "1151", "text": "990"}, {"range": "1152", "text": "992"}, {"range": "1153", "text": "990"}, {"range": "1154", "text": "992"}, {"range": "1155", "text": "990"}, {"range": "1156", "text": "992"}, {"range": "1157", "text": "990"}, {"range": "1158", "text": "992"}, {"range": "1159", "text": "990"}, {"range": "1160", "text": "992"}, {"range": "1161", "text": "990"}, {"range": "1162", "text": "992"}, {"range": "1163", "text": "990"}, {"range": "1164", "text": "992"}, {"range": "1165", "text": "990"}, {"range": "1166", "text": "992"}, {"range": "1167", "text": "990"}, {"range": "1168", "text": "992"}, {"range": "1169", "text": "990"}, {"range": "1170", "text": "992"}, {"range": "1171", "text": "990"}, {"range": "1172", "text": "992"}, {"range": "1173", "text": "990"}, {"range": "1174", "text": "992"}, {"range": "1175", "text": "990"}, {"range": "1176", "text": "992"}, {"range": "1177", "text": "990"}, {"range": "1178", "text": "992"}, "replaceEmptyObjectType", {"replacement": "1179"}, {"range": "1180", "text": "1179"}, "Replace `{}` with `object`.", {"replacement": "990"}, {"range": "1181", "text": "990"}, "Replace `{}` with `unknown`.", "directive", "", {"replacement": "1179"}, {"range": "1182", "text": "1179"}, {"replacement": "990"}, {"range": "1183", "text": "990"}, {"replacement": "1179"}, {"range": "1184", "text": "1179"}, {"replacement": "990"}, {"range": "1185", "text": "990"}, {"replacement": "1179"}, {"range": "1186", "text": "1179"}, {"replacement": "990"}, {"range": "1187", "text": "990"}, "replaceEmptyInterfaceWithSuper", {"range": "1188", "text": "1189"}, "Replace empty interface with a type alias.", {"range": "1190", "text": "990"}, {"range": "1191", "text": "992"}, {"range": "1192", "text": "990"}, {"range": "1193", "text": "992"}, {"range": "1194", "text": "990"}, {"range": "1195", "text": "992"}, {"range": "1196", "text": "990"}, {"range": "1197", "text": "992"}, {"range": "1198", "text": "990"}, {"range": "1199", "text": "992"}, {"range": "1200", "text": "990"}, {"range": "1201", "text": "992"}, {"range": "1202", "text": "990"}, {"range": "1203", "text": "992"}, {"range": "1204", "text": "990"}, {"range": "1205", "text": "992"}, {"range": "1206", "text": "990"}, {"range": "1207", "text": "992"}, [1476, 1479], "unknown", [1476, 1479], "never", [1687, 1690], [1687, 1690], [1724, 1727], [1724, 1727], [1751, 1754], [1751, 1754], [4289, 4292], [4289, 4292], [4344, 4347], [4344, 4347], [4395, 4398], [4395, 4398], [4578, 4581], [4578, 4581], [4628, 4631], [4628, 4631], [1148, 1151], [1148, 1151], [1421, 1424], [1421, 1424], [1474, 1477], [1474, 1477], [1525, 1528], [1525, 1528], [1569, 1572], [1569, 1572], [1615, 1618], [1615, 1618], [1708, 1711], [1708, 1711], [1764, 1767], [1764, 1767], [1821, 1824], [1821, 1824], [1882, 1885], [1882, 1885], [1996, 1999], [1996, 1999], [2047, 2050], [2047, 2050], [2095, 2098], [2095, 2098], [2204, 2207], [2204, 2207], [2262, 2265], [2262, 2265], [2316, 2319], [2316, 2319], [2576, 2579], [2576, 2579], [2621, 2624], [2621, 2624], [2720, 2723], [2720, 2723], [2779, 2782], [2779, 2782], [2848, 2851], [2848, 2851], [3040, 3043], [3040, 3043], [3095, 3098], [3095, 3098], [3146, 3149], [3146, 3149], [2077, 2080], [2077, 2080], [2117, 2120], [2117, 2120], [2189, 2192], [2189, 2192], [2345, 2348], [2345, 2348], [2373, 2376], [2373, 2376], [2920, 2923], [2920, 2923], [2975, 2978], [2975, 2978], [3026, 3029], [3026, 3029], [2265, 2268], [2265, 2268], [2302, 2305], [2302, 2305], [2370, 2373], [2370, 2373], [2407, 2410], [2407, 2410], [2486, 2489], [2486, 2489], [2523, 2526], [2523, 2526], [2610, 2613], [2610, 2613], [2650, 2653], [2650, 2653], [2724, 2727], [2724, 2727], [2764, 2767], [2764, 2767], [2836, 2839], [2836, 2839], [2876, 2879], [2876, 2879], [2964, 2967], [2964, 2967], [3004, 3007], [3004, 3007], [3135, 3138], [3135, 3138], [3158, 3161], [3158, 3161], [3172, 3175], [3172, 3175], [3385, 3388], [3385, 3388], [3408, 3411], [3408, 3411], [3419, 3422], [3419, 3422], [3605, 3608], [3605, 3608], [3628, 3631], [3628, 3631], [3639, 3642], [3639, 3642], [4002, 4005], [4002, 4005], [4280, 4283], [4280, 4283], [4373, 4376], [4373, 4376], [4424, 4427], [4424, 4427], [4517, 4520], [4517, 4520], [5037, 5040], [5037, 5040], [5092, 5095], [5092, 5095], [5143, 5146], [5143, 5146], [5271, 5274], [5271, 5274], [5313, 5316], [5313, 5316], [5355, 5358], [5355, 5358], [5397, 5400], [5397, 5400], [2147, 2150], [2147, 2150], [2187, 2190], [2187, 2190], [2709, 2712], [2709, 2712], [2737, 2740], [2737, 2740], [3319, 3322], [3319, 3322], [3540, 3543], [3540, 3543], [3595, 3598], [3595, 3598], [3646, 3649], [3646, 3649], [1730, 1733], [1730, 1733], [1755, 1758], [1755, 1758], [3806, 3809], [3806, 3809], [3861, 3864], [3861, 3864], [3912, 3915], [3912, 3915], [3215, 3218], [3215, 3218], [2231, 2234], [2231, 2234], [2358, 2361], [2358, 2361], [2485, 2488], [2485, 2488], [2612, 2615], [2612, 2615], "object", [1506, 1508], [1506, 1508], [1986, 1988], [1986, 1988], [3066, 3068], [3066, 3068], [3087, 3089], [3087, 3089], [22122, 22164], "type GeneratedTypes = Config", [1230, 1233], [1230, 1233], [1256, 1259], [1256, 1259], [3267, 3270], [3267, 3270], [5296, 5299], [5296, 5299], [6912, 6915], [6912, 6915], [7309, 7312], [7309, 7312], [7347, 7350], [7347, 7350], [7388, 7391], [7388, 7391], [3214, 3217], [3214, 3217]]