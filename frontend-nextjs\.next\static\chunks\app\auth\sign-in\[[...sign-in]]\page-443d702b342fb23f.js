try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="b41c036f-6185-4a06-a8c4-e88ed2879b05",e._sentryDebugIdIdentifier="sentry-dbid-b41c036f-6185-4a06-a8c4-e88ed2879b05")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1591,6059],{26613:(e,t,r)=>{"use strict";r.r(t),r.d(t,{KeylessCookieSync:()=>a});var n=r(95181),i=r(99004),o=r(44554);function a(e){var t;let a=(null==(t=(0,n.useSelectedLayoutSegments)()[0])?void 0:t.startsWith("/_not-found"))||!1;return(0,i.useEffect)(()=>{o.I&&!a&&r.e(9211).then(r.bind(r,29211)).then(t=>t.syncKeylessConfigAction({...e,returnUrl:window.location.href}))},[a]),e.children}},32094:(e,t,r)=>{"use strict";r.d(t,{AuthenticateWithRedirectCallback:()=>n.B$,ClerkLoaded:()=>n.z0,ClerkLoading:()=>n.A0,RedirectToCreateOrganization:()=>n.rm,RedirectToOrganizationProfile:()=>n.m2,RedirectToSignIn:()=>n.W5,RedirectToSignUp:()=>n.mO,RedirectToUserProfile:()=>n.eG});var n=r(87905);r(47959)},48481:(e,t,r)=>{"use strict";r.d(t,{y:()=>i});var n=r(11487);let i=(0,n.createServerReference)("7f22efd92a3b59d43d3d12fe480e87910640e1db9e",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},50930:(e,t,r)=>{"use strict";r.d(t,{CreateOrganization:()=>n.ul,GoogleOneTap:()=>n.PQ,OrganizationList:()=>n.oE,OrganizationProfile:()=>h,OrganizationSwitcher:()=>n.NC,SignIn:()=>f,SignInButton:()=>n.hZ,SignInWithMetamaskButton:()=>n.M_,SignOutButton:()=>n.ct,SignUp:()=>g,SignUpButton:()=>n.Ny,UserButton:()=>n.uF,UserProfile:()=>d,Waitlist:()=>n.cP});var n=r(87905),i=r(99004),o=r(47959),a=r(26980),s=r(42496);let l=(e,t,r,o=!0)=>{let l=i.useRef(0),{pagesRouter:c}=(0,s.r)(),{session:u,isLoaded:d}=(0,n.wV)();(0,a.Fj)()||i.useEffect(()=>{if(!d||r&&"path"!==r||o&&!u)return;let n=new AbortController,i=()=>{let r=c?`${t}/[[...index]].tsx`:`${t}/[[...rest]]/page.tsx`;throw Error(`
Clerk: The <${e}/> component is not configured correctly. The most likely reasons for this error are:

1. The "${t}" route is not a catch-all route.
It is recommended to convert this route to a catch-all route, eg: "${r}". Alternatively, you can update the <${e}/> component to use hash-based routing by setting the "routing" prop to "hash".

2. The <${e}/> component is mounted in a catch-all route, but all routes under "${t}" are protected by the middleware.
To resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the "createRouteMatcher" helper, consider adding "(.*)" to the end of the route pattern, eg: "${t}(.*)". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher
`)};return c?c.pathname.match(/\[\[\.\.\..+]]/)||i():(async()=>{let t;if(l.current++,!(l.current>1)){try{let r=`${window.location.origin}${window.location.pathname}/${e}_clerk_catchall_check_${Date.now()}`;t=await fetch(r,{signal:n.signal})}catch{}(null==t?void 0:t.status)===404&&i()}})(),()=>{l.current>1&&n.abort()}},[d])},c=()=>{let e=i.useRef(),{pagesRouter:t}=(0,s.r)();if(t)if(e.current)return e.current;else return e.current=t.pathname.replace(/\/\[\[\.\.\..*/,""),e.current;let n=r(95181).usePathname,o=r(95181).useParams,a=(n()||"").split("/").filter(Boolean),l=Object.values(o()||{}).filter(e=>Array.isArray(e)).flat(1/0);return e.current||(e.current=`/${a.slice(0,a.length-l.length).join("/")}`),e.current};function u(e,t,r=!0){let n=c(),i=(0,o.yC)(e,t,{path:n});return l(e,n,i.routing,r),i}let d=Object.assign(e=>i.createElement(n.Fv,{...u("UserProfile",e)}),{...n.Fv}),h=Object.assign(e=>i.createElement(n.nC,{...u("OrganizationProfile",e)}),{...n.nC}),f=e=>i.createElement(n.Ls,{...u("SignIn",e,!1)}),g=e=>i.createElement(n.Hx,{...u("SignUp",e,!1)})},83405:(e,t,r)=>{"use strict";r.d(t,{PromisifiedAuthProvider:()=>l,d:()=>c});var n=r(87905),i=r(47959),o=r(67637),a=r(99004);let s=a.createContext(null);function l(e){let{authPromise:t,children:r}=e;return a.createElement(s.Provider,{value:t},r)}function c(){let e=(0,o.useRouter)(),t=a.useContext(s),r=t;return(t&&"then"in t&&(r=a.use(t)),"undefined"!=typeof window)?(0,n.As)(r):e?(0,n.As)():(0,i.hP)(r)}},88019:(e,t,r)=>{Promise.resolve().then(r.bind(r,13938)),Promise.resolve().then(r.bind(r,26613)),Promise.resolve().then(r.bind(r,32094)),Promise.resolve().then(r.bind(r,91686)),Promise.resolve().then(r.bind(r,83405)),Promise.resolve().then(r.bind(r,50930)),Promise.resolve().then(r.t.bind(r,78808,23))},91686:(e,t,r)=>{"use strict";r.d(t,{useAuth:()=>i.d,useClerk:()=>n.ho,useEmailLink:()=>n.ui,useOrganization:()=>n.Z5,useOrganizationList:()=>n.D_,useReverification:()=>n.Wp,useSession:()=>n.wV,useSessionList:()=>n.g7,useSignIn:()=>n.go,useSignUp:()=>n.yC,useUser:()=>n.Jd});var n=r(87905);r(50323);var i=r(83405)}},e=>{var t=t=>e(e.s=t);e.O(0,[7905,899,8808,9442,4579,9253,7358],()=>t(88019)),_N_E=e.O()}]);