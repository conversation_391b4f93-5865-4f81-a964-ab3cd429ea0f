(()=>{var e={};e.id=6676,e.ids=[6676],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4e3:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},15892:(e,t,r)=>{"use strict";r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{default:()=>c});var s=r(90099),n=r(22809),o=r(51518),a=r(10724);r(98822);var l=r(79551),u=r(68117);r(56139);var d=e([u]);async function c(){let e=await (0,n.b3)(),t=await u.A,r=await (0,a.nm0)({config:t}),{user:i}=await r.auth({headers:e}),d=`vscode://file/${(0,l.fileURLToPath)("file:///C:/Users/<USER>/Desktop/nord-coast/backend/src/app/(frontend)/page.tsx")}`;return(0,s.jsxs)("div",{className:"home",children:[(0,s.jsxs)("div",{className:"content",children:[(0,s.jsxs)("picture",{children:[(0,s.jsx)("source",{srcSet:"https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-favicon.svg"}),(0,s.jsx)(o.default,{alt:"Payload Logo",height:65,src:"https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-favicon.svg",width:65})]}),!i&&(0,s.jsx)("h1",{children:"Welcome to your new project."}),i&&(0,s.jsxs)("h1",{children:["Welcome back, ",i.firstName||i.lastName||"User"]}),(0,s.jsxs)("div",{className:"links",children:[(0,s.jsx)("a",{className:"admin",href:t.routes.admin,rel:"noopener noreferrer",target:"_blank",children:"Go to admin panel"}),(0,s.jsx)("a",{className:"docs",href:"https://payloadcms.com/docs",rel:"noopener noreferrer",target:"_blank",children:"Documentation"})]})]}),(0,s.jsxs)("div",{className:"footer",children:[(0,s.jsx)("p",{children:"Update this page by editing"}),(0,s.jsx)("a",{className:"codeLink",href:d,children:(0,s.jsx)("code",{children:"app/(frontend)/page.tsx"})})]})]})}u=(d.then?(await d)():d)[0],i()}catch(e){i(e)}})},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20817:()=>{},21215:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let i=r(20857),s=r(75412),n=r(79097),o=s._(r(26620)),a=i._(r(69373)),l=i._(r(72110)),u=r(43951),d=r(27138),c=r(91801);r(86550);let p=r(97178),f=i._(r(47207)),m=r(16568),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,r,i,s,n,o){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&s(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let i=!1,s=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>i,isPropagationStopped:()=>s,persist:()=>{},preventDefault:()=>{i=!0,t.preventDefault()},stopPropagation:()=>{s=!0,t.stopPropagation()}})}(null==i?void 0:i.current)&&i.current(e)}}))}function b(e){return o.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,o.forwardRef)((e,t)=>{let{src:r,srcSet:i,sizes:s,height:a,width:l,decoding:u,className:d,style:c,fetchPriority:p,placeholder:f,loading:h,unoptimized:v,fill:x,onLoadRef:y,onLoadingCompleteRef:_,setBlurComplete:w,setShowAltText:j,sizesInput:P,onLoad:E,onError:q,...O}=e,S=(0,o.useCallback)(e=>{e&&(q&&(e.src=e.src),e.complete&&g(e,f,y,_,w,v,P))},[r,f,y,_,w,q,v,P]),C=(0,m.useMergedRef)(t,S);return(0,n.jsx)("img",{...O,...b(p),loading:h,width:l,height:a,decoding:u,"data-nimg":x?"fill":"1",className:d,style:c,sizes:s,srcSet:i,src:r,ref:C,onLoad:e=>{g(e.currentTarget,f,y,_,w,v,P)},onError:e=>{j(!0),"empty"!==f&&w(!0),q&&q(e)}})});function x(e){let{isAppRouter:t,imgAttributes:r}=e,i={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...b(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,i),null):(0,n.jsx)(l.default,{children:(0,n.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...i},"__nimg-"+r.src+r.srcSet+r.sizes)})}let y=(0,o.forwardRef)((e,t)=>{let r=(0,o.useContext)(p.RouterContext),i=(0,o.useContext)(c.ImageConfigContext),s=(0,o.useMemo)(()=>{var e;let t=h||i||d.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),s=t.deviceSizes.sort((e,t)=>e-t),n=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:s,qualities:n}},[i]),{onLoad:a,onLoadingComplete:l}=e,m=(0,o.useRef)(a);(0,o.useEffect)(()=>{m.current=a},[a]);let g=(0,o.useRef)(l);(0,o.useEffect)(()=>{g.current=l},[l]);let[b,y]=(0,o.useState)(!1),[_,w]=(0,o.useState)(!1),{props:j,meta:P}=(0,u.getImgProps)(e,{defaultLoader:f.default,imgConf:s,blurComplete:b,showAltText:_});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(v,{...j,unoptimized:P.unoptimized,placeholder:P.placeholder,fill:P.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:y,setShowAltText:w,sizesInput:e.sizes,ref:t}),P.priority?(0,n.jsx)(x,{isAppRouter:!r,imgAttributes:j}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21820:e=>{"use strict";e.exports=require("os")},23415:(e,t,r)=>{"use strict";e.exports=r(15843).vendored.contexts.HeadManagerContext},23519:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,metadata:()=>s});var i=r(90099);r(98822),r(56139);let s={description:"A blank template using Payload in a Next.js app.",title:"Payload Blank Template"};async function n(e){let{children:t}=e;return(0,i.jsx)("html",{lang:"en",children:(0,i.jsx)("body",{children:(0,i.jsx)("main",{children:t})})})}},27138:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31058:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,21215,23))},32467:e=>{"use strict";e.exports=require("node:http2")},32785:e=>{"use strict";e.exports=import("prettier")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},35672:e=>{"use strict";e.exports=require("dns/promises")},35673:(e,t,r)=>{let{createProxy:i}=r(32050);e.exports=i("C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10\\node_modules\\next\\dist\\client\\image-component.js")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},37830:e=>{"use strict";e.exports=require("node:stream/web")},38522:e=>{"use strict";e.exports=require("node:zlib")},39497:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return a}});let i=r(29511),s=r(65829),n=r(35673),o=i._(r(92225));function a(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=n.Image},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},43326:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:s,blurDataURL:n,objectFit:o}=e,a=i?40*i:t,l=s?40*s:r,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},43951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(86550);let i=r(43326),s=r(27138),n=["-moz-initial","fill","none","scale-down",void 0];function o(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let u,d,c,{src:p,sizes:f,unoptimized:m=!1,priority:h=!1,loading:g,className:b,quality:v,width:x,height:y,fill:_=!1,style:w,overrideSrc:j,onLoad:P,onLoadingComplete:E,placeholder:q="empty",blurDataURL:O,fetchPriority:S,decoding:C="async",layout:R,objectFit:z,objectPosition:k,lazyBoundary:M,lazyRoot:I,...D}=e,{imgConf:N,showAltText:A,blurComplete:T,defaultLoader:G}=t,U=N||s.imageConfigDefault;if("allSizes"in U)u=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),i=null==(r=U.qualities)?void 0:r.sort((e,t)=>e-t);u={...U,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===G)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=D.loader||G;delete D.loader,delete D.srcSet;let L="__next_img_default"in F;if(L){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+p+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...i}=t;return e(i)}}if(R){"fill"===R&&(_=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!f&&(f=t)}let B="",W=a(x),X=a(y);if((l=p)&&"object"==typeof l&&(o(l)||void 0!==l.src)){let e=o(p)?p.default:p;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,O=O||e.blurDataURL,B=e.src,!_)if(W||X){if(W&&!X){let t=W/e.width;X=Math.round(e.height*t)}else if(!W&&X){let t=X/e.height;W=Math.round(e.width*t)}}else W=e.width,X=e.height}let V=!h&&("lazy"===g||void 0===g);(!(p="string"==typeof p?p:B)||p.startsWith("data:")||p.startsWith("blob:"))&&(m=!0,V=!1),u.unoptimized&&(m=!0),L&&!u.dangerouslyAllowSVG&&p.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=a(v),$=Object.assign(_?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:z,objectPosition:k}:{},A?{}:{color:"transparent"},w),J=T||"empty"===q?null:"blur"===q?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:W,heightInt:X,blurWidth:d,blurHeight:c,blurDataURL:O||"",objectFit:$.objectFit})+'")':'url("'+q+'")',Y=n.includes($.objectFit)?"fill"===$.objectFit?"100% 100%":"cover":$.objectFit,K=J?{backgroundSize:Y,backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Z=function(e){let{config:t,src:r,unoptimized:i,width:s,quality:n,sizes:o,loader:a}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,r){let{deviceSizes:i,allSizes:s}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,o),d=l.length-1;return{sizes:o||"w"!==u?o:"100vw",srcSet:l.map((e,i)=>a({config:t,src:r,quality:n,width:e})+" "+("w"===u?e:i+1)+u).join(", "),src:a({config:t,src:r,quality:n,width:l[d]})}}({config:u,src:p,unoptimized:m,width:W,quality:H,sizes:f,loader:F});return{props:{...D,loading:V?"lazy":g,fetchPriority:S,width:W,height:X,decoding:C,className:b,style:{...$,...K},sizes:Z.sizes,srcSet:Z.srcSet,src:j||Z.src},meta:{unoptimized:m,priority:h,placeholder:q,fill:_}}}},47207:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:s,quality:n}=e,o=n||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+s+"&q="+o+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},51518:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var i=r(39497),s=r.n(i)},52926:()=>{},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56139:()=>{},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60581:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let i=r(26620),s=()=>{},n=()=>{};function o(e){var t;let{headManager:r,reduceComponentsToState:o}=e;function a(){if(r&&r.mountedInstances){let t=i.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(o(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),a(),s(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),s(()=>(r&&(r._pendingUpdate=a),()=>{r&&(r._pendingUpdate=a)})),n(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},65829:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(4e3);let i=r(92276),s=r(94984),n=["-moz-initial","fill","none","scale-down",void 0];function o(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let u,d,c,{src:p,sizes:f,unoptimized:m=!1,priority:h=!1,loading:g,className:b,quality:v,width:x,height:y,fill:_=!1,style:w,overrideSrc:j,onLoad:P,onLoadingComplete:E,placeholder:q="empty",blurDataURL:O,fetchPriority:S,decoding:C="async",layout:R,objectFit:z,objectPosition:k,lazyBoundary:M,lazyRoot:I,...D}=e,{imgConf:N,showAltText:A,blurComplete:T,defaultLoader:G}=t,U=N||s.imageConfigDefault;if("allSizes"in U)u=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),i=null==(r=U.qualities)?void 0:r.sort((e,t)=>e-t);u={...U,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===G)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=D.loader||G;delete D.loader,delete D.srcSet;let L="__next_img_default"in F;if(L){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+p+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...i}=t;return e(i)}}if(R){"fill"===R&&(_=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!f&&(f=t)}let B="",W=a(x),X=a(y);if((l=p)&&"object"==typeof l&&(o(l)||void 0!==l.src)){let e=o(p)?p.default:p;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,O=O||e.blurDataURL,B=e.src,!_)if(W||X){if(W&&!X){let t=W/e.width;X=Math.round(e.height*t)}else if(!W&&X){let t=X/e.height;W=Math.round(e.width*t)}}else W=e.width,X=e.height}let V=!h&&("lazy"===g||void 0===g);(!(p="string"==typeof p?p:B)||p.startsWith("data:")||p.startsWith("blob:"))&&(m=!0,V=!1),u.unoptimized&&(m=!0),L&&!u.dangerouslyAllowSVG&&p.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=a(v),$=Object.assign(_?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:z,objectPosition:k}:{},A?{}:{color:"transparent"},w),J=T||"empty"===q?null:"blur"===q?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:W,heightInt:X,blurWidth:d,blurHeight:c,blurDataURL:O||"",objectFit:$.objectFit})+'")':'url("'+q+'")',Y=n.includes($.objectFit)?"fill"===$.objectFit?"100% 100%":"cover":$.objectFit,K=J?{backgroundSize:Y,backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Z=function(e){let{config:t,src:r,unoptimized:i,width:s,quality:n,sizes:o,loader:a}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,r){let{deviceSizes:i,allSizes:s}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,o),d=l.length-1;return{sizes:o||"w"!==u?o:"100vw",srcSet:l.map((e,i)=>a({config:t,src:r,quality:n,width:e})+" "+("w"===u?e:i+1)+u).join(", "),src:a({config:t,src:r,quality:n,width:l[d]})}}({config:u,src:p,unoptimized:m,width:W,quality:H,sizes:f,loader:F});return{props:{...D,loading:V?"lazy":g,fetchPriority:S,width:W,height:X,decoding:C,className:b,style:{...$,...K},sizes:Z.sizes,srcSet:Z.srcSet,src:j||Z.src},meta:{unoptimized:m,priority:h,placeholder:q,fill:_}}}},67877:(e,t,r)=>{"use strict";e.exports=r(15843).vendored.contexts.AmpContext},72110:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return h},defaultHead:function(){return c}});let i=r(20857),s=r(75412),n=r(79097),o=s._(r(26620)),a=i._(r(60581)),l=r(67877),u=r(23415),d=r(78646);function c(e){void 0===e&&(e=!1);let t=[(0,n.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,n.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(86550);let f=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,i={};return s=>{let n=!0,o=!1;if(s.key&&"number"!=typeof s.key&&s.key.indexOf("$")>0){o=!0;let t=s.key.slice(s.key.indexOf("$")+1);e.has(t)?n=!1:e.add(t)}switch(s.type){case"title":case"base":t.has(s.type)?n=!1:t.add(s.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(s.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?n=!1:r.add(t);else{let e=s.props[t],r=i[t]||new Set;("name"!==t||!o)&&r.has(e)?n=!1:(r.add(e),i[t]=r)}}}return n}}()).reverse().map((e,t)=>{let i=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:i})})}let h=function(e){let{children:t}=e,r=(0,o.useContext)(l.AmpStateContext),i=(0,o.useContext)(u.HeadManagerContext);return(0,n.jsx)(a.default,{reduceComponentsToState:m,headManager:i,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},75663:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,8606,23)),Promise.resolve().then(r.t.bind(r,41880,23)),Promise.resolve().then(r.t.bind(r,92284,23)),Promise.resolve().then(r.t.bind(r,20975,23)),Promise.resolve().then(r.t.bind(r,9063,23)),Promise.resolve().then(r.t.bind(r,10575,23)),Promise.resolve().then(r.t.bind(r,76383,23)),Promise.resolve().then(r.t.bind(r,82857,23))},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},78646:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:i=!1}=void 0===e?{}:e;return t||r&&i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79670:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(14024);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79748:e=>{"use strict";e.exports=require("fs/promises")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83911:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,95192,23)),Promise.resolve().then(r.t.bind(r,93238,23)),Promise.resolve().then(r.t.bind(r,63094,23)),Promise.resolve().then(r.t.bind(r,25753,23)),Promise.resolve().then(r.t.bind(r,14429,23)),Promise.resolve().then(r.t.bind(r,33157,23)),Promise.resolve().then(r.t.bind(r,39629,23)),Promise.resolve().then(r.t.bind(r,21207,23))},83997:e=>{"use strict";e.exports=require("tty")},91306:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,35673,23))},91645:e=>{"use strict";e.exports=require("net")},91801:(e,t,r)=>{"use strict";e.exports=r(15843).vendored.contexts.ImageConfigContext},92225:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:s,quality:n}=e,o=n||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+s+"&q="+o+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},92276:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:s,blurDataURL:n,objectFit:o}=e,a=i?40*i:t,l=s?40*s:r,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},93618:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var i=r(69109),s=r(32498),n=r(92284),o=r.n(n),a=r(10987),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["(frontend)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15892)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,23519)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,16536,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,34961,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,25358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,79670))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,16536,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,34961,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,25358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,79670))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(frontend)\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(frontend)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},94735:e=>{"use strict";e.exports=require("events")},94984:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},97178:(e,t,r)=>{"use strict";e.exports=r(15843).vendored.contexts.RouterContext},98995:e=>{"use strict";e.exports=require("node:module")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[3889,2481,4493,2825,8754],()=>r(93618));module.exports=i})();