{"version": 3, "file": "9615.js", "mappings": "8hBGAyK,yBAAzK,wBAA6B,yBAA+B,kDAAkD,SAAS,EAAkD,uXAA8Z,QAAS,SAAS,YAAc,IAAI,sDAAuD,kBAAkB,KAAO,eAAe,qBAAiC,WAAkE,QAAvD,0BAA5C,CAA4C,GAAuD,IAAvD,0BAA5C,CAA4C,EAA5C,EAAqH,wDAAuD,cAAc,IAAI,MAAM,wCAAuC,2CAAyC,mBAAmB,YAAY,oBAAoB,sBAAsB,SAAS,6BAA6B,QAAQ,2BAA2B,gCAAgC,mBAAmB,+BAA+B,QAAQ,QAAQ,yVAAyV,wBAAwB,YAAY,IAAI,8BAA8B,6BAA6B,kCAAkC,cAAe,yBAAyB,UAAU,wDAAwD,gCAAgC,IAAI,MAAM,6BAA8B,6BAA4B,iBAAiB,IAAI,yBAAyB,6BAA6B,QAAQ,IAAI,mBAAmB,MAAM,uDAAuD,QAAQ,6BAA6B,uCAAn9D,4CAAkD,EAAw8D,YAAiB,OAAQ,aAAa,IAAI,mBAAmB,QAAQ,YAAY,IAAI,MAAM,8BAA8B,yDAAyD,kBAAkB,UAAU,oBAAoB,YAAY,IAAI,oDAAoD,qBAAqB,IAAK,aAAa,IAAI,mBAAmB,QAAQ,YAAY,IAAI,MAAM,8BAA8B,+BAA+B,kBAAkB,UAAU,oBAAoB,YAAY,IAAI,+CAA+C,mBAAmB,IAAI,aAAa,IAAI,kDAAmD,MAAM,qCAAyC,UAAU,yCAAkD,mBAAmB,cAAc,8BAA8B,QAAQ,oCAAoC,WAAW,OAAQ,sFAAqF,YAAY,OAAO,oBAAoB,uCAAkG,GAAzD,uDAAyD,qBAA4B,MAAM,YAAY,IAAI,+BAA+B,8BAA8B,kBAAkB,QAAQ,6BAA6B,qCAAqC,mBAAmB,eAAe,uEAA2E,wBAAwB,kCAAkC,4BAA4B,oCAAoC,QAAQ,uCAAuC,UAAU,4CAA4C,YAAY,8CAA8C,mBAAmB,iBAAiB,QAAQ,YAAY,eAAe,OAAQ,oCAAmC,wBAAwB,0CAA0C,gBAAgB,aAAa,gBAAgB,aAAa,wBAAwB,YAAY,IAAI,uCAAsC,uCAAuC,QAAQ,mBAAmB,+BAA+B,UAAU,mCAAmC,YAAY,mCAAmC,4DAA4D,YAAiB,aAAa,YAAY,IAAI,aAAa,+DAAuE,2BAA2B,qBAAqB,IAAK,aAAa,IAAI,mBAAmB,eAAe,UAAU,SAAS,YAAY,IAAI,MAAM,qGAA6G,YAAY,eAAe,sCAAsC,mBAAmB,UAAU,WAAW,WAAW,kBAAkB,UAAU,8CAA8C,OAAO,uCAAuC,YAAY,WAAW,0BAA0B,mBAAmB,MAAM,kBAAmB,eAAc,gBAAiB,2EAA2E,KAAS,YAAY,KAAK,gDAA+C,wBAAwB,uBAAuB,wBAAwB,qBAAqB,uBAAuB,wBAAwB,qBAAqB,oBAAoB,wBAAwB,qBAAqB,uBAAuB,wBAAwB,mBAAmB,WAAW,gEAA+D,qBAA6B,YAAY,KAAK,MAAM,iBAA/B,CAAkD,oDAAsD,6DAAxG,CAAwG,QAAxG,CAAwG,QAAxG,CAAwG,iEAAxG,CAAwG,uDAAyM,0hDAA0hD,cAAc,+DAA+D,uBAAuB,+BAAmC,mLAAkL,2BAA2B,YAAY,IAAI,MAAM,WAAW,qDAAqD,SAAS,QAAQ,6BAA6B,qCAAqC,2CAAiD,sBAAsB,eAAe,sCAAsC,gCAAqC,IAAI,aAAa,OAAO,MAAM,kDAAkD,uBAAuB,GAAG,KAAK,WAAW,EAAE,mDAAmD,YAAY,IAAI,+BAA+B,YAAY,0BAA0B,kBAAsB,mBAAmB,mFAAmF,4BAA4B,6CAA6C,4BAA4B,6CAA6C,wBAAwB,gCAAgC,eAAO,QAAe,uBAA6B,gBAAgB,wBAA+B,QAAQ,uCAAuC,WAAW,uCAAuC,YAAY,+CAA+C,oBAAoB,oBAAoB,YAAiB,WAAgB,sBAAuB,iBAAiB,kCAAkC,4BAA4B,kCAAkC,4BAA4B,oCAAoC,kBAAmB,iBAAqB,0CAA0C,YAAY,IAAI,kBAAkB,wBAAyB,4BAA4B,kBAAkB,oBAAoB,YAAY,GAAG,wEAAyE,4BAA4B,kBAAkB,oBAAoB,YAAY,oBAAoB,2DAA4D,OAAQ,SAAS,qDAAqD,YAAY,IAAI,eAAe,oBAAoB,YAAY,UAAU,sCAAwC,aAAe,mBAAmB,mBAAmB,yBAAyB,iBAAkB,sBAA2B,QAAQ,MAAM,uBAAuB,IAAI,MAAM,OAAO,YAAY,EAAG,gLAA+K,qBAAqB,6BAA6B,cAAc,OAAO,UAAU,UAAU,qJAAqJ,mBAAmB,eAAe,sBAAsB,YAAY,2CAA2C,CAAkT,kBAAmB,wBAAwB,uFAAuF,iBAAiB,8GAA8G,EAAE,wBAAwB,0CAA0C,+EAA+E,mBAAmB,6CAA8C,2BAA2B,OAAlyB,CAAK,aAAa,OAAO,oBAAoB,GAAG,8EAAoF,UAAU,6BAA8B,+GAA+G,oBAAoB,GAAG,CAA0f,EAAE,IAAkP,kBAAmB,wBAAwB,4FAA4F,UAAU,uCAAuC,oBAAoB,wBAAwB,0CAA0C,0BAA0B,0DAA0D,mDAAoD,4BAA2B,IAAnqB,CAAQ,mBAAmB,IAA8B,EAA9B,IAAQ,mBAA4B,CAAN,CAAM,YAAc,qBAAqB,yBAA2B,YAAY,eAAe,qCAAqC,gCAAgC,kBAAkB,GAAob,CAAO,EAAE,2DAAoE,YAAY,MAAM,mCAAmC,YAAY,YAAY,MAAM,MAAM,4BAA4B,+BAAgC,gDAA+C,oMAA8M,yDAAwD,WAAW,MAAM,yDAAyD,8BAA8B,kDAAmD,mBAAkB,yBAA0B,sBAAqB,wBAAwB,YAAY,IAAI,kNAAuN,yBAAwB,2BAA2B,YAAY,IAAI,MAAM,SAAU,oGAAyG,kBAAkB,oDAAwD,kBAAkB,cAAiB,MAAjB,CAAiB,kCAAjB,EAAiB,2CAAjB,CAAiB,sBAAmH,+BAA+B,8EAAkF,YAAY,IAAI,MAAM,yDAAyD,KAAK,yDAAyD,KAAK,yDAAyD,KAAK,yDAAyD,qBAAqB,sEAAsE,KAAK,sEAAsE,KAAK,sEAAsE,KAAK,sEAAsE,yCAAyC,aAAiB,0BAA0B,0BAA2B,WAAW,2EAA0E,qBAAqB,0DAA0D,YAAY,KAAK,MAAM,2BAAsB,CAAK,wCAAoC,kBAAiB,4BAA2B,8IAA6I,sEAAsE,cAAc,+DAA+D,mJAAkJ,QAAQ,6BAA6B,qCAAqC,gDAA4F,sCCoBn9d,uCACA,0BA4EA,iBACA,IAAO,SAAqB,IAC5B,cAEA,CAUA,mBACA,OAHS,EAAQ,CAMjB,KANiB,aAOjB,EAHA,YAAoB,KAAoB,CAMxC,CACA,iCAoBA,eACA,MACA,SAEA,MAAoC,QAAuB,GAAK,KAAc,EAAI,KAAU,CAAG,KAAc,EAAI,KAAU,KAC3H,IACA,cACA,CAAI,MACJ,GAAQ,GAAa,CACrB,IACA,eACA,CAAQ,MACR,IACA,CAEA,IACA,CACA,CACA,cACA,GAAM,QAAuB,GAC7B,YAAoB,KAAoB,CAExC,aAAkB,KAAuB,CACzC,CACA,iBAGA,kBAFyB,EAAG,cAC5B,SAA0C,CAAI,EAE9C,mBCvKA,cACA,qBACA,IAAY,iBAAe,cAC3B,KACA,WAEA,QACA,CACA,CACA,cACA,eACA,SAAY,YAAe,QAC3B,KACA,WAEA,QACA,CACA,qFChBA,UACA,2CACA,SAEA,YAAU,GAAU,EACpB,kBACA,qCACA,4DAEA,MADA,iFACA,aACA,EACA,mBACA,IACA,IAAY,WAAU,MAAQ,+BAAsB,CACpD,YACA,WAAe,aAAW,4BAA8B,UAA0B,CAClF,CAAI,SACJ,WACA,OAEA,aACA;AACA;AACA,kBAAkB,EAAE,EAEpB,CACA,+FC3BA,UACA,0BACA,SAEA,IACA,iDAA0D,EAAE,oBAC5D,CAAI,MACJ,QACA,CACA,EACA,KACA,gCACA,mBACA,KAGA,eADA,6DACA,SACG,WCfH,cACA,SACA,KACA,OACA,YACA,IACA,CAAK,CACL,eACA,GACA,4CAEA,CAAK,CACL,YACA,UAuCA,EAGA,EAxCA,aADA,aAsCA,EAtCA,EAuCA,uBAAgC,EAAK,KAtCrC,IACA,WACA,4BAAsD,EAAE,cACxD,oBACA,GAuCA,cACA,sBACA,2BAEA,EADA,YACA,QA3CA,MA4CA,wCACA,EA7CA,SAEA,cACA,CACA,aAgCA,EAhCA,EAiCA,qBAA8B,KAAK,mBAAmB,UAAkB,IAAe,CAAC,aAAa,qCAAyD,IAhC9J,CACA,CACA,CACA,EACA,SACA,SAEA,MADA,wBAAqG,GAAY,KAEjH,IAFiH,GAGjH,IACA,cACA,6DACA,kBACA,WACA,IACS,UAET,MADA,WACA,CACA,CAAS,EAGT,OADA,WACA,CACA,CAAM,SAEN,MADA,WACA,CACA,CACA,kDCnCA,cACA,wBAEA,EADA,kBACA,UACA,OAAS,IAAS,cAAsB,OAAY,CACpD,CACA,wBACA,gBACA,sCAEA,sBACA,EAAoB,QAAkB,cACtC,MACA,OACA,YAAmB,IAAY,0BAA0B,YAAkB,GAC3E,EAEA,YAA0B,QAAS,aACnC,aAAqC,UACrC,mBACA,iCACA,WACA,OACA,KAAuB,EAAc,GAAG,EAAe,EACvD,IACA,YAA4B,IAAO,qCAEnC,OAAa,KADb,GAAgC,EAAU,GAAG,IAAS,8BAAwC,OAAY,EAAE,EAE5G,CAAI,SACJ,OAAa,YAAa,IAAY,cACtC,CACA,CAGiB,OAAgB,CAAC,IAAS,EAC3C,MAAiB,OAAoB,CAAC,IAAS,EAChC,OAAgB,IACN,OAAgB,CAAC,IAAiB,4BCjD3D,iBAA8C,MAC9C,UAmBA,EAlBA,EAAqB,QAAqB,iBAC1C,EAAoB,QAAqB,gBACzC,EAAsB,QAAqB,kBAC3C,EAAqB,QAAqB,iBAC1C,EAAwB,QAAqB,mBAC7C,mDAA8D,6BAAqC,EACnG,MAA+B,QAAS,GAAM,IAAS,2BACvD,EAA+B,QAAuB,IACtD,GACA,qDAA6F,IAAU,CACvG,iCAA2D,IAAe,CAC1E,OAAY,IAAO,CACnB,WAAgB,IAAW,CAC3B,aACA,cACA,YACA,EAGA,GAFA,8CAEA,OAAoC,IAAU,UAE1C,CACA,QAAoB,kBACxB,MAAgB,EAAS,EACzB,MADyB,CACzB,mCACA,EAAiB,QAAkB,wBACnC,MANA,EAAiB,QAAmB,IAOpC,QACA,gBC3BA,QACA,kBACA,sBACC,GAAK,EAAU,KAChB,GADgB,GAChB,OAIA,GAHQ,QAAQ,CAAC,QAAS,GAAM,IAAS,wBACzC,WAEA,CAAS,QAAqB,KACpB,GAA+B,EACjC,QAAgB,MAExB,YAAqD,+BAAqC,0DAC1F,KACA,eAEM,QAAgB,KACtB,CACA,OAAW,EAAsB,GAAQ,cAAiB,CAC1D,EADiC,CAGjC,GACA,kBACA,sBACC,GAAK,EAAU,KAChB,QACQ,QAAQ,CAAC,QAAS,GAAM,IAAS,wBACzC,WAEI,QAAgB,MACT,EAAsB,GAAQ,cAAiB,GAEzD,EACD,CACA,4BACA,oBAAuB,QAAwB,EAC/C,CAAC,EClCD,IAAM,EAAS,CACf,QAXA,CACA,OASe,KATf,uBACA,+BACA,wBAEA,mBACA,yBAEA,0BACA,CAGA,EEgCA,MACA,QACA,sBAA2B,EAAa,oBAAsD,MAAtD,iBAA6C,IAAS,kFAAgG,IAAS,kFAAmG,EAAa,oBACvT,EACA,MACA,MACA,iCAAyB,IAAS,iDAAyD,IAAS,gDAA8D,IAAS,6DAC3K,EACA,qBAAiE,EAAa,6BAC9E,OACA,uBACA,IDvDA,YACA,CCsDoB,KDtDpB,yCACA,ECqDoB,GACpB,SAEA,IAAU,mBAAiB,wCAC3B,QAEA,IACA,EAEA,EACA,qBAAmE,EAAa,mCCvDhF,gBACE,EAAQ,KAAa,EACvB,UADS,EACe,QAAgB,GACxC,YACA,GAAQ,GAA+B,CACvC,SAEA,IACA,YAAgC,+BAAgD,2BAChF,sCAA6C,YAA0B,qBACjE,MACN,SAEA,EACA,QAA2B,EAAkB,CAC7C,eAD6C,CAC7C,SACA,oBAAyB,QAAqB,kBAC9C,CAAG,KACH,EAAmB,QAAqB,eAiBxC,wBAAqC,iBAhBrC,KAAqC,IACrC,MAAyB,QAAkB,IAC3C,8BAAmE,IAAS,4CAAyD,IAAS,qBAC9I,EAAiC,QAAS,GAAU,IAAS,2BAC7D,EAAiC,QAAuB,IACxD,MAAW,QAAc,EACzB,gBAAuB,UAAQ,CAC/B,kBACA,8BACA,iCAA6D,IAAe,CAC5E,uBAAmD,IAAW,CAC9D,uBAAmD,IAAW,CACzD,oBACL,sFACA,CAAK,CACL,CACqC,CAAkB,CACvD,EACA,wBACE,EAAQ,KAAa,EACvB,UADS,EACe,QAAgB,GACxC,YAQA,ODvDA,YACA,qBAAU,gDAA4D,EACtE,qBACA,gBAEA,OADA,uFACA,YACA,6FACA,uFAUA,MACA,EACA,KAEA,WAEA,SAGA,EAGA,qBACA,SACA,EAEA,IAEA,SACA,EAEA,IAXA,EAlBA,EACA,KAEA,KACA,IAEA,GAwBA,CACA,GCQ+B,CAC/B,UACA,aACA,oCACA,QAAY,YACZ,QAAY,YACT,KACH,EACA,yECzCA,uBACA,0BAA8E,EAAW;AACzF,IAAI,6BAAsD;AAC1D;AACA;;AAEA;AACA,EACA,8RACA,yQACA;;AAEA;;AAEA", "sources": ["webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-CYDR2ZSA.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/logger.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/proxy.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/vendor/crypto-es.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/utils.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/chunk-P263NW7Z.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server/utils.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/utils/logFormatter.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/utils/debugLogger.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/jwt/index.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/data/getAuthDataFromRequest.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/createGetAuth.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/constants.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/nextFetcher.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/protect.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/errors.js"], "sourcesContent": ["// src/logger.ts\nvar loggedMessages = /* @__PURE__ */ new Set();\nvar logger = {\n  /**\n   * A custom logger that ensures messages are logged only once.\n   * Reduces noise and duplicated messages when logs are in a hot codepath.\n   */\n  warnOnce: (msg) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n    loggedMessages.add(msg);\n    console.warn(msg);\n  },\n  logOnce: (msg) => {\n    if (loggedMessages.has(msg)) {\n      return;\n    }\n    console.log(msg);\n    loggedMessages.add(msg);\n  }\n};\n\nexport {\n  logger\n};\n//# sourceMappingURL=chunk-CYDR2ZSA.mjs.map", "import {\n  logger\n} from \"./chunk-CYDR2ZSA.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  logger\n};\n//# sourceMappingURL=logger.mjs.map", "import {\n  isHttpOrHttps,\n  isProxyUrlRelative,\n  isValidProxyUrl,\n  proxyUrlToAbsoluteURL\n} from \"./chunk-6NDGN2IU.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  isHttpOrHttps,\n  isProxyUrlRelative,\n  isValidProxyUrl,\n  proxyUrlToAbsoluteURL\n};\n//# sourceMappingURL=proxy.mjs.map", "var kt=Object.defineProperty;var bt=(c,t,s)=>t in c?kt(c,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):c[t]=s;var it=(c,t,s)=>bt(c,typeof t!=\"symbol\"?t+\"\":t,s);var lt,ht,dt,pt,xt,_t,at=((lt=typeof globalThis!=\"undefined\"?globalThis:void 0)==null?void 0:lt.crypto)||((ht=typeof global!=\"undefined\"?global:void 0)==null?void 0:ht.crypto)||((dt=typeof window!=\"undefined\"?window:void 0)==null?void 0:dt.crypto)||((pt=typeof self!=\"undefined\"?self:void 0)==null?void 0:pt.crypto)||((_t=(xt=typeof frames!=\"undefined\"?frames:void 0)==null?void 0:xt[0])==null?void 0:_t.crypto),Z;at?Z=c=>{let t=[];for(let s=0,e;s<c;s+=4)t.push(at.getRandomValues(new Uint32Array(1))[0]);return new u(t,c)}:Z=c=>{let t=[],s=e=>{let r=e,o=987654321,n=4294967295;return()=>{o=36969*(o&65535)+(o>>16)&n,r=18e3*(r&65535)+(r>>16)&n;let h=(o<<16)+r&n;return h/=4294967296,h+=.5,h*(Math.random()>.5?1:-1)}};for(let e=0,r;e<c;e+=4){let o=s((r||Math.random())*4294967296);r=o()*987654071,t.push(o()*4294967296|0)}return new u(t,c)};var m=class{static create(...t){return new this(...t)}mixIn(t){return Object.assign(this,t)}clone(){let t=new this.constructor;return Object.assign(t,this),t}},u=class extends m{constructor(t=[],s=t.length*4){super();let e=t;if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){let r=e.byteLength,o=[];for(let n=0;n<r;n+=1)o[n>>>2]|=e[n]<<24-n%4*8;this.words=o,this.sigBytes=r}else this.words=t,this.sigBytes=s}toString(t=Mt){return t.stringify(this)}concat(t){let s=this.words,e=t.words,r=this.sigBytes,o=t.sigBytes;if(this.clamp(),r%4)for(let n=0;n<o;n+=1){let h=e[n>>>2]>>>24-n%4*8&255;s[r+n>>>2]|=h<<24-(r+n)%4*8}else for(let n=0;n<o;n+=4)s[r+n>>>2]=e[n>>>2];return this.sigBytes+=o,this}clamp(){let{words:t,sigBytes:s}=this;t[s>>>2]&=4294967295<<32-s%4*8,t.length=Math.ceil(s/4)}clone(){let t=super.clone.call(this);return t.words=this.words.slice(0),t}};it(u,\"random\",Z);var Mt={stringify(c){let{words:t,sigBytes:s}=c,e=[];for(let r=0;r<s;r+=1){let o=t[r>>>2]>>>24-r%4*8&255;e.push((o>>>4).toString(16)),e.push((o&15).toString(16))}return e.join(\"\")},parse(c){let t=c.length,s=[];for(let e=0;e<t;e+=2)s[e>>>3]|=parseInt(c.substr(e,2),16)<<24-e%8*4;return new u(s,t/2)}},ft={stringify(c){let{words:t,sigBytes:s}=c,e=[];for(let r=0;r<s;r+=1){let o=t[r>>>2]>>>24-r%4*8&255;e.push(String.fromCharCode(o))}return e.join(\"\")},parse(c){let t=c.length,s=[];for(let e=0;e<t;e+=1)s[e>>>2]|=(c.charCodeAt(e)&255)<<24-e%4*8;return new u(s,t)}},X={stringify(c){try{return decodeURIComponent(escape(ft.stringify(c)))}catch{throw new Error(\"Malformed UTF-8 data\")}},parse(c){return ft.parse(unescape(encodeURIComponent(c)))}},N=class extends m{constructor(){super(),this._minBufferSize=0}reset(){this._data=new u,this._nDataBytes=0}_append(t){let s=t;typeof s==\"string\"&&(s=X.parse(s)),this._data.concat(s),this._nDataBytes+=s.sigBytes}_process(t){let s,{_data:e,blockSize:r}=this,o=e.words,n=e.sigBytes,h=r*4,x=n/h;t?x=Math.ceil(x):x=Math.max((x|0)-this._minBufferSize,0);let p=x*r,_=Math.min(p*4,n);if(p){for(let y=0;y<p;y+=r)this._doProcessBlock(o,y);s=o.splice(0,p),e.sigBytes-=_}return new u(s,_)}clone(){let t=super.clone.call(this);return t._data=this._data.clone(),t}},H=class extends N{constructor(t){super(),this.blockSize=512/32,this.cfg=Object.assign(new m,t),this.reset()}static _createHelper(t){return(s,e)=>new t(e).finalize(s)}static _createHmacHelper(t){return(s,e)=>new $(t,e).finalize(s)}reset(){super.reset.call(this),this._doReset()}update(t){return this._append(t),this._process(),this}finalize(t){return t&&this._append(t),this._doFinalize()}},$=class extends m{constructor(t,s){super();let e=new t;this._hasher=e;let r=s;typeof r==\"string\"&&(r=X.parse(r));let o=e.blockSize,n=o*4;r.sigBytes>n&&(r=e.finalize(s)),r.clamp();let h=r.clone();this._oKey=h;let x=r.clone();this._iKey=x;let p=h.words,_=x.words;for(let y=0;y<o;y+=1)p[y]^=1549556828,_[y]^=909522486;h.sigBytes=n,x.sigBytes=n,this.reset()}reset(){let t=this._hasher;t.reset(),t.update(this._iKey)}update(t){return this._hasher.update(t),this}finalize(t){let s=this._hasher,e=s.finalize(t);return s.reset(),s.finalize(this._oKey.clone().concat(e))}};var zt=(c,t,s)=>{let e=[],r=0;for(let o=0;o<t;o+=1)if(o%4){let n=s[c.charCodeAt(o-1)]<<o%4*2,h=s[c.charCodeAt(o)]>>>6-o%4*2,x=n|h;e[r>>>2]|=x<<24-r%4*8,r+=1}return u.create(e,r)},tt={stringify(c){let{words:t,sigBytes:s}=c,e=this._map;c.clamp();let r=[];for(let n=0;n<s;n+=3){let h=t[n>>>2]>>>24-n%4*8&255,x=t[n+1>>>2]>>>24-(n+1)%4*8&255,p=t[n+2>>>2]>>>24-(n+2)%4*8&255,_=h<<16|x<<8|p;for(let y=0;y<4&&n+y*.75<s;y+=1)r.push(e.charAt(_>>>6*(3-y)&63))}let o=e.charAt(64);if(o)for(;r.length%4;)r.push(o);return r.join(\"\")},parse(c){let t=c.length,s=this._map,e=this._reverseMap;if(!e){this._reverseMap=[],e=this._reverseMap;for(let o=0;o<s.length;o+=1)e[s.charCodeAt(o)]=o}let r=s.charAt(64);if(r){let o=c.indexOf(r);o!==-1&&(t=o)}return zt(c,t,e)},_map:\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\"};var d=[];for(let c=0;c<64;c+=1)d[c]=Math.abs(Math.sin(c+1))*4294967296|0;var w=(c,t,s,e,r,o,n)=>{let h=c+(t&s|~t&e)+r+n;return(h<<o|h>>>32-o)+t},B=(c,t,s,e,r,o,n)=>{let h=c+(t&e|s&~e)+r+n;return(h<<o|h>>>32-o)+t},k=(c,t,s,e,r,o,n)=>{let h=c+(t^s^e)+r+n;return(h<<o|h>>>32-o)+t},b=(c,t,s,e,r,o,n)=>{let h=c+(s^(t|~e))+r+n;return(h<<o|h>>>32-o)+t},L=class extends H{_doReset(){this._hash=new u([1732584193,4023233417,2562383102,271733878])}_doProcessBlock(t,s){let e=t;for(let Y=0;Y<16;Y+=1){let ct=s+Y,G=t[ct];e[ct]=(G<<8|G>>>24)&16711935|(G<<24|G>>>8)&4278255360}let r=this._hash.words,o=e[s+0],n=e[s+1],h=e[s+2],x=e[s+3],p=e[s+4],_=e[s+5],y=e[s+6],M=e[s+7],z=e[s+8],v=e[s+9],g=e[s+10],O=e[s+11],S=e[s+12],P=e[s+13],I=e[s+14],W=e[s+15],i=r[0],a=r[1],f=r[2],l=r[3];i=w(i,a,f,l,o,7,d[0]),l=w(l,i,a,f,n,12,d[1]),f=w(f,l,i,a,h,17,d[2]),a=w(a,f,l,i,x,22,d[3]),i=w(i,a,f,l,p,7,d[4]),l=w(l,i,a,f,_,12,d[5]),f=w(f,l,i,a,y,17,d[6]),a=w(a,f,l,i,M,22,d[7]),i=w(i,a,f,l,z,7,d[8]),l=w(l,i,a,f,v,12,d[9]),f=w(f,l,i,a,g,17,d[10]),a=w(a,f,l,i,O,22,d[11]),i=w(i,a,f,l,S,7,d[12]),l=w(l,i,a,f,P,12,d[13]),f=w(f,l,i,a,I,17,d[14]),a=w(a,f,l,i,W,22,d[15]),i=B(i,a,f,l,n,5,d[16]),l=B(l,i,a,f,y,9,d[17]),f=B(f,l,i,a,O,14,d[18]),a=B(a,f,l,i,o,20,d[19]),i=B(i,a,f,l,_,5,d[20]),l=B(l,i,a,f,g,9,d[21]),f=B(f,l,i,a,W,14,d[22]),a=B(a,f,l,i,p,20,d[23]),i=B(i,a,f,l,v,5,d[24]),l=B(l,i,a,f,I,9,d[25]),f=B(f,l,i,a,x,14,d[26]),a=B(a,f,l,i,z,20,d[27]),i=B(i,a,f,l,P,5,d[28]),l=B(l,i,a,f,h,9,d[29]),f=B(f,l,i,a,M,14,d[30]),a=B(a,f,l,i,S,20,d[31]),i=k(i,a,f,l,_,4,d[32]),l=k(l,i,a,f,z,11,d[33]),f=k(f,l,i,a,O,16,d[34]),a=k(a,f,l,i,I,23,d[35]),i=k(i,a,f,l,n,4,d[36]),l=k(l,i,a,f,p,11,d[37]),f=k(f,l,i,a,M,16,d[38]),a=k(a,f,l,i,g,23,d[39]),i=k(i,a,f,l,P,4,d[40]),l=k(l,i,a,f,o,11,d[41]),f=k(f,l,i,a,x,16,d[42]),a=k(a,f,l,i,y,23,d[43]),i=k(i,a,f,l,v,4,d[44]),l=k(l,i,a,f,S,11,d[45]),f=k(f,l,i,a,W,16,d[46]),a=k(a,f,l,i,h,23,d[47]),i=b(i,a,f,l,o,6,d[48]),l=b(l,i,a,f,M,10,d[49]),f=b(f,l,i,a,I,15,d[50]),a=b(a,f,l,i,_,21,d[51]),i=b(i,a,f,l,S,6,d[52]),l=b(l,i,a,f,x,10,d[53]),f=b(f,l,i,a,g,15,d[54]),a=b(a,f,l,i,n,21,d[55]),i=b(i,a,f,l,z,6,d[56]),l=b(l,i,a,f,W,10,d[57]),f=b(f,l,i,a,y,15,d[58]),a=b(a,f,l,i,P,21,d[59]),i=b(i,a,f,l,p,6,d[60]),l=b(l,i,a,f,O,10,d[61]),f=b(f,l,i,a,h,15,d[62]),a=b(a,f,l,i,v,21,d[63]),r[0]=r[0]+i|0,r[1]=r[1]+a|0,r[2]=r[2]+f|0,r[3]=r[3]+l|0}_doFinalize(){let t=this._data,s=t.words,e=this._nDataBytes*8,r=t.sigBytes*8;s[r>>>5]|=128<<24-r%32;let o=Math.floor(e/4294967296),n=e;s[(r+64>>>9<<4)+15]=(o<<8|o>>>24)&16711935|(o<<24|o>>>8)&4278255360,s[(r+64>>>9<<4)+14]=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360,t.sigBytes=(s.length+1)*4,this._process();let h=this._hash,x=h.words;for(let p=0;p<4;p+=1){let _=x[p];x[p]=(_<<8|_>>>24)&16711935|(_<<24|_>>>8)&4278255360}return h}clone(){let t=super.clone.call(this);return t._hash=this._hash.clone(),t}},St=H._createHelper(L),Pt=H._createHmacHelper(L);var T=class extends m{constructor(t){super(),this.cfg=Object.assign(new m,{keySize:128/32,hasher:L,iterations:1},t)}compute(t,s){let e,{cfg:r}=this,o=r.hasher.create(),n=u.create(),h=n.words,{keySize:x,iterations:p}=r;for(;h.length<x;){e&&o.update(e),e=o.update(t).finalize(s),o.reset();for(let _=1;_<p;_+=1)e=o.finalize(e),o.reset();n.concat(e)}return n.sigBytes=x*4,n}};var C=class extends N{constructor(t,s,e){super(),this.cfg=Object.assign(new m,e),this._xformMode=t,this._key=s,this.reset()}static createEncryptor(t,s){return this.create(this._ENC_XFORM_MODE,t,s)}static createDecryptor(t,s){return this.create(this._DEC_XFORM_MODE,t,s)}static _createHelper(t){let s=e=>typeof e==\"string\"?q:E;return{encrypt(e,r,o){return s(r).encrypt(t,e,r,o)},decrypt(e,r,o){return s(r).decrypt(t,e,r,o)}}}reset(){super.reset.call(this),this._doReset()}process(t){return this._append(t),this._process()}finalize(t){return t&&this._append(t),this._doFinalize()}};C._ENC_XFORM_MODE=1;C._DEC_XFORM_MODE=2;C.keySize=128/32;C.ivSize=128/32;var et=class extends m{constructor(t,s){super(),this._cipher=t,this._iv=s}static createEncryptor(t,s){return this.Encryptor.create(t,s)}static createDecryptor(t,s){return this.Decryptor.create(t,s)}};function yt(c,t,s){let e=c,r,o=this._iv;o?(r=o,this._iv=void 0):r=this._prevBlock;for(let n=0;n<s;n+=1)e[t+n]^=r[n]}var j=class extends et{};j.Encryptor=class extends j{processBlock(c,t){let s=this._cipher,{blockSize:e}=s;yt.call(this,c,t,e),s.encryptBlock(c,t),this._prevBlock=c.slice(t,t+e)}};j.Decryptor=class extends j{processBlock(c,t){let s=this._cipher,{blockSize:e}=s,r=c.slice(t,t+e);s.decryptBlock(c,t),yt.call(this,c,t,e),this._prevBlock=r}};var vt={pad(c,t){let s=t*4,e=s-c.sigBytes%s,r=e<<24|e<<16|e<<8|e,o=[];for(let h=0;h<e;h+=4)o.push(r);let n=u.create(o,e);c.concat(n)},unpad(c){let t=c,s=t.words[t.sigBytes-1>>>2]&255;t.sigBytes-=s}},U=class extends C{constructor(t,s,e){super(t,s,Object.assign({mode:j,padding:vt},e)),this.blockSize=128/32}reset(){let t;super.reset.call(this);let{cfg:s}=this,{iv:e,mode:r}=s;this._xformMode===this.constructor._ENC_XFORM_MODE?t=r.createEncryptor:(t=r.createDecryptor,this._minBufferSize=1),this._mode=t.call(r,this,e&&e.words),this._mode.__creator=t}_doProcessBlock(t,s){this._mode.processBlock(t,s)}_doFinalize(){let t,{padding:s}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(s.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),s.unpad(t)),t}},V=class extends m{constructor(t){super(),this.mixIn(t)}toString(t){return(t||this.formatter).stringify(this)}},Rt={stringify(c){let t,{ciphertext:s,salt:e}=c;return e?t=u.create([1398893684,1701076831]).concat(e).concat(s):t=s,t.toString(tt)},parse(c){let t,s=tt.parse(c),e=s.words;return e[0]===1398893684&&e[1]===1701076831&&(t=u.create(e.slice(2,4)),e.splice(0,4),s.sigBytes-=16),V.create({ciphertext:s,salt:t})}},E=class extends m{static encrypt(t,s,e,r){let o=Object.assign(new m,this.cfg,r),n=t.createEncryptor(e,o),h=n.finalize(s),x=n.cfg;return V.create({ciphertext:h,key:e,iv:x.iv,algorithm:t,mode:x.mode,padding:x.padding,blockSize:n.blockSize,formatter:o.format})}static decrypt(t,s,e,r){let o=s,n=Object.assign(new m,this.cfg,r);return o=this._parse(o,n.format),t.createDecryptor(e,n).finalize(o.ciphertext)}static _parse(t,s){return typeof t==\"string\"?s.parse(t,this):t}};E.cfg=Object.assign(new m,{format:Rt});var Ft={execute(c,t,s,e,r){let o=e;o||(o=u.random(64/8));let n;r?n=T.create({keySize:t+s,hasher:r}).compute(c,o):n=T.create({keySize:t+s}).compute(c,o);let h=u.create(n.words.slice(t),s*4);return n.sigBytes=t*4,V.create({key:n,iv:h,salt:o})}},q=class extends E{static encrypt(t,s,e,r){let o=Object.assign(new m,this.cfg,r),n=o.kdf.execute(e,t.keySize,t.ivSize,o.salt,o.hasher);o.iv=n.iv;let h=E.encrypt.call(this,t,s,n.key,o);return h.mixIn(n),h}static decrypt(t,s,e,r){let o=s,n=Object.assign(new m,this.cfg,r);o=this._parse(o,n.format);let h=n.kdf.execute(e,t.keySize,t.ivSize,o.salt,n.hasher);return n.iv=h.iv,E.decrypt.call(this,t,o,h.key,n)}};q.cfg=Object.assign(E.cfg,{kdf:Ft});var R=[],ut=[],gt=[],mt=[],wt=[],Bt=[],st=[],rt=[],ot=[],nt=[],A=[];for(let c=0;c<256;c+=1)c<128?A[c]=c<<1:A[c]=c<<1^283;var F=0,D=0;for(let c=0;c<256;c+=1){let t=D^D<<1^D<<2^D<<3^D<<4;t=t>>>8^t&255^99,R[F]=t,ut[t]=F;let s=A[F],e=A[s],r=A[e],o=A[t]*257^t*16843008;gt[F]=o<<24|o>>>8,mt[F]=o<<16|o>>>16,wt[F]=o<<8|o>>>24,Bt[F]=o,o=r*16843009^e*65537^s*257^F*16843008,st[t]=o<<24|o>>>8,rt[t]=o<<16|o>>>16,ot[t]=o<<8|o>>>24,nt[t]=o,F?(F=s^A[A[A[r^s]]],D^=A[A[D]]):(D=1,F=D)}var At=[0,1,2,4,8,16,32,64,128,27,54],J=class extends U{_doReset(){let t;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let s=this._keyPriorReset,e=s.words,r=s.sigBytes/4;this._nRounds=r+6;let n=(this._nRounds+1)*4;this._keySchedule=[];let h=this._keySchedule;for(let p=0;p<n;p+=1)p<r?h[p]=e[p]:(t=h[p-1],p%r?r>6&&p%r===4&&(t=R[t>>>24]<<24|R[t>>>16&255]<<16|R[t>>>8&255]<<8|R[t&255]):(t=t<<8|t>>>24,t=R[t>>>24]<<24|R[t>>>16&255]<<16|R[t>>>8&255]<<8|R[t&255],t^=At[p/r|0]<<24),h[p]=h[p-r]^t);this._invKeySchedule=[];let x=this._invKeySchedule;for(let p=0;p<n;p+=1){let _=n-p;p%4?t=h[_]:t=h[_-4],p<4||_<=4?x[p]=t:x[p]=st[R[t>>>24]]^rt[R[t>>>16&255]]^ot[R[t>>>8&255]]^nt[R[t&255]]}}encryptBlock(t,s){this._doCryptBlock(t,s,this._keySchedule,gt,mt,wt,Bt,R)}decryptBlock(t,s){let e=t,r=e[s+1];e[s+1]=e[s+3],e[s+3]=r,this._doCryptBlock(e,s,this._invKeySchedule,st,rt,ot,nt,ut),r=e[s+1],e[s+1]=e[s+3],e[s+3]=r}_doCryptBlock(t,s,e,r,o,n,h,x){let p=t,_=this._nRounds,y=p[s]^e[0],M=p[s+1]^e[1],z=p[s+2]^e[2],v=p[s+3]^e[3],g=4;for(let W=1;W<_;W+=1){let i=r[y>>>24]^o[M>>>16&255]^n[z>>>8&255]^h[v&255]^e[g];g+=1;let a=r[M>>>24]^o[z>>>16&255]^n[v>>>8&255]^h[y&255]^e[g];g+=1;let f=r[z>>>24]^o[v>>>16&255]^n[y>>>8&255]^h[M&255]^e[g];g+=1;let l=r[v>>>24]^o[y>>>16&255]^n[M>>>8&255]^h[z&255]^e[g];g+=1,y=i,M=a,z=f,v=l}let O=(x[y>>>24]<<24|x[M>>>16&255]<<16|x[z>>>8&255]<<8|x[v&255])^e[g];g+=1;let S=(x[M>>>24]<<24|x[z>>>16&255]<<16|x[v>>>8&255]<<8|x[y&255])^e[g];g+=1;let P=(x[z>>>24]<<24|x[v>>>16&255]<<16|x[y>>>8&255]<<8|x[M&255])^e[g];g+=1;let I=(x[v>>>24]<<24|x[y>>>16&255]<<16|x[M>>>8&255]<<8|x[z&255])^e[g];g+=1,p[s]=O,p[s+1]=S,p[s+2]=P,p[s+3]=I}};J.keySize=256/32;var Ht=U._createHelper(J);var K=[],Q=class extends H{_doReset(){this._hash=new u([1732584193,4023233417,2562383102,271733878,3285377520])}_doProcessBlock(t,s){let e=this._hash.words,r=e[0],o=e[1],n=e[2],h=e[3],x=e[4];for(let p=0;p<80;p+=1){if(p<16)K[p]=t[s+p]|0;else{let y=K[p-3]^K[p-8]^K[p-14]^K[p-16];K[p]=y<<1|y>>>31}let _=(r<<5|r>>>27)+x+K[p];p<20?_+=(o&n|~o&h)+1518500249:p<40?_+=(o^n^h)+1859775393:p<60?_+=(o&n|o&h|n&h)-1894007588:_+=(o^n^h)-899497514,x=h,h=n,n=o<<30|o>>>2,o=r,r=_}e[0]=e[0]+r|0,e[1]=e[1]+o|0,e[2]=e[2]+n|0,e[3]=e[3]+h|0,e[4]=e[4]+x|0}_doFinalize(){let t=this._data,s=t.words,e=this._nDataBytes*8,r=t.sigBytes*8;return s[r>>>5]|=128<<24-r%32,s[(r+64>>>9<<4)+14]=Math.floor(e/4294967296),s[(r+64>>>9<<4)+15]=e,t.sigBytes=s.length*4,this._process(),this._hash}clone(){let t=super.clone.call(this);return t._hash=this._hash.clone(),t}},Xt=H._createHelper(Q),Dt=H._createHmacHelper(Q);export{Ht as AES,Dt as HmacSHA1,X as Utf8};\n", "import \"../chunk-BUSYA2B4.js\";\nimport { constants } from \"@clerk/backend/internal\";\nimport { isDevelopmentFromSecretKey } from \"@clerk/shared/keys\";\nimport { logger } from \"@clerk/shared/logger\";\nimport { isHttpOrHttps } from \"@clerk/shared/proxy\";\nimport { handleValueOrFn, isProductionEnvironment } from \"@clerk/shared/utils\";\nimport { NextResponse } from \"next/server\";\nimport { constants as nextConstants } from \"../constants\";\nimport { canUseKeyless } from \"../utils/feature-flags\";\nimport { AES, HmacSHA1, Utf8 } from \"../vendor/crypto-es\";\nimport { DOMAIN, ENCRYPTION_KEY, IS_SATELLITE, PROXY_URL, SECRET_KEY, SIGN_IN_URL } from \"./constants\";\nimport {\n  authSignatureInvalid,\n  encryptionKeyInvalid,\n  encryptionKeyInvalidDev,\n  missingDomainAndProxy,\n  missingSignInUrlInDev\n} from \"./errors\";\nimport { errorThrower } from \"./errorThrower\";\nimport { detectClerkMiddleware } from \"./headers-utils\";\nconst OVERRIDE_HEADERS = \"x-middleware-override-headers\";\nconst MIDDLEWARE_HEADER_PREFIX = \"x-middleware-request\";\nconst setRequestHeadersOnNextResponse = (res, req, newHeaders) => {\n  if (!res.headers.get(OVERRIDE_HEADERS)) {\n    res.headers.set(OVERRIDE_HEADERS, [...req.headers.keys()]);\n    req.headers.forEach((val, key) => {\n      res.headers.set(`${MIDDLEWARE_HEADER_PREFIX}-${key}`, val);\n    });\n  }\n  Object.entries(newHeaders).forEach(([key, val]) => {\n    res.headers.set(OVERRIDE_HEADERS, `${res.headers.get(OVERRIDE_HEADERS)},${key}`);\n    res.headers.set(`${MIDDLEWARE_HEADER_PREFIX}-${key}`, val);\n  });\n};\nfunction decorateRequest(req, res, requestState, requestData, keylessMode) {\n  const { reason, message, status, token } = requestState;\n  if (!res) {\n    res = NextResponse.next();\n  }\n  if (res.headers.get(nextConstants.Headers.NextRedirect)) {\n    return res;\n  }\n  let rewriteURL;\n  if (res.headers.get(nextConstants.Headers.NextResume) === \"1\") {\n    res.headers.delete(nextConstants.Headers.NextResume);\n    rewriteURL = new URL(req.url);\n  }\n  const rewriteURLHeader = res.headers.get(nextConstants.Headers.NextRewrite);\n  if (rewriteURLHeader) {\n    const reqURL = new URL(req.url);\n    rewriteURL = new URL(rewriteURLHeader);\n    if (rewriteURL.origin !== reqURL.origin) {\n      return res;\n    }\n  }\n  if (rewriteURL) {\n    const clerkRequestData = encryptClerkRequestData(requestData, keylessMode);\n    setRequestHeadersOnNextResponse(res, req, {\n      [constants.Headers.AuthStatus]: status,\n      [constants.Headers.AuthToken]: token || \"\",\n      [constants.Headers.AuthSignature]: token ? createTokenSignature(token, (requestData == null ? void 0 : requestData.secretKey) || SECRET_KEY || keylessMode.secretKey || \"\") : \"\",\n      [constants.Headers.AuthMessage]: message || \"\",\n      [constants.Headers.AuthReason]: reason || \"\",\n      [constants.Headers.ClerkUrl]: req.clerkUrl.toString(),\n      ...clerkRequestData ? { [constants.Headers.ClerkRequestData]: clerkRequestData } : {}\n    });\n    res.headers.set(nextConstants.Headers.NextRewrite, rewriteURL.href);\n  }\n  return res;\n}\nconst handleMultiDomainAndProxy = (clerkRequest, opts) => {\n  const relativeOrAbsoluteProxyUrl = handleValueOrFn(opts == null ? void 0 : opts.proxyUrl, clerkRequest.clerkUrl, PROXY_URL);\n  let proxyUrl;\n  if (!!relativeOrAbsoluteProxyUrl && !isHttpOrHttps(relativeOrAbsoluteProxyUrl)) {\n    proxyUrl = new URL(relativeOrAbsoluteProxyUrl, clerkRequest.clerkUrl).toString();\n  } else {\n    proxyUrl = relativeOrAbsoluteProxyUrl;\n  }\n  const isSatellite = handleValueOrFn(opts.isSatellite, new URL(clerkRequest.url), IS_SATELLITE);\n  const domain = handleValueOrFn(opts.domain, new URL(clerkRequest.url), DOMAIN);\n  const signInUrl = (opts == null ? void 0 : opts.signInUrl) || SIGN_IN_URL;\n  if (isSatellite && !proxyUrl && !domain) {\n    throw new Error(missingDomainAndProxy);\n  }\n  if (isSatellite && !isHttpOrHttps(signInUrl) && isDevelopmentFromSecretKey(opts.secretKey || SECRET_KEY)) {\n    throw new Error(missingSignInUrlInDev);\n  }\n  return {\n    proxyUrl,\n    isSatellite,\n    domain,\n    signInUrl\n  };\n};\nconst redirectAdapter = (url) => {\n  return NextResponse.redirect(url, { headers: { [constants.Headers.ClerkRedirectTo]: \"true\" } });\n};\nfunction assertAuthStatus(req, error) {\n  if (!detectClerkMiddleware(req)) {\n    throw new Error(error);\n  }\n}\nfunction assertKey(key, onError) {\n  if (!key) {\n    onError();\n  }\n  return key;\n}\nfunction createTokenSignature(token, key) {\n  return HmacSHA1(token, key).toString();\n}\nfunction assertTokenSignature(token, key, signature) {\n  if (!signature) {\n    throw new Error(authSignatureInvalid);\n  }\n  const expectedSignature = createTokenSignature(token, key);\n  if (expectedSignature !== signature) {\n    throw new Error(authSignatureInvalid);\n  }\n}\nconst KEYLESS_ENCRYPTION_KEY = \"clerk_keyless_dummy_key\";\nfunction encryptClerkRequestData(requestData, keylessModeKeys) {\n  const isEmpty = (obj) => {\n    if (!obj) {\n      return true;\n    }\n    return !Object.values(obj).some((v) => v !== void 0);\n  };\n  if (isEmpty(requestData) && isEmpty(keylessModeKeys)) {\n    return;\n  }\n  if (requestData.secretKey && !ENCRYPTION_KEY) {\n    logger.warnOnce(\n      \"Clerk: Missing `CLERK_ENCRYPTION_KEY`. Required for propagating `secretKey` middleware option. See docs: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys\"\n    );\n    return;\n  }\n  const maybeKeylessEncryptionKey = isProductionEnvironment() ? ENCRYPTION_KEY || assertKey(SECRET_KEY, () => errorThrower.throwMissingSecretKeyError()) : ENCRYPTION_KEY || SECRET_KEY || KEYLESS_ENCRYPTION_KEY;\n  return AES.encrypt(JSON.stringify({ ...keylessModeKeys, ...requestData }), maybeKeylessEncryptionKey).toString();\n}\nfunction decryptClerkRequestData(encryptedRequestData) {\n  if (!encryptedRequestData) {\n    return {};\n  }\n  const maybeKeylessEncryptionKey = isProductionEnvironment() ? ENCRYPTION_KEY || SECRET_KEY : ENCRYPTION_KEY || SECRET_KEY || KEYLESS_ENCRYPTION_KEY;\n  try {\n    return decryptData(encryptedRequestData, maybeKeylessEncryptionKey);\n  } catch {\n    if (canUseKeyless) {\n      try {\n        return decryptData(encryptedRequestData, KEYLESS_ENCRYPTION_KEY);\n      } catch {\n        throwInvalidEncryptionKey();\n      }\n    }\n    throwInvalidEncryptionKey();\n  }\n}\nfunction throwInvalidEncryptionKey() {\n  if (isProductionEnvironment()) {\n    throw new Error(encryptionKeyInvalid);\n  }\n  throw new Error(encryptionKeyInvalidDev);\n}\nfunction decryptData(data, key) {\n  const decryptedBytes = AES.decrypt(data, key);\n  const encoded = decryptedBytes.toString(Utf8);\n  return JSON.parse(encoded);\n}\nexport {\n  assertAuthStatus,\n  assertKey,\n  assertTokenSignature,\n  decorateRequest,\n  decryptClerkRequestData,\n  encryptClerkRequestData,\n  handleMultiDomainAndProxy,\n  redirectAdapter,\n  setRequestHeadersOnNextResponse\n};\n//# sourceMappingURL=utils.js.map", "// src/jwt/legacyReturn.ts\nfunction withLegacyReturn(cb) {\n  return async (...args) => {\n    const { data, errors } = await cb(...args);\n    if (errors) {\n      throw errors[0];\n    }\n    return data;\n  };\n}\nfunction withLegacySyncReturn(cb) {\n  return (...args) => {\n    const { data, errors } = cb(...args);\n    if (errors) {\n      throw errors[0];\n    }\n    return data;\n  };\n}\n\nexport {\n  withLegacyReturn,\n  withLegacySyncReturn\n};\n//# sourceMappingURL=chunk-P263NW7Z.mjs.map", "import \"../../chunk-BUSYA2B4.js\";\nimport { NextRequest } from \"next/server\";\nconst isPrerenderingBailout = (e) => {\n  if (!(e instanceof Error) || !(\"message\" in e)) {\n    return false;\n  }\n  const { message } = e;\n  const lowerCaseInput = message.toLowerCase();\n  const dynamicServerUsage = lowerCaseInput.includes(\"dynamic server usage\");\n  const bailOutPrerendering = lowerCaseInput.includes(\"this page needs to bail out of prerendering\");\n  const routeRegex = /Route .*? needs to bail out of prerendering at this point because it used .*?./;\n  return routeRegex.test(message) || dynamicServerUsage || bailOutPrerendering;\n};\nasync function buildRequestLike() {\n  try {\n    const { headers } = await import(\"next/headers\");\n    const resolvedHeaders = await headers();\n    return new NextRequest(\"https://placeholder.com\", { headers: resolvedHeaders });\n  } catch (e) {\n    if (e && isPrerenderingBailout(e)) {\n      throw e;\n    }\n    throw new Error(\n      `Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).\nIf you're using /pages, try getAuth() instead.\nOriginal error: ${e}`\n    );\n  }\n}\nfunction getScriptNonceFromHeader(cspHeaderValue) {\n  var _a;\n  const directives = cspHeaderValue.split(\";\").map((directive2) => directive2.trim());\n  const directive = directives.find((dir) => dir.startsWith(\"script-src\")) || directives.find((dir) => dir.startsWith(\"default-src\"));\n  if (!directive) {\n    return;\n  }\n  const nonce = (_a = directive.split(\" \").slice(1).map((source) => source.trim()).find((source) => source.startsWith(\"'nonce-\") && source.length > 8 && source.endsWith(\"'\"))) == null ? void 0 : _a.slice(7, -1);\n  if (!nonce) {\n    return;\n  }\n  if (/[&><\\u2028\\u2029]/g.test(nonce)) {\n    throw new Error(\n      \"Nonce value from Content-Security-Policy contained invalid HTML escape characters, which is disallowed for security reasons. Make sure that your nonce value does not contain the following characters: `<`, `>`, `&`\"\n    );\n  }\n  return nonce;\n}\nexport {\n  buildRequestLike,\n  getScriptNonceFromHeader,\n  isPrerenderingBailout\n};\n//# sourceMappingURL=utils.js.map", "import \"../chunk-BUSYA2B4.js\";\nconst maskSecretKey = (str) => {\n  if (!str || typeof str !== \"string\") {\n    return str;\n  }\n  try {\n    return (str || \"\").replace(/^(sk_(live|test)_)(.+?)(.{3})$/, \"$1*********$4\");\n  } catch {\n    return \"\";\n  }\n};\nconst logFormatter = (entry) => {\n  return (Array.isArray(entry) ? entry : [entry]).map((entry2) => {\n    if (typeof entry2 === \"string\") {\n      return maskSecretKey(entry2);\n    }\n    const masked = Object.fromEntries(Object.entries(entry2).map(([k, v]) => [k, maskSecretKey(v)]));\n    return JSON.stringify(masked, null, 2);\n  }).join(\", \");\n};\nexport {\n  logFormatter\n};\n//# sourceMappingURL=logFormatter.js.map", "import \"../chunk-BUSYA2B4.js\";\nimport nextPkg from \"next/package.json\";\nimport { logFormatter } from \"./logFormatter\";\nconst createDebugLogger = (name, formatter) => () => {\n  const entries = [];\n  let isEnabled = false;\n  return {\n    enable: () => {\n      isEnabled = true;\n    },\n    debug: (...args) => {\n      if (isEnabled) {\n        entries.push(args.map((arg) => typeof arg === \"function\" ? arg() : arg));\n      }\n    },\n    commit: () => {\n      if (isEnabled) {\n        console.log(debugLogHeader(name));\n        for (const log of entries) {\n          let output = formatter(log);\n          output = output.split(\"\\n\").map((l) => `  ${l}`).join(\"\\n\");\n          if (process.env.VERCEL) {\n            output = truncate(output, 4096);\n          }\n          console.log(output);\n        }\n        console.log(debugLogFooter(name));\n      }\n    }\n  };\n};\nconst withLogger = (loggerFactoryOrName, handlerCtor) => {\n  return (...args) => {\n    const factory = typeof loggerFactoryOrName === \"string\" ? createDebugLogger(loggerFactoryOrName, logFormatter) : loggerFactoryOrName;\n    const logger = factory();\n    const handler = handlerCtor(logger);\n    try {\n      const res = handler(...args);\n      if (typeof res === \"object\" && \"then\" in res && typeof res.then === \"function\") {\n        return res.then((val) => {\n          logger.commit();\n          return val;\n        }).catch((err) => {\n          logger.commit();\n          throw err;\n        });\n      }\n      logger.commit();\n      return res;\n    } catch (err) {\n      logger.commit();\n      throw err;\n    }\n  };\n};\nfunction debugLogHeader(name) {\n  return `[clerk debug start: ${name}]`;\n}\nfunction debugLogFooter(name) {\n  return `[clerk debug end: ${name}] (@clerk/nextjs=${\"6.12.12\"},next=${nextPkg.version},timestamp=${Math.round((/* @__PURE__ */ new Date()).getTime() / 1e3)})`;\n}\nfunction truncate(str, maxLength) {\n  const encoder = new TextEncoder();\n  const decoder = new TextDecoder(\"utf-8\");\n  const encodedString = encoder.encode(str);\n  const truncatedString = encodedString.slice(0, maxLength);\n  return decoder.decode(truncatedString).replace(/\\uFFFD/g, \"\");\n}\nexport {\n  createDebugLogger,\n  withLogger\n};\n//# sourceMappingURL=debugLogger.js.map", "import {\n  withLegacyReturn,\n  withLegacySyncReturn\n} from \"../chunk-P263NW7Z.mjs\";\nimport {\n  base64url,\n  decodeJwt,\n  getCryptoAlgorithm,\n  hasValidSignature,\n  importKey,\n  runtime,\n  verifyJwt\n} from \"../chunk-AT3FJU3M.mjs\";\nimport {\n  SignJWTError\n} from \"../chunk-5JS2VYLU.mjs\";\n\n// src/jwt/signJwt.ts\nfunction encodeJwtData(value) {\n  const stringified = JSON.stringify(value);\n  const encoder = new TextEncoder();\n  const encoded = encoder.encode(stringified);\n  return base64url.stringify(encoded, { pad: false });\n}\nasync function signJwt(payload, key, options) {\n  if (!options.algorithm) {\n    throw new Error(\"No algorithm specified\");\n  }\n  const encoder = new TextEncoder();\n  const algorithm = getCryptoAlgorithm(options.algorithm);\n  if (!algorithm) {\n    return {\n      errors: [new SignJWTError(`Unsupported algorithm ${options.algorithm}`)]\n    };\n  }\n  const cryptoKey = await import<PERSON>ey(key, algorithm, \"sign\");\n  const header = options.header || { typ: \"JWT\" };\n  header.alg = options.algorithm;\n  payload.iat = Math.floor(Date.now() / 1e3);\n  const encodedHeader = encodeJwtData(header);\n  const encodedPayload = encodeJwtData(payload);\n  const firstPart = `${encodedHeader}.${encodedPayload}`;\n  try {\n    const signature = await runtime.crypto.subtle.sign(algorithm, cryptoKey, encoder.encode(firstPart));\n    const encodedSignature = `${firstPart}.${base64url.stringify(new Uint8Array(signature), { pad: false })}`;\n    return { data: encodedSignature };\n  } catch (error) {\n    return { errors: [new SignJWTError(error?.message)] };\n  }\n}\n\n// src/jwt/index.ts\nvar verifyJwt2 = withLegacyReturn(verifyJwt);\nvar decodeJwt2 = withLegacySyncReturn(decodeJwt);\nvar signJwt2 = withLegacyReturn(signJwt);\nvar hasValidSignature2 = withLegacyReturn(hasValidSignature);\nexport {\n  decodeJwt2 as decodeJwt,\n  hasValidSignature2 as hasValidSignature,\n  signJwt2 as signJwt,\n  verifyJwt2 as verifyJwt\n};\n//# sourceMappingURL=index.mjs.map", "import \"../../chunk-BUSYA2B4.js\";\nimport { AuthStatus, constants, signedInAuthObject, signedOutAuthObject } from \"@clerk/backend/internal\";\nimport { decodeJwt } from \"@clerk/backend/jwt\";\nimport { API_URL, API_VERSION, PUBLISHABLE_KEY, SECRET_KEY } from \"../constants\";\nimport { getAuthKeyFromRequest, getHeader } from \"../headers-utils\";\nimport { assertTokenSignature, decryptClerkRequestData } from \"../utils\";\nfunction getAuthDataFromRequest(req, opts = {}) {\n  var _a, _b, _c;\n  const authStatus = getAuthKeyFromRequest(req, \"AuthStatus\");\n  const authToken = getAuthKeyFromRequest(req, \"AuthToken\");\n  const authMessage = getAuthKeyFromRequest(req, \"AuthMessage\");\n  const authReason = getAuthKeyFromRequest(req, \"AuthReason\");\n  const authSignature = getAuthKeyFromRequest(req, \"AuthSignature\");\n  (_a = opts.logger) == null ? void 0 : _a.debug(\"headers\", { authStatus, authMessage, authReason });\n  const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);\n  const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n  const options = {\n    secretKey: (opts == null ? void 0 : opts.secretKey) || decryptedRequestData.secretKey || SECRET_KEY,\n    publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n    apiUrl: API_URL,\n    apiVersion: API_VERSION,\n    authStatus,\n    authMessage,\n    authReason\n  };\n  (_b = opts.logger) == null ? void 0 : _b.debug(\"auth options\", options);\n  let authObject;\n  if (!authStatus || authStatus !== AuthStatus.SignedIn) {\n    authObject = signedOutAuthObject(options);\n  } else {\n    assertTokenSignature(authToken, options.secretKey, authSignature);\n    const jwt = decodeJwt(authToken);\n    (_c = opts.logger) == null ? void 0 : _c.debug(\"jwt\", jwt.raw);\n    authObject = signedInAuthObject(options, jwt.raw.text, jwt.payload);\n  }\n  return authObject;\n}\nexport {\n  getAuthDataFromRequest\n};\n//# sourceMappingURL=getAuthDataFromRequest.js.map", "import \"../chunk-BUSYA2B4.js\";\nimport { constants } from \"@clerk/backend/internal\";\nimport { isTruthy } from \"@clerk/shared/underscore\";\nimport { withLogger } from \"../utils/debugLogger\";\nimport { isNextWithUnstableServerActions } from \"../utils/sdk-versions\";\nimport { getAuthDataFromRequest } from \"./data/getAuthDataFromRequest\";\nimport { getAuthAuthHeaderMissing } from \"./errors\";\nimport { detectClerkMiddleware, getHeader } from \"./headers-utils\";\nimport { assertAuthStatus } from \"./utils\";\nconst createAsyncGetAuth = ({\n  debugLoggerName,\n  noAuthStatusMessage\n}) => withLogger(debugLoggerName, (logger) => {\n  return async (req, opts) => {\n    if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {\n      logger.enable();\n    }\n    if (!detectClerkMiddleware(req)) {\n      if (isNextWithUnstableServerActions) {\n        assertAuthStatus(req, noAuthStatusMessage);\n      }\n      const missConfiguredMiddlewareLocation = await import(\"./fs/middleware-location.js\").then((m) => m.suggestMiddlewareLocation()).catch(() => void 0);\n      if (missConfiguredMiddlewareLocation) {\n        throw new Error(missConfiguredMiddlewareLocation);\n      }\n      assertAuthStatus(req, noAuthStatusMessage);\n    }\n    return getAuthDataFromRequest(req, { ...opts, logger });\n  };\n});\nconst createSyncGetAuth = ({\n  debugLoggerName,\n  noAuthStatusMessage\n}) => withLogger(debugLoggerName, (logger) => {\n  return (req, opts) => {\n    if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {\n      logger.enable();\n    }\n    assertAuthStatus(req, noAuthStatusMessage);\n    return getAuthDataFromRequest(req, { ...opts, logger });\n  };\n});\nconst getAuth = createSyncGetAuth({\n  debugLoggerName: \"getAuth()\",\n  noAuthStatusMessage: getAuthAuthHeaderMissing()\n});\nexport {\n  createAsyncGetAuth,\n  createSyncGetAuth,\n  getAuth\n};\n//# sourceMappingURL=createGetAuth.js.map", "import \"./chunk-BUSYA2B4.js\";\nconst Headers = {\n  NextRewrite: \"x-middleware-rewrite\",\n  NextResume: \"x-middleware-next\",\n  NextRedirect: \"Location\",\n  // Used by next to identify internal navigation for app router\n  NextUrl: \"next-url\",\n  NextAction: \"next-action\",\n  // Used by next to identify internal navigation for pages router\n  NextjsData: \"x-nextjs-data\"\n};\nconst constants = {\n  Headers\n};\nexport {\n  constants\n};\n//# sourceMappingURL=constants.js.map", "import \"../chunk-BUSYA2B4.js\";\nfunction isNextFetcher(fetch) {\n  return \"__nextPatched\" in fetch && fetch.__nextPatched === true;\n}\nexport {\n  isNextFetcher\n};\n//# sourceMappingURL=nextFetcher.js.map", "import \"../chunk-BUSYA2B4.js\";\nimport { constants } from \"@clerk/backend/internal\";\nimport { constants as nextConstants } from \"../constants\";\nimport { isNextFetcher } from \"./nextFetcher\";\nfunction createProtect(opts) {\n  const { redirectToSignIn, authObject, redirect, notFound, request } = opts;\n  return async (...args) => {\n    var _a, _b, _c, _d, _e, _f;\n    const optionValuesAsParam = ((_a = args[0]) == null ? void 0 : _a.unauthenticatedUrl) || ((_b = args[0]) == null ? void 0 : _b.unauthorizedUrl);\n    const paramsOrFunction = optionValuesAsParam ? void 0 : args[0];\n    const unauthenticatedUrl = ((_c = args[0]) == null ? void 0 : _c.unauthenticatedUrl) || ((_d = args[1]) == null ? void 0 : _d.unauthenticatedUrl);\n    const unauthorizedUrl = ((_e = args[0]) == null ? void 0 : _e.unauthorizedUrl) || ((_f = args[1]) == null ? void 0 : _f.unauthorizedUrl);\n    const handleUnauthenticated = () => {\n      if (unauthenticatedUrl) {\n        return redirect(unauthenticatedUrl);\n      }\n      if (isPageRequest(request)) {\n        return redirectToSignIn();\n      }\n      return notFound();\n    };\n    const handleUnauthorized = () => {\n      if (unauthorizedUrl) {\n        return redirect(unauthorizedUrl);\n      }\n      return notFound();\n    };\n    if (!authObject.userId) {\n      return handleUnauthenticated();\n    }\n    if (!paramsOrFunction) {\n      return authObject;\n    }\n    if (typeof paramsOrFunction === \"function\") {\n      if (paramsOrFunction(authObject.has)) {\n        return authObject;\n      }\n      return handleUnauthorized();\n    }\n    if (authObject.has(paramsOrFunction)) {\n      return authObject;\n    }\n    return handleUnauthorized();\n  };\n}\nconst isServerActionRequest = (req) => {\n  var _a, _b;\n  return !!req.headers.get(nextConstants.Headers.NextUrl) && (((_a = req.headers.get(constants.Headers.Accept)) == null ? void 0 : _a.includes(\"text/x-component\")) || ((_b = req.headers.get(constants.Headers.ContentType)) == null ? void 0 : _b.includes(\"multipart/form-data\")) || !!req.headers.get(nextConstants.Headers.NextAction));\n};\nconst isPageRequest = (req) => {\n  var _a;\n  return req.headers.get(constants.Headers.SecFetchDest) === \"document\" || req.headers.get(constants.Headers.SecFetchDest) === \"iframe\" || ((_a = req.headers.get(constants.Headers.Accept)) == null ? void 0 : _a.includes(\"text/html\")) || isAppRouterInternalNavigation(req) || isPagesRouterInternalNavigation(req);\n};\nconst isAppRouterInternalNavigation = (req) => !!req.headers.get(nextConstants.Headers.NextUrl) && !isServerActionRequest(req) || isPagePathAvailable();\nconst isPagePathAvailable = () => {\n  const __fetch = globalThis.fetch;\n  if (!isNextFetcher(__fetch)) {\n    return false;\n  }\n  const { page, pagePath } = __fetch.__nextGetStaticStore().getStore() || {};\n  return Boolean(\n    // available on next@14\n    pagePath || // available on next@15\n    page\n  );\n};\nconst isPagesRouterInternalNavigation = (req) => !!req.headers.get(nextConstants.Headers.NextjsData);\nexport {\n  createProtect\n};\n//# sourceMappingURL=protect.js.map", "import \"../../chunk-BUSYA2B4.js\";\nimport { constants, createClerkRequest, createRedirect } from \"@clerk/backend/internal\";\nimport { notFound, redirect } from \"next/navigation\";\nimport { PUBLISHABLE_KEY, SIGN_IN_URL, SIGN_UP_URL } from \"../../server/constants\";\nimport { createAsyncGetAuth } from \"../../server/createGetAuth\";\nimport { authAuthHeaderMissing } from \"../../server/errors\";\nimport { getAuthKeyFromRequest, getHeader } from \"../../server/headers-utils\";\nimport { createProtect } from \"../../server/protect\";\nimport { decryptClerkRequestData } from \"../../server/utils\";\nimport { isNextWithUnstableServerActions } from \"../../utils/sdk-versions\";\nimport { buildRequestLike } from \"./utils\";\nconst auth = async () => {\n  require(\"server-only\");\n  const request = await buildRequestLike();\n  const stepsBasedOnSrcDirectory = async () => {\n    if (isNextWithUnstableServerActions) {\n      return [];\n    }\n    try {\n      const isSrcAppDir = await import(\"../../server/fs/middleware-location.js\").then((m) => m.hasSrcAppDir());\n      return [`Your Middleware exists at ./${isSrcAppDir ? \"src/\" : \"\"}middleware.(ts|js)`];\n    } catch {\n      return [];\n    }\n  };\n  const authObject = await createAsyncGetAuth({\n    debugLoggerName: \"auth()\",\n    noAuthStatusMessage: authAuthHeaderMissing(\"auth\", await stepsBasedOnSrcDirectory())\n  })(request);\n  const clerkUrl = getAuthKeyFromRequest(request, \"ClerkUrl\");\n  const redirectToSignIn = (opts = {}) => {\n    const clerkRequest = createClerkRequest(request);\n    const devBrowserToken = clerkRequest.clerkUrl.searchParams.get(constants.QueryParameters.DevBrowser) || clerkRequest.cookies.get(constants.Cookies.DevBrowser);\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n    return createRedirect({\n      redirectAdapter: redirect,\n      devBrowserToken,\n      baseUrl: clerkRequest.clerkUrl.toString(),\n      publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n      signInUrl: decryptedRequestData.signInUrl || SIGN_IN_URL,\n      signUpUrl: decryptedRequestData.signUpUrl || SIGN_UP_URL\n    }).redirectToSignIn({\n      returnBackUrl: opts.returnBackUrl === null ? \"\" : opts.returnBackUrl || (clerkUrl == null ? void 0 : clerkUrl.toString())\n    });\n  };\n  return Object.assign(authObject, { redirectToSignIn });\n};\nauth.protect = async (...args) => {\n  require(\"server-only\");\n  const request = await buildRequestLike();\n  const authObject = await auth();\n  const protect = createProtect({\n    request,\n    authObject,\n    redirectToSignIn: authObject.redirectToSignIn,\n    notFound,\n    redirect\n  });\n  return protect(...args);\n};\nexport {\n  auth\n};\n//# sourceMappingURL=auth.js.map", "import \"../chunk-BUSYA2B4.js\";\nconst missingDomainAndProxy = `\nMissing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.\n\n1) With middleware\n   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'\n   `;\nconst missingSignInUrlInDev = `\nInvalid signInUrl. A satellite application requires a signInUrl for development instances.\nCheck if signInUrl is missing from your configuration or if it is not an absolute URL\n\n1) With middleware\n   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`;\nconst getAuthAuthHeaderMissing = () => authAuthHeaderMissing(\"getAuth\");\nconst authAuthHeaderMissing = (helperName = \"auth\", prefixSteps) => `Clerk: ${helperName}() was called but Clerk can't detect usage of clerkMiddleware(). Please ensure the following:\n- ${prefixSteps ? [...prefixSteps, \"\"].join(\"\\n- \") : \" \"}clerkMiddleware() is used in your Next.js Middleware.\n- Your Middleware matcher is configured to match this route or page.\n- If you are using the src directory, make sure the Middleware file is inside of it.\n\nFor more details, see https://clerk.com/docs/quickstarts/nextjs\n`;\nconst authSignatureInvalid = `Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)`;\nconst encryptionKeyInvalid = `Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`;\nconst encryptionKeyInvalidDev = `Clerk: Unable to decrypt request data.\n\nRefresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.\n\nFor more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`;\nexport {\n  authAuthHeaderMissing,\n  authSignatureInvalid,\n  encryptionKeyInvalid,\n  encryptionKeyInvalidDev,\n  getAuthAuthHeaderMissing,\n  missingDomainAndProxy,\n  missingSignInUrlInDev\n};\n//# sourceMappingURL=errors.js.map"], "names": [], "sourceRoot": ""}