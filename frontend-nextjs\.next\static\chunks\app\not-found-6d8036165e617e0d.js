try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f870779b-aef9-42f8-9124-1ea552af25e3",e._sentryDebugIdIdentifier="sentry-dbid-f870779b-aef9-42f8-9124-1ea552af25e3")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4345],{39552:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>o});var n=r(99004);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function i(...e){return n.useCallback(o(...e),e)}},41894:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(52880),a=r(95181),o=r(62054);function i(){let e=(0,a.useRouter)();return(0,n.jsxs)("div",{className:"absolute top-1/2 left-1/2 mb-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center text-center","data-sentry-component":"NotFound","data-sentry-source-file":"not-found.tsx",children:[(0,n.jsx)("span",{className:"from-foreground bg-linear-to-b to-transparent bg-clip-text text-[10rem] leading-none font-extrabold text-transparent",children:"404"}),(0,n.jsx)("h2",{className:"font-heading my-2 text-2xl font-bold",children:"Something's missing"}),(0,n.jsx)("p",{children:"Sorry, the page you are looking for doesn't exist or has been moved."}),(0,n.jsxs)("div",{className:"mt-8 flex justify-center gap-2",children:[(0,n.jsx)(o.$,{onClick:()=>e.back(),variant:"default",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"not-found.tsx",children:"Go back"}),(0,n.jsx)(o.$,{onClick:()=>e.push("/dashboard"),variant:"ghost",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"not-found.tsx",children:"Back to Home"})]})]})}},50516:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,xV:()=>l});var n=r(99004),a=r(39552),o=r(52880),i=n.forwardRef((e,t)=>{let{children:r,...a}=e,i=n.Children.toArray(r),l=i.find(u);if(l){let e=l.props.children,r=i.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(s,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,o.jsx)(s,{...a,ref:t,children:r})});i.displayName="Slot";var s=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let a=e[n],o=t[n];/^on[A-Z]/.test(n)?a&&o?r[n]=(...e)=>{o(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...o}:"className"===n&&(r[n]=[a,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,a.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});s.displayName="SlotClone";var l=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function u(e){return n.isValidElement(e)&&e.type===l}},54651:(e,t,r)=>{"use strict";r.d(t,{Jv:()=>u,cn:()=>o,fw:()=>l,r6:()=>s,z3:()=>i});var n=r(97921),a=r(56309);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function i(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:o="normal"}=n;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(a)," ").concat("accurate"===o?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][i])?r:"Bytes")}function s(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function l(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let r=Math.floor(t/60);if(r<60)return"".concat(r,"分钟前");let n=Math.floor(r/60);if(n<24)return"".concat(n,"小时前");let a=Math.floor(n/24);if(a<7)return"".concat(a,"天前");let o=Math.floor(a/7);if(o<4)return"".concat(o,"周前");let i=Math.floor(a/30);if(i<12)return"".concat(i,"个月前");let s=Math.floor(a/365);return"".concat(s,"年前")}function u(e){return("string"==typeof e?new Date(e):e)<new Date}},55038:(e,t,r)=>{Promise.resolve().then(r.bind(r,41894))},62054:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>s});var n=r(52880);r(99004);var a=r(50516),o=r(85017),i=r(54651);let s=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:o,asChild:l=!1,...u}=e,d=l?a.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,i.cn)(s({variant:r,size:o,className:t})),...u,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},85017:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(97921);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:s}=t,l=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let o=a(t)||a(n);return i[e][o]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},95181:(e,t,r)=>{"use strict";var n=r(4377);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}}),r.o(n,"useSelectedLayoutSegments")&&r.d(t,{useSelectedLayoutSegments:function(){return n.useSelectedLayoutSegments}})}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,9442,4579,9253,7358],()=>t(55038)),_N_E=e.O()}]);