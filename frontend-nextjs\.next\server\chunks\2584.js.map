{"version": 3, "file": "2584.js", "mappings": "2hBAMgBA,qCAAAA,aAL8B,WACF,OAIrC,SAASA,EACdC,CAAmB,CACnBC,CAAwB,CACxBC,CAAgB,CAChBC,CAAgC,CAChCC,CAAkC,EAGlC,GAAM,CAAEC,KAAMC,CAAS,UAAEC,CAAQ,MAAEC,CAAI,cAAEC,CAAY,CAAE,CAAGN,EAG1D,GAAiB,MAAM,CAAnBI,EACF,OAAO,EAGT,GAAIE,EAAc,CAChB,IAAMC,EAAMH,CAAQ,CAAC,EAAE,CAEvBL,EAAMS,OAAO,CADGJ,CAAQ,CAAC,EAAE,CAE3BL,EAAMQ,GAAG,CAAGA,EAMZR,EAAMU,WAAW,CAAG,KACpBC,CAAAA,EAAAA,EAAAA,6BAAAA,EACEb,EACAE,EACAD,EACAK,EACAC,EACAC,EACAJ,EAEJ,MAEEF,CAFK,CAECQ,GAAG,CAAGT,EAAcS,GAAG,CAI7BR,EAAMU,WAAW,CAAGX,EAAcW,WAAW,CAC7CV,EAAMY,cAAc,CAAG,IAAIC,IAAId,EAAca,cAAc,EAC3DZ,EAAMS,OAAO,CAAGV,EAAcU,OAAO,CAErCK,GAAAA,EAAAA,2BAAAA,EACEhB,EACAE,EACAD,EACAE,EACAC,GAIJ,OAAO,CACT,kUCpDgBa,qCAAAA,aATkB,WAMe,MAG1C,SAASA,EACdC,CAA2B,CAC3BC,CAAqB,MAmCVC,EAjCX,GAAM,KAAEC,CAAG,MAAEhB,CAAI,CAAE,CAAGc,EAChBG,EAAOC,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBF,GAOzBG,EAAgBnB,GAAQa,EAAMb,IAAI,CAElCoB,EAAWP,EAAMhB,KAAK,CAS5B,MAAO,CAELwB,aAAcJ,EACdK,QAAS,CACPC,aAAa,EACbC,cAAe,GAEfC,2BAA4B,EAC9B,EACAC,kBAAmBb,EAAMa,iBAAiB,CAC1C7B,MAZEuB,CAYKO,CACPC,cAAef,EAAMe,aAAa,CAElC5B,KAAMmB,EACNU,QAASd,OAAAA,EAAAA,CAAAA,EAAAA,EAAAA,gCAAAA,EAAiCI,EAAAA,CAAAA,CAAjCJ,EAAmDC,EAAIc,QAClE,CACF,GAzCqD,4UCErCC,qCAAAA,SAAAA,EACdC,CAAiC,CACjCC,CAAoC,EAEpC,GAAM,CAACC,EAASzB,EAAe,CAAGwB,EAE5B,CAACE,EAAgBC,EAAiB,CAAGJ,QAM3C,CAAKK,EAAAA,CAAD,CAACA,YAAAA,EAAaF,EAAgBD,GAWlC,EAFoBF,EAAkBM,GATM,GASA,GAAI,GAMzCP,CAJU,CAKfQ,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyBP,GACzBvB,CAAc,CAAC2B,EAAiB,IAf5BI,MAAMC,OAAO,CAACN,EAiBtB,aAnCyC,EAkBF,SAjBV,qXCkHbO,kBAAkB,mBAAlBA,GA9EA3B,gCAAgC,mBAAhCA,GA+FA4B,iBAAiB,mBAAjBA,SAAAA,EACdC,CAA8B,CAC9BC,CAAmB,EAInB,IAAK,IAAMC,KAJXD,KAAAA,IAAAA,IAAAA,EAAiB,EAAC,EAIUE,OAAOC,MAAM,CAFlBJ,CAAW,CAAC,EAAE,GAEsB,CACzD,IAAMV,EAAUY,CAAa,CAAC,EAAE,CAC1BG,EAAqBT,MAAMC,OAAO,CAACP,GACnCgB,EAAeD,EAAqBf,CAAO,CAAC,EAAE,CAAGA,CACnD,EAACgB,GAAgBA,EAAaC,UAAU,CAACC,EAAAA,gBAAgB,GAAG,CAI9DH,GAAuBf,CAAe,OAAR,CAAC,EAAE,EAA2B,MAArCA,CAAsBA,CAAO,CAAC,EAAE,EAAQ,CAGzD,CAACA,CAAO,CAAC,EAAE,CAAC,CAAGA,CAAO,CAAC,EAAE,CAACmB,KAAK,CAAC,KAC7BJ,GACTJ,EAAM,CAACX,CAAO,CAAC,EAAE,CAAC,CAAGA,CAAO,CAAC,IAG/BW,EAASF,EAAkBG,EAAeD,GAC5C,CAEA,OAAOA,CACT,aA/J2C,WAMpC,WACsB,OAEvBS,EAAqB,GACH,MAAfpB,CAAO,CAAC,EAAE,CAAWA,EAAQqB,KAAK,CAAC,GAAKrB,EAG3CsB,EAAoB,GACxB,UAAiC,OAAtBtB,EAGT,YAA4B,CAAxBA,EAA+B,GAE5BA,EAGFA,CAAO,CAAC,EAAE,CAGnB,SAASuB,EAAkBC,CAAkB,EAC3C,OACEA,EAASC,MAAM,CAAC,CAACC,EAAK1B,IAEpB,MADAA,EAAUoB,EAAmBpB,EAAAA,GACP2B,CAAAA,EAAAA,EAAAA,cAAAA,EAAe3B,GAC5B0B,EAGCA,EAAI,GAJiC,CAI9B1B,EAChB,KAAO,GAEd,CAEO,SAASnB,EACdkB,CAAoC,MAebA,EAbvB,IAAMC,EAAUM,MAAMC,OAAO,CAACR,CAAiB,CAAC,EAAE,EAC9CA,CAAiB,CAAC,EAAE,CAAC,EAAE,CACvBA,CAAiB,CAAC,EAAE,CAExB,GACEC,IAAY4B,EAAAA,mBAAmB,EAC/BC,EAAAA,0BAA0B,CAACC,IAAI,CAAC,GAAO9B,EAAQiB,UAAU,CAACc,IAE1D,OAAOC,GAELhC,EAAQiB,UAAU,CAACC,EAAAA,gBAAgB,EAAG,MAAO,GAEjD,IAAMM,EAAW,CAACF,EAAkBtB,GAAS,CACvCzB,EAAqC,OAApBwB,EAAAA,CAAiB,CAAC,IAAlBA,EAAwB,CAAC,EAE1CkC,EAAe1D,EAAe2D,QAAQ,CACxCrD,EAAiCN,EAAe2D,QAAQ,OACxDF,EAEJ,QAAqBA,IAAjBC,EACFT,EAASW,GADqB,CACjB,CAACF,QAEd,IAAK,GAAM,CAACG,EAAKC,EAAM,GAAIxB,OAAOyB,OAAO,CAAC/D,GAAiB,CACzD,GAAI6D,eAAoB,SAExB,IAAMG,EAAY1D,EAAiCwD,QAEjCL,IAAdO,GACFf,EAASW,EADkB,EACd,CAACI,EAElB,CAGF,OAAOhB,EAAkBC,EAC3B,CAyCO,SAAShB,EACdgC,CAAwB,CACxBC,CAAwB,EAExB,IAAMC,EAAcC,SA3CbA,EACPH,CAAwB,CACxBC,CAAwB,EAExB,GAAM,CAACG,EAAUC,EAAgB,CAAGL,EAC9B,CAACM,EAAUC,EAAgB,CAAGN,EAE9BO,EAAqB1B,EAAkBsB,GACvCK,EAAqB3B,EAAkBwB,GAE7C,GACEjB,EAAAA,0BAA0B,CAACC,IAAI,CAC7B,GACEkB,EAAmB/B,UAAU,CAACc,IAAMkB,EAAmBhC,UAAU,CAACc,IAGtE,CADA,KACO,GAGT,GAAI,CAAC5B,CAAAA,EAAAA,EAAAA,YAAAA,EAAayC,EAAUE,GAAW,KAE9BjE,EAAP,OAAOA,OAAAA,EAAAA,EAAiC4D,EAAAA,CAAAA,CAAjC5D,EAA2C,EACpD,CAEA,IAAK,IAAMqE,KAAqBL,EAC9B,GAAIE,CAAe,CAACG,EAAkB,CAAE,CACtC,IAAMR,CAFuC,CAEzBC,EAClBE,CAAe,CAACK,EAAkB,CAClCH,CAAe,CAACG,EAAkB,EAEpC,GAAoB,MAAM,CAAtBR,EACF,OAAUpB,EAAkBwB,GAAU,IAAGJ,CAE7C,CAGF,OAAO,IACT,EAM6CF,EAAOC,UAElD,MAAIC,GAAuC,KAAK,CAArBA,EAClBA,EAIFnB,EAAkBmB,EAAYvB,KAAK,CAAC,KAC7C,gUC9HagC,qCAAAA,mCACX,yBACA,uBACA,gBAmDA,qBAtDK,OAAMA,EAcXC,QAAWC,CAA2B,CAAc,CAIlD,IAHIC,EACAC,EAEEC,EAAc,IAAIC,QAAQ,CAACC,EAASC,KACxCL,EAAcI,EACdH,EAAaI,CACf,GAEMC,EAAO,UACX,GAAI,CACF,QAAI,CAAC,QACL,IAAMC,EAAS,MAAMR,IACrBC,EAAYO,EACd,CAAE,MAAOC,EAAO,CACdP,EAAWO,EACb,QAAU,CACR,QAAI,CAAC,QACL,QAAI,CAAC,OACP,CACF,EAOA,OAHA,QAAI,CAAC,MAAO3B,IAAI,CAAC4B,CAFOV,UAAWG,OAAaI,CAAK,GAGrD,QAAI,CAAC,QAEEJ,CACT,CAEAQ,KAAKX,CAAuB,CAAE,CAC5B,IAAMY,EAAQ,QAAI,CAAC,MAAOC,SAAS,CAAC,GAAUC,EAAKd,SAAS,GAAKA,GAEjE,GAAIY,EAAQ,CAAC,EAAG,CACd,IAAMG,EAAa,QAAI,CAAC,MAAOC,MAAM,CAACJ,EAAO,EAAE,CAAC,EAAE,CAClD,QAAI,CAAC,MAAOK,OAAO,CAACF,GACpB,QAAI,CAAC,OAAa,EACpB,CACF,CA5CAG,YAAYC,EAAiB,CAAC,CAAE,CA8ChC,wCArDA,yDACA,yDACA,yDAME,QAAI,CAAC,MAAkBA,EACvB,QAAI,CAAC,MAAgB,EACrB,QAAI,CAAC,MAAS,EAChB,CAkDF,CARE,WAAaC,CAAc,EACzB,GADWA,KAAAA,IAAAA,GAAAA,IAAS,GAEjB,SAAI,CAAC,MAAgB,QAAI,CAAC,OAAmBA,CAAAA,CAAAA,CAAK,CACnD,QAAI,CAAC,MAAOrE,MAAM,CAAG,EACrB,KACA,CAAiB,OAAjB,WAAI,CAAC,MAAOsE,KAAK,KAAjB,EAAqBd,IAAI,EAC3B,CACF,+WCsIce,wBAAwB,mBAAxBA,GA0DAC,sBAAsB,mBAAtBA,GA+BAC,sBAAsB,mBAAtBA,GAnDAC,wBAAwB,mBAAxBA,GAuEHC,uBAAuB,mBAAvBA,aA1SN,WACiB,WACQ,WACL,QAC0B,eACb,WACZ,WACqB,WACjB,WAM+B,OA4B/D,SAASC,EACPC,CAAiC,CACjCC,CAA8B,EAEF,MAAM,CAA9BD,EAAYE,OAAO,GACrBF,EAAYE,OAAO,CAAGF,EAAYE,OAAO,CAACC,IAAI,CAClB,MAAM,CAA9BH,EAAYE,OAAO,CAErBE,EAAU,aACRJ,EACArG,OAAQqG,EAAYE,OAAO,UAC3BD,CACF,GAGID,EAAYK,YAAY,EAAE,CAC5BL,EAAYK,YAAY,EAAG,EAC3BL,EAAYM,QAAQ,CAClB,CACEC,KAAMC,EAAAA,cAAc,CACpBC,OAAQC,OAAOC,QAAQ,CAACF,MAAM,EAEhCR,IAKV,CAEA,eAAeG,EAAU,CAQxB,EARwB,gBACvBJ,CAAW,CACXrG,QAAM,UACNsG,CAAQ,CAKT,CARwB,EASjBW,EAAYZ,EAAYtG,KAAK,CAEnCsG,EAAYE,OAAO,CAAGvG,EAEtB,IAAMkH,EAAUlH,EAAOkH,OAAO,CACxBC,EAAed,EAAYrG,MAAM,CAACiH,EAAWC,GAEnD,SAASE,EAAaC,CAAyB,EAEzCrH,EAAOsH,SAAS,EAAE,CAItBjB,EAAYtG,KAAK,CAAGsH,EAEpBjB,EAAoBC,EAAaC,GACjCtG,EAAO8E,OAAO,CAACuC,GACjB,CAGIE,GAAAA,EAAAA,UAAAA,EAAWJ,GACbA,EAAaK,IAAI,CAACJ,EAAc,GADJ,CAE1BhB,EAAoBC,EAAaC,GACjCtG,EAAO+E,MAAM,CAAC0C,EAChB,GAEAL,EAAaD,EAEjB,CAiFO,SAASpB,EACd2B,CAA4B,CAC5BC,CAAuD,EAEvD,IAAMtB,EAAoC,CACxCtG,MAAO2H,EACPf,SAAU,CAACO,EAAyBZ,IAClCsB,CAtFN,SAASA,CAC0B,CACjCV,CAAuB,CACvBZ,CAA8B,EAE9B,IAAIuB,EAGA,CAAE/C,QAASwB,EAAUvB,OAAQ,KAAO,CAAE,EAM1C,GAAImC,EAAQN,IAAI,GAAKkB,EAAAA,cAAc,CAAE,CAEnC,IAAMC,EAAkB,IAAIlD,QAAwB,CAACC,EAASC,KAC5D8C,EAAY,CAAE/C,iBAASC,CAAO,CAChC,GAEAiD,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,KAGd1B,EAASyB,EACX,EACF,CAEA,IAAME,EAA6B,SACjCf,EACAV,KAAM,KACN1B,QAAS+C,EAAU/C,OAAO,CAC1BC,OAAQ8C,EAAU9C,MAAM,CAIE,MAAM,EAA9BsB,EAAYE,OAAO,EAGrBF,EAAY6B,IAAI,CAAGD,EAEnBxB,EAAU,aACRJ,EACArG,OAAQiI,WACR3B,CACF,IAEAY,EAAQN,IAAI,GAAKuB,EAAAA,eAAe,EAChCjB,EAAQN,IAAI,GAAKkB,EAAAA,cAAc,EAC/B,EAGYvB,OAAO,CAACe,SAAS,EAAG,EAIhCW,EAAUzB,IAAI,CAAGH,EAAYE,OAAO,CAACC,IAAI,CAGrCH,EAAYE,OAAO,CAACW,OAAO,CAACN,IAAI,GAAKwB,EAAAA,oBAAoB,EAAE,CAC7D/B,EAAYK,YAAY,EAAG,GAG7BD,EAAU,aACRJ,EACArG,OAAQiI,WACR3B,CACF,KAIyB,MAAM,CAA3BD,EAAY6B,IAAI,GAClB7B,EAAY6B,IAAI,CAAC1B,IAAI,CAAGyB,CAAAA,EAE1B5B,EAAY6B,IAAI,CAAGD,EAEvB,GAWqB5B,EAAaa,EAASZ,GACvCtG,OAAQ,MAAOD,EAAuBC,IACrBqI,CAAAA,EAAAA,EAAAA,OAAAA,EAAQtI,EAAOC,GAGhCuG,QAAS,KACT2B,KAAM,KACNI,wBAC2B,OAAzBX,GACwD,mBAAjDA,EAAqBW,uBAAuB,CAE/CX,EAAqBW,uBAAuB,CAC5C,IACR,EAeA,OAAOjC,CACT,CAEO,SAASH,IACd,OAAOqC,IACT,CAWA,SAASC,QAZsB,GAgBtB,IACT,CAEO,SAASxC,EACd7F,CAAY,CACZsI,CAA4C,CAC5CC,CAAqB,CACrBC,CAAoC,EAIpC,EA3B2D,EA2BrDzI,EAAM,IAAI0I,IAAIC,CAAAA,EAAAA,EAAAA,WAAAA,EAAY1I,GAAO6G,SAAS7G,IAAI,EAKpD2I,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4BH,GAO5BI,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtBnC,KAAMuB,EAAAA,eAAe,CACrBjI,MACA8I,cAAeC,CAAAA,EAAAA,EAAAA,aAAAA,EAAc/I,GAC7BgJ,eAAgBlC,SAASmC,MAAM,cAC/BT,eACAD,EACAW,eAAe,CACjB,EACF,CAEO,SAASnD,EACd9F,CAAY,CACZjB,CAAmC,EAMnC6J,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtBnC,KAAMkB,EAAAA,cAAc,CACpB5H,IAAK,IAAI0I,IAAIzI,QACbjB,CACF,EACF,CAOO,IAAMiH,EAA6C,CACxDkD,KAAM,IAAMtC,OAAOuC,OAAO,CAACD,IAAI,GAC/BE,QAAS,IAAMxC,OAAOuC,OAAO,CAACC,OAAO,GACrCC,SAaI,CAbMC,EAaSC,IAb8B,CAe3C,EAbF,EAaQrD,EArFd,SAASsD,EAEL,MAAM,qBAEL,CAFK,MACJ,2EADI,+DAEN,EAGJ,IA+EczJ,EAAM0J,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBzJ,GAC9B,GAAY,OAARD,EAAc,KAURwJ,EAHRG,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBxD,EAAYtG,KAAK,CAAE,CACjC6G,KAAMkD,EAAAA,eAAe,KACrB5J,EACA6J,KAAML,OAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASK,IAAAA,EAATL,EAAiBM,EAAAA,YAAY,CAACC,IAAI,EAE5C,CACF,EACJC,QAAS,CAAC/J,EAAcuJ,KACtB1B,GAAAA,EAAAA,eAAAA,EAAgB,SAC0B0B,EAAxC1D,EAAuB7F,EAAM,UAAWuJ,OAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASS,MAAAA,GAATT,EAAyB,KACnE,EACF,EACAnG,KAAM,CAACpD,CAHwD,CAG1CuJ,KACnB1B,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,SACuB0B,EAArC1D,EAAuB7F,EAAM,OAAQuJ,OAAAA,EAAAA,QAAAA,KAAAA,EAAAA,EAASS,MAAM,GAAfT,EAAyB,KAChE,EACF,EACAU,OAH4D,CAGnD,KACPpC,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,KACde,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtBnC,KAAMC,EAAAA,cAAc,CACpBC,OAAQC,OAAOC,QAAQ,CAACF,MAAM,EAElC,EACF,EACAuD,WAAY,KAER,MAAM,qBAEL,CAFK,MACJ,gFADI,+DAEN,EASJ,CACF,mUC7WgBC,qCAAAA,aAXmB,MAOnC,SAASC,EAAkB9G,CAAQ,EACjC,OAAO,KAAiB,IAAVA,CAChB,CAEO,SAAS6G,EACdvK,CAA2B,CAC3ByK,CAAgB,MAGKA,EAwDbA,EAxDR,IAAM9B,EAAmC,OAApB8B,EAAAA,EAAQ9B,YAAAA,GAAR8B,EAEjBzJ,EAAUhB,EAAMgB,OAAO,CAE3B,GAAIwJ,EAAeC,EAAQC,GAJkB,QAIP,EAAG,CAEvC,IAAM3G,EAAclC,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmB7B,EAAMb,IAAI,CAAEsL,EAAQC,WAAW,EAClE3G,EAEF/C,EAAU+C,EACD,IAET/C,EAAUhB,CALK,CAKCQ,CAFG,WAEHA,CAGpB,CAEA,MAAO,CAELA,aAAcgK,EAAeC,EAAQjK,YAAY,EAC7CiK,EAAQjK,YAAY,GAAKR,EAAMQ,YAAY,CACzCR,EAAMQ,YAAY,CAClBiK,EAAQjK,YAAY,CACtBR,EAAMQ,YAAY,CACtBC,QAAS,CACPC,YAAa8J,EAAeC,EAAQ/J,WAAW,EAC3C+J,EAAQ/J,WAAW,CACnBV,EAAMS,OAAO,CAACC,WAAW,CAC7BC,cAAe6J,EAAeC,EAAQ9J,aAAa,EAC/C8J,EAAQ9J,aAAa,CACrBX,EAAMS,OAAO,CAACE,aAAa,CAC/BC,2BAA4B4J,EAC1BC,EAAQ7J,0BAA0B,EAEhC6J,EAAQ7J,0BAA0B,CAClCZ,EAAMS,OAAO,CAACG,0BAA0B,EAG9CC,kBAAmB,CACjB8J,QAAOhC,IACH6B,IAAeC,QAAAA,KAAAA,EAAAA,EAASG,kBAAkB,GAExC5K,CADA,CACMa,iBAAiB,CAAC8J,KAAAA,EAG9BE,CADI,cACYJ,EAAQI,cAAc,GAAI,EAC1CC,aAAcnC,EAEV,EACQmC,YAAY,EAA6B,KAAzBL,EAAQK,IADI,QACQ,CAE1CC,mBAAmBN,EAAQK,YAAY,CAACpI,KAAK,CAAC,IAC9C1C,EAAMa,iBAAiB,CAACiK,YAAY,CAEtC,KACJE,aAAcrC,EACV8B,MAAAA,GAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASG,kBAAAA,EAATH,EAA+BzK,EAAMa,iBAAiB,CAACmK,YAAY,CAEnE,EAAE,EAGRhM,MAAOyL,EAAQzL,KAAK,CAAGyL,EAAQzL,KAAK,CAAGgB,EAAMhB,KAAK,CAClD+B,cAAe0J,EAAQ1J,aAAa,CAChC0J,EAAQ1J,aAAa,CACrBf,EAAMe,aAAa,CAEvB5B,KAAMqL,EAAeC,EAAQC,WAAW,EACpCD,EAAQC,WAAW,CACnB1K,EAAMb,IAAI,SACd6B,CACF,CACF,wQCvFe,GACb,OAAS,EACP,KAAO,8BACP,KAAO,IACP,MAAQ,IACR,OAAS,aACT,IAAM,QACN,MAAQ,gBACR,WAAa,GACb,aAAe,SACf,cAAgB,QAClB,EACA,MAAQ,EACN,KAAO,8BACP,KAAO,IACP,MAAQ,IACR,OAAS,aACT,IAAM,gBACN,MAAQ,QAEZ,EChBA,CAAM,KAAuB,CAC3B,CACA,GACA,EACA,KADA,GAEG,CACG,CAAY,GAFlB,CAEkB,cAChB,CACE,CAAE,QAAQ,eAAgB,OAAO,EAAI,CAAJ,OAAI,EAAS,CAAG,IAAH,GAAG,YAAO,CAAW,YAAU,CAAG,KAChF,CAEA,uBACE,MACA,KACE,EACA,CAAG,IAAkB,EAAI,CACzB,CADyB,IAClB,GACP,MAAQ,GACR,UAAW,CAAC,CAAe,4BAAe,EAAY,GAAS,CAAE,EAAf,GAAe,CAAK,GAAG,EACzE,CAAI,UACA,GADA,EACA,CACE,EAFO,EAED,GAER,EACE,WAAa,GACb,MAAQ,EACV,EACJ,CAAG,KAEL,CACE,GAAS,mBAAc,SAAS,CAAE,GAAK,cAAe,KAAK,CACxD,CAAS,KAAI,CAAC,CAAC,CAAK,EAAK,CAAM,uBAAc,EAAK,KAAK,CAAC,CACvD,CAAM,aAAQ,CAAQ,EAAI,EAAW,CAAC,CAAQ,EAApB,GAAW,OAKvC,cAAc,EAAG,GAAc,EAElC,CACT,SAH2C,iHCoC3BiK,qCAAAA,SAAAA,EACd9J,CAAoC,CACpCC,CAAoC,CACpChC,CAA4B,CAC5B8L,CAAY,EAEZ,IAqBIC,EArBE,CAAC9J,EAASzB,EAAgBO,EAAKiL,EAASC,EAAa,CACzDjK,EAGF,GAAID,MAAkBM,MAAM,CAAQ,CAClC,IAAMtC,EAA0BmM,EAAWlK,EAAmBhC,GAI9D,MAFAmM,CAAAA,EAAAA,EAAAA,wCAAAA,EAAyCpM,EAAM+L,GAExC/L,CACT,CAEA,GAAM,CAACmC,EAAgBC,EAAiB,CAAGJ,EAG3C,GAAI,CAACK,CAAAA,EAAAA,EAAAA,YAAAA,EAAaF,EAAgBD,GAChC,OAD0C,KAO5C,GAHiD,CAG7CmK,GAHgBrK,EAAkBM,MAAM,CAI1C0J,CADe,CACMG,EAAW1L,CAAc,CAAC2B,EAAiB,CAAEnC,QASlE,GAAI+L,MAA6B,EAPjCA,EAAqBF,EACnBvJ,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyBP,GACzBvB,CAAc,CAAC2B,EAAiB,CAChCnC,EACA8L,EAAAA,EAIA,OAAO,KAIX,IAAM/L,EAA0B,CAC9BgC,CAAiB,CAAC,EAAE,CACpB,CACE,GAAGvB,CAAc,CACjB,CAAC2B,EAAiB,CAAE4J,CACtB,EACAhL,EACAiL,EACD,CASD,OANIC,IACFlM,CAAI,CAAC,EAAE,EAAG,GAGZoM,CAJkB,EAIlBA,EAAAA,wCAAAA,EAAyCpM,EAAM+L,GAExC/L,CACT,aAtIoC,WACK,WACZ,WAC4B,OAKzD,SAASmM,EACPG,CAA8B,CAC9BC,CAA4B,EAE5B,GAAM,CAACC,EAAgBC,EAAsB,CAAGH,EAC1C,CAACI,EAAcC,EAAoB,CAAGJ,EAI5C,GACEG,IAAiB5I,EAAAA,mBAAmB,EACpC0I,IAAmB1I,EAAAA,mBAAmB,CAEtC,CADA,MACOwI,EAGT,GAAIjK,CAAAA,EAAAA,EAAAA,YAAAA,EAAamK,EAAgBE,GAAe,CAC9C,IAAME,EAA0C,CAAC,EACjD,IAAK,IAAMtI,KAAOmI,EAEd,KAAoC,IAA7BE,CAAmB,CAACrI,EAAI,CAE/BsI,CAAiB,CAACtI,EAAI,CAAG6H,CAJY,CAKnCM,CAAqB,CAACnI,EAAI,CAC1BqI,CAAmB,CAACrI,EAAI,EAG1BsI,CAAiB,CAACtI,EAAI,CAAGmI,CAAqB,CAACnI,EAAI,CAIvD,IAAK,IAAMA,KAAOqI,EACZC,CAAiB,CAACtI,EAAI,EAAE,CAI5BsI,CAAiB,CAACtI,EAAI,CAAGqI,CAAmB,CAACrI,EAAAA,EALR,IAQjCtE,EAA0B,CAACwM,EAAgBI,EAAkB,CAenE,OAZIN,CAAW,CAAC,EAAE,EAAE,CAClBtM,CAAI,CAAC,EAAE,CAAGsM,CAAW,CAAC,IAGpBA,CAAW,CAAC,EAAE,EAAE,CAClBtM,CAAI,CAAC,EAAE,CAAGsM,CAAW,CAAC,IAGpBA,CAAW,CAAC,EAAE,EAAE,CAClBtM,CAAI,CAAC,EAAE,CAAGsM,CAAW,CAAC,IAGjBtM,CACT,CAEA,OAAOuM,CACT,gXCFaM,gBAAgB,mBAAhBA,GAHAC,mBAAmB,mBAAnBA,GAwHGC,iBAAiB,mBAAjBA,GA1CAC,iBAAiB,mBAAjBA,GA8FAC,uBAAuB,mBAAvBA,GAsBAC,kBAAkB,mBAAlBA,GAoFAC,gBAAgB,mBAAhBA,GA7QAvD,2BAA2B,mBAA3BA,GASAwD,+BAA+B,mBAA/BA,GAiIAC,2BAA2B,mBAA3BA,OAlNyB,yBAEZ,WACU,WASP,OAgD5BC,EAAmD,KAG1CR,EAAsB,CAAEzF,SAAS,CAAK,EAGtCwF,EAAmB,CAAExF,SAAS,CAAM,EAM1C,SAASuC,EAA4B2D,CAAyB,EACnEzE,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,KACdwE,MAAAA,CAAAA,EAAAA,EAA6BE,CAA7BF,sBAAoD,CAACT,GACrDU,MAAAA,CAAAA,EAAAA,EAAMC,CAAND,sBAA6B,CAACT,GAC9BQ,EAA8BC,CAChC,EACF,CAGO,SAASH,EAAgCG,CAAkB,EAC5DD,IAAgCC,IAClCD,EADwC,IACV,CAElC,CAIA,IAAMG,EAGe,YAAnB,OAAOC,QAAyB,IAAIA,QAAY,IAAIhN,IAMhDiN,EAAoD,IAAIC,IAGxDC,EACJ,mBAAOC,qBACH,IAAIA,qBAAqBC,SA0HtBA,CAAyD,EAChE,IAAK,IAAMC,KAASxJ,EAAS,CAI3B,IAAMyJ,EAAYD,EAAME,iBAAiB,CAAG,EAC5CjB,EAAwBe,EAAMG,MAAM,CAAuBF,EAC7D,CACF,EAlIgD,CACxCG,WAAY,OACd,GACA,KAEN,SAASC,EAAkBC,CAAgB,CAAEC,CAA8B,EAEhDrK,SADAuJ,EACW,GADK,CAACa,IAKxCjB,EAA4BiB,GAG9Bb,EAAae,GAAG,CAACF,EAASC,GACT,MAAM,CAAnBV,GACFA,EAASY,OAAO,CAACH,EAErB,CAEA,SAASI,EAAsBzN,CAAY,EACzC,GAAI,CACF,MAAOyJ,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBzJ,EAC3B,CAAE,QAAM,CAWN,MAHA0N,CADyB,YAAvB,OAAOC,YAA6BA,YAAcC,QAAQ7I,KAAAA,EAEzD,oBAAmB/E,EAAK,8CAEpB,IACT,CACF,CAEO,SAAS+L,EACdsB,CAAoB,CACpBrN,CAAY,CACZ6N,CAAyB,CACzBjE,CAA2C,CAC3CkE,CAAwB,CACxBvB,CAA+D,EAE/D,GAAIuB,EAAiB,CACnB,IAAMC,EAAcN,EAAsBzN,GAC1C,GAAoB,OAAhB+N,EAAsB,CACxB,IAAMT,EAAqC,QACzCO,EACAjE,OACAoD,WAAW,EACXgB,qBAAqB,EACrBC,aAAc,KACdC,aAAc,CAAC,EACfC,aAAcJ,EAAY/N,IAAI,yBAC9BuM,CACF,EAIA,OADAa,EAAkBC,EAASC,GACpBA,CACT,CACF,CAaA,MAV8C,CAUvCA,OATLO,OACAjE,EACAoD,WAAW,EACXgB,qBAAqB,EACrBC,aAAc,KACdC,aAAc,CAAC,EACfC,aAAc,6BACd5B,CACF,CAEF,CAEO,SAAST,EACduB,CAAwB,CACxBrN,CAAY,CACZ6N,CAAyB,CACzBjE,CAA2C,EAE3C,IAAMmE,EAAcN,EAAsBzN,EACtB,MAAM,EAAtB+N,GAiBJX,EAAkBC,EAVa,OAUJC,CATzBO,OACAjE,EACAoD,WAAW,EACXgB,oBAAqB,GACrBC,aAAc,KACdC,aAAc,CAAC,EACfC,aAAcJ,EAAY/N,IAAI,CAC9BuM,wBAAyB,IAC3B,EAEF,CAEO,SAASH,EAA4BiB,CAAgB,EAC1D,IAAMC,EAAWd,EAAa4B,GAAG,CAACf,GAClC,QAAiBpK,IAAbqK,EAAwB,CAC1Bd,EAAa6B,MAAM,CAAChB,GACpBX,EAAuB2B,MAAM,CAACf,GAC9B,IAAMW,EAAeX,EAASW,YAAY,MACf,EAAvBA,GACFK,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBL,EAEvB,CACiB,MAAM,CAAnBrB,GACFA,EAAS2B,SAAS,CAAClB,EAEvB,CAYO,SAASrB,EAAwBqB,CAAgB,CAAEL,CAAkB,EAQ1E,IAAMM,EAAWd,EAAa4B,GAAG,CAACf,QACjBpK,IAAbqK,IAIJA,EAASN,CAJmB,QAIV,CAAGA,EACjBA,EACFN,EAAuB8B,GAAG,CAAClB,GADd,EAGUe,MAAM,CAACf,GAEhCmB,EAAuBnB,GACzB,CAEO,SAASrB,EACdoB,CAAwC,CACxCqB,CAA0C,EAE1C,IAAMpB,EAAWd,EAAa4B,GAAG,CAACf,QACjBpK,IAAbqK,OAAwB,CAIXrK,IAAbqK,IACFA,EAASU,CADiB,kBACE,EAAG,EAQ/BS,EAAuBnB,GAE3B,CAEA,SAASmB,EAAuBnB,CAA8B,EAC5D,IAAMqB,EAAuBrB,EAASW,YAAY,CAElD,GAAI,CAACX,EAASN,SAAS,CAAE,CAGM,MAAM,CAA/B2B,GACFL,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBK,GAMrB,MACF,CA8CF,CAEO,SAASzC,EACdtL,CAAsB,CACtB7B,CAAuB,EASvB,IAAM6P,EAAsBC,CAAAA,EAAAA,EAAAA,sBAAAA,IAC5B,IAAK,IAAMvB,KAAYZ,EAAwB,CAC7C,IAAM7H,EAAOyI,EAASW,YAAY,CAClC,GACEpJ,UACAyI,EAASY,YAAY,GAAKU,GAC1B/J,EAAKxB,GAAG,CAACzC,OAAO,GAAKA,GACrBiE,EAAKiK,oBAAoB,GAAK/P,EAI9B,IAHA,IAOW,MAAM,EAAf8F,GACFyJ,GAAAA,EAAAA,kBAAAA,EAAmBzJ,GAErB,IAAMkK,EAAWC,CAAAA,EAAAA,EAAAA,cAAAA,EAAe1B,EAASa,YAAY,CAAEvN,GACjDqO,EAAW3B,EAASU,mBAAmB,CACzCkB,EAAAA,gBAAgB,CAACC,MAAM,CACvBD,EAAAA,gBAAgB,CAACE,OAAO,CAC5B9B,EAASW,YAAY,CAAGoB,CAAAA,EAAAA,EAAAA,oBAAAA,EACtBN,EACAhQ,EACAuO,EAAS1D,IAAI,GAAKC,EAAAA,YAAY,CAACC,IAAI,CACnCmF,GAEF3B,EAASY,YAAY,CAAGW,CAAAA,EAAAA,EAAAA,sBAAAA,GAC1B,CACF,0UCrXgBS,qCAAAA,aAVkB,OAU3B,SAASA,EACd1P,CAA2B,CAC3BC,CAAsB,CACtBb,CAA4B,EAY5B,MAAOuQ,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkB3P,EAAO,CAAC,EAAGA,EAAMQ,YAAY,CAAE,GAC1D,gXCwMgBoP,6BAA6B,mBAA7BA,GA1MAC,0BAA0B,mBAA1BA,aAjBT,WAE8B,WACO,WACV,WACG,WACqB,WAC5B,OAUvB,SAASA,EACd/Q,CAAmB,CACnBkB,CAA2B,CAC3Bf,CAA2C,CAC3CkB,CAAQ,CACRsK,CAAgB,EAEhB,IAGIqF,EAHA/N,EAAc/B,EAAMb,IAAI,CACxB4Q,EAAe/P,EAAMhB,KAAK,CACxBoB,EAAOC,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBF,GAG/B,GAA0B,UAAU,OAAzBlB,EACT,OAAO,EAGT,IAAK,IAAM+Q,KAAwB/Q,EAAY,CAE7C,GAAI,CAACgR,SAwFAA,EAA8B5Q,CAAkC,EACvE,GAAI,CAACA,EAAU,OAAO,EAEtB,IAAMO,EAAiBP,CAAQ,CAAC,EAAE,CAGlC,GAFgBA,CAAQ,CAAC,EAAE,CAGzB,KADW,EACJ,EAGT,IAAK,IAAMoE,KAAO7D,EAChB,GAAIqQ,EAA8BrQ,CAAc,CAAC6D,EAAI,EACnD,CADsD,CADxB,KAEvB,EAIX,OAAO,CACT,EAzGuCuM,EAAqB3Q,QAAQ,EAC9D,CADiE,QAInE,IAAID,EAAY4Q,EAAqB7Q,IAAI,CAIzCC,EAAYwQ,EACVxQ,EACA8C,OAAOgO,WAAW,CAAC/P,EAAIgQ,YAAY,GAGrC,GAAM,UAAE9Q,CAAQ,cAAEE,CAAY,eAAE6Q,CAAa,CAAE,CAAGJ,EAE5CK,EAAoC,CAAC,MAAOD,EAAc,CAKhEhR,EAAYwQ,EACVxQ,EACA8C,OAAOgO,WAAW,CAAC/P,EAAIgQ,YAAY,GAGrC,IAAIG,EAAUrF,CAAAA,EAAAA,EAAAA,2BAAAA,EACZoF,EACAtO,EACA3C,EACAgB,GAGIU,EAAWyP,GAAAA,EAAAA,oBAAAA,IAIjB,GAAIhR,GAAgBF,EAAU,CAE5B,IAAMG,EAAMH,CAAQ,CAAC,EAAE,CAEvByB,EAASrB,OAAO,CADAJ,CAAQ,CAAC,EAAE,CAE3ByB,EAAStB,GAAG,CAAGA,EAkErB,SAASgR,EACP1R,CAAmB,CACnBgC,CAAmB,CACnB/B,CAAwB,CACxB0R,CAA8B,CAC9BC,CAA2C,EAG3C,GAD6D,CACzDC,GADkBzO,OAAO0O,IAAI,CAACH,CAAW,CAAC,EAAE,EAAEhP,MAAM,CAKxD,IAAK,IAAMgC,KAAOgN,CAAW,CAAC,EAAE,CAAE,CAChC,IASII,EATEC,EAAqBL,CAAW,CAAC,EAAE,CAAChN,EAAI,CACxCsN,EAA0BD,CAAkB,CAAC,EAAE,CAC/C3B,EAAW6B,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBD,GAEhCE,EACkB,OAAtBP,QAA4DrN,IAA9BqN,CAAiB,CAAC,EAAE,CAACjN,EAAI,CACnDiN,CAAiB,CAAC,EAAE,CAACjN,EAAI,CACzB,KAGN,GAAyB,OAArBwN,EAA2B,CAE7B,IAAMzR,EAAMyR,CAAgB,CAAC,EAAE,CACzBxR,EAAUwR,CAAgB,CAAC,EAAE,CACnCJ,EAAe,CACbK,SAAU,KAEV1R,IAAKuR,EAAwBI,QAAQ,CAAC5O,EAAAA,gBAAgB,EAAI,KAAO/C,EACjEE,YAAa,KACbJ,KAAM,KACN8R,aAAc,KACdxR,eAAgB,IAAIC,YACpBJ,cACAX,CACF,CACF,MAGE+R,CAHK,CAGU,CACbK,SAAU,KACV1R,IAAK,KACLE,YAAa,KACbJ,KAAM,KACN8R,aAAc,KACdxR,eAAgB,IAAIC,IACpBJ,QAAS,KACTX,YAAa,CAAC,CAChB,EAGF,IAAMuS,EAAyBvQ,EAASlB,cAAc,CAAC4O,GAAG,CAAC/K,GACvD4N,EACFA,EAAuB1D,GAAG,CAACwB,EAAU0B,GAErC/P,EAASlB,SAHiB,KAGH,CAAC+N,GAAG,CAAClK,EAAK,IAAI5D,IAAI,CAAC,CAACsP,EAAU0B,EAAa,CAAC,GAGrEL,EACE1R,EACA+R,EACA9R,EACA+R,EACAG,EAEJ,CACF,EAlIQnS,EACAgC,EACAiP,EACA3Q,EACAC,EAEJ,MAEEyB,CAFK,CAEItB,GAAG,CAAGuQ,EAAavQ,GAAG,CAC/BsB,EAASpB,WAAW,CAAGqQ,EAAarQ,WAAW,CAC/CoB,EAASrB,OAAO,CAAGsQ,EAAatQ,OAAO,CACvCqB,EAASlB,cAAc,CAAG,IAAIC,IAAIkQ,EAAanQ,cAAc,EAG7D0R,CAAAA,EAAAA,EAAAA,yCAAAA,EACExS,EACAgC,EACAiP,EACAC,GAMAM,IACFvO,EAAcuO,EACdP,CAFW,CAEIjP,EACfgP,GAAU,EAEd,OAEA,CAAI,CAACA,IAILrF,EAAQC,GAJM,QAIK,CAAG3I,EACtB0I,EAAQzL,KAAK,CAAG+Q,EAChBtF,EAAQjK,YAAY,CAAGJ,EACvBqK,EAAQK,YAAY,CAAG3K,EAAIoR,IAAI,CAExBhH,CAAAA,EAAAA,EAAAA,aAAAA,EAAcvK,EAAOyK,GAC9B,CAkGO,SAASmF,EACdxO,CAAoC,CACpC+O,CAA2D,EAE3D,GAAM,CAAC9O,EAASzB,EAAgB,GAAG4R,EAAK,CAAGpQ,EAG3C,GAAIC,EAAQ8P,QAAQ,CAAC5O,EAAAA,gBAAgB,EAEnC,CAFsC,KAE/B,CADYkP,CAAAA,EAAAA,EAAAA,4BAAAA,EAA6BpQ,EAAS8O,GACrCvQ,KAAmB4R,EAAK,CAI9C,IAAME,EAA8D,CAAC,EAErE,IAAK,GAAM,CAACjO,EAAKxB,EAAc,GAAIC,OAAOyB,OAAO,CAAC/D,GAChD8R,CAAqB,CAACjO,EAAI,CAAGmM,EAC3B3N,EACAkO,GAIJ,CAPmE,KAO5D,CAAC9O,EAASqQ,KAA0BF,EAAK,yQC1OlD,gCAA+D,EAAM,cAC9D,EAAW,GAAI,CACf,aACP,MACA,mGACA,aAAgB,qBAA4B,EAC5C,yBACA,0BACA,sBACA,wBACA,iBACA,eACS,EACT,sCACA,kBACA,YAGA,SAFA,CAIA,CAAS,GAAI,EAkBb,mBAjBA,wDACA,IAAkB,0BAAoE,EACtF,mCACA,WACA,oCACA,KACA,KACiB,OACjB,KACA,KACA,CAAiB,OACjB,CAAa,MACb,EACA,EACA,EACA,EACA,CAAS,KACT,kDACA,0JCkFgB1R,2BAA2B,mBAA3BA,GAiBAwR,yCAAyC,mBAAzCA,aAvJ6B,WACC,WACT,WAEJ,OAMjC,SAASK,EACP7S,CAAmB,CACnBgC,CAAmB,CACnB/B,CAAwB,CACxBE,CAAgC,CAChCC,CAA6C,CAC7C0S,CAAsB,EAEtB,GAAM,aACJC,CAAW,CACXxS,SAAUqR,CAAiB,CAC3BvR,KAAMC,CAAS,MACfE,CAAI,CACL,CAAGL,EACA4R,EAAe/P,EACfgR,EAAoB/S,EAExB,IAAK,IAAIgT,EAAI,EAAGA,EAAIF,EAAYpQ,MAAM,CAAEsQ,GAAK,EAAG,CAC9C,IAAMxQ,EAA2BsQ,CAAW,CAACE,EAAE,CACzC1Q,EAAmBwQ,CAAW,CAACE,EAAI,EAAE,CAIrCC,EAAcD,IAAMF,EAAYpQ,MAAM,CAAG,EACzC0N,EAAW6B,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB3P,GAEhC4Q,EACJH,EAAkBlS,cAAc,CAAC4O,GAAG,CAACjN,GAEvC,GAAI,CAAC0Q,EAGH,SAGF,IAAIC,EAAkBrB,EAAajR,MANL,QAMmB,CAAC4O,GAAG,CAACjN,GACjD2Q,GAAmBA,IAAoBD,IAC1CC,EAAkB,IAAIrS,IAAIoS,GAC1BpB,EAAajR,MAFsD,QAExC,CAAC+N,GAAG,CAACpM,EAAkB2Q,IAGpD,IAAMC,EAAyBF,EAAwBzD,GAAG,CAACW,GACvDiD,EAAiBF,EAAgB1D,GAAG,CAACW,GAEzC,GAAI6C,EAAa,CACf,GACEtB,GACC,EAAC0B,GACA,CAACA,EAAelB,QAAQ,EADzB,IAEoBiB,CAAAA,CAAAA,CAAqB,CAE1C,IAAME,EAAkB3B,CAAiB,CAAC,EAAE,CACtClR,EAAMkR,CAAiB,CAAC,EAAE,CAC1BjR,EAAUiR,CAAiB,CAAC,EAAE,CAEpC0B,EAAiB,CACflB,SAAU,KAGV1R,IACEoS,GAAiBS,IAAoB9P,EAAAA,gBAAgB,CAAG/C,EAAM,KAChEE,YAAa,KACbJ,KAAM,KACN8R,aAAc,aACd3R,EACAG,eACEgS,GAAiBO,EACb,IAAItS,IAAIsS,EAAuBvS,cAAc,EAC7C,IAAIC,gBACVf,CACF,EAEIqT,GAA0BP,GAC5BU,CAAAA,EAAAA,EAAAA,OAD2C,qBAC3CA,EACEF,EACAD,EACA/S,GAGAwS,GACFjS,CAAAA,EAAAA,EAAAA,OADiB,sBACjBA,EACEb,EACAsT,EACAD,EACA/S,EACAsR,EACApR,EACAJ,GAIJgT,EAAgBvE,GAAG,CAACwB,EAAUiD,EAChC,CACA,QACF,CAEKA,GAAmBD,IAMpBC,IAAmBD,IACrBC,EAAiB,CAPI,SAAyB,EAQnBlB,MAFkB,EAEV,CACjC1R,IAAK4S,EAAe5S,GAAG,CACvBE,YAAa0S,EAAe1S,WAAW,CACvCJ,KAAM8S,EAAe9S,IAAI,CACzB8R,aAAcgB,EAAehB,YAAY,CACzCxR,eAAgB,IAAIC,IAAIuS,EAAexS,cAAc,EACrDH,QAAS2S,EAAe3S,OAC1B,EACAyS,EAAgBvE,GAAG,CAACwB,EAAUiD,IAIhCvB,EAAeuB,EACfN,EAAoBK,EACtB,CACF,CAKO,SAASrS,EACdhB,CAAmB,CACnBgC,CAAmB,CACnB/B,CAAwB,CACxBE,CAAgC,CAChCC,CAAkC,EAElCyS,EACE7S,EACAgC,EACA/B,EACAE,EACAC,GACA,EAEJ,CAEO,SAASoS,EACdxS,CAAmB,CACnBgC,CAAmB,CACnB/B,CAAwB,CACxBE,CAAgC,CAChCC,CAAkC,EAElCyS,EACE7S,EACAgC,EACA/B,EACAE,EACAC,GACA,EAEJ,gXCqJA,OAyZC,mBAzZuBqT,GA+ZXC,aAAa,mBAAbA,uCA1tB2D,YAE9C,WACO,WACJ,WACA,WACC,WACF,SACH,eASlB,WACoB,WACY,MAsRvC,SAASC,EAAkBC,CAAkC,QAC7B,UAA1B,OAAOA,EACFA,EAGFC,CAAAA,EAAAA,EAAAA,SAAAA,EAAUD,EACnB,CAYe,SAASH,EACtBK,CAGC,EAED,IAEIrP,EA+LAsP,EAyLAnG,EA1XE,CAACoG,EAAYnG,EAAwB,CAAGoG,GAAAA,EAAAA,aAAAA,EAAc/G,EAAAA,gBAAgB,EAItEpD,EAAkBoK,CAAAA,EAAAA,EAAAA,MAAAA,EAA4B,MAE9C,CACJ5S,KAAM6S,CAAQ,CACdC,GAAIC,CAAM,CACV5P,SAAU6P,CAAY,CACtB3J,SAAU4J,EAAe,IAAI,UAC7BC,CAAQ,SACRnJ,CAAO,SACPoJ,CAAO,CACPnJ,QAAM,SACNoJ,CAAO,CACPC,aAAcC,CAAgB,CAC9BC,aAAcC,CAAgB,gBAC9BC,GAAiB,CAAK,YACtBC,CAAU,CACVC,IAAKC,CAAY,yBACjBC,CAAuB,CACvB,GAAGC,EACJ,CAAGtB,EAEJrP,EAAW6P,EAGTS,GACC,kBAAOtQ,GAA6C,UAApB,OAAOA,CAAa,EAAO,EAC5D,EACW,SAAXA,CAAY4Q,IAAAA,MAAZ5Q,IAAeA,KAGjB,IAAM0K,EAASmG,EAAAA,OAAK,CAACC,UAAU,CAACC,EAAAA,gBAAgB,EAE1CpG,GAAmC,IAAjBmF,EAQlBkB,EACa,OAAjBlB,EAAwBpJ,EAAAA,YAAY,CAACuK,IAAI,CAAGvK,EAAAA,YAAY,CAACC,IAAI,CA2IzD,MAAE9J,CAAI,IAAE8S,CAAE,CAAE,CAAGkB,EAAAA,OAAK,CAACK,OAAO,CAAC,KACjC,IAAMC,EAAejC,EAAkBQ,GACvC,MAAO,CACL7S,KAAMsU,EACNxB,GAAIC,EAASV,EAAkBU,GAAUuB,CAC3C,CACF,EAAG,CAACzB,EAAUE,EAAO,EAIjBU,IA4BAhB,EAAQuB,EAAAA,OAAK,CA5BG,QA4BM,CAACO,IAAI,CAACpR,EAAAA,EAYhC,IAAMqR,EAAgBf,EAClBhB,GAAS,iBAAOA,GAAsBA,EAAMkB,GAAG,CAC/CC,EAMEa,EAA+BT,EAAAA,OAAK,CAACU,WAAW,CACnDrH,IACgB,MAAM,CAAjBQ,IACFrF,EAAgBmM,OAAO,CAAG5I,CAAAA,EAAAA,EAAAA,iBAAAA,EACxBsB,EACArN,EACA6N,EACAsG,EACArG,EACAvB,EAAAA,EAIG,KACD/D,EAAgBmM,OAAO,EAAE,CAC3BxI,CAAAA,EAAAA,EAAAA,+BAAAA,EAAgC3D,EAAgBmM,OAAO,EACvDnM,EAAgBmM,OAAO,CAAG,MAE5BvI,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4BiB,EAC9B,GAEF,CAACS,EAAiB9N,EAAM6N,EAAQsG,EAAiB5H,EAAwB,EAKrEqI,EAMF,CACFjB,IATgBkB,CAAAA,EAAAA,EAAAA,YAAAA,EAAaJ,EAA8BD,GAU3DpB,QAAQ0B,CAAC,EASH,GAAsC,YAAnB,OAAO1B,GAC5BA,EAAQ0B,GAIRrB,GACAhB,EAAMD,KAAK,EACoB,YAC/B,OADOC,EAAMD,KAAK,CAACY,OAAO,EAE1BX,EAAMD,KAAK,CAACY,OAAO,CAAC0B,GAGjBjH,IAIDiH,EAAEC,EAJO,cAIS,EAItBC,SAvYGA,CACY,CACnBhV,CAAY,CACZ8S,CAAU,CACVtK,CAAqD,CACrDuB,CAAiB,CACjBC,CAAgB,CAChB0J,CAAmC,EAEnC,GAAM,UAAEuB,CAAQ,CAAE,CAAGH,EAAEI,aAAa,CAKpC,IACGC,CAHiD,MAA3BF,EAASG,WAAW,IAGtBC,SA5BAC,CAAuB,EAE9C,IAAMpI,EADcoI,EAAMJ,aAAa,CACZK,YAAY,CAAC,UACxC,OACGrI,GAAqB,UAAXA,GACXoI,EAAME,OAAO,EACbF,EAAMG,OAAO,EACbH,EAAMI,QAAQ,EACdJ,EAAMK,MAAM,EACXL,EADe,WACE,EAAgC,IAA5BA,EAAMM,UADiB,CACN,CAACC,KAE5C,EAiByCf,IACrCA,EAAEI,aAAa,CAACY,YAAY,CAAC,cAC7B,GAKE,CAACC,CAAAA,EAAAA,EAAAA,UAAAA,EAAW/V,GAAO,CACjB+J,IAGF+K,EAAEkB,GAHS,WAGK,GAChBnP,SAASkD,OAAO,CAAC/J,IAInB,MACF,CAEA8U,EAAEkB,cAAc,GAyBhBhC,EAAAA,OAAK,CAACnM,eAAe,CAvBJ,KACf,GAAI6L,EAAY,CACd,IAAIuC,GAAqB,EAQzB,GANAvC,EAAW,CACTsC,eAAgB,KACdC,GAAqB,CACvB,CACF,GAEIA,EACF,MAEJ,CAEApQ,CAAAA,EAAAA,EAAAA,MAL0B,gBAK1BA,EACEiN,GAAM9S,EACN+J,EAAU,UAAY,OACtBC,SAAAA,EACAxB,EAAgBmM,KADN,EACa,CAE3B,GAGF,EA2UkBG,EAAG9U,EAAM8S,EAAItK,EAAiBuB,EAASC,EAAQ0J,GAC7D,EACAL,aAAayB,CAAC,EACR,GAA+C,YAA5B,OAAOxB,GAC5BA,EAAiBwB,GAIjBrB,GACAhB,EAAMD,KAAK,EACyB,YACpC,OADOC,EAAMD,KAAK,CAACa,YAAY,EAE/BZ,EAAMD,KAAK,CAACa,YAAY,CAACyB,GAGtBjH,GAIAC,GAKL7B,CAAAA,CATa,CASbA,EAAAA,WALwB3C,OAKxB2C,CALgCiK,CAM9BpB,EANiC,CAACqB,KAAa,GAAL,IAM3B,EACfC,IAH+BvC,EAKnC,EACAN,aAEI,CAFUjK,MAAsC,EAEvCiK,CADTtQ,CAASA,CAEFwQ,GAA8C,YAA5B,OAAOD,GAC5BA,EAAiBsB,GAIjBrB,GACAhB,EAAMD,KAAK,EACyB,YAApC,OAAOC,EAAMD,KAAK,CAACe,YAAY,EAE/Bd,EAAMD,KAAK,CAACe,YAAY,CAACuB,GAGtBjH,GAIAC,GAKL7B,CAAAA,CATa,CASbA,EAAAA,SALsB,SAKtBA,EACE6I,EAAEI,aAAa,EACfkB,IAH+BvC,EAKnC,CACN,EAmCA,MA9BIwC,CAAAA,EAAAA,EAAAA,aAAAA,EAAcvD,GAChB8B,EADqB,IACN,CAAG9B,EAElB,IACAI,IACgB,MAAfT,CAAsB,CAAhBhM,IAAI,EAAc,SAAUgM,EAAMD,KAAI,GAC7C,CACAoC,EAAW5U,IAAI,CAAG0I,CAAAA,EAAAA,EAAAA,WAAAA,EAAYoK,EAAAA,EAc9BxG,EATEmH,EASKO,EAAAA,CAAP1H,MAAY,CAACgK,IAAbhK,QAAyB,CAACmG,EAAOmC,GAG/B,UAACb,IAAAA,CAAG,GAAGD,CAAS,CAAG,GAAGc,CAAU,UAC7BzR,IAML,UAACoT,EAAkBC,QAAQ,EAAClT,MAAOoP,WAChCpG,GAGP,GAhsB0B,OAksB1B,IAAMiK,EAAoBE,GAAAA,EAAAA,aAAAA,EAExB7K,EAAAA,OAFI2K,SAEY,EAELnE,EAAgB,IACpB6B,CAAAA,EAAAA,EAAAA,UAAAA,EAAWsC,4QC3tBpB,gBACA,wBACA,WACI,UACJ,aAEA,gBCJA,EAAW,YAAgB,SAC3B,aAAU,QAAyB,EACnC,EAAwB,UAAc,YACtC,YACA,MACA,uBACA,WACA,MAIA,EAHA,EAAY,QAAc,YAA+B,UAAc,YACxD,gBAAoB,2BAKnC,MAA2B,SAAG,IAAc,oBAA2C,gBAAoB,IAAe,cAAkB,kBAA0C,CACtL,CACA,MAAyB,SAAG,IAAc,sBAA2C,CACrF,CAAC,EACD,qBACA,MAAgB,YAAgB,SAChC,aAAU,QAAyB,EACnC,GAAM,gBAAoB,KAC1B,eAuCA,GACA,0DACA,mDACA,EACA,MAGA,GADA,mDACA,wCAEA,YAEA,oBAlDA,GACA,WAeA,KACA,OAA0B,MAC1B,gBACA,WACA,OACA,mBAEA,KACA,cACA,QACA,OACA,EACQ,GACR,SAEM,YACN,MAAkC,WAC5B,iBACN,sCAEA,CACA,OAAW,UACX,EArCA,WAIA,OAHA,SAA0B,UAAc,EACxC,SDpBA,WCoB6C,CDpB7C,GACA,WACA,SACA,YACA,aAIA,OAHA,yBACA,OAEA,CACA,CAAK,EACL,KACA,WACA,YAAwB,WAAqB,KAC7C,WACA,qBACA,IAEA,YAEA,CACA,CAEA,CACA,ECH6C,QAElC,cAAkB,KAC7B,CACA,OAAS,UAAc,YAAuB,UAAc,gBAC5D,CAAC,CACD,2BACA,iBAAmB,EAAU,GACJ,SAAG,CAAC,UAAS,WAAI,EAAU,EAEpD,cACA,OAAS,gBAAoB,eAC7B,0JCiiCgBG,SAAS,mBAATA,GA1VAC,uBAAuB,mBAAvBA,GAvpBAC,kBAAkB,mBAAlBA,GA6jCAC,oCAAoC,mBAApCA,SAAAA,EACdC,CAAuB,CACvBzG,CAA8B,EAY9B,IAAM0G,EAAsB1G,CAAW,CAAC,EAAE,CACpC2G,EAAoBF,EAAatX,cAAc,CAC/CmM,EAAoB,IAAIlM,IAAIuX,GAClC,IAAK,IAAI7V,KAAoB4V,EAAqB,CAChD,IAAME,EACJF,CAAmB,CAAC5V,EAAiB,CACjC+V,EAAeD,CAAgB,CAAC,EAAE,CAClCE,EAAkBvG,GAAAA,EAAAA,oBAAAA,EAAqBsG,GACvCE,EAAqBJ,EAAkB5I,GAAG,CAACjN,GACjD,GAAIiW,KAAuBnU,MAAW,CACpC,IAAMoU,EAAoBD,EAAmBhJ,GAAG,CAAC+I,GACjD,QAA0BlU,IAAtBoU,EAAiC,CACnC,IAAMC,EAAoBT,EACxBQ,EACAJ,GAEIM,EAAqB,IAAI9X,IAAI2X,GACnCG,EAAmBhK,GAAG,CAAC4J,EAAiBG,GACxC3L,EAAkB4B,GAAG,CAACpM,EAAkBoW,EAC1C,CACF,CACF,CAUA,IAAMnY,EAAM0X,EAAa1X,GAAG,CACtBoY,EAAoBC,EAAcrY,IAAuB,YAAfA,EAAIsY,MAAM,CAE1D,MAAO,CACL5G,SAAU,SACV1R,EACAF,KAAM4X,EAAa5X,IAAI,CAEvB8R,aAAcwG,EAAoBV,EAAa9F,YAAY,CAAG,CAAC,KAAM,KAAK,CAC1E1R,YAAakY,EAAoBV,EAAaxX,WAAW,CAAG,KAC5DD,QAASyX,EAAazX,OAAO,CAG7BG,eAAgBmM,EAEhBjN,YAAaoY,EAAapY,WAC5B,CACF,aAvsCoC,WACP,WACQ,WAEO,WACP,OAiC/BiZ,EAAyC,CAC7CC,MAAO,KACPC,KAAM,KACNC,mBAAoB,KACpB3U,SAAU,IACZ,EAiCO,SAASyT,EACdlY,CAAmB,CACnBoY,CAAuB,CACvBiB,CAAiC,CACjCC,CAAiC,CACjCC,CAAsC,CACtCjH,CAA6B,CAC7BkH,CAA8B,CAC9BC,CAA6B,CAC7BC,CAAkD,EAGlD,OAAOC,SAeAA,EACP3Z,CAAmB,CACnBoY,CAAuB,CACvBiB,CAAiC,CACjCC,CAAiC,CACjCM,CAA0B,CAC1BL,CAAsC,CACtCjH,CAA6B,CAC7BkH,CAA8B,CAC9BC,CAA6B,CAC7B1G,CAA8B,CAC9B2G,CAAkD,EAGlD,IAAMG,EAAyBR,CAAc,CAAC,EAAE,CAC1CS,EAAyBR,CAAc,CAAC,EAAE,CAC1CS,EAAwC,OAAjBR,EAAwBA,CAAY,CAAC,EAAE,CAAG,KAElEK,GAIwC,KAAtBN,CAAc,CAAC,EAAE,GAGpCM,EAFgB,CAEI,GAIxB,IAAMtB,EAAoBF,EAAatX,cAAc,CAa/CkZ,EAAyB,IAAIjZ,IAAIuX,GAOnC2B,EAEA,CAAC,EACDC,EAAe,KAWfC,EAAsB,GAQtBC,EAEA,CAAC,EAEL,IAAK,IAAI3X,KAAoBqX,EAAwB,CACnD,IAyBIO,EAzBEC,EACJR,CAAsB,CAACrX,EAAiB,CACpC8X,EACJV,CAAsB,CAACpX,EAAiB,CACpCiW,EAAqBJ,EAAkB5I,GAAG,CAACjN,GAC3C+X,EACqB,OAAzBT,EACIA,CAAoB,CAACtX,EAAiB,CACtC,KAEAgY,EAAkBH,CAAmB,CAAC,EAAE,CACxCI,EAAsB3H,EAAY4H,MAAM,CAAC,CAC7ClY,EACAgY,EACD,EACKG,EAAqB1I,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBuI,GAE1CI,OACoBtW,IAAxBgW,EAAoCA,CAAmB,CAAC,EAAE,MAAGhW,EAEzDoU,OACmBpU,IAAvBmU,EACIA,EAAmBhJ,GAAG,CAACkL,QACvBrW,EA+HN,GAAI8V,QAhHAA,EAZAI,IAAoBtW,EAAAA,IAYR2W,eAZ2B,CAQrCP,CARuC,IAQfhW,MAufzB,CACL2U,IAxfyC,EAITqB,CAofzBQ,CACP5B,KAAM,KACNC,mBAAoB,KACpB3U,SAAU,IACZ,EArfkBuW,EACVhb,EACAua,EACAD,EACA3B,EACAiB,OACsBrV,IAAtBiW,EAAkCA,EAAoB,KACtDlI,EACAkH,EACAkB,EACAhB,GAIJD,GAO+C,GAC/C,CADArW,OAAO0O,IAAI,CAACwI,CAAmB,CAAC,EAAE,CANlC,CAMoC3X,MAAM,CAoB9BqY,EACVhb,EACAua,EACAD,EACA3B,EACAiB,OACsBrV,IAAtBiW,EAAkCA,EAAoB,EAhCrB,GAiCjClI,EACAkH,EACAkB,EACAhB,QAGsBnV,IAAxBgW,QACoBhW,IAApBsW,GACAnY,CAAAA,EAAAA,EAAAA,YAAAA,EAAa+X,EAAiBI,SAGNtW,IAAtBoU,GACA4B,EAHF,GAG0BhW,MAIZoV,EACV3Z,EACA2Y,CALF,CAME4B,EACAD,EACAV,EACAY,EACAlI,EACAkH,EACAC,EACAiB,EACAhB,GAKUsB,EACVhb,EACAua,EACAD,EACA3B,EACAiB,OACsBrV,IAAtBiW,EAAkCA,EAAoB,KACtDlI,EACAkH,EACAkB,EACAhB,IAmBkB,CAGtB,GAAwB,MAAM,CAA1BW,EAAUnB,KAAK,CAGjB,OAAOD,CAGY,MAAM,EAAvBiB,IACFA,EAAe,IAAInZ,GAAAA,EAErBmZ,EAAarL,GAAG,CAACpM,EAAkB4X,GACnC,IAAMzB,EAAoByB,EAAUlB,IAAI,CACxC,GAA0B,OAAtBP,EAA4B,CAC9B,IAAMC,EAAsC,IAAI9X,IAAI2X,GACpDG,EAAmBhK,GAAG,CAAC+L,EAAoBhC,GAC3CoB,EAAuBnL,GAAG,CAACpM,EAAkBoW,EAC/C,CAKA,IAAMoC,EAAiBZ,EAAUnB,KAAK,CACtCe,CAA0B,CAACxX,EAAiB,CAAGwY,EAE/C,IAAMC,EAA0Bb,EAAUjB,kBACV,MAAM,EAAlC8B,GAEFf,GAAsB,EACtBC,CAA0B,CAAC3X,EAAiB,CAAGyY,GAE/Cd,CAA0B,CAAC3X,EAAiB,CAAGwY,CAEnD,MAEEhB,CAFK,CAEsBxX,EAAiB,CAAG6X,EAC/CF,CAA0B,CAAC3X,EAAiB,CAAG6X,CAEnD,CAEA,GAAqB,MAAM,CAAvBJ,EAEF,OAAO,KAGT,IAAMnI,EAA+B,CACnCK,SAAU,KACV1R,IAAK0X,EAAa1X,GAAG,CAOrBE,YAAawX,EAAaxX,WAAW,CACrCJ,KAAM4X,EAAa5X,IAAI,CACvB8R,aAAc8F,EAAa9F,YAAY,CACvC3R,QAASyX,EAAazX,OAAO,CAG7BG,eAAgBkZ,cAEhBha,CACF,EAEA,MAAO,CAELkZ,MAAOiC,EACL7B,EACAW,GAEFd,KAAMpH,EACNqH,mBAAoBe,EAChBgB,EACE7B,EACAc,GAEF,KACJ3V,SAAUyV,CACZ,CACF,EAjUIla,EACAoY,EACAiB,EACAC,GACA,EACAC,EACAjH,EACAkH,EACAC,EAV4C,EAAE,CAY9CC,EAEJ,CAuTA,SAASsB,EACPhb,CAAmB,CACnBqZ,CAAwC,CACxCC,CAAiC,CACjCtG,CAAmC,CACnC4G,CAA0B,CAC1BL,CAAsC,CACtC6B,CAA4C,CAC5C5B,CAA8B,CAC9BzG,CAA8B,CAC9B2G,CAAkD,QAElD,CAAKE,IAqBDP,YACAgC,CAAAA,EAtBoB,EAsBpBA,2BAAAA,EAA4BhC,EAAgBC,EAAAA,EAGrCL,CAFP,CAKGqC,SAYAA,EACPtb,CAAmB,CACnB2R,CAA8B,CAC9BqB,CAAmC,CACnCuG,CAAsC,CACtC6B,CAA4C,CAC5C5B,CAA8B,CAC9BzG,CAA8B,CAC9B2G,CAAkD,EAQlD,IAQIhZ,EACAC,EACAH,EACA+a,EAXElD,EAAsB1G,CAAW,CAAC,EAAE,CACpC6J,EAA4D,IAA5CpY,OAAO0O,IAAI,CAACuG,GAAqB1V,MAAM,CAW7D,GACEqQ,YAIAA,CAHA,CAGkBhT,WAAW,CAAGyb,EAAAA,oBAAoB,CAAGzb,EAIvDU,EAAMsS,EAAkBtS,GAAG,CAC3BC,EAAUqS,CAJV,CAI4BrS,OAAO,CACnCH,EAAOwS,EAAkBxS,IAAI,CAG7B+a,CAZoE,CAY7CvI,EAAkBhT,WAAW,MAC/C,GAAqB,MAAM,CAAvBuZ,EAsCT,OAAOmC,EACL1b,EACA2R,EACA,KACAyJ,EACA5B,EACAzG,EACA2G,QAjCF,GARAhZ,EAAM6Y,CAAY,CAAC,EAAE,CACrB5Y,EAAU4Y,CAAY,CAAC,EAAE,CACzB/Y,EAAOgb,EAAgBJ,EAA8B,KAIrDG,EAAuBvb,EACMuZ,CAAY,CAAC,EAAE,EAKzCC,GAAyBgC,EAI1B,OAAOE,EACL1b,EACA2R,EALF,EAOEyJ,EACA5B,EACAzG,EACA2G,GAyBN,IAvC2C,EAuCG,OAAjBH,EAAwBA,CAAY,CAAC,EAAE,CAAG,KACjEW,EAAe,IAAInZ,IACnB4a,EACJ3I,KAAsBzO,MAAYyO,EAAkBlS,YAxCuB,EAwCT,CAAG,KACjE8a,EAAoB,IAAI7a,IAAI4a,GAC9BvB,EAEA,CAAC,EACDD,GAAsB,EAC1B,GAAIqB,EAOF9B,EAAyBhV,IAAI,CAACqO,MAPb,EASjB,IAAK,IAAItQ,KAAoB4V,EAAqB,CAChD,IAAME,EACJF,CAAmB,CAAC5V,EAAiB,CACjC+X,EACqB,OAAzBT,EACIA,CAAoB,CAACtX,EAAiB,CACtC,KACAoZ,EAC0B,OAA9BF,EACIA,EAA0BjM,GAAG,CAACjN,QAC9B8B,EACAiU,EAAeD,CAAgB,CAAC,EAAE,CAClCuD,EAAmB/I,EAAY4H,MAAM,CAAC,CAC1ClY,EACA+V,EACD,EACKC,EAAkBvG,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBsG,GAOvC6B,EAAYiB,EAChBtb,EACAuY,OAN4BhU,IAA5BsX,EACIA,EAAwBnM,GAAG,CAAC+I,QAC5BlU,EAMJiW,EACAY,EACA5B,EACAsC,EACApC,GAEFQ,EAAarL,GAAG,CAACpM,EAAkB4X,GACnC,IAAMa,EAA0Bb,EAAUjB,kBAAkB,MACtB,EAAlC8B,GAEFf,GAAsB,EACtBC,CAA0B,CAAC3X,EAAiB,CAAGyY,GAE/Cd,CAA0B,CAAC3X,EAAiB,CAAG8V,EAEjD,IAAMK,EAAoByB,EAAUlB,IAAI,CACxC,GAA0B,OAAtBP,EAA4B,CAC9B,IAAMC,EAAsC,IAAI9X,IAChD8X,EAAmBhK,GAAG,CAAC4J,EAAiBG,GACxCgD,EAAkB/M,GAAG,CAACpM,EAAkBoW,EAC1C,CACF,CAGF,MAAO,CAKLK,MAAOvH,EACPwH,KAAM,CACJ/G,SAAU,SAGV1R,EACAE,YAAa,UACbJ,EACA8R,aAAc,aACd3R,EACAG,eAAgB8a,EAChB5b,YAAaub,CACf,EACAnC,mBAAoBe,EAChBgB,EAAgCxJ,EAAayI,GAC7C,KACJ3V,SAAUyV,CACZ,CACF,EAtMIla,EACAsZ,EACAtG,EACAuG,EACA6B,EACA5B,EACAzG,EACA2G,EAEJ,CA+LA,SAASyB,EACPY,CAAkC,CAClCC,CAA8D,EAE9D,IAAMC,EAA2B,CAACF,CAAe,CAAC,EAAE,CAAEC,EAAY,CAalE,OATI,KAAKD,IACPE,CAAK,CAAC,EAAE,CAAGF,CAAe,CAAC,IAEzB,EAHsB,GAGjBA,IACPE,CAAK,CAAC,EAAE,CAAGF,CAAe,CAAC,IAEzB,EAHsB,GAGjBA,IACPE,CAAK,CAAC,EAAE,CAAGF,CAAe,CAAC,IAEtBE,CACT,CAJ4B,SAMnBP,EACP1b,CAAmB,CACnB2R,CAA8B,CAC9B4H,CAAsC,CACtCjH,CAA6B,CAC7BkH,CAA8B,CAC9BzG,CAA8B,CAC9B2G,CAAkD,EAMlD,IAAMN,EAAqB+B,EACzBxJ,EACAA,CAAW,CAAC,EAAE,EAsBhB,OAAOuK,CApBW,CAAC,EAAE,CAAG,UAEF,CACpBhD,MAAOvH,EAGPwH,KAAMgD,SA8MDA,EACPnc,CAAmB,CACnB2R,CAA8B,CAC9B4H,CAAsC,CACtCjH,CAA6B,CAC7BkH,CAA8B,CAC9BzG,CAA8B,CAC9B2G,CAAkD,EAElD,IAAMrB,EAAsB1G,CAAW,CAAC,EAAE,CACpCoI,EAAwC,OAAjBR,EAAwBA,CAAY,CAAC,EAAE,CAAG,KAEjEzY,EAAiB,IAAIC,IAC3B,IAAK,IAAI0B,KAAoB4V,EAAqB,CAChD,IAAME,EACJF,CAAmB,CAAC5V,EAAiB,CACjC+X,EACqB,OAAzBT,EACIA,CAAoB,CAACtX,EAAiB,CACtC,KAEA+V,EAAeD,CAAgB,CAAC,EAAE,CAClCuD,EAAmB/I,EAAY4H,MAAM,CAAC,CAC1ClY,EACA+V,EACD,EACKC,EAAkBvG,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBsG,GAEvCI,EAAoBuD,EACxBnc,EACAuY,OACsBhU,IAAtBiW,EAAkC,KAAOA,EACzClI,EACAkH,EACAsC,EACApC,GAGIb,EAAsC,IAAI9X,IAChD8X,EAAmBhK,GAAG,CAAC4J,EAAiBG,GACxC9X,EAAe+N,GAAG,CAACpM,EAAkBoW,EACvC,CAIA,IAAM2C,EAAwC,IAAxB1a,EAAesb,IAAI,CAErCZ,GAOF9B,EAAyBhV,IAAI,CAACqO,GAGhC,EAVmB,EAUbsJ,EAAoC,OAAjB9C,EAAwBA,CAAY,CAAC,EAAE,CAAG,KAC7D+C,EAAwC,OAAjB/C,EAAwBA,CAAY,CAAC,EAAE,CAAG,KACvE,MAAO,CACLnH,SAAU,KACVtR,eAAgBA,EAEhBF,iBAAkC2D,IAArB8X,EAAiCA,EAAmB,KACjE/J,aAAckJ,EAAgBlJ,EAAe,CAAC,KAAM,KAAK,CAKzD3R,QAAS2b,KAAyB/X,MAAY+X,EAAuB,KAIrE5b,IAAK6b,IACL/b,KAAMgb,EAAiBe,IAA0C,iBAEjEvc,CACF,CACF,EA3RMA,EACA2R,EACA4H,EACAjH,EACAkH,EACAzG,EACA2G,sBAIFN,EACA3U,SAAU,IACZ,CAEF,CA4BO,SAASwT,EACd9R,CAAuB,CACvBqW,CAAmD,EAEnDA,EAAgB7T,IAAI,CAClB,OAAC,YAAExI,CAAU,CAA6B,GACxC,GAA0B,UAAtB,OAAOA,GAMX,IAAK,IAAM+Q,KAAwB/Q,EAAY,CAC7C,GAAM,aACJ4S,CAAW,CACX1S,KAAMoc,CAAiB,CACvBlc,SAAUmc,CAAW,CACrBlc,KAAMmc,CAAW,CAClB,CAAGzL,EAECwL,GAOLE,SAqBCA,CA5BiB,CA8BxB7J,CAA8B,CAC9B0J,CAAoC,CACpCC,CAA8B,CAC9BC,CAAqB,EAYrB,IAAIxW,EAAO0W,EACX,IAAK,IAAI5J,EAAI,EAAGA,EAAIF,EAAYpQ,MAAM,CAAEsQ,GAAK,EAAG,CAC9C,IAAMxQ,EAA2BsQ,CAAW,CAACE,EAAE,CACzC1Q,EAAmBwQ,CAAW,CAACE,EAAI,EAAE,CACrCiH,EAAe/T,EAAK1B,QAAQ,CAClC,GAAqB,OAAjByV,EAAuB,CACzB,IAAMG,EAAYH,EAAaxK,GAAG,CAACjN,GACnC,GAAI4X,KAAc9V,MAAW,CAC3B,IAAMuY,EAAczC,EAAUnB,KAAK,CAAC,EAAE,CACtC,GAAIxW,CAAAA,EAAAA,EAAAA,YAAAA,EAAaH,EAASua,GAAc,CAEtC3W,EAAOkU,EACP,QACF,CACF,CACF,CAKA,MACF,EAEA0C,SAQOA,EACP5W,CAAuB,CACvBsW,CAAoC,CACpCC,CAA8B,CAC9BC,CAAqB,EAErB,GAAIxW,MAAkC,GAA7BiT,kBAAkB,CAEzB,OAKF,IAAMc,EAAe/T,EAAK1B,QAAQ,CAC5BuY,EAAW7W,EAAKgT,IAAI,CAC1B,GAAqB,OAAjBe,EAAuB,CAIR,MAAM,CAAnB8C,IACFC,SA+HGA,EACPC,CAAoB,CACpBC,CAA4B,CAC5BC,CAA8B,CAC9BV,CAA8B,CAC9BC,CAAqB,EAYrB,IAAMU,EAAoBF,CAAS,CAAC,EAAE,CAChCG,EAAsBF,CAAW,CAAC,EAAE,CACpCG,EAAeb,CAAW,CAAC,EAAE,CAK7B5b,EAAiBoc,EAAUpc,cAAc,CAC/C,IAAK,IAAI2B,KAAoB4a,EAAmB,CAC9C,IAAMG,EACJH,CAAiB,CAAC5a,EAAiB,CAC/Bgb,EACJH,CAAmB,CAAC7a,EAAiB,CACjCib,EACJH,CAAY,CAAC9a,EAAiB,CAE1Bkb,EAAkB7c,EAAe4O,GAAG,CAACjN,GACrCmb,EAAmBJ,CAAc,CAAC,EAAE,CACpCK,EAAsB3L,GAAAA,EAAAA,oBAAAA,EAAqB0L,GAE3CE,OACgBvZ,IAApBoZ,EACIA,EAAgBjO,GAAG,CAACmO,GACpBtZ,YAEiBA,IAAnBuZ,OAA8B,EAETvZ,IAArBkZ,GACA/a,CAAAA,EAAAA,EAAAA,YAAAA,EAAakb,EAAkBH,CAAgB,CAAC,EAAE,GAClD,MACIC,EAEFT,EACEa,EACAN,EACAC,EACAC,EACAf,EAPcpY,CAchBwZ,EAAsBP,EAAgBM,EAAgB,MAdzBJ,CA+BrC,IAAMhd,EAAMwc,EAAUxc,GAAG,CACnBsd,CAhC6C,CAgCxBtB,CAAW,CAAC,EAAE,CAhCgB,KAiCvC,EAAdhc,EAGFwc,EAAUxc,GAAG,CAAGsd,EACPjF,EAAcrY,IAIvBA,EAJ6B,OAIlB,CAACsd,GASd,IAAMxd,EAAO0c,EAAU1c,IAAI,CACvBuY,EAAcvY,IAChBA,EAAKyF,CADkB,MACX,CAAC0W,EAEjB,EAnOQK,EACA7W,EAAK+S,KAAK,CACVuD,EACAC,EACAC,GAGFxW,EAAKiT,kBAAkB,CAAG,MAE5B,MACF,CAGA,IAAM6E,EAAiBxB,CAAiB,CAAC,EAAE,CACrCyB,EAAsBxB,CAAW,CAAC,EAAE,CAE1C,IAAK,IAAMja,KAAoBga,EAAmB,CAChD,IAAM0B,EACJF,CAAc,CAACxb,EAAiB,CAC5B2b,EACJF,CAAmB,CAACzb,EAAiB,CAEjC4X,EAAYH,EAAaxK,GAAG,CAACjN,GACnC,QAAkB8B,IAAd8V,EAAyB,CAC3B,IAAMyC,EAAczC,EAAUnB,KAAK,CAAC,EAAE,CACtC,GACExW,GAAAA,EAAAA,YAAAA,EAAayb,CAAsB,CAAC,EAAE,CAAErB,IAExCsB,MADAA,EAIA,OAAOrB,EACL1C,EACA8D,EACAC,EACAzB,EAGN,CAKF,CAhB2B,EAvDzBxW,EACAsW,CAwDI,CAvDJC,EACAC,EAEJ,EAlEUxW,EACA4M,EACA0J,EACAC,EACAC,EAEJ,CAKA3E,EAAU7R,EAAM,MAClB,EACA,IAEE6R,EAAU7R,EAAME,EAClB,EAEJ,CA4SO,SAAS2R,EAAU7R,CAAuB,CAAEE,CAAU,EAC3D,IAAM6W,EAAY/W,EAAKgT,IAAI,CAC3B,GAAkB,MAAM,CAApB+D,EAEF,OAGF,IAAMhD,EAAe/T,EAAK1B,QAAQ,CAClC,GAAqB,MAAM,CAAvByV,EAGF6D,EAAsB5X,EAAK+S,KAAK,CAAEgE,EAAW7W,QAK7C,IAAK,IAAMgU,KAAaH,EAAa7W,MAAM,GAAI,EACnCgX,EAAWhU,GAKzBF,EAAKiT,kBAAkB,CAAG,IAC5B,CAEA,SAAS2E,EACPpM,CAA8B,CAC9BuL,CAAoB,CACpB7W,CAAU,EAMV,IAAMgS,EAAsB1G,CAAW,CAAC,EAAE,CACpC7Q,EAAiBoc,EAAUpc,cAAc,CAC/C,IAAK,IAAI2B,KAAoB4V,EAAqB,CAChD,IAAME,EACJF,CAAmB,CAAC5V,EAAiB,CACjCkb,EAAkB7c,EAAe4O,GAAG,CAACjN,GAC3C,QAAwB8B,IAApBoZ,EAGF,KAHiC,IAKnC,IAAMnF,EAAeD,CAAgB,CAAC,EAAE,CAClCE,EAAkBvG,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBsG,GACvCsF,EAAiBH,EAAgBjO,GAAG,CAAC+I,QACpBlU,IAAnBuZ,GACFC,EAAsBxF,EAAkBuF,EAAgBzX,EAK5D,CACA,IAAM3F,EAAMwc,EAAUxc,GAAG,CACrBqY,EAAcrY,KACF,CADQ,KACF,CAAhB2F,EAEF3F,EAAIuF,OAAO,CAAC,MAGZvF,EAAIwF,MAAM,CAACG,IAQf,IAAM7F,EAAO0c,EAAU1c,IACnBuY,GAAcvY,IAChBA,EAAKyF,CADkB,MACX,CAAC,KAEjB,CAkEA,IAAMoY,EAAWC,SAkCjB,SAASvF,EAAcnU,CAAU,EAC/B,OAAOA,GAASA,EAAM2Z,GAAG,GAAKF,CAChC,CAEA,SAAS9B,IAGP,IAFItW,EACAC,EACEsY,EAAa,IAAIxY,QAAyB,CAACyY,EAAKC,KACpDzY,EAAUwY,EACVvY,EAASwY,CACX,GAmBA,OAlBAF,EAAWxF,MAAM,CAAG,UACpBwF,EAAWvY,OAAO,CAAG,IACO,WAAW,CAAjCuY,EAAWxF,MAAM,GAEnB2F,EAAa3F,MAAM,CAAG,YADqBwF,EAE9B5Z,KAAK,CAAGA,EACrBqB,EAAQrB,GAEZ,EACA4Z,EAAWtY,MAAM,CAAG,IACQ,WAAW,CAAjCsY,EAAWxF,MAAM,GAEnB4F,EAAY5F,MAAM,CAAG,WACrB4F,EAAYC,MAAM,CAAGxY,EACrBH,EAAOG,GAEX,EACAmY,EAAWD,GAAG,CAAGF,EACVG,CACT,gXC5wCaM,aAAa,mBAAbA,GAEA9T,eAAe,mBAAfA,aAPgB,UAItB,OACM8T,EAAgB,IAAIpZ,EAAAA,YAAY,CAAC,GAEjCsF,EAcb,SAAS+T,CACoB,CAC3B5d,CAAsB,EAGtB6d,CAAAA,CAnB6BpU,CAmB7BoU,EAAAA,GAnBoE,GAClEC,CAAwCA,GACxCF,QAiBFC,EAAmB9d,EAAMe,aAAa,EAEtC,GAAM,KAAEZ,CAAG,CAAE,CAAGF,EAWhB,MATA+d,CAAAA,EAAAA,EAAAA,6BAAAA,EAA8B,KAC5B7d,EACAa,QAAShB,EAAMgB,OAAO,CACtBD,cAAef,EAAMe,aAAa,CAClCiJ,KAAM/J,EAAO+J,IAAI,CACjB7K,KAAMa,EAAMb,IAAI,CAChBkK,eAAe,CACjB,GAEOrJ,CACT,2VCrCgBie,qCAAAA,SAAAA,EACdnd,CAAmB,CACnB/B,CAAwB,CACxBoC,CAAoC,EAEpC,IAAM6Q,EAAc7Q,EAAkBM,MAAM,EAAI,EAC1C,CAACF,EAAkBF,EAAQ,CAAGF,EAE9BgO,EAAW6B,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB3P,GAEhC4Q,EACJlT,EAAca,cAAc,CAAC4O,GAAG,CAACjN,GAEnC,GAAI,CAAC0Q,EAGH,OAGF,IAAIC,EAAkBpR,EAASlB,QAND,MAMe,CAAC4O,GAAG,CAACjN,GAOlD,GANK2Q,GAAmBA,IAAoBD,IAC1CC,EAAkB,IAAIrS,IAAIoS,GAC1BnR,EAASlB,MAF0D,QAE5C,CAAC+N,GAAG,CAACpM,EAAkB2Q,IAI5CF,EAAa,YACfE,EAAgBzD,MAAM,CAACU,GAIzB,IAAMgD,EAAyBF,EAAwBzD,GAAG,CAACW,GACvDiD,EAAiBF,EAAgB1D,GAAG,CAACW,GAEpCiD,GAAmBD,IAMpBC,IAAmBD,IACrBC,EAAiB,CAPI,SAAyB,EAQnBlB,MAFkB,EAEV,CACjC1R,IAAK4S,EAAe5S,GAAG,CACvBE,YAAa0S,EAAe1S,WAAW,CACvCJ,KAAM8S,EAAe9S,IAAI,CACzB8R,aAAcgB,EAAehB,YAAY,CACzCxR,eAAgB,IAAIC,IAAIuS,EAAexS,cAAc,CACvD,EACAsS,EAAgBvE,GAAG,CAACwB,EAAUiD,IAGhC6L,EACE7L,EACAD,EACAzQ,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyBP,IAE7B,aA/DqC,WACI,oVCDzBgZ,qCAAAA,SAAAA,EACdpY,CAA8B,CAC9Bmc,CAA2B,EAG3B,IAAMC,EAAqBpc,CAAW,CAAC,EAAE,CACnCqc,EAAkBF,CAAQ,CAAC,EAAE,CAKnC,GAAIvc,MAAMC,OAAO,CAACuc,IAAuBxc,MAAMC,OAAO,CAACwc,IAGrD,GACED,CAAkB,CAAC,EAAE,GAAKC,CAAe,CAAC,EAAE,EAC5CD,CAAkB,CAAC,EAAE,GAAKC,CAAe,CAAC,EAAE,CAE5C,CADA,MACO,CACT,MACK,GAAID,IAAuBC,EAChC,OAAO,EAIT,GAAIrc,CAAW,CAAC,CALmC,CAKjC,CAEhB,CAFkB,KAEX,CAACmc,CAAQ,CAAC,EAAE,CAGrB,GAAIA,CAAQ,CAAC,EAAE,CACb,CADe,MACR,EAKT,IAAMG,EAAmBnc,OAAOC,MAAM,CAACJ,CAAW,CAAC,EAAE,CAAC,CAAC,EAAE,CACnDuc,EAAgBpc,OAAOC,MAAM,CAAC+b,CAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,OACnD,CAAKG,GAAD,CAAsBC,GACnBnE,EAA4BkE,EAAkBC,EACvD,MAF2C,CAAhB,MAAuB,wTC/BlCrJ,qCAAAA,aAT8B,OASvC,SAASA,EACdsJ,CAAmB,CACnBC,CAAmB,EAEnB,IAAMC,EAAWzL,CAAAA,EAAAA,EAAAA,MAAAA,EAA4B,MACvC0L,EAAW1L,CAAAA,EAAAA,EAAAA,MAAAA,EAA4B,MAS7C,MAAO8B,CAAAA,EAAAA,EAAAA,WAAAA,EACL,IACE,GAAIC,SAAkB,CACpB,IAAM4J,EAAaF,EAAS1J,OAAO,CAC/B4J,IACFF,EAAS1J,MADK,CACE,CAAG,KACnB4J,KAEF,IAAMC,EAAaF,EAAS3J,OAAO,CAC/B6J,IACFF,EAAS3J,MADK,CACE,CAAG,KACnB6J,IAEJ,MACML,CADC,GAEHE,EADQ,OACQ,CAAGI,EAASN,EAAMxJ,EAAAA,EAEhCyJ,IACFE,EAAS3J,OAAO,CAAG8J,EAASL,EAAMzJ,EAAAA,CAGxC,EACA,CAACwJ,EAAMC,EAAK,CAEhB,CAEA,SAASK,EACPN,CAAgC,CAChCxJ,CAAiB,EAEjB,GAAoB,YAAhB,OAAOwJ,EAST,OADAA,EAAKxJ,OAAO,CAAGA,EACR,KACLwJ,EAAKxJ,OAAO,CAAG,IACjB,CAX8B,EAC9B,IAAM+J,EAAUP,EAAKxJ,SACrB,YAAmC,OAAxB+J,EACFA,EAEA,IAAMP,EAAK,KAEtB,CAMF,MANS,+UCpDOQ,qCAAAA,SAAAA,EACdje,CAAmB,CACnB/B,CAAwB,CACxBoC,CAAoC,EAEpC,IAAM6Q,EAAc7Q,EAAkBM,MAAM,EAAI,EAE1C,CAACF,EAAkBF,EAAQ,CAAGF,EAC9BgO,EAAW6B,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB3P,GAEhC4Q,EACJlT,EAAca,cAAc,CAAC4O,GAAG,CAACjN,GAE/B2Q,EAAkBpR,EAASlB,cAAc,CAAC4O,GAAG,CAACjN,GAE7C2Q,GAAmBA,IAAoBD,IAC1CC,EAAkB,IAAIrS,IAAIoS,GAC1BnR,EAASlB,MAF0D,QAE5C,CAAC+N,GAAG,CAACpM,EAAkB2Q,IAGhD,IAAMC,EAAyBF,MAAAA,EAAAA,KAAAA,EAAAA,EAAyBzD,GAAG,CAACW,GACxDiD,EAAiBF,EAAgB1D,GAAG,CAACW,GAGzC,GAAI6C,EAAa,CAEZI,GACAA,EAAelB,QAAQ,EACxBkB,GADA,CACmBD,GAEnBD,EAAgBvE,GAAG,CAACwB,EAAU,CAC5B+B,SAAU,GAFZ,EAGE1R,IAAK,KACLE,YAAa,KACbJ,KAAM,KACN8R,aAAc,KACdxR,eAAgB,IAAIC,IACpBJ,QAAS,KACTX,YAAa,CAAC,CAChB,GAEF,MACF,CAEA,GAAI,CAACsT,GAAkB,CAACD,EAAwB,CAE1C,GACFD,EAAgBvE,GAAG,CAACwB,EAAU,CAC5B+B,IAFiB,KAEP,KACV1R,IAAK,KACLE,YAAa,KACbJ,KAAM,KACN8R,aAAc,KACdxR,eAAgB,IAAIC,IACpBJ,QAAS,KACTX,YAAa,CAAC,CAChB,GAEF,MACF,CAeA,OAbIsT,IAAmBD,IACrBC,EAAiB,CACflB,SAAUkB,EAAelB,MAFkB,EAEV,CACjC1R,IAAK4S,EAAe5S,GAAG,CACvBE,YAAa0S,EAAe1S,WAAW,CACvCJ,KAAM8S,EAAe9S,IAAI,CACzB8R,aAAcgB,EAAehB,YAAY,CACzCxR,eAAgB,IAAIC,IAAIuS,EAAexS,cAAc,EACrDH,QAAS2S,EAAe3S,OAC1B,EACAyS,EAAgBvE,GAAG,CAACwB,EAAUiD,IAGzB2M,EACL3M,EACAD,EACAzQ,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyBP,GAE7B,aArFyC,WACJ,4UC4HxB6d,qCAAAA,OA/HuB,QACF,SACU,SACA,SAOV,SACJ,SACE,QAEK,SACC,SACY,WA+GrCA,EAPb,SAASC,CACoB,CAC3BC,CAAyB,EAEzB,IA5FyDlf,GA4FlDA,CACT,CAGsB,GAChBif,wBACAE,CAAqBA,2SCnHXC,qCAAAA,aAfkB,WACU,WACA,WAOV,WACF,UACF,WAEO,OAE9B,SAASA,EACdpf,CAA2B,CAC3BC,CAAyB,EAEzB,GAAM,CACJof,eAAgB,CAAEpgB,YAAU,CAAEuB,aAAc8e,CAAoB,CAAE,aAClExgB,CAAW,CACZ,CAAGmB,EAEEwK,EAAmB,CAAC,EAK1B,GAHAA,EAAQ7J,0BAA0B,EAAG,EAGX,UAAU,OAAzB3B,EACT,MAAO0Q,CAAAA,EAAAA,EAAAA,iBAAAA,EACL3P,EACAyK,EACAxL,EACAe,EAAMS,OAAO,CAACC,WAAW,EAI7B,IAAIqB,EAAc/B,EAAMb,IAAI,CACxB4Q,EAAe/P,EAAMhB,KAAK,CAE9B,IAAK,IAAMgR,KAAwB/Q,EAAY,CAC7C,GAAM,CAAE4S,YAAa1Q,CAAiB,CAAEhC,KAAMC,CAAS,CAAE,CACvD4Q,EAEIM,EAAUrF,CAAAA,EAAAA,EAAAA,2BAAAA,EACd,CACC,MAAO9J,EAAkB,CAC1BY,EACA3C,EACAY,EAAMQ,YAAY,EAQpB,GAAgB,MAAM,CAAlB8P,EACF,OAAOtQ,EAGT,GAAIma,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4BpY,EAAauO,GAC3C,MAAOX,CAAAA,EAAAA,EAAAA,iBAAAA,EACL3P,EACAyK,EACAzK,EAAMQ,YAAY,CAClBR,EAAMS,OAAO,CAACC,WAAW,EAI7B,IAAM6e,EAA2BD,EAC7Bjf,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBif,QAClBjc,CAEAkc,KACF9U,EAAQjK,YAAY,CAAG+e,CAAAA,EAGzB,GAJ8B,CAIxBvgB,EAAmBuR,CAAAA,EAAAA,EAAAA,oBAAAA,IACzB1R,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBC,EAAaiR,EAAc/Q,EAAOgR,GAElDvF,EAAQC,WAAW,CAAG4F,EACtB7F,EAAQzL,KAAK,CAAGA,EAEhB+Q,EAAe/Q,EACf+C,EAAcuO,CAChB,CAEA,MAAO/F,CAAAA,EAAAA,EAAAA,aAAAA,EAAcvK,EAAOyK,EAC9B,iVCnFgB6H,qCAAAA,aALqB,OAK9B,SAASA,EACdxR,CAAmB,CACnB/B,CAAwB,CACxB0R,CAA8B,EAG9B,IAAK,IAAMhN,KAAOgN,CAAW,CAAC,EAAE,CAAE,CAChC,IAAMM,EAA0BN,CAAW,CAAC,EAAE,CAAChN,EAAI,CAAC,EAAE,CAChD0L,EAAW6B,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBD,GAChCyO,EACJzgB,EAAca,cAAc,CAAC4O,GAAG,CAAC/K,GACnC,GAAI+b,EAAiC,CACnC,IAAIC,EAAyB,IAAI5f,IAAI2f,GACrCC,EAAuBhR,MAAM,CAACU,GAC9BrO,EAASlB,cAAc,CAAC+N,GAAG,CAAClK,EAAKgc,EACnC,CACF,CACF,mUCJgBC,qCAAAA,aApBoB,UACF,WACU,WACA,WAOV,WACJ,WAEgB,WACT,WACC,WACY,WACF,OAGzC,SAASA,EACd1f,CAA2B,CAC3BC,CAAqB,EAErB,GAAM,CAAE8G,QAAM,CAAE,CAAG9G,EACbwK,EAAmB,CAAC,EACpBrK,EAAOJ,EAAMQ,YAAY,CAE3BuB,EAAc/B,EAAMb,IAAI,GAEpByB,0BAA0B,EAAG,EAErC,IAAM5B,EAAmBuR,CAAAA,EAAAA,EAAAA,oBAAAA,IAInBoP,EAAiBC,GAAAA,EAAAA,iCAAAA,EAAkC5f,EAAMb,IAAI,EAInEH,EAAMkS,QAAQ,CAAG2O,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB,IAAIhX,IAAIzI,EAAM2G,GAAS,CAC1D3F,kBAAmB,CACjBW,CAAW,CAAC,EAAE,CACdA,CAAW,CAAC,EAAE,CACdA,CAAW,CAAC,EAAE,CACd,UACD,CACDf,QAAS2e,EAAiB3f,EAAMgB,OAAO,CAAG,IAC5C,GAEA,IAAMlC,EAAcghB,KAAKC,GAAG,GAC5B,OAAO/gB,EAAMkS,QAAQ,CAACzJ,IAAI,CACxB,aAAO,YAAExI,CAAU,CAAEuB,aAAc8e,CAAoB,CAAE,GAEvD,GAA0B,UAAtB,OAAOrgB,EACT,MAAO0Q,GAAAA,EAAAA,iBAAAA,EACL3P,EACAyK,EACAxL,EACAe,EAAMS,OAAO,CAACC,WAAW,EAO7B,IAAK,IAAMsP,KAFXhR,EAAMkS,QAAQ,CAAG,KAEkBjS,GAAY,CAC7C,GAAM,CACJE,KAAMC,CAAS,CACfC,SAAUqR,CAAiB,MAC3BpR,CAAI,cACJC,CAAY,CACb,CAAGyQ,EAEJ,GAAI,CAACzQ,EAGH,OADAyO,KAFiB,GAETgS,GAAG,CAAC,kBACLhgB,EAGT,IAAMsQ,EAAUrF,GAAAA,EAAAA,2BAAAA,EACd,CACC,GAAG,CACJlJ,EACA3C,EACAY,EAAMQ,YAAY,EAGpB,GAAgB,MAAM,CAAlB8P,EACF,MAAOZ,CAAAA,EAAAA,EAAAA,qBAAAA,EAAsB1P,EAAOC,EAAQb,GAG9C,GAAI+a,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4BpY,EAAauO,GAC3C,MAAOX,CAD8C,EAC9CA,EAAAA,iBAAAA,EACL3P,EACAyK,EACArK,EACAJ,EAAMS,OAAO,CAACC,WAAW,EAI7B,IAAM6e,EAA2BD,EAC7Bjf,GAAAA,EAAAA,iBAAAA,EAAkBif,QAClBjc,EAOJ,GALIic,IACF7U,EAAQjK,YAAY,CAAG+e,CAAAA,EADC,OAKtB7O,EAA4B,CAC9B,IAAMlR,EAAMkR,CAAiB,CAAC,EAAE,CAC1BjR,EAAUiR,CAAiB,CAAC,EAAE,CACpC1R,EAAMQ,GAAG,CAAGA,EACZR,EAAMU,WAAW,CAAG,KACpBV,EAAMS,OAAO,CAAGA,EAChBE,CAAAA,EAAAA,EAAAA,6BAAAA,EACEb,EACAE,EACA,OAEAI,EACAsR,EACApR,OACA+D,GAKAoH,EAAQ1J,aAAa,CAAG,IAAIlB,GAEhC,CAEA,MAAMogB,CAAAA,EAAAA,EAAAA,+BAAAA,EAAgC,aACpCnhB,QACAkB,EACAkgB,YAAa5P,EACb6P,aAAcnhB,iBACd2gB,EACAnf,aAAciK,EAAQjK,YAAY,EAAIR,EAAMQ,YAC9C,GAEAiK,EAAQzL,KAAK,CAAGA,EAChByL,EAAQC,WAAW,CAAG4F,EAEtBvO,EAAcuO,CAChB,CAEA,MAAO/F,CAAAA,EAAAA,EAAAA,aAAAA,EAAcvK,EAAOyK,EAC9B,EACA,IAAMzK,EAEV,GAtIsC,+OClBtC,gBACA,8CACA,kEAGA,QACA,iLC8BgB2P,iBAAiB,mBAAjBA,GAsIAyQ,eAAe,mBAAxB,SAASA,EACdpgB,CAA2B,CAC3BC,CAAsB,EAEtB,GAAM,KAAEE,CAAG,eAAE8I,CAAa,CAAEP,cAAY,cAAEC,CAAY,eAAEU,CAAa,CAAE,CACrEpJ,EACIwK,EAAmB,CAAC,EACpB,CAAE8G,MAAI,CAAE,CAAGpR,EACXC,EAAOC,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBF,GACzBO,EAA+B,SAAjBgI,EAOpB,GALAoV,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmB9d,EAAMe,aAAa,EAEtC0J,EAAQ7J,0BAA0B,EAAG,EACrC6J,EAAQ/J,WAAW,CAAGA,EAElBuI,EACF,OAAO0G,EAAkB3P,EAAOyK,EAAStK,EAAIkgB,QAAQ,GAAI3f,GAK3D,GAAI4f,SAASC,cAAc,CAAC,wBAC1B,CADmD,MAC5C5Q,EAAkB3P,EAAOyK,EAASrK,EAAMM,GAsBjD,IAAM8f,EAAiBxC,CAAAA,EAAAA,EAAAA,6BAAAA,EAA8B,KACnD7d,EACAa,QAAShB,EAAMgB,OAAO,CACtB7B,KAAMa,EAAMb,IAAI,CAChB4B,cAAef,EAAMe,aAAa,eAClCsI,CACF,GACM,sBAAE6F,CAAoB,MAAEuR,CAAI,CAAE,CAAGD,EAIvC,OAFA5C,EAAAA,aAAa,CAACvY,IAAI,CAACob,GAEZA,EAAKhZ,IAAI,CACd,OAAC,YAAExI,CAAU,CAAEuB,aAAc8e,CAAoB,WAAEoB,CAAS,CAAE,GACtD5hB,EAAcghB,KAAKC,GAAG,GAExBY,GAAc,EAQlB,GANKH,EAAeI,YAAY,EAAE,CAEhCJ,EAAeI,YAAY,CAAG9hB,EAC9B6hB,GAAc,GAGZH,EAAeK,OAAO,CAAE,CAC1B,IAAM3b,EAAS2K,CAAAA,EAAAA,EAAAA,0BAAAA,EACb/Q,EACAkB,EACAf,EACAkB,EACAsK,SAMF,CAAe,IAAXvF,EACKkb,CADa,CACGpgB,EAAO,CAAE,GAAGC,CAAM,CAAEoJ,eAAe,CAAM,GAG3DnE,CACT,CAGA,GAAI,UAAgC,OAAzBjG,EACT,OAAO0Q,EAAkB3P,EAAOyK,EAASxL,EAAYyB,GAGvD,IAAMogB,EAAsBxB,EACxBjf,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBif,GAClBlf,EASJ,GANE,CAMEyK,EALF7K,EAAMQ,YAAY,CAACgC,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,GACjCse,EAAoBte,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAUtC,OALAiI,EAAQI,cAAc,EAAG,EACzBJ,EAAQjK,YAAY,CAAGsgB,EACvBrW,EAAQ9B,YAAY,CAAGA,EACvB8B,EAAQK,YAAY,CAAGyG,EACvB9G,EAAQG,kBAAkB,CAAG,EAAE,CACxBL,GAAAA,EAAAA,aAAAA,EAAcvK,EAAOyK,GAG9B,IAAI1I,EAAc/B,EAAMb,IAAI,CACxB4Q,EAAe/P,EAAMhB,KAAK,CAC1B4L,EAA0C,EAAE,CAChD,IAAK,IAAMoF,KAAwB/Q,EAAY,CAC7C,GAAM,CACJmR,cAAejP,CAAiB,UAChC9B,CAAQ,MACRC,CAAI,CACJyhB,eAAa,CACbxhB,cAAY,CACb,CAAGyQ,EACA5Q,EAAY4Q,EAAqB7Q,IAAI,CAGnCkR,EAAoC,CAAC,MAAOlP,EAAkB,CAGhEmP,EAAUrF,CAAAA,EAAAA,EAAAA,2BAAAA,EACZ,EAEAlJ,EACA3C,EACAgB,GAeF,GAVgB,MAAM,CAAlBkQ,GAToB,CAUtBA,EAAUrF,CAAAA,EAAAA,EAAAA,2BAAAA,EACR,EAEAiE,EACA9P,EACAgB,EAAAA,EAIY,OAAZkQ,EAAkB,CACpB,EATwB,CAiBtBjR,CAPA,EAQAE,GACAmhB,EACA,CACA,IAAMzb,EAAO+R,CAAAA,EAAAA,EAAAA,kBAAAA,EACXlY,EACAiR,EACAhO,EACA3C,EACAC,EACAC,EACAyhB,GACA,EAlBsD,GAsBxD,GAAa,OAAT9b,EAAe,CACjB,GAAmB,MAAM,CAArBA,EAAK+S,KAAK,CAGZ,OAAOrI,EAAkB3P,EAAOyK,EAASrK,EAAMM,GAOjD4P,EAD8CrL,EAAK+S,KAAK,CAC9CgJ,IAEJlgB,EAAWmE,EAAKgT,IAAI,MACH,MAGrBxN,EAAQzL,KAAK,CAAG8B,CAAAA,EAElB,IAAMoX,EAAqBjT,EAAKiT,kBAAkB,CAClD,GAAIA,SAA6B,CAc/B,IAAM+I,EAAiBpB,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB1f,EAAK,CAC9CiB,kBAAmB8W,EACnBlX,QAAShB,EAAMgB,OAAO,GAGxB+V,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB9R,EAAMgc,EAKhC,CAIF,MAJS,CAIF,CAKK7hB,CAEd,KAAO,CASL,GAAI+a,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4BpY,EAAauO,GAC3C,OADqD,EAC5BtQ,EAAOyK,EAASrK,EAAMM,GAGjD,IAAM1B,EAAmBuR,CAAAA,EAAAA,EAAAA,oBAAAA,IACrBT,GAAU,EAwDd,IAAK,IAAMoR,KArDTV,EAAe1I,MAAM,GAAKqJ,EAAAA,wBAAwB,CAACC,KAAK,EACvDT,EAAD,EAkBU9hB,CAAAA,EAAAA,EAAAA,IAjBV,WAiBUA,EACRC,EACAiR,EACA/Q,EACAgR,EACAwQ,IAfF1Q,EA7Vd,SAASuR,CACY,CACnBtR,CAAuB,CACvB5O,CAAoC,CACpC/B,CAA4B,EAE5B,IAAIkiB,GAAe,EAWnB,IAAK,IAAMtW,KATXlK,EAAStB,GAAG,CAAGuQ,EAAavQ,GASD+hB,CAR3BzgB,EAASpB,WAAW,CAAGqQ,EAAarQ,EAQW,SARA,CAC/CoB,EAASrB,OAAO,CAAGsQ,EAAatQ,OAAO,CACvCqB,EAASlB,cAAc,CAAG,IAAIC,IAAIkQ,EAAanQ,cAAc,EAElC4hB,EAA0BpiB,GAAWqiB,GAAG,CACjE,GAAa,IAAItgB,KAAsBE,EAAQ,GAI/C0d,CAAAA,EAAAA,EAAAA,gCAAAA,EAAiCje,EAAUiP,EAAc/E,GAEzDsW,GAAe,EAGjB,OAAOA,CACT,EAsUgBtiB,EACA+Q,EACA5O,EACA/B,GAIFohB,EAAeI,YAAY,CAAG9hB,GAWXoC,CAAAA,EAAAA,EAAAA,kBAAAA,EACnB,EAEAa,IAKA/C,EAAMQ,GAAG,CAAGuQ,EAAavQ,GAAG,CAC5BR,EAAMU,EARgB,SAQL,CAAGqQ,EAAarQ,WAAW,CAE5Cue,CAAAA,EAAAA,EAAAA,qCAAAA,EACEjf,EACA+Q,EACA5O,GAGFsJ,EAAQzL,KAAK,CAAGA,GACP8Q,IACTrF,EAAQzL,GADU,EACL,CAAGA,EAGhB+Q,EAAe/Q,GAGQwiB,EAA0BpiB,IAAY,CAC7D,IAAMsiB,EAAwB,IACzBvgB,KACA+f,EACJ,CAGCQ,CAAqB,CAACA,EAAsBjgB,MAAM,CAAG,EAAE,GACvDwB,EAAAA,mBAAmB,EACnB,EACmBO,IAAI,CAACke,EAE5B,CACF,CAEA3f,EAAcuO,CAChB,CACF,CAQA,OANA7F,EAAQC,WAAW,CAAG3I,EACtB0I,EAAQjK,YAAY,CAAGsgB,EACvBrW,EAAQG,kBAAkB,CAAGA,EAC7BH,EAAQK,YAAY,CAAGyG,EACvB9G,EAAQ9B,YAAY,CAAGA,EAEhB4B,GAAAA,EAAAA,aAAAA,EAAcvK,EAAOyK,EAC9B,EACA,IAAMzK,EAEV,aA7eoC,UACF,WACoB,WACV,WACT,UACS,WAOrC,WACuB,WACE,UACF,WACO,WACD,WACwB,WAIrD,WAC0C,WACN,OAOpC,SAAS2P,EACd3P,CAA2B,CAC3ByK,CAAgB,CAChBtK,CAAW,CACXO,CAAoB,EAOpB,OALA+J,EAAQ9J,aAAa,EAAG,EACxB8J,EAAQjK,YAAY,CAAGL,EACvBsK,EAAQ/J,WAAW,CAAGA,EACtB+J,EAAQG,kBAAkB,MAAGvH,EAEtBkH,CAAAA,EAAAA,EAAAA,aAAAA,EAAcvK,EAAOyK,EAC9B,CAEA,SAAS+W,EACPG,CAAoC,EAEpC,IAAM9e,EAAgC,EAAE,CAClC,CAACxB,EAASzB,EAAe,CAAG+hB,EAElC,GAA2C,GAAG,CAA1Czf,OAAO0O,IAAI,CAAChR,GAAgB6B,MAAM,CACpC,MAAO,CAAC,CAACJ,EAAQ,CAAC,CAGpB,IAAK,GAAM,CAACE,EAAkBU,EAAc,GAAIC,OAAOyB,OAAO,CAC5D/D,GAEA,IAAK,IAAMgiB,KAAgBJ,EAA0Bvf,GAEnC,IAAI,CAAhBZ,EACFwB,EAASW,GAHwD,CAGpD,CAAC,CAACjC,KAAqBqgB,EAAa,EAEjD/e,EAASW,IAAI,CAAC,CAACnC,EAASE,KAAqBqgB,EAAa,EAKhE,OAAO/e,CACT,GAxCO,oXCiEWgf,mBAAmB,mBAAnBA,GAUAvS,gBAAgB,mBAAhBA,GAxCLZ,kBAAkB,mBAAlBA,GAkBAU,cAAc,mBAAdA,GApCAH,sBAAsB,mBAAtBA,GAhBA6S,QAAQ,mBAARA,GAPArY,QAAQ,mBAARA,GAkDAsY,sBAAsB,mBAAtBA,GApCAC,qBAAqB,mBAArBA,GAkBAC,oBAAoB,mBAApBA,KAtCb,IAAMC,EAAkB,KACtB,MAAM,qBAEL,CAFK,MACJ,sEADI,+DAEN,EACF,EAEazY,EAKPyY,EAEOJ,EAKPI,EAEOF,EAOPE,CApBJxY,CAsBWuF,EAOPiT,CAtBJxY,CAwBWuY,CA/B4B,CAsCnCC,EArCA,CAEC,CA4CDA,EAvCA,CAEC,CA8CDA,EAEO9S,EAKP8S,EAjDJxY,IAwDgBmY,CA/ChBnY,CA+CgBmY,CAtChBnY,EAjBI,CAIC,CAIkC,EASA,CARnC,CAIC,CAKD,CAiBJA,CARI,CAiBmC,GACnC,CAVmC,GACnC,CAIC,UAeWmY,sEAAAA,OAUAvS,EAAAA,SAAAA,CAAAA,OAAAA,EAKf,SALeA,CAKf,aAIA,yBAKA,+BAdeA,sXC+RLiL,oBAAoB,mBAApBA,GAGA4H,mBAAmB,mBAAnBA,GAnIGC,8BAA8B,mBAA9BA,GA9GApE,6BAA6B,mBAA7BA,GA+NAF,kBAAkB,mBAAlBA,aA1XT,UAMA,WACuB,OAmB9B,SAASuE,EACPliB,CAAQ,CACRmiB,CAA4B,CAC5BC,CAAsB,EAKtB,IAAIC,EAAkBriB,EAAIc,QAAQ,OAclC,CAPIqhB,IAIFE,GAAmBriB,EAAIiJ,MAAAA,EAGrBmZ,GACM,CARe,EAQbA,EADA,IACyCC,EAG9CA,CAHcC,CAMvB,SAASC,EACPviB,CAAQ,CACR6J,CAA8B,CAC9BhJ,CAAuB,EAEvB,OAAOqhB,EAA2BliB,EAAK6J,IAASC,EAAAA,YAAY,CAACC,IAAI,CAAElJ,EACrE,CA8FO,SAASgd,EAA8B,CAW7C,EAX6C,QAC5C7d,CAAG,CACHa,SAAO,MACP7B,CAAI,eACJ4B,CAAa,MACbiJ,CAAI,eACJX,GAAgB,CAAI,CAKrB,CAX6C,EAYtCsZ,EAxGR,SACExiB,CAAQ,CACR6J,CAA2C,CAC3ChJ,CAAsB,CACtBD,CAA8C,CAC9CsI,CAAsB,EAKtB,IAAK,IAAMuZ,KARX5Y,KAAAA,IAAAA,IAAAA,EAAqBC,EAAAA,YAAY,CAAC4Y,SAAAA,EAQP,CAAC7hB,EAAS,KAAK,EAAE,CAC1C,IAAM8hB,EAAqBT,EACzBliB,GACA,EACAyiB,GAEIG,EAAwBV,EAC5BliB,GACA,EACAyiB,GAIII,EAAgB7iB,EAAIiJ,MAAM,CAC5B0Z,EACAC,EAEEE,EAAgBliB,EAAcyN,GAAG,CAACwU,GACxC,GAAIC,GAAiB5Z,EAAe,CAMlC,GAHE4Z,CAGEC,CAHY/iB,GAAG,CAACc,MAGL,EAHa,GAAKd,EAAIc,QAAQ,EAC3CgiB,EAAc9iB,GAAG,CAACiJ,MAAM,GAAKjJ,EAAIiJ,MAAM,CAGvC,MAAO,CACL,GAAG6Z,CAAa,CAChBpC,SAAS,CACX,EAGF,OAAOoC,CACT,CAMA,IAAME,EAAqBpiB,EAAcyN,GAAG,CAACuU,GAC7C,GACErZ,CAAAA,EAEAvJ,CAFoB,CAEhBiJ,MAAM,EACVY,IAASC,EAAAA,YAAY,CAACC,IAAI,EAC1BiZ,GAGA,CAACA,EAAmB1f,GAAG,CAAC0N,QAAQ,CAACsR,GAFjC,EAIA,MAAO,CAAE,GAAGU,CAAkB,CAAEtC,SAAS,CAAK,CAElD,CAOA,GAVI,CAWFnX,GAAoB,EACXO,YAAY,CAACC,IAAI,EAC1Bb,EAEA,KAAK,IAAM+Z,IADX,CACyBriB,EAAcoB,MAAM,EAlBqC,CAkBjC,GAE7CihB,EAAWjjB,GAAG,CAACc,QAAQ,GAAKd,EAAIc,QAAQ,EAGxC,CAACmiB,CAFD,CAEY3f,GAAG,CAAC0N,QAAQ,CAACsR,KAEzB,MAAO,CAAE,GAAGW,CAAU,CAAEvC,SAAS,CAAK,CAE1C,CAIJ,EAmBI1gB,CA1BI,CA2BJ6J,EACAhJ,EACAD,EACAsI,UAGF,GAEEsZ,EAAmB7K,MAAM,CAAGuL,EAA4BV,GAKtDA,EAAmB3Y,CAPC,GAOG,GAAKC,EAAAA,YAAY,CAGd,IAHmB,EAC7CD,IAASC,EAAAA,YAAY,CAACC,IAAI,EAM1ByY,EAAmBlC,IAAI,CAAChZ,IAAI,CAAC,IAQ3B,GAAI,CAAC6b,CANH3hB,MAAMC,OAAO,CAAC2hB,CAMK,CANYtkB,UAAU,GACzCskB,EAAiBtkB,UAAU,CAACkE,IAAI,CAAC,GAExBlE,EAAWM,YAAY,EAAIN,SAAWI,QAAQ,CACvD,EAGA,OAAOmkB,EAAwB,MAC7BrkB,MACAgB,EACAa,wBACAD,EAIAiJ,KAAMA,MAAAA,EAAAA,EAAQC,EAAAA,YAAY,CAAC4Y,SAAS,EAG1C,GAKE7Y,GAAQ2Y,EAAmB3Y,IAAI,GAAKC,EAAAA,YAAY,CAAC4Y,SAAS,EAAE,CAC9DF,EAAmB3Y,IAAI,CAAGA,CAAAA,EAIrB2Y,GAIFa,EAAwB,MAC7BrkB,MACAgB,EACAa,wBACAD,EACAiJ,KAAMA,GAAQC,EAAAA,YAAY,CAAC4Y,SAAS,EAExC,CAmCO,SAAST,EAA+B,CAW9C,EAX8C,YAC7CphB,CAAO,MACP7B,CAAI,eACJ4B,CAAa,KACbZ,CAAG,MACHsgB,CAAI,MACJzW,CAAI,CAKL,CAX8C,EAevCyZ,EAAmBhD,EAAKiD,kBAAkB,CAC5ChB,EAAuBviB,EAAK6J,EAAMhJ,GAClC0hB,EAAuBviB,EAAK6J,GAE1B9K,EAAgB,CACpBgQ,qBAAsB/P,EACtBshB,KAAM3b,QAAQC,OAAO,CAAC0b,QACtBzW,EACA2Z,aAAc7D,KAAKC,GAAG,GACtBa,aAAcd,KAAKC,GAAG,GACtB6D,UAAW,CAAC,EACZngB,IAAKggB,EACL3L,OAAQqJ,EAAAA,wBAAwB,CAAC0C,KAAK,KACtC1jB,CACF,EAIA,OAFAY,EAAc4M,GAAG,CAAC8V,EAAkBvkB,GAE7BA,CACT,CAKA,SAASskB,EAAwB,CAShC,EATgC,QAC/BrjB,CAAG,MACH6J,CAAI,MACJ7K,CAAI,SACJ6B,CAAO,eACPD,CAAa,CAId,CATgC,EAUzB0iB,EAAmBf,EAAuBviB,EAAK6J,GAI/CyW,EAAO7C,EAAAA,aAAa,CAACnZ,OAAO,CAAC,IACjCob,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB1f,EAAK,CACvBiB,kBAAmBjC,UACnB6B,EACA8iB,aAAc9Z,CAChB,GAAGvC,IAAI,CAAC,IAIN,IAAIsc,EAeJ,GAbIR,EAAiBG,kBAAkB,EAAE,CAEvCK,EA/FR,SAASC,CAQR,EARyC,QACxC7jB,CAAG,SACHa,CAAO,eACPD,CAAa,kBACbkjB,CAAgB,CAIjB,CARyC,EASlCtB,EAAqB5hB,EAAcyN,GAAG,CAACyV,GAC7C,GAAI,CAACtB,EAEH,OAGF,IAAMoB,EAAcrB,EAClBviB,EACAwiB,CAPuB,CAOJ3Y,IAAI,CACvBhJ,GAKF,OAHAD,EAAc4M,GAAG,CAACoW,EAAa,CAAE,GAAGpB,CAAkB,CAAElf,IAAKsgB,CAAY,GACzEhjB,EAAc0N,MAAM,CAACwV,GAEdF,CACT,EAuEuD,KAC7C5jB,EACA8jB,iBAAkBR,UAClBziB,gBACAD,CACF,IAMEwiB,EAAiBW,WAAW,CAAE,CAChC,IAAMvB,EAAqB5hB,EAAcyN,GAAG,CAE1CuV,MADA,EACAA,EAAeN,GAEbd,IACFA,EAAmB3Y,IAAI,CAAGC,EAAAA,OADJ,KACgB,CAACC,IAAI,CACR,CAAC,GAAG,CAAnCqZ,EAAiBK,SAAS,GAG5BjB,EAAmBiB,SAAS,CAAGL,EAAiBK,SAAAA,EAGtD,CAEA,OAAOL,CACT,IAGIrkB,EAAgB,CACpBgQ,qBAAsB/P,EACtBshB,OACAzW,OACA2Z,aAAc7D,KAAKC,GAAG,GACtBa,aAAc,KACdgD,UAAW,CAAC,EACZngB,IAAKggB,EACL3L,OAAQqJ,EAAAA,wBAAwB,CAAC0C,KAAK,KACtC1jB,CACF,EAIA,OAFAY,EAAc4M,GAAG,CAAC8V,EAAkBvkB,GAE7BA,CACT,CAEO,SAAS4e,EACd/c,CAAoD,EAEpD,IAAK,GAAM,CAACX,EAAM+jB,EAAmB,GAAIpjB,EAErCsiB,EAA4Bc,KAC5BhD,EAAAA,GAHoD,qBAG5B,CAACiD,OAAO,EAChC,EACc3V,MAAM,CAACrO,EAG3B,CAIO,IAAMma,EACkD,IAA7D8J,OAAO3a,GAAkD,EAE9CyY,EACiD,IAA5DkC,OAAO3a,KAAiD,EAE1D,SAAS2Z,EAA4B,CAKhB,EALgB,SACnCrZ,CAAI,CACJ2Z,cAAY,cACZ/C,CAAY,WACZgD,CAAS,CACU,CALgB,SAMnC,CAAmB,GAAG,GASb9D,KAAKC,GAAG,GAAK4D,EAAeC,EAC/BzC,EAAAA,wBAAwB,CAAC0C,KAAK,CAC9B1C,EAAAA,wBAAwB,CAACC,KAAK,CAIhCtB,KAAKC,GAAG,GAAMa,CAAAA,MAAAA,EAAAA,EAAgB+C,CAAAA,CAAAA,CAAW,EACpC/C,EACHO,EAAAA,gBAFkE,QAE1C,CAACmD,QAAQ,CACjCnD,EAAAA,wBAAwB,CAAC0C,KAAK,CAMhC7Z,IAASC,EAAAA,YAAY,CAACuK,IAAI,EAAE,KACrBuL,GAAG,GAAK4D,EAAexB,EACvBhB,EAAAA,iBAD4C,OACpB,CAACC,KAAK,CAKrCpX,IAASC,EAAAA,YAAY,CAACC,IAAI,EAAE,KACrB6V,GAAG,GAAK4D,EAAexB,EACvBhB,EAAAA,iBAD4C,OACpB,CAACmD,QAAQ,CAIrCnD,EAAAA,wBAAwB,CAACiD,OAAO,oUCzbzBG,qCAAAA,aAFqB,OAE9B,SAASA,EACdvlB,CAAgB,CAChBY,CAAoC,EAEpC,OAGF,SAAS4kB,EACPxlB,CAAgB,CAChBY,CAAoC,CACpC6kB,CAAiB,EAGjB,GAD0D,CACtDC,GADexiB,OAAO0O,EACV,EADc,CAAChR,GAAgB6B,MAAM,CAGnD,MAAO,CAACzC,EAAOylB,EAAU,CAK3B,GAAI7kB,EAAe2D,QAAQ,CAAE,CAC3B,GAAM,CAAClC,EAASsjB,EAAoB,CAAG/kB,EAAe2D,QAAQ,CACxD2O,EAAkBlT,EAAMY,cAAc,CAAC4O,GAAG,CAAC,YACjD,GAAI0D,EAAiB,CACnB,IAAM/C,EAAW6B,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB3P,GAChC2a,EAAY9J,EAAgB1D,GAAG,CAACW,GACtC,GAAI6M,EAAW,CACb,IAAMxW,EAAOgf,EACXxI,EACA2I,EACAF,EAAY,IAAMtV,GAEpB,GAAI3J,EAAM,OAAOA,CACnB,CACF,CACF,CAGA,IAAK,IAAM/B,KAAO7D,EAAgB,CAChC,GAAY,aAAR6D,EAAoB,SAExB,CAFiC,EAE3B,CAACpC,EAASsjB,EAAoB,CAAG/kB,CAAc,CAAC6D,EAAI,CACpDyO,EAAkBlT,EAAMY,OAH2B,OAGb,CAAC4O,GAAG,CAAC/K,GACjD,GAAI,CAACyO,EACH,SAGF,IAAM/C,EAJgB,CAIL6B,EAAAA,EAAAA,oBAAAA,EAAqB3P,GAEhC2a,EAAY9J,EAAgB1D,GAAG,CAACW,GACtC,GAAI,CAAC6M,EACH,SADc,IAIVxW,EAAOgf,EACXxI,EACA2I,EACAF,EAAY,IAAMtV,GAEpB,GAAI3J,EACF,IADQ,GACDA,CAEX,CAEA,OAAO,IACT,EA7D6BxG,EAAOY,EAAgB,GACpD,kVCEgBD,qCAAAA,SAAAA,EACdb,CAAmB,CACnBgC,CAAmB,CACnB/B,CAAoC,CACpC0R,CAA8B,CAC9BC,CAA2C,CAC3CpR,CAAqB,CACrBJ,CAA6C,EAG7C,GAD6D,CACzDyR,GADkBzO,OAAO0O,IAAI,CAACH,CAAW,CAAC,EAAE,EAAEhP,MAAM,CACrC,CACjBX,EAASxB,IAAI,CAAGA,EAChB,MACF,CAEA,IAAK,IAAMmE,KAAOgN,CAAW,CAAC,EAAE,CAAE,CAChC,IAgGII,EAhGEC,EAAqBL,CAAW,CAAC,EAAE,CAAChN,EAAI,CACxCsN,EAA0BD,CAAkB,CAAC,EAAE,CAC/C3B,EAAW6B,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBD,GAYhCE,EACJP,UAA8BA,KAA8BrN,KAAb,CAAC,EAAE,CAACI,EAAI,CACnDiN,CAAiB,CAAC,EAAE,CAACjN,EAAI,CACzB,KACN,GAAI1E,EAAe,CACjB,IAAMygB,EACJzgB,EAAca,cAAc,CAAC4O,GAAG,CAAC/K,GACnC,GAAI+b,EAAiC,CACnC,IAMI3O,EANE+T,EACJ1lB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAe8K,IAAAA,IAAS,QACxB9K,EAAc4Y,MAAM,GAAKqJ,EAAAA,wBAAwB,CAACmD,QAAQ,CAExD7E,EAAyB,IAAI5f,IAAI2f,GAC/B1N,EAAoB2N,EAAuBjR,GAAG,CAACW,GAMnD0B,EAJuB,MAAM,CAA3BI,EAIa,CACbC,SAAU,KACV1R,IAJeyR,CAAgB,CAAC,EAAE,CAUlCvR,YAAa,KACbJ,KAAM,KACN8R,aAAc,KACd3R,QAZcwR,CAAgB,CAAC,EAAE,CAajCrR,eAAgB,IAAIC,IAAIiS,MAAAA,EAAAA,KAAAA,EAAAA,EAAmBlS,cAAc,EACzDd,aACF,EACS8lB,GAAuB9S,EAGjB,CACbZ,SAAUY,EAAkBZ,KAJqB,GAIb,CACpC1R,IAAKsS,EAAkBtS,GAAG,CAI1BE,YAAaoS,EAAkBpS,WAAW,CAC1CJ,KAAMwS,EAAkBxS,IAAI,CAC5B8R,aAAcU,EAAkBV,YAAY,CAC5CxR,eAAgB,IAAIC,IAAIiS,EAAkBlS,cAAc,EACxDH,QAASqS,EAAkBrS,OAAO,EAKrB,CACbyR,SAAU,KACV1R,IAAK,KACLE,YAAa,KACbJ,KAAM,KACN8R,aAAc,KACdxR,eAAgB,IAAIC,IAAIiS,MAAAA,EAAAA,KAAAA,EAAAA,EAAmBlS,cAAc,EACzDH,QAAS,KACTX,aACF,EAIF2gB,EAAuB9R,GAAG,CAACwB,EAAU0B,GAErClR,EACEb,EACA+R,EACAiB,EACAhB,EACAG,GAAsC,KACtC3R,EACAJ,GAGF4B,EAASlB,IALYqR,UAKE,CAACtD,GAAG,CAAClK,EAAKgc,GACjC,QACF,CACF,CAGA,GAAyB,OAArBxO,EAA2B,CAE7B,IAAM4T,EAAW5T,CAAgB,CAAC,EAAE,CAC9BxR,EAAUwR,CAAgB,CAAC,EAAE,CACnCJ,EAAe,CACbK,SAAU,KACV1R,IAAKqlB,EACLnlB,YAAa,KACbJ,KAAM,KACN8R,aAAc,KACdxR,eAAgB,IAAIC,YACpBJ,cACAX,CACF,CACF,MAGE+R,CAHK,CAGU,CACbK,SAAU,KACV1R,IAAK,KACLE,YAAa,KACbJ,KAAM,KACN8R,aAAc,KACdxR,eAAgB,IAAIC,IACpBJ,QAAS,iBACTX,CACF,EAGF,IAAMuS,EAAyBvQ,EAASlB,cAAc,CAAC4O,GAAG,CAAC/K,GACvD4N,EACFA,EAAuB1D,GAAG,CAACwB,EAAU0B,GAErC/P,EAASlB,SAHiB,KAGH,CAAC+N,GAAG,CAAClK,EAAK,IAAI5D,IAAI,CAAC,CAACsP,EAAU0B,EAAa,CAAC,GAGrElR,EACEb,EACA+R,OACAxN,EACAyN,EACAG,EACA3R,EACAJ,EAEJ,CACF,aArKqC,WAI9B,6OCiBA,SAAS4lB,EACdC,CAAU,EAEV,IAAMC,EAAWC,SAASF,EAAGriB,KAAK,CAAC,EAAG,GAAI,IAEpCwiB,EAAWF,GAAY,EAAK,GAE5BG,EAAWxjB,MAAM,GAEvB,IAAK,IAAI2D,EAAQ,EAAGA,EAAQ,EAAGA,IAAS,CAEtC,IAAM8f,EAAOF,GADO,EAAI5f,EACe,EACvC6f,CAAQ,CADgBE,EACT,CAAW,IAARD,CACpB,CAEA,MAAO,CACLve,KAAMye,IAZSN,GAAY,EAAK,GAYV,YAAc,gBACpCG,SAAUA,EAQVI,YAAaC,GAnBER,CAAW,IAoB5B,CACF,CAMO,SAASS,EACdC,CAAe,CACfC,CAAyB,EAEzB,IAAMC,EAAe,MAAUF,EAAKjkB,MAAM,EAE1C,IAAK,IAAI6D,EAAQ,EAAGA,EAAQogB,EAAKjkB,MAAM,CAAE6D,KAEpCA,EAAQ,CAFqC,EAEhCqgB,EAAKR,QAAQ,CAAC7f,EAAM,EAGjCA,EAFD,CAEU,GAAKqgB,EAAKJ,WAAAA,EACpB,EACAK,CAAY,CAACtgB,EAAM,CAAGogB,CAAI,CAACpgB,EAAAA,EAI/B,OAAOsgB,CACT,gCAT8E,wGA1C9Dd,gCAAgC,mBAAhCA,GAiCAW,cAAc,mBAAdA,6JC8DAla,wCAAwC,mBAAxCA,SAAAA,EACdpM,CAAuB,CACvB+L,CAAY,EAEZ,GAAM,CAAC7J,EAASzB,GAAkBimB,EAAc,CAAG1mB,EAOnD,IAAK,IAAMsE,KALPpC,EAAQ8P,QAAQ,CAAC5O,EAAAA,gBAAgB,GAAuB,WAAW,CAA7BsjB,IACxC1mB,CAAI,CAAC,EAAE,CAAG+L,EACV/L,CAAI,CAAC,EAAE,CAAG,WAGMS,EAChB2L,EAAyC3L,CAAc,CAAC6D,EAAI,CAAEyH,EAElE,GA5GsB+U,CAyGc,8BAzGiB,mBAA/BA,aAxBU,UACI,UACH,OAsB1B,eAAeA,EACpBtW,CAAwC,EAExC,IAAMmc,EAAkB,IAAI/Y,GAC5B,OAAMgZ,EAAoC,CACxC,GAAGpc,CAAO,CACVqc,SAAUrc,EAAQuW,WAAW,iBAC7B4F,CACF,EACF,CAEA,eAAeC,EAAoC,CAYlD,EAZkD,gBACjDjnB,CAAW,OACXkB,CAAK,aACLkgB,CAAW,cACXC,CAAY,gBACZR,CAAc,iBACdmG,CAAe,CACfE,WAAW9F,CAAW,cACtB1f,CAAY,CAIb,CAZkD,EAa3C,EAAGZ,EAAgBqmB,EAAaJ,EAAc,CAAG3F,EACjDgG,EAAgB,EAAE,CAExB,GACED,GACAA,IAAgBzlB,GACE,YAAlBqlB,CACA,EAEA,CAACC,EAAgBK,GAAG,CAACF,GACrB,CACAH,EAAgBlX,GAAG,CAACqX,GAIpB,IAAMG,EAAevG,CAAAA,EAAAA,CAJY,CAIZA,mBAAAA,EACnB,IAAIhX,CALsD,GAKlDod,EAAahf,SAASF,MAAM,EACpC,CAGE3F,UAbwF,QAarE,CAAC4kB,CAAQ,CAAC,EAAE,CAAEA,CAAQ,CAAC,EAAE,CAAEA,CAAQ,CAAC,EAAE,CAAE,UAAU,CACrEhlB,QAAS2e,EAAiB3f,EAAMgB,OAAO,CAAG,IAC5C,GACAyG,IAAI,CAAC,OAAC,YAAExI,CAAU,CAAE,GACpB,GAA0B,UAAtB,OAAOA,EACT,IAAK,IAAMonB,KAAkBpnB,EAI3BJ,GAAAA,EAAAA,IAJuC,WAIvCA,EACEC,EACAqhB,EACAA,EACAkG,EAQR,GAEAH,EAAc1iB,IAAI,CAAC4iB,EACrB,CAEA,IAAK,IAAM3iB,KAAO7D,EAAgB,CAChC,IAAM0mB,EAAuBP,EAAoC,aAC/DjnB,QACAkB,EACAkgB,YAAatgB,CAAc,CAAC6D,EAAI,cAChC0c,iBACAR,kBACAmG,WACAE,eACAxlB,CACF,GAEA0lB,EAAc1iB,IAAI,CAAC8iB,EACrB,CAEA,MAAMxhB,QAAQyhB,GAAG,CAACL,EACpB,gXC8BgB3V,oBAAoB,mBAApBA,GA9EA1G,iBAAiB,mBAAjBA,GAwehB,OAwBC,mBAxBuB2c,GAnfRtd,aAAa,mBAAbA,uCA7CT,YAKA,WAEwB,WAEG,WAK3B,WACiD,eAKjD,WACe,WACM,WACO,WACF,WACD,WACG,WACJ,WACH,WACM,UAEG,WAK9B,UAC2D,WACpB,SACb,OAEjC,IAAMud,EAEF,CAAC,EAEE,SAASvd,EAAc/I,CAAQ,EACpC,OAAOA,EAAI4G,MAAM,GAAKC,OAAOC,QAAQ,CAACF,MAAM,CAUvC,SAAS8C,EAAkBzJ,CAAY,MAMxCD,EAJJ,GAAIumB,CAAAA,EAAAA,EAAAA,KAAAA,EAAM1f,OAAO2f,SAAS,CAACC,SAAS,EAClC,CADqC,MAC9B,KAIT,GAAI,CACFzmB,EAAM,IAAI0I,IAAIC,CAAAA,EAAAA,EAAAA,WAAAA,EAAY1I,GAAO4G,OAAOC,QAAQ,CAAC7G,IAAI,CACvD,CAAE,MAAOymB,EAAG,CAGV,MAAM,qBAEL,CAFSC,MACP,oBAAmB1mB,EAAK,8CADrB,+DAEN,EACF,QAQA,EAAkBD,GACT,GADe,EAIjBA,CACT,CAEA,SAAS4mB,EAAe,CAIvB,EAJuB,mBACtBC,CAAc,CAGf,CAJuB,EA6CtB,MAxCAC,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmB,KAOjB,GAAM,MAAE9nB,CAAI,SAAEsB,CAAO,cAAED,CAAY,CAAE,CAAGwmB,EAClCE,EAAe,CACnB,GAAIzmB,EAAQG,0BAA0B,CAAGoG,OAAOuC,OAAO,CAACvJ,KAAK,CAAG,CAAC,CAAC,CAIlEmnB,MAAM,EACNC,gCAAiCjoB,CACnC,CAEEsB,GAAQC,WAAW,EAGnBL,CAAAA,CAFA,CAEAA,EAAAA,iBAAAA,EAAkB,IAAIwI,IAAI7B,OAAOC,QAAQ,CAAC7G,IAAI,KAAOI,GAGrDC,EAAQC,SAFR,EAEmB,EAAG,EACtBsG,OAAOuC,OAAO,CAAC8d,KANgF,IAMvE,CAACH,EAAc,GAAI1mB,IAE3CwG,OAAOuC,OAAO,CAAC+d,YAAY,CAACJ,EAAc,GAAI1mB,EAElD,EAAG,CAACwmB,EAAe,EAEnBO,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KAQV,EAAG,CAACP,EAAehmB,OAAO,CAAEgmB,EAAe7nB,IAAI,CAAC,EAEzC,IACT,CAEO,SAASoR,IACd,MAAO,CACLW,SAAU,KACV1R,IAAK,KACLE,YAAa,KACbJ,KAAM,KACN8R,aAAc,KACdxR,eAAgB,IAAIC,IACpBJ,QAAS,KACTX,YAAa,CAAC,CAChB,CACF,CAEA,SAAS0oB,EAA+B/G,CAAS,EACnC,MAARA,IAAcA,EAAO,EAAC,EAC1B,IAAMgH,EAAezgB,OAAOuC,OAAO,CAACvJ,KAAK,CACnCmnB,EAAOM,MAAAA,EAAAA,KAAAA,EAAAA,EAAcN,IAAI,CAC3BA,IACF1G,EADQ,IACC,CAAG0G,CAAAA,EAEd,IAAMC,EACJK,MAAAA,EAAAA,KAAAA,EAAAA,EAAcL,+BAA+B,CAK/C,OAJIA,IACF3G,EAAK2G,2BAD8B,IACC,CAAGA,CAAAA,EAGlC3G,CACT,CAEA,SAASiH,EAAK,CAIb,EAJa,IACZC,eAAa,CAGd,CAJa,EAQNroB,EAAyB,OAAlBqoB,EAAyBA,EAAcroB,IAAI,CAAG,KACrD8R,EACc,OAAlBuW,EAAyBA,EAAcvW,YAAY,CAAG,KAGlDwW,EAAsBxW,SAAwBA,EAAe9R,EAKnE,MAAOuoB,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBvoB,EAAMsoB,EAChC,CAKA,SAASE,EAAO,CAQf,EARe,IA+QVxoB,EA/QU,aACdgH,CAAW,aACXyhB,CAAW,aACXC,CAAW,CAKZ,CARe,EASRhoB,EAAQioB,CAAAA,EAAAA,EAAAA,cAAAA,EAAe3hB,GACvB,cAAE9F,CAAY,CAAE,CAAGR,EAEnB,cAAEmQ,CAAY,UAAElP,CAAQ,CAAE,CAAGwT,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KACzC,IAAMtU,EAAM,IAAI0I,IACdrI,EACgC,YAAhC,CAA6CwG,CAAoB,GAApC,CAGxB,CAELmJ,aAAchQ,EAAIgQ,YAAY,CAC9BlP,SAAUinB,CAAAA,EAAAA,EAAAA,WAAAA,EAAY/nB,EAAIc,QAAQ,EAC9BknB,GAAAA,EAAAA,cAAAA,EAAehoB,EAAIc,QAAQ,EAC3Bd,EAAIc,QAAQ,CAEpB,EAAG,CAACT,EAAa,EAqBjB+mB,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KAKR,SAASa,EAAe1S,CAA0B,MAG7C1O,EADA0O,EAAM2S,SAAS,EAChB,QAACrhB,EAAAA,OAAOuC,OAAO,CAACvJ,KAAAA,EAAK,OAApBgH,EAAsBogB,+BAAAA,GACvB,CAOFX,EAAc6B,cAAc,MAAGjlB,EAE/B2F,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtBnC,KAAMkB,EAAAA,cAAc,CACpB5H,IAAK,IAAI0I,IAAI7B,OAAOC,QAAQ,CAAC7G,IAAI,EACjCjB,KAAM6H,OAAOuC,OAAO,CAACvJ,KAAK,CAAConB,+BAC7B,GACF,CAIA,OAFApgB,OAAOuhB,gBAAgB,CAAC,WAAYH,GAE7B,KACLphB,OAAOwhB,mBAAmB,CAAC,WAAYJ,EACzC,CACF,EAAG,EAAE,EAELb,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KAGR,SAASkB,EACP/S,CAAyC,EAEzC,IAAMvQ,EAAQ,WAAYuQ,EAAQA,EAAMiI,MAAM,CAAGjI,EAAMvQ,KAAK,CAC5D,GAAIujB,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBvjB,GAAQ,CAC1BuQ,EAAMU,cAAc,GACpB,IAAMjW,EAAMwoB,GAAAA,EAAAA,uBAAAA,EAAwBxjB,EAIhCyjB,CAHiBC,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyB1jB,KAGzB2jB,EAAAA,YAAY,CAACtlB,IAAI,CACpC4C,CADsC,CACtCA,uBAAuB,CAAC5C,IAAI,CAACrD,EAAK,CAAC,GAEnCiG,EAAAA,uBAAuB,CAAC+D,OAAO,CAAChK,EAAK,CAAC,EAE1C,CACF,CAIA,OAHA6G,OAAOuhB,gBAAgB,CAAC,QAASE,GACjCzhB,OAAOuhB,gBAAgB,CAAC,qBAAsBE,GAEvC,KACLzhB,OAAOwhB,mBAAmB,CAAC,QAASC,GACpCzhB,OAAOwhB,mBAAmB,CAAC,qBAAsBC,EACnD,CACF,EAAG,EAAE,EAYL,GAAM,SAAEhoB,CAAO,CAAE,CAAGT,EACpB,GAAIS,EAAQE,aAAa,CAAE,CAEzB,GAAI8lB,EAAc6B,cAAc,GAAK9nB,EAAc,CACjD,IAAMyG,EAAWD,OAAOC,QAAQ,CAC5BxG,EAAQC,WAAW,CACrBuG,CADuB,CACd8hB,MAAM,CAACvoB,GAEhByG,EAASkD,OAAO,CAAC3J,GAGnBimB,EAAc6B,cAAc,CAAG9nB,CACjC,CAIAwoB,CAAAA,EAAAA,EAAAA,GAAAA,EAAIC,EAAAA,kBAAkB,CACxB,CAEA1B,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAM2B,EAAoBliB,OAAOuC,OAAO,CAAC8d,SAAS,CAAC8B,IAAI,CAACniB,OAAOuC,OAAO,EAChE6f,EAAuBpiB,OAAOuC,OAAO,CAAC+d,YAAY,CAAC6B,IAAI,CAC3DniB,OAAOuC,OAAO,EAIV8f,EACJlpB,QAIE6G,EAFF,IAAM5G,EAAO4G,OAAOC,QAAQ,CAAC7G,IAAI,CAC3BjB,EAAAA,OACJ6H,EAAAA,OAAOuC,OAAO,CAACvJ,KAAAA,EAAK,OAApBgH,EAAsBogB,+BAA+B,CAEvDnf,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,KACde,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtBnC,KAAMkB,EAAAA,cAAc,CACpB5H,IAAK,IAAI0I,IAAI1I,MAAAA,EAAAA,EAAOC,EAAMA,QAC1BjB,CACF,EACF,EACF,EAOA6H,OAAOuC,OAAO,CAAC8d,SAAS,CAAG,SAASA,CACzB,CACTiC,CAAe,CACfnpB,CAAyB,SAGrBsgB,MAAAA,EAAAA,KAAAA,EAAAA,EAAM0G,IAAAA,IAAQ1G,MAAAA,EAAAA,KAAAA,EAAAA,EAAM8I,EAAAA,GAAI,CAI5B9I,EAAO+G,EAA+B/G,GAElCtgB,GACFkpB,EADO,IALAH,EAAkBzI,EAAM6I,EAASnpB,EAU5C,EAOA6G,OAAOuC,OAAO,CAAC+d,YAAY,CAAG,SAASA,CAC5B,CACTgC,CAAe,CACfnpB,CAAyB,SAGrBsgB,MAAAA,EAAAA,KAAAA,EAAAA,EAAM0G,IAAAA,IAAQ1G,MAAAA,EAAAA,KAAAA,EAAAA,EAAM8I,EAAAA,GAAI,CAG5B9I,EAAO+G,EAA+B/G,GAElCtgB,GACFkpB,EADO,IAJAD,EAAqB3I,EAAM6I,EAASnpB,EAQ/C,EAOA,IAAMqpB,EAAa,IACjB,GAAK9T,CAAD,CAAO1V,KAAK,EAAE,GAMd,CAAC0V,EAAM1V,KAAK,CAACmnB,IAAI,CAAE,YACrBngB,OAAOC,QAAQ,CAACwiB,MAAM,GAMxBxhB,GAAAA,EAAAA,eAAAA,EAAgB,KACd/B,CAAAA,EAAAA,EAAAA,sBAAAA,EACEc,OAAOC,QAAQ,CAAC7G,IAAI,CACpBsV,EAAM1V,KAAK,CAAConB,+BAA+B,CAE/C,GACF,EAIA,OADApgB,OAAOuhB,gBAAgB,CAAC,WAAYiB,GAC7B,KACLxiB,OAAOuC,OAAO,CAAC8d,SAAS,CAAG6B,EAC3BliB,OAAOuC,OAAO,CAAC+d,YAAY,CAAG8B,EAC9BpiB,OAAOwhB,mBAAmB,CAAC,WAAYgB,EACzC,CACF,EAAG,EAAE,EAEL,GAAM,OAAExqB,CAAK,MAAEG,CAAI,SAAE6B,CAAO,mBAAEH,CAAiB,CAAE,CAAGb,EAE9C0pB,EAAejV,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IACpB8P,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBvlB,EAAOG,CAAI,CAAC,EAAE,EACpC,CAACH,EAAOG,EAAK,EAGVwqB,EAAalV,GAAAA,EAAAA,OAAAA,EAAQ,IAClB3S,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkB3C,GACxB,CAACA,EAAK,EAEHyqB,EAAsBnV,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KAC3B,CACLoV,WAAY1qB,EACZ2qB,gBAAiB9qB,EACjB+qB,kBAAmB,KAGnB5pB,IAAKK,CACP,GACC,CAACrB,EAAMH,EAAOwB,EAAa,EAExBwpB,EAA4BvV,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KACjC,MACLtV,oBACA0B,UACAG,EACF,EACC,CAAC7B,EAAM0B,EAAmBG,EAAQ,EAGrC,GAAqB,OAAjB0oB,EAAuB,CAOzB,GAAM,CAAC/B,EAAesC,EAAQ,CAAGP,EACjCpqB,EAAO,KAAPA,GAAO,EAACooB,EAAAA,CAAmBC,GAA3BroB,WAA0CqoB,GAAxBsC,EACpB,MACE3qB,CADK,CACE,KAGT,IAAI4qB,EACF,QADEA,CACF,EAACC,EAAAA,MADCD,UACe,YACd5qB,EACAN,EAAMQ,GAAG,CACV,UAAC4qB,EAAAA,kBAAkB,EAACjrB,KAAMA,OAwC9B,OAVE+qB,EACE,UAACG,EAAAA,OADHH,MACgB,EACZI,eAAgBtC,CAAW,CAAC,EAAE,CAC9BuC,YAAavC,CAAW,CAAC,EAAE,UAE1BkC,IAML,iCACE,UAACnD,EAAAA,CAAeC,eAAgBhnB,IAChC,UAACwqB,EAAAA,CAAAA,GACD,UAACC,EAAAA,iBAAiB,CAAC7T,QAAQ,EAAClT,MAAOimB,WACjC,UAACe,EAAAA,eAAe,CAAC9T,QAAQ,EAAClT,MAAOzC,WAC/B,UAAC0pB,EAAAA,mBAAmB,CAAC/T,QAAQ,EAAClT,MAAOyM,WACnC,UAACya,EAAAA,yBAAyB,CAAChU,QAAQ,EACjClT,MAAOsmB,WAOP,UAAC1V,EAAAA,gBAAgB,CAACsC,QAAQ,EAAClT,MAAO0C,EAAAA,uBAAuB,UACvD,UAACykB,EAAAA,mBAAmB,CAACjU,QAAQ,EAAClT,MAAOkmB,WAClCM,gBASnB,CAEe,SAAS1D,EAAU,CAQjC,EARiC,gBAChClgB,CAAW,CACXwkB,8BAA+B,CAACC,EAAsBC,EAAkB,aACxEjD,CAAW,CAKZ,CARiC,EAWhC,MAFAkD,CAAAA,EAAAA,EAAAA,oBAAAA,IAGE,UAACZ,EAAAA,aAAa,EAGZC,eAAgBY,EAAAA,OAAkB,UAElC,UAACpD,EAAAA,CACCxhB,YAAaA,EACbyhB,YAAaA,EACbC,YAAa,CAAC+C,EAAsBC,EAAkB,IAI9D,CAEA,IAAMG,EAAgB,IAAIpe,IACtBqe,EAAsB,IAAIre,IAa9B,SAASyd,IACP,GAAM,EAAGa,EAAY,CAAGjX,EAAAA,OAAK,CAACkX,QAAQ,CAAC,GACjCC,EAAqBJ,EAAcjQ,IAAI,OAC7CqM,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAMiE,EAAU,IAAMH,EAAY,GAAOI,EAAI,GAK7C,OAJAL,EAAoBxc,GAAG,CAAC4c,GACpBD,IAAuBJ,EAAcjQ,IAAI,EAAE,IAGxC,KACLkQ,EAAoB3c,MAAM,CAAC+c,EAC7B,CACF,EAAG,CAACD,EAAoBF,EAAY,EAK7B,IAAIF,EAAc,CAAC1J,GAAG,CAAC,CAACrhB,EAAM2R,IAAAA,CACnC,SAACrF,CADkCqF,MAClCrF,CAECgf,IAAI,aACJtrB,KAAO,GAAEA,EAETurB,KAFgBC,MAEL,QAJN7Z,GAUX,CAxCA8Z,WAAWC,eAAe,CAAG,SAAU1rB,CAAY,EACjD,IAAI2rB,EAAMZ,EAAcjQ,IAAI,CAO5B,OANAiQ,EAAcvc,GAAG,CAACxO,GACd+qB,EAAcjQ,IAAI,GAAK6Q,GACzBX,EAD8B,OACH,CAAC,GAAQY,KAI/BlnB,QAAQC,OAAO,EACxB,wUCljBgBqlB,qCAAAA,aA7B4B,WACf,OAGvB6B,EAAiB,uBAyBhB,SAAS7B,EAAmB,CAAqC,EAArC,IAAEjrB,MAAI,CAA+B,CAArC,EAC3B,CAAC+sB,EAAYC,EAAc,CAAGb,CAAAA,EAAAA,EAAAA,QAAAA,EAA6B,MAEjE/D,GAAAA,EAAAA,SAAAA,EAAU,KAER4E,EA3BJ,SAASC,GA2BSC,GAzBZC,EADJ,IAAMA,EAAoBhM,SAASiM,iBAAiB,CAACN,EAAe,CAAC,EAAE,CACvE,GAAIK,MAAAA,CAAAA,EAAAA,GAAAA,IAAAA,EAAAA,EAAmBE,UAAAA,EAAU,OAA7BF,EAA+BG,UAAU,CAAC,EAAE,CAC9C,CADgD,MACzCH,EAAkBE,UAAU,CAACC,UAAU,CAAC,EAAE,EAEjD,IAAMC,EAAYpM,SAASqM,aAAa,CAACV,GACzCS,EAAUE,KAAK,CAACC,OAAO,CAAG,oBAC1B,IAAMR,EAAY/L,SAASqM,aAAa,CAAC,OAWzC,OAVAN,EAAUS,QAAQ,CAAG,YACrBT,EAAUtH,EAAE,CAXK,EAWFgI,yBACfV,EAAUW,IAAI,CAAG,QACjBX,EAAUO,KAAK,CAACC,OAAO,CACrB,+IAGaH,EAAUO,YAAY,CAAC,CAAEC,KAAM,MAAO,GAC9CC,WAAW,CAACd,GACnB/L,SAAS8M,IAAI,CAACD,WAAW,CAACT,GACnBL,CACT,CACF,KAQW,KACL,IAAMK,EAAYpM,SAAS+M,oBAAoB,CAACpB,EAAe,CAAC,EAAE,EAC9DS,QAAAA,KAAAA,EAAAA,EAAWY,WAAAA,EAAa,CAC1BhN,SAAS8M,IAAI,CAACG,WAAW,CAACb,EAE9B,GACC,EAAE,EAEL,GAAM,CAACc,EAAmBC,EAAqB,CAAGnC,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACrDoC,EAAgB1a,GAAAA,EAAAA,MAAAA,OAA2B3P,GAwBjD,MAtBAkkB,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAIoG,EAAe,GACnB,GAAIrN,SAASsN,KAAK,CAChBD,CADkB,CACHrN,SAASsN,KAAK,KACxB,CACL,IAAMC,EAAavN,SAASwN,aAAa,CAAC,MACtCD,IACFF,EAAeE,EAAWE,IADZ,KACqB,EAAIF,EAAWG,WAAW,EAAI,GAErE,CAK4B3qB,WAAZ0R,OAAO,EACrB2Y,EAAc3Y,OAAO,GAAK4Y,GAE1BF,EAAqBE,GAEvBD,EAAc3Y,IAHZ,GAGmB,CAAG4Y,CAC1B,EAAG,CAACxuB,EAAK,EAEF+sB,EAAa+B,CAAAA,EAAAA,EAAAA,MAAb/B,MAAa+B,EAAaT,EAAmBtB,CAA7CA,EAA2D,IACpE,iQCrEA,QAEA,cACA,4BACA,6GCKgBgC,qCAAAA,aATY,OASrB,SAASA,EAAejnB,CAAgB,CAAE9G,CAAQ,EACvD,GAAI8G,EAAS3E,UAAU,CAAC,KAAM,CAC5B,IAAM6rB,EAAUhuB,EAAI4G,MAAM,CAAG5G,EAAIc,QAAQ,CACzC,OAAO,IAAI4H,IACT,CAGCslB,EAAQC,QAAQ,CAAC,KAAOD,EAAUA,EAAU,KAAE,EAEnD,CAEA,OAAO,IAAItlB,IAAIC,CAAAA,EAAAA,EAAAA,WAAAA,EAAY7B,GAAW9G,EAAIC,IAAI,CAChD,qBAPmG,uSCH1FiuB,qCAAAA,KAXT,IAAIA,EAAY,IAAgB,sGCiEnB/lB,qCAAAA,OAzDN,SAMyB,SACG,SACJ,QACA,SACC,SACE,SACE,WA6CvBA,EARb,QASE,CATOgmB,CACoB,CAC3BpP,CAAuB,CAOM,CAL7B,EAKgCoP,KALzBtuB,CACT,UAIkDuuB,CAAaA,8TC0I/CC,qCAAAA,aAxMW,WACM,UAO1B,WAoBA,WACwB,WACG,WACA,WACU,WACA,WAEd,WACgB,WACT,WACa,WACZ,WACU,WAIzC,WAC0B,WACJ,WACkB,WAChB,WACH,WAIrB,SAC+B,OAxCtC,GAAM,iBAAEC,CAAe,6BAAEC,CAA2B,aAAEC,CAAW,CAAE,CAG7DC,EAAQ,IAAsC,CAFxB,CAsD5B,EAlDMA,CAA0C,QAFnCA,IAoDEC,EACb7uB,CAA2B,CAC3BgB,CAAwC,CACxC,CAA4C,EAA5C,IAqCI4nB,EAaAkG,EAlDJ,UAAEC,CAAQ,YAAEC,CAAU,CAAsB,CAA5C,EAEMC,EAAsBP,IACtB/I,EAAOb,CAAAA,EAAAA,EAAAA,gCAAAA,EAAiCiK,GAKxC5J,EACU,cAAdQ,EAAK9e,IAAI,CAAmB4e,GAAAA,EAAAA,cAAAA,EAAeuJ,EAAYrJ,GAAQqJ,EAE3D5B,EAAO,MAAMuB,EAAYxJ,EAAU,qBAAE8J,CAAoB,GAEzD1R,EAAM,MAAM2R,MAAM,GAAI,CAC1BC,OAAQ,OACRC,QAAS,CACPC,OAAQC,EAAAA,uBAAuB,CAC/B,CAACC,EAAAA,aAAa,CAAC,CAAER,EACjB,CAACS,EAAAA,6BAA6B,CAAC,CAAEC,mBAC/BC,KAAKC,SAAS,CAAC3vB,EAAMb,IAAI,GAE3B,GAAIuK,CAIC,CAAC,CACN,GALkC,EAM9B,CACE,CAACkmB,EAAAA,QAAQ,CAAC,CAAE5uB,CACd,EACA,CAAC,CAAC,EAERosB,MACF,GAEMyC,EAAiBtS,EAAI6R,OAAO,CAAC5gB,GAAG,CAAC,qBACjC,CAACvH,EAAU6oB,EAAc,CAAGD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgBrtB,KAAK,CAAC,OAAQ,EAAE,CAElE,OAAQstB,GACN,IAAK,OACHlH,EAAeE,EAAAA,YAAY,CAACtlB,IAAI,CAChC,KACF,KAAK,UACHolB,EAAeE,EAAAA,YAAY,CAAC3e,OAAO,CACnC,KACF,SACEye,OAAevlB,CACnB,CAEA,IAAM0sB,EAAc,CAAC,CAACxS,EAAI6R,OAAO,CAAC5gB,GAAG,CAACwhB,EAAAA,wBAAwB,EAE9D,GAAI,CACF,IAAMC,EAAoBP,KAAKQ,KAAK,CAClC3S,EAAI6R,OAAO,CAAC5gB,GAAG,CAAC,yBAA2B,YAE7CsgB,EAAmB,CACjBqB,MAAOF,CAAiB,CAAC,EAAE,EAAI,EAAE,CACjC5S,IAAK,CAAC,CAAC4S,CAAiB,CAAC,EAAE,CAC3BG,OAAQH,CAAiB,CAAC,EAAE,CAEhC,CAAE,MAAO/a,EAAG,CACV4Z,EAAmB,CACjBqB,MAAO,EAAE,CACT9S,KAAK,EACL+S,QAAQ,CACV,CACF,CAEA,IAAMC,EAAmBppB,EACrBinB,CAAAA,EAAAA,EAAAA,cAAAA,EACEjnB,EACA,IAAI4B,IAAI7I,EAAMQ,YAAY,CAAEwG,OAAOC,QAAQ,CAAC7G,IAAI,QAElDiD,EAEEitB,EAAc/S,EAAI6R,OAAO,CAAC5gB,GAAG,CAAC,gBAEpC,GAAI8hB,MAAAA,EAAAA,KAAAA,EAAAA,EAAahuB,UAAU,CAACgtB,EAAAA,uBAAuB,EAAG,CACpD,IAAMiB,EAAiC,MAAM9B,EAC3C3pB,QAAQC,OAAO,CAACwY,GAChB,CAAEiT,WAAAA,EAAAA,UAAU,CAAEC,iBAAAA,EAAAA,gBAAgB,qBAAExB,CAAoB,UAGtD,EAES,CACLyB,OAHU,UAGQC,GAAAA,EAAAA,mBAAAA,EAAoBJ,EAASK,CAAC,mBAChDP,eACAzH,mBACAkG,cACAiB,CACF,EAGK,CACL3oB,aAAcmpB,EAASpc,CAAC,CACxBuc,iBAAkBC,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBJ,EAASK,CAAC,EAChDP,mBACAzH,gCACAkG,cACAiB,CACF,CACF,CAGA,GAAIxS,EAAIzF,MAAM,EAAI,IAQhB,CARqB,KAQf,qBAAgB,CAAhB,MAJY,eAAhBwY,EACI,MAAM/S,EAAIsT,IAAI,GACd,wDAEA,+DAAe,GAGvB,MAAO,kBACLR,eACAzH,mBACAkG,cACAiB,CACF,CACF,CAMO,SAASvB,EACdxuB,CAA2B,CAC3BC,CAA0B,EAE1B,GAAM,CAAE8E,SAAO,QAAEC,CAAM,CAAE,CAAG/E,EACtBwK,EAA+B,CAAC,EAElC1I,EAAc/B,EAAMb,IAAI,CAE5BsL,EAAQ7J,0BAA0B,EAAG,EAMrC,IAAMI,EACJhB,EAAMgB,OAAO,EAAI4e,CAAAA,EAAAA,EAAAA,iCAAAA,EAAkC5f,EAAMb,IAAI,EACzDa,EAAMgB,OAAO,CACb,KAEAlC,EAAcghB,KAAKC,GAAG,GAE5B,OAAO8O,EAAkB7uB,EAAOgB,EAASf,GAAQwH,IAAI,CACnD,cAQMqpB,EARC,cACL1pB,CAAY,CACZspB,iBAAkBzxB,CAAU,kBAC5BoxB,CAAgB,cAChBzH,CAAY,aACZmH,CAAW,CACXjB,kBAAgB,CACjB,GAiBC,GAbIuB,IACEzH,IAAiBE,EAAAA,QADD,IACa,CAAC3e,OAAO,EAAE,EACnC1J,OAAO,CAACC,WAAW,EAAG,EAC5B+J,EAAQ/J,WAAW,CAAG,KAEtBV,EAAMS,OAAO,CAACC,WAAW,EAAG,EAC5B+J,EAAQ/J,WAAW,EAAG,GAIxB+J,EAAQjK,YAAY,CADpBswB,EACuBA,CADRzwB,EAAAA,EAAAA,iBAAAA,EAAkBgwB,GAAkB,IAIjD,CAACpxB,QAIH,CAHA8F,EAAQqC,CADO,EAIXipB,GACK1gB,CAAAA,EAAAA,EAAAA,UADa,OACbA,EACL3P,EACAyK,EACA4lB,EAAiBjwB,IAAI,CACrBJ,EAAMS,OAAO,CAACC,WAAW,EAGtBV,EAGT,GAA0B,UAAtB,OAAOf,EAIT,OAFA8F,EAAQqC,GAEDuI,CAAAA,EAAAA,EAAAA,iBAAAA,EACL3P,EACAyK,EACAxL,EACAe,EAAMS,OAAO,CAACC,WAAW,EAI7B,IAAMqwB,EACJjC,EAAiBqB,KAAK,CAAC1uB,MAAM,CAAG,GAChCqtB,EAAiBzR,GAAG,EACpByR,EAAiBsB,MAAM,CAEzB,IAAK,IAAMpgB,KAAwB/Q,EAAY,CAC7C,GAAM,CACJE,KAAMC,CAAS,CACfC,SAAUqR,CAAiB,CAC3BpR,MAAI,cACJC,CAAY,CACb,CAAGyQ,EAEJ,GAAI,CAACzQ,EAKH,OAHAyO,KAFiB,GAETgS,GAAG,CAAC,8BACZjb,EAAQqC,GAEDpH,EAIT,IAAMsQ,EAAUrF,CAAAA,EAAAA,EAAAA,2BAAAA,EAEd,CAAC,GAAG,CACJlJ,EACA3C,EACA0xB,GAA8B9wB,EAAMQ,UAArBswB,EAAiC,EAGlD,GAAgB,MAAM,CAAlBxgB,EAGF,OAFAvL,EAAQqC,GAEDsI,CAAAA,EAAAA,EAAAA,qBAAAA,EAAsB1P,EAAOC,EAAQb,GAG9C,GAAI+a,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4BpY,EAAauO,GAG3C,OAHqD,EAC7ClJ,GAEDuI,CAAAA,EAAAA,EAAAA,iBAAAA,EACL3P,EACAyK,EACAqmB,GAAgB9wB,EAAMQ,YAAY,CAClCR,EAAMS,OAAO,CAACC,WAAW,EAK7B,GAA0B,OAAtBgQ,EAA4B,CAC9B,IAAMlR,EAAMkR,CAAiB,CAAC,EAAE,CAC1B1R,EAAmBuR,CAAAA,EAAAA,EAAAA,oBAAAA,IACzBvR,EAAMQ,GAAG,CAAGA,EACZR,EAAMU,WAAW,CAAG,KACpBV,EAAMS,OAAO,CAAGiR,CAAiB,CAAC,EAAE,CACpC/Q,CAAAA,EAAAA,EAAAA,6BAAAA,EACEb,EACAE,OACA,EAEAI,EACAsR,EACApR,OACA+D,GAGFoH,EAAQzL,KAAK,CAAGA,EAIdyL,EAAQ1J,aAAa,CAAG,IAAIlB,IAE1BkxB,GACF,MAAM9Q,CAAAA,EAAAA,EAAAA,KADe,0BACfA,EAAgC,aACpCnhB,QACAkB,EACAkgB,YAAa5P,EACb6P,aAAcnhB,EACd2gB,gBAAgBqR,CAAQhwB,EACxBR,aAAciK,EAAQjK,YAAY,EAAIR,EAAMQ,YAAY,EAG9D,CAEAiK,EAAQC,WAAW,CAAG4F,EACtBvO,EAAcuO,CAChB,CAoDA,OAlDI+f,GAAoBS,GAC2BC,IAW/C3O,CAAAA,EAAAA,EAAAA,EAZkC,QACgC,oBAWlEA,EAA+B,CAC7BjiB,IAAKkwB,EACL5P,KAAM,YACJxhB,EACAuB,aAAc6C,OACdqgB,oBAAoB,EACpBQ,aAAa,EACbxD,WAAW,EAGXkD,UAAW,CAAC,CACd,EACAzkB,KAAMa,EAAMb,IAAI,CAChB4B,cAAef,EAAMe,aAAa,CAClCC,QAAShB,EAAMgB,OAAO,CACtBgJ,KAAM+lB,EAAc9lB,EAAAA,YAAY,CAACC,IAAI,CAAGD,EAAAA,YAAY,CAACuK,IAAI,GAE3D/J,EAAQ1J,aAAa,CAAGf,EAAMe,aAAa,EAS7CiE,EACEisB,CAAAA,EAAAA,EAAAA,gBAAAA,EACE/I,CAAAA,EAAAA,EAAAA,WAAAA,EAAY4I,GACR3I,CAAAA,EAAAA,EAAAA,cAAAA,EAAe2I,GACfA,EACJlI,GAAgBE,EAAAA,YAAY,CAACtlB,IAAI,IAIrCuB,EAAQqC,GAGHmD,CAAAA,EAAAA,EAAAA,aAAAA,EAAcvK,EAAOyK,EAC9B,EACA,IAEEzF,EAAOkQ,GAEAlV,GAGb", "sources": ["webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/apply-flight-data.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/should-hard-navigate.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/compute-changed-path.ts", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/promise-queue.ts", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/app-router-instance.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/handle-mutable.ts", "webpack://next-shadcn-dashboard-starter/../../src/defaultAttributes.ts", "webpack://next-shadcn-dashboard-starter/../../src/createReactComponent.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/apply-router-state-patch-to-tree.ts", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/links.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/handle-segment-mismatch.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/aliased-prefetch-navigations.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/fill-cache-with-new-subtree-data.ts", "webpack://next-shadcn-dashboard-starter/../../../src/client/app-dir/link.tsx", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@radix-ui+react-compose-ref_c3d011aa809cacc32979e459cdd6cd2c/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@radix-ui+react-slot@1.1.2_@types+react@19.0.1_react@19.0.0/node_modules/@radix-ui/react-slot/dist/index.mjs", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/ppr-navigations.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/client/components/router-reducer/reducers/prefetch-reducer.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/is-navigating-to-new-root-layout.ts", "webpack://next-shadcn-dashboard-starter/../../src/client/use-merged-ref.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/clear-cache-node-data-for-segment-path.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/client/components/router-reducer/reducers/hmr-refresh-reducer.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/invalidate-cache-by-router-state.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_class_private_field_loose_base.js", "webpack://next-shadcn-dashboard-starter/../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/segment-cache.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/prefetch-cache-utils.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/client/components/router-reducer/reducers/find-head-in-cache.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.ts", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/server-reference-info.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/refetch-inactive-parallel-segments.ts", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/app-router.tsx", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/app-router-announcer.tsx", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_class_private_field_loose_key.js", "webpack://next-shadcn-dashboard-starter/../../src/client/assign-location.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/shared/lib/utils/error-once.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/client/components/router-reducer/router-reducer.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { fillCacheWithNewSubTreeData } from './fill-cache-with-new-subtree-data'\nimport type { PrefetchCacheEntry } from './router-reducer-types'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\n\nexport function applyFlightData(\n  navigatedAt: number,\n  existingCache: CacheNode,\n  cache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): boolean {\n  // The one before last item is the router state tree patch\n  const { tree: treePatch, seedData, head, isRootRender } = flightData\n\n  // Handles case where prefetch only returns the router tree patch without rendered components.\n  if (seedData === null) {\n    return false\n  }\n\n  if (isRootRender) {\n    const rsc = seedData[1]\n    const loading = seedData[3]\n    cache.loading = loading\n    cache.rsc = rsc\n    // This is a PPR-only field. When PPR is enabled, we shouldn't hit\n    // this path during a navigation, but until PPR is fully implemented\n    // yet it's possible the existing node does have a non-null\n    // `prefetchRsc`. As an incremental step, we'll just de-opt to the\n    // old behavior — no PPR value.\n    cache.prefetchRsc = null\n    fillLazyItemsTillLeafWithHead(\n      navigatedAt,\n      cache,\n      existingCache,\n      treePatch,\n      seedData,\n      head,\n      prefetchEntry\n    )\n  } else {\n    // Copy rsc for the root node of the cache.\n    cache.rsc = existingCache.rsc\n    // This is a PPR-only field. Unlike the previous branch, since we're\n    // just cloning the existing cache node, we might as well keep the\n    // PPR value, if it exists.\n    cache.prefetchRsc = existingCache.prefetchRsc\n    cache.parallelRoutes = new Map(existingCache.parallelRoutes)\n    cache.loading = existingCache.loading\n    // Create a copy of the existing cache with the rsc applied.\n    fillCacheWithNewSubTreeData(\n      navigatedAt,\n      cache,\n      existingCache,\n      flightData,\n      prefetchEntry\n    )\n  }\n\n  return true\n}\n", "import { createHrefFromUrl } from '../create-href-from-url'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  RestoreAction,\n} from '../router-reducer-types'\nimport { extractPathFromFlightRouterState } from '../compute-changed-path'\nimport { updateCacheNodeOnPopstateRestoration } from '../ppr-navigations'\n\nexport function restoreReducer(\n  state: ReadonlyReducerState,\n  action: RestoreAction\n): ReducerState {\n  const { url, tree } = action\n  const href = createHrefFromUrl(url)\n  // This action is used to restore the router state from the history state.\n  // However, it's possible that the history state no longer contains the `FlightRouterState`.\n  // We will copy over the internal state on pushState/replaceState events, but if a history entry\n  // occurred before hydration, or if the user navigated to a hash using a regular anchor link,\n  // the history state will not contain the `FlightRouterState`.\n  // In this case, we'll continue to use the existing tree so the router doesn't get into an invalid state.\n  const treeToRestore = tree || state.tree\n\n  const oldCache = state.cache\n  const newCache = process.env.__NEXT_PPR\n    ? // When PPR is enabled, we update the cache to drop the prefetch\n      // data for any segment whose dynamic data was already received. This\n      // prevents an unnecessary flash back to PPR state during a\n      // back/forward navigation.\n      updateCacheNodeOnPopstateRestoration(oldCache, treeToRestore)\n    : oldCache\n\n  return {\n    // Set canonical url\n    canonicalUrl: href,\n    pushRef: {\n      pendingPush: false,\n      mpaNavigation: false,\n      // Ensures that the custom history state that was set is preserved when applying this update.\n      preserveCustomHistoryState: true,\n    },\n    focusAndScrollRef: state.focusAndScrollRef,\n    cache: newCache,\n    prefetchCache: state.prefetchCache,\n    // Restore provided tree\n    tree: treeToRestore,\n    nextUrl: extractPathFromFlightRouterState(treeToRestore) ?? url.pathname,\n  }\n}\n", "import type {\n  FlightRouterState,\n  FlightDataPath,\n  Segment,\n} from '../../../server/app-render/types'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { matchSegment } from '../match-segments'\n\n// TODO-APP: flightSegmentPath will be empty in case of static response, needs to be handled.\nexport function shouldHardNavigate(\n  flightSegmentPath: FlightDataPath,\n  flightRouterState: FlightRouterState\n): boolean {\n  const [segment, parallelRoutes] = flightRouterState\n  // TODO-APP: Check if `as` can be replaced.\n  const [currentSegment, parallelRouteKey] = flightSegmentPath as [\n    Segment,\n    string,\n  ]\n\n  // Check if current segment matches the existing segment.\n  if (!matchSegment(currentSegment, segment)) {\n    // If dynamic parameter in tree doesn't match up with segment path a hard navigation is triggered.\n    if (Array.isArray(currentSegment)) {\n      return true\n    }\n\n    // If the existing segment did not match soft navigation is triggered.\n    return false\n  }\n  const lastSegment = flightSegmentPath.length <= 2\n\n  if (lastSegment) {\n    return false\n  }\n\n  return shouldHardNavigate(\n    getNextFlightSegmentPath(flightSegmentPath),\n    parallelRoutes[parallelRouteKey]\n  )\n}\n", "import type {\n  FlightRouterState,\n  Segment,\n} from '../../../server/app-render/types'\nimport { INTERCEPTION_ROUTE_MARKERS } from '../../../shared/lib/router/utils/interception-routes'\nimport type { Params } from '../../../server/request/params'\nimport {\n  isGroupSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\n\nconst removeLeadingSlash = (segment: string): string => {\n  return segment[0] === '/' ? segment.slice(1) : segment\n}\n\nconst segmentToPathname = (segment: Segment): string => {\n  if (typeof segment === 'string') {\n    // 'children' is not a valid path -- it's technically a parallel route that corresponds with the current segment's page\n    // if we don't skip it, then the computed pathname might be something like `/children` which doesn't make sense.\n    if (segment === 'children') return ''\n\n    return segment\n  }\n\n  return segment[1]\n}\n\nfunction normalizeSegments(segments: string[]): string {\n  return (\n    segments.reduce((acc, segment) => {\n      segment = removeLeadingSlash(segment)\n      if (segment === '' || isGroupSegment(segment)) {\n        return acc\n      }\n\n      return `${acc}/${segment}`\n    }, '') || '/'\n  )\n}\n\nexport function extractPathFromFlightRouterState(\n  flightRouterState: FlightRouterState\n): string | undefined {\n  const segment = Array.isArray(flightRouterState[0])\n    ? flightRouterState[0][1]\n    : flightRouterState[0]\n\n  if (\n    segment === DEFAULT_SEGMENT_KEY ||\n    INTERCEPTION_ROUTE_MARKERS.some((m) => segment.startsWith(m))\n  )\n    return undefined\n\n  if (segment.startsWith(PAGE_SEGMENT_KEY)) return ''\n\n  const segments = [segmentToPathname(segment)]\n  const parallelRoutes = flightRouterState[1] ?? {}\n\n  const childrenPath = parallelRoutes.children\n    ? extractPathFromFlightRouterState(parallelRoutes.children)\n    : undefined\n\n  if (childrenPath !== undefined) {\n    segments.push(childrenPath)\n  } else {\n    for (const [key, value] of Object.entries(parallelRoutes)) {\n      if (key === 'children') continue\n\n      const childPath = extractPathFromFlightRouterState(value)\n\n      if (childPath !== undefined) {\n        segments.push(childPath)\n      }\n    }\n  }\n\n  return normalizeSegments(segments)\n}\n\nfunction computeChangedPathImpl(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const [segmentA, parallelRoutesA] = treeA\n  const [segmentB, parallelRoutesB] = treeB\n\n  const normalizedSegmentA = segmentToPathname(segmentA)\n  const normalizedSegmentB = segmentToPathname(segmentB)\n\n  if (\n    INTERCEPTION_ROUTE_MARKERS.some(\n      (m) =>\n        normalizedSegmentA.startsWith(m) || normalizedSegmentB.startsWith(m)\n    )\n  ) {\n    return ''\n  }\n\n  if (!matchSegment(segmentA, segmentB)) {\n    // once we find where the tree changed, we compute the rest of the path by traversing the tree\n    return extractPathFromFlightRouterState(treeB) ?? ''\n  }\n\n  for (const parallelRouterKey in parallelRoutesA) {\n    if (parallelRoutesB[parallelRouterKey]) {\n      const changedPath = computeChangedPathImpl(\n        parallelRoutesA[parallelRouterKey],\n        parallelRoutesB[parallelRouterKey]\n      )\n      if (changedPath !== null) {\n        return `${segmentToPathname(segmentB)}/${changedPath}`\n      }\n    }\n  }\n\n  return null\n}\n\nexport function computeChangedPath(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const changedPath = computeChangedPathImpl(treeA, treeB)\n\n  if (changedPath == null || changedPath === '/') {\n    return changedPath\n  }\n\n  // lightweight normalization to remove route groups\n  return normalizeSegments(changedPath.split('/'))\n}\n\n/**\n * Recursively extracts dynamic parameters from FlightRouterState.\n */\nexport function getSelectedParams(\n  currentTree: FlightRouterState,\n  params: Params = {}\n): Params {\n  const parallelRoutes = currentTree[1]\n\n  for (const parallelRoute of Object.values(parallelRoutes)) {\n    const segment = parallelRoute[0]\n    const isDynamicParameter = Array.isArray(segment)\n    const segmentValue = isDynamicParameter ? segment[1] : segment\n    if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) continue\n\n    // Ensure catchAll and optional catchall are turned into an array\n    const isCatchAll =\n      isDynamicParameter && (segment[2] === 'c' || segment[2] === 'oc')\n\n    if (isCatchAll) {\n      params[segment[0]] = segment[1].split('/')\n    } else if (isDynamicParameter) {\n      params[segment[0]] = segment[1]\n    }\n\n    params = getSelectedParams(parallelRoute, params)\n  }\n\n  return params\n}\n", "/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/\nexport class PromiseQueue {\n  #maxConcurrency: number\n  #runningCount: number\n  #queue: Array<{\n    promiseFn: Promise<any>\n    task: () => void\n  }>\n\n  constructor(maxConcurrency = 5) {\n    this.#maxConcurrency = maxConcurrency\n    this.#runningCount = 0\n    this.#queue = []\n  }\n\n  enqueue<T>(promiseFn: () => Promise<T>): Promise<T> {\n    let taskResolve: (value: T | PromiseLike<T>) => void\n    let taskReject: (reason?: any) => void\n\n    const taskPromise = new Promise((resolve, reject) => {\n      taskResolve = resolve\n      taskReject = reject\n    }) as Promise<T>\n\n    const task = async () => {\n      try {\n        this.#runningCount++\n        const result = await promiseFn()\n        taskResolve(result)\n      } catch (error) {\n        taskReject(error)\n      } finally {\n        this.#runningCount--\n        this.#processNext()\n      }\n    }\n\n    const enqueueResult = { promiseFn: taskPromise, task }\n    // wonder if we should take a LIFO approach here\n    this.#queue.push(enqueueResult)\n    this.#processNext()\n\n    return taskPromise\n  }\n\n  bump(promiseFn: Promise<any>) {\n    const index = this.#queue.findIndex((item) => item.promiseFn === promiseFn)\n\n    if (index > -1) {\n      const bumpedItem = this.#queue.splice(index, 1)[0]\n      this.#queue.unshift(bumpedItem)\n      this.#processNext(true)\n    }\n  }\n\n  #processNext(forced = false) {\n    if (\n      (this.#runningCount < this.#maxConcurrency || forced) &&\n      this.#queue.length > 0\n    ) {\n      this.#queue.shift()?.task()\n    }\n  }\n}\n", "import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n  type NavigateAction,\n  ACTION_HMR_REFRESH,\n  PrefetchKind,\n  ACTION_PREFETCH,\n} from './router-reducer/router-reducer-types'\nimport { reducer } from './router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport { prefetch as prefetchWithSegmentCache } from './segment-cache'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { addBasePath } from '../add-base-path'\nimport { createPrefetchURL, isExternalURL } from './app-router'\nimport { prefetchReducer } from './router-reducer/reducers/prefetch-reducer'\nimport type {\n  AppRouterInstance,\n  NavigateOptions,\n  PrefetchOptions,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { setLinkForCurrentNavigation, type LinkInstance } from './links'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport type { ClientInstrumentationHooks } from '../app-index'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n\n  onRouterTransitionStart:\n    | ((url: string, type: 'push' | 'replace' | 'traverse') => void)\n    | null\n\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // The rest of the current queue should still execute after this navigation.\n    // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n    newAction.next = actionQueue.pending.next\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nlet globalActionQueue: AppRouterActionQueue | null = null\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState,\n  instrumentationHooks: ClientInstrumentationHooks | null\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n    onRouterTransitionStart:\n      instrumentationHooks !== null &&\n      typeof instrumentationHooks.onRouterTransitionStart === 'function'\n        ? // This profiling hook will be called at the start of every navigation.\n          instrumentationHooks.onRouterTransitionStart\n        : null,\n  }\n\n  if (typeof window !== 'undefined') {\n    // The action queue is lazily created on hydration, but after that point\n    // it doesn't change. So we can store it in a global rather than pass\n    // it around everywhere via props/context.\n    if (globalActionQueue !== null) {\n      throw new Error(\n        'Internal Next.js Error: createMutableActionQueue was called more ' +\n          'than once'\n      )\n    }\n    globalActionQueue = actionQueue\n  }\n\n  return actionQueue\n}\n\nexport function getCurrentAppRouterState(): AppRouterState | null {\n  return globalActionQueue !== null ? globalActionQueue.state : null\n}\n\nfunction getAppRouterActionQueue(): AppRouterActionQueue {\n  if (globalActionQueue === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  return globalActionQueue\n}\n\nfunction getProfilingHookForOnNavigationStart() {\n  if (globalActionQueue !== null) {\n    return globalActionQueue.onRouterTransitionStart\n  }\n  return null\n}\n\nexport function dispatchNavigateAction(\n  href: string,\n  navigateType: NavigateAction['navigateType'],\n  shouldScroll: boolean,\n  linkInstanceRef: LinkInstance | null\n): void {\n  // TODO: This stuff could just go into the reducer. Leaving as-is for now\n  // since we're about to rewrite all the router reducer stuff anyway.\n  const url = new URL(addBasePath(href), location.href)\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    window.next.__pendingUrl = url\n  }\n\n  setLinkForCurrentNavigation(linkInstanceRef)\n\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, navigateType)\n  }\n\n  dispatchAppRouterAction({\n    type: ACTION_NAVIGATE,\n    url,\n    isExternalUrl: isExternalURL(url),\n    locationSearch: location.search,\n    shouldScroll,\n    navigateType,\n    allowAliasing: true,\n  })\n}\n\nexport function dispatchTraverseAction(\n  href: string,\n  tree: FlightRouterState | undefined\n) {\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, 'traverse')\n  }\n  dispatchAppRouterAction({\n    type: ACTION_RESTORE,\n    url: new URL(href),\n    tree,\n  })\n}\n\n/**\n * The app router that is exposed through `useRouter`. These are public API\n * methods. Internal Next.js code should call the lower level methods directly\n * (although there's lots of existing code that doesn't do that).\n */\nexport const publicAppRouterInstance: AppRouterInstance = {\n  back: () => window.history.back(),\n  forward: () => window.history.forward(),\n  prefetch: process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? // Unlike the old implementation, the Segment Cache doesn't store its\n      // data in the router reducer state; it writes into a global mutable\n      // cache. So we don't need to dispatch an action.\n      (href: string, options?: PrefetchOptions) => {\n        const actionQueue = getAppRouterActionQueue()\n        prefetchWithSegmentCache(\n          href,\n          actionQueue.state.nextUrl,\n          actionQueue.state.tree,\n          options?.kind === PrefetchKind.FULL\n        )\n      }\n    : (href: string, options?: PrefetchOptions) => {\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue()\n        const url = createPrefetchURL(href)\n        if (url !== null) {\n          // The prefetch reducer doesn't actually update any state or\n          // trigger a rerender. It just writes to a mutable cache. So we\n          // shouldn't bother calling setState/dispatch; we can just re-run\n          // the reducer directly using the current state.\n          // TODO: Refactor this away from a \"reducer\" so it's\n          // less confusing.\n          prefetchReducer(actionQueue.state, {\n            type: ACTION_PREFETCH,\n            url,\n            kind: options?.kind ?? PrefetchKind.FULL,\n          })\n        }\n      },\n  replace: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'replace', options?.scroll ?? true, null)\n    })\n  },\n  push: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'push', options?.scroll ?? true, null)\n    })\n  },\n  refresh: () => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_REFRESH,\n        origin: window.location.origin,\n      })\n    })\n  },\n  hmrRefresh: () => {\n    if (process.env.NODE_ENV !== 'development') {\n      throw new Error(\n        'hmrRefresh can only be used in development mode. Please use refresh instead.'\n      )\n    } else {\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_HMR_REFRESH,\n          origin: window.location.origin,\n        })\n      })\n    }\n  },\n}\n\n// Exists for debugging purposes. Don't use in application code.\nif (typeof window !== 'undefined' && window.next) {\n  window.next.router = publicAppRouterInstance\n}\n", "import { computeChangedPath } from './compute-changed-path'\nimport type {\n  Mu<PERSON>,\n  ReadonlyReducerState,\n  ReducerState,\n} from './router-reducer-types'\n\nfunction isNotUndefined<T>(value: T): value is Exclude<T, undefined> {\n  return typeof value !== 'undefined'\n}\n\nexport function handleMutable(\n  state: ReadonlyReducerState,\n  mutable: Mutable\n): ReducerState {\n  // shouldScroll is true by default, can override to false.\n  const shouldScroll = mutable.shouldScroll ?? true\n\n  let nextUrl = state.nextUrl\n\n  if (isNotUndefined(mutable.patchedTree)) {\n    // If we received a patched tree, we need to compute the changed path.\n    const changedPath = computeChangedPath(state.tree, mutable.patchedTree)\n    if (changedPath) {\n      // If the tree changed, we need to update the nextUrl\n      nextUrl = changedPath\n    } else if (!nextUrl) {\n      // if the tree ends up being the same (ie, no changed path), and we don't have a nextUrl, then we should use the canonicalUrl\n      nextUrl = state.canonicalUrl\n    }\n    // otherwise this will be a no-op and continue to use the existing nextUrl\n  }\n\n  return {\n    // Set href.\n    canonicalUrl: isNotUndefined(mutable.canonicalUrl)\n      ? mutable.canonicalUrl === state.canonicalUrl\n        ? state.canonicalUrl\n        : mutable.canonicalUrl\n      : state.canonicalUrl,\n    pushRef: {\n      pendingPush: isNotUndefined(mutable.pendingPush)\n        ? mutable.pendingPush\n        : state.pushRef.pendingPush,\n      mpaNavigation: isNotUndefined(mutable.mpaNavigation)\n        ? mutable.mpaNavigation\n        : state.pushRef.mpaNavigation,\n      preserveCustomHistoryState: isNotUndefined(\n        mutable.preserveCustomHistoryState\n      )\n        ? mutable.preserveCustomHistoryState\n        : state.pushRef.preserveCustomHistoryState,\n    },\n    // All navigation requires scroll and focus management to trigger.\n    focusAndScrollRef: {\n      apply: shouldScroll\n        ? isNotUndefined(mutable?.scrollableSegments)\n          ? true\n          : state.focusAndScrollRef.apply\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          false,\n      onlyHashChange: mutable.onlyHashChange || false,\n      hashFragment: shouldScroll\n        ? // Empty hash should trigger default behavior of scrolling layout into view.\n          // #top is handled in layout-router.\n          mutable.hashFragment && mutable.hashFragment !== ''\n          ? // Remove leading # and decode hash to make non-latin hashes work.\n            decodeURIComponent(mutable.hashFragment.slice(1))\n          : state.focusAndScrollRef.hashFragment\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          null,\n      segmentPaths: shouldScroll\n        ? mutable?.scrollableSegments ?? state.focusAndScrollRef.segmentPaths\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          [],\n    },\n    // Apply cache.\n    cache: mutable.cache ? mutable.cache : state.cache,\n    prefetchCache: mutable.prefetchCache\n      ? mutable.prefetchCache\n      : state.prefetchCache,\n    // Apply patched router state.\n    tree: isNotUndefined(mutable.patchedTree)\n      ? mutable.patchedTree\n      : state.tree,\n    nextUrl,\n  }\n}\n", "export default {\n  outline: {\n    xmlns: 'http://www.w3.org/2000/svg',\n    width: 24,\n    height: 24,\n    viewBox: '0 0 24 24',\n    fill: 'none',\n    stroke: 'currentColor',\n    strokeWidth: 2,\n    strokeLinecap: 'round',\n    strokeLinejoin: 'round',\n  },\n  filled: {\n    xmlns: 'http://www.w3.org/2000/svg',\n    width: 24,\n    height: 24,\n    viewBox: '0 0 24 24',\n    fill: 'currentColor',\n    stroke: 'none',\n  },\n};\n", "import { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport type { IconNode, IconProps, Icon } from './types';\n\nconst createReactComponent = (\n  type: 'outline' | 'filled',\n  iconName: string,\n  iconNamePascal: string,\n  iconNode: IconNode,\n) => {\n  const Component = forwardRef<Icon, IconProps>(\n    (\n      { color = 'currentColor', size = 24, stroke = 2, title, className, children, ...rest }: IconProps,\n      ref,\n    ) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes[type],\n          width: size,\n          height: size,\n          className: [`tabler-icon`, `tabler-icon-${iconName}`, className].join(' '),\n          ...(type === 'filled'\n            ? {\n                fill: color,\n              }\n            : {\n                strokeWidth: stroke,\n                stroke: color,\n              }),\n          ...rest,\n        },\n        [\n          title && createElement('title', { key: 'svg-title' }, title),\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ],\n      ),\n  );\n\n  Component.displayName = `${iconNamePascal}`;\n\n  return Component;\n};\n\nexport default createReactComponent;\n", "import type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { matchSegment } from '../match-segments'\nimport { addRefreshMarkerToActiveParallelSegments } from './refetch-inactive-parallel-segments'\n\n/**\n * Deep merge of the two router states. Parallel route keys are preserved if the patch doesn't have them.\n */\nfunction applyPatch(\n  initialTree: FlightRouterState,\n  patchTree: FlightRouterState\n): FlightRouterState {\n  const [initialSegment, initialParallelRoutes] = initialTree\n  const [patchSegment, patchParallelRoutes] = patchTree\n\n  // if the applied patch segment is __DEFAULT__ then it can be ignored in favor of the initial tree\n  // this is because the __DEFAULT__ segment is used as a placeholder on navigation\n  if (\n    patchSegment === DEFAULT_SEGMENT_KEY &&\n    initialSegment !== DEFAULT_SEGMENT_KEY\n  ) {\n    return initialTree\n  }\n\n  if (matchSegment(initialSegment, patchSegment)) {\n    const newParallelRoutes: FlightRouterState[1] = {}\n    for (const key in initialParallelRoutes) {\n      const isInPatchTreeParallelRoutes =\n        typeof patchParallelRoutes[key] !== 'undefined'\n      if (isInPatchTreeParallelRoutes) {\n        newParallelRoutes[key] = applyPatch(\n          initialParallelRoutes[key],\n          patchParallelRoutes[key]\n        )\n      } else {\n        newParallelRoutes[key] = initialParallelRoutes[key]\n      }\n    }\n\n    for (const key in patchParallelRoutes) {\n      if (newParallelRoutes[key]) {\n        continue\n      }\n\n      newParallelRoutes[key] = patchParallelRoutes[key]\n    }\n\n    const tree: FlightRouterState = [initialSegment, newParallelRoutes]\n\n    // Copy over the existing tree\n    if (initialTree[2]) {\n      tree[2] = initialTree[2]\n    }\n\n    if (initialTree[3]) {\n      tree[3] = initialTree[3]\n    }\n\n    if (initialTree[4]) {\n      tree[4] = initialTree[4]\n    }\n\n    return tree\n  }\n\n  return patchTree\n}\n\n/**\n * Apply the router state from the Flight response, but skip patching default segments.\n * Useful for patching the router cache when navigating, where we persist the existing default segment if there isn't a new one.\n * Creates a new router state tree.\n */\nexport function applyRouterStatePatchToTree(\n  flightSegmentPath: FlightSegmentPath,\n  flightRouterState: FlightRouterState,\n  treePatch: FlightRouterState,\n  path: string\n): FlightRouterState | null {\n  const [segment, parallelRoutes, url, refetch, isRootLayout] =\n    flightRouterState\n\n  // Root refresh\n  if (flightSegmentPath.length === 1) {\n    const tree: FlightRouterState = applyPatch(flightRouterState, treePatch)\n\n    addRefreshMarkerToActiveParallelSegments(tree, path)\n\n    return tree\n  }\n\n  const [currentSegment, parallelRouteKey] = flightSegmentPath\n\n  // Tree path returned from the server should always match up with the current tree in the browser\n  if (!matchSegment(currentSegment, segment)) {\n    return null\n  }\n\n  const lastSegment = flightSegmentPath.length === 2\n\n  let parallelRoutePatch\n  if (lastSegment) {\n    parallelRoutePatch = applyPatch(parallelRoutes[parallelRouteKey], treePatch)\n  } else {\n    parallelRoutePatch = applyRouterStatePatchToTree(\n      getNextFlightSegmentPath(flightSegmentPath),\n      parallelRoutes[parallelRouteKey],\n      treePatch,\n      path\n    )\n\n    if (parallelRoutePatch === null) {\n      return null\n    }\n  }\n\n  const tree: FlightRouterState = [\n    flightSegmentPath[0],\n    {\n      ...parallelRoutes,\n      [parallelRouteKey]: parallelRoutePatch,\n    },\n    url,\n    refetch,\n  ]\n\n  // Current segment is the root layout\n  if (isRootLayout) {\n    tree[4] = true\n  }\n\n  addRefreshMarkerToActiveParallelSegments(tree, path)\n\n  return tree\n}\n", "import type { FlightRouterState } from '../../server/app-render/types'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { getCurrentAppRouterState } from './app-router-instance'\nimport { createPrefetchURL } from './app-router'\nimport { PrefetchKind } from './router-reducer/router-reducer-types'\nimport { getCurrentCacheVersion } from './segment-cache'\nimport { createCacheKey } from './segment-cache'\nimport {\n  type PrefetchTask,\n  PrefetchPriority,\n  schedulePrefetchTask as scheduleSegmentPrefetchTask,\n  cancelPrefetchTask,\n  reschedulePrefetchTask,\n} from './segment-cache'\nimport { startTransition } from 'react'\n\ntype LinkElement = HTMLAnchorElement | SVGAElement\n\ntype Element = LinkElement | HTMLFormElement\n\n// Properties that are shared between Link and Form instances. We use the same\n// shape for both to prevent a polymorphic de-opt in the VM.\ntype LinkOrFormInstanceShared = {\n  router: AppRouterInstance\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n\n  isVisible: boolean\n  wasHoveredOrTouched: boolean\n\n  // The most recently initiated prefetch task. It may or may not have\n  // already completed.  The same prefetch task object can be reused across\n  // multiple prefetches of the same link.\n  prefetchTask: PrefetchTask | null\n\n  // The cache version at the time the task was initiated. This is used to\n  // determine if the cache was invalidated since the task was initiated.\n  cacheVersion: number\n}\n\nexport type FormInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: null\n}\n\ntype PrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype NonPrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: null\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype PrefetchableInstance = PrefetchableLinkInstance | FormInstance\n\nexport type LinkInstance =\n  | PrefetchableLinkInstance\n  | NonPrefetchableLinkInstance\n\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation: LinkInstance | null = null\n\n// Status object indicating link is pending\nexport const PENDING_LINK_STATUS = { pending: true }\n\n// Status object indicating link is idle\nexport const IDLE_LINK_STATUS = { pending: false }\n\n// Updates the loading state when navigating between links\n// - Resets the previous link's loading state\n// - Sets the new link's loading state\n// - Updates tracking of current navigation\nexport function setLinkForCurrentNavigation(link: LinkInstance | null) {\n  startTransition(() => {\n    linkForMostRecentNavigation?.setOptimisticLinkStatus(IDLE_LINK_STATUS)\n    link?.setOptimisticLinkStatus(PENDING_LINK_STATUS)\n    linkForMostRecentNavigation = link\n  })\n}\n\n// Unmounts the current link instance from navigation tracking\nexport function unmountLinkForCurrentNavigation(link: LinkInstance) {\n  if (linkForMostRecentNavigation === link) {\n    linkForMostRecentNavigation = null\n  }\n}\n\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable:\n  | WeakMap<Element, PrefetchableInstance>\n  | Map<Element, PrefetchableInstance> =\n  typeof WeakMap === 'function' ? new WeakMap() : new Map()\n\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible: Set<PrefetchableInstance> = new Set()\n\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer: IntersectionObserver | null =\n  typeof IntersectionObserver === 'function'\n    ? new IntersectionObserver(handleIntersect, {\n        rootMargin: '200px',\n      })\n    : null\n\nfunction observeVisibility(element: Element, instance: PrefetchableInstance) {\n  const existingInstance = prefetchable.get(element)\n  if (existingInstance !== undefined) {\n    // This shouldn't happen because each <Link> component should have its own\n    // anchor tag instance, but it's defensive coding to avoid a memory leak in\n    // case there's a logical error somewhere else.\n    unmountPrefetchableInstance(element)\n  }\n  // Only track prefetchable links that have a valid prefetch URL\n  prefetchable.set(element, instance)\n  if (observer !== null) {\n    observer.observe(element)\n  }\n}\n\nfunction coercePrefetchableUrl(href: string): URL | null {\n  try {\n    return createPrefetchURL(href)\n  } catch {\n    // createPrefetchURL sometimes throws an error if an invalid URL is\n    // provided, though I'm not sure if it's actually necessary.\n    // TODO: Consider removing the throw from the inner function, or change it\n    // to reportError. Or maybe the error isn't even necessary for automatic\n    // prefetches, just navigations.\n    const reportErrorFn =\n      typeof reportError === 'function' ? reportError : console.error\n    reportErrorFn(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n    return null\n  }\n}\n\nexport function mountLinkInstance(\n  element: LinkElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL,\n  prefetchEnabled: boolean,\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n): LinkInstance {\n  if (prefetchEnabled) {\n    const prefetchURL = coercePrefetchableUrl(href)\n    if (prefetchURL !== null) {\n      const instance: PrefetchableLinkInstance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus,\n      }\n      // We only observe the link's visibility if it's prefetchable. For\n      // example, this excludes links to external URLs.\n      observeVisibility(element, instance)\n      return instance\n    }\n  }\n  // If the link is not prefetchable, we still create an instance so we can\n  // track its optimistic state (i.e. useLinkStatus).\n  const instance: NonPrefetchableLinkInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: null,\n    setOptimisticLinkStatus,\n  }\n  return instance\n}\n\nexport function mountFormInstance(\n  element: HTMLFormElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n): void {\n  const prefetchURL = coercePrefetchableUrl(href)\n  if (prefetchURL === null) {\n    // This href is not prefetchable, so we don't track it.\n    // TODO: We currently observe/unobserve a form every time its href changes.\n    // For Links, this isn't a big deal because the href doesn't usually change,\n    // but for forms it's extremely common. We should optimize this.\n    return\n  }\n  const instance: FormInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: prefetchURL.href,\n    setOptimisticLinkStatus: null,\n  }\n  observeVisibility(element, instance)\n}\n\nexport function unmountPrefetchableInstance(element: Element) {\n  const instance = prefetchable.get(element)\n  if (instance !== undefined) {\n    prefetchable.delete(element)\n    prefetchableAndVisible.delete(instance)\n    const prefetchTask = instance.prefetchTask\n    if (prefetchTask !== null) {\n      cancelPrefetchTask(prefetchTask)\n    }\n  }\n  if (observer !== null) {\n    observer.unobserve(element)\n  }\n}\n\nfunction handleIntersect(entries: Array<IntersectionObserverEntry>) {\n  for (const entry of entries) {\n    // Some extremely old browsers or polyfills don't reliably support\n    // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n    // really. But whatever this is fine.)\n    const isVisible = entry.intersectionRatio > 0\n    onLinkVisibilityChanged(entry.target as HTMLAnchorElement, isVisible)\n  }\n}\n\nexport function onLinkVisibilityChanged(element: Element, isVisible: boolean) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Prefetching on viewport is disabled in development for performance\n    // reasons, because it requires compiling the target page.\n    // TODO: Investigate re-enabling this.\n    return\n  }\n\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n\n  instance.isVisible = isVisible\n  if (isVisible) {\n    prefetchableAndVisible.add(instance)\n  } else {\n    prefetchableAndVisible.delete(instance)\n  }\n  rescheduleLinkPrefetch(instance)\n}\n\nexport function onNavigationIntent(\n  element: HTMLAnchorElement | SVGAElement,\n  unstable_upgradeToDynamicPrefetch: boolean\n) {\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n  // Prefetch the link on hover/touchstart.\n  if (instance !== undefined) {\n    instance.wasHoveredOrTouched = true\n    if (\n      process.env.__NEXT_DYNAMIC_ON_HOVER &&\n      unstable_upgradeToDynamicPrefetch\n    ) {\n      // Switch to a full, dynamic prefetch\n      instance.kind = PrefetchKind.FULL\n    }\n    rescheduleLinkPrefetch(instance)\n  }\n}\n\nfunction rescheduleLinkPrefetch(instance: PrefetchableInstance) {\n  const existingPrefetchTask = instance.prefetchTask\n\n  if (!instance.isVisible) {\n    // Cancel any in-progress prefetch task. (If it already finished then this\n    // is a no-op.)\n    if (existingPrefetchTask !== null) {\n      cancelPrefetchTask(existingPrefetchTask)\n    }\n    // We don't need to reset the prefetchTask to null upon cancellation; an\n    // old task object can be rescheduled with reschedulePrefetchTask. This is a\n    // micro-optimization but also makes the code simpler (don't need to\n    // worry about whether an old task object is stale).\n    return\n  }\n\n  if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // The old prefetch implementation does not have different priority levels.\n    // Just schedule a new prefetch task.\n    prefetchWithOldCacheImplementation(instance)\n    return\n  }\n\n  // In the Segment Cache implementation, we assign a higher priority level to\n  // links that were at one point hovered or touched. Since the queue is last-\n  // in-first-out, the highest priority Link is whichever one was hovered last.\n  //\n  // We also increase the relative priority of links whenever they re-enter the\n  // viewport, as if they were being scheduled for the first time.\n  const priority = instance.wasHoveredOrTouched\n    ? PrefetchPriority.Intent\n    : PrefetchPriority.Default\n  const appRouterState = getCurrentAppRouterState()\n  if (appRouterState !== null) {\n    const treeAtTimeOfPrefetch = appRouterState.tree\n    if (existingPrefetchTask === null) {\n      // Initiate a prefetch task.\n      const nextUrl = appRouterState.nextUrl\n      const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n      instance.prefetchTask = scheduleSegmentPrefetchTask(\n        cacheKey,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    } else {\n      // We already have an old task object that we can reschedule. This is\n      // effectively the same as canceling the old task and creating a new one.\n      reschedulePrefetchTask(\n        existingPrefetchTask,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    }\n\n    // Keep track of the cache version at the time the prefetch was requested.\n    // This is used to check if the prefetch is stale.\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nexport function pingVisibleLinks(\n  nextUrl: string | null,\n  tree: FlightRouterState\n) {\n  // For each currently visible link, cancel the existing prefetch task (if it\n  // exists) and schedule a new one. This is effectively the same as if all the\n  // visible links left and then re-entered the viewport.\n  //\n  // This is called when the Next-Url or the base tree changes, since those\n  // may affect the result of a prefetch task. It's also called after a\n  // cache invalidation.\n  const currentCacheVersion = getCurrentCacheVersion()\n  for (const instance of prefetchableAndVisible) {\n    const task = instance.prefetchTask\n    if (\n      task !== null &&\n      instance.cacheVersion === currentCacheVersion &&\n      task.key.nextUrl === nextUrl &&\n      task.treeAtTimeOfPrefetch === tree\n    ) {\n      // The cache has not been invalidated, and none of the inputs have\n      // changed. Bail out.\n      continue\n    }\n    // Something changed. Cancel the existing prefetch task and schedule a\n    // new one.\n    if (task !== null) {\n      cancelPrefetchTask(task)\n    }\n    const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n    const priority = instance.wasHoveredOrTouched\n      ? PrefetchPriority.Intent\n      : PrefetchPriority.Default\n    instance.prefetchTask = scheduleSegmentPrefetchTask(\n      cacheKey,\n      tree,\n      instance.kind === PrefetchKind.FULL,\n      priority\n    )\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nfunction prefetchWithOldCacheImplementation(instance: PrefetchableInstance) {\n  // This is the path used when the Segment Cache is not enabled.\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return instance.router.prefetch(instance.prefetchHref, {\n      kind: instance.kind,\n    })\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n", "import type { FlightRouterState } from '../../../server/app-render/types'\nimport { handleExternalUrl } from './reducers/navigate-reducer'\nimport type {\n  ReadonlyReducerState,\n  ReducerActions,\n} from './router-reducer-types'\n\n/**\n * Handles the case where the client router attempted to patch the tree but, due to a mismatch, the patch failed.\n * This will perform an MPA navigation to return the router to a valid state.\n */\nexport function handleSegmentMismatch(\n  state: ReadonlyReducerState,\n  action: ReducerActions,\n  treePatch: FlightRouterState\n) {\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\n      'Performing hard navigation because your application experienced an unrecoverable error. If this keeps occurring, please file a Next.js issue.\\n\\n' +\n        'Reason: Segment mismatch\\n' +\n        `Last Action: ${action.type}\\n\\n` +\n        `Current Tree: ${JSON.stringify(state.tree)}\\n\\n` +\n        `Tree Patch Payload: ${JSON.stringify(treePatch)}`\n    )\n  }\n\n  return handleExternalUrl(state, {}, state.canonicalUrl, true)\n}\n", "import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n} from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport {\n  addSearchParamsIfPageSegment,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\nimport { createEmptyCacheNode } from '../app-router'\nimport { applyRouterStatePatchToTree } from './apply-router-state-patch-to-tree'\nimport { createHrefFromUrl } from './create-href-from-url'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport { fillCacheWithNewSubTreeDataButOnlyLoading } from './fill-cache-with-new-subtree-data'\nimport { handleMutable } from './handle-mutable'\nimport type { Mutable, ReadonlyReducerState } from './router-reducer-types'\n\n/**\n * This is a stop-gap until per-segment caching is implemented. It leverages the `aliased` flag that is added\n * to prefetch entries when it's determined that the loading state from that entry should be used for this navigation.\n * This function takes the aliased entry and only applies the loading state to the updated cache node.\n * We should remove this once per-segment fetching is implemented as ideally the prefetch cache will contain a\n * more granular segment map and so the router will be able to simply re-use the loading segment for the new navigation.\n */\nexport function handleAliasedPrefetchEntry(\n  navigatedAt: number,\n  state: ReadonlyReducerState,\n  flightData: string | NormalizedFlightData[],\n  url: URL,\n  mutable: Mutable\n) {\n  let currentTree = state.tree\n  let currentCache = state.cache\n  const href = createHrefFromUrl(url)\n  let applied\n\n  if (typeof flightData === 'string') {\n    return false\n  }\n\n  for (const normalizedFlightData of flightData) {\n    // If the segment doesn't have a loading component, we don't need to do anything.\n    if (!hasLoadingComponentInSeedData(normalizedFlightData.seedData)) {\n      continue\n    }\n\n    let treePatch = normalizedFlightData.tree\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    const { seedData, isRootRender, pathToSegment } = normalizedFlightData\n    // TODO-APP: remove ''\n    const flightSegmentPathWithLeadingEmpty = ['', ...pathToSegment]\n\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    let newTree = applyRouterStatePatchToTree(\n      flightSegmentPathWithLeadingEmpty,\n      currentTree,\n      treePatch,\n      href\n    )\n\n    const newCache = createEmptyCacheNode()\n\n    // The prefetch cache entry was aliased -- this signals that we only fill in the cache with the\n    // loading state and not the actual parallel route seed data.\n    if (isRootRender && seedData) {\n      // Fill in the cache with the new loading / rsc data\n      const rsc = seedData[1]\n      const loading = seedData[3]\n      newCache.loading = loading\n      newCache.rsc = rsc\n\n      // Construct a new tree and apply the aliased loading state for each parallel route\n      fillNewTreeWithOnlyLoadingSegments(\n        navigatedAt,\n        newCache,\n        currentCache,\n        treePatch,\n        seedData\n      )\n    } else {\n      // Copy rsc for the root node of the cache.\n      newCache.rsc = currentCache.rsc\n      newCache.prefetchRsc = currentCache.prefetchRsc\n      newCache.loading = currentCache.loading\n      newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n      // copy the loading state only into the leaf node (the part that changed)\n      fillCacheWithNewSubTreeDataButOnlyLoading(\n        navigatedAt,\n        newCache,\n        currentCache,\n        normalizedFlightData\n      )\n    }\n\n    // If we don't have an updated tree, there's no reason to update the cache, as the tree\n    // dictates what cache nodes to render.\n    if (newTree) {\n      currentTree = newTree\n      currentCache = newCache\n      applied = true\n    }\n  }\n\n  if (!applied) {\n    return false\n  }\n\n  mutable.patchedTree = currentTree\n  mutable.cache = currentCache\n  mutable.canonicalUrl = href\n  mutable.hashFragment = url.hash\n\n  return handleMutable(state, mutable)\n}\n\nfunction hasLoadingComponentInSeedData(seedData: CacheNodeSeedData | null) {\n  if (!seedData) return false\n\n  const parallelRoutes = seedData[2]\n  const loading = seedData[3]\n\n  if (loading) {\n    return true\n  }\n\n  for (const key in parallelRoutes) {\n    if (hasLoadingComponentInSeedData(parallelRoutes[key])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction fillNewTreeWithOnlyLoadingSegments(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  routerState: FlightRouterState,\n  cacheNodeSeedData: CacheNodeSeedData | null\n) {\n  const isLastSegment = Object.keys(routerState[1]).length === 0\n  if (isLastSegment) {\n    return\n  }\n\n  for (const key in routerState[1]) {\n    const parallelRouteState = routerState[1][key]\n    const segmentForParallelRoute = parallelRouteState[0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n\n    const parallelSeedData =\n      cacheNodeSeedData !== null && cacheNodeSeedData[2][key] !== undefined\n        ? cacheNodeSeedData[2][key]\n        : null\n\n    let newCacheNode: CacheNode\n    if (parallelSeedData !== null) {\n      // New data was sent from the server.\n      const rsc = parallelSeedData[1]\n      const loading = parallelSeedData[3]\n      newCacheNode = {\n        lazyData: null,\n        // copy the layout but null the page segment as that's not meant to be used\n        rsc: segmentForParallelRoute.includes(PAGE_SEGMENT_KEY) ? null : rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading,\n        navigatedAt,\n      }\n    } else {\n      // No data available for this node. This will trigger a lazy fetch\n      // during render.\n      newCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      }\n    }\n\n    const existingParallelRoutes = newCache.parallelRoutes.get(key)\n    if (existingParallelRoutes) {\n      existingParallelRoutes.set(cacheKey, newCacheNode)\n    } else {\n      newCache.parallelRoutes.set(key, new Map([[cacheKey, newCacheNode]]))\n    }\n\n    fillNewTreeWithOnlyLoadingSegments(\n      navigatedAt,\n      newCacheNode,\n      existingCache,\n      parallelRouteState,\n      parallelSeedData\n    )\n  }\n}\n\n/**\n * Add search params to the page segments in the flight router state\n * Page segments that are associated with search params have a page segment key\n * followed by a query string. This function will add those params to the page segment.\n * This is useful if we return an aliased prefetch entry (ie, won't have search params)\n * but the canonical router URL has search params.\n */\nexport function addSearchParamsToPageSegments(\n  flightRouterState: FlightRouterState,\n  searchParams: Record<string, string | string[] | undefined>\n): FlightRouterState {\n  const [segment, parallelRoutes, ...rest] = flightRouterState\n\n  // If it's a page segment, modify the segment by adding search params\n  if (segment.includes(PAGE_SEGMENT_KEY)) {\n    const newSegment = addSearchParamsIfPageSegment(segment, searchParams)\n    return [newSegment, parallelRoutes, ...rest]\n  }\n\n  // Otherwise, recurse through the parallel routes and return a new tree\n  const updatedParallelRoutes: { [key: string]: FlightRouterState } = {}\n\n  for (const [key, parallelRoute] of Object.entries(parallelRoutes)) {\n    updatedParallelRoutes[key] = addSearchParamsToPageSegments(\n      parallelRoute,\n      searchParams\n    )\n  }\n\n  return [segment, updatedParallelRoutes, ...rest]\n}\n", "/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { Segment } from '../../../server/app-render/types'\nimport { invalidateCacheByRouterState } from './invalidate-cache-by-router-state'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport type { PrefetchCacheEntry } from './router-reducer-types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\n\n/**\n * Common logic for filling cache with new sub tree data.\n */\nfunction fillCacheHelper(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry: PrefetchCacheEntry | undefined,\n  fillLazyItems: boolean\n): void {\n  const {\n    segmentPath,\n    seedData: cacheNodeSeedData,\n    tree: treePatch,\n    head,\n  } = flightData\n  let newCacheNode = newCache\n  let existingCacheNode = existingCache\n\n  for (let i = 0; i < segmentPath.length; i += 2) {\n    const parallelRouteKey: string = segmentPath[i]\n    const segment: Segment = segmentPath[i + 1]\n\n    // segmentPath is a repeating tuple of parallelRouteKey and segment\n    // we know we've hit the last entry we've reached our final pair\n    const isLastEntry = i === segmentPath.length - 2\n    const cacheKey = createRouterCacheKey(segment)\n\n    const existingChildSegmentMap =\n      existingCacheNode.parallelRoutes.get(parallelRouteKey)\n\n    if (!existingChildSegmentMap) {\n      // Bailout because the existing cache does not have the path to the leaf node\n      // Will trigger lazy fetch in layout-router because of missing segment\n      continue\n    }\n\n    let childSegmentMap = newCacheNode.parallelRoutes.get(parallelRouteKey)\n    if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n      childSegmentMap = new Map(existingChildSegmentMap)\n      newCacheNode.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n    }\n\n    const existingChildCacheNode = existingChildSegmentMap.get(cacheKey)\n    let childCacheNode = childSegmentMap.get(cacheKey)\n\n    if (isLastEntry) {\n      if (\n        cacheNodeSeedData &&\n        (!childCacheNode ||\n          !childCacheNode.lazyData ||\n          childCacheNode === existingChildCacheNode)\n      ) {\n        const incomingSegment = cacheNodeSeedData[0]\n        const rsc = cacheNodeSeedData[1]\n        const loading = cacheNodeSeedData[3]\n\n        childCacheNode = {\n          lazyData: null,\n          // When `fillLazyItems` is false, we only want to fill the RSC data for the layout,\n          // not the page segment.\n          rsc:\n            fillLazyItems || incomingSegment !== PAGE_SEGMENT_KEY ? rsc : null,\n          prefetchRsc: null,\n          head: null,\n          prefetchHead: null,\n          loading,\n          parallelRoutes:\n            fillLazyItems && existingChildCacheNode\n              ? new Map(existingChildCacheNode.parallelRoutes)\n              : new Map(),\n          navigatedAt,\n        }\n\n        if (existingChildCacheNode && fillLazyItems) {\n          invalidateCacheByRouterState(\n            childCacheNode,\n            existingChildCacheNode,\n            treePatch\n          )\n        }\n        if (fillLazyItems) {\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            childCacheNode,\n            existingChildCacheNode,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            prefetchEntry\n          )\n        }\n\n        childSegmentMap.set(cacheKey, childCacheNode)\n      }\n      continue\n    }\n\n    if (!childCacheNode || !existingChildCacheNode) {\n      // Bailout because the existing cache does not have the path to the leaf node\n      // Will trigger lazy fetch in layout-router because of missing segment\n      continue\n    }\n\n    if (childCacheNode === existingChildCacheNode) {\n      childCacheNode = {\n        lazyData: childCacheNode.lazyData,\n        rsc: childCacheNode.rsc,\n        prefetchRsc: childCacheNode.prefetchRsc,\n        head: childCacheNode.head,\n        prefetchHead: childCacheNode.prefetchHead,\n        parallelRoutes: new Map(childCacheNode.parallelRoutes),\n        loading: childCacheNode.loading,\n      } as CacheNode\n      childSegmentMap.set(cacheKey, childCacheNode)\n    }\n\n    // Move deeper into the cache nodes\n    newCacheNode = childCacheNode\n    existingCacheNode = existingChildCacheNode\n  }\n}\n\n/**\n * Fill cache with rsc based on flightDataPath\n */\nexport function fillCacheWithNewSubTreeData(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): void {\n  fillCacheHelper(\n    navigatedAt,\n    newCache,\n    existingCache,\n    flightData,\n    prefetchEntry,\n    true\n  )\n}\n\nexport function fillCacheWithNewSubTreeDataButOnlyLoading(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): void {\n  fillCacheHelper(\n    navigatedAt,\n    newCache,\n    existingCache,\n    flightData,\n    prefetchEntry,\n    false\n  )\n}\n", "'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n", "// packages/react/compose-refs/src/composeRefs.tsx\nimport * as React from \"react\";\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\nexport {\n  composeRefs,\n  useComposedRefs\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/slot/src/slot.tsx\nimport * as React from \"react\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { Fragment as Fragment2, jsx } from \"react/jsx-runtime\";\nvar Slot = React.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = React.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (React.Children.count(newElement) > 1) return React.Children.only(null);\n        return React.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ jsx(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = React.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (React.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    const props2 = mergeProps(slotProps, children.props);\n    if (children.type !== React.Fragment) {\n      props2.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n    }\n    return React.cloneElement(children, props2);\n  }\n  return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ jsx(Fragment2, { children });\n};\nfunction isSlottable(child) {\n  return React.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\nexport {\n  Root,\n  Slot,\n  Slottable\n};\n//# sourceMappingURL=index.mjs.map\n", "import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../../../server/app-render/types'\nimport type {\n  CacheNode,\n  ChildSegmentMap,\n  HeadData,\n  LoadingModuleData,\n  ReadyCacheNode,\n} from '../../../shared/lib/app-router-context.shared-runtime'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport type { FetchServerResponseResult } from './fetch-server-response'\nimport { isNavigatingToNewRootLayout } from './is-navigating-to-new-root-layout'\nimport { DYNAMIC_STALETIME_MS } from './prefetch-cache-utils'\n\n// This is yet another tree type that is used to track pending promises that\n// need to be fulfilled once the dynamic data is received. The terminal nodes of\n// this tree represent the new Cache Node trees that were created during this\n// request. We can't use the Cache Node tree or Route State tree directly\n// because those include reused nodes, too. This tree is discarded as soon as\n// the navigation response is received.\ntype SPANavigationTask = {\n  // The router state that corresponds to the tree that this Task represents.\n  route: FlightRouterState\n  // The CacheNode that corresponds to the tree that this Task represents. If\n  // `children` is null (i.e. if this is a terminal task node), then `node`\n  // represents a brand new Cache Node tree, which way or may not need to be\n  // filled with dynamic data from the server.\n  node: CacheNode | null\n  // The tree sent to the server during the dynamic request. This is the\n  // same as `route`, except with the `refetch` marker set on dynamic segments.\n  // If all the segments are static, then this will be null, and no server\n  // request is required.\n  dynamicRequestTree: FlightRouterState | null\n  children: Map<string, SPANavigationTask> | null\n}\n\n// A special type used to bail out and trigger a full-page navigation.\ntype MPANavigationTask = {\n  // MPA tasks are distinguised from SPA tasks by having a null `route`.\n  route: null\n  node: null\n  dynamicRequestTree: null\n  children: null\n}\n\nconst MPA_NAVIGATION_TASK: MPANavigationTask = {\n  route: null,\n  node: null,\n  dynamicRequestTree: null,\n  children: null,\n}\n\nexport type Task = SPANavigationTask | MPANavigationTask\n\n// Creates a new Cache Node tree (i.e. copy-on-write) that represents the\n// optimistic result of a navigation, using both the current Cache Node tree and\n// data that was prefetched prior to navigation.\n//\n// At the moment we call this function, we haven't yet received the navigation\n// response from the server. It could send back something completely different\n// from the tree that was prefetched — due to rewrites, default routes, parallel\n// routes, etc.\n//\n// But in most cases, it will return the same tree that we prefetched, just with\n// the dynamic holes filled in. So we optimistically assume this will happen,\n// and accept that the real result could be arbitrarily different.\n//\n// We'll reuse anything that was already in the previous tree, since that's what\n// the server does.\n//\n// New segments (ones that don't appear in the old tree) are assigned an\n// unresolved promise. The data for these promises will be fulfilled later, when\n// the navigation response is received.\n//\n// The tree can be rendered immediately after it is created (that's why this is\n// a synchronous function). Any new trees that do not have prefetch data will\n// suspend during rendering, until the dynamic data streams in.\n//\n// Returns a Task object, which contains both the updated Cache Node and a path\n// to the pending subtrees that need to be resolved by the navigation response.\n//\n// A return value of `null` means there were no changes, and the previous tree\n// can be reused without initiating a server request.\nexport function startPPRNavigation(\n  navigatedAt: number,\n  oldCacheNode: CacheNode,\n  oldRouterState: FlightRouterState,\n  newRouterState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  isSamePageNavigation: boolean,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task | null {\n  const segmentPath: Array<FlightSegmentPath> = []\n  return updateCacheNodeOnNavigation(\n    navigatedAt,\n    oldCacheNode,\n    oldRouterState,\n    newRouterState,\n    false,\n    prefetchData,\n    prefetchHead,\n    isPrefetchHeadPartial,\n    isSamePageNavigation,\n    segmentPath,\n    scrollableSegmentsResult\n  )\n}\n\nfunction updateCacheNodeOnNavigation(\n  navigatedAt: number,\n  oldCacheNode: CacheNode,\n  oldRouterState: FlightRouterState,\n  newRouterState: FlightRouterState,\n  didFindRootLayout: boolean,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  isSamePageNavigation: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task | null {\n  // Diff the old and new trees to reuse the shared layouts.\n  const oldRouterStateChildren = oldRouterState[1]\n  const newRouterStateChildren = newRouterState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  if (!didFindRootLayout) {\n    // We're currently traversing the part of the tree that was also part of\n    // the previous route. If we discover a root layout, then we don't need to\n    // trigger an MPA navigation. See beginRenderingNewRouteTree for context.\n    const isRootLayout = newRouterState[4] === true\n    if (isRootLayout) {\n      // Found a matching root layout.\n      didFindRootLayout = true\n    }\n  }\n\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n\n  // Clone the current set of segment children, even if they aren't active in\n  // the new tree.\n  // TODO: We currently retain all the inactive segments indefinitely, until\n  // there's an explicit refresh, or a parent layout is lazily refreshed. We\n  // rely on this for popstate navigations, which update the Router State Tree\n  // but do not eagerly perform a data fetch, because they expect the segment\n  // data to already be in the Cache Node tree. For highly static sites that\n  // are mostly read-only, this may happen only rarely, causing memory to\n  // leak. We should figure out a better model for the lifetime of inactive\n  // segments, so we can maintain instant back/forward navigations without\n  // leaking memory indefinitely.\n  const prefetchParallelRoutes = new Map(oldParallelRoutes)\n\n  // As we diff the trees, we may sometimes modify (copy-on-write, not mutate)\n  // the Route Tree that was returned by the server — for example, in the case\n  // of default parallel routes, we preserve the currently active segment. To\n  // avoid mutating the original tree, we clone the router state children along\n  // the return path.\n  let patchedRouterStateChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n  let taskChildren = null\n\n  // Most navigations require a request to fetch additional data from the\n  // server, either because the data was not already prefetched, or because the\n  // target route contains dynamic data that cannot be prefetched.\n  //\n  // However, if the target route is fully static, and it's already completely\n  // loaded into the segment cache, then we can skip the server request.\n  //\n  // This starts off as `false`, and is set to `true` if any of the child\n  // routes requires a dynamic request.\n  let needsDynamicRequest = false\n  // As we traverse the children, we'll construct a FlightRouterState that can\n  // be sent to the server to request the dynamic data. If it turns out that\n  // nothing in the subtree is dynamic (i.e. needsDynamicRequest is false at the\n  // end), then this will be discarded.\n  // TODO: We can probably optimize the format of this data structure to only\n  // include paths that are dynamic. Instead of reusing the\n  // FlightRouterState type.\n  let dynamicRequestTreeChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n\n  for (let parallelRouteKey in newRouterStateChildren) {\n    const newRouterStateChild: FlightRouterState =\n      newRouterStateChildren[parallelRouteKey]\n    const oldRouterStateChild: FlightRouterState | void =\n      oldRouterStateChildren[parallelRouteKey]\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    const prefetchDataChild: CacheNodeSeedData | void | null =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const newSegmentChild = newRouterStateChild[0]\n    const newSegmentPathChild = segmentPath.concat([\n      parallelRouteKey,\n      newSegmentChild,\n    ])\n    const newSegmentKeyChild = createRouterCacheKey(newSegmentChild)\n\n    const oldSegmentChild =\n      oldRouterStateChild !== undefined ? oldRouterStateChild[0] : undefined\n\n    const oldCacheNodeChild =\n      oldSegmentMapChild !== undefined\n        ? oldSegmentMapChild.get(newSegmentKeyChild)\n        : undefined\n\n    let taskChild: Task | null\n    if (newSegmentChild === DEFAULT_SEGMENT_KEY) {\n      // This is another kind of leaf segment — a default route.\n      //\n      // Default routes have special behavior. When there's no matching segment\n      // for a parallel route, Next.js preserves the currently active segment\n      // during a client navigation — but not for initial render. The server\n      // leaves it to the client to account for this. So we need to handle\n      // it here.\n      if (oldRouterStateChild !== undefined) {\n        // Reuse the existing Router State for this segment. We spawn a \"task\"\n        // just to keep track of the updated router state; unlike most, it's\n        // already fulfilled and won't be affected by the dynamic response.\n        taskChild = spawnReusedTask(oldRouterStateChild)\n      } else {\n        // There's no currently active segment. Switch to the \"create\" path.\n        taskChild = beginRenderingNewRouteTree(\n          navigatedAt,\n          oldRouterStateChild,\n          newRouterStateChild,\n          oldCacheNodeChild,\n          didFindRootLayout,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      }\n    } else if (\n      isSamePageNavigation &&\n      // Check if this is a page segment.\n      // TODO: We're not consistent about how we do this check. Some places\n      // check if the segment starts with PAGE_SEGMENT_KEY, but most seem to\n      // check if there any any children, which is why I'm doing it here. We\n      // should probably encode an empty children set as `null` though. Either\n      // way, we should update all the checks to be consistent.\n      Object.keys(newRouterStateChild[1]).length === 0\n    ) {\n      // We special case navigations to the exact same URL as the current\n      // location. It's a common UI pattern for apps to refresh when you click a\n      // link to the current page. So when this happens, we refresh the dynamic\n      // data in the page segments.\n      //\n      // Note that this does not apply if the any part of the hash or search\n      // query has changed. This might feel a bit weird but it makes more sense\n      // when you consider that the way to trigger this behavior is to click\n      // the same link multiple times.\n      //\n      // TODO: We should probably refresh the *entire* route when this case\n      // occurs, not just the page segments. Essentially treating it the same as\n      // a refresh() triggered by an action, which is the more explicit way of\n      // modeling the UI pattern described above.\n      //\n      // Also note that this only refreshes the dynamic data, not static/\n      // cached data. If the page segment is fully static and prefetched, the\n      // request is skipped. (This is also how refresh() works.)\n      taskChild = beginRenderingNewRouteTree(\n        navigatedAt,\n        oldRouterStateChild,\n        newRouterStateChild,\n        oldCacheNodeChild,\n        didFindRootLayout,\n        prefetchDataChild !== undefined ? prefetchDataChild : null,\n        prefetchHead,\n        isPrefetchHeadPartial,\n        newSegmentPathChild,\n        scrollableSegmentsResult\n      )\n    } else if (\n      oldRouterStateChild !== undefined &&\n      oldSegmentChild !== undefined &&\n      matchSegment(newSegmentChild, oldSegmentChild)\n    ) {\n      if (\n        oldCacheNodeChild !== undefined &&\n        oldRouterStateChild !== undefined\n      ) {\n        // This segment exists in both the old and new trees. Recursively update\n        // the children.\n        taskChild = updateCacheNodeOnNavigation(\n          navigatedAt,\n          oldCacheNodeChild,\n          oldRouterStateChild,\n          newRouterStateChild,\n          didFindRootLayout,\n          prefetchDataChild,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          isSamePageNavigation,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      } else {\n        // There's no existing Cache Node for this segment. Switch to the\n        // \"create\" path.\n        taskChild = beginRenderingNewRouteTree(\n          navigatedAt,\n          oldRouterStateChild,\n          newRouterStateChild,\n          oldCacheNodeChild,\n          didFindRootLayout,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      }\n    } else {\n      // This is a new tree. Switch to the \"create\" path.\n      taskChild = beginRenderingNewRouteTree(\n        navigatedAt,\n        oldRouterStateChild,\n        newRouterStateChild,\n        oldCacheNodeChild,\n        didFindRootLayout,\n        prefetchDataChild !== undefined ? prefetchDataChild : null,\n        prefetchHead,\n        isPrefetchHeadPartial,\n        newSegmentPathChild,\n        scrollableSegmentsResult\n      )\n    }\n\n    if (taskChild !== null) {\n      // Recursively propagate up the child tasks.\n\n      if (taskChild.route === null) {\n        // One of the child tasks discovered a change to the root layout.\n        // Immediately unwind from this recursive traversal.\n        return MPA_NAVIGATION_TASK\n      }\n\n      if (taskChildren === null) {\n        taskChildren = new Map()\n      }\n      taskChildren.set(parallelRouteKey, taskChild)\n      const newCacheNodeChild = taskChild.node\n      if (newCacheNodeChild !== null) {\n        const newSegmentMapChild: ChildSegmentMap = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(newSegmentKeyChild, newCacheNodeChild)\n        prefetchParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n\n      // The child tree's route state may be different from the prefetched\n      // route sent by the server. We need to clone it as we traverse back up\n      // the tree.\n      const taskChildRoute = taskChild.route\n      patchedRouterStateChildren[parallelRouteKey] = taskChildRoute\n\n      const dynamicRequestTreeChild = taskChild.dynamicRequestTree\n      if (dynamicRequestTreeChild !== null) {\n        // Something in the child tree is dynamic.\n        needsDynamicRequest = true\n        dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild\n      } else {\n        dynamicRequestTreeChildren[parallelRouteKey] = taskChildRoute\n      }\n    } else {\n      // The child didn't change. We can use the prefetched router state.\n      patchedRouterStateChildren[parallelRouteKey] = newRouterStateChild\n      dynamicRequestTreeChildren[parallelRouteKey] = newRouterStateChild\n    }\n  }\n\n  if (taskChildren === null) {\n    // No new tasks were spawned.\n    return null\n  }\n\n  const newCacheNode: ReadyCacheNode = {\n    lazyData: null,\n    rsc: oldCacheNode.rsc,\n    // We intentionally aren't updating the prefetchRsc field, since this node\n    // is already part of the current tree, because it would be weird for\n    // prefetch data to be newer than the final data. It probably won't ever be\n    // observable anyway, but it could happen if the segment is unmounted then\n    // mounted again, because LayoutRouter will momentarily switch to rendering\n    // prefetchRsc, via useDeferredValue.\n    prefetchRsc: oldCacheNode.prefetchRsc,\n    head: oldCacheNode.head,\n    prefetchHead: oldCacheNode.prefetchHead,\n    loading: oldCacheNode.loading,\n\n    // Everything is cloned except for the children, which we computed above.\n    parallelRoutes: prefetchParallelRoutes,\n\n    navigatedAt,\n  }\n\n  return {\n    // Return a cloned copy of the router state with updated children.\n    route: patchRouterStateWithNewChildren(\n      newRouterState,\n      patchedRouterStateChildren\n    ),\n    node: newCacheNode,\n    dynamicRequestTree: needsDynamicRequest\n      ? patchRouterStateWithNewChildren(\n          newRouterState,\n          dynamicRequestTreeChildren\n        )\n      : null,\n    children: taskChildren,\n  }\n}\n\nfunction beginRenderingNewRouteTree(\n  navigatedAt: number,\n  oldRouterState: FlightRouterState | void,\n  newRouterState: FlightRouterState,\n  existingCacheNode: CacheNode | void,\n  didFindRootLayout: boolean,\n  prefetchData: CacheNodeSeedData | null,\n  possiblyPartialPrefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task {\n  if (!didFindRootLayout) {\n    // The route tree changed before we reached a layout. (The highest-level\n    // layout in a route tree is referred to as the \"root\" layout.) This could\n    // mean that we're navigating between two different root layouts. When this\n    // happens, we perform a full-page (MPA-style) navigation.\n    //\n    // However, the algorithm for deciding where to start rendering a route\n    // (i.e. the one performed in order to reach this function) is stricter\n    // than the one used to detect a change in the root layout. So just because\n    // we're re-rendering a segment outside of the root layout does not mean we\n    // should trigger a full-page navigation.\n    //\n    // Specifically, we handle dynamic parameters differently: two segments are\n    // considered the same even if their parameter values are different.\n    //\n    // Refer to isNavigatingToNewRootLayout for details.\n    //\n    // Note that we only have to perform this extra traversal if we didn't\n    // already discover a root layout in the part of the tree that is unchanged.\n    // In the common case, this branch is skipped completely.\n    if (\n      oldRouterState === undefined ||\n      isNavigatingToNewRootLayout(oldRouterState, newRouterState)\n    ) {\n      // The root layout changed. Perform a full-page navigation.\n      return MPA_NAVIGATION_TASK\n    }\n  }\n  return createCacheNodeOnNavigation(\n    navigatedAt,\n    newRouterState,\n    existingCacheNode,\n    prefetchData,\n    possiblyPartialPrefetchHead,\n    isPrefetchHeadPartial,\n    segmentPath,\n    scrollableSegmentsResult\n  )\n}\n\nfunction createCacheNodeOnNavigation(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  existingCacheNode: CacheNode | void,\n  prefetchData: CacheNodeSeedData | null,\n  possiblyPartialPrefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): SPANavigationTask {\n  // Same traversal as updateCacheNodeNavigation, but we switch to this path\n  // once we reach the part of the tree that was not in the previous route. We\n  // don't need to diff against the old tree, we just need to create a new one.\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const routerStateChildren = routerState[1]\n  const isLeafSegment = Object.keys(routerStateChildren).length === 0\n\n  // Even we're rendering inside the \"new\" part of the target tree, we may have\n  // a locally cached segment that we can reuse. This may come from either 1)\n  // the CacheNode tree, which lives in React state and is populated by previous\n  // navigations; or 2) the prefetch cache, which is a separate cache that is\n  // populated by prefetches.\n  let rsc: React.ReactNode\n  let loading: LoadingModuleData | Promise<LoadingModuleData>\n  let head: HeadData | null\n  let cacheNodeNavigatedAt: number\n  if (\n    existingCacheNode !== undefined &&\n    // DYNAMIC_STALETIME_MS defaults to 0, but it can be increased using\n    // the experimental.staleTimes.dynamic config. When set, we'll avoid\n    // refetching dynamic data if it was fetched within the given threshold.\n    existingCacheNode.navigatedAt + DYNAMIC_STALETIME_MS > navigatedAt\n  ) {\n    // We have an existing CacheNode for this segment, and it's not stale. We\n    // should reuse it rather than request a new one.\n    rsc = existingCacheNode.rsc\n    loading = existingCacheNode.loading\n    head = existingCacheNode.head\n\n    // Don't update the navigatedAt timestamp, since we're reusing stale data.\n    cacheNodeNavigatedAt = existingCacheNode.navigatedAt\n  } else if (prefetchData !== null) {\n    // There's no existing CacheNode for this segment, but we do have prefetch\n    // data. If the prefetch data is fully static (i.e. does not contain any\n    // dynamic holes), we don't need to request it from the server.\n    rsc = prefetchData[1]\n    loading = prefetchData[3]\n    head = isLeafSegment ? possiblyPartialPrefetchHead : null\n    // Even though we're accessing the data from the prefetch cache, this is\n    // conceptually a new segment, not a reused one. So we should update the\n    // navigatedAt timestamp.\n    cacheNodeNavigatedAt = navigatedAt\n    const isPrefetchRscPartial = prefetchData[4]\n    if (\n      // Check if the segment data is partial\n      isPrefetchRscPartial ||\n      // Check if the head is partial (only relevant if this is a leaf segment)\n      (isPrefetchHeadPartial && isLeafSegment)\n    ) {\n      // We only have partial data from this segment. Like missing segments, we\n      // must request the full data from the server.\n      return spawnPendingTask(\n        navigatedAt,\n        routerState,\n        prefetchData,\n        possiblyPartialPrefetchHead,\n        isPrefetchHeadPartial,\n        segmentPath,\n        scrollableSegmentsResult\n      )\n    } else {\n      // The prefetch data is fully static, so we can omit it from the\n      // navigation request.\n    }\n  } else {\n    // There's no prefetch for this segment. Everything from this point will be\n    // requested from the server, even if there are static children below it.\n    // Create a terminal task node that will later be fulfilled by\n    // server response.\n    return spawnPendingTask(\n      navigatedAt,\n      routerState,\n      null,\n      possiblyPartialPrefetchHead,\n      isPrefetchHeadPartial,\n      segmentPath,\n      scrollableSegmentsResult\n    )\n  }\n\n  // We already have a full segment we can render, so we don't need to request a\n  // new one from the server. Keep traversing down the tree until we reach\n  // something that requires a dynamic request.\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n  const taskChildren = new Map()\n  const existingCacheNodeChildren =\n    existingCacheNode !== undefined ? existingCacheNode.parallelRoutes : null\n  const cacheNodeChildren = new Map(existingCacheNodeChildren)\n  let dynamicRequestTreeChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n  let needsDynamicRequest = false\n  if (isLeafSegment) {\n    // The segment path of every leaf segment (i.e. page) is collected into\n    // a result array. This is used by the LayoutRouter to scroll to ensure that\n    // new pages are visible after a navigation.\n    // TODO: We should use a string to represent the segment path instead of\n    // an array. We already use a string representation for the path when\n    // accessing the Segment Cache, so we can use the same one.\n    scrollableSegmentsResult.push(segmentPath)\n  } else {\n    for (let parallelRouteKey in routerStateChildren) {\n      const routerStateChild: FlightRouterState =\n        routerStateChildren[parallelRouteKey]\n      const prefetchDataChild: CacheNodeSeedData | void | null =\n        prefetchDataChildren !== null\n          ? prefetchDataChildren[parallelRouteKey]\n          : null\n      const existingSegmentMapChild =\n        existingCacheNodeChildren !== null\n          ? existingCacheNodeChildren.get(parallelRouteKey)\n          : undefined\n      const segmentChild = routerStateChild[0]\n      const segmentPathChild = segmentPath.concat([\n        parallelRouteKey,\n        segmentChild,\n      ])\n      const segmentKeyChild = createRouterCacheKey(segmentChild)\n\n      const existingCacheNodeChild =\n        existingSegmentMapChild !== undefined\n          ? existingSegmentMapChild.get(segmentKeyChild)\n          : undefined\n\n      const taskChild = createCacheNodeOnNavigation(\n        navigatedAt,\n        routerStateChild,\n        existingCacheNodeChild,\n        prefetchDataChild,\n        possiblyPartialPrefetchHead,\n        isPrefetchHeadPartial,\n        segmentPathChild,\n        scrollableSegmentsResult\n      )\n      taskChildren.set(parallelRouteKey, taskChild)\n      const dynamicRequestTreeChild = taskChild.dynamicRequestTree\n      if (dynamicRequestTreeChild !== null) {\n        // Something in the child tree is dynamic.\n        needsDynamicRequest = true\n        dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild\n      } else {\n        dynamicRequestTreeChildren[parallelRouteKey] = routerStateChild\n      }\n      const newCacheNodeChild = taskChild.node\n      if (newCacheNodeChild !== null) {\n        const newSegmentMapChild: ChildSegmentMap = new Map()\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n        cacheNodeChildren.set(parallelRouteKey, newSegmentMapChild)\n      }\n    }\n  }\n\n  return {\n    // Since we're inside a new route tree, unlike the\n    // `updateCacheNodeOnNavigation` path, the router state on the children\n    // tasks is always the same as the router state we pass in. So we don't need\n    // to clone/modify it.\n    route: routerState,\n    node: {\n      lazyData: null,\n      // Since this segment is already full, we don't need to use the\n      // `prefetchRsc` field.\n      rsc,\n      prefetchRsc: null,\n      head,\n      prefetchHead: null,\n      loading,\n      parallelRoutes: cacheNodeChildren,\n      navigatedAt: cacheNodeNavigatedAt,\n    },\n    dynamicRequestTree: needsDynamicRequest\n      ? patchRouterStateWithNewChildren(routerState, dynamicRequestTreeChildren)\n      : null,\n    children: taskChildren,\n  }\n}\n\nfunction patchRouterStateWithNewChildren(\n  baseRouterState: FlightRouterState,\n  newChildren: { [parallelRouteKey: string]: FlightRouterState }\n): FlightRouterState {\n  const clone: FlightRouterState = [baseRouterState[0], newChildren]\n  // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n  // confirm whether we need to copy all of these fields. Not sure the server\n  // ever sends, e.g. the refetch marker.\n  if (2 in baseRouterState) {\n    clone[2] = baseRouterState[2]\n  }\n  if (3 in baseRouterState) {\n    clone[3] = baseRouterState[3]\n  }\n  if (4 in baseRouterState) {\n    clone[4] = baseRouterState[4]\n  }\n  return clone\n}\n\nfunction spawnPendingTask(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): SPANavigationTask {\n  // Create a task that will later be fulfilled by data from the server.\n\n  // Clone the prefetched route tree and the `refetch` marker to it. We'll send\n  // this to the server so it knows where to start rendering.\n  const dynamicRequestTree = patchRouterStateWithNewChildren(\n    routerState,\n    routerState[1]\n  )\n  dynamicRequestTree[3] = 'refetch'\n\n  const newTask: Task = {\n    route: routerState,\n\n    // Corresponds to the part of the route that will be rendered on the server.\n    node: createPendingCacheNode(\n      navigatedAt,\n      routerState,\n      prefetchData,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      segmentPath,\n      scrollableSegmentsResult\n    ),\n    // Because this is non-null, and it gets propagated up through the parent\n    // tasks, the root task will know that it needs to perform a server request.\n    dynamicRequestTree,\n    children: null,\n  }\n  return newTask\n}\n\nfunction spawnReusedTask(reusedRouterState: FlightRouterState): Task {\n  // Create a task that reuses an existing segment, e.g. when reusing\n  // the current active segment in place of a default route.\n  return {\n    route: reusedRouterState,\n    node: null,\n    dynamicRequestTree: null,\n    children: null,\n  }\n}\n\n// Writes a dynamic server response into the tree created by\n// updateCacheNodeOnNavigation. All pending promises that were spawned by the\n// navigation will be resolved, either with dynamic data from the server, or\n// `null` to indicate that the data is missing.\n//\n// A `null` value will trigger a lazy fetch during render, which will then patch\n// up the tree using the same mechanism as the non-PPR implementation\n// (serverPatchReducer).\n//\n// Usually, the server will respond with exactly the subset of data that we're\n// waiting for — everything below the nearest shared layout. But technically,\n// the server can return anything it wants.\n//\n// This does _not_ create a new tree; it modifies the existing one in place.\n// Which means it must follow the Suspense rules of cache safety.\nexport function listenForDynamicRequest(\n  task: SPANavigationTask,\n  responsePromise: Promise<FetchServerResponseResult>\n) {\n  responsePromise.then(\n    ({ flightData }: FetchServerResponseResult) => {\n      if (typeof flightData === 'string') {\n        // Happens when navigating to page in `pages` from `app`. We shouldn't\n        // get here because should have already handled this during\n        // the prefetch.\n        return\n      }\n      for (const normalizedFlightData of flightData) {\n        const {\n          segmentPath,\n          tree: serverRouterState,\n          seedData: dynamicData,\n          head: dynamicHead,\n        } = normalizedFlightData\n\n        if (!dynamicData) {\n          // This shouldn't happen. PPR should always send back a response.\n          // However, `FlightDataPath` is a shared type and the pre-PPR handling of\n          // this might return null.\n          continue\n        }\n\n        writeDynamicDataIntoPendingTask(\n          task,\n          segmentPath,\n          serverRouterState,\n          dynamicData,\n          dynamicHead\n        )\n      }\n\n      // Now that we've exhausted all the data we received from the server, if\n      // there are any remaining pending tasks in the tree, abort them now.\n      // If there's any missing data, it will trigger a lazy fetch.\n      abortTask(task, null)\n    },\n    (error: any) => {\n      // This will trigger an error during render\n      abortTask(task, error)\n    }\n  )\n}\n\nfunction writeDynamicDataIntoPendingTask(\n  rootTask: SPANavigationTask,\n  segmentPath: FlightSegmentPath,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n) {\n  // The data sent by the server represents only a subtree of the app. We need\n  // to find the part of the task tree that matches the server response, and\n  // fulfill it using the dynamic data.\n  //\n  // segmentPath represents the parent path of subtree. It's a repeating pattern\n  // of parallel route key and segment:\n  //\n  //   [string, Segment, string, Segment, string, Segment, ...]\n  //\n  // Iterate through the path and finish any tasks that match this payload.\n  let task = rootTask\n  for (let i = 0; i < segmentPath.length; i += 2) {\n    const parallelRouteKey: string = segmentPath[i]\n    const segment: Segment = segmentPath[i + 1]\n    const taskChildren = task.children\n    if (taskChildren !== null) {\n      const taskChild = taskChildren.get(parallelRouteKey)\n      if (taskChild !== undefined) {\n        const taskSegment = taskChild.route[0]\n        if (matchSegment(segment, taskSegment)) {\n          // Found a match for this task. Keep traversing down the task tree.\n          task = taskChild\n          continue\n        }\n      }\n    }\n    // We didn't find a child task that matches the server data. Exit. We won't\n    // abort the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n    return\n  }\n\n  finishTaskUsingDynamicDataPayload(\n    task,\n    serverRouterState,\n    dynamicData,\n    dynamicHead\n  )\n}\n\nfunction finishTaskUsingDynamicDataPayload(\n  task: SPANavigationTask,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n) {\n  if (task.dynamicRequestTree === null) {\n    // Everything in this subtree is already complete. Bail out.\n    return\n  }\n\n  // dynamicData may represent a larger subtree than the task. Before we can\n  // finish the task, we need to line them up.\n  const taskChildren = task.children\n  const taskNode = task.node\n  if (taskChildren === null) {\n    // We've reached the leaf node of the pending task. The server data tree\n    // lines up the pending Cache Node tree. We can now switch to the\n    // normal algorithm.\n    if (taskNode !== null) {\n      finishPendingCacheNode(\n        taskNode,\n        task.route,\n        serverRouterState,\n        dynamicData,\n        dynamicHead\n      )\n      // Set this to null to indicate that this task is now complete.\n      task.dynamicRequestTree = null\n    }\n    return\n  }\n  // The server returned more data than we need to finish the task. Skip over\n  // the extra segments until we reach the leaf task node.\n  const serverChildren = serverRouterState[1]\n  const dynamicDataChildren = dynamicData[2]\n\n  for (const parallelRouteKey in serverRouterState) {\n    const serverRouterStateChild: FlightRouterState =\n      serverChildren[parallelRouteKey]\n    const dynamicDataChild: CacheNodeSeedData | null | void =\n      dynamicDataChildren[parallelRouteKey]\n\n    const taskChild = taskChildren.get(parallelRouteKey)\n    if (taskChild !== undefined) {\n      const taskSegment = taskChild.route[0]\n      if (\n        matchSegment(serverRouterStateChild[0], taskSegment) &&\n        dynamicDataChild !== null &&\n        dynamicDataChild !== undefined\n      ) {\n        // Found a match for this task. Keep traversing down the task tree.\n        return finishTaskUsingDynamicDataPayload(\n          taskChild,\n          serverRouterStateChild,\n          dynamicDataChild,\n          dynamicHead\n        )\n      }\n    }\n    // We didn't find a child task that matches the server data. We won't abort\n    // the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n  }\n}\n\nfunction createPendingCacheNode(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): ReadyCacheNode {\n  const routerStateChildren = routerState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  const parallelRoutes = new Map()\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const prefetchDataChild: CacheNodeSeedData | null | void =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const segmentChild = routerStateChild[0]\n    const segmentPathChild = segmentPath.concat([\n      parallelRouteKey,\n      segmentChild,\n    ])\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n\n    const newCacheNodeChild = createPendingCacheNode(\n      navigatedAt,\n      routerStateChild,\n      prefetchDataChild === undefined ? null : prefetchDataChild,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      segmentPathChild,\n      scrollableSegmentsResult\n    )\n\n    const newSegmentMapChild: ChildSegmentMap = new Map()\n    newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n    parallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n  }\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const isLeafSegment = parallelRoutes.size === 0\n\n  if (isLeafSegment) {\n    // The segment path of every leaf segment (i.e. page) is collected into\n    // a result array. This is used by the LayoutRouter to scroll to ensure that\n    // new pages are visible after a navigation.\n    // TODO: We should use a string to represent the segment path instead of\n    // an array. We already use a string representation for the path when\n    // accessing the Segment Cache, so we can use the same one.\n    scrollableSegmentsResult.push(segmentPath)\n  }\n\n  const maybePrefetchRsc = prefetchData !== null ? prefetchData[1] : null\n  const maybePrefetchLoading = prefetchData !== null ? prefetchData[3] : null\n  return {\n    lazyData: null,\n    parallelRoutes: parallelRoutes,\n\n    prefetchRsc: maybePrefetchRsc !== undefined ? maybePrefetchRsc : null,\n    prefetchHead: isLeafSegment ? prefetchHead : [null, null],\n\n    // TODO: Technically, a loading boundary could contain dynamic data. We must\n    // have separate `loading` and `prefetchLoading` fields to handle this, like\n    // we do for the segment data and head.\n    loading: maybePrefetchLoading !== undefined ? maybePrefetchLoading : null,\n\n    // Create a deferred promise. This will be fulfilled once the dynamic\n    // response is received from the server.\n    rsc: createDeferredRsc() as React.ReactNode,\n    head: isLeafSegment ? (createDeferredRsc() as React.ReactNode) : null,\n\n    navigatedAt,\n  }\n}\n\nfunction finishPendingCacheNode(\n  cacheNode: CacheNode,\n  taskState: FlightRouterState,\n  serverState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n): void {\n  // Writes a dynamic response into an existing Cache Node tree. This does _not_\n  // create a new tree, it updates the existing tree in-place. So it must follow\n  // the Suspense rules of cache safety — it can resolve pending promises, but\n  // it cannot overwrite existing data. It can add segments to the tree (because\n  // a missing segment will cause the layout router to suspend).\n  // but it cannot delete them.\n  //\n  // We must resolve every promise in the tree, or else it will suspend\n  // indefinitely. If we did not receive data for a segment, we will resolve its\n  // data promise to `null` to trigger a lazy fetch during render.\n  const taskStateChildren = taskState[1]\n  const serverStateChildren = serverState[1]\n  const dataChildren = dynamicData[2]\n\n  // The router state that we traverse the tree with (taskState) is the same one\n  // that we used to construct the pending Cache Node tree. That way we're sure\n  // to resolve all the pending promises.\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in taskStateChildren) {\n    const taskStateChild: FlightRouterState =\n      taskStateChildren[parallelRouteKey]\n    const serverStateChild: FlightRouterState | void =\n      serverStateChildren[parallelRouteKey]\n    const dataChild: CacheNodeSeedData | null | void =\n      dataChildren[parallelRouteKey]\n\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    const taskSegmentChild = taskStateChild[0]\n    const taskSegmentKeyChild = createRouterCacheKey(taskSegmentChild)\n\n    const cacheNodeChild =\n      segmentMapChild !== undefined\n        ? segmentMapChild.get(taskSegmentKeyChild)\n        : undefined\n\n    if (cacheNodeChild !== undefined) {\n      if (\n        serverStateChild !== undefined &&\n        matchSegment(taskSegmentChild, serverStateChild[0])\n      ) {\n        if (dataChild !== undefined && dataChild !== null) {\n          // This is the happy path. Recursively update all the children.\n          finishPendingCacheNode(\n            cacheNodeChild,\n            taskStateChild,\n            serverStateChild,\n            dataChild,\n            dynamicHead\n          )\n        } else {\n          // The server never returned data for this segment. Trigger a lazy\n          // fetch during render. This shouldn't happen because the Route Tree\n          // and the Seed Data tree sent by the server should always be the same\n          // shape when part of the same server response.\n          abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n        }\n      } else {\n        // The server never returned data for this segment. Trigger a lazy\n        // fetch during render.\n        abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n      }\n    } else {\n      // The server response matches what was expected to receive, but there's\n      // no matching Cache Node in the task tree. This is a bug in the\n      // implementation because we should have created a node for every\n      // segment in the tree that's associated with this task.\n    }\n  }\n\n  // Use the dynamic data from the server to fulfill the deferred RSC promise\n  // on the Cache Node.\n  const rsc = cacheNode.rsc\n  const dynamicSegmentData = dynamicData[1]\n  if (rsc === null) {\n    // This is a lazy cache node. We can overwrite it. This is only safe\n    // because we know that the LayoutRouter suspends if `rsc` is `null`.\n    cacheNode.rsc = dynamicSegmentData\n  } else if (isDeferredRsc(rsc)) {\n    // This is a deferred RSC promise. We can fulfill it with the data we just\n    // received from the server. If it was already resolved by a different\n    // navigation, then this does nothing because we can't overwrite data.\n    rsc.resolve(dynamicSegmentData)\n  } else {\n    // This is not a deferred RSC promise, nor is it empty, so it must have\n    // been populated by a different navigation. We must not overwrite it.\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved with the dynamic head from\n  // the server.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(dynamicHead)\n  }\n}\n\nexport function abortTask(task: SPANavigationTask, error: any): void {\n  const cacheNode = task.node\n  if (cacheNode === null) {\n    // This indicates the task is already complete.\n    return\n  }\n\n  const taskChildren = task.children\n  if (taskChildren === null) {\n    // Reached the leaf task node. This is the root of a pending cache\n    // node tree.\n    abortPendingCacheNode(task.route, cacheNode, error)\n  } else {\n    // This is an intermediate task node. Keep traversing until we reach a\n    // task node with no children. That will be the root of the cache node tree\n    // that needs to be resolved.\n    for (const taskChild of taskChildren.values()) {\n      abortTask(taskChild, error)\n    }\n  }\n\n  // Set this to null to indicate that this task is now complete.\n  task.dynamicRequestTree = null\n}\n\nfunction abortPendingCacheNode(\n  routerState: FlightRouterState,\n  cacheNode: CacheNode,\n  error: any\n): void {\n  // For every pending segment in the tree, resolve its `rsc` promise to `null`\n  // to trigger a lazy fetch during render.\n  //\n  // Or, if an error object is provided, it will error instead.\n  const routerStateChildren = routerState[1]\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    if (segmentMapChild === undefined) {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n      continue\n    }\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const cacheNodeChild = segmentMapChild.get(segmentKeyChild)\n    if (cacheNodeChild !== undefined) {\n      abortPendingCacheNode(routerStateChild, cacheNodeChild, error)\n    } else {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n    }\n  }\n  const rsc = cacheNode.rsc\n  if (isDeferredRsc(rsc)) {\n    if (error === null) {\n      // This will trigger a lazy fetch during render.\n      rsc.resolve(null)\n    } else {\n      // This will trigger an error during rendering.\n      rsc.reject(error)\n    }\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved. If an error was provided, we\n  // will not resolve it with an error, since this is rendered at the root of\n  // the app. We want the segment to error, not the entire app.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(null)\n  }\n}\n\nexport function updateCacheNodeOnPopstateRestoration(\n  oldCacheNode: CacheNode,\n  routerState: FlightRouterState\n): ReadyCacheNode {\n  // A popstate navigation reads data from the local cache. It does not issue\n  // new network requests (unless the cache entries have been evicted). So, we\n  // update the cache to drop the prefetch data for any segment whose dynamic\n  // data was already received. This prevents an unnecessary flash back to PPR\n  // state during a back/forward navigation.\n  //\n  // This function clones the entire cache node tree and sets the `prefetchRsc`\n  // field to `null` to prevent it from being rendered. We can't mutate the node\n  // in place because this is a concurrent data structure.\n\n  const routerStateChildren = routerState[1]\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n  const newParallelRoutes = new Map(oldParallelRoutes)\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    if (oldSegmentMapChild !== undefined) {\n      const oldCacheNodeChild = oldSegmentMapChild.get(segmentKeyChild)\n      if (oldCacheNodeChild !== undefined) {\n        const newCacheNodeChild = updateCacheNodeOnPopstateRestoration(\n          oldCacheNodeChild,\n          routerStateChild\n        )\n        const newSegmentMapChild = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n        newParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n    }\n  }\n\n  // Only show prefetched data if the dynamic data is still pending.\n  //\n  // Tehnically, what we're actually checking is whether the dynamic network\n  // response was received. But since it's a streaming response, this does not\n  // mean that all the dynamic data has fully streamed in. It just means that\n  // _some_ of the dynamic data was received. But as a heuristic, we assume that\n  // the rest dynamic data will stream in quickly, so it's still better to skip\n  // the prefetch state.\n  const rsc = oldCacheNode.rsc\n  const shouldUsePrefetch = isDeferredRsc(rsc) && rsc.status === 'pending'\n\n  return {\n    lazyData: null,\n    rsc,\n    head: oldCacheNode.head,\n\n    prefetchHead: shouldUsePrefetch ? oldCacheNode.prefetchHead : [null, null],\n    prefetchRsc: shouldUsePrefetch ? oldCacheNode.prefetchRsc : null,\n    loading: oldCacheNode.loading,\n\n    // These are the cloned children we computed above\n    parallelRoutes: newParallelRoutes,\n\n    navigatedAt: oldCacheNode.navigatedAt,\n  }\n}\n\nconst DEFERRED = Symbol()\n\ntype PendingDeferredRsc = Promise<React.ReactNode> & {\n  status: 'pending'\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype FulfilledDeferredRsc = Promise<React.ReactNode> & {\n  status: 'fulfilled'\n  value: React.ReactNode\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype RejectedDeferredRsc = Promise<React.ReactNode> & {\n  status: 'rejected'\n  reason: any\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype DeferredRsc =\n  | PendingDeferredRsc\n  | FulfilledDeferredRsc\n  | RejectedDeferredRsc\n\n// This type exists to distinguish a DeferredRsc from a Flight promise. It's a\n// compromise to avoid adding an extra field on every Cache Node, which would be\n// awkward because the pre-PPR parts of codebase would need to account for it,\n// too. We can remove it once type Cache Node type is more settled.\nfunction isDeferredRsc(value: any): value is DeferredRsc {\n  return value && value.tag === DEFERRED\n}\n\nfunction createDeferredRsc(): PendingDeferredRsc {\n  let resolve: any\n  let reject: any\n  const pendingRsc = new Promise<React.ReactNode>((res, rej) => {\n    resolve = res\n    reject = rej\n  }) as PendingDeferredRsc\n  pendingRsc.status = 'pending'\n  pendingRsc.resolve = (value: React.ReactNode) => {\n    if (pendingRsc.status === 'pending') {\n      const fulfilledRsc: FulfilledDeferredRsc = pendingRsc as any\n      fulfilledRsc.status = 'fulfilled'\n      fulfilledRsc.value = value\n      resolve(value)\n    }\n  }\n  pendingRsc.reject = (error: any) => {\n    if (pendingRsc.status === 'pending') {\n      const rejectedRsc: RejectedDeferredRsc = pendingRsc as any\n      rejectedRsc.status = 'rejected'\n      rejectedRsc.reason = error\n      reject(error)\n    }\n  }\n  pendingRsc.tag = DEFERRED\n  return pendingRsc\n}\n", "import type {\n  PrefetchAction,\n  ReducerState,\n  ReadonlyReducerState,\n} from '../router-reducer-types'\nimport { PromiseQueue } from '../../promise-queue'\nimport {\n  getOrCreatePrefetchCacheEntry,\n  prunePrefetchCache,\n} from '../prefetch-cache-utils'\nexport const prefetchQueue = new PromiseQueue(5)\n\nexport const prefetchReducer = process.env.__NEXT_CLIENT_SEGMENT_CACHE\n  ? identityReducerWhenSegmentCacheIsEnabled\n  : prefetchReducerImpl\n\nfunction identityReducerWhenSegmentCacheIsEnabled<T>(state: T): T {\n  // Unlike the old implementation, the Segment Cache doesn't store its data in\n  // the router reducer state.\n  //\n  // This shouldn't be reachable because we wrap the prefetch API in a check,\n  // too, which prevents the action from being dispatched. But it's here for\n  // clarity + code elimination.\n  return state\n}\n\nfunction prefetchReducerImpl(\n  state: ReadonlyReducerState,\n  action: PrefetchAction\n): ReducerState {\n  // let's prune the prefetch cache before we do anything else\n  prunePrefetchCache(state.prefetchCache)\n\n  const { url } = action\n\n  getOrCreatePrefetchCacheEntry({\n    url,\n    nextUrl: state.nextUrl,\n    prefetchCache: state.prefetchCache,\n    kind: action.kind,\n    tree: state.tree,\n    allowAliasing: true,\n  })\n\n  return state\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightSegmentPath } from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\n\n/**\n * Fill cache up to the end of the flightSegmentPath, invalidating anything below it.\n */\nexport function invalidateCacheBelowFlightSegmentPath(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath\n): void {\n  const isLastEntry = flightSegmentPath.length <= 2\n  const [parallelRouteKey, segment] = flightSegmentPath\n\n  const cacheKey = createRouterCacheKey(segment)\n\n  const existingChildSegmentMap =\n    existingCache.parallelRoutes.get(parallelRouteKey)\n\n  if (!existingChildSegmentMap) {\n    // Bailout because the existing cache does not have the path to the leaf node\n    // Will trigger lazy fetch in layout-router because of missing segment\n    return\n  }\n\n  let childSegmentMap = newCache.parallelRoutes.get(parallelRouteKey)\n  if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n    childSegmentMap = new Map(existingChildSegmentMap)\n    newCache.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n  }\n\n  // In case of last entry don't copy further down.\n  if (isLastEntry) {\n    childSegmentMap.delete(cacheKey)\n    return\n  }\n\n  const existingChildCacheNode = existingChildSegmentMap.get(cacheKey)\n  let childCacheNode = childSegmentMap.get(cacheKey)\n\n  if (!childCacheNode || !existingChildCacheNode) {\n    // Bailout because the existing cache does not have the path to the leaf node\n    // Will trigger lazy fetch in layout-router because of missing segment\n    return\n  }\n\n  if (childCacheNode === existingChildCacheNode) {\n    childCacheNode = {\n      lazyData: childCacheNode.lazyData,\n      rsc: childCacheNode.rsc,\n      prefetchRsc: childCacheNode.prefetchRsc,\n      head: childCacheNode.head,\n      prefetchHead: childCacheNode.prefetchHead,\n      parallelRoutes: new Map(childCacheNode.parallelRoutes),\n    } as CacheNode\n    childSegmentMap.set(cacheKey, childCacheNode)\n  }\n\n  invalidateCacheBelowFlightSegmentPath(\n    childCacheNode,\n    existingChildCacheNode,\n    getNextFlightSegmentPath(flightSegmentPath)\n  )\n}\n", "import type { FlightRouterState } from '../../../server/app-render/types'\n\nexport function isNavigatingToNewRootLayout(\n  currentTree: FlightRouterState,\n  nextTree: FlightRouterState\n): boolean {\n  // Compare segments\n  const currentTreeSegment = currentTree[0]\n  const nextTreeSegment = nextTree[0]\n\n  // If any segment is different before we find the root layout, the root layout has changed.\n  // E.g. /same/(group1)/layout.js -> /same/(group2)/layout.js\n  // First segment is 'same' for both, keep looking. (group1) changed to (group2) before the root layout was found, it must have changed.\n  if (Array.isArray(currentTreeSegment) && Array.isArray(nextTreeSegment)) {\n    // Compare dynamic param name and type but ignore the value, different values would not affect the current root layout\n    // /[name] - /slug1 and /slug2, both values (slug1 & slug2) still has the same layout /[name]/layout.js\n    if (\n      currentTreeSegment[0] !== nextTreeSegment[0] ||\n      currentTreeSegment[2] !== nextTreeSegment[2]\n    ) {\n      return true\n    }\n  } else if (currentTreeSegment !== nextTreeSegment) {\n    return true\n  }\n\n  // Current tree root layout found\n  if (currentTree[4]) {\n    // If the next tree doesn't have the root layout flag, it must have changed.\n    return !nextTree[4]\n  }\n  // Current tree didn't have its root layout here, must have changed.\n  if (nextTree[4]) {\n    return true\n  }\n  // We can't assume it's `parallelRoutes.children` here in case the root layout is `app/@something/layout.js`\n  // But it's not possible to be more than one parallelRoutes before the root layout is found\n  // TODO-APP: change to traverse all parallel routes\n  const currentTreeChild = Object.values(currentTree[1])[0]\n  const nextTreeChild = Object.values(nextTree[1])[0]\n  if (!currentTreeChild || !nextTreeChild) return true\n  return isNavigatingToNewRootLayout(currentTreeChild, nextTreeChild)\n}\n", "import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n", "import type { FlightSegmentPath } from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { createRouterCacheKey } from './create-router-cache-key'\n\n/**\n * This will clear the CacheNode data for a particular segment path. This will cause a lazy-fetch in layout router to fill in new data.\n */\nexport function clearCacheNodeDataForSegmentPath(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath\n): void {\n  const isLastEntry = flightSegmentPath.length <= 2\n\n  const [parallelRouteKey, segment] = flightSegmentPath\n  const cacheKey = createRouterCacheKey(segment)\n\n  const existingChildSegmentMap =\n    existingCache.parallelRoutes.get(parallelRouteKey)\n\n  let childSegmentMap = newCache.parallelRoutes.get(parallelRouteKey)\n\n  if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n    childSegmentMap = new Map(existingChildSegmentMap)\n    newCache.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n  }\n\n  const existingChildCacheNode = existingChildSegmentMap?.get(cacheKey)\n  let childCacheNode = childSegmentMap.get(cacheKey)\n\n  // In case of last segment start off the fetch at this level and don't copy further down.\n  if (isLastEntry) {\n    if (\n      !childCacheNode ||\n      !childCacheNode.lazyData ||\n      childCacheNode === existingChildCacheNode\n    ) {\n      childSegmentMap.set(cacheKey, {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      })\n    }\n    return\n  }\n\n  if (!childCacheNode || !existingChildCacheNode) {\n    // Start fetch in the place where the existing cache doesn't have the data yet.\n    if (!childCacheNode) {\n      childSegmentMap.set(cacheKey, {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      })\n    }\n    return\n  }\n\n  if (childCacheNode === existingChildCacheNode) {\n    childCacheNode = {\n      lazyData: childCacheNode.lazyData,\n      rsc: childCacheNode.rsc,\n      prefetchRsc: childCacheNode.prefetchRsc,\n      head: childCacheNode.head,\n      prefetchHead: childCacheNode.prefetchHead,\n      parallelRoutes: new Map(childCacheNode.parallelRoutes),\n      loading: childCacheNode.loading,\n    } as CacheNode\n    childSegmentMap.set(cacheKey, childCacheNode)\n  }\n\n  return clearCacheNodeDataForSegmentPath(\n    childCacheNode,\n    existingChildCacheNode,\n    getNextFlightSegmentPath(flightSegmentPath)\n  )\n}\n", "import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  HmrRefreshAction,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\n\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(\n  state: ReadonlyReducerState,\n  action: HmrRefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  const navigatedAt = Date.now()\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [state.tree[0], state.tree[1], state.tree[2], 'refetch'],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n    isHmrRefresh: true,\n  })\n\n  return cache.lazyData.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n\n      for (const normalizedFlightData of flightData) {\n        const { tree: treePatch, isRootRender } = normalizedFlightData\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n        const applied = applyFlightData(\n          navigatedAt,\n          currentCache,\n          cache,\n          normalizedFlightData\n        )\n\n        if (applied) {\n          mutable.cache = cache\n          currentCache = cache\n        }\n\n        mutable.patchedTree = newTree\n        mutable.canonicalUrl = href\n\n        currentTree = newTree\n      }\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n\nfunction hmrRefreshReducerNoop(\n  state: ReadonlyReducerState,\n  _action: HmrRefreshAction\n): ReducerState {\n  return state\n}\n\nexport const hmrRefreshReducer =\n  process.env.NODE_ENV === 'production'\n    ? hmrRefreshReducerNoop\n    : hmrRefreshReducerImpl\n", "import { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ServerPatchAction,\n  ReducerState,\n  ReadonlyReducerState,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyFlightData } from '../apply-flight-data'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\n\nexport function serverPatchReducer(\n  state: ReadonlyReducerState,\n  action: ServerPatchAction\n): ReducerState {\n  const {\n    serverResponse: { flightData, canonicalUrl: canonicalUrlOverride },\n    navigatedAt,\n  } = action\n\n  const mutable: Mutable = {}\n\n  mutable.preserveCustomHistoryState = false\n\n  // Handle case when navigating to page in `pages` from `app`\n  if (typeof flightData === 'string') {\n    return handleExternalUrl(\n      state,\n      mutable,\n      flightData,\n      state.pushRef.pendingPush\n    )\n  }\n\n  let currentTree = state.tree\n  let currentCache = state.cache\n\n  for (const normalizedFlightData of flightData) {\n    const { segmentPath: flightSegmentPath, tree: treePatch } =\n      normalizedFlightData\n\n    const newTree = applyRouterStatePatchToTree(\n      // TODO-APP: remove ''\n      ['', ...flightSegmentPath],\n      currentTree,\n      treePatch,\n      state.canonicalUrl\n    )\n\n    // `applyRouterStatePatchToTree` returns `null` when it determined that the server response is not applicable to the current tree.\n    // In other words, the server responded with a tree that doesn't match what the client is currently rendering.\n    // This can happen if the server patch action took longer to resolve than a subsequent navigation which would have changed the tree.\n    // Previously this case triggered an MPA navigation but it should be safe to simply discard the server response rather than forcing\n    // the entire page to reload.\n    if (newTree === null) {\n      return state\n    }\n\n    if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n      return handleExternalUrl(\n        state,\n        mutable,\n        state.canonicalUrl,\n        state.pushRef.pendingPush\n      )\n    }\n\n    const canonicalUrlOverrideHref = canonicalUrlOverride\n      ? createHrefFromUrl(canonicalUrlOverride)\n      : undefined\n\n    if (canonicalUrlOverrideHref) {\n      mutable.canonicalUrl = canonicalUrlOverrideHref\n    }\n\n    const cache: CacheNode = createEmptyCacheNode()\n    applyFlightData(navigatedAt, currentCache, cache, normalizedFlightData)\n\n    mutable.patchedTree = newTree\n    mutable.cache = cache\n\n    currentCache = cache\n    currentTree = newTree\n  }\n\n  return handleMutable(state, mutable)\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightRouterState } from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\n\n/**\n * Invalidate cache one level down from the router state.\n */\nexport function invalidateCacheByRouterState(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  routerState: FlightRouterState\n): void {\n  // Remove segment that we got data for so that it is filled in during rendering of rsc.\n  for (const key in routerState[1]) {\n    const segmentForParallelRoute = routerState[1][key][0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n    const existingParallelRoutesCacheNode =\n      existingCache.parallelRoutes.get(key)\n    if (existingParallelRoutesCacheNode) {\n      let parallelRouteCacheNode = new Map(existingParallelRoutesCacheNode)\n      parallelRouteCacheNode.delete(cacheKey)\n      newCache.parallelRoutes.set(key, parallelRouteCacheNode)\n    }\n  }\n}\n", "import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  Mutable,\n  ReadonlyReducerState,\n  ReducerState,\n  RefreshAction,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport { revalidateEntireCache } from '../../segment-cache'\n\nexport function refreshReducer(\n  state: ReadonlyReducerState,\n  action: RefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [\n      currentTree[0],\n      currentTree[1],\n      currentTree[2],\n      'refetch',\n    ],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n  })\n\n  const navigatedAt = Date.now()\n  return cache.lazyData.then(\n    async ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n\n        // Handles case where prefetch only returns the router tree patch without rendered components.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const loading = cacheNodeSeedData[3]\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = loading\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as `router.refresh()` has to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n        }\n\n        await refreshInactiveParallelSegments({\n          navigatedAt,\n          state,\n          updatedTree: newTree,\n          updatedCache: cache,\n          includeNextUrl,\n          canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n        })\n\n        mutable.cache = cache\n        mutable.patchedTree = newTree\n\n        currentTree = newTree\n      }\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n", "function _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexport { _class_private_field_loose_base as _ };\n", "import type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../../server/app-render/types'\nimport { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { invalidateCacheBelowFlightSegmentPath } from '../invalidate-cache-below-flight-segmentpath'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { shouldHardNavigate } from '../should-hard-navigate'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport {\n  PrefetchCacheEntryStatus,\n  type Mutable,\n  type NavigateAction,\n  type ReadonlyReducerState,\n  type ReducerState,\n} from '../router-reducer-types'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport { prefetchQueue } from './prefetch-reducer'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { DEFAULT_SEGMENT_KEY } from '../../../../shared/lib/segment'\nimport { listenForDynamicRequest, startPPRNavigation } from '../ppr-navigations'\nimport {\n  getOrCreatePrefetchCacheEntry,\n  prunePrefetchCache,\n} from '../prefetch-cache-utils'\nimport { clearCacheNodeDataForSegmentPath } from '../clear-cache-node-data-for-segment-path'\nimport { handleAliasedPrefetchEntry } from '../aliased-prefetch-navigations'\nimport {\n  navigate as navigateUsingSegmentCache,\n  NavigationResultTag,\n  type NavigationResult,\n} from '../../segment-cache'\n\nexport function handleExternalUrl(\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  url: string,\n  pendingPush: boolean\n) {\n  mutable.mpaNavigation = true\n  mutable.canonicalUrl = url\n  mutable.pendingPush = pendingPush\n  mutable.scrollableSegments = undefined\n\n  return handleMutable(state, mutable)\n}\n\nfunction generateSegmentsFromPatch(\n  flightRouterPatch: FlightRouterState\n): FlightSegmentPath[] {\n  const segments: FlightSegmentPath[] = []\n  const [segment, parallelRoutes] = flightRouterPatch\n\n  if (Object.keys(parallelRoutes).length === 0) {\n    return [[segment]]\n  }\n\n  for (const [parallelRouteKey, parallelRoute] of Object.entries(\n    parallelRoutes\n  )) {\n    for (const childSegment of generateSegmentsFromPatch(parallelRoute)) {\n      // If the segment is empty, it means we are at the root of the tree\n      if (segment === '') {\n        segments.push([parallelRouteKey, ...childSegment])\n      } else {\n        segments.push([segment, parallelRouteKey, ...childSegment])\n      }\n    }\n  }\n\n  return segments\n}\n\nfunction triggerLazyFetchForLeafSegments(\n  newCache: CacheNode,\n  currentCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath,\n  treePatch: FlightRouterState\n) {\n  let appliedPatch = false\n\n  newCache.rsc = currentCache.rsc\n  newCache.prefetchRsc = currentCache.prefetchRsc\n  newCache.loading = currentCache.loading\n  newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n  const segmentPathsToFill = generateSegmentsFromPatch(treePatch).map(\n    (segment) => [...flightSegmentPath, ...segment]\n  )\n\n  for (const segmentPaths of segmentPathsToFill) {\n    clearCacheNodeDataForSegmentPath(newCache, currentCache, segmentPaths)\n\n    appliedPatch = true\n  }\n\n  return appliedPatch\n}\n\nfunction handleNavigationResult(\n  url: URL,\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  pendingPush: boolean,\n  result: NavigationResult\n): ReducerState {\n  switch (result.tag) {\n    case NavigationResultTag.MPA: {\n      // Perform an MPA navigation.\n      const newUrl = result.data\n      return handleExternalUrl(state, mutable, newUrl, pendingPush)\n    }\n    case NavigationResultTag.NoOp: {\n      // The server responded with no change to the current page. However, if\n      // the URL changed, we still need to update that.\n      const newCanonicalUrl = result.data.canonicalUrl\n      mutable.canonicalUrl = newCanonicalUrl\n\n      // Check if the only thing that changed was the hash fragment.\n      const oldUrl = new URL(state.canonicalUrl, url)\n      const onlyHashChange =\n        // We don't need to compare the origins, because client-driven\n        // navigations are always same-origin.\n        url.pathname === oldUrl.pathname &&\n        url.search === oldUrl.search &&\n        url.hash !== oldUrl.hash\n      if (onlyHashChange) {\n        // The only updated part of the URL is the hash.\n        mutable.onlyHashChange = true\n        mutable.shouldScroll = result.data.shouldScroll\n        mutable.hashFragment = url.hash\n        // Setting this to an empty array triggers a scroll for all new and\n        // updated segments. See `ScrollAndFocusHandler` for more details.\n        mutable.scrollableSegments = []\n      }\n\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Success: {\n      // Received a new result.\n      mutable.cache = result.data.cacheNode\n      mutable.patchedTree = result.data.flightRouterState\n      mutable.canonicalUrl = result.data.canonicalUrl\n      mutable.scrollableSegments = result.data.scrollableSegments\n      mutable.shouldScroll = result.data.shouldScroll\n      mutable.hashFragment = result.data.hash\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Async: {\n      return result.data.then(\n        (asyncResult) =>\n          handleNavigationResult(url, state, mutable, pendingPush, asyncResult),\n        // If the navigation failed, return the current state.\n        // TODO: This matches the current behavior but we need to do something\n        // better here if the network fails.\n        () => {\n          return state\n        }\n      )\n    }\n    default: {\n      result satisfies never\n      return state\n    }\n  }\n}\n\nexport function navigateReducer(\n  state: ReadonlyReducerState,\n  action: NavigateAction\n): ReducerState {\n  const { url, isExternalUrl, navigateType, shouldScroll, allowAliasing } =\n    action\n  const mutable: Mutable = {}\n  const { hash } = url\n  const href = createHrefFromUrl(url)\n  const pendingPush = navigateType === 'push'\n  // we want to prune the prefetch cache on every navigation to avoid it growing too large\n  prunePrefetchCache(state.prefetchCache)\n\n  mutable.preserveCustomHistoryState = false\n  mutable.pendingPush = pendingPush\n\n  if (isExternalUrl) {\n    return handleExternalUrl(state, mutable, url.toString(), pendingPush)\n  }\n\n  // Handles case where `<meta http-equiv=\"refresh\">` tag is present,\n  // which will trigger an MPA navigation.\n  if (document.getElementById('__next-page-redirect')) {\n    return handleExternalUrl(state, mutable, href, pendingPush)\n  }\n\n  if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // (Very Early Experimental Feature) Segment Cache\n    //\n    // Bypass the normal prefetch cache and use the new per-segment cache\n    // implementation instead. This is only supported if PPR is enabled, too.\n    //\n    // Temporary glue code between the router reducer and the new navigation\n    // implementation. Eventually we'll rewrite the router reducer to a\n    // state machine.\n    const result = navigateUsingSegmentCache(\n      url,\n      state.cache,\n      state.tree,\n      state.nextUrl,\n      shouldScroll\n    )\n    return handleNavigationResult(url, state, mutable, pendingPush, result)\n  }\n\n  const prefetchValues = getOrCreatePrefetchCacheEntry({\n    url,\n    nextUrl: state.nextUrl,\n    tree: state.tree,\n    prefetchCache: state.prefetchCache,\n    allowAliasing,\n  })\n  const { treeAtTimeOfPrefetch, data } = prefetchValues\n\n  prefetchQueue.bump(data)\n\n  return data.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride, postponed }) => {\n      const navigatedAt = Date.now()\n\n      let isFirstRead = false\n      // we only want to mark this once\n      if (!prefetchValues.lastUsedTime) {\n        // important: we should only mark the cache node as dirty after we unsuspend from the call above\n        prefetchValues.lastUsedTime = navigatedAt\n        isFirstRead = true\n      }\n\n      if (prefetchValues.aliased) {\n        const result = handleAliasedPrefetchEntry(\n          navigatedAt,\n          state,\n          flightData,\n          url,\n          mutable\n        )\n\n        // We didn't return new router state because we didn't apply the aliased entry for some reason.\n        // We'll re-invoke the navigation handler but ensure that we don't attempt to use the aliased entry. This\n        // will create an on-demand prefetch entry.\n        if (result === false) {\n          return navigateReducer(state, { ...action, allowAliasing: false })\n        }\n\n        return result\n      }\n\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(state, mutable, flightData, pendingPush)\n      }\n\n      const updatedCanonicalUrl = canonicalUrlOverride\n        ? createHrefFromUrl(canonicalUrlOverride)\n        : href\n\n      const onlyHashChange =\n        !!hash &&\n        state.canonicalUrl.split('#', 1)[0] ===\n          updatedCanonicalUrl.split('#', 1)[0]\n\n      // If only the hash has changed, the server hasn't sent us any new data. We can just update\n      // the mutable properties responsible for URL and scroll handling and return early.\n      if (onlyHashChange) {\n        mutable.onlyHashChange = true\n        mutable.canonicalUrl = updatedCanonicalUrl\n        mutable.shouldScroll = shouldScroll\n        mutable.hashFragment = hash\n        mutable.scrollableSegments = []\n        return handleMutable(state, mutable)\n      }\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n      let scrollableSegments: FlightSegmentPath[] = []\n      for (const normalizedFlightData of flightData) {\n        const {\n          pathToSegment: flightSegmentPath,\n          seedData,\n          head,\n          isHeadPartial,\n          isRootRender,\n        } = normalizedFlightData\n        let treePatch = normalizedFlightData.tree\n\n        // TODO-APP: remove ''\n        const flightSegmentPathWithLeadingEmpty = ['', ...flightSegmentPath]\n\n        // Create new tree based on the flightSegmentPath and router state patch\n        let newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          flightSegmentPathWithLeadingEmpty,\n          currentTree,\n          treePatch,\n          href\n        )\n\n        // If the tree patch can't be applied to the current tree then we use the tree at time of prefetch\n        // TODO-APP: This should instead fill in the missing pieces in `currentTree` with the data from `treeAtTimeOfPrefetch`, then apply the patch.\n        if (newTree === null) {\n          newTree = applyRouterStatePatchToTree(\n            // TODO-APP: remove ''\n            flightSegmentPathWithLeadingEmpty,\n            treeAtTimeOfPrefetch,\n            treePatch,\n            href\n          )\n        }\n\n        if (newTree !== null) {\n          if (\n            // This is just a paranoid check. When a route is PPRed, the server\n            // will send back a static response that's rendered from\n            // the root. If for some reason it doesn't, we fall back to the\n            // non-PPR implementation.\n            // TODO: We should get rid of the else branch and do all navigations\n            // via startPPRNavigation. The current structure is just\n            // an incremental step.\n            seedData &&\n            isRootRender &&\n            postponed\n          ) {\n            const task = startPPRNavigation(\n              navigatedAt,\n              currentCache,\n              currentTree,\n              treePatch,\n              seedData,\n              head,\n              isHeadPartial,\n              false,\n              scrollableSegments\n            )\n\n            if (task !== null) {\n              if (task.route === null) {\n                // Detected a change to the root layout. Perform an full-\n                // page navigation.\n                return handleExternalUrl(state, mutable, href, pendingPush)\n              }\n              // Use the tree computed by startPPRNavigation instead\n              // of the one computed by applyRouterStatePatchToTree.\n              // TODO: We should remove applyRouterStatePatchToTree\n              // from the PPR path entirely.\n              const patchedRouterState: FlightRouterState = task.route\n              newTree = patchedRouterState\n\n              const newCache = task.node\n              if (newCache !== null) {\n                // We've created a new Cache Node tree that contains a prefetched\n                // version of the next page. This can be rendered instantly.\n                mutable.cache = newCache\n              }\n              const dynamicRequestTree = task.dynamicRequestTree\n              if (dynamicRequestTree !== null) {\n                // The prefetched tree has dynamic holes in it. We initiate a\n                // dynamic request to fill them in.\n                //\n                // Do not block on the result. We'll immediately render the Cache\n                // Node tree and suspend on the dynamic parts. When the request\n                // comes in, we'll fill in missing data and ping React to\n                // re-render. Unlike the lazy fetching model in the non-PPR\n                // implementation, this is modeled as a single React update +\n                // streaming, rather than multiple top-level updates. (However,\n                // even in the new model, we'll still need to sometimes update the\n                // root multiple times per navigation, like if the server sends us\n                // a different response than we expected. For now, we revert back\n                // to the lazy fetching mechanism in that case.)\n                const dynamicRequest = fetchServerResponse(url, {\n                  flightRouterState: dynamicRequestTree,\n                  nextUrl: state.nextUrl,\n                })\n\n                listenForDynamicRequest(task, dynamicRequest)\n                // We store the dynamic request on the `lazyData` property of the CacheNode\n                // because we're not going to await the dynamic request here. Since we're not blocking\n                // on the dynamic request, `layout-router` will\n                // task.node.lazyData = dynamicRequest\n              } else {\n                // The prefetched tree does not contain dynamic holes — it's\n                // fully static. We can skip the dynamic request.\n              }\n            } else {\n              // Nothing changed, so reuse the old cache.\n              // TODO: What if the head changed but not any of the segment data?\n              // Is that possible? If so, we should clone the whole tree and\n              // update the head.\n              newTree = treePatch\n            }\n          } else {\n            // The static response does not include any dynamic holes, so\n            // there's no need to do a second request.\n            // TODO: As an incremental step this just reverts back to the\n            // non-PPR implementation. We can simplify this branch further,\n            // given that PPR prefetches are always static and return the whole\n            // tree. Or in the meantime we could factor it out into a\n            // separate function.\n\n            if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n              return handleExternalUrl(state, mutable, href, pendingPush)\n            }\n\n            const cache: CacheNode = createEmptyCacheNode()\n            let applied = false\n\n            if (\n              prefetchValues.status === PrefetchCacheEntryStatus.stale &&\n              !isFirstRead\n            ) {\n              // When we have a stale prefetch entry, we only want to re-use the loading state of the route we're navigating to, to support instant loading navigations\n              // this will trigger a lazy fetch for the actual page data by nulling the `rsc` and `prefetchRsc` values for page data,\n              // while copying over the `loading` for the segment that contains the page data.\n              // We only do this on subsequent reads, as otherwise there'd be no loading data to re-use.\n\n              // We skip this branch if only the hash fragment has changed, as we don't want to trigger a lazy fetch in that case\n              applied = triggerLazyFetchForLeafSegments(\n                cache,\n                currentCache,\n                flightSegmentPath,\n                treePatch\n              )\n              // since we re-used the stale cache's loading state & refreshed the data,\n              // update the `lastUsedTime` so that it can continue to be re-used for the next 30s\n              prefetchValues.lastUsedTime = navigatedAt\n            } else {\n              applied = applyFlightData(\n                navigatedAt,\n                currentCache,\n                cache,\n                normalizedFlightData,\n                prefetchValues\n              )\n            }\n\n            const hardNavigate = shouldHardNavigate(\n              // TODO-APP: remove ''\n              flightSegmentPathWithLeadingEmpty,\n              currentTree\n            )\n\n            if (hardNavigate) {\n              // Copy rsc for the root node of the cache.\n              cache.rsc = currentCache.rsc\n              cache.prefetchRsc = currentCache.prefetchRsc\n\n              invalidateCacheBelowFlightSegmentPath(\n                cache,\n                currentCache,\n                flightSegmentPath\n              )\n              // Ensure the existing cache value is used when the cache was not invalidated.\n              mutable.cache = cache\n            } else if (applied) {\n              mutable.cache = cache\n              // If we applied the cache, we update the \"current cache\" value so any other\n              // segments in the FlightDataPath will be able to reference the updated cache.\n              currentCache = cache\n            }\n\n            for (const subSegment of generateSegmentsFromPatch(treePatch)) {\n              const scrollableSegmentPath = [\n                ...flightSegmentPath,\n                ...subSegment,\n              ]\n              // Filter out the __DEFAULT__ paths as they shouldn't be scrolled to in this case.\n              if (\n                scrollableSegmentPath[scrollableSegmentPath.length - 1] !==\n                DEFAULT_SEGMENT_KEY\n              ) {\n                scrollableSegments.push(scrollableSegmentPath)\n              }\n            }\n          }\n\n          currentTree = newTree\n        }\n      }\n\n      mutable.patchedTree = currentTree\n      mutable.canonicalUrl = updatedCanonicalUrl\n      mutable.scrollableSegments = scrollableSegments\n      mutable.hashFragment = hash\n      mutable.shouldScroll = shouldScroll\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n", "/**\n * Entry point to the Segment Cache implementation.\n *\n * All code related to the Segment Cache lives `segment-cache-impl` directory.\n * Callers access it through this indirection.\n *\n * This is to ensure the code is dead code eliminated from the bundle if the\n * flag is disabled.\n *\n * TODO: This is super tedious. Since experimental flags are an essential part\n * of our workflow, we should establish a better pattern for dead code\n * elimination. Ideally it would be done at the bundler level, like how React's\n * build process works. In the React repo, you don't even need to add any extra\n * configuration per experiment — if the code is not reachable, it gets stripped\n * from the build automatically by Rollup. Or, shorter term, we could stub out\n * experimental modules at build time by updating the build config, i.e. a more\n * automated version of what I'm doing manually in this file.\n */\n\nexport type { NavigationResult } from './segment-cache-impl/navigation'\nexport type { PrefetchTask } from './segment-cache-impl/scheduler'\n\nconst notEnabled: any = () => {\n  throw new Error(\n    'Segment Cache experiment is not enabled. This is a bug in Next.js.'\n  )\n}\n\nexport const prefetch: typeof import('./segment-cache-impl/prefetch').prefetch =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/prefetch').prefetch(...args)\n      }\n    : notEnabled\n\nexport const navigate: typeof import('./segment-cache-impl/navigation').navigate =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/navigation').navigate(...args)\n      }\n    : notEnabled\n\nexport const revalidateEntireCache: typeof import('./segment-cache-impl/cache').revalidateEntireCache =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').revalidateEntireCache(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const getCurrentCacheVersion: typeof import('./segment-cache-impl/cache').getCurrentCacheVersion =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').getCurrentCacheVersion(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const schedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').schedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').schedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const cancelPrefetchTask: typeof import('./segment-cache-impl/scheduler').cancelPrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').cancelPrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const reschedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').reschedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').reschedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const createCacheKey: typeof import('./segment-cache-impl/cache-key').createCacheKey =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache-key').createCacheKey(...args)\n      }\n    : notEnabled\n\n/**\n * Below are public constants. They're small enough that we don't need to\n * DCE them.\n */\n\nexport const enum NavigationResultTag {\n  MPA,\n  Success,\n  NoOp,\n  Async,\n}\n\n/**\n * The priority of the prefetch task. Higher numbers are higher priority.\n */\nexport const enum PrefetchPriority {\n  /**\n   * Assigned to any visible link that was hovered/touched at some point. This\n   * is not removed on mouse exit, because a link that was momentarily\n   * hovered is more likely to to be interacted with than one that was not.\n   */\n  Intent = 2,\n  /**\n   * The default priority for prefetch tasks.\n   */\n  Default = 1,\n  /**\n   * Assigned to tasks when they spawn non-blocking background work, like\n   * revalidating a partially cached entry to see if more data is available.\n   */\n  Background = 0,\n}\n", "import {\n  fetchServerResponse,\n  type FetchServerResponseResult,\n} from './fetch-server-response'\nimport {\n  PrefetchCacheEntryStatus,\n  type PrefetchCacheEntry,\n  PrefetchKind,\n  type ReadonlyReducerState,\n} from './router-reducer-types'\nimport { prefetchQueue } from './reducers/prefetch-reducer'\n\nconst INTERCEPTION_CACHE_KEY_MARKER = '%'\n\nexport type AliasedPrefetchCacheEntry = PrefetchCacheEntry & {\n  /** This is a special property that indicates a prefetch entry associated with a different URL\n   * was returned rather than the requested URL. This signals to the router that it should only\n   * apply the part that doesn't depend on searchParams (specifically the loading state).\n   */\n  aliased?: boolean\n}\n\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */\nfunction createPrefetchCacheKeyImpl(\n  url: URL,\n  includeSearchParams: boolean,\n  prefix?: string | null\n) {\n  // Initially we only use the pathname as the cache key. We don't want to include\n  // search params so that multiple URLs with the same search parameter can re-use\n  // loading states.\n  let pathnameFromUrl = url.pathname\n\n  // RSC responses can differ based on search params, specifically in the case where we aren't\n  // returning a partial response (ie with `PrefetchKind.AUTO`).\n  // In the auto case, since loading.js & layout.js won't have access to search params,\n  // we can safely re-use that cache entry. But for full prefetches, we should not\n  // re-use the cache entry as the response may differ.\n  if (includeSearchParams) {\n    // if we have a full prefetch, we can include the search param in the key,\n    // as we'll be getting back a full response. The server might have read the search\n    // params when generating the full response.\n    pathnameFromUrl += url.search\n  }\n\n  if (prefix) {\n    return `${prefix}${INTERCEPTION_CACHE_KEY_MARKER}${pathnameFromUrl}`\n  }\n\n  return pathnameFromUrl\n}\n\nfunction createPrefetchCacheKey(\n  url: URL,\n  kind: PrefetchKind | undefined,\n  nextUrl?: string | null\n) {\n  return createPrefetchCacheKeyImpl(url, kind === PrefetchKind.FULL, nextUrl)\n}\n\nfunction getExistingCacheEntry(\n  url: URL,\n  kind: PrefetchKind = PrefetchKind.TEMPORARY,\n  nextUrl: string | null,\n  prefetchCache: Map<string, PrefetchCacheEntry>,\n  allowAliasing: boolean\n): AliasedPrefetchCacheEntry | undefined {\n  // We first check if there's a more specific interception route prefetch entry\n  // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n  // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n  for (const maybeNextUrl of [nextUrl, null]) {\n    const cacheKeyWithParams = createPrefetchCacheKeyImpl(\n      url,\n      true,\n      maybeNextUrl\n    )\n    const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(\n      url,\n      false,\n      maybeNextUrl\n    )\n\n    // First, we check if we have a cache entry that exactly matches the URL\n    const cacheKeyToUse = url.search\n      ? cacheKeyWithParams\n      : cacheKeyWithoutParams\n\n    const existingEntry = prefetchCache.get(cacheKeyToUse)\n    if (existingEntry && allowAliasing) {\n      // We know we're returning an aliased entry when the pathname matches but the search params don't,\n      const isAliased =\n        existingEntry.url.pathname === url.pathname &&\n        existingEntry.url.search !== url.search\n\n      if (isAliased) {\n        return {\n          ...existingEntry,\n          aliased: true,\n        }\n      }\n\n      return existingEntry\n    }\n\n    // If the request contains search params, and we're not doing a full prefetch, we can return the\n    // param-less entry if it exists.\n    // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n    // but lets us arrive there quicker in the param-full case.\n    const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams)\n    if (\n      process.env.NODE_ENV !== 'development' &&\n      allowAliasing &&\n      url.search &&\n      kind !== PrefetchKind.FULL &&\n      entryWithoutParams &&\n      // We shouldn't return the aliased entry if it was relocated to a new cache key.\n      // Since it's rewritten, it could respond with a completely different loading state.\n      !entryWithoutParams.key.includes(INTERCEPTION_CACHE_KEY_MARKER)\n    ) {\n      return { ...entryWithoutParams, aliased: true }\n    }\n  }\n\n  // If we've gotten to this point, we didn't find a specific cache entry that matched\n  // the request URL.\n  // We attempt a partial match by checking if there's a cache entry with the same pathname.\n  // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n  // This will signal to the router that it should only apply the loading state on the prefetched data.\n  if (\n    process.env.NODE_ENV !== 'development' &&\n    kind !== PrefetchKind.FULL &&\n    allowAliasing\n  ) {\n    for (const cacheEntry of prefetchCache.values()) {\n      if (\n        cacheEntry.url.pathname === url.pathname &&\n        // We shouldn't return the aliased entry if it was relocated to a new cache key.\n        // Since it's rewritten, it could respond with a completely different loading state.\n        !cacheEntry.key.includes(INTERCEPTION_CACHE_KEY_MARKER)\n      ) {\n        return { ...cacheEntry, aliased: true }\n      }\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Returns a prefetch cache entry if one exists. Otherwise creates a new one and enqueues a fetch request\n * to retrieve the prefetch data from the server.\n */\nexport function getOrCreatePrefetchCacheEntry({\n  url,\n  nextUrl,\n  tree,\n  prefetchCache,\n  kind,\n  allowAliasing = true,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'prefetchCache' | 'tree'> & {\n  url: URL\n  kind?: PrefetchKind\n  allowAliasing: boolean\n}): AliasedPrefetchCacheEntry {\n  const existingCacheEntry = getExistingCacheEntry(\n    url,\n    kind,\n    nextUrl,\n    prefetchCache,\n    allowAliasing\n  )\n\n  if (existingCacheEntry) {\n    // Grab the latest status of the cache entry and update it\n    existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry)\n\n    // when `kind` is provided, an explicit prefetch was requested.\n    // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n    const switchedToFullPrefetch =\n      existingCacheEntry.kind !== PrefetchKind.FULL &&\n      kind === PrefetchKind.FULL\n\n    if (switchedToFullPrefetch) {\n      // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n      // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n      // are seeded but without a prefetch intent)\n      existingCacheEntry.data.then((prefetchResponse) => {\n        const isFullPrefetch =\n          Array.isArray(prefetchResponse.flightData) &&\n          prefetchResponse.flightData.some((flightData) => {\n            // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n            return flightData.isRootRender && flightData.seedData !== null\n          })\n\n        if (!isFullPrefetch) {\n          return createLazyPrefetchEntry({\n            tree,\n            url,\n            nextUrl,\n            prefetchCache,\n            // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n            // rather than assuming the same intent as the previous entry, to be consistent with how we\n            // lazily create prefetch entries when intent is left unspecified.\n            kind: kind ?? PrefetchKind.TEMPORARY,\n          })\n        }\n      })\n    }\n\n    // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n    // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n    if (kind && existingCacheEntry.kind === PrefetchKind.TEMPORARY) {\n      existingCacheEntry.kind = kind\n    }\n\n    // We've determined that the existing entry we found is still valid, so we return it.\n    return existingCacheEntry\n  }\n\n  // If we didn't return an entry, create a new one.\n  return createLazyPrefetchEntry({\n    tree,\n    url,\n    nextUrl,\n    prefetchCache,\n    kind: kind || PrefetchKind.TEMPORARY,\n  })\n}\n\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */\nfunction prefixExistingPrefetchCacheEntry({\n  url,\n  nextUrl,\n  prefetchCache,\n  existingCacheKey,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'prefetchCache'> & {\n  url: URL\n  existingCacheKey: string\n}) {\n  const existingCacheEntry = prefetchCache.get(existingCacheKey)\n  if (!existingCacheEntry) {\n    // no-op -- there wasn't an entry to move\n    return\n  }\n\n  const newCacheKey = createPrefetchCacheKey(\n    url,\n    existingCacheEntry.kind,\n    nextUrl\n  )\n  prefetchCache.set(newCacheKey, { ...existingCacheEntry, key: newCacheKey })\n  prefetchCache.delete(existingCacheKey)\n\n  return newCacheKey\n}\n\n/**\n * Use to seed the prefetch cache with data that has already been fetched.\n */\nexport function createSeededPrefetchCacheEntry({\n  nextUrl,\n  tree,\n  prefetchCache,\n  url,\n  data,\n  kind,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'tree' | 'prefetchCache'> & {\n  url: URL\n  data: FetchServerResponseResult\n  kind: PrefetchKind\n}) {\n  // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n  // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n  // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n  const prefetchCacheKey = data.couldBeIntercepted\n    ? createPrefetchCacheKey(url, kind, nextUrl)\n    : createPrefetchCacheKey(url, kind)\n\n  const prefetchEntry = {\n    treeAtTimeOfPrefetch: tree,\n    data: Promise.resolve(data),\n    kind,\n    prefetchTime: Date.now(),\n    lastUsedTime: Date.now(),\n    staleTime: -1,\n    key: prefetchCacheKey,\n    status: PrefetchCacheEntryStatus.fresh,\n    url,\n  } satisfies PrefetchCacheEntry\n\n  prefetchCache.set(prefetchCacheKey, prefetchEntry)\n\n  return prefetchEntry\n}\n\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */\nfunction createLazyPrefetchEntry({\n  url,\n  kind,\n  tree,\n  nextUrl,\n  prefetchCache,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'tree' | 'prefetchCache'> & {\n  url: URL\n  kind: PrefetchKind\n}): PrefetchCacheEntry {\n  const prefetchCacheKey = createPrefetchCacheKey(url, kind)\n\n  // initiates the fetch request for the prefetch and attaches a listener\n  // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n  const data = prefetchQueue.enqueue(() =>\n    fetchServerResponse(url, {\n      flightRouterState: tree,\n      nextUrl,\n      prefetchKind: kind,\n    }).then((prefetchResponse) => {\n      // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n      // to avoid drift between this cache key prefixing logic\n      // (which is currently directly influenced by the server response)\n      let newCacheKey\n\n      if (prefetchResponse.couldBeIntercepted) {\n        // Determine if we need to prefix the cache key with the nextUrl\n        newCacheKey = prefixExistingPrefetchCacheEntry({\n          url,\n          existingCacheKey: prefetchCacheKey,\n          nextUrl,\n          prefetchCache,\n        })\n      }\n\n      // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n      // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n      // staleTime.\n      if (prefetchResponse.prerendered) {\n        const existingCacheEntry = prefetchCache.get(\n          // if we prefixed the cache key due to route interception, we want to use the new key. Otherwise we use the original key\n          newCacheKey ?? prefetchCacheKey\n        )\n        if (existingCacheEntry) {\n          existingCacheEntry.kind = PrefetchKind.FULL\n          if (prefetchResponse.staleTime !== -1) {\n            // This is the stale time that was collected by the server during\n            // static generation. Use this in place of the default stale time.\n            existingCacheEntry.staleTime = prefetchResponse.staleTime\n          }\n        }\n      }\n\n      return prefetchResponse\n    })\n  )\n\n  const prefetchEntry = {\n    treeAtTimeOfPrefetch: tree,\n    data,\n    kind,\n    prefetchTime: Date.now(),\n    lastUsedTime: null,\n    staleTime: -1,\n    key: prefetchCacheKey,\n    status: PrefetchCacheEntryStatus.fresh,\n    url,\n  }\n\n  prefetchCache.set(prefetchCacheKey, prefetchEntry)\n\n  return prefetchEntry\n}\n\nexport function prunePrefetchCache(\n  prefetchCache: ReadonlyReducerState['prefetchCache']\n) {\n  for (const [href, prefetchCacheEntry] of prefetchCache) {\n    if (\n      getPrefetchEntryCacheStatus(prefetchCacheEntry) ===\n      PrefetchCacheEntryStatus.expired\n    ) {\n      prefetchCache.delete(href)\n    }\n  }\n}\n\n// These values are set by `define-env-plugin` (based on `nextConfig.experimental.staleTimes`)\n// and default to 5 minutes (static) / 0 seconds (dynamic)\nexport const DYNAMIC_STALETIME_MS =\n  Number(process.env.__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME) * 1000\n\nexport const STATIC_STALETIME_MS =\n  Number(process.env.__NEXT_CLIENT_ROUTER_STATIC_STALETIME) * 1000\n\nfunction getPrefetchEntryCacheStatus({\n  kind,\n  prefetchTime,\n  lastUsedTime,\n  staleTime,\n}: PrefetchCacheEntry): PrefetchCacheEntryStatus {\n  if (staleTime !== -1) {\n    // `staleTime` is the value sent by the server during static generation.\n    // When this is available, it takes precedence over any of the heuristics\n    // that follow.\n    //\n    // TODO: When PPR is enabled, the server will *always* return a stale time\n    // when prefetching. We should never use a prefetch entry that hasn't yet\n    // received data from the server. So the only two cases should be 1) we use\n    // the server-generated stale time 2) the unresolved entry is discarded.\n    return Date.now() < prefetchTime + staleTime\n      ? PrefetchCacheEntryStatus.fresh\n      : PrefetchCacheEntryStatus.stale\n  }\n\n  // We will re-use the cache entry data for up to the `dynamic` staletime window.\n  if (Date.now() < (lastUsedTime ?? prefetchTime) + DYNAMIC_STALETIME_MS) {\n    return lastUsedTime\n      ? PrefetchCacheEntryStatus.reusable\n      : PrefetchCacheEntryStatus.fresh\n  }\n\n  // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n  // A stale entry will only re-use the `loading` boundary, not the full data.\n  // This will trigger a \"lazy fetch\" for the full data.\n  if (kind === PrefetchKind.AUTO) {\n    if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n      return PrefetchCacheEntryStatus.stale\n    }\n  }\n\n  // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n  if (kind === PrefetchKind.FULL) {\n    if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n      return PrefetchCacheEntryStatus.reusable\n    }\n  }\n\n  return PrefetchCacheEntryStatus.expired\n}\n", "import type { FlightRouterState } from '../../../../server/app-render/types'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createRouterCacheKey } from '../create-router-cache-key'\n\nexport function findHeadInCache(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1]\n): [CacheNode, string] | null {\n  return findHeadInCacheImpl(cache, parallelRoutes, '')\n}\n\nfunction findHeadInCacheImpl(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1],\n  keyPrefix: string\n): [CacheNode, string] | null {\n  const isLastItem = Object.keys(parallelRoutes).length === 0\n  if (isLastItem) {\n    // Returns the entire Cache Node of the segment whose head we will render.\n    return [cache, keyPrefix]\n  }\n\n  // First try the 'children' parallel route if it exists\n  // when starting from the \"root\", this corresponds with the main page component\n  if (parallelRoutes.children) {\n    const [segment, childParallelRoutes] = parallelRoutes.children\n    const childSegmentMap = cache.parallelRoutes.get('children')\n    if (childSegmentMap) {\n      const cacheKey = createRouterCacheKey(segment)\n      const cacheNode = childSegmentMap.get(cacheKey)\n      if (cacheNode) {\n        const item = findHeadInCacheImpl(\n          cacheNode,\n          childParallelRoutes,\n          keyPrefix + '/' + cacheKey\n        )\n        if (item) return item\n      }\n    }\n  }\n\n  // if we didn't find metadata in the page slot, check the other parallel routes\n  for (const key in parallelRoutes) {\n    if (key === 'children') continue // already checked above\n\n    const [segment, childParallelRoutes] = parallelRoutes[key]\n    const childSegmentMap = cache.parallelRoutes.get(key)\n    if (!childSegmentMap) {\n      continue\n    }\n\n    const cacheKey = createRouterCacheKey(segment)\n\n    const cacheNode = childSegmentMap.get(cacheKey)\n    if (!cacheNode) {\n      continue\n    }\n\n    const item = findHeadInCacheImpl(\n      cacheNode,\n      childParallelRoutes,\n      keyPrefix + '/' + cacheKey\n    )\n    if (item) {\n      return item\n    }\n  }\n\n  return null\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  CacheNodeSeedData,\n} from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport {\n  PrefetchCacheEntryStatus,\n  type PrefetchCacheEntry,\n} from './router-reducer-types'\n\nexport function fillLazyItemsTillLeafWithHead(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode | undefined,\n  routerState: FlightRouterState,\n  cacheNodeSeedData: CacheNodeSeedData | null,\n  head: React.ReactNode,\n  prefetchEntry: PrefetchCacheEntry | undefined\n): void {\n  const isLastSegment = Object.keys(routerState[1]).length === 0\n  if (isLastSegment) {\n    newCache.head = head\n    return\n  }\n  // Remove segment that we got data for so that it is filled in during rendering of rsc.\n  for (const key in routerState[1]) {\n    const parallelRouteState = routerState[1][key]\n    const segmentForParallelRoute = parallelRouteState[0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n\n    // TODO: We should traverse the cacheNodeSeedData tree instead of the router\n    // state tree. Ideally, they would always be the same shape, but because of\n    // the loading.js pattern, cacheNodeSeedData sometimes only represents a\n    // partial tree. That's why this node is sometimes null. Once PPR lands,\n    // loading.js will no longer have special behavior and we can traverse the\n    // data tree instead.\n    //\n    // We should also consider merging the router state tree and the data tree\n    // in the response format, so that we don't have to send the keys twice.\n    // Then the client can convert them into separate representations.\n    const parallelSeedData =\n      cacheNodeSeedData !== null && cacheNodeSeedData[2][key] !== undefined\n        ? cacheNodeSeedData[2][key]\n        : null\n    if (existingCache) {\n      const existingParallelRoutesCacheNode =\n        existingCache.parallelRoutes.get(key)\n      if (existingParallelRoutesCacheNode) {\n        const hasReusablePrefetch =\n          prefetchEntry?.kind === 'auto' &&\n          prefetchEntry.status === PrefetchCacheEntryStatus.reusable\n\n        let parallelRouteCacheNode = new Map(existingParallelRoutesCacheNode)\n        const existingCacheNode = parallelRouteCacheNode.get(cacheKey)\n        let newCacheNode: CacheNode\n        if (parallelSeedData !== null) {\n          // New data was sent from the server.\n          const seedNode = parallelSeedData[1]\n          const loading = parallelSeedData[3]\n          newCacheNode = {\n            lazyData: null,\n            rsc: seedNode,\n            // This is a PPR-only field. When PPR is enabled, we shouldn't hit\n            // this path during a navigation, but until PPR is fully implemented\n            // yet it's possible the existing node does have a non-null\n            // `prefetchRsc`. As an incremental step, we'll just de-opt to the\n            // old behavior — no PPR value.\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            loading,\n            parallelRoutes: new Map(existingCacheNode?.parallelRoutes),\n            navigatedAt,\n          }\n        } else if (hasReusablePrefetch && existingCacheNode) {\n          // No new data was sent from the server, but the existing cache node\n          // was prefetched, so we should reuse that.\n          newCacheNode = {\n            lazyData: existingCacheNode.lazyData,\n            rsc: existingCacheNode.rsc,\n            // This is a PPR-only field. Unlike the previous branch, since we're\n            // just cloning the existing cache node, we might as well keep the\n            // PPR value, if it exists.\n            prefetchRsc: existingCacheNode.prefetchRsc,\n            head: existingCacheNode.head,\n            prefetchHead: existingCacheNode.prefetchHead,\n            parallelRoutes: new Map(existingCacheNode.parallelRoutes),\n            loading: existingCacheNode.loading,\n          } as CacheNode\n        } else {\n          // No data available for this node. This will trigger a lazy fetch\n          // during render.\n          newCacheNode = {\n            lazyData: null,\n            rsc: null,\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            parallelRoutes: new Map(existingCacheNode?.parallelRoutes),\n            loading: null,\n            navigatedAt,\n          }\n        }\n\n        // Overrides the cache key with the new cache node.\n        parallelRouteCacheNode.set(cacheKey, newCacheNode)\n        // Traverse deeper to apply the head / fill lazy items till the head.\n        fillLazyItemsTillLeafWithHead(\n          navigatedAt,\n          newCacheNode,\n          existingCacheNode,\n          parallelRouteState,\n          parallelSeedData ? parallelSeedData : null,\n          head,\n          prefetchEntry\n        )\n\n        newCache.parallelRoutes.set(key, parallelRouteCacheNode)\n        continue\n      }\n    }\n\n    let newCacheNode: CacheNode\n    if (parallelSeedData !== null) {\n      // New data was sent from the server.\n      const seedNode = parallelSeedData[1]\n      const loading = parallelSeedData[3]\n      newCacheNode = {\n        lazyData: null,\n        rsc: seedNode,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading,\n        navigatedAt,\n      }\n    } else {\n      // No data available for this node. This will trigger a lazy fetch\n      // during render.\n      newCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt,\n      }\n    }\n\n    const existingParallelRoutes = newCache.parallelRoutes.get(key)\n    if (existingParallelRoutes) {\n      existingParallelRoutes.set(cacheKey, newCacheNode)\n    } else {\n      newCache.parallelRoutes.set(key, new Map([[cacheKey, newCacheNode]]))\n    }\n\n    fillLazyItemsTillLeafWithHead(\n      navigatedAt,\n      newCacheNode,\n      undefined,\n      parallelRouteState,\n      parallelSeedData,\n      head,\n      prefetchEntry\n    )\n  }\n}\n", "export interface ServerReferenceInfo {\n  type: 'server-action' | 'use-cache'\n  usedArgs: [boolean, boolean, boolean, boolean, boolean, boolean]\n  hasRestArgs: boolean\n}\n\n/**\n * Extracts info about the server reference for the given server reference ID by\n * parsing the first byte of the hex-encoded ID.\n *\n * ```\n * Bit positions: [7]      [6] [5] [4] [3] [2] [1]  [0]\n * Bits:          typeBit  argMask                  restArgs\n * ```\n *\n * If the `typeBit` is `1` the server reference represents a `\"use cache\"`\n * function, otherwise a server action.\n *\n * The `argMask` encodes whether the function uses the argument at the\n * respective position.\n *\n * The `restArgs` bit indicates whether the function uses a rest parameter. It's\n * also set to 1 if the function has more than 6 args.\n *\n * @param id hex-encoded server reference ID\n */\nexport function extractInfoFromServerReferenceId(\n  id: string\n): ServerReferenceInfo {\n  const infoByte = parseInt(id.slice(0, 2), 16)\n  const typeBit = (infoByte >> 7) & 0x1\n  const argMask = (infoByte >> 1) & 0x3f\n  const restArgs = infoByte & 0x1\n  const usedArgs = Array(6)\n\n  for (let index = 0; index < 6; index++) {\n    const bitPosition = 5 - index\n    const bit = (argMask >> bitPosition) & 0x1\n    usedArgs[index] = bit === 1\n  }\n\n  return {\n    type: typeBit === 1 ? 'use-cache' : 'server-action',\n    usedArgs: usedArgs as [\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n    ],\n    hasRestArgs: restArgs === 1,\n  }\n}\n\n/**\n * Creates a sparse array containing only the used arguments based on the\n * provided action info.\n */\nexport function omitUnusedArgs(\n  args: unknown[],\n  info: ServerReferenceInfo\n): unknown[] {\n  const filteredArgs = new Array(args.length)\n\n  for (let index = 0; index < args.length; index++) {\n    if (\n      (index < 6 && info.usedArgs[index]) ||\n      // This assumes that the server reference info byte has the restArgs bit\n      // set to 1 if there are more than 6 args.\n      (index >= 6 && info.hasRestArgs)\n    ) {\n      filteredArgs[index] = args[index]\n    }\n  }\n\n  return filteredArgs\n}\n", "import type { FlightRouterState } from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { AppRouterState } from './router-reducer-types'\nimport { applyFlightData } from './apply-flight-data'\nimport { fetchServerResponse } from './fetch-server-response'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\ninterface RefreshInactiveParallelSegments {\n  navigatedAt: number\n  state: AppRouterState\n  updatedTree: FlightRouterState\n  updatedCache: CacheNode\n  includeNextUrl: boolean\n  canonicalUrl: string\n}\n\n/**\n * Refreshes inactive segments that are still in the current FlightRouterState.\n * A segment is considered \"inactive\" when the server response indicates it didn't match to a page component.\n * This happens during a soft-navigation, where the server will want to patch in the segment\n * with the \"default\" component, but we explicitly ignore the server in this case\n * and keep the existing state for that segment. New data for inactive segments are inherently\n * not part of the server response when we patch the tree, because they were associated with a response\n * from an earlier navigation/request. For each segment, once it becomes \"active\", we encode the URL that provided\n * the data for it. This function traverses parallel routes looking for these markers so that it can re-fetch\n * and patch the new data into the tree.\n */\nexport async function refreshInactiveParallelSegments(\n  options: RefreshInactiveParallelSegments\n) {\n  const fetchedSegments = new Set<string>()\n  await refreshInactiveParallelSegmentsImpl({\n    ...options,\n    rootTree: options.updatedTree,\n    fetchedSegments,\n  })\n}\n\nasync function refreshInactiveParallelSegmentsImpl({\n  navigatedAt,\n  state,\n  updatedTree,\n  updatedCache,\n  includeNextUrl,\n  fetchedSegments,\n  rootTree = updatedTree,\n  canonicalUrl,\n}: RefreshInactiveParallelSegments & {\n  fetchedSegments: Set<string>\n  rootTree: FlightRouterState\n}) {\n  const [, parallelRoutes, refetchPath, refetchMarker] = updatedTree\n  const fetchPromises = []\n\n  if (\n    refetchPath &&\n    refetchPath !== canonicalUrl &&\n    refetchMarker === 'refresh' &&\n    // it's possible for the tree to contain multiple segments that contain data at the same URL\n    // we keep track of them so we can dedupe the requests\n    !fetchedSegments.has(refetchPath)\n  ) {\n    fetchedSegments.add(refetchPath) // Mark this URL as fetched\n\n    // Eagerly kick off the fetch for the refetch path & the parallel routes. This should be fine to do as they each operate\n    // independently on their own cache nodes, and `applyFlightData` will copy anything it doesn't care about from the existing cache.\n    const fetchPromise = fetchServerResponse(\n      new URL(refetchPath, location.origin),\n      {\n        // refetch from the root of the updated tree, otherwise it will be scoped to the current segment\n        // and might not contain the data we need to patch in interception route data (such as dynamic params from a previous segment)\n        flightRouterState: [rootTree[0], rootTree[1], rootTree[2], 'refetch'],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n      }\n    ).then(({ flightData }) => {\n      if (typeof flightData !== 'string') {\n        for (const flightDataPath of flightData) {\n          // we only pass the new cache as this function is called after clearing the router cache\n          // and filling in the new page data from the server. Meaning the existing cache is actually the cache that's\n          // just been created & has been written to, but hasn't been \"committed\" yet.\n          applyFlightData(\n            navigatedAt,\n            updatedCache,\n            updatedCache,\n            flightDataPath\n          )\n        }\n      } else {\n        // When flightData is a string, it suggests that the server response should have triggered an MPA navigation\n        // I'm not 100% sure of this decision, but it seems unlikely that we'd want to introduce a redirect side effect\n        // when refreshing on-screen data, so handling this has been ommitted.\n      }\n    })\n\n    fetchPromises.push(fetchPromise)\n  }\n\n  for (const key in parallelRoutes) {\n    const parallelFetchPromise = refreshInactiveParallelSegmentsImpl({\n      navigatedAt,\n      state,\n      updatedTree: parallelRoutes[key],\n      updatedCache,\n      includeNextUrl,\n      fetchedSegments,\n      rootTree,\n      canonicalUrl,\n    })\n\n    fetchPromises.push(parallelFetchPromise)\n  }\n\n  await Promise.all(fetchPromises)\n}\n\n/**\n * Walks the current parallel segments to determine if they are \"active\".\n * An active parallel route will have a `__PAGE__` segment in the FlightRouterState.\n * As opposed to a `__DEFAULT__` segment, which means there was no match for that parallel route.\n * We add a special marker here so that we know how to refresh its data when the router is revalidated.\n */\nexport function addRefreshMarkerToActiveParallelSegments(\n  tree: FlightRouterState,\n  path: string\n) {\n  const [segment, parallelRoutes, , refetchMarker] = tree\n  // a page segment might also contain concatenated search params, so we do a partial match on the key\n  if (segment.includes(PAGE_SEGMENT_KEY) && refetchMarker !== 'refresh') {\n    tree[2] = path\n    tree[3] = 'refresh'\n  }\n\n  for (const key in parallelRoutes) {\n    addRefreshMarkerToActiveParallelSegments(parallelRoutes[key], path)\n  }\n}\n", "'use client'\n\nimport React, {\n  use,\n  useEffect,\n  useMemo,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type { CacheNode } from '../../shared/lib/app-router-context.shared-runtime'\nimport { ACTION_RESTORE } from './router-reducer/router-reducer-types'\nimport type { AppRouterState } from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { dispatchAppRouterAction, useActionQueue } from './use-action-queue'\nimport {\n  default as DefaultGlobalError,\n  ErrorBoundary,\n  type GlobalErrorComponent,\n} from './error-boundary'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport {\n  dispatchTraverseAction,\n  publicAppRouterInstance,\n  type AppRouterActionQueue,\n} from './app-router-instance'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\nimport { pingVisibleLinks } from './links'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nexport function isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n\n  useEffect(() => {\n    // The Next-Url and the base tree may affect the result of a prefetch\n    // task. Re-prefetch all visible links with the updated values. In most\n    // cases, this will not result in any new network requests, only if\n    // the prefetch result actually varies on one of these inputs.\n    if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n      pingVisibleLinks(appRouterState.nextUrl, appRouterState.tree)\n    }\n  }, [appRouterState.nextUrl, appRouterState.tree])\n\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n    navigatedAt: -1,\n  }\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n  globalError,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const state = useActionQueue(actionQueue)\n  const { canonicalUrl } = state\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = state\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: publicAppRouterInstance,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatchAppRouterAction({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        // TODO: This should access the router methods directly, rather than\n        // go through the public interface.\n        if (redirectType === RedirectType.push) {\n          publicAppRouterInstance.push(url, {})\n        } else {\n          publicAppRouterInstance.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = state\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    use(unresolvedThenable)\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatchTraverseAction(\n          window.location.href,\n          event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n        )\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = state\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      parentTree: tree,\n      parentCacheNode: cache,\n      parentSegmentPath: null,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n    }\n  }, [tree, cache, canonicalUrl])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    // In development, we apply few error boundaries and hot-reloader:\n    // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n    // - HotReloader:\n    //  - hot-reload the app when the code changes\n    //  - render dev overlay\n    //  - catch runtime errors and display global-error when necessary\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('./react-dev-overlay/app/hot-reloader-client').default =\n      require('./react-dev-overlay/app/hot-reloader-client').default\n\n    content = (\n      <HotReloader assetPrefix={assetPrefix} globalError={globalError}>\n        {content}\n      </HotReloader>\n    )\n  } else {\n    // In production, we only apply the user-customized global error boundary.\n    content = (\n      <ErrorBoundary\n        errorComponent={globalError[0]}\n        errorStyles={globalError[1]}\n      >\n        {content}\n      </ErrorBoundary>\n    )\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={state} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              {/* TODO: We should be able to remove this context. useRouter\n                  should import from app-router-instance instead. It's only\n                  necessary because useRouter is shared between Pages and\n                  App Router. We should fork that module, then remove this\n                  context provider. */}\n              <AppRouterContext.Provider value={publicAppRouterInstance}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles],\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorComponentAndStyles: [GlobalErrorComponent, React.ReactNode]\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  return (\n    <ErrorBoundary\n      // At the very top level, use the default GlobalError component as the final fallback.\n      // When the app router itself fails, which means the framework itself fails, we show the default error.\n      errorComponent={DefaultGlobalError}\n    >\n      <Router\n        actionQueue={actionQueue}\n        assetPrefix={assetPrefix}\n        globalError={[globalErrorComponent, globalErrorStyles]}\n      />\n    </ErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n", "import { useEffect, useRef, useState } from 'react'\nimport { createPortal } from 'react-dom'\nimport type { FlightRouterState } from '../../server/app-render/types'\n\nconst ANNOUNCER_TYPE = 'next-route-announcer'\nconst ANNOUNCER_ID = '__next-route-announcer__'\n\nfunction getAnnouncerNode() {\n  const existingAnnouncer = document.getElementsByName(ANNOUNCER_TYPE)[0]\n  if (existingAnnouncer?.shadowRoot?.childNodes[0]) {\n    return existingAnnouncer.shadowRoot.childNodes[0] as HTMLElement\n  } else {\n    const container = document.createElement(ANNOUNCER_TYPE)\n    container.style.cssText = 'position:absolute'\n    const announcer = document.createElement('div')\n    announcer.ariaLive = 'assertive'\n    announcer.id = ANNOUNCER_ID\n    announcer.role = 'alert'\n    announcer.style.cssText =\n      'position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal'\n\n    // Use shadow DOM here to avoid any potential CSS bleed\n    const shadow = container.attachShadow({ mode: 'open' })\n    shadow.appendChild(announcer)\n    document.body.appendChild(container)\n    return announcer\n  }\n}\n\nexport function AppRouterAnnouncer({ tree }: { tree: FlightRouterState }) {\n  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    const announcer = getAnnouncerNode()\n    setPortalNode(announcer)\n    return () => {\n      const container = document.getElementsByTagName(ANNOUNCER_TYPE)[0]\n      if (container?.isConnected) {\n        document.body.removeChild(container)\n      }\n    }\n  }, [])\n\n  const [routeAnnouncement, setRouteAnnouncement] = useState('')\n  const previousTitle = useRef<string | undefined>(undefined)\n\n  useEffect(() => {\n    let currentTitle = ''\n    if (document.title) {\n      currentTitle = document.title\n    } else {\n      const pageHeader = document.querySelector('h1')\n      if (pageHeader) {\n        currentTitle = pageHeader.innerText || pageHeader.textContent || ''\n      }\n    }\n\n    // Only announce the title change, but not for the first load because screen\n    // readers do that automatically.\n    if (\n      previousTitle.current !== undefined &&\n      previousTitle.current !== currentTitle\n    ) {\n      setRouteAnnouncement(currentTitle)\n    }\n    previousTitle.current = currentTitle\n  }, [tree])\n\n  return portalNode ? createPortal(routeAnnouncement, portalNode) : null\n}\n", "var id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexport { _class_private_field_loose_key as _ };\n", "import { addBasePath } from './add-base-path'\n\n/**\n * Function to correctly assign location to URL\n *\n * The method will add basePath, and will also correctly add location (including if it is a relative path)\n * @param location Location that should be added to the url\n * @param url Base URL to which the location should be assigned\n */\nexport function assignLocation(location: string, url: URL): URL {\n  if (location.startsWith('.')) {\n    const urlBase = url.origin + url.pathname\n    return new URL(\n      // In order for a relative path to be added to the current url correctly, the current url must end with a slash\n      // new URL('./relative', 'https://example.com/subdir').href -> 'https://example.com/relative'\n      // new URL('./relative', 'https://example.com/subdir/').href -> 'https://example.com/subdir/relative'\n      (urlBase.endsWith('/') ? urlBase : urlBase + '/') + location\n    )\n  }\n\n  return new URL(addBasePath(location), url.href)\n}\n", "let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n", "import {\n  ACTION_NAVIGATE,\n  ACTION_SERVER_PATCH,\n  ACTION_RESTORE,\n  ACTION_REFRESH,\n  ACTION_PREFETCH,\n  ACTION_HMR_REFRESH,\n  ACTION_SERVER_ACTION,\n} from './router-reducer-types'\nimport type {\n  ReducerActions,\n  ReducerState,\n  ReadonlyReducerState,\n} from './router-reducer-types'\nimport { navigateReducer } from './reducers/navigate-reducer'\nimport { serverPatchReducer } from './reducers/server-patch-reducer'\nimport { restoreReducer } from './reducers/restore-reducer'\nimport { refreshReducer } from './reducers/refresh-reducer'\nimport { prefetchReducer } from './reducers/prefetch-reducer'\nimport { hmrRefreshReducer } from './reducers/hmr-refresh-reducer'\nimport { serverActionReducer } from './reducers/server-action-reducer'\n\n/**\n * Reducer that handles the app-router state updates.\n */\nfunction clientReducer(\n  state: ReadonlyReducerState,\n  action: ReducerActions\n): ReducerState {\n  switch (action.type) {\n    case ACTION_NAVIGATE: {\n      return navigateReducer(state, action)\n    }\n    case ACTION_SERVER_PATCH: {\n      return serverPatchReducer(state, action)\n    }\n    case ACTION_RESTORE: {\n      return restoreReducer(state, action)\n    }\n    case ACTION_REFRESH: {\n      return refreshReducer(state, action)\n    }\n    case ACTION_HMR_REFRESH: {\n      return hmrRefreshReducer(state, action)\n    }\n    case ACTION_PREFETCH: {\n      return prefetchReducer(state, action)\n    }\n    case ACTION_SERVER_ACTION: {\n      return serverActionReducer(state, action)\n    }\n    // This case should never be hit as dispatch is strongly typed.\n    default:\n      throw new Error('Unknown action')\n  }\n}\n\nfunction serverReducer(\n  state: ReadonlyReducerState,\n  _action: ReducerActions\n): ReducerState {\n  return state\n}\n\n// we don't run the client reducer on the server, so we use a noop function for better tree shaking\nexport const reducer =\n  typeof window === 'undefined' ? serverReducer : clientReducer\n", "import type {\n  ActionFlightResponse,\n  ActionResult,\n} from '../../../../server/app-render/types'\nimport { callServer } from '../../../app-call-server'\nimport { findSourceMapURL } from '../../../app-find-source-map-url'\nimport {\n  ACTION_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n} from '../../app-router-headers'\n\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromFetch } from 'react-server-dom-webpack/client'\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { encodeReply } from 'react-server-dom-webpack/client'\nconst { createFromFetch, createTemporaryReferenceSet, encodeReply } = (\n  !!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')\n) as typeof import('react-server-dom-webpack/client')\n\nimport {\n  PrefetchKind,\n  type ReadonlyReducerState,\n  type ReducerState,\n  type ServerActionAction,\n  type ServerActionMutable,\n} from '../router-reducer-types'\nimport { assignLocation } from '../../../assign-location'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { handleMutable } from '../handle-mutable'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport {\n  normalizeFlightData,\n  type NormalizedFlightData,\n} from '../../../flight-data-helpers'\nimport { getRedirectError } from '../../redirect'\nimport { RedirectType } from '../../redirect-error'\nimport { createSeededPrefetchCacheEntry } from '../prefetch-cache-utils'\nimport { removeBasePath } from '../../../remove-base-path'\nimport { hasBasePath } from '../../../has-base-path'\nimport {\n  extractInfoFromServerReferenceId,\n  omitUnusedArgs,\n} from '../../../../shared/lib/server-reference-info'\nimport { revalidateEntireCache } from '../../segment-cache'\n\ntype FetchServerActionResult = {\n  redirectLocation: URL | undefined\n  redirectType: RedirectType | undefined\n  actionResult?: ActionResult\n  actionFlightData?: NormalizedFlightData[] | string\n  isPrerender: boolean\n  revalidatedParts: {\n    tag: boolean\n    cookie: boolean\n    paths: string[]\n  }\n}\n\nasync function fetchServerAction(\n  state: ReadonlyReducerState,\n  nextUrl: ReadonlyReducerState['nextUrl'],\n  { actionId, actionArgs }: ServerActionAction\n): Promise<FetchServerActionResult> {\n  const temporaryReferences = createTemporaryReferenceSet()\n  const info = extractInfoFromServerReferenceId(actionId)\n\n  // TODO: Currently, we're only omitting unused args for the experimental \"use\n  // cache\" functions. Once the server reference info byte feature is stable, we\n  // should apply this to server actions as well.\n  const usedArgs =\n    info.type === 'use-cache' ? omitUnusedArgs(actionArgs, info) : actionArgs\n\n  const body = await encodeReply(usedArgs, { temporaryReferences })\n\n  const res = await fetch('', {\n    method: 'POST',\n    headers: {\n      Accept: RSC_CONTENT_TYPE_HEADER,\n      [ACTION_HEADER]: actionId,\n      [NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(\n        JSON.stringify(state.tree)\n      ),\n      ...(process.env.NEXT_DEPLOYMENT_ID\n        ? {\n            'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID,\n          }\n        : {}),\n      ...(nextUrl\n        ? {\n            [NEXT_URL]: nextUrl,\n          }\n        : {}),\n    },\n    body,\n  })\n\n  const redirectHeader = res.headers.get('x-action-redirect')\n  const [location, _redirectType] = redirectHeader?.split(';') || []\n  let redirectType: RedirectType | undefined\n  switch (_redirectType) {\n    case 'push':\n      redirectType = RedirectType.push\n      break\n    case 'replace':\n      redirectType = RedirectType.replace\n      break\n    default:\n      redirectType = undefined\n  }\n\n  const isPrerender = !!res.headers.get(NEXT_IS_PRERENDER_HEADER)\n  let revalidatedParts: FetchServerActionResult['revalidatedParts']\n  try {\n    const revalidatedHeader = JSON.parse(\n      res.headers.get('x-action-revalidated') || '[[],0,0]'\n    )\n    revalidatedParts = {\n      paths: revalidatedHeader[0] || [],\n      tag: !!revalidatedHeader[1],\n      cookie: revalidatedHeader[2],\n    }\n  } catch (e) {\n    revalidatedParts = {\n      paths: [],\n      tag: false,\n      cookie: false,\n    }\n  }\n\n  const redirectLocation = location\n    ? assignLocation(\n        location,\n        new URL(state.canonicalUrl, window.location.href)\n      )\n    : undefined\n\n  const contentType = res.headers.get('content-type')\n\n  if (contentType?.startsWith(RSC_CONTENT_TYPE_HEADER)) {\n    const response: ActionFlightResponse = await createFromFetch(\n      Promise.resolve(res),\n      { callServer, findSourceMapURL, temporaryReferences }\n    )\n\n    if (location) {\n      // if it was a redirection, then result is just a regular RSC payload\n      return {\n        actionFlightData: normalizeFlightData(response.f),\n        redirectLocation,\n        redirectType,\n        revalidatedParts,\n        isPrerender,\n      }\n    }\n\n    return {\n      actionResult: response.a,\n      actionFlightData: normalizeFlightData(response.f),\n      redirectLocation,\n      redirectType,\n      revalidatedParts,\n      isPrerender,\n    }\n  }\n\n  // Handle invalid server action responses\n  if (res.status >= 400) {\n    // The server can respond with a text/plain error message, but we'll fallback to something generic\n    // if there isn't one.\n    const error =\n      contentType === 'text/plain'\n        ? await res.text()\n        : 'An unexpected response was received from the server.'\n\n    throw new Error(error)\n  }\n\n  return {\n    redirectLocation,\n    redirectType,\n    revalidatedParts,\n    isPrerender,\n  }\n}\n\n/*\n * This reducer is responsible for calling the server action and processing any side-effects from the server action.\n * It does not mutate the state by itself but rather delegates to other reducers to do the actual mutation.\n */\nexport function serverActionReducer(\n  state: ReadonlyReducerState,\n  action: ServerActionAction\n): ReducerState {\n  const { resolve, reject } = action\n  const mutable: ServerActionMutable = {}\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  // only pass along the `nextUrl` param (used for interception routes) if the current route was intercepted.\n  // If the route has been intercepted, the action should be as well.\n  // Otherwise the server action might be intercepted with the wrong action id\n  // (ie, one that corresponds with the intercepted route)\n  const nextUrl =\n    state.nextUrl && hasInterceptionRouteInCurrentTree(state.tree)\n      ? state.nextUrl\n      : null\n\n  const navigatedAt = Date.now()\n\n  return fetchServerAction(state, nextUrl, action).then(\n    async ({\n      actionResult,\n      actionFlightData: flightData,\n      redirectLocation,\n      redirectType,\n      isPrerender,\n      revalidatedParts,\n    }) => {\n      let redirectHref: string | undefined\n\n      // honor the redirect type instead of defaulting to push in case of server actions.\n      if (redirectLocation) {\n        if (redirectType === RedirectType.replace) {\n          state.pushRef.pendingPush = false\n          mutable.pendingPush = false\n        } else {\n          state.pushRef.pendingPush = true\n          mutable.pendingPush = true\n        }\n\n        redirectHref = createHrefFromUrl(redirectLocation, false)\n        mutable.canonicalUrl = redirectHref\n      }\n\n      if (!flightData) {\n        resolve(actionResult)\n\n        // If there is a redirect but no flight data we need to do a mpaNavigation.\n        if (redirectLocation) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectLocation.href,\n            state.pushRef.pendingPush\n          )\n        }\n        return state\n      }\n\n      if (typeof flightData === 'string') {\n        // Handle case when navigating to page in `pages` from `app`\n        resolve(actionResult)\n\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      const actionRevalidated =\n        revalidatedParts.paths.length > 0 ||\n        revalidatedParts.tag ||\n        revalidatedParts.cookie\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('SERVER ACTION APPLY FAILED')\n          resolve(actionResult)\n\n          return state\n        }\n\n        // Given the path can only have two items the items are only the router state and rsc for the root.\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          redirectHref ? redirectHref : state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          resolve(actionResult)\n\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          resolve(actionResult)\n\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectHref || state.canonicalUrl,\n            state.pushRef.pendingPush\n          )\n        }\n\n        // The server sent back RSC data for the server action, so we need to apply it to the cache.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const cache: CacheNode = createEmptyCacheNode()\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = cacheNodeSeedData[3]\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as server actions have to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n\n          mutable.cache = cache\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n          if (actionRevalidated) {\n            await refreshInactiveParallelSegments({\n              navigatedAt,\n              state,\n              updatedTree: newTree,\n              updatedCache: cache,\n              includeNextUrl: Boolean(nextUrl),\n              canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n            })\n          }\n        }\n\n        mutable.patchedTree = newTree\n        currentTree = newTree\n      }\n\n      if (redirectLocation && redirectHref) {\n        if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE && !actionRevalidated) {\n          // Because the RedirectBoundary will trigger a navigation, we need to seed the prefetch cache\n          // with the FlightData that we got from the server action for the target page, so that it's\n          // available when the page is navigated to and doesn't need to be re-fetched.\n          // We only do this if the server action didn't revalidate any data, as in that case the\n          // client cache will be cleared and the data will be re-fetched anyway.\n          // NOTE: We don't do this in the Segment Cache implementation.\n          // Dynamic data should never be placed into the cache, unless it's\n          // \"converted\" to static data using <Link prefetch={true}>. What we\n          // do instead is re-prefetch links and forms whenever the cache is\n          // invalidated.\n          createSeededPrefetchCacheEntry({\n            url: redirectLocation,\n            data: {\n              flightData,\n              canonicalUrl: undefined,\n              couldBeIntercepted: false,\n              prerendered: false,\n              postponed: false,\n              // TODO: We should be able to set this if the server action\n              // returned a fully static response.\n              staleTime: -1,\n            },\n            tree: state.tree,\n            prefetchCache: state.prefetchCache,\n            nextUrl: state.nextUrl,\n            kind: isPrerender ? PrefetchKind.FULL : PrefetchKind.AUTO,\n          })\n          mutable.prefetchCache = state.prefetchCache\n        }\n\n        // If the action triggered a redirect, the action promise will be rejected with\n        // a redirect so that it's handled by RedirectBoundary as we won't have a valid\n        // action result to resolve the promise with. This will effectively reset the state of\n        // the component that called the action as the error boundary will remount the tree.\n        // The status code doesn't matter here as the action handler will have already sent\n        // a response with the correct status code.\n        reject(\n          getRedirectError(\n            hasBasePath(redirectHref)\n              ? removeBasePath(redirectHref)\n              : redirectHref,\n            redirectType || RedirectType.push\n          )\n        )\n      } else {\n        resolve(actionResult)\n      }\n\n      return handleMutable(state, mutable)\n    },\n    (e: any) => {\n      // When the server action is rejected we don't update the state and instead call the reject handler of the promise.\n      reject(e)\n\n      return state\n    }\n  )\n}\n"], "names": ["applyFlightData", "navigatedAt", "existingCache", "cache", "flightData", "prefetchEntry", "tree", "treePatch", "seedData", "head", "isRootRender", "rsc", "loading", "prefetchRsc", "fillLazyItemsTillLeafWithHead", "parallelRoutes", "Map", "fillCacheWithNewSubTreeData", "restoreReducer", "state", "action", "extractPathFromFlightRouterState", "url", "href", "createHrefFromUrl", "treeToRestore", "<PERSON><PERSON><PERSON>", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "newCache", "prefetchCache", "nextUrl", "pathname", "shouldHardNavigate", "flightSegmentPath", "flightRouterState", "segment", "currentSegment", "parallelRouteKey", "matchSegment", "length", "getNextFlightSegmentPath", "Array", "isArray", "computeChangedPath", "getSelectedParams", "currentTree", "params", "parallelRoute", "Object", "values", "isDynamicParameter", "segmentValue", "startsWith", "PAGE_SEGMENT_KEY", "split", "removeLeadingSlash", "slice", "segmentToPathname", "normalizeSegments", "segments", "reduce", "acc", "isGroupSegment", "DEFAULT_SEGMENT_KEY", "INTERCEPTION_ROUTE_MARKERS", "some", "m", "undefined", "<PERSON><PERSON><PERSON>", "children", "push", "key", "value", "entries", "child<PERSON><PERSON>", "treeA", "treeB", "changedPath", "computeChangedPathImpl", "segmentA", "parallelRoutesA", "segmentB", "parallelRoutesB", "normalizedSegmentA", "normalizedSegmentB", "parallel<PERSON><PERSON>er<PERSON>ey", "PromiseQueue", "enqueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "Promise", "resolve", "reject", "task", "result", "error", "enqueueResult", "bump", "index", "findIndex", "item", "bumpedItem", "splice", "unshift", "constructor", "maxConcurrency", "forced", "shift", "createMutableActionQueue", "dispatchNavigateAction", "dispatchTraverseAction", "getCurrentAppRouterState", "publicAppRouterInstance", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "needsRefresh", "dispatch", "type", "ACTION_REFRESH", "origin", "window", "location", "prevState", "payload", "actionResult", "handleResult", "nextState", "discarded", "isThenable", "then", "err", "initialState", "<PERSON><PERSON><PERSON><PERSON>", "dispatchAction", "resolvers", "ACTION_RESTORE", "deferred<PERSON><PERSON><PERSON>", "startTransition", "newAction", "last", "ACTION_NAVIGATE", "ACTION_SERVER_ACTION", "reducer", "onRouterTransitionStart", "globalActionQueue", "getProfilingHookForOnNavigationStart", "navigateType", "shouldScroll", "linkInstanceRef", "URL", "addBasePath", "setLinkForCurrentNavigation", "dispatchAppRouterAction", "isExternalUrl", "isExternalURL", "locationSearch", "search", "allowAliasing", "back", "history", "forward", "prefetch", "process", "options", "getAppRouterActionQueue", "createPrefetchURL", "prefetchReducer", "ACTION_PREFETCH", "kind", "PrefetchKind", "FULL", "replace", "scroll", "refresh", "hmrRefresh", "handleMutable", "isNotUndefined", "mutable", "patchedTree", "apply", "scrollableSegments", "onlyHashChange", "hashFragment", "decodeURIComponent", "segmentPaths", "applyRouterStatePatchToTree", "path", "parallelRoutePatch", "refetch", "isRootLayout", "applyPatch", "addRefreshMarkerToActiveParallelSegments", "lastSegment", "initialTree", "patchTree", "initialSegment", "initialParallelRoutes", "patchSegment", "patchParallelRoutes", "newParallelRoutes", "IDLE_LINK_STATUS", "PENDING_LINK_STATUS", "mountFormInstance", "mountLinkInstance", "onLinkVisibilityChanged", "onNavigationIntent", "pingVisibleLinks", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "linkForMostRecentNavigation", "link", "setOptimisticLinkStatus", "prefetchable", "WeakMap", "prefetchableAndVisible", "Set", "observer", "IntersectionObserver", "handleIntersect", "entry", "isVisible", "intersectionRatio", "target", "rootMargin", "observeVisibility", "element", "instance", "set", "observe", "coercePrefetchableUrl", "reportErrorFn", "reportError", "console", "router", "prefetchEnabled", "prefetchURL", "wasHoveredOrTouched", "prefetchTask", "cacheVersion", "prefetchHref", "get", "delete", "cancelPrefetchTask", "unobserve", "add", "rescheduleLinkPrefetch", "unstable_upgradeToDynamicPrefetch", "existingPrefetchTask", "currentCacheVersion", "getCurrentCacheVersion", "treeAtTimeOfPrefetch", "cache<PERSON>ey", "createCacheKey", "priority", "PrefetchPriority", "Intent", "<PERSON><PERSON><PERSON>", "scheduleSegmentPrefetchTask", "handleSegmentMismatch", "handleExternalUrl", "addSearchParamsToPageSegments", "handleAliasedPrefetchEntry", "applied", "currentCache", "normalizedFlightData", "hasLoadingComponentInSeedData", "fromEntries", "searchParams", "pathToSegment", "flightSegmentPathWithLeadingEmpty", "newTree", "createEmptyCacheNode", "fillNewTreeWithOnlyLoadingSegments", "routerState", "cacheNodeSeedData", "isLastSegment", "keys", "newCacheNode", "parallelRouteState", "segmentForParallelRoute", "createRouterCache<PERSON>ey", "parallelSeedData", "lazyData", "includes", "prefetchHead", "existingParallelRoutes", "fillCacheWithNewSubTreeDataButOnlyLoading", "hash", "rest", "addSearchParamsIfPageSegment", "updatedParallelRoutes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillLazyItems", "segmentPath", "existingCacheNode", "i", "isLastEntry", "existingChildSegmentMap", "childSegmentMap", "existingChildCacheNode", "childCacheNode", "incomingSegment", "invalidateCacheByRouterState", "LinkComponent", "useLinkStatus", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "child", "linkStatus", "useOptimistic", "useRef", "hrefProp", "as", "asProp", "childrenProp", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "onNavigate", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "React", "useContext", "AppRouterContext", "appPrefetchKind", "AUTO", "useMemo", "resolvedHref", "only", "childRef", "observeLinkVisibilityOnMount", "useCallback", "current", "childProps", "useMergedRef", "e", "defaultPrevented", "linkClicked", "nodeName", "currentTarget", "isAnchorNodeName", "toUpperCase", "isModifiedEvent", "event", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "hasAttribute", "isLocalURL", "preventDefault", "isDefaultPrevented", "env", "NODE_ENV", "upgradeToDynamicPrefetch", "isAbsoluteUrl", "cloneElement", "LinkStatusContext", "Provider", "createContext", "abortTask", "listenForDynamicRequest", "startPPRNavigation", "updateCacheNodeOnPopstateRestoration", "oldCacheNode", "routerStateChildren", "oldParallelRoutes", "routerStateChild", "segmentChild", "segmentKeyChild", "oldSegmentMapChild", "oldCacheNodeChild", "newCacheNodeChild", "newSegmentMapChild", "shouldUsePrefetch", "isDeferredRsc", "status", "MPA_NAVIGATION_TASK", "route", "node", "dynamicRequestTree", "oldRouterState", "newRouterState", "prefetchData", "isPrefetchHeadPartial", "isSamePageNavigation", "scrollableSegmentsResult", "updateCacheNodeOnNavigation", "didFindRootLayout", "oldRouterStateChildren", "newRouterStateChildren", "prefetchDataChildren", "prefetchParallelRoutes", "patchedRouterStateChildren", "task<PERSON><PERSON><PERSON><PERSON>", "needsDynamicRequest", "dynamicRequestTreeChildren", "task<PERSON><PERSON><PERSON>", "newRouterStateChild", "oldRouterStateChild", "prefetchDataChild", "newSegmentChild", "newSegment<PERSON>ath<PERSON><PERSON><PERSON>", "concat", "newSegmentKeyChild", "oldSegment<PERSON>hild", "spawnReusedTask", "reusedRouterState", "beginRenderingNewRouteTree", "taskChildRoute", "dynamicRequestTreeChild", "patchRouterStateWithNewChildren", "possiblyPartialPrefetchHead", "isNavigatingToNewRootLayout", "createCacheNodeOnNavigation", "cacheNodeNavigatedAt", "isLeafSegment", "DYNAMIC_STALETIME_MS", "spawnPendingTask", "existingCacheNodeChildren", "cacheNodeChildren", "existingSegmentMapChild", "segmentPathChild", "baseRouterState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clone", "newTask", "createPendingCacheNode", "size", "maybePrefetchRsc", "maybePrefetchLoading", "createDeferredRsc", "responsePromise", "serverRouterState", "dynamicData", "dynamicHead", "writeDynamicDataIntoPendingTask", "rootTask", "taskSegment", "finishTaskUsingDynamicDataPayload", "taskNode", "finishPendingCacheNode", "cacheNode", "taskState", "serverState", "taskStateChildren", "serverStateChildren", "dataChildren", "taskStateChild", "serverStateChild", "dataChild", "segmentMapChild", "taskSegmentChild", "taskSegmentKeyChild", "cacheNodeChild", "abortPendingCacheNode", "dynamicSegmentData", "serverChildren", "dynamicDataChildren", "serverRouterStateChild", "dynamicDataChild", "DEFERRED", "Symbol", "tag", "pendingRsc", "res", "rej", "fulfilledRsc", "rejectedRsc", "reason", "prefetchQueue", "prefetchReducerImpl", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "identityReducerWhenSegmentCacheIsEnabled", "getOrCreatePrefetchCacheEntry", "invalidateCacheBelowFlightSegmentPath", "nextTree", "currentTreeSegment", "nextTreeSegment", "currentTreeChild", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "refA", "refB", "cleanupA", "cleanupB", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup", "clearCacheNodeDataForSegmentPath", "hmrRefreshReducer", "hmrRefreshReducerNoop", "_action", "hmrRefreshReducerImpl", "serverPatchReducer", "serverResponse", "canonicalUrlOverride", "canonicalUrlOverrideHref", "existingParallelRoutesCacheNode", "parallelRouteCacheNode", "refreshReducer", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "fetchServerResponse", "Date", "now", "log", "refreshInactiveParallelSegments", "updatedTree", "updatedCache", "navigateReducer", "toString", "document", "getElementById", "prefetchValues", "data", "postponed", "isFirstRead", "lastUsedTime", "aliased", "updatedCanonicalUrl", "isHeadPartial", "patchedRouterState", "dynamicRequest", "subSegment", "PrefetchCacheEntryStatus", "stale", "triggerLazyFetchForLeafSegments", "appliedPatch", "segmentPathsToFill", "generateSegmentsFromPatch", "map", "scrollableSegmentPath", "flightRouterPatch", "childSegment", "NavigationResultTag", "navigate", "reschedulePrefetchTask", "revalidateEntireCache", "schedulePrefetchTask", "notEnabled", "STATIC_STALETIME_MS", "createSeededPrefetchCacheEntry", "createPrefetchCacheKeyImpl", "includeSearchParams", "prefix", "pathnameFromUrl", "INTERCEPTION_CACHE_KEY_MARKER", "createPrefetchCacheKey", "existingCacheEntry", "maybeNextUrl", "TEMPORARY", "cacheKeyWithParams", "cacheKeyWithoutParams", "cacheKeyToUse", "existingEntry", "isAliased", "entryWithoutParams", "cacheEntry", "getPrefetchEntryCacheStatus", "isFullPrefetch", "prefetchResponse", "createLazyPrefetchEntry", "prefetchCache<PERSON>ey", "couldBeIntercepted", "prefetchTime", "staleTime", "fresh", "prefetchKind", "newCache<PERSON>ey", "prefixExistingPrefetchCacheEntry", "existingCacheKey", "prerendered", "prefetchCacheEntry", "expired", "Number", "reusable", "findHeadInCache", "findHeadInCacheImpl", "keyPrefix", "isLastItem", "childPara<PERSON>l<PERSON><PERSON><PERSON>", "hasReusablePrefetch", "seedNode", "extractInfoFromServerReferenceId", "id", "infoByte", "parseInt", "argMask", "usedArgs", "bit", "bitPosition", "typeBit", "hasRestArgs", "restArgs", "omit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args", "info", "filteredArgs", "refetch<PERSON><PERSON><PERSON>", "fetchedSegments", "refreshInactiveParallelSegmentsImpl", "rootTree", "refetch<PERSON>ath", "fetchPromises", "has", "fetchPromise", "flightDataPath", "parallelFetchPromise", "all", "AppRouter", "globalMutable", "isBot", "navigator", "userAgent", "_", "Error", "HistoryUpdater", "appRouterState", "useInsertionEffect", "historyState", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pushState", "replaceState", "useEffect", "copyNextJsInternalHistoryState", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "useDeferredValue", "Router", "assetPrefix", "globalError", "useActionQueue", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "handlePageShow", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "isRedirectError", "getURLFromRedirectError", "redirectType", "getRedirectTypeFromError", "RedirectType", "assign", "use", "unresolvedThenable", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "matchingHead", "pathParams", "layoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "RedirectBoundary", "AppRouterAnnouncer", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "errorStyles", "RuntimeStyles", "PathParamsContext", "PathnameContext", "SearchParamsContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "globalErrorComponentAndStyles", "globalErrorComponent", "globalErrorStyles", "useNavFailureHandler", "DefaultGlobalError", "runtimeStyles", "runtimeStyleChanged", "forceUpdate", "useState", "renderedStylesSize", "changed", "c", "rel", "precedence", "dplId", "globalThis", "_N_E_STYLE_LOAD", "len", "cb", "ANNOUNCER_TYPE", "portalNode", "setPortalNode", "getAnnouncerNode", "announcer", "existingAnnouncer", "getElementsByName", "shadowRoot", "childNodes", "container", "createElement", "style", "cssText", "ariaLive", "ANNOUNCER_ID", "role", "attachShadow", "mode", "append<PERSON><PERSON><PERSON>", "body", "getElementsByTagName", "isConnected", "<PERSON><PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "previousTitle", "currentTitle", "title", "pageHeader", "querySelector", "innerText", "textContent", "createPortal", "assignLocation", "urlBase", "endsWith", "errorOnce", "serverReducer", "clientReducer", "serverActionReducer", "createFromFetch", "createTemporaryReferenceSet", "encodeReply", "require", "fetchServerAction", "revalidatedParts", "actionId", "actionArgs", "temporaryReferences", "fetch", "method", "headers", "Accept", "RSC_CONTENT_TYPE_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "encodeURIComponent", "JSON", "stringify", "NEXT_URL", "redirectHeader", "_redirectType", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_IS_PRERENDER_HEADER", "revalidatedHeader", "parse", "paths", "cookie", "redirectLocation", "contentType", "response", "callServer", "findSourceMapURL", "actionFlightData", "normalizeFlightData", "f", "text", "redirectHref", "actionRevalidated", "Boolean", "getRedirectError"], "sourceRoot": ""}