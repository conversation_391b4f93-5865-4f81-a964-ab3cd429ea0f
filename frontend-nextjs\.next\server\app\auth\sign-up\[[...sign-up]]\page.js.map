{"version": 3, "file": "../app/auth/sign-up/[[...sign-up]]/page.js", "mappings": "ubAAA,6GCAA,iVCcA,OACA,UACA,GACA,CACA,UACA,OACA,CACA,UACA,UACA,CACA,UACA,iBACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,uCAA8I,CAuBlK,6GAES,EACF,CACP,CAGA,EAEA,CAAO,CACP,CAGA,EACA,CACO,CACP,CAEA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAzDA,IAAsB,uCAAiH,CAyDvI,gFACA,gBAzDA,IAAsB,uCAAuH,CAyD7I,sFACA,aAzDA,IAAsB,sCAAoH,CAyD1I,mFACA,WAzDA,IAAsB,4CAAgF,CAyDtG,+CACA,cAzDA,IAAsB,4CAAmF,CAyDzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,gHAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,yCACA,wCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BCpGD,qGCAA,mECAA,0GCAA,oREAA,8PEIA,IAAMA,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,8bAA+b,CACxdC,SAAU,CACRC,QAAS,CACPC,QAAS,mEACTC,YAAa,8JACbC,QAAS,wIACTC,UAAW,yEACXC,MAAO,uEACPC,KAAM,iDACR,EACAC,KAAM,CACJN,QAAS,gCACTO,GAAI,gDACJC,GAAI,uCACJC,KAAM,QACR,CACF,EACAC,gBAAiB,CACfX,QAAS,UACTO,KAAM,SACR,CACF,GACA,SAASK,EAAO,WACdC,CAAS,SACTb,CAAO,MACPO,CAAI,SACJO,GAAU,CAAK,CACf,GAAGC,EAGJ,EACC,IAAMC,EAAOF,EAAUG,EAAAA,EAAIA,CAAG,SAC9B,MAAO,UAACD,EAAAA,CAAKE,YAAU,SAASL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACtB,EAAe,SAC3DG,EACAO,iBACAM,CACF,IAAM,GAAGE,CAAK,CAAEK,sBAAoB,OAAOC,wBAAsB,SAASC,0BAAwB,cACpG,0BCzCA,oDCAA,kDCAA,+CCAA,yGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,iDCAA,uCAA6P,CAE7P,uCAAmQ,CAEnQ,uCAA8c,CAE9c,uCAAqa,CAEra,uCAAiT,CAEjT,uCAAif,CAEjf,2CAA4Q,yBCZ5Q,oDCAA,uECAA,oDCAA,kECAA,yDCAA,gDCAA,uCAA6P,CAE7P,uCAAmQ,CAEnQ,sCAA8c,CAE9c,uCAAqa,CAEra,uCAAiT,CAEjT,uCAAif,CAEjf,4CAA4Q,yBCZ5Q,6GCAA,qDCAA,4DCAA,4DGmBI,sBAAsB,uMFRX,SAASC,EAAe,OACrCC,CAAK,CAGN,EACC,MAAO,WAACC,MAAAA,CAAIZ,UAAU,sGAAsGQ,wBAAsB,iBAAiBC,0BAAwB,6BACvL,UAACI,IAAIA,CAACC,KAAK,KAAND,sBAAiCb,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACtB,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC,CACnEG,QAAS,OACX,GAAI,qDAAsDoB,sBAAoB,OAAOE,0BAAwB,4BAAmB,YAG9H,WAACG,MAAAA,CAAIZ,UAAU,2FACb,UAACY,MAAAA,CAAIZ,UAAU,iCACf,WAACY,MAAAA,CAAIZ,UAAU,gEACb,UAACe,MAAAA,CAAIC,MAAM,6BAA6BC,QAAQ,YAAYC,KAAK,OAAOC,OAAO,eAAeC,YAAY,IAAIC,cAAc,QAAQC,eAAe,QAAQtB,UAAU,eAAeO,sBAAoB,MAAME,0BAAwB,4BACpO,UAACc,OAAAA,CAAKC,EAAE,0EAA0EjB,sBAAoB,OAAOE,0BAAwB,uBACjI,UAGR,UAACG,MAAAA,CAAIZ,UAAU,iCACb,WAACyB,aAAAA,CAAWzB,UAAU,sBACpB,UAAC0B,IAAAA,CAAE1B,UAAU,mBAAU,uIAKvB,UAAC2B,SAAAA,CAAO3B,UAAU,mBAAU,wBAIlC,UAACY,MAAAA,CAAIZ,UAAU,8DACb,WAACY,MAAAA,CAAIZ,UAAU,gFAEb,WAACa,IAAIA,CAACb,UAADa,CAAYP,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2CAA4CsB,OAAO,SAASd,KAAM,4DAA6DP,sBAAoB,OAAOE,0BAAwB,6BACpM,WAACG,MAAAA,CAAIZ,UAAU,8BACb,UAAC6B,EAAAA,GAAcA,CAAAA,CAAC7B,UAAU,SAASO,sBAAoB,iBAAiBE,0BAAwB,qBAChG,UAACqB,OAAAA,CAAK9B,UAAU,uBAAc,mBAAsB,OAEtD,WAACY,MAAAA,CAAIZ,UAAU,yDACb,UAAC+B,EAAAA,CAAQA,CAAAA,CAAC/B,UAAU,+EAA+EkB,KAAK,eAAeX,sBAAoB,WAAWE,0BAAwB,qBAC9K,UAACqB,OAAAA,CAAK9B,UAAU,oCAA4BW,UAGhD,UAACqB,EAAAA,MAAeA,CAAAA,CAACC,cAAe,CAChCC,aAAc,kCAChB,EAAG3B,sBAAoB,kBAAkBE,0BAAwB,qBAC/D,WAACiB,IAAAA,CAAE1B,UAAU,2DAAiD,yCACrB,IACvC,UAACa,IAAIA,CAACC,KAAK,KAAND,IAAeb,UAAU,kDAAkDO,sBAAoB,OAAOE,0BAAwB,4BAAmB,qBAE9I,IAAI,MACR,IACJ,UAACI,IAAIA,CAACC,KAAK,KAAND,MAAiBb,UAAU,kDAAkDO,sBAAoB,OAAOE,0BAAwB,4BAAmB,mBAEjJ,cAMnB,oBCrEa0B,EAAqB,CAChCC,KAAO,KADyB,uBAEhCC,WAAa,mCACf,EACe,eAAeC,IAAAA,GAAAA,CACxB3B,EAAQ,GAARA,CAEJ,GAAI,CACF,IAAM4B,EAAW,MAAXA,KAAiBC,CAAM,qEAAuE,EAClGC,IAAM,EACJC,UAAY,MACd,CACF,GACIH,EAASI,EAAE,EAAE,CAEfhC,CAFE4B,CAEMK,CADK,EACbjC,CAAakC,GADMN,EAASO,IAAI,EAAbP,EACNM,gBAAgB,EAAIlC,CAAAA,CAAAA,CAAAA,MAE5BoC,EAAO,CAEhB,CACA,CAHgB,KAGTC,CAAAA,EAAAA,EAAAA,GAAAA,CAACtC,CAAAA,EAAAA,CAAeC,KAAOA,CAAAA,EAAOJ,GAAPI,kBAA2B,kBAAiBH,uBAAsB,QAAOC,yBAAwB,aACjI,CCfA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OASN,EATe,IASc,KAAK,CAPZwC,EAO8B,CAClD,CARiD,IAQ5C,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAIH,CALS,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,MAAQ,CAAC,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,8BAA8B,CAC9C,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAC,CAOxB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,wDCAA,iECAA,uDCAA,sDCAA,yDCAA,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/?3305", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/?7e12", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/?5250", "webpack://next-shadcn-dashboard-starter/./src/components/ui/button.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/?499b", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/?b5fe", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/./src/features/auth/components/sign-up-view.tsx", "webpack://next-shadcn-dashboard-starter/src/app/auth/sign-up/[[...sign-up]]/page.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\auth\\\\sign-up\\\\[[...sign-up]]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'sign-up',\n        {\n        children: [\n        '[[...sign-up]]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\auth\\\\sign-up\\\\[[...sign-up]]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\auth\\\\sign-up\\\\[[...sign-up]]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/auth/sign-up/[[...sign-up]]/page\",\n        pathname: \"/auth/sign-up/[[...sign-up]]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "\nexport { deleteKeylessAction as \"7f7b45347fd50452ee6e2850ded1018991a7b086f0\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { syncKeylessConfigAction as \"7f909588461cb83e855875f4939d6f26e4ae81b49e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { createOrReadKeylessAction as \"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\n", "module.exports = require(\"os\");", "\nexport { invalidateCacheAction as \"7f22efd92a3b59d43d3d12fe480e87910640e1db9e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\server-actions.js\"\n", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst buttonVariants = cva(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n  variants: {\n    variant: {\n      default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n      destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n      secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n      ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n      link: 'text-primary underline-offset-4 hover:underline'\n    },\n    size: {\n      default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n      sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n      lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n      icon: 'size-9'\n    }\n  },\n  defaultVariants: {\n    variant: 'default',\n    size: 'default'\n  }\n});\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> & VariantProps<typeof buttonVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'button';\n  return <Comp data-slot='button' className={cn(buttonVariants({\n    variant,\n    size,\n    className\n  }))} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Button\" data-sentry-source-file=\"button.tsx\" />;\n}\nexport { Button, buttonVariants };", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\client\\\\ClerkProvider.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\client\\\\keyless-cookie-sync.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"AuthenticateWithRedirectCallback\",\"ClerkLoaded\",\"ClerkLoading\",\"RedirectToCreateOrganization\",\"RedirectToOrganizationProfile\",\"RedirectToSignIn\",\"RedirectToSignUp\",\"RedirectToUserProfile\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\client-boundary\\\\controlComponents.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"useAuth\",\"useClerk\",\"useEmailLink\",\"useOrganization\",\"useOrganizationList\",\"useReverification\",\"useSession\",\"useSessionList\",\"useSignIn\",\"useSignUp\",\"useUser\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\client-boundary\\\\hooks.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"PromisifiedAuthProvider\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\client-boundary\\\\PromisifiedAuthProvider.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"CreateOrganization\",\"GoogleOneTap\",\"OrganizationList\",\"OrganizationProfile\",\"OrganizationSwitcher\",\"SignIn\",\"SignInButton\",\"SignInWithMetamaskButton\",\"SignOutButton\",\"SignUp\",\"SignUpButton\",\"UserButton\",\"UserProfile\",\"Waitlist\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\client-boundary\\\\uiComponents.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"__esModule\",\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\app-dir\\\\link.js\");\n", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\client\\\\ClerkProvider.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\client\\\\keyless-cookie-sync.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"AuthenticateWithRedirectCallback\",\"ClerkLoaded\",\"ClerkLoading\",\"RedirectToCreateOrganization\",\"RedirectToOrganizationProfile\",\"RedirectToSignIn\",\"RedirectToSignUp\",\"RedirectToUserProfile\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\client-boundary\\\\controlComponents.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"useAuth\",\"useClerk\",\"useEmailLink\",\"useOrganization\",\"useOrganizationList\",\"useReverification\",\"useSession\",\"useSessionList\",\"useSignIn\",\"useSignUp\",\"useUser\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\client-boundary\\\\hooks.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"PromisifiedAuthProvider\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\client-boundary\\\\PromisifiedAuthProvider.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"CreateOrganization\",\"GoogleOneTap\",\"OrganizationList\",\"OrganizationProfile\",\"OrganizationSwitcher\",\"SignIn\",\"SignInButton\",\"SignInWithMetamaskButton\",\"SignOutButton\",\"SignUp\",\"SignUpButton\",\"UserButton\",\"UserProfile\",\"Waitlist\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\client-boundary\\\\uiComponents.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"__esModule\",\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\app-dir\\\\link.js\");\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "import { buttonVariants } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\nimport { SignUp as ClerkSignUpForm } from '@clerk/nextjs';\nimport { GitHubLogoIcon } from '@radix-ui/react-icons';\nimport { IconStar } from '@tabler/icons-react';\nimport { Metadata } from 'next';\nimport Link from 'next/link';\nexport const metadata: Metadata = {\n  title: 'Authentication',\n  description: 'Authentication forms built using the components.'\n};\nexport default function SignUpViewPage({\n  stars\n}: {\n  stars: number;\n}) {\n  return <div className='relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0' data-sentry-component=\"SignUpViewPage\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n      <Link href='/examples/authentication' className={cn(buttonVariants({\n      variant: 'ghost'\n    }), 'absolute top-4 right-4 hidden md:top-8 md:right-8')} data-sentry-element=\"Link\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n        Sign Up\r\n      </Link>\r\n      <div className='bg-muted relative hidden h-full flex-col p-10 text-white lg:flex dark:border-r'>\r\n        <div className='absolute inset-0 bg-zinc-900' />\r\n        <div className='relative z-20 flex items-center text-lg font-medium'>\r\n          <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' className='mr-2 h-6 w-6' data-sentry-element=\"svg\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n            <path d='M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3' data-sentry-element=\"path\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n          </svg>\r\n          Logo\r\n        </div>\r\n        <div className='relative z-20 mt-auto'>\r\n          <blockquote className='space-y-2'>\r\n            <p className='text-lg'>\r\n              &ldquo;This starter template has saved me countless hours of work\r\n              and helped me deliver projects to my clients faster than ever\r\n              before.&rdquo;\r\n            </p>\r\n            <footer className='text-sm'>Random Dude</footer>\r\n          </blockquote>\r\n        </div>\r\n      </div>\r\n      <div className='flex h-full items-center justify-center p-4 lg:p-8'>\r\n        <div className='flex w-full max-w-md flex-col items-center justify-center space-y-6'>\r\n          {/* github link  */}\r\n          <Link className={cn('group inline-flex hover:text-yellow-200')} target='_blank' href={'https://github.com/kiranism/next-shadcn-dashboard-starter'} data-sentry-element=\"Link\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n            <div className='flex items-center'>\r\n              <GitHubLogoIcon className='size-4' data-sentry-element=\"GitHubLogoIcon\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n              <span className='ml-1 inline'>Star on GitHub</span>{' '}\r\n            </div>\r\n            <div className='ml-2 flex items-center gap-1 text-sm md:flex'>\r\n              <IconStar className='size-4 text-gray-500 transition-all duration-300 group-hover:text-yellow-300' fill='currentColor' data-sentry-element=\"IconStar\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n              <span className='font-display font-medium'>{stars}</span>\r\n            </div>\r\n          </Link>\r\n          <ClerkSignUpForm initialValues={{\n          emailAddress: '<EMAIL>'\n        }} data-sentry-element=\"ClerkSignUpForm\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n          <p className='text-muted-foreground px-8 text-center text-sm'>\r\n            By clicking continue, you agree to our{' '}\r\n            <Link href='/terms' className='hover:text-primary underline underline-offset-4' data-sentry-element=\"Link\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n              Terms of Service\r\n            </Link>{' '}\r\n            and{' '}\r\n            <Link href='/privacy' className='hover:text-primary underline underline-offset-4' data-sentry-element=\"Link\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n              Privacy Policy\r\n            </Link>\r\n            .\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>;\n}", "import { Metadata } from 'next';\nimport SignUpViewPage from '@/features/auth/components/sign-up-view';\nexport const metadata: Metadata = {\n  title: 'Authentication | Sign Up',\n  description: 'Sign Up page for authentication.'\n};\nexport default async function Page() {\n  let stars = 3000; // Default value\n\n  try {\n    const response = await fetch('https://api.github.com/repos/kiranism/next-shadcn-dashboard-starter', {\n      next: {\n        revalidate: 86400\n      }\n    });\n    if (response.ok) {\n      const data = await response.json();\n      stars = data.stargazers_count || stars; // Update stars if API response is valid\n    }\n  } catch (error) {\n    // Error fetching GitHub stars, using default value\n  }\n  return <SignUpViewPage stars={stars} data-sentry-element=\"SignUpViewPage\" data-sentry-component=\"Page\" data-sentry-source-file=\"page.tsx\" />;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/auth/sign-up/[[...sign-up]]',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/auth/sign-up/[[...sign-up]]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/auth/sign-up/[[...sign-up]]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/auth/sign-up/[[...sign-up]]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["buttonVariants", "cva", "variants", "variant", "default", "destructive", "outline", "secondary", "ghost", "link", "size", "sm", "lg", "icon", "defaultVariants", "<PERSON><PERSON>", "className", "<PERSON><PERSON><PERSON><PERSON>", "props", "Comp", "Slot", "data-slot", "cn", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "SignUpViewPage", "stars", "div", "Link", "href", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "blockquote", "p", "footer", "target", "GitHubLogoIcon", "span", "IconStar", "ClerkSignUpForm", "initialValues", "emailAddress", "metadata", "title", "description", "Page", "response", "fetch", "next", "revalidate", "ok", "data", "stargazers_count", "json", "error", "_jsx", "serverComponentModule.default"], "sourceRoot": ""}