{"version": 3, "file": "490.js", "mappings": "gdACO,IAAMA,EAAe,CAE1BC,IAAK,CACHC,UAAW,MACXC,aAAc,OACdC,SAAU,OACVC,WAAY,OACZC,MAAO,OACPC,QAAS,KACTC,QAAS,OACTC,MAAO,KACPC,SAAU,IACZ,EAGAR,UAAW,CACTS,MAAO,qBACPC,SAAU,eACVC,QAAS,CACPC,kBAAmB,OACnBC,eAAgB,OAChBC,cAAe,OACfC,iBAAkB,OAClBC,kBAAmB,OACnBC,8BAA+B,UAC/BC,oBAAqB,QACrBC,8BAA+B,YAC/BC,wBAAyB,SACzBC,wBAAyB,UACzBC,0BAA2B,SAC3BC,mBAAoB,SACpBC,OAAQ,KACRC,UAAW,OACXC,QAAS,OACTC,UAAW,IACb,EACAC,OAAQ,CACNC,iBAAkB,WAClBC,oBAAqB,WACvB,CACF,EAGA7B,aAAc,CACZQ,MAAO,OACPC,SAAU,YACVqB,eAAgB,OAChBC,gBAAiB,OACjBC,mBAAoB,OACpBC,kBAAmB,MACnBC,oBAAqB,WACrBC,eAAgB,OAChBC,QAAS,CACPC,IAAK,KACLC,MAAO,KACPC,SAAU,KACVC,UAAW,KACXC,OAAQ,KACRC,UAAW,MACb,EACAD,OAAQ,CACNE,UAAW,MACXC,UAAW,MACXC,WAAY,MACZC,UAAW,MACXC,UAAW,MACXC,OAAQ,KACV,EACAC,KAAM,CACJC,QAAS,KACTC,cAAe,OACfC,UAAW,OACXC,gBAAiB,SACjBC,KAAM,KACNC,KAAM,KACNC,MAAO,KACPC,iBAAkB,WAClBhB,OAAQ,IACV,CACF,EAGAxC,SAAU,CACRO,MAAO,OACPC,SAAU,YACViD,WAAY,OACZC,YAAa,OACbC,eAAgB,OAChBC,cAAe,MACfC,gBAAiB,WACjBC,WAAY,OACZC,kBAAmB,gBACnBf,KAAM,CACJgB,SAAU,KACVC,oBAAqB,UACrBC,MAAO,KACPC,iBAAkB,UAClBC,MAAO,KACPC,iBAAkB,cAClBC,aAAc,OACdC,wBAAyB,aAC3B,CACF,EAGAtE,WAAY,CACVM,MAAO,OACPC,SAAU,WACVgE,aAAc,OACdC,cAAe,OACfC,iBAAkB,OAClBC,gBAAiB,QACjBC,kBAAmB,aACnBC,aAAc,SACd7B,KAAM,CACJ8B,KAAM,OACNC,gBAAiB,UACjBC,YAAa,OACbC,uBAAwB,UACxBC,SAAU,OACVC,oBAAqB,cACrBC,MAAO,KACPC,iBAAkB,OACpB,CACF,EAGAnF,MAAO,CACLK,MAAO,OACPC,SAAU,cACV8E,eAAgB,OAChBC,eAAgB,OAChBC,eAAgB,OAChBC,MAAO,KACPC,MAAO,CACLxF,MAAO,MACPyF,OAAQ,KACRC,UAAW,IACb,CACF,EAGAC,OAAQ,CAENC,QAAS,CACPC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,OAAQ,KACRC,MAAO,KACPC,OAAQ,KACRC,MAAO,KACPC,QAAS,KACTC,KAAM,KACNC,KAAM,MACNC,SAAU,MACVC,IAAK,KACLC,OAAQ,KACRC,OAAQ,KACRC,OAAQ,IACV,EAGAxE,OAAQ,CACNyE,QAAS,SACTC,QAAS,KACTC,MAAO,KACPC,QAAS,KACTC,KAAM,KACNC,QAAS,MACThG,OAAQ,KACRiG,SAAU,MACVC,QAAS,MACTC,SAAU,KACZ,EAGAnE,KAAM,CACJjB,MAAO,KACPqF,UAAW,KACXC,SAAU,KACVrF,SAAU,KACVsF,SAAU,KACVC,SAAU,KACVtF,UAAW,KACXuF,UAAW,KACXC,UAAW,KACXC,SAAU,KACVC,SAAU,KACVC,SAAU,IACZ,EAGAC,cAAe,CACb5H,MAAO,OACP6H,YAAa,OACbC,cAAe,uBACfC,YAAa,OACbC,cAAe,qBACfC,UAAW,OACXC,YAAa,cACf,CACF,EAGAC,WAAY,CACVC,SAAU,UACVvE,MAAO,aACPF,MAAO,aACP0E,UAAW,iBACXC,UAAW,iBACXC,OAAQ,WACRC,SAAU,QACV1F,KAAM,WACNC,KAAM,UACR,EAGA5B,OAAQ,CACNsH,QAAS,gBACTC,QAAS,mBACTC,aAAc,aACdC,SAAU,WACVC,YAAa,cACbC,gBAAiB,WACjBC,WAAY,SACZC,WAAY,SACZC,aAAc,SACdC,aAAc,SACdC,aAAc,QAChB,EAGAxC,QAAS,CACPyC,MAAO,OACPC,QAAS,OACTC,QAAS,OACTC,QAAS,OACTC,KAAM,OACNC,SAAU,OACVC,WAAY,MACd,CACF,EAAW,SAGKC,EAAEC,CAAW,CAAEC,CAAwC,EACrE,IAAMC,EAAOF,EAAIG,KAAK,CAAC,KACnBC,EAAa3K,EAEjB,IAAK,IAAM4K,KAAKH,EACd,GADoB,CAChBE,GAA0B,UAAjB,OAAOA,KAAsBC,KAAKD,CAAAA,EAI7C,CAJoD,MAGpDE,QAAQC,IAAI,CAAC,CAAC,2BAA2B,EAAEP,EAAAA,CAAK,EACzCA,KAAK,EAHZI,EAAQA,CAAK,CAACC,EAAE,OAOpB,KAJoC,KAIhC,OAAOD,GACTE,QAAQC,IAAI,CAAC,CAAC,mCAAmC,EAAEP,EAAAA,CAAK,EACjDA,GAILC,EACKG,EAAMI,IADH,GACU,CAAC,aAAc,CAACC,EAAOC,IAClCT,CAAM,CAACS,EAAS,EAAEC,YAAcF,GAIpCL,CACT,mBCnQA,eAAeQ,EAAcC,CAAgB,CAAEC,CAAqB,EAClE,IAAMC,EAAM,CAAC,IAAI,EAAEF,EAAAA,CAAU,CAE7B,GAAI,CACF,IAAMG,EAAW,MAAMC,MAAMF,EAAK,CAChCG,QAAS,CACP,eAAgB,mBAChB,GAAGJ,GAASI,OAAO,EAErB,GAAGJ,CACL,GAEMK,EAAe,MAAMH,EAASI,IAAI,GAExC,GAAI,CAACJ,EAASK,EAAE,CAAE,CAEhB,GAAIF,EAAanE,KAAK,CACpB,CADsB,KAChB,MAAUmE,EAAanE,KAAK,CAEpC,OAAM,MAAU,CAAC,oBAAoB,EAAEgE,EAAS3I,MAAM,CAAC,CAAC,EAAE2I,EAASM,UAAU,EAAE,CACjF,CAEA,OAAOH,CACT,CAAE,MAAOnE,EAAO,CAEd,MADAsD,QAAQtD,KAAK,CAAC,CAAC,eAAe,EAAE+D,EAAI,QAAQ,CAAC,CAAE/D,GACzCA,CACR,CACF,8CAUO,IAAMuE,EAAkB,CAC7BC,OAAQ,MAAOvB,IACb,IAAMwB,EAAe,IAAIC,gBACrBzB,GAAQ0B,OAAOF,EAAaG,MAAM,CAAC,QAAS3B,EAAO0B,KAAK,CAAChB,QAAQ,IACjEV,GAAQ4B,MAAMJ,EAAaG,MAAM,CAAC,OAAQ3B,EAAO4B,IAAI,CAAClB,QAAQ,IAG9DV,GAAQ6B,OAAO,EAGNA,KAAK,CAAChJ,OAAO,EAAEiJ,QAAQ,EACnBH,MAAM,CAAC,yBAA0B3B,EAAO6B,KAAK,CAAChJ,OAAO,CAACiJ,MAAM,EAI7E,IAAMC,EAAQP,EAAad,QAAQ,GAAK,CAAC,CAAC,EAAEc,EAAad,QAAQ,IAAI,CAAG,GACxE,OAAOC,EAAyC,CAAC,aAAa,EAAEoB,EAAAA,CAAO,CACzE,EAEAC,QAAS,MAAOC,GACPtB,EAAwB,CAAC,cAAc,EAAEsB,EAAAA,CAAI,EAGtDrF,OAAQ,MAAOsF,GACNvB,EAAwB,gBAAiB,CAC9CwB,OAAQ,OACRC,KAAMC,KAAKC,SAAS,CAACJ,EACvB,GAGFvF,OAAQ,MAAOsF,EAAYC,IAClBvB,EAAwB,CAAC,cAAc,EAAEsB,EAAAA,CAAI,CAAE,CACpDE,OAAQ,QACRC,KAAMC,KAAKC,SAAS,CAACJ,EACvB,GAGFpG,OAAQ,MAAOmG,GACNtB,EAAiB,CAAC,cAAc,EAAEsB,EAAAA,CAAI,CAAE,CAC7CE,OAAQ,QACV,EAEJ,EAAE,EAGyB,CACzBZ,OAAQ,MAAOvB,IACb,IAAMwB,EAAe,IAAIC,gBACrBzB,GAAQ0B,OAAOF,EAAaG,MAAM,CAAC,QAAS3B,EAAO0B,KAAK,CAAChB,QAAQ,IACjEV,GAAQ4B,MAAMJ,EAAaG,MAAM,CAAC,OAAQ3B,EAAO4B,IAAI,CAAClB,QAAQ,IAC9DV,GAAQhE,QAAQwF,EAAaG,MAAM,CAAC,mCAAoC3B,EAAOhE,MAAM,EAEzF,IAAM+F,EAAQP,EAAad,QAAQ,GAAK,CAAC,CAAC,EAAEc,EAAad,QAAQ,IAAI,CAAG,GACxE,OAAOC,EAAqC,CAAC,SAAS,EAAEoB,EAAAA,CAAO,CACjE,EAEAC,QAAS,MAAOC,GACPtB,EAAoB,CAAC,UAAU,EAAEsB,EAAAA,CAAI,EAG9CrF,OAAQ,MAAOsF,GACNvB,EAAoB,YAAa,CACtCwB,OAAQ,OACRC,KAAMC,KAAKC,SAAS,CAACJ,EACvB,GAGFvF,OAAQ,MAAOsF,EAAYC,IAClBvB,EAAoB,CAAC,UAAU,EAAEsB,EAAAA,CAAI,CAAE,CAC5CE,OAAQ,QACRC,KAAMC,KAAKC,SAAS,CAACJ,EACvB,GAGFpG,OAAQ,MAAOmG,GACNtB,EAAiB,CAAC,UAAU,EAAEsB,EAAAA,CAAI,CAAE,CACzCE,OAAQ,QACV,EAEJ,EAGaI,EAAgB,CAC3BhB,OAAQ,MAAOvB,IACb,IAAMwB,EAAe,IAAIC,gBACrBzB,GAAQ0B,OAAOF,EAAaG,MAAM,CAAC,QAAS3B,EAAO0B,KAAK,CAAChB,QAAQ,IACjEV,GAAQ4B,MAAMJ,EAAaG,MAAM,CAAC,OAAQ3B,EAAO4B,IAAI,CAAClB,QAAQ,IAElE,IAAMqB,EAAQP,EAAad,QAAQ,GAAK,CAAC,CAAC,EAAEc,EAAad,QAAQ,IAAI,CAAG,GACxE,OAAOC,EAAuC,CAAC,WAAW,EAAEoB,EAAAA,CAAO,CACrE,EAEAC,QAAS,MAAOC,GACPtB,EAAsB,CAAC,YAAY,EAAEsB,EAAAA,CAAI,EAGlDrF,OAAQ,MAAOsF,GACNvB,EAAsB,cAAe,CAC1CwB,OAAQ,OACRC,KAAMC,KAAKC,SAAS,CAACJ,EACvB,GAGFvF,OAAQ,MAAOsF,EAAYC,IAClBvB,EAAsB,CAAC,YAAY,EAAEsB,EAAAA,CAAI,CAAE,CAChDE,OAAQ,QACRC,KAAMC,KAAKC,SAAS,CAACJ,EACvB,GAGFpG,OAAQ,MAAOmG,GACNtB,EAAiB,CAAC,YAAY,EAAEsB,EAAAA,CAAI,CAAE,CAC3CE,OAAQ,QACV,EAEJ,EAAE,EAGiC,UACjC,GAAI,CAEF,IAAMlK,EAAQ,IAAIuK,OAAOC,WAAW,GAAGvC,KAAK,CAAC,IAAI,CAAC,EAAE,CAM9CwC,EAAapM,CALO,MAAMgL,EAAgBC,MAAM,CAAC,CACrDG,MAAO,GACT,IAGqCiB,IAAI,CAAC1G,MAAM,CAAC2G,GAC/CA,EAAIC,eAAe,CAACC,UAAU,CAAC7K,IAC/B8K,MAAM,CAGFC,EAAU,IAAIR,KACpBQ,EAAQC,OAAO,CAACD,EAAQE,OAAO,GAAK,GACpC,IAAMC,EAAc,MAAMC,EAAY7B,MAAM,CAAC,CAAEG,MAAO,GAAK,GACrDnL,EAAiB4M,EAAYR,IAAI,CAAC1G,MAAM,CAACpD,GAC7C,IAAI2J,KAAK3J,EAAQwK,SAAS,GAAKL,GAC/BD,MAAM,CAGFO,EAAgB,MAAMf,EAAchB,MAAM,CAAC,CAAEG,MAAO,GAAK,GAE/D,MAAO,CACLpL,kBAAmBoM,EACnBnM,iBACAC,cAAe2M,EAAYI,SAAS,CACpC9M,iBAAkB6M,EAAcC,SAClC,CACF,CAAE,MAAOxG,EAAO,CAGd,OAFAsD,QAAQtD,KAAK,CAAC,qCAAsCA,GAE7C,CACLzG,kBAAmB,EACnBC,eAAgB,EAChBC,cAAe,EACfC,iBAAkB,CACpB,CACF,CACF,EAAE,yECjNa,SAAS+M,EAAc,UACpCC,CAAQ,YACRC,GAAa,CAAI,CAIlB,EACC,MAAO,+BACFA,EAAa,UAACC,EAAAA,UAAUA,CAAAA,CAACC,UAAU,iCAChC,UAACC,MAAAA,CAAID,UAAU,mCAA2BH,MAC5B,UAACI,MAAAA,CAAID,UAAU,mCAA2BH,KAElE,oCFbO,IAAMjO,EAAe,CAE1BC,IAAK,CACHC,UAAW,MACXC,aAAc,OACdC,SAAU,OACVC,WAAY,OACZC,MAAO,OACPC,QAAS,KACTC,QAAS,OACTC,MAAO,KACPC,SAAU,IACZ,EAGAR,UAAW,CACTS,MAAO,qBACPC,SAAU,eACVC,QAAS,CACPC,kBAAmB,OACnBC,eAAgB,OAChBC,cAAe,OACfC,iBAAkB,OAClBC,kBAAmB,OACnBC,8BAA+B,UAC/BC,oBAAqB,QACrBC,8BAA+B,YAC/BC,wBAAyB,SACzBC,wBAAyB,UACzBC,0BAA2B,SAC3BC,mBAAoB,SACpBC,OAAQ,KACRC,UAAW,OACXC,QAAS,OACTC,UAAW,IACb,EACAC,OAAQ,CACNC,iBAAkB,WAClBC,oBAAqB,WACvB,CACF,EAGA7B,aAAc,CACZQ,MAAO,OACPC,SAAU,YACVqB,eAAgB,OAChBC,gBAAiB,OACjBC,mBAAoB,OACpBC,kBAAmB,MACnBC,oBAAqB,WACrBC,eAAgB,OAChBC,QAAS,CACPC,IAAK,KACLC,MAAO,KACPC,SAAU,KACVC,UAAW,KACXC,OAAQ,KACRC,UAAW,MACb,EACAD,OAAQ,CACNE,UAAW,MACXC,UAAW,MACXC,WAAY,MACZC,UAAW,MACXC,UAAW,MACXC,OAAQ,KACV,EACAC,KAAM,CACJC,QAAS,KACTC,cAAe,OACfC,UAAW,OACXC,gBAAiB,SACjBC,KAAM,KACNC,KAAM,KACNC,MAAO,KACPC,iBAAkB,WAClBhB,OAAQ,IACV,CACF,EAGAxC,SAAU,CACRO,MAAO,OACPC,SAAU,YACViD,WAAY,OACZC,YAAa,OACbC,eAAgB,OAChBC,cAAe,MACfC,gBAAiB,WACjBC,WAAY,OACZC,kBAAmB,gBACnBf,KAAM,CACJgB,SAAU,KACVC,oBAAqB,UACrBC,MAAO,KACPC,iBAAkB,UAClBC,MAAO,KACPC,iBAAkB,cAClBC,aAAc,OACdC,wBAAyB,aAC3B,CACF,EAGAtE,WAAY,CACVM,MAAO,OACPC,SAAU,WACVgE,aAAc,OACdC,cAAe,OACfC,iBAAkB,OAClBC,gBAAiB,QACjBC,kBAAmB,aACnBC,aAAc,SACd7B,KAAM,CACJ8B,KAAM,OACNC,gBAAiB,UACjBC,YAAa,OACbC,uBAAwB,UACxBC,SAAU,OACVC,oBAAqB,cACrBC,MAAO,KACPC,iBAAkB,OACpB,CACF,EAGAnF,MAAO,CACLK,MAAO,OACPC,SAAU,cACV8E,eAAgB,OAChBC,eAAgB,OAChBC,eAAgB,OAChBC,MAAO,KACPC,MAAO,CACLxF,MAAO,MACPyF,OAAQ,KACRC,UAAW,IACb,CACF,EAGAC,OAAQ,CAENC,QAAS,CACPC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,OAAQ,KACRC,MAAO,KACPC,OAAQ,KACRC,MAAO,KACPC,QAAS,KACTC,KAAM,KACNC,KAAM,MACNC,SAAU,MACVC,IAAK,KACLC,OAAQ,KACRC,OAAQ,KACRC,OAAQ,IACV,EAGAxE,OAAQ,CACNyE,QAAS,SACTC,QAAS,KACTC,MAAO,KACPC,QAAS,KACTC,KAAM,KACNC,QAAS,MACThG,OAAQ,KACRiG,SAAU,MACVC,QAAS,MACTC,SAAU,KACZ,EAGAnE,KAAM,CACJjB,MAAO,KACPqF,UAAW,KACXC,SAAU,KACVrF,SAAU,KACVsF,SAAU,KACVC,SAAU,KACVtF,UAAW,KACXuF,UAAW,KACXC,UAAW,KACXC,SAAU,KACVC,SAAU,KACVC,SAAU,IACZ,EAGAC,cAAe,CACb5H,MAAO,OACP6H,YAAa,OACbC,cAAe,uBACfC,YAAa,OACbC,cAAe,qBACfC,UAAW,OACXC,YAAa,cACf,CACF,EAGAC,WAAY,CACVC,SAAU,UACVvE,MAAO,aACPF,MAAO,aACP0E,UAAW,iBACXC,UAAW,iBACXC,OAAQ,WACRC,SAAU,QACV1F,KAAM,WACNC,KAAM,UACR,EAGA5B,OAAQ,CACNsH,QAAS,gBACTC,QAAS,mBACTC,aAAc,aACdC,SAAU,WACVC,YAAa,cACbC,gBAAiB,WACjBC,WAAY,SACZC,WAAY,SACZC,aAAc,SACdC,aAAc,SACdC,aAAc,QAChB,EAGAxC,QAAS,CACPyC,MAAO,OACPC,QAAS,OACTC,QAAS,OACTC,QAAS,OACTC,KAAM,OACNC,SAAU,OACVC,WAAY,MACd,CACF,EAGO,SAASC,EAAEC,CAAW,CAAEC,CAAwC,EACrE,IAAMC,EAAOF,EAAIG,KAAK,CAAC,KACnBC,EAAa3K,EAEjB,IAAK,IAAM4K,KAAKH,EACd,GADoB,CAChBE,GAAS,iBAAOA,KAAsBC,KAAKD,CAAAA,EAI7C,CAJoD,MAGpDE,QAAQC,IAAI,CAAC,CAAC,2BAA2B,EAAEP,EAAAA,CAAK,EACzCA,KAAK,EAHZI,EAAQA,CAAK,CAACC,EAAE,OAOpB,KAJoC,KAIhC,OAAOD,GACTE,QAAQC,IAAI,CAAC,CAAC,mCAAmC,EAAEP,EAAAA,CAAK,EACjDA,GAILC,EACKG,EAAMI,IADH,GACU,CAAC,aAAc,CAACC,EAAOC,IAClCT,CAAM,CAACS,EAAS,EAAEC,YAAcF,GAIpCL,CACT", "sources": ["webpack://next-shadcn-dashboard-starter/./src/lib/translations.ts", "webpack://next-shadcn-dashboard-starter/./src/lib/api.ts", "webpack://next-shadcn-dashboard-starter/./src/components/layout/page-container.tsx"], "sourcesContent": ["// 中文翻译文件\nexport const translations = {\n  // 导航菜单\n  nav: {\n    dashboard: '仪表板',\n    appointments: '预约管理',\n    patients: '患者管理',\n    treatments: '治疗项目',\n    admin: '系统管理',\n    account: '账户',\n    profile: '个人资料',\n    login: '登录',\n    overview: '概览'\n  },\n\n  // 仪表板\n  dashboard: {\n    title: '诊所控制台 🏥',\n    subtitle: '欢迎使用您的诊所管理系统',\n    metrics: {\n      todayAppointments: '今日预约',\n      recentPatients: '近期患者',\n      totalPatients: '患者总数',\n      activetreatments: '可用治疗',\n      scheduledForToday: '今日安排',\n      appointmentsScheduledForToday: '今日安排的预约',\n      newPatientsThisWeek: '本周新患者',\n      patientsRegisteredInLast7Days: '过去7天注册的患者',\n      totalRegisteredPatients: '注册患者总数',\n      completePatientDatabase: '完整患者数据库',\n      treatmentOptionsAvailable: '可用治疗选项',\n      fullServiceCatalog: '完整服务目录',\n      active: '活跃',\n      last7Days: '过去7天',\n      allTime: '全部时间',\n      available: '可用'\n    },\n    errors: {\n      loadingDashboard: '加载仪表板时出错',\n      failedToLoadMetrics: '无法加载仪表板数据'\n    }\n  },\n\n  // 预约管理\n  appointments: {\n    title: '预约管理',\n    subtitle: '管理患者预约和排程',\n    newAppointment: '新建预约',\n    editAppointment: '编辑预约',\n    appointmentDetails: '预约详情',\n    appointmentsCount: '个预约',\n    loadingAppointments: '加载预约中...',\n    noAppointments: '暂无预约',\n    filters: {\n      all: '全部',\n      today: '今天',\n      thisWeek: '本周',\n      thisMonth: '本月',\n      status: '状态',\n      dateRange: '日期范围'\n    },\n    status: {\n      scheduled: '已安排',\n      confirmed: '已确认',\n      inProgress: '进行中',\n      completed: '已完成',\n      cancelled: '已取消',\n      noShow: '未到场'\n    },\n    form: {\n      patient: '患者',\n      selectPatient: '选择患者',\n      treatment: '治疗项目',\n      selectTreatment: '选择治疗项目',\n      date: '日期',\n      time: '时间',\n      notes: '备注',\n      notesPlaceholder: '预约备注（可选）',\n      status: '状态'\n    }\n  },\n\n  // 患者管理\n  patients: {\n    title: '患者管理',\n    subtitle: '管理患者信息和病历',\n    newPatient: '新建患者',\n    editPatient: '编辑患者',\n    patientDetails: '患者详情',\n    patientsCount: '位患者',\n    loadingPatients: '加载患者中...',\n    noPatients: '暂无患者',\n    searchPlaceholder: '按姓名、电话或邮箱搜索患者',\n    form: {\n      fullName: '姓名',\n      fullNamePlaceholder: '请输入患者姓名',\n      phone: '电话',\n      phonePlaceholder: '请输入电话号码',\n      email: '邮箱',\n      emailPlaceholder: '请输入邮箱地址（可选）',\n      medicalNotes: '病历备注',\n      medicalNotesPlaceholder: '请输入病历备注（可选）'\n    }\n  },\n\n  // 治疗项目\n  treatments: {\n    title: '治疗项目',\n    subtitle: '管理诊所治疗服务',\n    newTreatment: '新建治疗',\n    editTreatment: '编辑治疗',\n    treatmentDetails: '治疗详情',\n    treatmentsCount: '个治疗项目',\n    loadingTreatments: '加载治疗项目中...',\n    noTreatments: '暂无治疗项目',\n    form: {\n      name: '治疗名称',\n      namePlaceholder: '请输入治疗名称',\n      description: '治疗描述',\n      descriptionPlaceholder: '请输入治疗描述',\n      duration: '治疗时长',\n      durationPlaceholder: '请输入治疗时长（分钟）',\n      price: '价格',\n      pricePlaceholder: '请输入价格'\n    }\n  },\n\n  // 系统管理\n  admin: {\n    title: '系统管理',\n    subtitle: '管理用户权限和系统设置',\n    userManagement: '用户管理',\n    roleManagement: '角色管理',\n    systemSettings: '系统设置',\n    users: '用户',\n    roles: {\n      admin: '管理员',\n      doctor: '医生',\n      frontDesk: '前台'\n    }\n  },\n\n  // 通用文本\n  common: {\n    // 操作按钮\n    actions: {\n      save: '保存',\n      cancel: '取消',\n      edit: '编辑',\n      delete: '删除',\n      view: '查看',\n      search: '搜索',\n      filter: '筛选',\n      reset: '重置',\n      submit: '提交',\n      close: '关闭',\n      confirm: '确认',\n      back: '返回',\n      next: '下一步',\n      previous: '上一步',\n      add: '添加',\n      remove: '移除',\n      update: '更新',\n      create: '创建'\n    },\n\n    // 状态\n    status: {\n      loading: '加载中...',\n      success: '成功',\n      error: '错误',\n      warning: '警告',\n      info: '信息',\n      pending: '待处理',\n      active: '活跃',\n      inactive: '非活跃',\n      enabled: '已启用',\n      disabled: '已禁用'\n    },\n\n    // 时间相关\n    time: {\n      today: '今天',\n      yesterday: '昨天',\n      tomorrow: '明天',\n      thisWeek: '本周',\n      lastWeek: '上周',\n      nextWeek: '下周',\n      thisMonth: '本月',\n      lastMonth: '上月',\n      nextMonth: '下月',\n      thisYear: '今年',\n      lastYear: '去年',\n      nextYear: '明年'\n    },\n\n    // 确认对话框\n    confirmDialog: {\n      title: '确认操作',\n      deleteTitle: '确认删除',\n      deleteMessage: '您确定要删除这个项目吗？此操作无法撤销。',\n      cancelTitle: '确认取消',\n      cancelMessage: '您确定要取消吗？未保存的更改将丢失。',\n      saveTitle: '确认保存',\n      saveMessage: '您确定要保存这些更改吗？'\n    }\n  },\n\n  // 表单验证\n  validation: {\n    required: '此字段为必填项',\n    email: '请输入有效的邮箱地址',\n    phone: '请输入有效的电话号码',\n    minLength: '至少需要 {min} 个字符',\n    maxLength: '最多允许 {max} 个字符',\n    number: '请输入有效的数字',\n    positive: '请输入正数',\n    date: '请选择有效的日期',\n    time: '请选择有效的时间'\n  },\n\n  // 错误消息\n  errors: {\n    general: '发生了未知错误，请稍后重试',\n    network: '网络连接错误，请检查您的网络连接',\n    unauthorized: '您没有权限执行此操作',\n    notFound: '请求的资源未找到',\n    serverError: '服务器错误，请稍后重试',\n    validationError: '输入数据验证失败',\n    loadFailed: '加载数据失败',\n    saveFailed: '保存数据失败',\n    deleteFailed: '删除数据失败',\n    updateFailed: '更新数据失败',\n    createFailed: '创建数据失败'\n  },\n\n  // 成功消息\n  success: {\n    saved: '保存成功',\n    deleted: '删除成功',\n    updated: '更新成功',\n    created: '创建成功',\n    sent: '发送成功',\n    uploaded: '上传成功',\n    downloaded: '下载成功'\n  }\n} as const;\n\n// 翻译工具函数\nexport function t(key: string, params?: Record<string, string | number>): string {\n  const keys = key.split('.');\n  let value: any = translations;\n  \n  for (const k of keys) {\n    if (value && typeof value === 'object' && k in value) {\n      value = value[k];\n    } else {\n      console.warn(`Translation key not found: ${key}`);\n      return key; // 返回原始key作为fallback\n    }\n  }\n  \n  if (typeof value !== 'string') {\n    console.warn(`Translation value is not a string: ${key}`);\n    return key;\n  }\n  \n  // 处理参数替换\n  if (params) {\n    return value.replace(/\\{(\\w+)\\}/g, (match, paramKey) => {\n      return params[paramKey]?.toString() || match;\n    });\n  }\n  \n  return value;\n}\n\n// 类型定义\nexport type TranslationKey = keyof typeof translations;\n", "// Enhanced API utilities with best-practice authentication\nimport { Appointment, Patient, Treatment, PayloadResponse, DashboardMetrics, AppointmentCreateData, AppointmentUpdateData } from '@/types/clinic';\n\nexport interface ApiError {\n  error: string;\n  timestamp: string;\n  details?: any;\n}\n\nexport interface ApiResponse<T> {\n  data?: T;\n  error?: ApiError;\n  success: boolean;\n}\n\n// Enhanced API fetch function with proper error handling\nasync function apiRequest<T>(endpoint: string, options?: RequestInit): Promise<T> {\n  const url = `/api${endpoint}`;\n\n  try {\n    const response = await fetch(url, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    });\n\n    const responseData = await response.json();\n\n    if (!response.ok) {\n      // Handle structured error responses\n      if (responseData.error) {\n        throw new Error(responseData.error);\n      }\n      throw new Error(`API request failed: ${response.status} ${response.statusText}`);\n    }\n\n    return responseData;\n  } catch (error) {\n    console.error(`API request to ${url} failed:`, error);\n    throw error;\n  }\n}\n\n// User sync utility\nexport const authApi = {\n  syncUser: async (): Promise<any> => {\n    return apiRequest('/auth/sync', { method: 'POST' });\n  },\n};\n\n// Appointments API\nexport const appointmentsApi = {\n  getAll: async (params?: { limit?: number; page?: number; where?: any }): Promise<PayloadResponse<Appointment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.page) searchParams.append('page', params.page.toString());\n\n    // Handle where clause for filtering\n    if (params?.where) {\n      // Convert where object to query parameters\n      // This is a simplified implementation - you might need to expand this based on your needs\n      if (params.where.patient?.equals) {\n        searchParams.append('where[patient][equals]', params.where.patient.equals);\n      }\n    }\n\n    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';\n    return apiRequest<PayloadResponse<Appointment>>(`/appointments${query}`);\n  },\n\n  getById: async (id: string): Promise<Appointment> => {\n    return apiRequest<Appointment>(`/appointments/${id}`);\n  },\n\n  create: async (data: AppointmentCreateData): Promise<Appointment> => {\n    return apiRequest<Appointment>('/appointments', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  update: async (id: string, data: AppointmentUpdateData): Promise<Appointment> => {\n    return apiRequest<Appointment>(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  },\n\n  delete: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/appointments/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n\n// Patients API\nexport const patientsApi = {\n  getAll: async (params?: { limit?: number; page?: number; search?: string }): Promise<PayloadResponse<Patient>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.search) searchParams.append('where[or][0][fullName][contains]', params.search);\n    \n    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';\n    return apiRequest<PayloadResponse<Patient>>(`/patients${query}`);\n  },\n\n  getById: async (id: string): Promise<Patient> => {\n    return apiRequest<Patient>(`/patients/${id}`);\n  },\n\n  create: async (data: Partial<Patient>): Promise<Patient> => {\n    return apiRequest<Patient>('/patients', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  update: async (id: string, data: Partial<Patient>): Promise<Patient> => {\n    return apiRequest<Patient>(`/patients/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  },\n\n  delete: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/patients/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n\n// Treatments API\nexport const treatmentsApi = {\n  getAll: async (params?: { limit?: number; page?: number }): Promise<PayloadResponse<Treatment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.page) searchParams.append('page', params.page.toString());\n    \n    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';\n    return apiRequest<PayloadResponse<Treatment>>(`/treatments${query}`);\n  },\n\n  getById: async (id: string): Promise<Treatment> => {\n    return apiRequest<Treatment>(`/treatments/${id}`);\n  },\n\n  create: async (data: Partial<Treatment>): Promise<Treatment> => {\n    return apiRequest<Treatment>('/treatments', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  update: async (id: string, data: Partial<Treatment>): Promise<Treatment> => {\n    return apiRequest<Treatment>(`/treatments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  },\n\n  delete: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/treatments/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n\n// Dashboard metrics helper\nexport const getDashboardMetrics = async (): Promise<DashboardMetrics> => {\n  try {\n    // Get today's appointments\n    const today = new Date().toISOString().split('T')[0];\n    const todayAppointments = await appointmentsApi.getAll({\n      limit: 1000, // Get all to count\n    });\n    \n    // Filter for today's appointments\n    const todayCount = todayAppointments.docs.filter(apt => \n      apt.appointmentDate.startsWith(today)\n    ).length;\n\n    // Get recent patients (last 7 days)\n    const weekAgo = new Date();\n    weekAgo.setDate(weekAgo.getDate() - 7);\n    const allPatients = await patientsApi.getAll({ limit: 1000 });\n    const recentPatients = allPatients.docs.filter(patient => \n      new Date(patient.createdAt) >= weekAgo\n    ).length;\n\n    // Get all treatments\n    const allTreatments = await treatmentsApi.getAll({ limit: 1000 });\n\n    return {\n      todayAppointments: todayCount,\n      recentPatients,\n      totalPatients: allPatients.totalDocs,\n      activetreatments: allTreatments.totalDocs,\n    };\n  } catch (error) {\n    console.error('Failed to fetch dashboard metrics:', error);\n    // Return default values on error\n    return {\n      todayAppointments: 0,\n      recentPatients: 0,\n      totalPatients: 0,\n      activetreatments: 0,\n    };\n  }\n};\n", "import React from 'react';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nexport default function PageContainer({\n  children,\n  scrollable = true\n}: {\n  children: React.ReactNode;\n  scrollable?: boolean;\n}) {\n  return <>\r\n      {scrollable ? <ScrollArea className='h-[calc(100dvh-52px)]'>\r\n          <div className='flex flex-1 p-4 md:px-6'>{children}</div>\r\n        </ScrollArea> : <div className='flex flex-1 p-4 md:px-6'>{children}</div>}\r\n    </>;\n}"], "names": ["translations", "nav", "dashboard", "appointments", "patients", "treatments", "admin", "account", "profile", "login", "overview", "title", "subtitle", "metrics", "todayAppointments", "recentPatients", "totalPatients", "activetreatments", "scheduledForToday", "appointmentsScheduledForToday", "newPatientsThisWeek", "patientsRegisteredInLast7Days", "totalRegisteredPatients", "completePatientDatabase", "treatmentOptionsAvailable", "fullServiceCatalog", "active", "last7Days", "allTime", "available", "errors", "loadingDashboard", "failedToLoadMetrics", "newAppointment", "editAppointment", "appointmentDetails", "appointmentsCount", "loadingAppointments", "noAppointments", "filters", "all", "today", "thisWeek", "thisMonth", "status", "date<PERSON><PERSON><PERSON>", "scheduled", "confirmed", "inProgress", "completed", "cancelled", "noShow", "form", "patient", "selectPatient", "treatment", "selectTreatment", "date", "time", "notes", "notesPlaceholder", "newPatient", "editPatient", "patientDetails", "patientsCount", "loadingPatients", "noPatients", "searchPlaceholder", "fullName", "fullNamePlaceholder", "phone", "phonePlaceholder", "email", "emailPlaceholder", "medicalNotes", "medicalNotesPlaceholder", "newTreatment", "editTreatment", "treatmentDetails", "treatmentsCount", "loadingTreatments", "noTreatments", "name", "namePlaceholder", "description", "descriptionPlaceholder", "duration", "durationPlaceholder", "price", "pricePlaceholder", "userManagement", "roleManagement", "systemSettings", "users", "roles", "doctor", "frontDesk", "common", "actions", "save", "cancel", "edit", "delete", "view", "search", "filter", "reset", "submit", "close", "confirm", "back", "next", "previous", "add", "remove", "update", "create", "loading", "success", "error", "warning", "info", "pending", "inactive", "enabled", "disabled", "yesterday", "tomorrow", "lastWeek", "nextWeek", "lastM<PERSON>h", "nextMonth", "thisYear", "lastYear", "nextYear", "confirmDialog", "deleteTitle", "deleteMessage", "cancelTitle", "cancelMessage", "saveTitle", "saveMessage", "validation", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "number", "positive", "general", "network", "unauthorized", "notFound", "serverError", "validationError", "loadFailed", "saveFailed", "deleteFailed", "updateFailed", "createFailed", "saved", "deleted", "updated", "created", "sent", "uploaded", "downloaded", "t", "key", "params", "keys", "split", "value", "k", "console", "warn", "replace", "match", "<PERSON><PERSON><PERSON><PERSON>", "toString", "apiRequest", "endpoint", "options", "url", "response", "fetch", "headers", "responseData", "json", "ok", "statusText", "appointmentsApi", "getAll", "searchParams", "URLSearchParams", "limit", "append", "page", "where", "equals", "query", "getById", "id", "data", "method", "body", "JSON", "stringify", "treatmentsApi", "Date", "toISOString", "todayCount", "docs", "apt", "appointmentDate", "startsWith", "length", "weekAgo", "setDate", "getDate", "allPatients", "patientsApi", "createdAt", "allTreatments", "totalDocs", "<PERSON><PERSON><PERSON><PERSON>", "children", "scrollable", "ScrollArea", "className", "div"], "sourceRoot": ""}