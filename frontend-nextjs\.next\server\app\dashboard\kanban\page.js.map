{"version": 3, "file": "../app/dashboard/kanban/page.js", "mappings": "gbAAA,uCAAiK,CAEjK,sCAAiL,CAEjL,uCAAgL,wBCJhL,0kBCAA,wICIA,IAAMA,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRC,QAAS,CACPC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfL,QAAS,SACX,CACF,GACA,SAASM,EAAM,WACbC,CAAS,SACTP,CAAO,SACPQ,EAAU,EAAK,CACf,GAAGC,EAGJ,EACC,IAAMC,EAAOF,EAAUG,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAKE,YAAU,QAAQL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAChB,EAAc,SACzDG,CACF,GAAIO,GAAa,GAAGE,CAAK,CAAEK,sBAAoB,OAAOC,wBAAsB,QAAQC,0BAAwB,aAC9G,0BC7BA,8FCAA,uCAAiK,CAEjK,uCAAiL,CAEjL,uCAAgL,yBCJhL,mECAA,0GCAA,qDCAA,0DGmBI,sBAAsB,wKFfX,SAASC,IACtB,MAAO,UAACC,EAAAA,CAAaA,CAAAA,CAACJ,sBAAoB,gBAAgBC,wBAAsB,iBAAiBC,0BAAwB,gCACrH,WAACG,MAAAA,CAAIZ,UAAU,sBACb,WAACY,MAAAA,CAAIZ,UAAU,6CACb,UAACa,EAAAA,CAAOA,CAAAA,CAACC,MAAO,CAAC,MAAM,CAAC,CAAEC,YAAY,sBAAsBR,sBAAoB,UAAUE,0BAAwB,yBAClH,UAACO,EAAAA,OAAaA,CAAAA,CAACT,sBAAoB,gBAAgBE,0BAAwB,4BAE7E,UAACQ,EAAAA,WAAWA,CAAAA,CAACV,sBAAoB,cAAcE,0BAAwB,6BAG/E,oBCbaS,EAAW,CACtBJ,KADsB,CACf,yBACT,ECKM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OASN,EAAyB,IAAI,KAAK,CDpBrB,SAASK,CCoBA,CDnBtB,EADsBA,ECoB4B,CDpB5BA,CACfC,CAAAA,EAAAA,EAAAA,GAAAA,CAACV,CAAAA,EAAAA,CAAeH,WAAfG,UAAmC,kBAAiBF,uBAAsB,QAAOC,yBAAwB,aACnH,ECkBsD,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,IAAI,CACrC,IAD0C,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAAC,GAAG,CAAC,EAAd,OAAuB,CAAC,EAAI,OAC7D,EADsE,GACzC,EAAtB,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,mBAAmB,CACnC,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,mDCAA,mDCAA,gDCAA,wGCAA,qLCEA,SAASY,EAAK,WACZrB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,OAAOL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASa,EAAW,WAClBtB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASc,EAAU,WACjBvB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,aAAaL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASe,EAAgB,CACvBxB,WAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,mBAAmBL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,kBAAkBC,0BAAwB,YACjL,CACA,SAASgB,EAAW,WAClBzB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iEAAkEN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,aAAaC,0BAAwB,YACxM,CACA,SAASiB,EAAY,CACnB1B,WAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,eAAeL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASkB,EAAW,WAClB3B,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,kDCAA,iECAA,6FCIO,IAAMI,EAAkC,CAAC,OAC9CC,CAAK,aACLC,CAAW,CACZ,GACQ,WAACH,MAAAA,CAAIJ,wBAAsB,UAAUC,0BAAwB,wBAChE,UAACmB,KAAAA,CAAG5B,UAAU,6CAAqCc,IACnD,UAACe,IAAAA,CAAE7B,UAAU,yCAAiCe,+BCVpD,uDCAA,qDCAA,+FCAA,UAEA,IADA,EACA,UACA,UACA,kCACA,oBACA,QACA,6DAA8H,MAC9H,oBACA,CACA,EACA,QAMA,YAAgB,6BALhB,MAKgB,UAJhB,IACA,SACA,gBAEgB,EAChB,aACA,QACA,EACA,cClBA,OAUA,MACA,MAAc,EAAW,GACzB,MAXA,gBACA,MAAgB,sBAA0B,CAC1C,YACA,oBACA,4BAGA,OADE,eAAmB,IACrB,EACA,EAGA,KAEA,OADA,mBACA,CACA,iBCjBA,MAAe,CAAE,UAAU,cAAE,CCA7B,CDA8B,CCA9B,oBACA,WCDA,KACA,YAAgB,MAAS,IACzB,sCCuBA,MAvBA,QAuBe,CAvBf,OACA,GAAQ,EAAM,mBACd,OAAe,EAAM,aAGrB,MADA,UACA,oBFJA,CEIsD,CFJtD,EEIyD,MFJzD,MACQ,oBAAc,IACtB,KAEA,kBECA,eACA,iDAIA,GAFA,gBACA,iBACA,GAEA,GADA,SACA,iBACA,oCAAoD,EAAO,GAAG,MAAa,0BAE3E,YAAwB,KAAQ,IAChC,YAEA,QACA,CACA,ODnBO,eCmBmB,CDlB1B,iBACA,UACA,UACA,UACA,IACA,UACA,UACA,IACA,UACA,UACA,IACA,UACA,UACA,IACA,WACA,WACA,WACA,WACA,WACA,yBACA,ECF0B,EAC1B,ECuPA,SACA,IACA,WACA,wBACA,SAEA,OACA,QACA,QAEA,SACA,YAEA,CACA,CAAI,SACJ,OACA,QACA,YACO,CACP,SACA,OAEA,CACA,CACA,EChSMe,EAAc,CAClB,CACEC,GAAI,OACJjB,MAAO,MACT,EACD,CAiBKkB,EAAuB,CAC3B,CACED,GAAI,QACJE,OAAQ,OACRnB,MAAO,iCACT,EACA,CACEiB,GAAI,QACJE,OAAQ,OACRnB,MAAO,uCACT,EACD,CAaYoB,ENnCb,gBKsRA,OClPSC,CDkPT,UACA,IA+CA,EA/CA,GACA,iBAzDA,KACA,MACA,IACA,KACA,CAAI,SACJ,MACA,CAsBA,MArBA,CACA,YACA,MACA,SACA,SACA,KAEA,qBAEA,uCACA,qBACA,UAEA,IACA,CAAK,CACL,yBACA,EACA,0BAEA,6BACA,CAEA,EA4BA,kBACA,gBACA,UACA,eACA,KACA,KACA,EACA,MAEA,KACA,UACA,UACA,YACA,MACA,SACA,SACA,aACA,uDAAiE,OAAa,iDAE9E,OACA,CAAO,CACP,EACA,GAGA,WACA,oBAAuC,OAAU,EACjD,yBACA,QACA,kBACK,CACL,EACA,YACA,oBACA,OACA,GACA,EACA,QACA,SACA,QACA,GACA,CAAK,CACL,EACA,EAEA,yBAEA,WACA,QACA,aACA,KACA,cACA,MACA,2BACA,CAAK,EACL,kFACA,6CACA,KACA,qDAeA,uBAfA,CACA,cACA,gBACA,QACA,kBAEA,qBACA,kBAEA,OAEA,cACA,wFAEA,CAIA,CAJU,KAIV,WACA,CAAK,WACL,MACA,WAMA,GADA,EAJA,UACA,EACA,mBAEA,IACA,EACA,UAEA,CAAK,YACL,qBACA,MACA,KACA,kBACA,CAAK,YACL,oBACA,CAAK,CACL,EAiCA,OAhCA,WACA,eACA,GACA,KACA,MAEA,WACA,aAEA,CAAK,CACL,kBACA,6BACA,CAAK,CACL,iBACA,kBACA,kBACA,cACA,SACA,KACA,WACA,GAEA,sBACA,SACA,KACA,WACA,EAEA,EACA,iBACA,IAEA,IACA,GCrXI,GAAU,EACRC,CADQ,KACDJ,EACPK,QAASP,EACTQ,YAAa,KACbC,QAAS,CAACzB,EAAeC,IACvByB,EAAI,GAAY,EACdJ,GADc,GACP,IACFK,EAAML,KAAK,CACd,CAAEL,GAAIW,MAAIA,IAAI5B,EAAOC,cAAakB,OAAQ,MAAO,EAClD,CACH,GACFU,UAAW,CAACZ,EAAsBa,IAChCJ,EAAI,GAAY,EACdH,GADc,KACLI,EAAMJ,OAAO,CAACQ,GAAG,CAAC,GACzBC,EAAIf,EAAE,GAAKA,EAAK,CAAE,GAAGe,CAAG,CAAEhC,MAAO8B,CAAQ,EAAIE,GAEjD,GACFC,OAASjC,GACP0B,EAAI,GAAY,EACdH,GADc,KACL,IACJI,EAAMJ,OAAO,CAChB,OAAEvB,EAAOiB,GAAIU,EAAMJ,OAAO,CAACW,MAAM,CAAGlC,EAAMmC,WAAW,GAAK,MAAO,EAClE,CACH,GACFC,SAAU,GAAuBV,EAAI,CAAEF,YAAaP,CAAG,GACvDoB,WAAY,GACVX,EAAI,GAAY,EACdJ,GADc,GACPK,EAAML,KAAK,CAACgB,MAAM,CAAEC,GAASA,EAAKtB,EAAE,GAAKA,GAClD,GACFuB,UAAW,GACTd,EAAI,GAAY,EACdH,GADc,KACLI,EAAMJ,OAAO,CAACe,MAAM,CAAC,GAASN,EAAIf,EAAE,GAAKA,GACpD,GACFwB,SAAU,GAAsBf,EAAI,CAAEJ,MAAOoB,CAAS,GACtDC,QAAS,GAAuBjB,EAAI,CAAEH,QAASqB,CAAQ,GACzD,EACA,CAAEC,KAAM,aAAcC,eAAe,CAAK,IAE5C,wBC9FF,kVCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,SACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAkI,CAoBtJ,iGAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAA4H,CAgClJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAjDA,IAAsB,uCAAiH,CAiDvI,gFACA,gBAjDA,IAAsB,uCAAuH,CAiD7I,sFACA,aAjDA,IAAsB,sCAAoH,CAiD1I,mFACA,WAjDA,IAAsB,4CAAgF,CAiDtG,+CACA,cAjDA,IAAsB,4CAAmF,CAiDzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,oGAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,8BACA,6BAEA,cACA,YACA,WACA,CAAK,CACL,UACA,YACA,CACA,CAAC,0BC5FD,yKCOe,SAAS5C,IACtB,IAAMuB,EAAUL,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACO,GAASA,EAAMF,OAAO,EAYnD,MAAO,WAACsB,EAAAA,EAAMA,CAAAA,CAACtD,sBAAoB,SAASC,wBAAsB,gBAAgBC,0BAAwB,gCACtG,UAACqD,EAAAA,EAAaA,CAAAA,CAAC7D,OAAO,IAACM,sBAAoB,gBAAgBE,0BAAwB,+BACjF,UAACsD,EAAAA,CAAMA,CAAAA,CAACtE,QAAQ,YAAYuE,KAAK,KAAKzD,sBAAoB,SAASE,0BAAwB,+BAAsB,qBAInH,WAACwD,EAAAA,EAAaA,CAAAA,CAACjE,UAAU,mBAAmBO,sBAAoB,gBAAgBE,0BAAwB,gCACtG,WAACyD,EAAAA,EAAYA,CAAAA,CAAC3D,sBAAoB,eAAeE,0BAAwB,gCACvE,UAAC0D,EAAAA,EAAWA,CAAAA,CAAC5D,sBAAoB,cAAcE,0BAAwB,+BAAsB,iBAC7F,UAAC2D,EAAAA,EAAiBA,CAAAA,CAAC7D,sBAAoB,oBAAoBE,0BAAwB,+BAAsB,2CAI3G,WAAC4D,OAAAA,CAAKtC,GAAG,YAAY/B,UAAU,kBAAkBsE,SAxBjCC,CAwB2CC,GAvB/DD,EAAEE,cAAc,GAGhB,GAAM,OACJ3D,CAAK,CACLC,aAAW,CACZ,CAAG2D,OAAOC,WAAW,CAJL,IAAIC,SADRL,EAAEM,aAAa,GAMP,UAAjB,OAAO/D,GAA6C,UAAvB,OAAOC,GACxCwB,EAAQzB,EAAOC,EACjB,YAeQ,UAACH,MAAAA,CAAIZ,UAAU,+CACb,UAAC8E,EAAAA,CAAKA,CAAAA,CAAC/C,GAAG,QAAQ4B,KAAK,QAAQoB,YAAY,gBAAgB/E,UAAU,aAAaO,sBAAoB,QAAQE,0BAAwB,0BAExI,UAACG,MAAAA,CAAIZ,UAAU,+CACb,UAACgF,EAAAA,CAAQA,CAAAA,CAACjD,GAAG,cAAc4B,KAAK,cAAcoB,YAAY,iBAAiB/E,UAAU,aAAaO,sBAAoB,WAAWE,0BAAwB,6BAG7J,UAACwE,EAAAA,EAAYA,CAAAA,CAAC1E,sBAAoB,eAAeE,0BAAwB,+BACvE,UAACqD,EAAAA,EAAaA,CAAAA,CAAC7D,OAAO,IAACM,sBAAoB,gBAAgBE,0BAAwB,+BACjF,UAACsD,EAAAA,CAAMA,CAAAA,CAACmB,KAAK,SAASlB,KAAK,KAAKK,KAAK,YAAY9D,sBAAoB,SAASE,0BAAwB,+BAAsB,sBAOxI,0BClDA,oDCAA,kECAA,wDCAA,wDCAA,4sBCAA,qDCAA,4DCAA,iDCAA,yDCAA,iECAA,uDCAA,sDCAA,yDCAA,iDCAA,2DCAA,yHCEA,SAASuE,EAAS,CAChBhF,WAAS,CACT,GAAGE,EAC8B,EACjC,MAAO,UAACiF,WAAAA,CAAS9E,YAAU,WAAWL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,scAAucN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,WAAWC,0BAAwB,gBAC7kB,wFCLe,SAASE,EAAc,CACpCyE,UAAQ,YACRC,EAAa,EAAI,CAIlB,EACC,MAAO,+BACFA,EAAa,UAACC,EAAAA,UAAUA,CAAAA,CAACtF,UAAU,iCAChC,UAACY,MAAAA,CAAIZ,UAAU,mCAA2BoF,MAC5B,UAACxE,MAAAA,CAAIZ,UAAU,mCAA2BoF,KAElE,0BCdA,iDCAA,wDCAA,65BI6lHA,kCA/6GA,EA+lBA,EA4RA,EAmBI,EAgkBJ,EA0FA,EAOA,EA4LA,EAQA,EA63BA,SAluDgB,8CHtjCT,SAASG,EACdC,CAA2B,EAI3B,GAAI,CAACA,EACH,KADU,CACH,GAGT,IAAMC,EAAOD,EAAMC,IAAI,CAACC,OAAO,QAE3BD,GAAMP,OAAS,UAAYO,GAAMP,OAAS,MAKhD,CCRA,CDGwD,GCHxD,+FAEA,cACA,wCACA,6BACA,qBACA,CAEA,cACA,oBACA,CAEA,cACA,eAEA,EAIA,KACA,EAGA,MAIA,kEAXA,MAYA,CAEA,cACA,IACA,WACA,CAAI,KACJ,qBACA,CAEA,oBACA,OAIA,8BAGA,cACA,oCAGA,qBACA,EAIA,KACA,WAGA,KAIA,KACA,EAGA,WACA,gBAGA,SAXA,SARA,QAoBA,CAOA,QAA8C,iBAAe,CAAG,WAAS,CAEzE,cACA,MAAqB,YAAM,IAI3B,OAHA,OACA,WACA,CAAG,EACM,iBAAW,YACpB,0CAAwE,IAAa,IACrF,kBAGA,6CACA,CAAG,IACH,CAgBA,gBACA,YACA,QAGA,MAAmB,YAAM,IAMzB,OALA,OACA,eACA,aAEA,CAAG,IACH,CACA,CAEA,gBACA,MAAmB,YAAM,GACzB,MAAS,aAAO,MAChB,mBAEA,OADA,YACA,CACA,CAAG,CACH,OACA,CAEA,cACA,WACA,EAAe,YAAM,OACrB,EAAqB,iBAAW,KAChC,eACA,0BAGA,WACA,CAAG,CACH,IACA,YAGA,cACA,MAAc,YAAM,GAIpB,MAHE,eAAS,MACX,WACA,CAAG,MACH,UAGA,SACA,gBACA,MAAS,aAAO,MAChB,KACA,SAGA,0BAEA,OADA,OACA,OACA,CAAG,OACH,CAEA,cACA,mBACA,kDAAkG,IAAa,IAC/G,oBAGA,wBAGA,eAFA,kBAEA,CACA,iBAEA,GACA,YAEA,CAEA,QACA,CAAK,EAAI,KACJ,CACL,CACA,CAEA,WACM,EAAQ,MAMd,cANc,GAOd,GACA,SAGA,IACA,gBACA,CAAI,YACJ,wBACA,CAiBA,cACA,YAhBA,GACA,MACA,SAGA,IACA,aACA,CAAI,YACJ,wBACA,EAOA,GACA,iCACA,IACA,UACA,UACA,CAAQ,aACR,OACA,IACA,GACA,CACA,EAAM,kDACN,IACA,UACA,UACA,CAAQ,oBACR,OACA,IACA,GACA,CACA,QAjDA,YAoDA,GApDA,YAoDA,EACA,CACA,YACA,aAIA,IACA,CAEA,qBACA,WACA,YACA,MACA,OAGA,IACA,IACA,IACA,CAAQ,EACR,4EACA,CAEA,CAAG,CACH,OACA,YACA,MACA,OAGA,IACA,SACA,SACA,CAAQ,EACR,mCACA,CAEA,CAAG,CACH,WACA,YACA,KAIA,6DACA,CAEA,CAAG,CACH,YACA,YACA,IACA,WACA,WACA,SACA,CAAQ,EACR,sBACA,CAEA,CACA,CAAC,EAED,2IChUA,GACA,cACA,EACA,cACA,IACA,KACA,QACA,CAAI,EACJ,OAAS,iBAAmB,QAC5B,KACA,OACA,CAAG,GACH,CAEA,cACA,IACA,KACA,eACA,2BACA,CAAI,EAgBJ,OAAS,iBAAmB,QAC5B,KACA,MAhBA,CACA,iBACA,MACA,OACA,QACA,SACA,UACA,SACA,UACA,kBACA,qBACA,uBACA,mBACA,EAIA,cACA,cACA,gBACA,CAAG,GACH,CCvCA,MAAuC,mBAAa,OAkCpD,GACA,yNACA,EACA,GACA,eACA,IACA,SACA,CAAM,EACN,0CACA,CAAG,CAEH,cACA,IACA,SACA,OACA,CAAM,SAEN,EACA,kEAGA,6DACA,CAAG,CAEH,aACA,IACA,SACA,OACA,CAAM,SAEN,EACA,gEAGA,sCACA,CAAG,CAEH,gBACA,IACA,SACA,CAAM,EACN,oEACA,CAEA,EAEA,cACA,IACA,kBACA,YACA,0BACA,6BACA,CAAI,EACJ,CACA,WACA,eACA,CDjDA,WACA,SAA0C,cAAQ,KAMlD,OACA,SANmB,iBAAW,KAC9B,SACA,IAEA,CAAG,KAGH,cACA,CACA,ICuCA,EAAuB,EAAW,iBAClC,MAAgC,cAAQ,KACtC,eAAS,MACX,KACA,CAAG,SA7FH,EA8FgB,aAAO,OACvB,eACA,IACA,SACA,CAAQ,EACR,iBACA,QACA,CAAO,EACP,CAAK,CAEL,cACA,IACA,SACA,OACA,CAAQ,CAER,eACA,gBACA,SACA,MACA,CAAS,EAET,CAAK,CAEL,cACA,IACA,SACA,OACA,CAAQ,EACR,gBACA,SACA,MACA,CAAO,EACP,CAAK,CAEL,aACA,IACA,SACA,OACA,CAAQ,EACR,eACA,SACA,MACA,CAAO,EACP,CAAK,CAEL,gBACA,IACA,SACA,OACA,CAAQ,EACR,kBACA,SACA,MACA,CAAO,EACP,CAEA,EAAG,QAtJH,MAA2B,gBAAU,IAwJrC,GAvJE,eAAS,MACX,MACA,4EAIA,OADA,IAEA,CAAG,QAgJH,GACA,YAGA,MAAiB,iBAAmB,CAAC,YAAc,MAAQ,iBAAmB,CAAC,EAAU,CACzF,KACA,EAFyF,IAEzF,WACA,CAAG,EAAG,iBAAmB,CAAC,EAAU,CACpC,KACA,EAFoC,WAEpC,CACA,CAAG,GACH,SAAqB,kBAAY,OACjC,CAeA,cAEA,gBACA,MAAS,aAAO,OAChB,SACA,oBACA,EAAG,CACH,MACA,EAnBA,YACA,wBACA,sBACA,oBACA,0BACA,sBACA,wCACA,8CACA,2CACA,CAAC,UAAwB,EAqBzB,qBACA,IACA,GACA,CAAC,EA2CD,gBACA,IACA,MACA,QACA,CACA,CAAI,EACJ,CACA,MACA,QACA,CACA,CAAI,EACJ,UACA,CA6JA,WACA,IACA,gBACA,iBACA,sBACA,CAAI,EACJ,KAEA,gBACA,IACA,KACA,CAAM,EACN,WAEA,MACA,eAvCA,KACA,4BACA,0BACA,0CACA,0CAIA,aACA,uBACA,mBACA,GANA,KACA,MAOA,cADA,YACA,WACA,CAGA,CAHI,MAGJ,CACA,EAqBA,IAEA,MACA,QACA,KACA,MACA,qBACA,OACA,CACA,CAAS,CAET,CACA,CAEA,gBACA,EAsEA,iBACA,aACA,gBACA,eACI,CACJ,CAiBA,gBAfA,GACA,mBACA,kDAAkG,IAAa,IAC/G,oBAGA,yBAAsD,KACtD,gBACA,sBACA,kBACA,oBACA,EAAK,CAAK,KACL,CACL,CACA,EACA,GAEA,eACA,8BACA,gCACA,OACA,SACA,SACA,aACA,aAEA,CAAI,4BACJ,gCACA,OACA,QACA,QACA,aACA,aAEA,CAEA,WACA,CA6BA,QACA,kBACA,EAKA,iBACA,YACA,OAGA,gCAEA,sBACA,IACA,YACA,kBACA,CAAM,EAAW,sBAEjB,IACA,GAhDA,gBACA,YAEA,MACA,SAGA,IACA,SACA,SACA,IACA,IACA,CAAI,EACJ,+BACA,sDACA,sBACA,wBACA,OACA,QACA,SACA,MACA,UACA,WACA,MACA,CACA,EAuBA,OAEA,CAEA,IACA,MACA,OACA,QACA,SACA,SACA,QACA,CAAI,EACJ,OACA,MACA,OACA,QACA,SACA,SACA,OACA,CACA,CAUA,eACA,aACA,kBACA,CAAG,CACH,CAoCA,SAAS,GAAsB,KAC/B,gBAuCA,EAIA,KA5C+B,IAG/B,SAxBA,EAyBA,yBAIA,GAHA,SAOA,GAAQ,EAAU,8DAElB,OADA,2BACA,EAGA,IAAS,EAAa,IAAU,EAAY,IAI5C,CAJsB,CAItB,IAJ4C,IAI5C,IAHA,SAOA,MAA0B,EAAS,6BAQnC,CANA,OACA,SAzCA,KACA,YACA,GAAoB,EAAS,wBAG7B,8BAEA,MADA,qCACA,SACA,WACA,mCACA,CAAG,CACH,EA8BA,MACA,UAjDA,UADA,EAsDA,IApDA,GAAoB,EAoDpB,GApD6B,iBAoD7B,EApD6B,EAG7B,sBAkDA,EAGA,eACA,EAMA,GAHA,CAIA,CACA,eACA,OAAoC,GAAsB,KAC1D,qBACA,CAEA,CAJ0D,QAI1D,aACA,GAAgB,EAIV,EAAQ,EAJE,CAKhB,EAGO,CAJO,CAID,GAIP,CAJO,CAIG,UAAyC,oBACzD,OAGM,EAAa,GACnB,EAGA,KAXA,CAOmB,IAfnB,IAoBA,CAEA,sBACA,EAAc,GACd,GADc,MACd,CAGA,aAEA,sBACA,EAAc,GACd,GADc,MACd,CAGA,YAEA,eACA,OACA,QACA,OACA,CACA,CASA,qBACA,EAAO,IAAS,IAIhB,CAJgB,GAIhB,yBACA,CAEA,eACA,OACA,IACA,GACA,EACA,SACA,0BACA,yBACI,CACJ,sBACA,qBAEA,GACA,wBACA,2BAEA,mBACA,oBAGA,OACA,QACA,SACA,SALA,iBAMA,QALA,kBAMA,YACA,WACA,CACA,EAzCA,YACA,yBACA,2BACA,CAAC,SAA8B,GAwC/B,QACA,KACA,IACA,EA8FA,eACA,uBACW,EAAG,SACX,EACH,CAYA,iBAKA,GAJA,YACA,OAGA,GACA,OAGA,IACA,MACA,OACA,SACA,QACA,CAAI,KACJ,OAMA,2DACA,kBACA,eACA,eACA,CAAK,CAEL,CAEA,8BAxCA,YACA,uBACA,QACG,EACH,EAoCA,uBAnCA,YACA,uBACA,QACG,EACH,EA+BA,QACA,GACA,iBACA,iBACA,kBACA,mBACA,gBACA,mBACA,kBACA,iBACA,MAAgC,GAAsB,GACtD,QAMA,iBALA,WAAkB,IAClB,EACA,mBACA,qBAEA,IACA,eACA,8BACA,SACA,WACA,SACA,qBACA,CAAW,CACX,aACA,CAAS,EAIT,mCACA,aACA,CAAK,CACL,CAEA,CAEA,SACA,eACA,mBACA,kBAEA,oBACA,2BACA,MAEA,+DACA,CAAO,CACP,EAEA,aACA,CAEA,WACA,KAEA,kDACA,4BACA,CAEA,CAcA,iBACA,oBACA,sBAEA,mBACA,uBAGA,iBACA,aAGA,QACA,MAGA,SACA,MAkBA,eACA,kBACA,CACA,eACA,mBACA,EAfA,YACA,gBACA,wBACA,oBACA,4BACA,kBACA,oCACA,qCACA,CAAC,UAA8B,EAW/B,YACA,gBACA,mBACA,qBACA,mBACA,eACA,eACA,gBACA,WACA,CAAC,CAAE,GAAiB,GAAY,GAAK,EAErC,QACA,EAHe,GAAiB,CAGhC,CAAU,EAAY,MAAQ,EAAY,OAC1C,IADsB,GACtB,CAAW,EAAY,EADmB,CACnB,EACvB,KAAQ,EAAY,MAAQ,CADL,CACiB,MAAQ,EAAY,MAE5D,KAFwC,CAExC,KACA,EAH4D,CAG5D,CACA,qBACA,CAAI,EAEJ,eACA,KAAS,EAAY,MACrB,OAAe,KACf,CAFqB,CAErB,MACA,CAEA,MAAS,EAAY,KACrB,OAAe,KACf,EAFqB,EAErB,IACA,CAEA,MAAS,EAAY,KACrB,OAAe,KACf,QACA,CAEA,MAAS,EAAY,GACrB,OAAe,KACf,IAFqB,CAErB,GACA,CACA,CAGA,CAEA,UACA,eACA,kBACA,0BACA,iCACA,sBACA,4BACA,aACA,IACA,OACA,SACA,CACA,CAAM,CACN,cACA,sBAAmC,EAAgB,IACnD,UADmD,UACnD,QAAyC,EAAS,IAClD,GADkD,CAClD,6CACA,+CACA,aACA,CAEA,SACA,mBACA,qDACA,+DACA,gEACA,CAEA,cACA,IACA,aACA,UACA,CAAM,WACN,iBAEA,GACA,MAGA,IACA,CAEA,iBACA,GAAQ,EAAe,IACvB,IACA,KAFuB,EAEvB,EACA,UACA,UACA,CAAQ,WACR,CACA,mBACA,sBACA,0BACA,CAAQ,EACR,CACA,OACA,CAAQ,EAER,iCACA,kBAIA,oCACA,qBAIA,IACA,gBACA,CAAQ,UACR,KACA,SACA,SACQ,CAER,4BACA,8BAGA,WACA,SACA,kBACA,oBACA,CAAO,EAEP,MACA,MAAiC,EAAQ,KACzC,GACA,IACA,GACA,EACA,CACA,EANyC,kBAMzC,EACA,CAAU,UAEV,gBACA,aACA,CACA,QACA,UACA,SACA,WACA,YACA,YACA,CAAY,MACZ,WA3VA,GACA,kCACA,IACA,aACA,cACA,CAAM,OACN,OACA,MACA,OACA,QACA,SACA,QACA,QACA,CACA,CAEA,IACA,MACA,OACA,QACA,SACA,CAAI,0BACJ,OACA,MACA,OACA,QACA,SACA,oBACA,sBAEA,EA6TA,GACA,GACA,eAAsC,EAAY,6CAAiH,EAAY,oCAC/K,eAAsC,EAAY,+CAAmH,EAAY,iCACjL,EACA,MAA2C,EAAY,eAAoC,EAAY,EAAhD,EAAgD,KACvG,MAA2C,EAAY,EADgD,EAChD,UAAoC,EAAY,GAAhD,CAAgD,GAEvG,iBACA,uBACA,MAA4D,EAAY,qBAA2E,aAEnJ,uBAGA,YACA,OACA,UACA,CAAe,EAIf,EACA,mBAEA,QAA4C,EAAY,wCAGxD,KACA,YACA,UACA,UACA,CAAe,EAGf,KACA,CAAY,iBACZ,sBACA,MAA4D,EAAY,kBAA8D,CAA9D,CAA0E,WAElJ,QAFkJ,CAElJ,cAGA,YACA,MACA,UACA,CAAe,EAIf,EACA,kBAEA,QAA4C,EAAY,qCAGxD,KACA,YACA,SACA,UACA,CAAe,EAGf,KACA,CACA,CAEA,kBAA+B,EAAI,CAAD,CAAS,gCAC3C,CACA,CACA,CAEA,gBACA,IACA,SACA,CAAM,WACN,mBACA,IACA,CAEA,aACA,IACA,QACA,CAAM,WACN,mBACA,cACA,GACA,CAEA,gBACA,IACA,WACA,CAAM,WACN,mBACA,cACA,GACA,CAEA,SACA,2BACA,gCACA,CAEA,CAiCA,eACA,2BACA,CAEA,eACA,wBACA,CAtCA,gBACA,sBACA,kBACA,IACA,mBACA,eACA,CAAM,EACN,CACA,SACA,CAAM,EACN,CACA,OACA,CAAM,cAEN,wBACA,oCAEA,qBAIA,mBACA,YACA,oBACO,EACP,GACA,CAEA,QACA,CACA,CAAC,QAUD,GACA,mBACA,KAEA,aACA,GArWA,YAMA,IACA,cACA,CAAM,EAAS,GACf,IADe,GACf,iBAAkD,EAAgB,EAClE,EA2VA,UA5VkE,IA4VlE,GAGA,kBACA,mBACA,0BACA,qBACA,kBACA,+BACA,oBACA,sBACA,8BACA,4BACA,aACA,cACA,IACA,QACA,CAAM,EACN,CACA,SACA,CAAM,CACN,cACA,cACA,cAAoB,EAAgB,GACpC,WADoC,WACpC,uBACA,yBACA,4BAAyC,EAAS,IAClD,GADkD,CAClD,oBAAyE,MAAzE,GAAsD,EAAmB,QACzE,SADyE,OACzE,6BACA,2CACA,yCACA,+CACA,iDACA,6DACA,aACA,CAEA,SACA,IACA,SACA,OACA,SACA,uBACA,6BACA,CACA,CACA,CAAM,KAgBN,GAfA,gDACA,UACA,CAAK,EACL,8CAEA,UACA,oDAGA,qDACA,yCACA,+DACA,2CACA,yDAEA,GACA,eACA,uBACA,iCACA,2BACO,EACP,0BAGA,UACA,oDACA,sBACA,MACA,CAEA,qBACA,qBAGA,CAEA,kBACA,CAEA,SACA,2BACA,iCAGA,CAHsC,UAGtC,qCAEA,wBACA,6BACA,oBAEA,CAEA,mBACA,IACA,SACA,YACA,CAAM,WACN,gCACA,CAEA,cACA,IACA,qBACA,CAAM,KACN,CACA,UACA,CAAM,WAEN,IACA,kBAEA,IAF6B,CAE7B,kCACA,UACA,CAAO,EAEP,CAFU,GAEV,uBAEA,CAFkC,GAElC,mEACA,KAEA,CAEA,cACA,MAEA,IACA,YACA,qBACA,QACA,CAAM,KACN,CACA,SACA,SACA,uBACA,CACA,CAAM,EAEN,MACA,OAGA,eAAiD,EAAmB,QACpE,EAAkB,EAAQ,KAE1B,UACA,CAH6D,EAG7D,EAH0B,CAG1B,IACA,wCACA,2BAGA,oBACA,yBAEA,QAEA,OACA,kBACA,yBAIA,uBAEA,CAEA,cACA,mBAGA,IACA,CAEA,YACA,IACA,UACA,QACA,CAAM,WACN,cAEA,gBACA,qBAGA,GACA,CAEA,eACA,IACA,UACA,WACA,CAAM,WACN,cAEA,gBACA,qBAGA,GACA,CAEA,iBACA,SAAuB,EAAY,KACnC,cADmC,GACnC,EAEA,CAEA,sBACA,KAEA,4DACA,CAEA,CAEA,QACA,QACA,oBACA,CAAG,CACH,MACA,kBACA,CAAG,CACH,KACA,gBACA,CACA,CACA,qBACA,eACA,IACA,QACA,CAAM,EAIN,KAJe,CAIf,KAD2B,EAAgB,UAE3C,CAEA,CACA,EAL2C,CAK3C,aACA,0BACA,gBACA,IACA,cACA,CAAM,EACN,CACA,eACA,CAAM,QAEN,8BAIA,YACA,OACA,CAAK,EACL,GACA,CACA,CAAC,EAED,QACA,MACA,gBACA,CAAG,CACH,KACA,cACA,CACA,GAGA,YACA,8BACA,CAAC,UAAkC,CAEnC,qBACA,eACA,WAA2B,EAAgB,gBAC3C,CAEA,CACA,gBACA,wBACA,gBACA,IACA,cACA,CAAM,EACN,CACA,eACA,CAAM,SAEN,0BAIA,YACA,OACA,CAAK,EACL,GACA,CACA,CAAC,EAED,QACA,QACA,kBACA,CAAG,CACH,MACA,gBACA,CAAG,CACH,KACA,eACA,CACA,CACA,qBACA,eACA,WACA,CAEA,eAQA,OAJA,wCACA,WACA,UACA,CAAK,EACL,WACA,0CACA,EAGA,CAHO,QAGP,KACA,CAEA,CACA,gBACA,yBACA,gBACA,IACA,cACA,CAAM,EACN,CACA,eACA,CAAM,EACN,CACA,UACA,CAAM,QAEN,gBAIA,YACA,OACA,CAAK,EACL,GACA,CACA,CAAC,EAID,YACA,yBACA,oCACA,CAAC,UAAkD,EAInD,YACA,6BACA,4CACA,CAAC,YAgHD,QACA,GACA,gBACA,cACA,CAAG,CACH,GACA,gBACA,cACA,CACA,GAgEA,YACA,uBACA,uCACA,oCACA,CAAC,YAKD,CACC,SAAgD,EADjD,sBAGA,eAmHA,iBACA,OAAS,EAAW,GACpB,EAIA,GAIA,CAToB,YASpB,iBAPA,KAQG,MACH,CAsCA,eACA,IACA,WACA,WACA,CAAI,EACJ,EAAuB,EAAQ,GAC/B,EAAyB,CADM,EACN,UAAO,MAChC,iEACA,OAGA,IACA,iBACA,CAAM,OACN,eACA,CAAG,CACH,KAIA,MAHE,eAAS,KACX,kCACG,KACH,CACA,CAEA,eACA,sBACA,CAEA,mBACA,YACA,OAGA,SAA0B,cAAQ,OAElC,aACA,MACA,MACA,YAGA,uBACA,MAIA,mCACA,CAEA,kBAEA,sCACA,EAGA,CACA,CAAK,CACL,CAEA,MArFA,YACA,IACA,WACA,WACA,CAAI,EACJ,EAA0B,EAAQ,GAClC,EAA2B,CADO,EACP,UAAO,MAClC,mEACA,OAGA,IACA,mBACA,CAAM,OACN,eACA,CAAG,QAIH,MAHE,eAAS,KACX,kCACG,KACH,CACA,EAiEA,CACA,YACA,KAIA,gBACA,IACA,OACA,SACA,CAAU,EAEV,6DACA,IACA,KACA,CACA,CACA,CAEA,CAAG,EACH,MACA,UACA,CAAG,EAeH,OAdE,EAAyB,KAC3B,IAEA,GACA,WAJ2B,OAI3B,IACA,kCACA,aACA,UACA,CAAO,IAEP,wBACA,wBAEA,CAAG,MACH,CACA,CAOA,UAkFA,iBACA,YACA,OAGA,MAA+B,YAAM,OAgBrC,MAfE,eAAS,MACX,cACA,CAAG,CACH,GACE,eAAS,MACX,WAEA,gBACA,cAGA,eACA,gBAEA,CAAG,MACH,UAAwC,EAAQ,cAChD,CA8CA,KA/CgD,IA+ChD,MACA,MAAS,aAAO,OA/rDhB,YACA,mBACA,gBACA,OACA,MACA,OACA,QACA,SACA,QACA,QACA,CACA,EAorDgB,YAChB,CAEA,UA+BA,eACA,MACA,YAGA,uBACA,SAGA,oBACA,OAAS,EAAa,MACtB,CA4CA,IA7CsB,GA6CtB,EACA,UACA,UACA,CAAC,EACD,UACA,UACA,CAAC,EACD,IACA,UACA,EACA,IACA,WACA,UACA,CAAG,CACH,WACA,WACA,yBACA,sBACG,CACH,aACA,UACA,CACA,CAEA,sBACA,OACA,MAEA,+CACA,CAEA,UACA,gCACA,CAEA,aACA,iCACA,IACA,WACA,CAAQ,EACR,QACA,CAAK,CACL,CAEA,cACA,QAEA,qEACA,CAEA,CAEA,QACA,oBACA,YACA,gBACA,oBACA,gBACA,uBACA,uBACA,uBACA,2BACA,UACA,aACA,SACA,YACA,CAAK,CACL,UACA,QACA,CAAG,CACH,uBACA,2BACA,0BACA,6BACA,gBACA,qBACA,EACA,IACA,oBACA,cACA,YACA,oBACA,mBACA,YACA,CAAG,CACH,WACA,uBACA,UACA,4BACA,EACA,GAAqC,mBAAa,KAClD,GAAmC,mBAAa,KAEhD,cACA,OACA,WACA,YACA,oBACA,IACA,GACA,CAAO,CACP,cACA,WACA,IACA,GACA,CACA,CAAK,CACL,WACA,iBACA,CACA,CACA,CACA,iBACA,eACA,iBACA,OAAe,KACf,WAAqB,eACrB,wCACA,gBAEA,CAEA,iBACA,4BACA,SAGA,OAAe,KACf,WAAqB,eACrB,WACA,mDACA,mDAEA,CACA,CAEA,gBACA,kBACA,OAAe,KACf,WAAqB,eACrB,YACA,oBACA,IACA,GACA,CAAW,CACX,WACA,IACA,GACA,CACA,CACA,CAEA,0BACA,CACA,IACA,UACA,CAAU,EACV,CACA,KACA,CAAU,EACV,iCAEA,OADA,WACA,CAAiB,KACjB,WAAuB,eACvB,YACA,CACA,CACA,CAEA,4BACA,CACA,IACA,KACA,MACA,WACA,CAAU,EACV,gCAEA,iBACA,SAGA,qCAIA,OAHA,SAA6B,KAC7B,UACA,CAAS,EACT,CAAiB,KACjB,WAAuB,eACvB,YACA,CACA,CACA,CAEA,2BACA,CACA,IACA,KACA,MACA,CAAU,EACV,gCAEA,iBACA,SAGA,qCAEA,OADA,YACA,CAAiB,KACjB,WAAuB,eACvB,YACA,CACA,CACA,CAEA,QAEA,QAEA,CACA,CAEA,eACA,IACA,WACA,CAAI,EACJ,CACA,SACA,iBACA,iBACA,CAAM,gBAAU,KAChB,EAAiC,EAAW,GAC5C,EAA2B,EAAW,EADM,IACN,eAgDtC,CAhD6E,KAE3E,eAAS,MACX,OAIA,gBACA,IAAW,EAAe,IAI1B,SAJ0B,aAI1B,YAHA,OAQA,eAEA,MACA,OAGA,IACA,gBACA,OACA,CAAQ,EAER,wBAIA,2BACA,mCACA,MACA,SAGA,MF52EA,aE42EsD,CAAtB,CFx2EhC,mBE02EA,EAFsD,CAEtD,GACA,UACA,KACA,CACA,CACA,CAAO,CACP,CACA,CAAG,cACH,IACA,CAEA,iBACA,IACA,YACA,KACA,CAAI,EACJ,yCACA,GACA,YACA,KACK,EACF,IACH,CAkFA,OAA4C,mBAAa,EAAG,KAC5D,SACA,QACA,CAAC,GAGD,YACA,qCACA,mCACA,gCACA,CAAC,UAAwB,EAEzB,OAAgC,UAAI,iBACpC,QAryEA,IAuyEA,IACA,KACA,gBACA,gBACA,WACA,aACA,wBACA,YACA,YACA,KACA,CAAI,EAEJ,MADgB,gBAAU,eAE1B,MAnyFA,WACA,OAAsB,cAAQ,cAC9B,EAA2B,iBAAW,KACtC,SACA,iBACG,KAYH,OAXmB,iBAAW,KAC9B,IACA,OACA,QACA,CAAM,EACN,cACA,MAEA,wCACA,CAAK,CACL,CAAG,MACH,EACA,IAkxFA,MAA8B,cAAQ,kBACtC,oBACA,CACA,WACA,SACA,QACA,YACK,CACL,WACA,aACA,CACA,CAAI,EACJ,wBACA,EAAsB,YAAM,EAC5B,aACA,eACA,CAAG,EACH,EAAiB,aAAO,MACxB,MAEA,gBACA,KAEA,0CACA,MACA,EAAM,IACN,CAAG,QACH,EAAoB,YAAM,OAC1B,QAA0C,cAAQ,OAClD,QAA8C,cAAQ,OACtD,GAAsB,EAAc,oBACpC,GAAiC,EAAW,oBAC5C,GAAqC,aAAO,yBAC5C,GA7IS,aAAO,OAChB,WAAiB,gBACjB,SA2IA,EA3IA,mBACK,CACL,WAAiB,gBACjB,SAwIA,EAxIA,mBACK,CACL,aAAmB,kBACnB,SAqIA,EArIA,OAqIA,EArIA,YAEA,EAAG,CACH,OAkIA,EAlIA,OAkIA,EAlIA,gBAkIA,EAlIA,yBAkIA,EAlIA,OAkIA,EAlIA,cAmIA,CACA,kBACA,8BACA,sBACA,CAAI,SAr7BJ,KACA,IACA,WACA,eACA,SACA,CAAI,EACJ,MAA4B,cAAQ,OACpC,CACA,YACA,UACA,WACA,CAAI,EACJ,EAAwB,YAAM,IAC9B,aAwFA,UACA,cACA,QAEA,uBACA,QAEA,SACA,QACA,CACA,IAjGA,EAAsB,EAAc,GACpC,EAAqC,OADD,SACC,CAAW,aAChD,YACA,OAGA,WAIA,KACA,SACA,EAGA,sCAEA,CAAG,MACH,EAAoB,YAAM,OAC1B,EAAyB,EAAW,IACpC,KADoC,CACpC,GACA,UAGA,uCACA,cAEA,gBACA,MACA,SAGA,qDAEA,2BACA,QACA,CAEA,qBACA,uBACA,kBAEA,GACA,aAEA,CAEA,QACA,CAEA,QACA,CAAG,cA6BH,MA5BE,eAAS,MACX,WACA,CAAG,MACD,eAAS,MACX,GAIA,GACA,CAAG,CACH,OACE,eAAS,MACX,eACA,OAEA,CAAG,CACH,qBACE,eAAS,MACX,yCAIA,2BACA,IACA,cACA,CAAK,IACL,CAAG,CACH,cACA,CACA,iBACA,6BACA,0BACA,CAcA,EAq0BI,IACJ,WACA,uBACA,oBACG,EACH,GAv+BA,cACA,8BACA,wBACA,OAAS,EAAW,IACpB,KADoB,QAGpB,QACA,KAMA,4BACA,CAAG,OACH,EAw9BA,KACA,GAAgC,aAAO,QAAwB,EAAmB,eAClF,EADkF,CAClF,WAmcA,kDACA,2CACA,kBAEA,mBACA,CAAe,KACf,SACA,EAGA,CACA,SACA,CACA,IA/cA,GA5zBA,GA4zBA,0BACA,SA7IA,GACA,IACA,aACA,UACA,cACA,YACA,CAAI,EACJ,EAAsB,YAAM,KAC5B,CACA,IACA,IACA,CAAI,qBACJ,IACA,GACA,EAAI,EACF,EAAyB,KAG3B,GAFA,QAEA,IACA,GAJ2B,MAI3B,IACA,MACA,CAEA,iBAGA,OAIA,oCAEA,0BAGA,OAIA,SADA,KACA,GAaA,GAXA,GACA,QAGA,GACA,QAIA,aAEA,kCACA,WAEA,IACA,YACA,QACA,SACS,CAET,CACA,CAAG,aACH,EA8EA,CACA,iCACA,kCACA,eACA,6BACG,EACH,sCACA,gCACA,GAAwB,YAAM,EAC9B,oBACA,YACA,cACA,mBACA,gBACA,kBACA,iBACA,kBACA,sBACA,sBACA,UACA,uBACA,4BACA,CAAG,EACH,uDACA,YArgBA,GACA,IACA,UACA,CAAI,EACJ,MAA0B,cAAQ,OAiBlC,MACA,SAjBuB,iBAAW,KAClC,QACA,SACA,CAAM,IACN,GAAU,EAAa,IACvB,MACA,CAFuB,GAEvB,OACA,UAA0B,KAC1B,cACA,iBACY,CACZ,CAAS,EACT,KACA,CAEA,CAAG,KAGH,CAAG,EAWH,MAA4B,EAVD,QAUW,QAVX,CAAW,KACtC,WACA,yBAEA,GACA,wBAGA,cACA,CAAG,SAEH,MAAS,aAAO,OAChB,UACA,OACA,QACA,EAAG,SACH,EA6dA,CACA,+BACG,EAEH,CAFM,EAEN,kCACA,iCACA,mCAGA,CAHoF,EAGpF,SAztBA,GACA,YACA,cACA,EAstBA,YAEA,SAAkD,EAAS,EAFoB,EAEpB,MAE3D,CAFmF,EArtBnF,YACA,MAAuB,YAAM,IAC7B,EAAoB,EAAW,GAC/B,EAIA,IAL+B,GAK/B,sDACA,EAGW,GAAsB,GAPjC,GAQG,KAIH,MAHE,WAF+B,GAE/B,CAAS,MACX,WACA,CAAG,MACH,CACA,EAssBA,uBACA,YA9jBA,KACA,YACA,OAGA,SACA,OAAkD,EAAS,SAC3D,MAA4B,cAAQ,KAEpC,aACA,MACA,SAIA,iCAHA,GAKA,CAEA,UACA,UACA,CAAG,EAMH,OALE,EAAyB,KAC3B,kBAD2B,GAC3B,GACA,IACA,yCACA,CAAG,MACH,CACA,EAkiBA,IAEA,SACA,SAHiE,CAGjE,CACA,WACA,WACA,SACA,QACA,CAAK,CACL,kBACA,SACA,kBACA,qBACA,oBACA,qBACA,wBACA,uBACA,2BACA,aACA,CAAG,EACH,MAAqD,EAAG,WACxD,GA1tBA,YACA,SAAoD,cAAQ,OAC5D,EAAuB,YAAM,IAE7B,EAAuB,MAFkB,UAElB,CAAW,KAClC,mBAEA,GAIA,KACA,GAIA,eACA,YAJA,KAMA,CAAG,KAkCH,MAjCE,eAAS,MACX,gBAEA,UACA,KACA,gBACA,mBAEA,GACA,+BACA,UACA,CAAW,EACX,WAGA,IACA,CAAO,qBACP,4BACA,WACA,CAEA,WACA,KACA,IACA,EAEA,cACA,cACA,WACA,2CACA,CAAO,CACP,CACA,CAAG,QACM,aAAO,KAChB,SACA,uCAAqG,EAAG,cAGxG,EACG,MACH,EA8pBA,IAEA,UAEA,QAJ+D,CAI/D,GAFiE,CAEjE,EACA,GAAkC,EAAG,OACrC,qBACA,YACA,SACA,iBACA,kBACA,uBACA,qBACA,CAAG,OACH,GAAiB,SA5oFR,CAAiB,IAC1B,YA2oFkC,CA3oFlC,OACA,CAF0B,MAE1B,KAGA,SACA,SAsoFkC,EAroFlC,EAqoFkC,SAClC,QAA0B,cAAQ,OAIlC,CAJ0C,GA96E1C,EAi7EA,MAAiE,EAAG,OAj7EpE,EAk7EA,yCAj7EA,CAAW,KACX,UAg7EA,GAh7EA,QAg7EA,GAh7EA,QACA,UA+6EA,GA/6EA,oBACA,GA+6EA,GAA0B,YAAM,OAChC,GAA4B,iBAAW,SACvC,IACA,SACA,UACA,CAAM,EAEN,mBACA,OAGA,uBAEA,MACA,OAGA,oBACA,SACA,iBACA,aACA,QACA,UAGA,WAEA,WAGA,IAFA,SAGA,OAGA,IACA,cACA,CAAU,WACV,GACA,IACA,CACA,eACA,GACA,mBACA,OACA,CAAS,CACT,CAAO,CAEP,mBAGA,IAFA,SAGA,OAGA,IACA,gBACA,CAAU,WACV,GACA,KACA,aACA,qBACA,QACA,CACA,eACA,GACA,qBACA,OACA,CAAS,CACT,CAAO,CAEP,WACA,gBAEA,WACA,OAGA,eAEA,MACA,OAGA,IACA,cACA,CAAU,WACV,GACA,iBACA,QACA,KACA,YACA,MACA,CACA,EACQ,6BAAuB,MAC/B,cACA,kBACA,GACA,iBACA,qBACA,QACA,CAAW,EACX,GACA,mBACA,OACA,CAAW,EACX,eACA,KACA,CAAS,CACT,CAAO,CAEP,UACA,GACA,gBACA,aACA,CAAS,CACT,CAAO,CAEP,mBACA,wBACA,CAAK,EAGL,cACA,wBACA,IACA,SACA,aACA,OACA,0BACA,CAAU,WACV,OAEA,SACA,IACA,aACA,CAAY,WACZ,GACA,iBACA,SACA,aACA,QACA,MACA,EAEA,qCACA,6BAGA,gBAGA,CAEA,eACQ,6BAAuB,MAC/B,GACA,MACA,CAAW,EACX,mBACA,SACA,SACA,SACA,gBACA,+CAEA,MACA,0BACA,QACA,GACA,OACA,OACA,CAAa,CACb,CACA,CAAS,CACT,CACA,CAvDA,YAwDA,CAAG,CACH,KACA,GAA4C,iBAAW,QACvD,QACA,oBACA,UAGA,oBACA,GACA,8BASA,KAFA,cAHA,CACA,QACA,KAIA,UACA,qBAEA,YACA,QAEA,EACG,QACH,GArvCS,aAAO,sBAChB,IACA,SACA,CAAM,EAKN,eAJA,sBACA,sBACA,QA+uCA,GA/uCA,YACA,EAAK,EACL,CACG,MA4uCH,KA5uCG,EAwYD,eAAS,MACX,IAAS,EACT,OADkB,IAIlB,YACA,IACA,SACA,CAAQ,EACR,qCACA,CAAK,EACL,WACA,eACA,YAEA,CACA,CAAG,CAq1BH,EAn1BA,QACA,IACA,SACA,CAAM,EACN,QACA,CAAG,GA+0BD,EAAyB,KAC3B,kBAD2B,IAC3B,EACA,gBAEA,CAAG,SACD,eAAS,MACX,IACA,aACA,CAAM,WACN,CACA,SACA,iBACA,aACA,OACA,CAAM,WAEN,UACA,OAGA,OACA,SACA,iBACA,aACA,OACA,OACA,OACO,CACP,MACA,EACI,6BAAuB,MAC3B,cACA,GACA,kBACA,OACA,CAAO,CACP,CAAK,CACL,CAAG,CACH,aACE,eAAS,MACX,IACA,SACA,iBACA,aACA,sBACA,0BACA,CAAM,WAEN,+BACA,OAGA,IACA,aACA,CAAM,WACN,YACA,qBACA,QACA,oBACA,YACA,qBACM,KACN,GACA,SACA,iBACA,aACA,OACA,MACA,KACA,CAAO,CACP,MACA,EACI,6BAAuB,MAC3B,MACA,cACA,GACA,kBACA,OACA,CAAO,CACP,CAAK,CACL,CAAG,CACH,MACE,EAAyB,KAC3B,YACA,MAF2B,SAE3B,GACA,SACA,cACA,iBACA,cACA,kBACA,iBACA,gBACA,oBACA,sBACA,QACA,uBACA,0BACA,EACA,WACA,WACA,aACA,CACA,CAAG,qCAtgDH,YACA,IACA,eACA,sBACA,YACA,eACA,UACA,aACA,oBACA,qBACA,sBACA,0BACA,QACA,YACA,CAAI,EACJ,WA0GA,GACA,IACA,QACA,WACA,CAAI,EACJ,EAAwB,EAAW,GACnC,MADmC,CAC1B,EAAW,IACpB,KADoB,CACpB,OAEA,UAGA,OACA,qBACA,oBACA,EAEA,CAFO,KAEP,CACA,GACA,uCACA,mCACA,CAAO,CACP,GACA,uCACA,oCAEA,CACA,CAAG,SACH,EAtIA,CACA,QACA,WACA,CAAG,EACH,MF3oDA,WACA,MAAsB,YAAM,OAU5B,OATc,iBAAW,SACzB,0BACA,CAAG,KACa,iBAAW,MAC3B,mBACA,yBACA,eAEA,CAAG,KACH,IEioDA,EAAsB,YAAM,EAC5B,IACA,GACA,CAAG,EACH,EAA0B,YAAM,EAChC,IACA,GACA,CAAG,EACH,EAAe,aAAO,MACtB,UACA,eACA,UACA,QACA,WACA,SACA,WACU,IAEV,sBACA,QACA,CACA,CAAG,UACH,EAA6B,YAAM,OACnC,EAAqB,iBAAW,MAChC,gBAEA,MACA,OAGA,8BACA,0BACA,eACA,CAAG,KACH,EAAoC,aAAO,+CACzC,eAAS,MACX,iCACA,IAIA,gBACA,8BACA,SAIA,QADA,aACA,CAEA,MACA,SAGA,IACA,YACA,QACA,CAAQ,SAl/BR,WACA,IACA,MACA,OACA,QACA,SACA,CAAI,CAEJ,aACA,OAGA,YACA,OAGA,IACA,QACA,WACA,SACA,UACA,CAAI,MACJ,GACA,IACA,GACA,EACA,GACA,IACA,GACA,EACA,GACA,oBACA,mBAuBA,MApBA,uBAEA,eACA,6CACI,2BAEJ,cACA,gDAGA,wBAEA,cACA,6CACI,wBAEJ,eACA,4CAGA,CACA,YACA,OACA,CACA,EAu7BQ,WAER,sBACA,aACA,OACA,QAIA,iBACA,IACA,YACA,OACA,YACA,YACA,MACA,CACA,CAEA,WACA,IACA,GACA,EACA,WACA,IACA,GACA,EACA,GACA,CAAG,CACH,aACA,kBACA,0BACA,mBACA,EA05CA,CAAoB,MACpB,QACA,gBACA,sBACA,uBACA,0BACA,CAAG,EACH,OAAwB,aAAO,KAC/B,EACA,SACA,cACA,kBACA,kBACA,cACA,qBACA,eACA,iBACA,sBACA,kBACA,QACA,8BACA,uBACA,2BACA,0BACA,sBACA,aACA,GAEG,mDACH,GAA0B,aAAO,KACjC,EACA,kBACA,cACA,SACA,kBACA,mBACA,YACA,CAAO,CACP,WACA,iBACA,QACA,6BACA,GAEG,2BACH,OAAS,iBAAmB,aAC5B,OACA,CAAG,CAAE,iBAAmB,cACxB,QACA,CAAG,CAAE,iBAAmB,cACxB,QACA,CAAG,CAAE,iBAAmB,cACxB,QACA,CAAG,KAAc,iBAAmB,KACpC,6CACA,CAAG,GAAI,iBAAmB,IAAkB,KAC5C,0BACA,CAAG,EAiBH,CAAC,EAED,GAAiC,mBAAa,OAC9C,YAsEA,cACA,MAAS,gBAAU,IACnB,CAGA,QACA,UACA,EAoHA,eACA,IACA,YACA,WACA,CAAI,EACJ,MAA8C,cAAQ,OACtD,MAAgC,cAAQ,OACxC,EAA2B,EAAW,GAuBtC,MAvBsC,CAEtC,UACA,KAGE,EAAyB,KAC3B,MACA,OAGA,KAL2B,CAK3B,qBACA,4BAEA,gCACA,QAIA,kCACA,OACA,CAAK,CACL,CAAG,UACM,iBAAmB,CAAC,YAAc,UAAmC,kBAAY,IAC1F,KACA,CAAG,OACH,CAEA,QACA,IACA,IACA,SACA,QACA,EACA,eACA,IACA,WACA,CAAI,EACJ,OAAS,iBAAmB,cAC5B,QACA,CAAG,CAAE,iBAAmB,cACxB,QACA,CAAG,IACH,CAEA,QACA,iBACA,kBACA,EAEA,MAC8B,EAAe,GAC7C,UAD6C,aAC7C,OAGA,GAAuC,gBAAU,SACjD,IACA,KACA,iBACA,cACA,WACA,YACA,OACA,QACA,YACA,gBACA,CAAI,EAEJ,MACA,YAGA,WAA6D,KAC7D,SACA,QACA,EACA,GAAmB,MACnB,cACA,gBACA,UACA,YACA,UAAe,EAAG,sBAClB,qBAx3GA,cACA,MAA2B,EAAmB,GAE9C,MACA,QAH8C,IAM9C,OACA,2BACA,0BACA,EACA,uBACA,EA42GA,YACA,uCACA,MAEA,OAAS,iBAAmB,IAC5B,YACA,QACA,KACA,CAAG,GACH,CAAC,EAmED,IACA,aACA,cACA,UAjBA,IACA,IACA,WACA,UACA,QACA,CACA,CAAI,EACJ,QACA,UAAe,EAAG,qBAClB,CAAG,EACH,UAAe,EAAG,qBAClB,CAAG,GAOH,aArEA,EAqEA,CACA,QACA,QACA,WACA,CACA,CACA,CAAG,CA3EH,IACA,IACA,SACA,cACA,CAAI,EACJ,KACA,CACA,SACA,YACA,CAAI,EAEJ,qBACA,wCACA,aAIA,sCACA,+BAIA,0BACA,6CACA,YAIA,8BAYA,OARA,mBACA,+BAGA,wBACA,oCAGA,WACA,iCACA,6BAGA,oBACA,iCAEA,CACA,EA2BA,EAmIA,KAYA,GAAiC,QAAU,KAC3C,IACA,iBACA,WACA,gBACA,QACA,aACA,YACA,uBACA,YACA,aACA,CAAI,EACJ,CACA,iBACA,SACA,iBACA,oBACA,iBACA,sBACA,cACA,OACA,yBACA,sBACA,0BACA,aACA,CAAI,KACJ,EAAoB,gBAAU,KAC9B,EAtCA,YACA,MAAS,aAAO,MAChB,WAKA,UACA,CAAG,KACH,EA6BA,qBACA,QACA,iBACA,SACA,iBACA,oBACA,wBACA,OACA,uBACA,sBACA,0BACA,YACA,YACA,CAAG,EACH,QACA,EAxLA,YACA,IACA,SACA,iBACA,sBACA,yBACA,CAAI,EACJ,OAAS,EAAQ,QACjB,YACA,OAGA,eAEA,MACA,OAGA,qBAEA,MACA,OAGA,YAEA,MACA,OAGA,IACA,YACA,CAAM,EAAW,uBACjB,QAEA,MACA,OAGA,sCAqBA,GACA,IACA,WACA,SACA,cACA,YACA,CAAI,CAAI,MACR,MAEA,WACA,IACA,SACA,cACA,YACA,KACA,CAAM,EAEN,MAEA,OAGA,OACA,0BACA,uBACA,EACA,GACA,yDACA,0DACA,EACA,GACA,UACA,UACA,MAEA,KAA2C,KAC3C,SACA,cACA,WACA,UACA,OACA,CACA,CAAK,EACL,MACA,gBAEA,yCAEA,OAGA,wBACA,SACA,cACA,KACK,EACL,oBACA,WACA,SACA,eACA,CAAK,EACL,uBACA,gBACA,aACA,GACA,CACA,CAAK,CACL,CACA,EAzFA,GAEA,OADA,0BACA,GACA,QACA,KACA,YACA,OACA,2BACA,CAAO,CACP,iBACA,aACA,OACA,6BACA,CAAO,CACP,sBACA,yBACA,WACA,CAAK,CACL,CAAG,CACH,EA8HA,CACA,SACA,iBACA,sBACA,wBACA,CAAG,EAGH,CAHM,CAGN,kBACA,OAAS,iBAAmB,SAAiC,iBAAmB,KAChF,WACA,CAAG,MAAkB,iBAAmB,KACxC,MACA,QACA,MACA,KACA,iBACA,cACA,YACA,aACA,OACA,OACA,SACA,KACK,CACL,WACA,CAAG,UACH,CAAC,ECt3HD,mBACA,gBAEA,OADA,8CACA,CACA,CAwBA,eACA,qBACA,CA2GA,WACA,IACA,QACA,cACA,YACA,QACA,CAAI,EACJ,YACA,OACA,cAEA,KAIA,CACA,gBACA,cACA,uBACA,0BAPA,IASA,EA8GM,GAAS,WACf,GAA6B,KADd,YACiC,EAChD,eACA,YAAe,GACf,mBADwB,EAExB,SACA,aACA,kBACA,eACA,YACA,UACA,aACA,YACA,CACA,CAAC,EACD,eACA,IACA,WACA,KACA,QACA,cACA,cACA,CAAI,EACJ,CACA,SACA,cACA,iBACA,OACA,6BACA,CAAI,KACJ,EAAsB,EAAY,GAAS,CADxB,EAEnB,GADiC,MACjC,OAD2C,EAE3B,aAAO,wDACvB,UACA,uBACA,uBACA,EAA2B,YAAM,IACjC,GAjRA,cACA,SACA,SAGA,uBACA,SAGA,YAAkB,WAAc,IAChC,eACA,SAIA,QACA,EAiQA,aACA,oBACA,EAhQA,kBAgQA,EA/PA,CACA,UA8PA,EA7PA,UA6PA,CA5PA,EA4PA,EACE,EAAyB,KAC3B,MACA,IAEA,CAAG,OAJwB,CAIxB,IACD,eAAS,MACX,WACA,CAAG,MACH,MAAuB,aAAO,OAC9B,cACA,cACA,WACA,oBACA,QACA,YACA,iBACA,YAnTA,mBACA,MAkTA,EAlTA,OAMA,OAJA,GACA,SAGA,CACA,CAAG,kBA4SH,UACA,EAAG,CACH,2CACA,OAAS,iBAAmB,cAC5B,OACA,CAAG,GACH,CAEA,WACA,IACA,KACA,QACA,cACA,YACA,CAAI,EACJ,2BACA,EACA,OACA,IACA,cACA,YACA,cACA,QACA,QACA,WACA,gBACA,sBACA,aACA,CAAI,QAEJ,UAIA,mBAIA,GAIA,aACA,EACM,GAAiB,CACvB,aACA,aAFuB,EAIvB,eACA,GAAwC,EAAG,qBAC3C,YACA,WACA,eACA,CAAC,EACD,IACA,0BACA,EAiDA,mBA2LA,IACA,IA3LA,IACA,0BACA,aACA,WACA,OACA,iBACA,KACA,WACA,uBACA,aAAiB,EAAiB,CAClC,CAAI,EACJ,CACA,QACA,cACA,cACA,WACA,oBACA,cACA,YACA,iBACA,WACA,CAAI,CAAE,eAAU,KAChB,GAoKA,EApKA,EAoKA,EApKA,EAuKA,oBACA,CACA,YAEA,YACA,EAGA,CACA,6DACA,4DACA,GAjLA,eACA,EAAe,aAAO,OACtB,UACA,cACA,QACA,OACA,CAAK,CACL,KACA,CAAG,YACH,EAAoC,aAAO,kCAC3C,CACA,OACA,OACA,SACA,aACA,CAAI,SD06FJ,GC16FkB,GD26FlB,CACA,OACA,cACA,KACA,uBACA,CAAI,EACJ,EAAc,EAXd,SAWyB,IACzB,CACA,SACA,WACA,OACA,6BACA,CAAM,gBAAU,KAChB,EAAmB,YAAM,EACzB,UACA,CAAG,EACH,EAAkC,YAAM,KACxC,EAAe,YAAM,OACrB,EAAqB,YAAM,OAC3B,CACA,WACA,wBACA,UACA,CAAI,CAAI,MACR,IACA,EACA,EAAc,EAAc,aAmB5B,MACA,SAnBuB,iBAAW,MAClC,eAGA,aACA,MACA,CAEA,iBACA,wBAGA,0BACA,kDACA,cACA,CAAK,GACL,CAAG,CACH,KAGA,cACA,CAAG,EAeH,MAAgC,EAdL,QAce,QAdf,CAAW,SACtC,IAIA,IACA,eACA,cAGA,GACA,aAEA,CAAG,OAEH,EAAkB,EAAc,GAwChC,MAvCE,GAD8B,EAC9B,UAAS,MACX,eAIA,eACA,aACA,qBACA,CAAG,QACD,eAAS,MACX,GACA,yBACA,SACA,KACA,MACA,WACA,OACA,OACA,MACA,CACA,CAAK,EACL,OACA,2BACA,MACA,IACA,CAAK,GAEL,KACE,eAAS,MACX,yBACA,GACA,4BACA,KACA,MACA,UACA,CAAO,EACP,qBAEA,CAAG,YACH,CACA,SACA,OACA,iCACA,OACA,OACA,YACA,CACA,EC3hGkB,CAClB,KACA,OACA,qBACA,sBACA,wBACA,KAEA,CAAG,EACH,CACA,SACA,iBACA,iBACA,aACA,aACA,YACA,aACA,OACA,sBACA,YACA,CAAI,SD00FJ,GC10FkB,GD20FlB,CACA,KACA,OACA,cACA,aACA,CAAI,EACJ,EAAc,EARd,SAQyB,IACzB,CACA,aACA,iBACA,SACA,iBACA,oBACA,iBACA,OACA,CAAM,gBAAU,KAChB,CACA,UACA,8BACA,aACA,CAAI,aACJ,4BACA,EAAoB,gBAAU,UAC9B,MAA6B,IAC7B,MADuC,IAEvC,EA3hCS,IA0hCgD,CA1hChD,QAAO,KAChB,iBACA,IACA,YACA,UACA,CAAQ,EAMR,OAJA,SACA,IAmhCA,EAlhCA,EAEA,CACA,CAAK,GAAI,EACN,CA8gCH,IA9gCG,EA+gCH,EAAkB,EAAc,GA0BhC,OAzBE,EAD8B,KAEhC,SACA,KACA,MACA,OACA,gBACA,MACA,CAAK,EACL,KACA,cAEA,eACA,WAEA,GAEA,OASA,CACA,SACA,iBACA,iBACA,WAZ6B,aAAO,OACpC,OACA,WACA,kBACA,mCACA,yBACA,+BACA,CAAG,0BAMH,aACA,qBACA,OACA,OACA,aACA,sBACA,WACA,CACA,EC54FkB,CAClB,KACA,OACA,YAAkB,MAClB,KACK,CACL,qBACG,EACH,EHtfA,WACA,0CAAsE,IAAa,IACnF,kBAGA,MAAS,aAAO,SAChB,kBACA,CAAG,CACH,EACA,EG6eoC,KACpC,MACA,sBACA,QACA,cAEA,eADA,cACA,CACA,QACA,iBACA,cACA,YACA,OACA,CAAG,OACH,mBACA,KACA,QACA,cACA,WACA,CAAG,IACH,uBACA,GAAmB,YAAM,EACzB,YACA,QACA,YACA,aACA,CAAG,EACH,wBACA,MACA,SACA,cACA,aACA,YACA,KACA,QACA,QACA,6BACA,+BACA,2CACA,aACA,sCACG,EACH,YAtJA,GACA,IACA,WACA,QACA,OACA,OACA,CAAI,EACJ,MAAkD,cAAQ,OAC1D,EAAwB,YAAM,IA+B9B,OA9BE,EAAyB,KAC3B,kBAD2B,EAC3B,aACA,gBAEA,MACA,MAAwB,GAAa,WACrC,kBACA,CAAS,EACT,GACA,gBACA,cACA,uBACA,yBAGA,aACA,IAEA,CACA,CAEA,eACA,aAEA,CAAG,YACD,eAAS,MACX,GACA,OAEA,CAAG,MACH,CACA,EA8GA,CACA,aACA,QACA,OACA,MACA,CAAG,EA6BH,MA5BE,eAAS,MACX,6BACA,yBAGA,4BACA,2BAGA,sBACA,oBAEA,CAAG,aACD,eAAS,MACX,4BACA,OAGA,6BACA,uBACA,MACA,CAEA,sBACA,sBACA,CAAK,KACL,yBACA,CAAG,OACH,CACA,SACA,cACA,aACA,OACA,OACA,QACA,YACA,QACA,SACA,YACA,aACA,YACA,OACA,YACA,OACA,aACA,sBACA,sBACA,sBACA,yBACA,WAIA,IAEA,4BACA,GAGA,KAAqC,EAAe,QAIpD,KAJoD,CAIpD,EACa,EAAG,qBAAuB,KACvC,WACA,CAAO,QAhBP,CAqBA,CAiCoB,EAAY,KAAO,EAAY,MAAQ,EAAY,GAAK,CAA5C,CAAwD,MAArC,QAAoB,KAAiB,EC9oBzE,kBAAqB,CAAW,yBAAiB,kBAAoB,EAAC,CAAC,OAAO,CAAC,EAAI,wCAAyC,KAAM,OAAO,EAAG,EAAC,MAAO,EAAC,EAAI,0CAA0C,IAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAI,yCAA0C,KAAM,CAAO,OAAC,CAAE,EAAC,MAAO,EAAC,EAAI,0CAA0C,CAAM,UAAQ,GAAE,CAAC,OAAO,CAAC,EAAI,CAA2C,8CAAM,OAAO,CAAC,CAAE,EAAC,CAAO,OAAC,CAAI,4CAA2C,IAAM,QAAQ,CAAC,CAAC,sFCUzf,SAASS,GAAc,OAC5B7E,CAAK,IACLiB,CAAE,CAIH,EACC,GAAM,CAAC4B,EAAMiC,EAAQ,CAAGC,EAAAA,QAAc,CAAC/E,GACjC6B,EAAYT,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACO,GAASA,EAAME,SAAS,EACjDW,EAAYpB,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACO,GAASA,EAAMa,SAAS,EACjD,CAACwC,EAAaC,EAAiB,CAAGF,EAAAA,QAAc,EAAC,GACjD,CAACG,EAAkBC,EAAoB,CAAGJ,EAAAA,QAAc,CAAC,IACzDK,EAAWL,EAAAA,MAAY,CAAmB,MAChD,MAAO,iCACH,UAACxB,OAAAA,CAAKC,SAAUC,IAChBA,EAAEE,cAAc,GAChBsB,EAAiB,CAACD,GAClBnD,EAAUZ,EAAI4B,GACdwC,CAAAA,EAAAA,GAAAA,KAAAA,CAAKA,CAAC,GAAGrF,EAAM,YAAY,EAAE6C,EAAAA,CAAM,CACrC,WACI,UAACmB,GAAAA,CAAKA,CAAAA,CAACsB,MAAOzC,EAAM0C,SAAU9B,GAAKqB,EAAQrB,EAAE+B,MAAM,CAACF,KAAK,EAAGpG,UAAU,4FAA4FuG,SAAUT,EAAaU,IAAKN,EAAU3F,sBAAoB,QAAQE,0BAAwB,wBAE9P,WAACgG,GAAAA,EAAYA,CAAAA,CAACC,OAAO,EAAOnG,sBAAoB,eAAeE,0BAAwB,8BACrF,UAACkG,GAAAA,EAAmBA,CAAAA,CAAC1G,OAAO,IAACM,sBAAoB,sBAAsBE,0BAAwB,6BAC7F,WAACsD,GAAAA,CAAMA,CAAAA,CAACtE,QAAQ,YAAYO,UAAU,OAAOO,sBAAoB,SAASE,0BAAwB,8BAChG,UAACmG,OAAAA,CAAK5G,UAAU,mBAAU,YAC1B,UAAC6G,GAAAA,GAAkBA,CAAAA,CAAC7G,UAAU,UAAUO,sBAAoB,qBAAqBE,0BAAwB,2BAG7G,WAACqG,GAAAA,EAAmBA,CAAAA,CAACC,MAAM,MAAMxG,sBAAoB,sBAAsBE,0BAAwB,8BACjG,UAACuG,GAAAA,EAAgBA,CAAAA,CAACC,SAAU,KAC5BlB,EAAiB,CAACD,GAClBoB,WAAW,KACThB,EAASR,OAAO,EAAIQ,EAASR,OAAO,EAAEyB,OACxC,EAAG,IACL,EAAG5G,sBAAoB,mBAAmBE,0BAAwB,6BAAoB,WAGpF,UAAC2G,GAAAA,EAAqBA,CAAAA,CAAC7G,sBAAoB,wBAAwBE,0BAAwB,sBAE3F,UAACuG,GAAAA,EAAgBA,CAAAA,CAACC,SAAU,IAAMhB,GAAoB,GAAOjG,UAAU,eAAeO,sBAAoB,mBAAmBE,0BAAwB,6BAAoB,yBAK7K,UAAC4G,GAAAA,EAAWA,CAAAA,CAACC,KAAMtB,EAAkBuB,aAActB,EAAqB1F,sBAAoB,cAAcE,0BAAwB,6BAChI,WAAC+G,GAAAA,EAAkBA,CAAAA,CAACjH,sBAAoB,qBAAqBE,0BAAwB,8BACnF,WAACgH,GAAAA,EAAiBA,CAAAA,CAAClH,sBAAoB,oBAAoBE,0BAAwB,8BACjF,UAACiH,GAAAA,EAAgBA,CAAAA,CAACnH,sBAAoB,mBAAmBE,0BAAwB,6BAAoB,wCAGrG,UAACkH,GAAAA,EAAsBA,CAAAA,CAACpH,sBAAoB,yBAAyBE,0BAAwB,6BAAoB,sEAInH,WAACmH,GAAAA,EAAiBA,CAAAA,CAACrH,sBAAoB,oBAAoBE,0BAAwB,8BACjF,UAACoH,GAAAA,EAAiBA,CAAAA,CAACtH,sBAAoB,oBAAoBE,0BAAwB,6BAAoB,WACvG,UAACsD,GAAAA,CAAMA,CAAAA,CAACtE,QAAQ,cAAcqI,QAAS,KAEvCZ,WAAW,IAAMa,SAASC,IAAI,CAACC,KAAK,CAACC,aAAa,CAAG,GAAI,KACzDjC,GAAoB,GACpB3C,EAAUvB,GACVoE,CAAAA,EAAAA,GAAAA,KAAAA,CAAKA,CAAC,gCACR,EAAG5F,sBAAoB,SAASE,0BAAwB,6BAAoB,qBAOtF,iBCzDO,SAAS0H,GAAS,MACvB9E,CAAI,WACJ+E,CAAS,CACK,EACd,GAAM,YACJC,CAAU,YACVC,CAAU,WACVC,CAAS,WACTC,CAAS,YACTC,CAAU,YACVC,CAAU,CACX,CAAGC,GAAY,CACd5G,GAAIsB,EAAKtB,EADI4G,CAEblD,KAAM,CACJP,KAAM,YACN7B,CACF,EACAiF,WAAY,CACVM,gBAAiB,MACnB,CACF,GACMX,EAAQ,YACZQ,EACAD,UAAWK,EAAIC,CAADD,QAAU,CAACE,QAAQ,CAACP,EACpC,EACMhJ,EAAWD,CAAAA,EAAAA,GAAAA,CAAAA,CAAGA,CAAC,OAAQ,CAC3BC,SAAU,CACRwJ,SAAU,CACRC,KAAM,oBACNC,QAAS,qBACX,CACF,CACF,GACA,MAAO,WAAC7H,GAAAA,EAAIA,CAAAA,CAACmF,IAAK6B,EAAYJ,MAAOA,EAAOjI,UAAWR,EAAS,CAC9DwJ,SAAUZ,EAAY,UAAYM,EAAa,OAASS,MAC1D,GAAI5I,sBAAoB,OAAOC,wBAAsB,WAAWC,0BAAwB,0BACpF,WAACa,GAAAA,EAAUA,CAAAA,CAACtB,UAAU,6EAA6EO,sBAAoB,aAAaE,0BAAwB,0BAC1J,WAACsD,GAAAA,CAAMA,CAAAA,CAACtE,QAAS,QAAU,GAAG6I,CAAU,CAAG,GAAGC,CAAS,CAAEvI,UAAU,4DAA4DO,sBAAoB,SAASE,0BAAwB,0BAClL,UAACmG,OAAAA,CAAK5G,UAAU,mBAAU,cAC1B,UAACoJ,GAAgBA,CAAC7I,YAAD6I,UAAqB,mBAAmB3I,0BAAwB,qBAEnF,UAACV,GAAAA,CAAKA,CAAAA,CAACN,QAAS,UAAWO,UAAU,wBAAwBO,sBAAoB,QAAQE,0BAAwB,yBAAgB,YAInI,UAACiB,GAAAA,EAAWA,CAAAA,CAAC1B,UAAU,+CAA+CO,sBAAoB,cAAcE,0BAAwB,yBAC7H4C,EAAKvC,KAAK,KAGnB,iBC/CO,SAASuI,GAAY,QAC1BC,CAAM,OACNlH,CAAK,WACLgG,CAAS,CACQ,EACjB,IAAMmB,EAAWC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAChBpH,EAAMS,GAAG,CAACQ,GAAQA,EAAKtB,EAAE,EAC/B,CAACK,EAAM,EACJ,CACJiG,YAAU,YACVC,CAAU,WACVC,CAAS,WACTC,CAAS,YACTC,CAAU,YACVC,CAAU,CACX,CAAGC,GAAY,CACd5G,GAAIuH,EAAOvH,EADE4G,CAEblD,KAAM,CACJP,KAAM,gBACNoE,CACF,EACAhB,WAAY,CACVM,gBAAiB,CAAC,QAAQ,EAAEU,EAAOxI,KAAK,EAAE,CAE9C,GACMmH,EAAQ,YACZQ,EACAD,UAAWK,EAAIC,CAADD,QAAU,CAACE,QAAQ,CAACP,EACpC,EACMhJ,EAAWD,CAAAA,EAAAA,GAAAA,CAAAA,CAAGA,CAAC,6FAA8F,CACjHC,SAAU,CACRwJ,SAAU,CACRtJ,QAAS,8BACTuJ,KAAM,oBACNC,QAAS,qBACX,CACF,CACF,GACA,MAAO,WAAC7H,GAAAA,EAAIA,CAAAA,CAACmF,IAAK6B,EAAYJ,MAAOA,EAAOjI,UAAWR,EAAS,CAC9DwJ,SAAUZ,EAAY,UAAYM,EAAa,YAASS,CAC1D,GAAI5I,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,6BACvF,WAACa,GAAAA,EAAUA,CAAAA,CAACtB,UAAU,kFAAkFO,sBAAoB,aAAaE,0BAAwB,6BAC/J,WAACsD,GAAAA,CAAMA,CAAAA,CAACtE,QAAS,QAAU,GAAG6I,CAAU,CAAG,GAAGC,CAAS,CAAEvI,UAAU,wDAAwDO,sBAAoB,SAASE,0BAAwB,6BAC9K,UAACmG,OAAAA,CAAK5G,UAAU,mBAAW,CAAC,aAAa,EAAEsJ,EAAOxI,KAAK,EAAE,GACzD,UAACsI,GAAgBA,CAAC7I,YAAD6I,UAAqB,mBAAmB3I,0BAAwB,wBAOnF,UAACkF,GAAaA,CAAC5D,GAAIuH,EAAOvH,EAAE,CAAEjB,CAAhB6E,KAAuB2D,EAAOxI,KAAK,CAAEP,sBAAoB,gBAAgBE,0BAAwB,wBAEjH,UAACiB,GAAAA,EAAWA,CAAAA,CAAC1B,UAAU,iDAAiDO,sBAAoB,cAAcE,0BAAwB,4BAChI,UAAC6E,GAAAA,UAAUA,CAAAA,CAACtF,UAAU,SAASO,sBAAoB,aAAaE,0BAAwB,4BACtF,UAACgJ,GAAeA,CAACC,MAAOH,EAAUhJ,GAAlBkJ,mBAAsC,kBAAkBhJ,0BAAwB,4BAC7F2B,EAAMS,GAAG,CAACQ,GAAQ,UAAC8E,GAAQA,CAAe9E,IAAf8E,CAAqB9E,GAAfA,EAAKtB,EAAE,WAKrD,CACO,SAAS4H,GAAe,UAC7BvE,CAAQ,CAGT,EACC,IAAMwE,EAAaC,KACbC,EAAavK,CAAAA,EAAAA,GADasK,CACbtK,CAAGA,CAAC,2CAA4C,CACjEC,SAAU,CACRwJ,SAAU,CACRtJ,QAAS,GACTqK,OAAQ,WACV,CACF,CACF,GACA,MAAO,WAACzE,GAAAA,UAAUA,CAAAA,CAACtF,UAAU,sCAAsCO,sBAAoB,aAAaC,wBAAsB,iBAAiBC,0BAAwB,6BAC/J,UAACG,MAAAA,CAAIZ,UAAW8J,EAAW,CAC3Bd,SAAUY,EAAWG,MAAM,CAAG,SAAW,SAC3C,YACI,UAACnJ,MAAAA,CAAIZ,UAAU,0DACZoF,MAGL,UAAC4E,GAAAA,CAASA,CAAAA,CAACC,YAAY,aAAa1J,sBAAoB,YAAYE,0BAAwB,uBAElG,iBC1Ge,SAASyJ,KACtB,IAAMnH,EAASb,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACO,GAASA,EAAMM,MAAM,EAWjD,MAAO,WAACc,GAAAA,EAAMA,CAAAA,CAACtD,sBAAoB,SAASC,wBAAsB,mBAAmBC,0BAAwB,mCACzG,UAACqD,GAAAA,EAAaA,CAAAA,CAAC7D,OAAO,IAACM,sBAAoB,gBAAgBE,0BAAwB,kCACjF,UAACsD,GAAAA,CAAMA,CAAAA,CAACtE,QAAQ,YAAYuE,KAAK,KAAKhE,UAAU,SAASO,sBAAoB,SAASE,0BAAwB,kCAAyB,wBAIzI,WAACwD,GAAAA,EAAaA,CAAAA,CAACjE,UAAU,mBAAmBO,sBAAoB,gBAAgBE,0BAAwB,mCACtG,WAACyD,GAAAA,EAAYA,CAAAA,CAAC3D,sBAAoB,eAAeE,0BAAwB,mCACvE,UAAC0D,GAAAA,EAAWA,CAAAA,CAAC5D,sBAAoB,cAAcE,0BAAwB,kCAAyB,oBAChG,UAAC2D,GAAAA,EAAiBA,CAAAA,CAAC7D,sBAAoB,oBAAoBE,0BAAwB,kCAAyB,2CAI9G,UAAC4D,OAAAA,CAAKtC,GAAG,YAAY/B,UAAU,kBAAkBsE,SAvBlC,CAuB4CE,GAtB/DD,EAAEE,cAAc,GAGhB,GAAM,OACJ3D,CAAK,CACN,CAAG4D,OAAOC,WAAW,CAACwF,IAHFvF,SAASP,EADfQ,aAAa,GAKP,UAAjB,OAAO/D,GACXiC,EAAOjC,EACT,WAeQ,UAACF,MAAAA,CAAIZ,UAAU,+CACb,UAAC8E,GAAAA,CAAKA,CAAAA,CAAC/C,GAAG,QAAQ4B,KAAK,QAAQoB,YAAY,mBAAmB/E,UAAU,aAAaO,sBAAoB,QAAQE,0BAAwB,+BAG7I,UAACwE,GAAAA,EAAYA,CAAAA,CAAC1E,sBAAoB,eAAeE,0BAAwB,kCACvE,UAACqD,GAAAA,EAAaA,CAAAA,CAAC7D,OAAO,IAACM,sBAAoB,gBAAgBE,0BAAwB,kCACjF,UAACsD,GAAAA,CAAMA,CAAAA,CAACmB,KAAK,SAASlB,KAAK,KAAKK,KAAK,YAAY9D,sBAAoB,SAASE,0BAAwB,kCAAyB,yBAO3I,CCpBO,SAASQ,KAEd,IAAMoB,EAAUH,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACO,GAASA,EAAMJ,OAAO,EAC7C+H,EAAalI,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACO,GAASA,EAAMgB,OAAO,EAChD4G,EAAqBC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAA6C,QACxEC,EAAYf,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAMnH,EAAQQ,GAAG,CAACC,GAAOA,EAAIf,EAAE,EAAG,CAACM,EAAQ,EAG/DD,EAAQF,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACO,GAASA,EAAML,KAAK,EACzCmB,EAAWrB,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAACO,GAASA,EAAMc,QAAQ,EAC/C,CAACiH,EAAcC,EAAgB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC1D,CAACC,EAAWC,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAU,GAC9C,CAACG,EAAYC,EAAc,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MACpDK,EP+JR,UO/J4BC,CPgK5B,0CAAyE,IAAa,IACtF,kBAGA,MAAS,aAAO,+BAChB,OACA,EOtK6BC,EAAUC,IAAcD,EAAUE,CAAzBF,IAAYC,EAAYD,CAWvDN,CAAD,CAkFJ,CA7FwEQ,KA6FjE,GAlFS,EAkFT,MAACC,GAAUA,CAACC,MAADD,QAAgB,CAChCE,cAxEmC,CACnCC,YAAY,QACVxB,CAAM,CACP,EACC,GAAKxE,CAAD,CAAkBwE,IACtB,GAAIA,EAD2B,IAChB,CADMxE,OACE,EAAEL,OAAS,SAAU,CAC1C,IAAMsG,EAAiBjB,EAAUkB,SAAS,CAAC1J,GAAMA,IAAOgI,EAAOhI,EAAE,EAC3D2J,EAAcrJ,CAAO,CAACmJ,EAAe,CAC3C,MAAO,CAAC,iBAAiB,EAAEE,GAAa5K,MAAM,cAAc,EAAE0K,EAAiB,EAAE,IAAI,EAAEjB,EAAUvH,MAAM,EAAE,MACpG,GAAI+G,EAAOtE,IAAI,CAACC,OAAO,EAAER,OAAS,OAAQ,CAC/CmF,EAAmB3E,OAAO,CAAGqE,EAAOtE,IAAI,CAACC,OAAO,CAACrC,IAAI,CAACpB,MAAM,CAC5D,GAAM,eACJ0J,CAAa,CACbC,cAAY,QACZtC,CAAM,CACP,CAAGuC,EAAoB9B,EAAOhI,EAAE,CAAEsI,EAAmB3E,OAAO,EAC7D,MAAO,CAAC,eAAe,EAAEqE,EAAOtE,IAAI,CAACC,OAAO,CAACrC,IAAI,CAACvC,KAAK,CAAC,cAAc,EAAE8K,EAAe,EAAE,IAAI,EAAED,EAAc3I,MAAM,CAAC,WAAW,EAAEsG,GAAQxI,MAAAA,CAAO,CAClJ,CACF,EACAgL,WAAW,QACT/B,CAAM,MACNd,CAAI,CACL,EACC,GAAI,EAAkBc,IAAYxE,EAAiB0D,IACnD,CADiC,EAAyB,CAArC1D,CACVE,IAAI,CADmCF,OAC3B,EAAEL,OAAS,UAAY+D,EAAKxD,IAAI,CAACC,OAAO,EAAER,OAAS,SAAU,CAClF,IAAM6G,EAAgBxB,EAAUkB,SAAS,CAAC1J,GAAMA,IAAOkH,EAAKlH,EAAE,EAC9D,MAAO,CAAC,OAAO,EAAEgI,EAAOtE,IAAI,CAACC,OAAO,CAAC4D,MAAM,CAACxI,KAAK,CAAC,gBAAgB,EAAEmI,EAAKxD,IAAI,CAACC,OAAO,CAAC4D,MAAM,CAACxI,KAAK,CAAC,aAAa,EAAEiL,EAAgB,EAAE,IAAI,EAAExB,EAAUvH,MAAM,EAAE,MACvJ,GAAI+G,EAAOtE,IAAI,CAACC,OAAO,EAAER,OAAS,QAAU+D,EAAKxD,IAAI,CAACC,OAAO,EAAER,OAAS,OAAQ,CACrF,GAAM,eACJyG,CAAa,cACbC,CAAY,QACZtC,CAAM,CACP,CAAGuC,EAAoB5C,EAAKlH,EAAE,CAAEkH,EAAKxD,IAAI,CAACC,OAAO,CAACrC,IAAI,CAACpB,MAAM,SAC9D,EAASwD,IAAI,CAACC,OAAO,CAACrC,IAAI,CAACpB,MAAM,GAAKoI,EAAmB3E,OAAO,CACvD,CADyD,KACnD,EAAEqE,EAAOtE,IAAI,CAACC,OAAO,CAACrC,IAAI,CAACvC,KAAK,CAAC,uBAAuB,EAAEwI,GAAQxI,MAAM,aAAa,EAAE8K,EAAe,EAAE,IAAI,EAAED,EAAc3I,MAAM,EAAE,CAE5I,CAAC,6BAA6B,EAAE4I,EAAe,EAAE,IAAI,EAAED,EAAc3I,MAAM,CAAC,WAAW,EAAEsG,GAAQxI,MAAAA,CAAO,CACjH,CACF,EACAkL,UAAU,QACRjC,CAAM,MACNd,CAAI,CACL,EACC,GAAI,CAAC1D,EAAiBwE,IAAW,CAACxE,EAAiB0D,GAAO,CACxDoB,EAAmB3E,CADAH,MACO,CADsBA,OAEhD,MACF,CACA,GAAIwE,EAAOtE,IAAI,CAACC,OAAO,EAAER,OAAS,UAAY+D,EAAKxD,IAAI,CAACC,OAAO,EAAER,OAAS,SAAU,CAClF,IAAM+G,EAAqB1B,EAAUkB,SAAS,CAAC1J,GAAMA,IAAOkH,EAAKlH,EAAE,EACnE,MAAO,CAAC,OAAO,EAAEgI,EAAOtE,IAAI,CAACC,OAAO,CAAC4D,MAAM,CAACxI,KAAK,CAAC,2BAA2B,EAAEmL,EAAqB,EAAE,IAAI,EAAE1B,EAAUvH,MAAM,EAAE,CACzH,GAAI+G,EAAOtE,IAAI,CAACC,OAAO,EAAER,OAAS,QAAU+D,EAAKxD,IAAI,CAACC,OAAO,EAAER,OAAS,OAAQ,CACrF,GAAM,eACJyG,CAAa,cACbC,CAAY,QACZtC,CAAM,CACP,CAAGuC,EAAoB5C,EAAKlH,EAAE,CAAEkH,EAAKxD,IAAI,CAACC,OAAO,CAACrC,IAAI,CAACpB,MAAM,SAC9D,EAASwD,IAAI,CAACC,OAAO,CAACrC,IAAI,CAACpB,MAAM,GAAKoI,EAAmB3E,OAAO,CACvD,CADyD,6BAC3B,EAAE4D,GAAQxI,MAAM,aAAa,EAAE8K,EAAe,EAAE,IAAI,EAAED,EAAc3I,MAAM,EAAE,CAE5G,CAAC,+BAA+B,EAAE4I,EAAe,EAAE,IAAI,EAAED,EAAc3I,MAAM,CAAC,WAAW,EAAEsG,GAAQxI,MAAAA,CAC5G,CACAuJ,EAAmB3E,OAAO,CAAG,MAC/B,EACAwG,aAAa,QACXnC,CAAM,CACP,EAEC,GADAM,CACI,CADe3E,OAAO,CAAG,OACxBH,EAAiBwE,GACtB,MAAO,CAAC,IADaxE,KACJ,EAAEwE,EAAOtE,IAAI,CAACC,OAAO,EAAER,KAAK,WAAW,CAAC,CAE7D,CAGA,EAAG6F,QAASA,EAASQ,YAkBrB,CAlBkCA,QAkBbY,CAAqB,EACxC,GAAI,CAAC5G,EAAiB4G,EAAMpC,MAAM,EAAG,IAAhBxE,GACrB,IAAME,EAAO0G,EAAMpC,MAAM,CAACtE,IAAI,CAACC,OAAO,QACtC,GAAUR,OAAS,UAAU,IAC3BuF,EAAgBhF,EAAK6D,MAAM,EAGzB7D,GAAMP,OAAS,QAAQ,IACzB4F,EAAcrF,EAAKpC,IAAI,QAG3B,EA7B+C2I,UA8B/C,CA9B0DA,QA8BjDA,CAA6B,EACpCvB,EAAgB,MAChBK,EAAc,MACd,GAAM,CACJf,QAAM,MACNd,CAAI,CACL,CAAGkD,EACJ,GAAI,CAAClD,EAAM,OACX,IAAMmD,EAAWrC,EAAOhI,EAAE,CACpBsK,EAASpD,EAAKlH,EAAE,CACtB,GAAI,CAACwD,EAAiBwE,GAAS,OAC/B,IADqBxE,EACFwE,EAAOtE,IAAI,CAACC,OAAO,CACtC,GAAI0G,IAAaC,GACOC,GAAYpH,OAAS,SADpB,OAGzB,IAAMqH,EAAoBlK,EAAQoJ,SAAS,CAAC3I,GAAOA,EAAIf,EAAE,GAAKqK,GACxDI,EAAkBnK,EAAQoJ,SAAS,CAAC3I,GAAOA,EAAIf,EAAE,GAAKsK,GAC5DjC,EAAWqC,GAAUpK,EAASkK,EAAmBC,EAA7BC,CACtB,EAhDqEX,WAiDrE,CAjDiFA,QAiDxEA,CAA+B,EACtC,GAAM,QACJ/B,CAAM,MACNd,CAAI,CACL,CAAGkD,EACJ,GAAI,CAAClD,EAAM,OACX,IAAMmD,EAAWrC,EAAOhI,EAAE,CACpBsK,EAASpD,EAAKlH,EAAE,CACtB,GAAIqK,IAAaC,GACb,CAAC9G,EAAiBwE,IAAW,CAACxE,EAAiB0D,GAD1B,IACJ1D,GACrB,IADkDA,EAC/BwE,EAAOtE,IAAI,CAACC,OAAO,CAChCgH,EAAWzD,EAAKxD,IAAI,CAACC,OAAO,CAC5BiH,EAAgBL,GAAYpH,OAAS,OACrC0H,EAAcN,GAAYpH,OAAS,OACzC,GAAI,CAACyH,EAAe,OAGpB,GAAIA,GAAiBC,EAAa,CAChC,IAAMC,EAAczK,EAAMqJ,SAAS,CAACqB,GAAKA,EAAE/K,EAAE,GAAKqK,GAC5CW,EAAY3K,EAAMqJ,SAAS,CAACqB,GAAKA,EAAE/K,EAAE,GAAKsK,GAC1CxB,EAAazI,CAAK,CAACyK,EAAY,CAC/BG,EAAW5K,CAAK,CAAC2K,EAAU,CAC7BlC,GAAcmC,GAAYnC,EAAW5I,MAAM,GAAK+K,EAAS/K,MAAM,EAAE,CACnE4I,EAAW5I,MAAM,CAAG+K,EAAS/K,MAAM,CACnCsB,EAASkJ,GAAUrK,EAAOyK,EAAaE,EAArBN,KAEpBlJ,EAASkJ,GAAUrK,EAAOyK,EAAaE,EAArBN,CACpB,CACA,IAAMQ,EAAgBP,GAAUxH,OAAS,SAGzC,GAAIyH,GAAiBM,EAAe,CAClC,IAAMJ,EAAczK,EAAMqJ,SAAS,CAACqB,GAAKA,EAAE/K,EAAE,GAAKqK,GAC5CvB,EAAazI,CAAK,CAACyK,EAAY,CACjChC,IACFA,EAAW5I,MADG,CACMoK,EACpB9I,EAASkJ,GAAUrK,EAAOyK,EAAaA,EAArBJ,EAEtB,CACF,EAxF6FlM,sBAAoB,aAAaC,wBAAsB,cAAcC,0BAAwB,6BACtL,UAACkJ,GAAcA,CAACpJ,UAADoJ,YAAqB,iBAAiBlJ,0BAAwB,4BAC3E,WAACgJ,GAAeA,CAACC,MAAOa,EAAWhK,GAAnBkJ,mBAAuC,kBAAkBhJ,0BAAwB,6BAC9F4B,GAASQ,IAAI,CAACC,EAAKoK,IAAU,WAACC,EAAAA,QAAQA,CAAAA,WACnC,UAAC9D,GAAWA,CAACC,OAADD,EAAcjH,MAAOA,EAAMgB,MAAM,CAACC,GAAQA,EAAKpB,MAAM,GAAKa,EAAIf,EAAE,IAC3EmL,IAAU7K,GAASW,OAAS,GAAK,UAACpC,MAAAA,CAAIZ,UAAU,qBAC7C,UAACkK,GAAgBA,CAAAA,OAHoBpH,EAAIf,EAAE,CAG1BmI,EAGxB,CAAC7H,EAAQW,MAAM,EAAI,UAACkH,GAAgBA,CAAAA,QAIxC,IAJwCA,SAI1BkD,QAAUC,CAAAA,EAAAA,EAAAA,YAAAA,CAAYA,CAAC,WAACC,GAAWA,QAAAA,GAC3C9C,GAAgB,UAACnB,GAAWA,CAACjB,OAADiB,EAAU,IAACC,OAAQkB,EAAcpI,MAAOA,EAAMgB,MAAM,CAACC,GAAQA,EAAKpB,MAAM,GAAKuI,EAAazI,EAAE,IACxH8I,GAAc,UAAC1C,GAAQA,CAAC9E,IAAD8E,CAAO0C,EAAYzC,SAAS,SACtCL,SAASC,IAAI,KAnGrC,SAAS6D,EAAoB0B,CAAwB,CAAEC,CAAkB,EACvE,IAAM7B,EAAgBvJ,EAAMgB,MAAM,CAACC,GAAQA,EAAKpB,MAAM,GAAKuL,GACrD5B,EAAeD,EAAcF,SAAS,CAACpI,GAAQA,EAAKtB,EAAE,GAAKwL,GAEjE,MAAO,eACL5B,eACAC,EACAtC,OAJajH,EAAQoL,IAAI,CAAC3K,GAAOA,EAAIf,EAAE,GAAKyL,EAK9C,CACF,CAmKF,0BC9NA", "sources": ["webpack://next-shadcn-dashboard-starter/?e3b8", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/badge.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/?3183", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/./src/features/kanban/components/kanban-view-page.tsx", "webpack://next-shadcn-dashboard-starter/src/app/dashboard/kanban/page.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/card.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/heading.tsx", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/zustand@5.0.3_@types+react@_d5f34d148df5a7fb99324bc71cf56f16/node_modules/zustand/esm/vanilla.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/zustand@5.0.3_@types+react@_d5f34d148df5a7fb99324bc71cf56f16/node_modules/zustand/esm/react.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm/native.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm/rng.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm/stringify.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm/v4.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/zustand@5.0.3_@types+react@_d5f34d148df5a7fb99324bc71cf56f16/node_modules/zustand/esm/middleware.mjs", "webpack://next-shadcn-dashboard-starter/./src/features/kanban/utils/store.ts", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/?a6a0", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/./src/features/kanban/components/new-task-dialog.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/textarea.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/layout/page-container.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/./src/features/kanban/utils/index.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@dnd-kit+utilities@3.2.2_react@19.0.0/node_modules/@dnd-kit/utilities/dist/utilities.esm.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@dnd-kit+accessibility@3.1.1_react@19.0.0/node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@dnd-kit+core@6.3.1_react-d_c04787ae0a17bdff9b2a5e54cbe2af11/node_modules/@dnd-kit/core/dist/core.esm.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@dnd-kit+sortable@8.0.0_@dn_c50eb90f744d388756e14ec90eb94481/node_modules/@dnd-kit/sortable/dist/sortable.esm.js", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconGripVertical.ts", "webpack://next-shadcn-dashboard-starter/./src/features/kanban/components/column-action.tsx", "webpack://next-shadcn-dashboard-starter/./src/features/kanban/components/task-card.tsx", "webpack://next-shadcn-dashboard-starter/./src/features/kanban/components/board-column.tsx", "webpack://next-shadcn-dashboard-starter/./src/features/kanban/components/new-section-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/features/kanban/components/kanban-board.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["import(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"KanbanBoard\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\kanban\\\\components\\\\kanban-board.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\kanban\\\\components\\\\new-task-dialog.tsx\");\n", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"KanbanBoard\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\kanban\\\\components\\\\kanban-board.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\kanban\\\\components\\\\new-task-dialog.tsx\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "import PageContainer from '@/components/layout/page-container';\nimport { Heading } from '@/components/ui/heading';\nimport { KanbanBoard } from './kanban-board';\nimport NewTaskDialog from './new-task-dialog';\nexport default function KanbanViewPage() {\n  return <PageContainer data-sentry-element=\"PageContainer\" data-sentry-component=\"KanbanViewPage\" data-sentry-source-file=\"kanban-view-page.tsx\">\r\n      <div className='space-y-4'>\r\n        <div className='flex items-start justify-between'>\r\n          <Heading title={`Kanban`} description='Manage tasks by dnd' data-sentry-element=\"Heading\" data-sentry-source-file=\"kanban-view-page.tsx\" />\r\n          <NewTaskDialog data-sentry-element=\"NewTaskDialog\" data-sentry-source-file=\"kanban-view-page.tsx\" />\r\n        </div>\r\n        <KanbanBoard data-sentry-element=\"KanbanBoard\" data-sentry-source-file=\"kanban-view-page.tsx\" />\r\n      </div>\r\n    </PageContainer>;\n}", "import KanbanViewPage from '@/features/kanban/components/kanban-view-page';\nexport const metadata = {\n  title: 'Dashboard : Kanban view'\n};\nexport default function page() {\n  return <KanbanViewPage data-sentry-element=\"KanbanViewPage\" data-sentry-component=\"page\" data-sentry-source-file=\"page.tsx\" />;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/kanban',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/kanban',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/kanban',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/kanban',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "interface HeadingProps {\n  title: string;\n  description: string;\n}\nexport const Heading: React.FC<HeadingProps> = ({\n  title,\n  description\n}) => {\n  return <div data-sentry-component=\"Heading\" data-sentry-source-file=\"heading.tsx\">\r\n      <h2 className='text-3xl font-bold tracking-tight'>{title}</h2>\r\n      <p className='text-muted-foreground text-sm'>{description}</p>\r\n    </div>;\n};", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n", "import React from 'react';\nimport { createStore } from 'zustand/vanilla';\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = React.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = createStore(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\nexport { create, useStore };\n", "import { randomUUID } from 'crypto';\nexport default { randomUUID };\n", "import { randomFillSync } from 'crypto';\nconst rnds8Pool = new Uint8Array(256);\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n    if (poolPtr > rnds8Pool.length - 16) {\n        randomFillSync(rnds8Pool);\n        poolPtr = 0;\n    }\n    return rnds8Pool.slice(poolPtr, (poolPtr += 16));\n}\n", "import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n", "const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === undefined) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === undefined ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === undefined) {\n      connection == null ? undefined : connection.send(action, get());\n      return r;\n    }\n    connection == null ? undefined : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? undefined : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? undefined : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if ((import.meta.env ? import.meta.env.MODE : undefined) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === undefined) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === undefined || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === undefined) {\n              return connection == null ? undefined : connection.init(api.getState());\n            }\n            return connection == null ? undefined : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === undefined) {\n              connection == null ? undefined : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? undefined : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === undefined) {\n                setStateFromDevtools(state);\n                connection == null ? undefined : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? undefined : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === undefined) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? undefined : _a.state;\n            if (!lastComputedState) return;\n            if (store === undefined) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? undefined : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== undefined) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? undefined : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? undefined : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? undefined : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? undefined : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? undefined : _b.call(options, (_a = get()) != null ? _a : configResult)) || undefined;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            const migration = options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n            if (migration instanceof Promise) {\n              return migration.then((result) => [true, result]);\n            }\n            return [true, migration];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, undefined];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? undefined : postRehydrationCallback(stateFromStorage, undefined);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? undefined : postRehydrationCallback(undefined, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? undefined : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n", "import { create } from 'zustand';\r\nimport { v4 as uuid } from 'uuid';\r\nimport { persist } from 'zustand/middleware';\r\nimport { UniqueIdentifier } from '@dnd-kit/core';\r\nimport { Column } from '../components/board-column';\r\n\r\nexport type Status = 'TODO' | 'IN_PROGRESS' | 'DONE';\r\n\r\nconst defaultCols = [\r\n  {\r\n    id: 'TODO' as const,\r\n    title: 'Todo'\r\n  }\r\n] satisfies Column[];\r\n\r\nexport type ColumnId = (typeof defaultCols)[number]['id'];\r\n\r\nexport type Task = {\r\n  id: string;\r\n  title: string;\r\n  description?: string;\r\n  status: Status;\r\n};\r\n\r\nexport type State = {\r\n  tasks: Task[];\r\n  columns: Column[];\r\n  draggedTask: string | null;\r\n};\r\n\r\nconst initialTasks: Task[] = [\r\n  {\r\n    id: 'task1',\r\n    status: 'TODO',\r\n    title: 'Project initiation and planning'\r\n  },\r\n  {\r\n    id: 'task2',\r\n    status: 'TODO',\r\n    title: 'Gather requirements from stakeholders'\r\n  }\r\n];\r\n\r\nexport type Actions = {\r\n  addTask: (title: string, description?: string) => void;\r\n  addCol: (title: string) => void;\r\n  dragTask: (id: string | null) => void;\r\n  removeTask: (title: string) => void;\r\n  removeCol: (id: UniqueIdentifier) => void;\r\n  setTasks: (updatedTask: Task[]) => void;\r\n  setCols: (cols: Column[]) => void;\r\n  updateCol: (id: UniqueIdentifier, newName: string) => void;\r\n};\r\n\r\nexport const useTaskStore = create<State & Actions>()(\r\n  persist(\r\n    (set) => ({\r\n      tasks: initialTasks,\r\n      columns: defaultCols,\r\n      draggedTask: null,\r\n      addTask: (title: string, description?: string) =>\r\n        set((state) => ({\r\n          tasks: [\r\n            ...state.tasks,\r\n            { id: uuid(), title, description, status: 'TODO' }\r\n          ]\r\n        })),\r\n      updateCol: (id: UniqueIdentifier, newName: string) =>\r\n        set((state) => ({\r\n          columns: state.columns.map((col) =>\r\n            col.id === id ? { ...col, title: newName } : col\r\n          )\r\n        })),\r\n      addCol: (title: string) =>\r\n        set((state) => ({\r\n          columns: [\r\n            ...state.columns,\r\n            { title, id: state.columns.length ? title.toUpperCase() : 'TODO' }\r\n          ]\r\n        })),\r\n      dragTask: (id: string | null) => set({ draggedTask: id }),\r\n      removeTask: (id: string) =>\r\n        set((state) => ({\r\n          tasks: state.tasks.filter((task) => task.id !== id)\r\n        })),\r\n      removeCol: (id: UniqueIdentifier) =>\r\n        set((state) => ({\r\n          columns: state.columns.filter((col) => col.id !== id)\r\n        })),\r\n      setTasks: (newTasks: Task[]) => set({ tasks: newTasks }),\r\n      setCols: (newCols: Column[]) => set({ columns: newCols })\r\n    }),\r\n    { name: 'task-store', skipHydration: true }\r\n  )\r\n);\r\n", "module.exports = require(\"node:os\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\kanban\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'kanban',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\kanban\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\kanban\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/kanban/page\",\n        pathname: \"/dashboard/kanban\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"node:diagnostics_channel\");", "'use client';\n\nimport { Button } from '@/components/ui/button';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { useTaskStore } from '../utils/store';\nexport default function NewTaskDialog() {\n  const addTask = useTaskStore(state => state.addTask);\n  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const form = e.currentTarget;\n    const formData = new FormData(form);\n    const {\n      title,\n      description\n    } = Object.fromEntries(formData);\n    if (typeof title !== 'string' || typeof description !== 'string') return;\n    addTask(title, description);\n  };\n  return <Dialog data-sentry-element=\"Dialog\" data-sentry-component=\"NewTaskDialog\" data-sentry-source-file=\"new-task-dialog.tsx\">\r\n      <DialogTrigger asChild data-sentry-element=\"DialogTrigger\" data-sentry-source-file=\"new-task-dialog.tsx\">\r\n        <Button variant='secondary' size='sm' data-sentry-element=\"Button\" data-sentry-source-file=\"new-task-dialog.tsx\">\r\n          ＋ Add New Todo\r\n        </Button>\r\n      </DialogTrigger>\r\n      <DialogContent className='sm:max-w-[425px]' data-sentry-element=\"DialogContent\" data-sentry-source-file=\"new-task-dialog.tsx\">\r\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"new-task-dialog.tsx\">\r\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"new-task-dialog.tsx\">Add New Todo</DialogTitle>\r\n          <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"new-task-dialog.tsx\">\r\n            What do you want to get done today?\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <form id='todo-form' className='grid gap-4 py-4' onSubmit={handleSubmit}>\r\n          <div className='grid grid-cols-4 items-center gap-4'>\r\n            <Input id='title' name='title' placeholder='Todo title...' className='col-span-4' data-sentry-element=\"Input\" data-sentry-source-file=\"new-task-dialog.tsx\" />\r\n          </div>\r\n          <div className='grid grid-cols-4 items-center gap-4'>\r\n            <Textarea id='description' name='description' placeholder='Description...' className='col-span-4' data-sentry-element=\"Textarea\" data-sentry-source-file=\"new-task-dialog.tsx\" />\r\n          </div>\r\n        </form>\r\n        <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"new-task-dialog.tsx\">\r\n          <DialogTrigger asChild data-sentry-element=\"DialogTrigger\" data-sentry-source-file=\"new-task-dialog.tsx\">\r\n            <Button type='submit' size='sm' form='todo-form' data-sentry-element=\"Button\" data-sentry-source-file=\"new-task-dialog.tsx\">\r\n              Add Todo\r\n            </Button>\r\n          </DialogTrigger>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>;\n}", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Textarea({\n  className,\n  ...props\n}: React.ComponentProps<'textarea'>) {\n  return <textarea data-slot='textarea' className={cn('border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className)} {...props} data-sentry-component=\"Textarea\" data-sentry-source-file=\"textarea.tsx\" />;\n}\nexport { Textarea };", "import React from 'react';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nexport default function PageContainer({\n  children,\n  scrollable = true\n}: {\n  children: React.ReactNode;\n  scrollable?: boolean;\n}) {\n  return <>\r\n      {scrollable ? <ScrollArea className='h-[calc(100dvh-52px)]'>\r\n          <div className='flex flex-1 p-4 md:px-6'>{children}</div>\r\n        </ScrollArea> : <div className='flex flex-1 p-4 md:px-6'>{children}</div>}\r\n    </>;\n}", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "import { Active, DataRef, Over } from '@dnd-kit/core';\r\nimport { ColumnDragData } from '../components/board-column';\r\nimport { TaskDragData } from '../components/task-card';\r\n\r\ntype DraggableData = ColumnDragData | TaskDragData;\r\n\r\nexport function hasDraggableData<T extends Active | Over>(\r\n  entry: T | null | undefined\r\n): entry is T & {\r\n  data: DataRef<DraggableData>;\r\n} {\r\n  if (!entry) {\r\n    return false;\r\n  }\r\n\r\n  const data = entry.data.current;\r\n\r\n  if (data?.type === 'Column' || data?.type === 'Task') {\r\n    return true;\r\n  }\r\n\r\n  return false;\r\n}\r\n", "import { useMemo, useLayoutEffect, useEffect, useRef, useCallback } from 'react';\n\nfunction useCombinedRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n\n  return useMemo(() => node => {\n    refs.forEach(ref => ref(node));\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  refs);\n}\n\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === '[object Window]' || // In Electron context the Window object serializes to [object global]\n  elementString === '[object global]';\n}\n\nfunction isNode(node) {\n  return 'nodeType' in node;\n}\n\nfunction getWindow(target) {\n  var _target$ownerDocument, _target$ownerDocument2;\n\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return (_target$ownerDocument = (_target$ownerDocument2 = target.ownerDocument) == null ? void 0 : _target$ownerDocument2.defaultView) != null ? _target$ownerDocument : window;\n}\n\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document;\n}\n\nfunction isHTMLElement(node) {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement;\n}\n\nfunction getOwnerDocument(target) {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n\n/**\r\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\r\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\r\n */\n\nconst useIsomorphicLayoutEffect = canUseDOM ? useLayoutEffect : useEffect;\n\nfunction useEvent(handler) {\n  const handlerRef = useRef(handler);\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n  return useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return handlerRef.current == null ? void 0 : handlerRef.current(...args);\n  }, []);\n}\n\nfunction useInterval() {\n  const intervalRef = useRef(null);\n  const set = useCallback((listener, duration) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n  return [set, clear];\n}\n\nfunction useLatestValue(value, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [value];\n  }\n\n  const valueRef = useRef(value);\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n  return valueRef;\n}\n\nfunction useLazyMemo(callback, dependencies) {\n  const valueRef = useRef();\n  return useMemo(() => {\n    const newValue = callback(valueRef.current);\n    valueRef.current = newValue;\n    return newValue;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...dependencies]);\n}\n\nfunction useNodeRef(onChange) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef(null);\n  const setNodeRef = useCallback(element => {\n    if (element !== node.current) {\n      onChangeHandler == null ? void 0 : onChangeHandler(element, node.current);\n    }\n\n    node.current = element;\n  }, //eslint-disable-next-line\n  []);\n  return [node, setNodeRef];\n}\n\nfunction usePrevious(value) {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\n\nlet ids = {};\nfunction useUniqueId(prefix, value) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n    return prefix + \"-\" + id;\n  }, [prefix, value]);\n}\n\nfunction createAdjustmentFn(modifier) {\n  return function (object) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((accumulator, adjustment) => {\n      const entries = Object.entries(adjustment);\n\n      for (const [key, valueAdjustment] of entries) {\n        const value = accumulator[key];\n\n        if (value != null) {\n          accumulator[key] = value + modifier * valueAdjustment;\n        }\n      }\n\n      return accumulator;\n    }, { ...object\n    });\n  };\n}\n\nconst add = /*#__PURE__*/createAdjustmentFn(1);\nconst subtract = /*#__PURE__*/createAdjustmentFn(-1);\n\nfunction hasViewportRelativeCoordinates(event) {\n  return 'clientX' in event && 'clientY' in event;\n}\n\nfunction isKeyboardEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n\nfunction isTouchEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    TouchEvent\n  } = getWindow(event.target);\n  return TouchEvent && event instanceof TouchEvent;\n}\n\n/**\r\n * Returns the normalized x and y coordinates for mouse and touch events.\r\n */\n\nfunction getEventCoordinates(event) {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.touches[0];\n      return {\n        x,\n        y\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.changedTouches[0];\n      return {\n        x,\n        y\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  return null;\n}\n\nconst CSS = /*#__PURE__*/Object.freeze({\n  Translate: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        x,\n        y\n      } = transform;\n      return \"translate3d(\" + (x ? Math.round(x) : 0) + \"px, \" + (y ? Math.round(y) : 0) + \"px, 0)\";\n    }\n\n  },\n  Scale: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        scaleX,\n        scaleY\n      } = transform;\n      return \"scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\";\n    }\n\n  },\n  Transform: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      return [CSS.Translate.toString(transform), CSS.Scale.toString(transform)].join(' ');\n    }\n\n  },\n  Transition: {\n    toString(_ref) {\n      let {\n        property,\n        duration,\n        easing\n      } = _ref;\n      return property + \" \" + duration + \"ms \" + easing;\n    }\n\n  }\n});\n\nconst SELECTOR = 'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\nfunction findFirstFocusableNode(element) {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n\nexport { CSS, add, canUseDOM, findFirstFocusableNode, getEventCoordinates, getOwnerDocument, getWindow, hasViewportRelativeCoordinates, isDocument, isHTMLElement, isKeyboardEvent, isNode, isSVGElement, isTouchEvent, isWindow, subtract, useCombinedRefs, useEvent, useInterval, useIsomorphicLayoutEffect, useLatestValue, useLazyMemo, useNodeRef, usePrevious, useUniqueId };\n//# sourceMappingURL=utilities.esm.js.map\n", "import React, { useState, useCallback } from 'react';\n\nconst hiddenStyles = {\n  display: 'none'\n};\nfunction HiddenText(_ref) {\n  let {\n    id,\n    value\n  } = _ref;\n  return React.createElement(\"div\", {\n    id: id,\n    style: hiddenStyles\n  }, value);\n}\n\nfunction LiveRegion(_ref) {\n  let {\n    id,\n    announcement,\n    ariaLiveType = \"assertive\"\n  } = _ref;\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap'\n  };\n  return React.createElement(\"div\", {\n    id: id,\n    style: visuallyHidden,\n    role: \"status\",\n    \"aria-live\": ariaLiveType,\n    \"aria-atomic\": true\n  }, announcement);\n}\n\nfunction useAnnouncement() {\n  const [announcement, setAnnouncement] = useState('');\n  const announce = useCallback(value => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n  return {\n    announce,\n    announcement\n  };\n}\n\nexport { HiddenText, LiveRegion, useAnnouncement };\n//# sourceMappingURL=accessibility.esm.js.map\n", "import React, { createContext, useContext, useEffect, useState, useCallback, useMemo, useRef, memo, useReducer, cloneElement, forwardRef } from 'react';\nimport { createPortal, unstable_batchedUpdates } from 'react-dom';\nimport { useUniqueId, getEventCoordinates, getWindow, isDocument, isHTMLElement, isSVGElement, canUseDOM, isWindow, isNode, getOwnerDocument, add, isKeyboardEvent, subtract, useLazyMemo, useInterval, usePrevious, useLatestValue, useEvent, useIsomorphicLayoutEffect, useNodeRef, findFirstFocusableNode, CSS } from '@dnd-kit/utilities';\nimport { useAnnouncement, HiddenText, LiveRegion } from '@dnd-kit/accessibility';\n\nconst DndMonitorContext = /*#__PURE__*/createContext(null);\n\nfunction useDndMonitor(listener) {\n  const registerListener = useContext(DndMonitorContext);\n  useEffect(() => {\n    if (!registerListener) {\n      throw new Error('useDndMonitor must be used within a children of <DndContext>');\n    }\n\n    const unsubscribe = registerListener(listener);\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n\nfunction useDndMonitorProvider() {\n  const [listeners] = useState(() => new Set());\n  const registerListener = useCallback(listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  }, [listeners]);\n  const dispatch = useCallback(_ref => {\n    let {\n      type,\n      event\n    } = _ref;\n    listeners.forEach(listener => {\n      var _listener$type;\n\n      return (_listener$type = listener[type]) == null ? void 0 : _listener$type.call(listener, event);\n    });\n  }, [listeners]);\n  return [dispatch, registerListener];\n}\n\nconst defaultScreenReaderInstructions = {\n  draggable: \"\\n    To pick up a draggable item, press the space bar.\\n    While dragging, use the arrow keys to move the item.\\n    Press space again to drop the item in its new position, or press escape to cancel.\\n  \"\n};\nconst defaultAnnouncements = {\n  onDragStart(_ref) {\n    let {\n      active\n    } = _ref;\n    return \"Picked up draggable item \" + active.id + \".\";\n  },\n\n  onDragOver(_ref2) {\n    let {\n      active,\n      over\n    } = _ref2;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was moved over droppable area \" + over.id + \".\";\n    }\n\n    return \"Draggable item \" + active.id + \" is no longer over a droppable area.\";\n  },\n\n  onDragEnd(_ref3) {\n    let {\n      active,\n      over\n    } = _ref3;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was dropped over droppable area \" + over.id;\n    }\n\n    return \"Draggable item \" + active.id + \" was dropped.\";\n  },\n\n  onDragCancel(_ref4) {\n    let {\n      active\n    } = _ref4;\n    return \"Dragging was cancelled. Draggable item \" + active.id + \" was dropped.\";\n  }\n\n};\n\nfunction Accessibility(_ref) {\n  let {\n    announcements = defaultAnnouncements,\n    container,\n    hiddenTextDescribedById,\n    screenReaderInstructions = defaultScreenReaderInstructions\n  } = _ref;\n  const {\n    announce,\n    announcement\n  } = useAnnouncement();\n  const liveRegionId = useUniqueId(\"DndLiveRegion\");\n  const [mounted, setMounted] = useState(false);\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n  useDndMonitor(useMemo(() => ({\n    onDragStart(_ref2) {\n      let {\n        active\n      } = _ref2;\n      announce(announcements.onDragStart({\n        active\n      }));\n    },\n\n    onDragMove(_ref3) {\n      let {\n        active,\n        over\n      } = _ref3;\n\n      if (announcements.onDragMove) {\n        announce(announcements.onDragMove({\n          active,\n          over\n        }));\n      }\n    },\n\n    onDragOver(_ref4) {\n      let {\n        active,\n        over\n      } = _ref4;\n      announce(announcements.onDragOver({\n        active,\n        over\n      }));\n    },\n\n    onDragEnd(_ref5) {\n      let {\n        active,\n        over\n      } = _ref5;\n      announce(announcements.onDragEnd({\n        active,\n        over\n      }));\n    },\n\n    onDragCancel(_ref6) {\n      let {\n        active,\n        over\n      } = _ref6;\n      announce(announcements.onDragCancel({\n        active,\n        over\n      }));\n    }\n\n  }), [announce, announcements]));\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = React.createElement(React.Fragment, null, React.createElement(HiddenText, {\n    id: hiddenTextDescribedById,\n    value: screenReaderInstructions.draggable\n  }), React.createElement(LiveRegion, {\n    id: liveRegionId,\n    announcement: announcement\n  }));\n  return container ? createPortal(markup, container) : markup;\n}\n\nvar Action;\n\n(function (Action) {\n  Action[\"DragStart\"] = \"dragStart\";\n  Action[\"DragMove\"] = \"dragMove\";\n  Action[\"DragEnd\"] = \"dragEnd\";\n  Action[\"DragCancel\"] = \"dragCancel\";\n  Action[\"DragOver\"] = \"dragOver\";\n  Action[\"RegisterDroppable\"] = \"registerDroppable\";\n  Action[\"SetDroppableDisabled\"] = \"setDroppableDisabled\";\n  Action[\"UnregisterDroppable\"] = \"unregisterDroppable\";\n})(Action || (Action = {}));\n\nfunction noop() {}\n\nfunction useSensor(sensor, options) {\n  return useMemo(() => ({\n    sensor,\n    options: options != null ? options : {}\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [sensor, options]);\n}\n\nfunction useSensors() {\n  for (var _len = arguments.length, sensors = new Array(_len), _key = 0; _key < _len; _key++) {\n    sensors[_key] = arguments[_key];\n  }\n\n  return useMemo(() => [...sensors].filter(sensor => sensor != null), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...sensors]);\n}\n\nconst defaultCoordinates = /*#__PURE__*/Object.freeze({\n  x: 0,\n  y: 0\n});\n\n/**\r\n * Returns the distance between two points\r\n */\nfunction distanceBetween(p1, p2) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n\nfunction getRelativeTransformOrigin(event, rect) {\n  const eventCoordinates = getEventCoordinates(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: (eventCoordinates.x - rect.left) / rect.width * 100,\n    y: (eventCoordinates.y - rect.top) / rect.height * 100\n  };\n  return transformOrigin.x + \"% \" + transformOrigin.y + \"%\";\n}\n\n/**\r\n * Sort collisions from smallest to greatest value\r\n */\nfunction sortCollisionsAsc(_ref, _ref2) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref2;\n  return a - b;\n}\n/**\r\n * Sort collisions from greatest to smallest value\r\n */\n\nfunction sortCollisionsDesc(_ref3, _ref4) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref3;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref4;\n  return b - a;\n}\n/**\r\n * Returns the coordinates of the corners of a given rectangle:\r\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\r\n */\n\nfunction cornersOfRectangle(_ref5) {\n  let {\n    left,\n    top,\n    height,\n    width\n  } = _ref5;\n  return [{\n    x: left,\n    y: top\n  }, {\n    x: left + width,\n    y: top\n  }, {\n    x: left,\n    y: top + height\n  }, {\n    x: left + width,\n    y: top + height\n  }];\n}\nfunction getFirstCollision(collisions, property) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n  return property ? firstCollision[property] : firstCollision;\n}\n\n/**\r\n * Returns the coordinates of the center of a given ClientRect\r\n */\n\nfunction centerOfRectangle(rect, left, top) {\n  if (left === void 0) {\n    left = rect.left;\n  }\n\n  if (top === void 0) {\n    top = rect.top;\n  }\n\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5\n  };\n}\n/**\r\n * Returns the closest rectangles from an array of rectangles to the center of a given\r\n * rectangle.\r\n */\n\n\nconst closestCenter = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const centerRect = centerOfRectangle(collisionRect, collisionRect.left, collisionRect.top);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: distBetween\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the closest rectangles from an array of rectangles to the corners of\r\n * another rectangle.\r\n */\n\nconst closestCorners = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the intersecting rectangle area between two rectangles\r\n */\n\nfunction getIntersectionRatio(entry, target) {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  } // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n\n\n  return 0;\n}\n/**\r\n * Returns the rectangles that has the greatest intersection area with a given\r\n * rectangle in an array of rectangles.\r\n */\n\nconst rectIntersection = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {\n            droppableContainer,\n            value: intersectionRatio\n          }\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n\n/**\r\n * Check if a given point is contained within a bounding rectangle\r\n */\n\nfunction isPointWithinRect(point, rect) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = rect;\n  return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;\n}\n/**\r\n * Returns the rectangles that the pointer is hovering over\r\n */\n\n\nconst pointerWithin = _ref => {\n  let {\n    droppableContainers,\n    droppableRects,\n    pointerCoordinates\n  } = _ref;\n\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\r\n       * with the pointer coordinates. In order to sort the\r\n       * colliding rectangles, we measure the distance between\r\n       * the pointer and the corners of the intersecting rectangle\r\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\nfunction adjustScale(transform, rect1, rect2) {\n  return { ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1\n  };\n}\n\nfunction getRectDelta(rect1, rect2) {\n  return rect1 && rect2 ? {\n    x: rect1.left - rect2.left,\n    y: rect1.top - rect2.top\n  } : defaultCoordinates;\n}\n\nfunction createRectAdjustmentFn(modifier) {\n  return function adjustClientRect(rect) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((acc, adjustment) => ({ ...acc,\n      top: acc.top + modifier * adjustment.y,\n      bottom: acc.bottom + modifier * adjustment.y,\n      left: acc.left + modifier * adjustment.x,\n      right: acc.right + modifier * adjustment.x\n    }), { ...rect\n    });\n  };\n}\nconst getAdjustedRect = /*#__PURE__*/createRectAdjustmentFn(1);\n\nfunction parseTransform(transform) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n\n  return null;\n}\n\nfunction inverseTransform(rect, transform, transformOrigin) {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\nconst defaultOptions = {\n  ignoreTransform: false\n};\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n */\n\nfunction getClientRect(element, options) {\n  if (options === void 0) {\n    options = defaultOptions;\n  }\n\n  let rect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {\n      transform,\n      transformOrigin\n    } = getWindow(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  } = rect;\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  };\n}\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n *\r\n * @remarks\r\n * The ClientRect returned by this method does not take into account transforms\r\n * applied to the element it measures.\r\n *\r\n */\n\nfunction getTransformAgnosticClientRect(element) {\n  return getClientRect(element, {\n    ignoreTransform: true\n  });\n}\n\nfunction getWindowClientRect(element) {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\n\nfunction isFixed(node, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = getWindow(node).getComputedStyle(node);\n  }\n\n  return computedStyle.position === 'fixed';\n}\n\nfunction isScrollable(element, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = getWindow(element).getComputedStyle(element);\n  }\n\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n\nfunction getScrollableAncestors(element, limit) {\n  const scrollParents = [];\n\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if (isDocument(node) && node.scrollingElement != null && !scrollParents.includes(node.scrollingElement)) {\n      scrollParents.push(node.scrollingElement);\n      return scrollParents;\n    }\n\n    if (!isHTMLElement(node) || isSVGElement(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = getWindow(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\n\nfunction getScrollableElement(element) {\n  if (!canUseDOM || !element) {\n    return null;\n  }\n\n  if (isWindow(element)) {\n    return element;\n  }\n\n  if (!isNode(element)) {\n    return null;\n  }\n\n  if (isDocument(element) || element === getOwnerDocument(element).scrollingElement) {\n    return window;\n  }\n\n  if (isHTMLElement(element)) {\n    return element;\n  }\n\n  return null;\n}\n\nfunction getScrollXCoordinate(element) {\n  if (isWindow(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\nfunction getScrollYCoordinate(element) {\n  if (isWindow(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\nfunction getScrollCoordinates(element) {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element)\n  };\n}\n\nvar Direction;\n\n(function (Direction) {\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n\nfunction isDocumentScrollingElement(element) {\n  if (!canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n\nfunction getScrollPosition(scrollingContainer) {\n  const minScroll = {\n    x: 0,\n    y: 0\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer) ? {\n    height: window.innerHeight,\n    width: window.innerWidth\n  } : {\n    height: scrollingContainer.clientHeight,\n    width: scrollingContainer.clientWidth\n  };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height\n  };\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll\n  };\n}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nfunction getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, _ref, acceleration, thresholdPercentage) {\n  let {\n    top,\n    left,\n    right,\n    bottom\n  } = _ref;\n\n  if (acceleration === void 0) {\n    acceleration = 10;\n  }\n\n  if (thresholdPercentage === void 0) {\n    thresholdPercentage = defaultThreshold;\n  }\n\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollContainer);\n  const direction = {\n    x: 0,\n    y: 0\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold.height - top) / threshold.height);\n  } else if (!isBottom && bottom >= scrollContainerRect.bottom - threshold.height) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold.height - bottom) / threshold.height);\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold.width - right) / threshold.width);\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold.width - left) / threshold.width);\n  }\n\n  return {\n    direction,\n    speed\n  };\n}\n\nfunction getScrollElementRect(element) {\n  if (element === document.scrollingElement) {\n    const {\n      innerWidth,\n      innerHeight\n    } = window;\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight\n    };\n  }\n\n  const {\n    top,\n    left,\n    right,\n    bottom\n  } = element.getBoundingClientRect();\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight\n  };\n}\n\nfunction getScrollOffsets(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return add(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\nfunction getScrollXOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\nfunction getScrollYOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n\nfunction scrollIntoViewIfNeeded(element, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  if (!element) {\n    return;\n  }\n\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (bottom <= 0 || right <= 0 || top >= window.innerHeight || left >= window.innerWidth) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center'\n    });\n  }\n}\n\nconst properties = [['x', ['left', 'right'], getScrollXOffset], ['y', ['top', 'bottom'], getScrollYOffset]];\nclass Rect {\n  constructor(rect, element) {\n    this.rect = void 0;\n    this.width = void 0;\n    this.height = void 0;\n    this.top = void 0;\n    this.bottom = void 0;\n    this.right = void 0;\n    this.left = void 0;\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n    this.rect = { ...rect\n    };\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {\n      enumerable: false\n    });\n  }\n\n}\n\nclass Listeners {\n  constructor(target) {\n    this.target = void 0;\n    this.listeners = [];\n\n    this.removeAll = () => {\n      this.listeners.forEach(listener => {\n        var _this$target;\n\n        return (_this$target = this.target) == null ? void 0 : _this$target.removeEventListener(...listener);\n      });\n    };\n\n    this.target = target;\n  }\n\n  add(eventName, handler, options) {\n    var _this$target2;\n\n    (_this$target2 = this.target) == null ? void 0 : _this$target2.addEventListener(eventName, handler, options);\n    this.listeners.push([eventName, handler, options]);\n  }\n\n}\n\nfunction getEventListenerTarget(target) {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n  const {\n    EventTarget\n  } = getWindow(target);\n  return target instanceof EventTarget ? target : getOwnerDocument(target);\n}\n\nfunction hasExceededDistance(delta, measurement) {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n\nvar EventName;\n\n(function (EventName) {\n  EventName[\"Click\"] = \"click\";\n  EventName[\"DragStart\"] = \"dragstart\";\n  EventName[\"Keydown\"] = \"keydown\";\n  EventName[\"ContextMenu\"] = \"contextmenu\";\n  EventName[\"Resize\"] = \"resize\";\n  EventName[\"SelectionChange\"] = \"selectionchange\";\n  EventName[\"VisibilityChange\"] = \"visibilitychange\";\n})(EventName || (EventName = {}));\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\n\nvar KeyboardCode;\n\n(function (KeyboardCode) {\n  KeyboardCode[\"Space\"] = \"Space\";\n  KeyboardCode[\"Down\"] = \"ArrowDown\";\n  KeyboardCode[\"Right\"] = \"ArrowRight\";\n  KeyboardCode[\"Left\"] = \"ArrowLeft\";\n  KeyboardCode[\"Up\"] = \"ArrowUp\";\n  KeyboardCode[\"Esc\"] = \"Escape\";\n  KeyboardCode[\"Enter\"] = \"Enter\";\n  KeyboardCode[\"Tab\"] = \"Tab\";\n})(KeyboardCode || (KeyboardCode = {}));\n\nconst defaultKeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab]\n};\nconst defaultKeyboardCoordinateGetter = (event, _ref) => {\n  let {\n    currentCoordinates\n  } = _ref;\n\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x + 25\n      };\n\n    case KeyboardCode.Left:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x - 25\n      };\n\n    case KeyboardCode.Down:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y + 25\n      };\n\n    case KeyboardCode.Up:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y - 25\n      };\n  }\n\n  return undefined;\n};\n\nclass KeyboardSensor {\n  constructor(props) {\n    this.props = void 0;\n    this.autoScrollEnabled = false;\n    this.referenceCoordinates = void 0;\n    this.listeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    const {\n      event: {\n        target\n      }\n    } = props;\n    this.props = props;\n    this.listeners = new Listeners(getOwnerDocument(target));\n    this.windowListeners = new Listeners(getWindow(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    this.handleStart();\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  handleStart() {\n    const {\n      activeNode,\n      onStart\n    } = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  handleKeyDown(event) {\n    if (isKeyboardEvent(event)) {\n      const {\n        active,\n        context,\n        options\n      } = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth'\n      } = options;\n      const {\n        code\n      } = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {\n        collisionRect\n      } = context.current;\n      const currentCoordinates = collisionRect ? {\n        x: collisionRect.left,\n        y: collisionRect.top\n      } : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = subtract(newCoordinates, currentCoordinates);\n        const scrollDelta = {\n          x: 0,\n          y: 0\n        };\n        const {\n          scrollableAncestors\n        } = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {\n            isTop,\n            isRight,\n            isLeft,\n            isBottom,\n            maxScroll,\n            minScroll\n          } = getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n          const clampedCoordinates = {\n            x: Math.min(direction === KeyboardCode.Right ? scrollElementRect.right - scrollElementRect.width / 2 : scrollElementRect.right, Math.max(direction === KeyboardCode.Right ? scrollElementRect.left : scrollElementRect.left + scrollElementRect.width / 2, newCoordinates.x)),\n            y: Math.min(direction === KeyboardCode.Down ? scrollElementRect.bottom - scrollElementRect.height / 2 : scrollElementRect.bottom, Math.max(direction === KeyboardCode.Down ? scrollElementRect.top : scrollElementRect.top + scrollElementRect.height / 2, newCoordinates.y))\n          };\n          const canScrollX = direction === KeyboardCode.Right && !isRight || direction === KeyboardCode.Left && !isLeft;\n          const canScrollY = direction === KeyboardCode.Down && !isBottom || direction === KeyboardCode.Up && !isTop;\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates = scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Right && newScrollCoordinates <= maxScroll.x || direction === KeyboardCode.Left && newScrollCoordinates >= minScroll.x;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x = direction === KeyboardCode.Right ? scrollContainer.scrollLeft - maxScroll.x : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates = scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Down && newScrollCoordinates <= maxScroll.y || direction === KeyboardCode.Up && newScrollCoordinates >= minScroll.y;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y = direction === KeyboardCode.Down ? scrollContainer.scrollTop - maxScroll.y : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(event, add(subtract(newCoordinates, this.referenceCoordinates), scrollDelta));\n      }\n    }\n  }\n\n  handleMove(event, coordinates) {\n    const {\n      onMove\n    } = this.props;\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  handleEnd(event) {\n    const {\n      onEnd\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel(event) {\n    const {\n      onCancel\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n}\nKeyboardSensor.activators = [{\n  eventName: 'onKeyDown',\n  handler: (event, _ref, _ref2) => {\n    let {\n      keyboardCodes = defaultKeyboardCodes,\n      onActivation\n    } = _ref;\n    let {\n      active\n    } = _ref2;\n    const {\n      code\n    } = event.nativeEvent;\n\n    if (keyboardCodes.start.includes(code)) {\n      const activator = active.activatorNode.current;\n\n      if (activator && event.target !== activator) {\n        return false;\n      }\n\n      event.preventDefault();\n      onActivation == null ? void 0 : onActivation({\n        event: event.nativeEvent\n      });\n      return true;\n    }\n\n    return false;\n  }\n}];\n\nfunction isDistanceConstraint(constraint) {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(constraint) {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nclass AbstractPointerSensor {\n  constructor(props, events, listenerTarget) {\n    var _getEventCoordinates;\n\n    if (listenerTarget === void 0) {\n      listenerTarget = getEventListenerTarget(props.event.target);\n    }\n\n    this.props = void 0;\n    this.events = void 0;\n    this.autoScrollEnabled = true;\n    this.document = void 0;\n    this.activated = false;\n    this.initialCoordinates = void 0;\n    this.timeoutId = null;\n    this.listeners = void 0;\n    this.documentListeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    this.events = events;\n    const {\n      event\n    } = props;\n    const {\n      target\n    } = event;\n    this.props = props;\n    this.events = events;\n    this.document = getOwnerDocument(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners(getWindow(target));\n    this.initialCoordinates = (_getEventCoordinates = getEventCoordinates(event)) != null ? _getEventCoordinates : defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    const {\n      events,\n      props: {\n        options: {\n          activationConstraint,\n          bypassActivationConstraint\n        }\n      }\n    } = this;\n    this.listeners.add(events.move.name, this.handleMove, {\n      passive: false\n    });\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (bypassActivationConstraint != null && bypassActivationConstraint({\n        event: this.props.event,\n        activeNode: this.props.activeNode,\n        options: this.props.options\n      })) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(this.handleStart, activationConstraint.delay);\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll(); // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  handlePending(constraint, offset) {\n    const {\n      active,\n      onPending\n    } = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  handleStart() {\n    const {\n      initialCoordinates\n    } = this;\n    const {\n      onStart\n    } = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true; // Stop propagation of click events once activation constraints are met\n\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true\n      }); // Remove any text selection from the document\n\n      this.removeTextSelection(); // Prevent further text selection while dragging\n\n      this.documentListeners.add(EventName.SelectionChange, this.removeTextSelection);\n      onStart(initialCoordinates);\n    }\n  }\n\n  handleMove(event) {\n    var _getEventCoordinates2;\n\n    const {\n      activated,\n      initialCoordinates,\n      props\n    } = this;\n    const {\n      onMove,\n      options: {\n        activationConstraint\n      }\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = (_getEventCoordinates2 = getEventCoordinates(event)) != null ? _getEventCoordinates2 : defaultCoordinates;\n    const delta = subtract(initialCoordinates, coordinates); // Constraint validation\n\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (activationConstraint.tolerance != null && hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  handleEnd() {\n    const {\n      onAbort,\n      onEnd\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onEnd();\n  }\n\n  handleCancel() {\n    const {\n      onAbort,\n      onCancel\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onCancel();\n  }\n\n  handleKeydown(event) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  removeTextSelection() {\n    var _this$document$getSel;\n\n    (_this$document$getSel = this.document.getSelection()) == null ? void 0 : _this$document$getSel.removeAllRanges();\n  }\n\n}\n\nconst events = {\n  cancel: {\n    name: 'pointercancel'\n  },\n  move: {\n    name: 'pointermove'\n  },\n  end: {\n    name: 'pointerup'\n  }\n};\nclass PointerSensor extends AbstractPointerSensor {\n  constructor(props) {\n    const {\n      event\n    } = props; // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n\n    const listenerTarget = getOwnerDocument(event.target);\n    super(props, events, listenerTarget);\n  }\n\n}\nPointerSensor.activators = [{\n  eventName: 'onPointerDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (!event.isPrimary || event.button !== 0) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$1 = {\n  move: {\n    name: 'mousemove'\n  },\n  end: {\n    name: 'mouseup'\n  }\n};\nvar MouseButton;\n\n(function (MouseButton) {\n  MouseButton[MouseButton[\"RightClick\"] = 2] = \"RightClick\";\n})(MouseButton || (MouseButton = {}));\n\nclass MouseSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$1, getOwnerDocument(props.event.target));\n  }\n\n}\nMouseSensor.activators = [{\n  eventName: 'onMouseDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (event.button === MouseButton.RightClick) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$2 = {\n  cancel: {\n    name: 'touchcancel'\n  },\n  move: {\n    name: 'touchmove'\n  },\n  end: {\n    name: 'touchend'\n  }\n};\nclass TouchSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$2);\n  }\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events$2.move.name, noop, {\n      capture: false,\n      passive: false\n    });\n    return function teardown() {\n      window.removeEventListener(events$2.move.name, noop);\n    }; // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n\n    function noop() {}\n  }\n\n}\nTouchSensor.activators = [{\n  eventName: 'onTouchStart',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    const {\n      touches\n    } = event;\n\n    if (touches.length > 1) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nvar AutoScrollActivator;\n\n(function (AutoScrollActivator) {\n  AutoScrollActivator[AutoScrollActivator[\"Pointer\"] = 0] = \"Pointer\";\n  AutoScrollActivator[AutoScrollActivator[\"DraggableRect\"] = 1] = \"DraggableRect\";\n})(AutoScrollActivator || (AutoScrollActivator = {}));\n\nvar TraversalOrder;\n\n(function (TraversalOrder) {\n  TraversalOrder[TraversalOrder[\"TreeOrder\"] = 0] = \"TreeOrder\";\n  TraversalOrder[TraversalOrder[\"ReversedTreeOrder\"] = 1] = \"ReversedTreeOrder\";\n})(TraversalOrder || (TraversalOrder = {}));\n\nfunction useAutoScroller(_ref) {\n  let {\n    acceleration,\n    activator = AutoScrollActivator.Pointer,\n    canScroll,\n    draggingRect,\n    enabled,\n    interval = 5,\n    order = TraversalOrder.TreeOrder,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    delta,\n    threshold\n  } = _ref;\n  const scrollIntent = useScrollIntent({\n    delta,\n    disabled: !enabled\n  });\n  const [setAutoScrollInterval, clearAutoScrollInterval] = useInterval();\n  const scrollSpeed = useRef({\n    x: 0,\n    y: 0\n  });\n  const scrollDirection = useRef({\n    x: 0,\n    y: 0\n  });\n  const rect = useMemo(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates ? {\n          top: pointerCoordinates.y,\n          bottom: pointerCoordinates.y,\n          left: pointerCoordinates.x,\n          right: pointerCoordinates.x\n        } : null;\n\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = useRef(null);\n  const autoScroll = useCallback(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = useMemo(() => order === TraversalOrder.TreeOrder ? [...scrollableAncestors].reverse() : scrollableAncestors, [order, scrollableAncestors]);\n  useEffect(() => {\n    if (!enabled || !scrollableAncestors.length || !rect) {\n      clearAutoScrollInterval();\n      return;\n    }\n\n    for (const scrollContainer of sortedScrollableAncestors) {\n      if ((canScroll == null ? void 0 : canScroll(scrollContainer)) === false) {\n        continue;\n      }\n\n      const index = scrollableAncestors.indexOf(scrollContainer);\n      const scrollContainerRect = scrollableAncestorRects[index];\n\n      if (!scrollContainerRect) {\n        continue;\n      }\n\n      const {\n        direction,\n        speed\n      } = getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, rect, acceleration, threshold);\n\n      for (const axis of ['x', 'y']) {\n        if (!scrollIntent[axis][direction[axis]]) {\n          speed[axis] = 0;\n          direction[axis] = 0;\n        }\n      }\n\n      if (speed.x > 0 || speed.y > 0) {\n        clearAutoScrollInterval();\n        scrollContainerRef.current = scrollContainer;\n        setAutoScrollInterval(autoScroll, interval);\n        scrollSpeed.current = speed;\n        scrollDirection.current = direction;\n        return;\n      }\n    }\n\n    scrollSpeed.current = {\n      x: 0,\n      y: 0\n    };\n    scrollDirection.current = {\n      x: 0,\n      y: 0\n    };\n    clearAutoScrollInterval();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [acceleration, autoScroll, canScroll, clearAutoScrollInterval, enabled, interval, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(rect), // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(scrollIntent), setAutoScrollInterval, scrollableAncestors, sortedScrollableAncestors, scrollableAncestorRects, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(threshold)]);\n}\nconst defaultScrollIntent = {\n  x: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  },\n  y: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  }\n};\n\nfunction useScrollIntent(_ref2) {\n  let {\n    delta,\n    disabled\n  } = _ref2;\n  const previousDelta = usePrevious(delta);\n  return useLazyMemo(previousIntent => {\n    if (disabled || !previousDelta || !previousIntent) {\n      // Reset scroll intent tracking when auto-scrolling is disabled\n      return defaultScrollIntent;\n    }\n\n    const direction = {\n      x: Math.sign(delta.x - previousDelta.x),\n      y: Math.sign(delta.y - previousDelta.y)\n    }; // Keep track of the user intent to scroll in each direction for both axis\n\n    return {\n      x: {\n        [Direction.Backward]: previousIntent.x[Direction.Backward] || direction.x === -1,\n        [Direction.Forward]: previousIntent.x[Direction.Forward] || direction.x === 1\n      },\n      y: {\n        [Direction.Backward]: previousIntent.y[Direction.Backward] || direction.y === -1,\n        [Direction.Forward]: previousIntent.y[Direction.Forward] || direction.y === 1\n      }\n    };\n  }, [disabled, delta, previousDelta]);\n}\n\nfunction useCachedNode(draggableNodes, id) {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n  return useLazyMemo(cachedNode => {\n    var _ref;\n\n    if (id == null) {\n      return null;\n    } // In some cases, the draggable node can unmount while dragging\n    // This is the case for virtualized lists. In those situations,\n    // we fall back to the last known value for that node.\n\n\n    return (_ref = node != null ? node : cachedNode) != null ? _ref : null;\n  }, [node, id]);\n}\n\nfunction useCombineActivators(sensors, getSyntheticHandler) {\n  return useMemo(() => sensors.reduce((accumulator, sensor) => {\n    const {\n      sensor: Sensor\n    } = sensor;\n    const sensorActivators = Sensor.activators.map(activator => ({\n      eventName: activator.eventName,\n      handler: getSyntheticHandler(activator.handler, sensor)\n    }));\n    return [...accumulator, ...sensorActivators];\n  }, []), [sensors, getSyntheticHandler]);\n}\n\nvar MeasuringStrategy;\n\n(function (MeasuringStrategy) {\n  MeasuringStrategy[MeasuringStrategy[\"Always\"] = 0] = \"Always\";\n  MeasuringStrategy[MeasuringStrategy[\"BeforeDragging\"] = 1] = \"BeforeDragging\";\n  MeasuringStrategy[MeasuringStrategy[\"WhileDragging\"] = 2] = \"WhileDragging\";\n})(MeasuringStrategy || (MeasuringStrategy = {}));\n\nvar MeasuringFrequency;\n\n(function (MeasuringFrequency) {\n  MeasuringFrequency[\"Optimized\"] = \"optimized\";\n})(MeasuringFrequency || (MeasuringFrequency = {}));\n\nconst defaultValue = /*#__PURE__*/new Map();\nfunction useDroppableMeasuring(containers, _ref) {\n  let {\n    dragging,\n    dependencies,\n    config\n  } = _ref;\n  const [queue, setQueue] = useState(null);\n  const {\n    frequency,\n    measure,\n    strategy\n  } = config;\n  const containersRef = useRef(containers);\n  const disabled = isDisabled();\n  const disabledRef = useLatestValue(disabled);\n  const measureDroppableContainers = useCallback(function (ids) {\n    if (ids === void 0) {\n      ids = [];\n    }\n\n    if (disabledRef.current) {\n      return;\n    }\n\n    setQueue(value => {\n      if (value === null) {\n        return ids;\n      }\n\n      return value.concat(ids.filter(id => !value.includes(id)));\n    });\n  }, [disabledRef]);\n  const timeoutId = useRef(null);\n  const droppableRects = useLazyMemo(previousValue => {\n    if (disabled && !dragging) {\n      return defaultValue;\n    }\n\n    if (!previousValue || previousValue === defaultValue || containersRef.current !== containers || queue != null) {\n      const map = new Map();\n\n      for (let container of containers) {\n        if (!container) {\n          continue;\n        }\n\n        if (queue && queue.length > 0 && !queue.includes(container.id) && container.rect.current) {\n          // This container does not need to be re-measured\n          map.set(container.id, container.rect.current);\n          continue;\n        }\n\n        const node = container.node.current;\n        const rect = node ? new Rect(measure(node), node) : null;\n        container.rect.current = rect;\n\n        if (rect) {\n          map.set(container.id, rect);\n        }\n      }\n\n      return map;\n    }\n\n    return previousValue;\n  }, [containers, queue, dragging, disabled, measure]);\n  useEffect(() => {\n    containersRef.current = containers;\n  }, [containers]);\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    measureDroppableContainers();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [dragging, disabled]);\n  useEffect(() => {\n    if (queue && queue.length > 0) {\n      setQueue(null);\n    }\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [JSON.stringify(queue)]);\n  useEffect(() => {\n    if (disabled || typeof frequency !== 'number' || timeoutId.current !== null) {\n      return;\n    }\n\n    timeoutId.current = setTimeout(() => {\n      measureDroppableContainers();\n      timeoutId.current = null;\n    }, frequency);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [frequency, disabled, measureDroppableContainers, ...dependencies]);\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n\n      default:\n        return !dragging;\n    }\n  }\n}\n\nfunction useInitialValue(value, computeFn) {\n  return useLazyMemo(previousValue => {\n    if (!value) {\n      return null;\n    }\n\n    if (previousValue) {\n      return previousValue;\n    }\n\n    return typeof computeFn === 'function' ? computeFn(value) : value;\n  }, [computeFn, value]);\n}\n\nfunction useInitialRect(node, measure) {\n  return useInitialValue(node, measure);\n}\n\n/**\r\n * Returns a new MutationObserver instance.\r\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useMutationObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleMutations = useEvent(callback);\n  const mutationObserver = useMemo(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.MutationObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      MutationObserver\n    } = window;\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n  useEffect(() => {\n    return () => mutationObserver == null ? void 0 : mutationObserver.disconnect();\n  }, [mutationObserver]);\n  return mutationObserver;\n}\n\n/**\r\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\r\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useResizeObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleResize = useEvent(callback);\n  const resizeObserver = useMemo(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.ResizeObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      ResizeObserver\n    } = window;\n    return new ResizeObserver(handleResize);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [disabled]);\n  useEffect(() => {\n    return () => resizeObserver == null ? void 0 : resizeObserver.disconnect();\n  }, [resizeObserver]);\n  return resizeObserver;\n}\n\nfunction defaultMeasure(element) {\n  return new Rect(getClientRect(element), element);\n}\n\nfunction useRect(element, measure, fallbackRect) {\n  if (measure === void 0) {\n    measure = defaultMeasure;\n  }\n\n  const [rect, setRect] = useState(null);\n\n  function measureRect() {\n    setRect(currentRect => {\n      if (!element) {\n        return null;\n      }\n\n      if (element.isConnected === false) {\n        var _ref;\n\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return (_ref = currentRect != null ? currentRect : fallbackRect) != null ? _ref : null;\n      }\n\n      const newRect = measure(element);\n\n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n\n      return newRect;\n    });\n  }\n\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {\n          type,\n          target\n        } = record;\n\n        if (type === 'childList' && target instanceof HTMLElement && target.contains(element)) {\n          measureRect();\n          break;\n        }\n      }\n    }\n\n  });\n  const resizeObserver = useResizeObserver({\n    callback: measureRect\n  });\n  useIsomorphicLayoutEffect(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(element);\n      mutationObserver == null ? void 0 : mutationObserver.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    }\n  }, [element]);\n  return rect;\n}\n\nfunction useRectDelta(rect) {\n  const initialRect = useInitialValue(rect);\n  return getRectDelta(rect, initialRect);\n}\n\nconst defaultValue$1 = [];\nfunction useScrollableAncestors(node) {\n  const previousNode = useRef(node);\n  const ancestors = useLazyMemo(previousValue => {\n    if (!node) {\n      return defaultValue$1;\n    }\n\n    if (previousValue && previousValue !== defaultValue$1 && node && previousNode.current && node.parentNode === previousNode.current.parentNode) {\n      return previousValue;\n    }\n\n    return getScrollableAncestors(node);\n  }, [node]);\n  useEffect(() => {\n    previousNode.current = node;\n  }, [node]);\n  return ancestors;\n}\n\nfunction useScrollOffsets(elements) {\n  const [scrollCoordinates, setScrollCoordinates] = useState(null);\n  const prevElements = useRef(elements); // To-do: Throttle the handleScroll callback\n\n  const handleScroll = useCallback(event => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates(scrollCoordinates => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(scrollingElement, getScrollCoordinates(scrollingElement));\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n  useEffect(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n      const entries = elements.map(element => {\n        const scrollableElement = getScrollableElement(element);\n\n        if (scrollableElement) {\n          scrollableElement.addEventListener('scroll', handleScroll, {\n            passive: true\n          });\n          return [scrollableElement, getScrollCoordinates(scrollableElement)];\n        }\n\n        return null;\n      }).filter(entry => entry != null);\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements) {\n      elements.forEach(element => {\n        const scrollableElement = getScrollableElement(element);\n        scrollableElement == null ? void 0 : scrollableElement.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n  return useMemo(() => {\n    if (elements.length) {\n      return scrollCoordinates ? Array.from(scrollCoordinates.values()).reduce((acc, coordinates) => add(acc, coordinates), defaultCoordinates) : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n\nfunction useScrollOffsetsDelta(scrollOffsets, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [];\n  }\n\n  const initialScrollOffsets = useRef(null);\n  useEffect(() => {\n    initialScrollOffsets.current = null;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  dependencies);\n  useEffect(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n  return initialScrollOffsets.current ? subtract(scrollOffsets, initialScrollOffsets.current) : defaultCoordinates;\n}\n\nfunction useSensorSetup(sensors) {\n  useEffect(() => {\n    if (!canUseDOM) {\n      return;\n    }\n\n    const teardownFns = sensors.map(_ref => {\n      let {\n        sensor\n      } = _ref;\n      return sensor.setup == null ? void 0 : sensor.setup();\n    });\n    return () => {\n      for (const teardown of teardownFns) {\n        teardown == null ? void 0 : teardown();\n      }\n    };\n  }, // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  sensors.map(_ref2 => {\n    let {\n      sensor\n    } = _ref2;\n    return sensor;\n  }));\n}\n\nfunction useSyntheticListeners(listeners, id) {\n  return useMemo(() => {\n    return listeners.reduce((acc, _ref) => {\n      let {\n        eventName,\n        handler\n      } = _ref;\n\n      acc[eventName] = event => {\n        handler(event, id);\n      };\n\n      return acc;\n    }, {});\n  }, [listeners, id]);\n}\n\nfunction useWindowRect(element) {\n  return useMemo(() => element ? getWindowClientRect(element) : null, [element]);\n}\n\nconst defaultValue$2 = [];\nfunction useRects(elements, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(firstElement ? getWindow(firstElement) : null);\n  const [rects, setRects] = useState(defaultValue$2);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue$2;\n      }\n\n      return elements.map(element => isDocumentScrollingElement(element) ? windowRect : new Rect(measure(element), element));\n    });\n  }\n\n  const resizeObserver = useResizeObserver({\n    callback: measureRects\n  });\n  useIsomorphicLayoutEffect(() => {\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n    measureRects();\n    elements.forEach(element => resizeObserver == null ? void 0 : resizeObserver.observe(element));\n  }, [elements]);\n  return rects;\n}\n\nfunction getMeasurableNode(node) {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n\n  const firstChild = node.children[0];\n  return isHTMLElement(firstChild) ? firstChild : node;\n}\n\nfunction useDragOverlayMeasuring(_ref) {\n  let {\n    measure\n  } = _ref;\n  const [rect, setRect] = useState(null);\n  const handleResize = useCallback(entries => {\n    for (const {\n      target\n    } of entries) {\n      if (isHTMLElement(target)) {\n        setRect(rect => {\n          const newRect = measure(target);\n          return rect ? { ...rect,\n            width: newRect.width,\n            height: newRect.height\n          } : newRect;\n        });\n        break;\n      }\n    }\n  }, [measure]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize\n  });\n  const handleNodeChange = useCallback(element => {\n    const node = getMeasurableNode(element);\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n\n    if (node) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(node);\n    }\n\n    setRect(node ? measure(node) : null);\n  }, [measure, resizeObserver]);\n  const [nodeRef, setRef] = useNodeRef(handleNodeChange);\n  return useMemo(() => ({\n    nodeRef,\n    rect,\n    setRef\n  }), [rect, nodeRef, setRef]);\n}\n\nconst defaultSensors = [{\n  sensor: PointerSensor,\n  options: {}\n}, {\n  sensor: KeyboardSensor,\n  options: {}\n}];\nconst defaultData = {\n  current: {}\n};\nconst defaultMeasuringConfiguration = {\n  draggable: {\n    measure: getTransformAgnosticClientRect\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized\n  },\n  dragOverlay: {\n    measure: getClientRect\n  }\n};\n\nclass DroppableContainersMap extends Map {\n  get(id) {\n    var _super$get;\n\n    return id != null ? (_super$get = super.get(id)) != null ? _super$get : undefined : undefined;\n  }\n\n  toArray() {\n    return Array.from(this.values());\n  }\n\n  getEnabled() {\n    return this.toArray().filter(_ref => {\n      let {\n        disabled\n      } = _ref;\n      return !disabled;\n    });\n  }\n\n  getNodeFor(id) {\n    var _this$get$node$curren, _this$get;\n\n    return (_this$get$node$curren = (_this$get = this.get(id)) == null ? void 0 : _this$get.node.current) != null ? _this$get$node$curren : undefined;\n  }\n\n}\n\nconst defaultPublicContext = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: /*#__PURE__*/new Map(),\n  droppableRects: /*#__PURE__*/new Map(),\n  droppableContainers: /*#__PURE__*/new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null\n    },\n    rect: null,\n    setRef: noop\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false\n};\nconst defaultInternalContext = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: ''\n  },\n  dispatch: noop,\n  draggableNodes: /*#__PURE__*/new Map(),\n  over: null,\n  measureDroppableContainers: noop\n};\nconst InternalContext = /*#__PURE__*/createContext(defaultInternalContext);\nconst PublicContext = /*#__PURE__*/createContext(defaultPublicContext);\n\nfunction getInitialState() {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {\n        x: 0,\n        y: 0\n      },\n      nodes: new Map(),\n      translate: {\n        x: 0,\n        y: 0\n      }\n    },\n    droppable: {\n      containers: new DroppableContainersMap()\n    }\n  };\n}\nfunction reducer(state, action) {\n  switch (action.type) {\n    case Action.DragStart:\n      return { ...state,\n        draggable: { ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active\n        }\n      };\n\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return { ...state,\n        draggable: { ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y\n          }\n        }\n      };\n\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return { ...state,\n        draggable: { ...state.draggable,\n          active: null,\n          initialCoordinates: {\n            x: 0,\n            y: 0\n          },\n          translate: {\n            x: 0,\n            y: 0\n          }\n        }\n      };\n\n    case Action.RegisterDroppable:\n      {\n        const {\n          element\n        } = action;\n        const {\n          id\n        } = element;\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, element);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.SetDroppableDisabled:\n      {\n        const {\n          id,\n          key,\n          disabled\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, { ...element,\n          disabled\n        });\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.UnregisterDroppable:\n      {\n        const {\n          id,\n          key\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.delete(id);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    default:\n      {\n        return state;\n      }\n  }\n}\n\nfunction RestoreFocus(_ref) {\n  let {\n    disabled\n  } = _ref;\n  const {\n    active,\n    activatorEvent,\n    draggableNodes\n  } = useContext(InternalContext);\n  const previousActivatorEvent = usePrevious(activatorEvent);\n  const previousActiveId = usePrevious(active == null ? void 0 : active.id); // Restore keyboard focus on the activator node\n\n  useEffect(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!isKeyboardEvent(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {\n        activatorNode,\n        node\n      } = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = findFirstFocusableNode(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [activatorEvent, disabled, draggableNodes, previousActiveId, previousActivatorEvent]);\n  return null;\n}\n\nfunction applyModifiers(modifiers, _ref) {\n  let {\n    transform,\n    ...args\n  } = _ref;\n  return modifiers != null && modifiers.length ? modifiers.reduce((accumulator, modifier) => {\n    return modifier({\n      transform: accumulator,\n      ...args\n    });\n  }, transform) : transform;\n}\n\nfunction useMeasuringConfiguration(config) {\n  return useMemo(() => ({\n    draggable: { ...defaultMeasuringConfiguration.draggable,\n      ...(config == null ? void 0 : config.draggable)\n    },\n    droppable: { ...defaultMeasuringConfiguration.droppable,\n      ...(config == null ? void 0 : config.droppable)\n    },\n    dragOverlay: { ...defaultMeasuringConfiguration.dragOverlay,\n      ...(config == null ? void 0 : config.dragOverlay)\n    }\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [config == null ? void 0 : config.draggable, config == null ? void 0 : config.droppable, config == null ? void 0 : config.dragOverlay]);\n}\n\nfunction useLayoutShiftScrollCompensation(_ref) {\n  let {\n    activeNode,\n    measure,\n    initialRect,\n    config = true\n  } = _ref;\n  const initialized = useRef(false);\n  const {\n    x,\n    y\n  } = typeof config === 'boolean' ? {\n    x: config,\n    y: config\n  } : config;\n  useIsomorphicLayoutEffect(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    } // Get the most up to date node ref for the active draggable\n\n\n    const node = activeNode == null ? void 0 : activeNode.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    } // Only perform layout shift scroll compensation once\n\n\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n\nconst ActiveDraggableContext = /*#__PURE__*/createContext({ ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1\n});\nvar Status;\n\n(function (Status) {\n  Status[Status[\"Uninitialized\"] = 0] = \"Uninitialized\";\n  Status[Status[\"Initializing\"] = 1] = \"Initializing\";\n  Status[Status[\"Initialized\"] = 2] = \"Initialized\";\n})(Status || (Status = {}));\n\nconst DndContext = /*#__PURE__*/memo(function DndContext(_ref) {\n  var _sensorContext$curren, _dragOverlay$nodeRef$, _dragOverlay$rect, _over$rect;\n\n  let {\n    id,\n    accessibility,\n    autoScroll = true,\n    children,\n    sensors = defaultSensors,\n    collisionDetection = rectIntersection,\n    measuring,\n    modifiers,\n    ...props\n  } = _ref;\n  const store = useReducer(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] = useDndMonitorProvider();\n  const [status, setStatus] = useState(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {\n      active: activeId,\n      nodes: draggableNodes,\n      translate\n    },\n    droppable: {\n      containers: droppableContainers\n    }\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = useRef({\n    initial: null,\n    translated: null\n  });\n  const active = useMemo(() => {\n    var _node$data;\n\n    return activeId != null ? {\n      id: activeId,\n      // It's possible for the active node to unmount while dragging\n      data: (_node$data = node == null ? void 0 : node.data) != null ? _node$data : defaultData,\n      rect: activeRects\n    } : null;\n  }, [activeId, node]);\n  const activeRef = useRef(null);\n  const [activeSensor, setActiveSensor] = useState(null);\n  const [activatorEvent, setActivatorEvent] = useState(null);\n  const latestProps = useLatestValue(props, Object.values(props));\n  const draggableDescribedById = useUniqueId(\"DndDescribedBy\", id);\n  const enabledDroppableContainers = useMemo(() => droppableContainers.getEnabled(), [droppableContainers]);\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled\n  } = useDroppableMeasuring(enabledDroppableContainers, {\n    dragging: isInitialized,\n    dependencies: [translate.x, translate.y],\n    config: measuringConfiguration.droppable\n  });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = useMemo(() => activatorEvent ? getEventCoordinates(activatorEvent) : null, [activatorEvent]);\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(activeNode, measuringConfiguration.draggable.measure);\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure\n  });\n  const activeNodeRect = useRect(activeNode, measuringConfiguration.draggable.measure, initialActiveNodeRect);\n  const containerNodeRect = useRect(activeNode ? activeNode.parentElement : null);\n  const sensorContext = useRef({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null\n  });\n  const overNode = droppableContainers.getNodeFor((_sensorContext$curren = sensorContext.current.over) == null ? void 0 : _sensorContext$curren.id);\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure\n  }); // Use the rect of the drag overlay if it is mounted\n\n  const draggingNode = (_dragOverlay$nodeRef$ = dragOverlay.nodeRef.current) != null ? _dragOverlay$nodeRef$ : activeNode;\n  const draggingNodeRect = isInitialized ? (_dragOverlay$rect = dragOverlay.rect) != null ? _dragOverlay$rect : activeNodeRect : null;\n  const usesDragOverlay = Boolean(dragOverlay.nodeRef.current && dragOverlay.rect); // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect); // Get the window rect of the dragging node\n\n  const windowRect = useWindowRect(draggingNode ? getWindow(draggingNode) : null); // Get scrollable ancestors of the dragging node\n\n  const scrollableAncestors = useScrollableAncestors(isInitialized ? overNode != null ? overNode : activeNode : null);\n  const scrollableAncestorRects = useRects(scrollableAncestors); // Apply modifiers\n\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  });\n  const pointerCoordinates = activationCoordinates ? add(activationCoordinates, translate) : null;\n  const scrollOffsets = useScrollOffsets(scrollableAncestors); // Represents the scroll delta since dragging was initiated\n\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets); // Represents the scroll delta since the last time the active node rect was measured\n\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [activeNodeRect]);\n  const scrollAdjustedTranslate = add(modifiedTranslate, scrollAdjustment);\n  const collisionRect = draggingNodeRect ? getAdjustedRect(draggingNodeRect, modifiedTranslate) : null;\n  const collisions = active && collisionRect ? collisionDetection({\n    active,\n    collisionRect,\n    droppableRects,\n    droppableContainers: enabledDroppableContainers,\n    pointerCoordinates\n  }) : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = useState(null); // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n\n  const appliedTranslate = usesDragOverlay ? modifiedTranslate : add(modifiedTranslate, activeNodeScrollDelta);\n  const transform = adjustScale(appliedTranslate, (_over$rect = over == null ? void 0 : over.rect) != null ? _over$rect : null, activeNodeRect);\n  const activeSensorRef = useRef(null);\n  const instantiateSensor = useCallback((event, _ref2) => {\n    let {\n      sensor: Sensor,\n      options\n    } = _ref2;\n\n    if (activeRef.current == null) {\n      return;\n    }\n\n    const activeNode = draggableNodes.get(activeRef.current);\n\n    if (!activeNode) {\n      return;\n    }\n\n    const activatorEvent = event.nativeEvent;\n    const sensorInstance = new Sensor({\n      active: activeRef.current,\n      activeNode,\n      event: activatorEvent,\n      options,\n      // Sensors need to be instantiated with refs for arguments that change over time\n      // otherwise they are frozen in time with the stale arguments\n      context: sensorContext,\n\n      onAbort(id) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragAbort\n        } = latestProps.current;\n        const event = {\n          id\n        };\n        onDragAbort == null ? void 0 : onDragAbort(event);\n        dispatchMonitorEvent({\n          type: 'onDragAbort',\n          event\n        });\n      },\n\n      onPending(id, constraint, initialCoordinates, offset) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragPending\n        } = latestProps.current;\n        const event = {\n          id,\n          constraint,\n          initialCoordinates,\n          offset\n        };\n        onDragPending == null ? void 0 : onDragPending(event);\n        dispatchMonitorEvent({\n          type: 'onDragPending',\n          event\n        });\n      },\n\n      onStart(initialCoordinates) {\n        const id = activeRef.current;\n\n        if (id == null) {\n          return;\n        }\n\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragStart\n        } = latestProps.current;\n        const event = {\n          activatorEvent,\n          active: {\n            id,\n            data: draggableNode.data,\n            rect: activeRects\n          }\n        };\n        unstable_batchedUpdates(() => {\n          onDragStart == null ? void 0 : onDragStart(event);\n          setStatus(Status.Initializing);\n          dispatch({\n            type: Action.DragStart,\n            initialCoordinates,\n            active: id\n          });\n          dispatchMonitorEvent({\n            type: 'onDragStart',\n            event\n          });\n          setActiveSensor(activeSensorRef.current);\n          setActivatorEvent(activatorEvent);\n        });\n      },\n\n      onMove(coordinates) {\n        dispatch({\n          type: Action.DragMove,\n          coordinates\n        });\n      },\n\n      onEnd: createHandler(Action.DragEnd),\n      onCancel: createHandler(Action.DragCancel)\n    });\n    activeSensorRef.current = sensorInstance;\n\n    function createHandler(type) {\n      return async function handler() {\n        const {\n          active,\n          collisions,\n          over,\n          scrollAdjustedTranslate\n        } = sensorContext.current;\n        let event = null;\n\n        if (active && scrollAdjustedTranslate) {\n          const {\n            cancelDrop\n          } = latestProps.current;\n          event = {\n            activatorEvent,\n            active: active,\n            collisions,\n            delta: scrollAdjustedTranslate,\n            over\n          };\n\n          if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n            const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n            if (shouldCancel) {\n              type = Action.DragCancel;\n            }\n          }\n        }\n\n        activeRef.current = null;\n        unstable_batchedUpdates(() => {\n          dispatch({\n            type\n          });\n          setStatus(Status.Uninitialized);\n          setOver(null);\n          setActiveSensor(null);\n          setActivatorEvent(null);\n          activeSensorRef.current = null;\n          const eventName = type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n          if (event) {\n            const handler = latestProps.current[eventName];\n            handler == null ? void 0 : handler(event);\n            dispatchMonitorEvent({\n              type: eventName,\n              event\n            });\n          }\n        });\n      };\n    }\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes]);\n  const bindActivatorToSensorInstantiator = useCallback((handler, sensor) => {\n    return (event, active) => {\n      const nativeEvent = event.nativeEvent;\n      const activeDraggableNode = draggableNodes.get(active);\n\n      if ( // Another sensor is already instantiating\n      activeRef.current !== null || // No active draggable\n      !activeDraggableNode || // Event has already been captured\n      nativeEvent.dndKit || nativeEvent.defaultPrevented) {\n        return;\n      }\n\n      const activationContext = {\n        active: activeDraggableNode\n      };\n      const shouldActivate = handler(event, sensor.options, activationContext);\n\n      if (shouldActivate === true) {\n        nativeEvent.dndKit = {\n          capturedBy: sensor.sensor\n        };\n        activeRef.current = active;\n        instantiateSensor(event, sensor);\n      }\n    };\n  }, [draggableNodes, instantiateSensor]);\n  const activators = useCombineActivators(sensors, bindActivatorToSensorInstantiator);\n  useSensorSetup(sensors);\n  useIsomorphicLayoutEffect(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n  useEffect(() => {\n    const {\n      onDragMove\n    } = latestProps.current;\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      over\n    } = sensorContext.current;\n\n    if (!active || !activatorEvent) {\n      return;\n    }\n\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    unstable_batchedUpdates(() => {\n      onDragMove == null ? void 0 : onDragMove(event);\n      dispatchMonitorEvent({\n        type: 'onDragMove',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]);\n  useEffect(() => {\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      droppableContainers,\n      scrollAdjustedTranslate\n    } = sensorContext.current;\n\n    if (!active || activeRef.current == null || !activatorEvent || !scrollAdjustedTranslate) {\n      return;\n    }\n\n    const {\n      onDragOver\n    } = latestProps.current;\n    const overContainer = droppableContainers.get(overId);\n    const over = overContainer && overContainer.rect.current ? {\n      id: overContainer.id,\n      rect: overContainer.rect.current,\n      data: overContainer.data,\n      disabled: overContainer.disabled\n    } : null;\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    unstable_batchedUpdates(() => {\n      setOver(over);\n      onDragOver == null ? void 0 : onDragOver(event);\n      dispatchMonitorEvent({\n        type: 'onDragOver',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [overId]);\n  useIsomorphicLayoutEffect(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate\n    };\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect\n    };\n  }, [active, activeNode, collisions, collisionRect, draggableNodes, draggingNode, draggingNodeRect, droppableRects, droppableContainers, over, scrollableAncestors, scrollAdjustedTranslate]);\n  useAutoScroller({ ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects\n  });\n  const publicContext = useMemo(() => {\n    const context = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect\n    };\n    return context;\n  }, [active, activeNode, activeNodeRect, activatorEvent, collisions, containerNodeRect, dragOverlay, draggableNodes, droppableContainers, droppableRects, over, measureDroppableContainers, scrollableAncestors, scrollableAncestorRects, measuringConfiguration, measuringScheduled, windowRect]);\n  const internalContext = useMemo(() => {\n    const context = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers\n    };\n    return context;\n  }, [activatorEvent, activators, active, activeNodeRect, dispatch, draggableDescribedById, draggableNodes, over, measureDroppableContainers]);\n  return React.createElement(DndMonitorContext.Provider, {\n    value: registerMonitorListener\n  }, React.createElement(InternalContext.Provider, {\n    value: internalContext\n  }, React.createElement(PublicContext.Provider, {\n    value: publicContext\n  }, React.createElement(ActiveDraggableContext.Provider, {\n    value: transform\n  }, children)), React.createElement(RestoreFocus, {\n    disabled: (accessibility == null ? void 0 : accessibility.restoreFocus) === false\n  })), React.createElement(Accessibility, { ...accessibility,\n    hiddenTextDescribedById: draggableDescribedById\n  }));\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll = (activeSensor == null ? void 0 : activeSensor.autoScrollEnabled) === false;\n    const autoScrollGloballyDisabled = typeof autoScroll === 'object' ? autoScroll.enabled === false : autoScroll === false;\n    const enabled = isInitialized && !activeSensorDisablesAutoscroll && !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return { ...autoScroll,\n        enabled\n      };\n    }\n\n    return {\n      enabled\n    };\n  }\n});\n\nconst NullContext = /*#__PURE__*/createContext(null);\nconst defaultRole = 'button';\nconst ID_PREFIX = 'Draggable';\nfunction useDraggable(_ref) {\n  let {\n    id,\n    data,\n    disabled = false,\n    attributes\n  } = _ref;\n  const key = useUniqueId(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over\n  } = useContext(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0\n  } = attributes != null ? attributes : {};\n  const isDragging = (active == null ? void 0 : active.id) === id;\n  const transform = useContext(isDragging ? ActiveDraggableContext : NullContext);\n  const [node, setNodeRef] = useNodeRef();\n  const [activatorNode, setActivatorNodeRef] = useNodeRef();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = useLatestValue(data);\n  useIsomorphicLayoutEffect(() => {\n    draggableNodes.set(id, {\n      id,\n      key,\n      node,\n      activatorNode,\n      data: dataRef\n    });\n    return () => {\n      const node = draggableNodes.get(id);\n\n      if (node && node.key === key) {\n        draggableNodes.delete(id);\n      }\n    };\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes, id]);\n  const memoizedAttributes = useMemo(() => ({\n    role,\n    tabIndex,\n    'aria-disabled': disabled,\n    'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n    'aria-roledescription': roleDescription,\n    'aria-describedby': ariaDescribedById.draggable\n  }), [disabled, role, tabIndex, isDragging, roleDescription, ariaDescribedById.draggable]);\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform\n  };\n}\n\nfunction useDndContext() {\n  return useContext(PublicContext);\n}\n\nconst ID_PREFIX$1 = 'Droppable';\nconst defaultResizeObserverConfig = {\n  timeout: 25\n};\nfunction useDroppable(_ref) {\n  let {\n    data,\n    disabled = false,\n    id,\n    resizeObserverConfig\n  } = _ref;\n  const key = useUniqueId(ID_PREFIX$1);\n  const {\n    active,\n    dispatch,\n    over,\n    measureDroppableContainers\n  } = useContext(InternalContext);\n  const previous = useRef({\n    disabled\n  });\n  const resizeObserverConnected = useRef(false);\n  const rect = useRef(null);\n  const callbackId = useRef(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout\n  } = { ...defaultResizeObserverConfig,\n    ...resizeObserverConfig\n  };\n  const ids = useLatestValue(updateMeasurementsFor != null ? updateMeasurementsFor : id);\n  const handleResize = useCallback(() => {\n    if (!resizeObserverConnected.current) {\n      // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n      // assuming the element is rendered and displayed.\n      resizeObserverConnected.current = true;\n      return;\n    }\n\n    if (callbackId.current != null) {\n      clearTimeout(callbackId.current);\n    }\n\n    callbackId.current = setTimeout(() => {\n      measureDroppableContainers(Array.isArray(ids.current) ? ids.current : [ids.current]);\n      callbackId.current = null;\n    }, resizeObserverTimeout);\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [resizeObserverTimeout]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active\n  });\n  const handleNodeChange = useCallback((newElement, previousElement) => {\n    if (!resizeObserver) {\n      return;\n    }\n\n    if (previousElement) {\n      resizeObserver.unobserve(previousElement);\n      resizeObserverConnected.current = false;\n    }\n\n    if (newElement) {\n      resizeObserver.observe(newElement);\n    }\n  }, [resizeObserver]);\n  const [nodeRef, setNodeRef] = useNodeRef(handleNodeChange);\n  const dataRef = useLatestValue(data);\n  useEffect(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n  useEffect(() => {\n    dispatch({\n      type: Action.RegisterDroppable,\n      element: {\n        id,\n        key,\n        disabled,\n        node: nodeRef,\n        rect,\n        data: dataRef\n      }\n    });\n    return () => dispatch({\n      type: Action.UnregisterDroppable,\n      key,\n      id\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [id]);\n  useEffect(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled\n      });\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n  return {\n    active,\n    rect,\n    isOver: (over == null ? void 0 : over.id) === id,\n    node: nodeRef,\n    over,\n    setNodeRef\n  };\n}\n\nfunction AnimationManager(_ref) {\n  let {\n    animation,\n    children\n  } = _ref;\n  const [clonedChildren, setClonedChildren] = useState(null);\n  const [element, setElement] = useState(null);\n  const previousChildren = usePrevious(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  useIsomorphicLayoutEffect(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren == null ? void 0 : clonedChildren.key;\n    const id = clonedChildren == null ? void 0 : clonedChildren.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n  return React.createElement(React.Fragment, null, children, clonedChildren ? cloneElement(clonedChildren, {\n    ref: setElement\n  }) : null);\n}\n\nconst defaultTransform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction NullifiedContextProvider(_ref) {\n  let {\n    children\n  } = _ref;\n  return React.createElement(InternalContext.Provider, {\n    value: defaultInternalContext\n  }, React.createElement(ActiveDraggableContext.Provider, {\n    value: defaultTransform\n  }, children));\n}\n\nconst baseStyles = {\n  position: 'fixed',\n  touchAction: 'none'\n};\n\nconst defaultTransition = activatorEvent => {\n  const isKeyboardActivator = isKeyboardEvent(activatorEvent);\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nconst PositionedOverlay = /*#__PURE__*/forwardRef((_ref, ref) => {\n  let {\n    as,\n    activatorEvent,\n    adjustScale,\n    children,\n    className,\n    rect,\n    style,\n    transform,\n    transition = defaultTransition\n  } = _ref;\n\n  if (!rect) {\n    return null;\n  }\n\n  const scaleAdjustedTransform = adjustScale ? transform : { ...transform,\n    scaleX: 1,\n    scaleY: 1\n  };\n  const styles = { ...baseStyles,\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    left: rect.left,\n    transform: CSS.Transform.toString(scaleAdjustedTransform),\n    transformOrigin: adjustScale && activatorEvent ? getRelativeTransformOrigin(activatorEvent, rect) : undefined,\n    transition: typeof transition === 'function' ? transition(activatorEvent) : transition,\n    ...style\n  };\n  return React.createElement(as, {\n    className,\n    style: styles,\n    ref\n  }, children);\n});\n\nconst defaultDropAnimationSideEffects = options => _ref => {\n  let {\n    active,\n    dragOverlay\n  } = _ref;\n  const originalStyles = {};\n  const {\n    styles,\n    className\n  } = options;\n\n  if (styles != null && styles.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles != null && styles.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className != null && className.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className != null && className.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className != null && className.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver = _ref2 => {\n  let {\n    transform: {\n      initial,\n      final\n    }\n  } = _ref2;\n  return [{\n    transform: CSS.Transform.toString(initial)\n  }, {\n    transform: CSS.Transform.toString(final)\n  }];\n};\n\nconst defaultDropAnimationConfiguration = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: /*#__PURE__*/defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0'\n      }\n    }\n  })\n};\nfunction useDropAnimation(_ref3) {\n  let {\n    config,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  } = _ref3;\n  return useEvent((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n\n    const {\n      transform\n    } = getWindow(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation = typeof config === 'function' ? config : createDefaultDropAnimation(config);\n    scrollIntoViewIfNeeded(activeNode, measuringConfiguration.draggable.measure);\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode)\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode)\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(options) {\n  const {\n    duration,\n    easing,\n    sideEffects,\n    keyframes\n  } = { ...defaultDropAnimationConfiguration,\n    ...options\n  };\n  return _ref4 => {\n    let {\n      active,\n      dragOverlay,\n      transform,\n      ...rest\n    } = _ref4;\n\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top\n    };\n    const scale = {\n      scaleX: transform.scaleX !== 1 ? active.rect.width * transform.scaleX / dragOverlay.rect.width : 1,\n      scaleY: transform.scaleY !== 1 ? active.rect.height * transform.scaleY / dragOverlay.rect.height : 1\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale\n    };\n    const animationKeyframes = keyframes({ ...rest,\n      active,\n      dragOverlay,\n      transform: {\n        initial: transform,\n        final: finalTransform\n      }\n    });\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects == null ? void 0 : sideEffects({\n      active,\n      dragOverlay,\n      ...rest\n    });\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards'\n    });\n    return new Promise(resolve => {\n      animation.onfinish = () => {\n        cleanup == null ? void 0 : cleanup();\n        resolve();\n      };\n    });\n  };\n}\n\nlet key = 0;\nfunction useKey(id) {\n  return useMemo(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n\nconst DragOverlay = /*#__PURE__*/React.memo(_ref => {\n  let {\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999\n  } = _ref;\n  const {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggableNodes,\n    droppableContainers,\n    dragOverlay,\n    over,\n    measuringConfiguration,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  } = useDndContext();\n  const transform = useContext(ActiveDraggableContext);\n  const key = useKey(active == null ? void 0 : active.id);\n  const modifiedTransform = applyModifiers(modifiers, {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect: dragOverlay.rect,\n    over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    transform,\n    windowRect\n  });\n  const initialRect = useInitialValue(activeNodeRect);\n  const dropAnimation = useDropAnimation({\n    config: dropAnimationConfig,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  }); // We need to wait for the active node to be measured before connecting the drag overlay ref\n  // otherwise collisions can be computed against a mispositioned drag overlay\n\n  const ref = initialRect ? dragOverlay.setRef : undefined;\n  return React.createElement(NullifiedContextProvider, null, React.createElement(AnimationManager, {\n    animation: dropAnimation\n  }, active && key ? React.createElement(PositionedOverlay, {\n    key: key,\n    id: active.id,\n    ref: ref,\n    as: wrapperElement,\n    activatorEvent: activatorEvent,\n    adjustScale: adjustScale,\n    className: className,\n    transition: transition,\n    rect: initialRect,\n    style: {\n      zIndex,\n      ...style\n    },\n    transform: modifiedTransform\n  }, children) : null));\n});\n\nexport { AutoScrollActivator, DndContext, DragOverlay, KeyboardCode, KeyboardSensor, MeasuringFrequency, MeasuringStrategy, MouseSensor, PointerSensor, TouchSensor, TraversalOrder, applyModifiers, closestCenter, closestCorners, defaultAnnouncements, defaultCoordinates, defaultDropAnimationConfiguration as defaultDropAnimation, defaultDropAnimationSideEffects, defaultKeyboardCoordinateGetter, defaultScreenReaderInstructions, getClientRect, getFirstCollision, getScrollableAncestors, pointerWithin, rectIntersection, useDndContext, useDndMonitor, useDraggable, useDroppable, useSensor, useSensors };\n//# sourceMappingURL=core.esm.js.map\n", "import React, { useMemo, useRef, useEffect, useState, useContext } from 'react';\nimport { useDndContext, getClientRect, useDroppable, useDraggable, closestCorners, getFirstCollision, getScrollableAncestors, KeyboardCode } from '@dnd-kit/core';\nimport { useUniqueId, useIsomorphicLayoutEffect, CSS, useCombinedRefs, isKeyboardEvent, subtract } from '@dnd-kit/utilities';\n\n/**\r\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\r\n */\nfunction arrayMove(array, from, to) {\n  const newArray = array.slice();\n  newArray.splice(to < 0 ? newArray.length + to : to, 0, newArray.splice(from, 1)[0]);\n  return newArray;\n}\n\n/**\r\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\r\n */\nfunction arraySwap(array, from, to) {\n  const newArray = array.slice();\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n  return newArray;\n}\n\nfunction getSortedRects(items, rects) {\n  return items.reduce((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n\nfunction isValidIndex(index) {\n  return index !== null && index >= 0;\n}\n\nfunction itemsEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction normalizeDisabled(disabled) {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled\n    };\n  }\n\n  return disabled;\n}\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst horizontalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    rects,\n    activeNodeRect: fallbackActiveRect,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x: activeIndex < overIndex ? newIndexRect.left + newIndexRect.width - (activeNodeRect.left + activeNodeRect.width) : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale\n  };\n};\n\nfunction getItemGap(rects, index, activeIndex) {\n  const currentRect = rects[index];\n  const previousRect = rects[index - 1];\n  const nextRect = rects[index + 1];\n\n  if (!currentRect || !previousRect && !nextRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.left - (previousRect.left + previousRect.width) : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect ? nextRect.left - (currentRect.left + currentRect.width) : currentRect.left - (previousRect.left + previousRect.width);\n}\n\nconst rectSortingStrategy = _ref => {\n  let {\n    rects,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\nconst rectSwappingStrategy = _ref => {\n  let {\n    activeIndex,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\n// To-do: We should be calculating scale transformation\nconst defaultScale$1 = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst verticalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    activeIndex,\n    activeNodeRect: fallbackActiveRect,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y: activeIndex < overIndex ? overIndexRect.top + overIndexRect.height - (activeNodeRect.top + activeNodeRect.height) : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale$1\n    };\n  }\n\n  const itemGap = getItemGap$1(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale$1\n  };\n};\n\nfunction getItemGap$1(clientRects, index, activeIndex) {\n  const currentRect = clientRects[index];\n  const previousRect = clientRects[index - 1];\n  const nextRect = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.top - (previousRect.top + previousRect.height) : nextRect ? nextRect.top - (currentRect.top + currentRect.height) : 0;\n  }\n\n  return nextRect ? nextRect.top - (currentRect.top + currentRect.height) : previousRect ? currentRect.top - (previousRect.top + previousRect.height) : 0;\n}\n\nconst ID_PREFIX = 'Sortable';\nconst Context = /*#__PURE__*/React.createContext({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false\n  }\n});\nfunction SortableContext(_ref) {\n  let {\n    children,\n    id,\n    items: userDefinedItems,\n    strategy = rectSortingStrategy,\n    disabled: disabledProp = false\n  } = _ref;\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers\n  } = useDndContext();\n  const containerId = useUniqueId(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = useMemo(() => userDefinedItems.map(item => typeof item === 'object' && 'id' in item ? item.id : item), [userDefinedItems]);\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = useRef(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms = overIndex !== -1 && activeIndex === -1 || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n  useIsomorphicLayoutEffect(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n  useEffect(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n  const contextValue = useMemo(() => ({\n    activeIndex,\n    containerId,\n    disabled,\n    disableTransforms,\n    items,\n    overIndex,\n    useDragOverlay,\n    sortedRects: getSortedRects(items, droppableRects),\n    strategy\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [activeIndex, containerId, disabled.draggable, disabled.droppable, disableTransforms, items, overIndex, droppableRects, useDragOverlay, strategy]);\n  return React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nconst defaultNewIndexGetter = _ref => {\n  let {\n    id,\n    items,\n    activeIndex,\n    overIndex\n  } = _ref;\n  return arrayMove(items, activeIndex, overIndex).indexOf(id);\n};\nconst defaultAnimateLayoutChanges = _ref2 => {\n  let {\n    containerId,\n    isSorting,\n    wasDragging,\n    index,\n    items,\n    newIndex,\n    previousItems,\n    previousContainerId,\n    transition\n  } = _ref2;\n\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\nconst defaultTransition = {\n  duration: 200,\n  easing: 'ease'\n};\nconst transitionProperty = 'transform';\nconst disabledTransition = /*#__PURE__*/CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear'\n});\nconst defaultAttributes = {\n  roleDescription: 'sortable'\n};\n\n/*\r\n * When the index of an item changes while sorting,\r\n * we need to temporarily disable the transforms\r\n */\n\nfunction useDerivedTransform(_ref) {\n  let {\n    disabled,\n    index,\n    node,\n    rect\n  } = _ref;\n  const [derivedTransform, setDerivedtransform] = useState(null);\n  const previousIndex = useRef(index);\n  useIsomorphicLayoutEffect(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = getClientRect(node.current, {\n          ignoreTransform: true\n        });\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n  useEffect(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n  return derivedTransform;\n}\n\nfunction useSortable(_ref) {\n  let {\n    animateLayoutChanges = defaultAnimateLayoutChanges,\n    attributes: userDefinedAttributes,\n    disabled: localDisabled,\n    data: customData,\n    getNewIndex = defaultNewIndexGetter,\n    id,\n    strategy: localStrategy,\n    resizeObserverConfig,\n    transition = defaultTransition\n  } = _ref;\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy\n  } = useContext(Context);\n  const disabled = normalizeLocalDisabled(localDisabled, globalDisabled);\n  const index = items.indexOf(id);\n  const data = useMemo(() => ({\n    sortable: {\n      containerId,\n      index,\n      items\n    },\n    ...customData\n  }), [containerId, customData, index, items]);\n  const itemsAfterCurrentSortable = useMemo(() => items.slice(items.indexOf(id)), [items, id]);\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef\n  } = useDroppable({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig\n    }\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform\n  } = useDraggable({\n    id,\n    data,\n    attributes: { ...defaultAttributes,\n      ...userDefinedAttributes\n    },\n    disabled: disabled.draggable\n  });\n  const setNodeRef = useCombinedRefs(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem = isSorting && !disableTransforms && isValidIndex(activeIndex) && isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement = shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy != null ? localStrategy : globalStrategy;\n  const finalTransform = displaceItem ? dragSourceDisplacement != null ? dragSourceDisplacement : strategy({\n    rects: sortedRects,\n    activeNodeRect,\n    activeIndex,\n    overIndex,\n    index\n  }) : null;\n  const newIndex = isValidIndex(activeIndex) && isValidIndex(overIndex) ? getNewIndex({\n    id,\n    items,\n    activeIndex,\n    overIndex\n  }) : index;\n  const activeId = active == null ? void 0 : active.id;\n  const previous = useRef({\n    activeId,\n    items,\n    newIndex,\n    containerId\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null\n  });\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect\n  });\n  useEffect(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n  useEffect(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId && !previous.current.activeId) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform != null ? derivedTransform : finalTransform,\n    transition: getTransition()\n  };\n\n  function getTransition() {\n    if ( // Temporarily disable transitions for a single frame to set up derived transforms\n    derivedTransform || // Or to prevent items jumping to back to their \"new\" position when items change\n    itemsHaveChanged && previous.current.newIndex === index) {\n      return disabledTransition;\n    }\n\n    if (shouldDisplaceDragSource && !isKeyboardEvent(activatorEvent) || !transition) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return CSS.Transition.toString({ ...transition,\n        property: transitionProperty\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(localDisabled, globalDisabled) {\n  var _localDisabled$dragga, _localDisabled$droppa;\n\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false\n    };\n  }\n\n  return {\n    draggable: (_localDisabled$dragga = localDisabled == null ? void 0 : localDisabled.draggable) != null ? _localDisabled$dragga : globalDisabled.draggable,\n    droppable: (_localDisabled$droppa = localDisabled == null ? void 0 : localDisabled.droppable) != null ? _localDisabled$droppa : globalDisabled.droppable\n  };\n}\n\nfunction hasSortableData(entry) {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (data && 'sortable' in data && typeof data.sortable === 'object' && 'containerId' in data.sortable && 'items' in data.sortable && 'index' in data.sortable) {\n    return true;\n  }\n\n  return false;\n}\n\nconst directions = [KeyboardCode.Down, KeyboardCode.Right, KeyboardCode.Up, KeyboardCode.Left];\nconst sortableKeyboardCoordinates = (event, _ref) => {\n  let {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors\n    }\n  } = _ref;\n\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers = [];\n    droppableContainers.getEnabled().forEach(entry => {\n      if (!entry || entry != null && entry.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n      }\n    });\n    const collisions = closestCorners({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null\n    });\n    let closestId = getFirstCollision(collisions, 'id');\n\n    if (closestId === (over == null ? void 0 : over.id) && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable == null ? void 0 : newDroppable.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = getScrollableAncestors(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some((element, index) => scrollableAncestors[index] !== element);\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset = hasDifferentScrollAncestors || !hasSameContainer ? {\n          x: 0,\n          y: 0\n        } : {\n          x: isAfterActive ? collisionRect.width - newRect.width : 0,\n          y: isAfterActive ? collisionRect.height - newRect.height : 0\n        };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top\n        };\n        const newCoordinates = offset.x && offset.y ? rectCoordinates : subtract(rectCoordinates, offset);\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.containerId === b.data.current.sortable.containerId;\n}\n\nfunction isAfter(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n\nexport { SortableContext, arrayMove, arraySwap, defaultAnimateLayoutChanges, defaultNewIndexGetter, hasSortableData, horizontalListSortingStrategy, rectSortingStrategy, rectSwappingStrategy, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy };\n//# sourceMappingURL=sortable.esm.js.map\n", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'grip-vertical', 'IconGripVertical', [[\"path\",{\"d\":\"M9 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M9 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M9 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M15 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M15 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0\",\"key\":\"svg-4\"}],[\"path\",{\"d\":\"M15 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0\",\"key\":\"svg-5\"}]]);", "'use client';\n\nimport { DotsHorizontalIcon } from '@radix-ui/react-icons';\nimport * as React from 'react';\nimport { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';\nimport { Button } from '@/components/ui/button';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { useTaskStore } from '../utils/store';\nimport { UniqueIdentifier } from '@dnd-kit/core';\nimport { Input } from '@/components/ui/input';\nimport { toast } from 'sonner';\nexport function ColumnActions({\n  title,\n  id\n}: {\n  title: string;\n  id: UniqueIdentifier;\n}) {\n  const [name, setName] = React.useState(title);\n  const updateCol = useTaskStore(state => state.updateCol);\n  const removeCol = useTaskStore(state => state.removeCol);\n  const [editDisable, setIsEditDisable] = React.useState(true);\n  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);\n  const inputRef = React.useRef<HTMLInputElement>(null);\n  return <>\r\n      <form onSubmit={e => {\n      e.preventDefault();\n      setIsEditDisable(!editDisable);\n      updateCol(id, name);\n      toast(`${title} updated to ${name}`);\n    }}>\r\n        <Input value={name} onChange={e => setName(e.target.value)} className='mt-0! mr-auto text-base disabled:cursor-pointer disabled:border-none disabled:opacity-100' disabled={editDisable} ref={inputRef} data-sentry-element=\"Input\" data-sentry-source-file=\"column-action.tsx\" />\r\n      </form>\r\n      <DropdownMenu modal={false} data-sentry-element=\"DropdownMenu\" data-sentry-source-file=\"column-action.tsx\">\r\n        <DropdownMenuTrigger asChild data-sentry-element=\"DropdownMenuTrigger\" data-sentry-source-file=\"column-action.tsx\">\r\n          <Button variant='secondary' className='ml-1' data-sentry-element=\"Button\" data-sentry-source-file=\"column-action.tsx\">\r\n            <span className='sr-only'>Actions</span>\r\n            <DotsHorizontalIcon className='h-4 w-4' data-sentry-element=\"DotsHorizontalIcon\" data-sentry-source-file=\"column-action.tsx\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align='end' data-sentry-element=\"DropdownMenuContent\" data-sentry-source-file=\"column-action.tsx\">\r\n          <DropdownMenuItem onSelect={() => {\n          setIsEditDisable(!editDisable);\n          setTimeout(() => {\n            inputRef.current && inputRef.current?.focus();\n          }, 500);\n        }} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"column-action.tsx\">\r\n            Rename\r\n          </DropdownMenuItem>\r\n          <DropdownMenuSeparator data-sentry-element=\"DropdownMenuSeparator\" data-sentry-source-file=\"column-action.tsx\" />\r\n\r\n          <DropdownMenuItem onSelect={() => setShowDeleteDialog(true)} className='text-red-600' data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"column-action.tsx\">\r\n            Delete Section\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog} data-sentry-element=\"AlertDialog\" data-sentry-source-file=\"column-action.tsx\">\r\n        <AlertDialogContent data-sentry-element=\"AlertDialogContent\" data-sentry-source-file=\"column-action.tsx\">\r\n          <AlertDialogHeader data-sentry-element=\"AlertDialogHeader\" data-sentry-source-file=\"column-action.tsx\">\r\n            <AlertDialogTitle data-sentry-element=\"AlertDialogTitle\" data-sentry-source-file=\"column-action.tsx\">\r\n              Are you sure want to delete column?\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription data-sentry-element=\"AlertDialogDescription\" data-sentry-source-file=\"column-action.tsx\">\r\n              NOTE: All tasks related to this category will also be deleted.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter data-sentry-element=\"AlertDialogFooter\" data-sentry-source-file=\"column-action.tsx\">\r\n            <AlertDialogCancel data-sentry-element=\"AlertDialogCancel\" data-sentry-source-file=\"column-action.tsx\">Cancel</AlertDialogCancel>\r\n            <Button variant='destructive' onClick={() => {\n            // yes, you have to set a timeout\n            setTimeout(() => document.body.style.pointerEvents = '', 100);\n            setShowDeleteDialog(false);\n            removeCol(id);\n            toast('This column has been deleted.');\n          }} data-sentry-element=\"Button\" data-sentry-source-file=\"column-action.tsx\">\r\n              Delete\r\n            </Button>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>;\n}", "import { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { Task } from '../utils/store';\nimport { useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { cva } from 'class-variance-authority';\nimport { IconGripVertical } from '@tabler/icons-react';\nimport { Badge } from '@/components/ui/badge';\n\n// export interface Task {\n//   id: UniqueIdentifier;\n//   columnId: ColumnId;\n//   content: string;\n// }\n\ninterface TaskCardProps {\n  task: Task;\n  isOverlay?: boolean;\n}\nexport type TaskType = 'Task';\nexport interface TaskDragData {\n  type: TaskType;\n  task: Task;\n}\nexport function TaskCard({\n  task,\n  isOverlay\n}: TaskCardProps) {\n  const {\n    setNodeRef,\n    attributes,\n    listeners,\n    transform,\n    transition,\n    isDragging\n  } = useSortable({\n    id: task.id,\n    data: {\n      type: 'Task',\n      task\n    } satisfies TaskDragData,\n    attributes: {\n      roleDescription: 'Task'\n    }\n  });\n  const style = {\n    transition,\n    transform: CSS.Translate.toString(transform)\n  };\n  const variants = cva('mb-2', {\n    variants: {\n      dragging: {\n        over: 'ring-2 opacity-30',\n        overlay: 'ring-2 ring-primary'\n      }\n    }\n  });\n  return <Card ref={setNodeRef} style={style} className={variants({\n    dragging: isOverlay ? 'overlay' : isDragging ? 'over' : undefined\n  })} data-sentry-element=\"Card\" data-sentry-component=\"TaskCard\" data-sentry-source-file=\"task-card.tsx\">\r\n      <CardHeader className='space-between border-secondary relative flex flex-row border-b-2 px-3 py-3' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"task-card.tsx\">\r\n        <Button variant={'ghost'} {...attributes} {...listeners} className='text-secondary-foreground/50 -ml-2 h-auto cursor-grab p-1' data-sentry-element=\"Button\" data-sentry-source-file=\"task-card.tsx\">\r\n          <span className='sr-only'>Move task</span>\r\n          <IconGripVertical data-sentry-element=\"IconGripVertical\" data-sentry-source-file=\"task-card.tsx\" />\r\n        </Button>\r\n        <Badge variant={'outline'} className='ml-auto font-semibold' data-sentry-element=\"Badge\" data-sentry-source-file=\"task-card.tsx\">\r\n          Task\r\n        </Badge>\r\n      </CardHeader>\r\n      <CardContent className='px-3 pt-3 pb-6 text-left whitespace-pre-wrap' data-sentry-element=\"CardContent\" data-sentry-source-file=\"task-card.tsx\">\r\n        {task.title}\r\n      </CardContent>\r\n    </Card>;\n}", "import { Task } from '../utils/store';\nimport { useDndContext, type UniqueIdentifier } from '@dnd-kit/core';\nimport { SortableContext, useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { cva } from 'class-variance-authority';\nimport { IconGripVertical } from '@tabler/icons-react';\nimport { useMemo } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { ColumnActions } from './column-action';\nimport { TaskCard } from './task-card';\nimport { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';\nexport interface Column {\n  id: UniqueIdentifier;\n  title: string;\n}\nexport type ColumnType = 'Column';\nexport interface ColumnDragData {\n  type: ColumnType;\n  column: Column;\n}\ninterface BoardColumnProps {\n  column: Column;\n  tasks: Task[];\n  isOverlay?: boolean;\n}\nexport function BoardColumn({\n  column,\n  tasks,\n  isOverlay\n}: BoardColumnProps) {\n  const tasksIds = useMemo(() => {\n    return tasks.map(task => task.id);\n  }, [tasks]);\n  const {\n    setNodeRef,\n    attributes,\n    listeners,\n    transform,\n    transition,\n    isDragging\n  } = useSortable({\n    id: column.id,\n    data: {\n      type: 'Column',\n      column\n    } satisfies ColumnDragData,\n    attributes: {\n      roleDescription: `Column: ${column.title}`\n    }\n  });\n  const style = {\n    transition,\n    transform: CSS.Translate.toString(transform)\n  };\n  const variants = cva('h-[75vh] max-h-[75vh] w-[350px] max-w-full bg-secondary flex flex-col shrink-0 snap-center', {\n    variants: {\n      dragging: {\n        default: 'border-2 border-transparent',\n        over: 'ring-2 opacity-30',\n        overlay: 'ring-2 ring-primary'\n      }\n    }\n  });\n  return <Card ref={setNodeRef} style={style} className={variants({\n    dragging: isOverlay ? 'overlay' : isDragging ? 'over' : undefined\n  })} data-sentry-element=\"Card\" data-sentry-component=\"BoardColumn\" data-sentry-source-file=\"board-column.tsx\">\r\n      <CardHeader className='space-between flex flex-row items-center border-b-2 p-4 text-left font-semibold' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"board-column.tsx\">\r\n        <Button variant={'ghost'} {...attributes} {...listeners} className='text-primary/50 relative -ml-2 h-auto cursor-grab p-1' data-sentry-element=\"Button\" data-sentry-source-file=\"board-column.tsx\">\r\n          <span className='sr-only'>{`Move column: ${column.title}`}</span>\r\n          <IconGripVertical data-sentry-element=\"IconGripVertical\" data-sentry-source-file=\"board-column.tsx\" />\r\n        </Button>\r\n        {/* <span className=\"mr-auto mt-0!\"> {column.title}</span> */}\r\n        {/* <Input\r\n          defaultValue={column.title}\r\n          className=\"text-base mt-0! mr-auto\"\r\n         /> */}\r\n        <ColumnActions id={column.id} title={column.title} data-sentry-element=\"ColumnActions\" data-sentry-source-file=\"board-column.tsx\" />\r\n      </CardHeader>\r\n      <CardContent className='flex grow flex-col gap-4 overflow-x-hidden p-2' data-sentry-element=\"CardContent\" data-sentry-source-file=\"board-column.tsx\">\r\n        <ScrollArea className='h-full' data-sentry-element=\"ScrollArea\" data-sentry-source-file=\"board-column.tsx\">\r\n          <SortableContext items={tasksIds} data-sentry-element=\"SortableContext\" data-sentry-source-file=\"board-column.tsx\">\r\n            {tasks.map(task => <TaskCard key={task.id} task={task} />)}\r\n          </SortableContext>\r\n        </ScrollArea>\r\n      </CardContent>\r\n    </Card>;\n}\nexport function BoardContainer({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const dndContext = useDndContext();\n  const variations = cva('px-2  pb-4 md:px-0 flex lg:justify-start', {\n    variants: {\n      dragging: {\n        default: '',\n        active: 'snap-none'\n      }\n    }\n  });\n  return <ScrollArea className='w-full rounded-md whitespace-nowrap' data-sentry-element=\"ScrollArea\" data-sentry-component=\"BoardContainer\" data-sentry-source-file=\"board-column.tsx\">\r\n      <div className={variations({\n      dragging: dndContext.active ? 'active' : 'default'\n    })}>\r\n        <div className='flex flex-row items-start justify-center gap-4'>\r\n          {children}\r\n        </div>\r\n      </div>\r\n      <ScrollBar orientation='horizontal' data-sentry-element=\"ScrollBar\" data-sentry-source-file=\"board-column.tsx\" />\r\n    </ScrollArea>;\n}", "'use client';\n\nimport { Button } from '@/components/ui/button';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Input } from '@/components/ui/input';\nimport { useTaskStore } from '../utils/store';\nexport default function NewSectionDialog() {\n  const addCol = useTaskStore(state => state.addCol);\n  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const form = e.currentTarget;\n    const formData = new FormData(form);\n    const {\n      title\n    } = Object.fromEntries(formData);\n    if (typeof title !== 'string') return;\n    addCol(title);\n  };\n  return <Dialog data-sentry-element=\"Dialog\" data-sentry-component=\"NewSectionDialog\" data-sentry-source-file=\"new-section-dialog.tsx\">\r\n      <DialogTrigger asChild data-sentry-element=\"DialogTrigger\" data-sentry-source-file=\"new-section-dialog.tsx\">\r\n        <Button variant='secondary' size='lg' className='w-full' data-sentry-element=\"Button\" data-sentry-source-file=\"new-section-dialog.tsx\">\r\n          ＋ Add New Section\r\n        </Button>\r\n      </DialogTrigger>\r\n      <DialogContent className='sm:max-w-[425px]' data-sentry-element=\"DialogContent\" data-sentry-source-file=\"new-section-dialog.tsx\">\r\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"new-section-dialog.tsx\">\r\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"new-section-dialog.tsx\">Add New Section</DialogTitle>\r\n          <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"new-section-dialog.tsx\">\r\n            What section you want to add today?\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <form id='todo-form' className='grid gap-4 py-4' onSubmit={handleSubmit}>\r\n          <div className='grid grid-cols-4 items-center gap-4'>\r\n            <Input id='title' name='title' placeholder='Section title...' className='col-span-4' data-sentry-element=\"Input\" data-sentry-source-file=\"new-section-dialog.tsx\" />\r\n          </div>\r\n        </form>\r\n        <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"new-section-dialog.tsx\">\r\n          <DialogTrigger asChild data-sentry-element=\"DialogTrigger\" data-sentry-source-file=\"new-section-dialog.tsx\">\r\n            <Button type='submit' size='sm' form='todo-form' data-sentry-element=\"Button\" data-sentry-source-file=\"new-section-dialog.tsx\">\r\n              Add Section\r\n            </Button>\r\n          </DialogTrigger>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>;\n}", "'use client';\n\nimport { Fragment, useEffect, useMemo, useRef, useState } from 'react';\nimport { createPortal } from 'react-dom';\nimport { Task, useTaskStore } from '../utils/store';\nimport { hasDraggableData } from '../utils';\nimport { Announcements, DndContext, DragOverlay, MouseSensor, TouchSensor, UniqueIdentifier, useSensor, useSensors, type DragEndEvent, type DragOverEvent, type DragStartEvent } from '@dnd-kit/core';\nimport { SortableContext, arrayMove } from '@dnd-kit/sortable';\nimport type { Column } from './board-column';\nimport { BoardColumn, BoardContainer } from './board-column';\nimport NewSectionDialog from './new-section-dialog';\nimport { TaskCard } from './task-card';\n// import { coordinateGetter } from \"./multipleContainersKeyboardPreset\";\n\nconst defaultCols = [{\n  id: 'TODO' as const,\n  title: 'Todo'\n}, {\n  id: 'IN_PROGRESS' as const,\n  title: 'In progress'\n}, {\n  id: 'DONE' as const,\n  title: 'Done'\n}] satisfies Column[];\nexport type ColumnId = (typeof defaultCols)[number]['id'];\nexport function KanbanBoard() {\n  // const [columns, setColumns] = useState<Column[]>(defaultCols);\n  const columns = useTaskStore(state => state.columns);\n  const setColumns = useTaskStore(state => state.setCols);\n  const pickedUpTaskColumn = useRef<ColumnId | 'TODO' | 'IN_PROGRESS' | 'DONE'>('TODO');\n  const columnsId = useMemo(() => columns.map(col => col.id), [columns]);\n\n  // const [tasks, setTasks] = useState<Task[]>(initialTasks);\n  const tasks = useTaskStore(state => state.tasks);\n  const setTasks = useTaskStore(state => state.setTasks);\n  const [activeColumn, setActiveColumn] = useState<Column | null>(null);\n  const [isMounted, setIsMounted] = useState<Boolean>(false);\n  const [activeTask, setActiveTask] = useState<Task | null>(null);\n  const sensors = useSensors(useSensor(MouseSensor), useSensor(TouchSensor)\n  // useSensor(KeyboardSensor, {\n  //   coordinateGetter: coordinateGetter,\n  // }),\n  );\n  useEffect(() => {\n    setIsMounted(true);\n  }, [isMounted]);\n  useEffect(() => {\n    useTaskStore.persist.rehydrate();\n  }, []);\n  if (!isMounted) return;\n  function getDraggingTaskData(taskId: UniqueIdentifier, columnId: ColumnId) {\n    const tasksInColumn = tasks.filter(task => task.status === columnId);\n    const taskPosition = tasksInColumn.findIndex(task => task.id === taskId);\n    const column = columns.find(col => col.id === columnId);\n    return {\n      tasksInColumn,\n      taskPosition,\n      column\n    };\n  }\n  const announcements: Announcements = {\n    onDragStart({\n      active\n    }) {\n      if (!hasDraggableData(active)) return;\n      if (active.data.current?.type === 'Column') {\n        const startColumnIdx = columnsId.findIndex(id => id === active.id);\n        const startColumn = columns[startColumnIdx];\n        return `Picked up Column ${startColumn?.title} at position: ${startColumnIdx + 1} of ${columnsId.length}`;\n      } else if (active.data.current?.type === 'Task') {\n        pickedUpTaskColumn.current = active.data.current.task.status;\n        const {\n          tasksInColumn,\n          taskPosition,\n          column\n        } = getDraggingTaskData(active.id, pickedUpTaskColumn.current);\n        return `Picked up Task ${active.data.current.task.title} at position: ${taskPosition + 1} of ${tasksInColumn.length} in column ${column?.title}`;\n      }\n    },\n    onDragOver({\n      active,\n      over\n    }) {\n      if (!hasDraggableData(active) || !hasDraggableData(over)) return;\n      if (active.data.current?.type === 'Column' && over.data.current?.type === 'Column') {\n        const overColumnIdx = columnsId.findIndex(id => id === over.id);\n        return `Column ${active.data.current.column.title} was moved over ${over.data.current.column.title} at position ${overColumnIdx + 1} of ${columnsId.length}`;\n      } else if (active.data.current?.type === 'Task' && over.data.current?.type === 'Task') {\n        const {\n          tasksInColumn,\n          taskPosition,\n          column\n        } = getDraggingTaskData(over.id, over.data.current.task.status);\n        if (over.data.current.task.status !== pickedUpTaskColumn.current) {\n          return `Task ${active.data.current.task.title} was moved over column ${column?.title} in position ${taskPosition + 1} of ${tasksInColumn.length}`;\n        }\n        return `Task was moved over position ${taskPosition + 1} of ${tasksInColumn.length} in column ${column?.title}`;\n      }\n    },\n    onDragEnd({\n      active,\n      over\n    }) {\n      if (!hasDraggableData(active) || !hasDraggableData(over)) {\n        pickedUpTaskColumn.current = 'TODO';\n        return;\n      }\n      if (active.data.current?.type === 'Column' && over.data.current?.type === 'Column') {\n        const overColumnPosition = columnsId.findIndex(id => id === over.id);\n        return `Column ${active.data.current.column.title} was dropped into position ${overColumnPosition + 1} of ${columnsId.length}`;\n      } else if (active.data.current?.type === 'Task' && over.data.current?.type === 'Task') {\n        const {\n          tasksInColumn,\n          taskPosition,\n          column\n        } = getDraggingTaskData(over.id, over.data.current.task.status);\n        if (over.data.current.task.status !== pickedUpTaskColumn.current) {\n          return `Task was dropped into column ${column?.title} in position ${taskPosition + 1} of ${tasksInColumn.length}`;\n        }\n        return `Task was dropped into position ${taskPosition + 1} of ${tasksInColumn.length} in column ${column?.title}`;\n      }\n      pickedUpTaskColumn.current = 'TODO';\n    },\n    onDragCancel({\n      active\n    }) {\n      pickedUpTaskColumn.current = 'TODO';\n      if (!hasDraggableData(active)) return;\n      return `Dragging ${active.data.current?.type} cancelled.`;\n    }\n  };\n  return <DndContext accessibility={{\n    announcements\n  }} sensors={sensors} onDragStart={onDragStart} onDragEnd={onDragEnd} onDragOver={onDragOver} data-sentry-element=\"DndContext\" data-sentry-component=\"KanbanBoard\" data-sentry-source-file=\"kanban-board.tsx\">\r\n      <BoardContainer data-sentry-element=\"BoardContainer\" data-sentry-source-file=\"kanban-board.tsx\">\r\n        <SortableContext items={columnsId} data-sentry-element=\"SortableContext\" data-sentry-source-file=\"kanban-board.tsx\">\r\n          {columns?.map((col, index) => <Fragment key={col.id}>\r\n              <BoardColumn column={col} tasks={tasks.filter(task => task.status === col.id)} />\r\n              {index === columns?.length - 1 && <div className='w-[300px]'>\r\n                  <NewSectionDialog />\r\n                </div>}\r\n            </Fragment>)}\r\n          {!columns.length && <NewSectionDialog />}\r\n        </SortableContext>\r\n      </BoardContainer>\r\n\r\n      {'document' in window && createPortal(<DragOverlay>\r\n            {activeColumn && <BoardColumn isOverlay column={activeColumn} tasks={tasks.filter(task => task.status === activeColumn.id)} />}\r\n            {activeTask && <TaskCard task={activeTask} isOverlay />}\r\n          </DragOverlay>, document.body)}\r\n    </DndContext>;\n  function onDragStart(event: DragStartEvent) {\n    if (!hasDraggableData(event.active)) return;\n    const data = event.active.data.current;\n    if (data?.type === 'Column') {\n      setActiveColumn(data.column);\n      return;\n    }\n    if (data?.type === 'Task') {\n      setActiveTask(data.task);\n      return;\n    }\n  }\n  function onDragEnd(event: DragEndEvent) {\n    setActiveColumn(null);\n    setActiveTask(null);\n    const {\n      active,\n      over\n    } = event;\n    if (!over) return;\n    const activeId = active.id;\n    const overId = over.id;\n    if (!hasDraggableData(active)) return;\n    const activeData = active.data.current;\n    if (activeId === overId) return;\n    const isActiveAColumn = activeData?.type === 'Column';\n    if (!isActiveAColumn) return;\n    const activeColumnIndex = columns.findIndex(col => col.id === activeId);\n    const overColumnIndex = columns.findIndex(col => col.id === overId);\n    setColumns(arrayMove(columns, activeColumnIndex, overColumnIndex));\n  }\n  function onDragOver(event: DragOverEvent) {\n    const {\n      active,\n      over\n    } = event;\n    if (!over) return;\n    const activeId = active.id;\n    const overId = over.id;\n    if (activeId === overId) return;\n    if (!hasDraggableData(active) || !hasDraggableData(over)) return;\n    const activeData = active.data.current;\n    const overData = over.data.current;\n    const isActiveATask = activeData?.type === 'Task';\n    const isOverATask = activeData?.type === 'Task';\n    if (!isActiveATask) return;\n\n    // Im dropping a Task over another Task\n    if (isActiveATask && isOverATask) {\n      const activeIndex = tasks.findIndex(t => t.id === activeId);\n      const overIndex = tasks.findIndex(t => t.id === overId);\n      const activeTask = tasks[activeIndex];\n      const overTask = tasks[overIndex];\n      if (activeTask && overTask && activeTask.status !== overTask.status) {\n        activeTask.status = overTask.status;\n        setTasks(arrayMove(tasks, activeIndex, overIndex - 1));\n      }\n      setTasks(arrayMove(tasks, activeIndex, overIndex));\n    }\n    const isOverAColumn = overData?.type === 'Column';\n\n    // Im dropping a Task over a column\n    if (isActiveATask && isOverAColumn) {\n      const activeIndex = tasks.findIndex(t => t.id === activeId);\n      const activeTask = tasks[activeIndex];\n      if (activeTask) {\n        activeTask.status = overId as ColumnId;\n        setTasks(arrayMove(tasks, activeIndex, activeIndex));\n      }\n    }\n  }\n}", "module.exports = require(\"events\");"], "names": ["badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "className", "<PERSON><PERSON><PERSON><PERSON>", "props", "Comp", "Slot", "data-slot", "cn", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "KanbanViewPage", "<PERSON><PERSON><PERSON><PERSON>", "div", "Heading", "title", "description", "NewTaskDialog", "KanbanBoard", "metadata", "page", "_jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "CardAction", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "h2", "p", "defaultCols", "id", "initialTasks", "status", "useTaskStore", "persist", "tasks", "columns", "draggedTask", "addTask", "set", "state", "uuid", "updateCol", "newName", "map", "col", "addCol", "length", "toUpperCase", "dragTask", "removeTask", "filter", "task", "removeCol", "setTasks", "newTasks", "setCols", "newCols", "name", "skipHydration", "Dialog", "DialogTrigger", "<PERSON><PERSON>", "size", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "form", "onSubmit", "e", "handleSubmit", "preventDefault", "Object", "fromEntries", "FormData", "currentTarget", "Input", "placeholder", "Textarea", "<PERSON><PERSON><PERSON><PERSON>er", "type", "textarea", "children", "scrollable", "ScrollArea", "hasDraggableData", "entry", "data", "current", "ColumnActions", "setName", "React", "editDisable", "setIsEditDisable", "showDeleteDialog", "setShowDeleteDialog", "inputRef", "toast", "value", "onChange", "target", "disabled", "ref", "DropdownMenu", "modal", "DropdownMenuTrigger", "span", "DotsHorizontalIcon", "DropdownMenuContent", "align", "DropdownMenuItem", "onSelect", "setTimeout", "focus", "DropdownMenuSeparator", "AlertDialog", "open", "onOpenChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "onClick", "document", "body", "style", "pointerEvents", "TaskCard", "isOverlay", "setNodeRef", "attributes", "listeners", "transform", "transition", "isDragging", "useSortable", "roleDescription", "CSS", "Translate", "toString", "dragging", "over", "overlay", "undefined", "IconGripVertical", "BoardColumn", "column", "tasksIds", "useMemo", "SortableContext", "items", "BoardContainer", "dndContext", "useDndContext", "variations", "active", "<PERSON><PERSON>Bar", "orientation", "NewSectionDialog", "formData", "setColumns", "pickedUpTaskColumn", "useRef", "columnsId", "activeColumn", "setActiveColumn", "useState", "isMounted", "setIsMounted", "activeTask", "setActiveTask", "sensors", "useSensors", "useSensor", "MouseSensor", "TouchSensor", "DndContext", "accessibility", "announcements", "onDragStart", "startColumnIdx", "findIndex", "startColumn", "tasksInColumn", "taskPosition", "getDraggingTaskData", "onDragOver", "overColumnIdx", "onDragEnd", "overColumnPosition", "onDragCancel", "event", "activeId", "overId", "activeData", "activeColumnIndex", "overColumnIndex", "arrayMove", "overData", "isActiveATask", "isOverATask", "activeIndex", "t", "overIndex", "overTask", "isOverAColumn", "index", "Fragment", "window", "createPortal", "DragOverlay", "taskId", "columnId", "find"], "sourceRoot": ""}