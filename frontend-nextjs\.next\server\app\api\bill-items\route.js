try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="88363f13-db12-40e2-b816-64595798b27b",e._sentryDebugIdIdentifier="sentry-dbid-88363f13-db12-40e2-b816-64595798b27b")}catch(e){}(()=>{var e={};e.id=5754,e.ids=[5754],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7760:()=>{},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45962:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=45962,e.exports=r},48161:e=>{"use strict";e.exports=require("node:os")},48664:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>j,serverHooks:()=>T,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>k});var s={};t.r(s),t.d(s,{DELETE:()=>g,GET:()=>f,HEAD:()=>w,OPTIONS:()=>E,PATCH:()=>v,POST:()=>m,PUT:()=>b});var i=t(86047),o=t(85544),n=t(36135),u=t(63033),a=t(35886),p=t(79615),c=t(19761);let d="http://localhost:8002";async function l(e){try{let{userId:r}=await (0,p.j)();if(!r)return a.NextResponse.json({error:"Authentication required"},{status:401});let t=await fetch(`https://api.clerk.com/v1/users/${r}`,{headers:{Authorization:`Bearer ${process.env.CLERK_SECRET_KEY}`}}).then(e=>e.json()),s=new URL(e.url),i=`${d}/api/bill-items${s.search}`,o=await fetch(i,{method:"GET",headers:{"Content-Type":"application/json","x-clerk-user-id":r,"x-user-email":t.email_addresses[0]?.email_address||""}}),n=await o.json();if(!o.ok)return a.NextResponse.json(n,{status:o.status});return a.NextResponse.json(n)}catch(e){return console.error("Error proxying bill-items request:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){try{let{userId:r}=await (0,p.j)();if(!r)return a.NextResponse.json({error:"Authentication required"},{status:401});let t=await fetch(`https://api.clerk.com/v1/users/${r}`,{headers:{Authorization:`Bearer ${process.env.CLERK_SECRET_KEY}`}}).then(e=>e.json()),s=await e.json(),i=`${d}/api/bill-items`,o=await fetch(i,{method:"POST",headers:{"Content-Type":"application/json","x-clerk-user-id":r,"x-user-email":t.email_addresses[0]?.email_address||""},body:JSON.stringify(s)}),n=await o.json();if(!o.ok)return a.NextResponse.json(n,{status:o.status});return a.NextResponse.json(n)}catch(e){return console.error("Error proxying bill-items request:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let h={...u},q="workUnitAsyncStorage"in h?h.workUnitAsyncStorage:"requestAsyncStorage"in h?h.requestAsyncStorage:void 0;function y(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let i;try{let e=q?.getStore();i=e?.headers}catch(e){}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/bill-items",headers:i}).apply(t,s)}})}let f=y(l,"GET"),m=y(x,"POST"),b=y(void 0,"PUT"),v=y(void 0,"PATCH"),g=y(void 0,"DELETE"),w=y(void 0,"HEAD"),E=y(void 0,"OPTIONS"),j=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/bill-items/route",pathname:"/api/bill-items",filename:"route",bundlePath:"app/api/bill-items/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bill-items\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:k,serverHooks:T}=j;function R(){return(0,n.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:k})}},49616:()=>{},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86047:(e,r,t)=>{"use strict";e.exports=t(44870)},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[55,3738,1950,5886,9615],()=>t(48664));module.exports=s})();
//# sourceMappingURL=route.js.map