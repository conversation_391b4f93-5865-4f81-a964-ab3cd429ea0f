try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="61993272-78f2-4114-a240-d90d2cb2c2eb",e._sentryDebugIdIdentifier="sentry-dbid-61993272-78f2-4114-a240-d90d2cb2c2eb")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3912],{2757:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(52880);a(99004);var r=a(90917);function n(e){let{children:t,scrollable:a=!0}=e;return(0,s.jsx)(s.<PERSON>,{children:a?(0,s.jsx)(r.<PERSON>,{className:"h-[calc(100dvh-52px)]",children:(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:t})}):(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:t})})}},25192:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var s=a(52880);a(99004);var r=a(54651);function n(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},29873:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>I});var s=a(52880),r=a(99004),n=a(83405),l=a(29980),i=a(95181),o=a(2757),d=a(62054),c=a(42094),u=a(88151),m=a(68290),x=a(66444),f=a(60664),p=a(26368),g=a(93900),h=a(79254),y=a(68065),v=a(87378),b=a(38406),j=a(90290),N=a(73259),w=a(77362),P=a(56420),D=a(25192),C=a(4629);function k(e,t){let a={patient:"患者",appointment:"预约",treatment:"治疗项目",user:"用户"}[t.toLowerCase()]||t;switch(e){case"create":return"".concat(a,"创建成功！");case"update":return"".concat(a,"更新成功！");case"delete":return"".concat(a,"删除成功！");case"complete":return"".concat(a,"已标记为完成！");case"cancel":return"".concat(a,"取消成功！");case"sync":return"".concat(a,"同步成功！");default:return"".concat(a).concat(e,"完成！")}}var S=a(26230);let A=N.Ik({fullName:N.Yj().min(2,"Full name must be at least 2 characters").max(100,"Full name cannot exceed 100 characters").regex(/^[a-zA-Z\s'-]+$/,"Full name can only contain letters, spaces, hyphens, and apostrophes"),phone:N.Yj().min(10,"Phone number must be at least 10 digits").max(15,"Phone number cannot exceed 15 digits").regex(/^[\+]?[1-9][\d]{0,15}$/,"Please enter a valid phone number (e.g., +********** or **********)"),email:N.Yj().email("Please enter a valid email address (e.g., <EMAIL>)").optional().or(N.eu("")),medicalNotes:N.Yj().max(2e3,"Medical notes cannot exceed 2000 characters").optional()});function z(e){let{open:t,onOpenChange:a,patient:n,onSuccess:i}=e,[o,u]=(0,r.useState)(!1),m=!!n,x=(0,b.mN)({resolver:(0,j.u)(A),defaultValues:{fullName:(null==n?void 0:n.fullName)||"",phone:(null==n?void 0:n.phone)||"",email:(null==n?void 0:n.email)||"",medicalNotes:(null==n?void 0:n.medicalNotes)||""}});r.useEffect(()=>{t&&x.reset({fullName:(null==n?void 0:n.fullName)||"",phone:(null==n?void 0:n.phone)||"",email:(null==n?void 0:n.email)||"",medicalNotes:(null==n?void 0:n.medicalNotes)||""})},[t,n,x]);let f=async e=>{u(!0);let t=C.toast.loading(function(e,t){let a={patient:"患者",appointment:"预约",treatment:"治疗项目",user:"用户"}[t.toLowerCase()]||t;switch(e){case"create":return"正在创建".concat(a,"...");case"update":return"正在更新".concat(a,"...");case"delete":return"正在删除".concat(a,"...");case"load":return"正在加载".concat(a,"...");case"save":return"正在保存".concat(a,"...");case"sync":return"正在同步".concat(a,"...");default:return"正在处理".concat(a,"...")}}(m?"update":"create","patient"));try{let s={fullName:e.fullName,phone:e.phone,email:e.email||void 0,medicalNotes:e.medicalNotes||void 0};m?(await v.fJ.update(n.id,s),C.toast.success(k("update","patient"),{id:t})):(await v.fJ.create(s),C.toast.success(k("create","patient"),{id:t})),null==i||i(),a(!1),x.reset()}catch(a){console.error("Failed to save patient:",a);let e=function(e){var t;if(!navigator.onLine)return"无网络连接。请检查您的网络连接后重试。";if("AbortError"===e.name||(null==(t=e.message)?void 0:t.includes("timeout")))return"请求超时。请重试。";if(e.error||e.message){let t=e.error||e.message;if(t.includes("duplicate key")||t.includes("unique constraint"))return t.includes("phone")?"此电话号码已被注册。请使用不同的电话号码。":t.includes("email")?"此邮箱地址已被注册。请使用不同的邮箱地址。":t.includes("name")?"此名称已被使用。请选择不同的名称。":"此信息已被使用。请检查您的输入后重试。";if(t.includes("validation")||t.includes("invalid"))return"请检查您的输入，确保所有必填字段都正确填写。";if(t.includes("permission")||t.includes("unauthorized")||t.includes("forbidden"))return"您没有权限执行此操作。请联系您的管理员。";if(t.includes("not found")||t.includes("does not exist"))return"找不到请求的项目。它可能已被删除或移动。";if(t.includes("appointment")){if(t.includes("conflict")||t.includes("overlap"))return"此预约时间与其他预约冲突。请选择不同的时间。";if(t.includes("past"))return"无法安排过去的预约。请选择未来的日期和时间。"}if(t.includes("patient")&&t.includes("appointments"))return"无法删除有预约记录的患者。请先取消或完成所有预约。";if(t.includes("treatment")&&t.includes("appointments"))return"无法删除有预约记录的治疗项目。请先取消或完成所有预约。";if(t.length<100&&!t.includes("Error:")&&!t.includes("failed"))return t}if(e.status)switch(e.status){case 400:return"请求无效。请检查您的输入后重试。";case 401:return"您没有权限执行此操作。请登录后重试。";case 403:return"您没有权限执行此操作。";case 404:return"找不到请求的项目。";case 409:return"此操作与现有数据冲突。请检查您的输入。";case 422:return"提供的数据无效。请检查您的输入后重试。";case 429:return"请求过于频繁。请稍等片刻后重试。";case 500:return"服务器错误。请稍后重试。";case 502:case 503:case 504:return"服务暂时不可用。请稍后重试。";default:return"发生了意外错误。请重试。"}return"发生了意外错误。请重试，如果问题持续存在，请联系技术支持。"}(a);C.toast.error(e,{id:t})}finally{u(!1)}};return(0,s.jsx)(w.lG,{open:t,onOpenChange:a,"data-sentry-element":"Dialog","data-sentry-component":"PatientFormDialog","data-sentry-source-file":"patient-form-dialog.tsx",children:(0,s.jsxs)(w.Cf,{className:"sm:max-w-[500px]","data-sentry-element":"DialogContent","data-sentry-source-file":"patient-form-dialog.tsx",children:[(0,s.jsxs)(w.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"patient-form-dialog.tsx",children:[(0,s.jsx)(w.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"patient-form-dialog.tsx",children:m?(0,S.t)("patients.editPatient"):(0,S.t)("patients.newPatient")}),(0,s.jsx)(w.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"patient-form-dialog.tsx",children:m?"更新下方的患者信息。":"填写患者详细信息以创建新记录。"})]}),(0,s.jsx)(P.lV,{...x,"data-sentry-element":"Form","data-sentry-source-file":"patient-form-dialog.tsx",children:(0,s.jsxs)("form",{onSubmit:x.handleSubmit(f),className:"space-y-4",children:[(0,s.jsx)(P.zB,{control:x.control,name:"fullName",render:e=>{let{field:t}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsxs)(P.lR,{children:[(0,S.t)("patients.form.fullName")," *"]}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(c.p,{placeholder:(0,S.t)("patients.form.fullNamePlaceholder"),...t})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"请输入患者的完整法定姓名，与身份证件上的姓名一致"}),(0,s.jsx)(P.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"patient-form-dialog.tsx"}),(0,s.jsx)(P.zB,{control:x.control,name:"phone",render:e=>{let{field:t}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsxs)(P.lR,{children:[(0,S.t)("patients.form.phone")," *"]}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(c.p,{placeholder:(0,S.t)("patients.form.phonePlaceholder"),type:"tel",...t})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"用于预约提醒和更新的主要联系电话"}),(0,s.jsx)(P.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"patient-form-dialog.tsx"}),(0,s.jsx)(P.zB,{control:x.control,name:"email",render:e=>{let{field:t}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsxs)(P.lR,{children:[(0,S.t)("patients.form.email")," (可选)"]}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(c.p,{placeholder:(0,S.t)("patients.form.emailPlaceholder"),type:"email",...t})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"用于预约确认和电子收据"}),(0,s.jsx)(P.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"patient-form-dialog.tsx"}),(0,s.jsx)(l.Bk,{permission:"canEditMedicalNotes","data-sentry-element":"PermissionGate","data-sentry-source-file":"patient-form-dialog.tsx",children:(0,s.jsx)(P.zB,{control:x.control,name:"medicalNotes",render:e=>{let{field:t}=e;return(0,s.jsxs)(P.eI,{children:[(0,s.jsx)(P.lR,{children:(0,S.t)("patients.form.medicalNotes")}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(D.T,{placeholder:(0,S.t)("patients.form.medicalNotesPlaceholder"),className:"min-h-[100px]",...t})}),(0,s.jsx)(P.C5,{}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"机密医疗信息 - 仅限医务人员查看"})]})},"data-sentry-element":"FormField","data-sentry-source-file":"patient-form-dialog.tsx"})}),(0,s.jsxs)(w.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"patient-form-dialog.tsx",children:[(0,s.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>a(!1),disabled:o,"data-sentry-element":"Button","data-sentry-source-file":"patient-form-dialog.tsx",children:(0,S.t)("common.actions.cancel")}),(0,s.jsx)(d.$,{type:"submit",disabled:o,"data-sentry-element":"Button","data-sentry-source-file":"patient-form-dialog.tsx",children:o?"保存中...":m?"更新患者":"创建患者"})]})]})})]})})}var F=a(65674);function I(){let{userId:e,isLoaded:t}=(0,n.d)(),{hasPermission:a}=(0,l.It)(),[b,j]=(0,r.useState)([]),[N,w]=(0,r.useState)(!0),[P,D]=(0,r.useState)(null),[k,A]=(0,r.useState)(""),[I,L]=(0,r.useState)([]),[B,E]=(0,r.useState)(!1),[_,T]=(0,r.useState)(),[J,M]=(0,r.useState)(!1),[O,$]=(0,r.useState)(),[R,V]=(0,r.useState)(!1);t&&!e&&(0,i.redirect)("/auth/sign-in");let G=async()=>{try{w(!0);let e=await v.fJ.getAll({limit:100});j(e.docs),L(e.docs),D(null)}catch(e){console.error("Failed to fetch patients:",e),D((0,S.t)("patients.loadingPatients"))}finally{w(!1)}};(0,r.useEffect)(()=>{e&&G()},[e]);let Y=()=>{T(void 0),E(!0)},q=e=>{T(e),E(!0)},H=async e=>{try{if(V(!0),(await v.RG.getAll({limit:1,where:{patient:{equals:e.id}}})).docs.length>0)return void C.toast.error("无法删除有预约记录的患者。请先取消或完成所有预约。");$(e),M(!0)}catch(e){console.error("Failed to check patient appointments:",e),C.toast.error("验证患者预约失败")}finally{V(!1)}},K=async()=>{if(O){V(!0);try{await v.fJ.delete(O.id),C.toast.success("患者删除成功"),M(!1),$(void 0),G()}catch(e){console.error("Failed to delete patient:",e),C.toast.error("删除患者失败")}finally{V(!1)}}};return((0,r.useEffect)(()=>{k?L(b.filter(e=>e.fullName.toLowerCase().includes(k.toLowerCase())||e.phone.includes(k)||e.email&&e.email.toLowerCase().includes(k.toLowerCase()))):L(b)},[k,b]),!t||N)?(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,S.t)("patients.loadingPatients")})]})})}):(0,s.jsxs)(o.A,{"data-sentry-element":"PageContainer","data-sentry-component":"PatientsPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex flex-1 flex-col space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"size-6","data-sentry-element":"IconUsers","data-sentry-source-file":"page.tsx"}),(0,S.t)("patients.title")]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,S.t)("patients.subtitle")})]}),(0,s.jsx)(l.Bk,{permission:"canCreatePatients","data-sentry-element":"PermissionGate","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(d.$,{className:"flex items-center gap-2",onClick:Y,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(x.A,{className:"size-4","data-sentry-element":"IconPlus","data-sentry-source-file":"page.tsx"}),(0,S.t)("patients.newPatient")]})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative flex-1 max-w-sm",children:[(0,s.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground","data-sentry-element":"IconSearch","data-sentry-source-file":"page.tsx"}),(0,s.jsx)(c.p,{placeholder:(0,S.t)("patients.searchPlaceholder"),value:k,onChange:e=>A(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(u.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"page.tsx",children:[I.length," / ",b.length," ",(0,S.t)("patients.patientsCount")]})]}),P&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-800",children:P})}),!P&&(0,s.jsx)("div",{className:"rounded-lg border",children:(0,s.jsx)("div",{className:"p-4",children:0===I.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(m.A,{className:"size-12 mx-auto text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:k?"未找到患者":"暂无患者注册"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:k?"请尝试调整搜索条件。":"开始注册您的第一位患者。"}),!k&&(0,s.jsx)(l.Bk,{permission:"canCreatePatients",children:(0,s.jsxs)(d.$,{onClick:Y,children:[(0,s.jsx)(x.A,{className:"size-4 mr-2"}),(0,S.t)("patients.newPatient")]})})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:k&&'"'.concat(k,'" 的搜索结果')}),(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:I.map(e=>(0,s.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-lg",children:e.fullName}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["患者编号: ","string"==typeof e.id?e.id.slice(-8):e.id]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>q(e),className:"h-8 w-8 p-0",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})}),(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>H(e),className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",disabled:R,children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(h.A,{className:"size-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"font-medium",children:e.phone})]}),e.email&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(y.A,{className:"size-4 text-muted-foreground"}),(0,s.jsx)("span",{children:e.email})]})]}),(0,s.jsx)(l.Bk,{permission:"canViewMedicalNotes",children:e.medicalNotes&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"病历备注:"}),(0,s.jsx)("p",{className:"text-sm bg-muted p-2 rounded text-muted-foreground line-clamp-2",children:e.medicalNotes})]})}),(0,s.jsx)("div",{className:"pt-2 border-t",children:(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["注册时间: ",new Date(e.createdAt).toLocaleDateString()]})})]})},e.id))})]})})})]}),(0,s.jsx)(z,{open:B,onOpenChange:E,patient:_,onSuccess:()=>{E(!1),T(void 0),G()},"data-sentry-element":"PatientFormDialog","data-sentry-source-file":"page.tsx"}),(0,s.jsx)(F.K,{open:J,onOpenChange:M,title:"删除患者",description:"您确定要删除患者 ".concat(null==O?void 0:O.fullName," 吗？此操作无法撤销。"),confirmText:"删除",variant:"destructive",onConfirm:K,loading:R,"data-sentry-element":"ConfirmationDialog","data-sentry-source-file":"page.tsx"})]})}},42094:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(52880);a(99004);var r=a(54651);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},77362:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>x,L3:()=>f,c7:()=>m,lG:()=>i,rr:()=>p,zM:()=>o});var s=a(52880);a(99004);var r=a(88749),n=a(48086),l=a(54651);function i(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...t,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function o(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...t,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...t,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u(e){let{className:t,children:a,...i}=e;return(0,s.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,s.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[a,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,s.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",t),...a,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},84692:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var s=a(52880);a(99004);var r=a(71002),n=a(54651);function l(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},88151:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var s=a(52880);a(99004);var r=a(50516),n=a(85017),l=a(54651);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:n=!1,...o}=e,d=n?r.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(i({variant:a}),t),...o,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},90917:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,ScrollArea:()=>l});var s=a(52880);a(99004);var r=a(71359),n=a(54651);function l(e){let{className:t,children:a,...l}=e;return(0,s.jsxs)(r.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",t),...l,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,s.jsx)(r.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:a}),(0,s.jsx)(i,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,s.jsx)(r.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function i(e){let{className:t,orientation:a="vertical",...l}=e;return(0,s.jsx)(r.VM,{"data-slot":"scroll-area-scrollbar",orientation:a,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===a&&"h-full w-2.5 border-l border-l-transparent","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent",t),...l,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,s.jsx)(r.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},99031:(e,t,a)=>{Promise.resolve().then(a.bind(a,29873))}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,7905,1359,4089,4629,2090,290,7534,229,3571,9442,4579,9253,7358],()=>t(99031)),_N_E=e.O()}]);