(()=>{var e={};e.id=3271,e.ids=[3271],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},32785:e=>{"use strict";e.exports=import("prettier")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},34869:()=>{},35672:e=>{"use strict";e.exports=require("dns/promises")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},37830:e=>{"use strict";e.exports=require("node:stream/web")},38522:e=>{"use strict";e.exports=require("node:zlib")},40278:(e,t,a)=>{"use strict";a.d(t,{L:()=>te});let r=require("graphql");var n=a.t(r,2);let o=require("graphql/execution/values.js");class i{complexity;context;estimators;includeDirectiveDef;OperationDefinition;options;requestContext;skipDirectiveDef;variableValues;constructor(e,t){if(!("number"==typeof t.maximumComplexity&&t.maximumComplexity>0))throw Error("Maximum query complexity must be a positive number");this.context=e,this.complexity=0,this.options=t,this.includeDirectiveDef=this.context.getSchema().getDirective("include"),this.skipDirectiveDef=this.context.getSchema().getDirective("skip"),this.estimators=t.estimators,this.variableValues={},this.requestContext=t.context,this.OperationDefinition={enter:this.onOperationDefinitionEnter,leave:this.onOperationDefinitionLeave}}createError(){var e,t;return"function"==typeof this.options.createError?this.options.createError(this.options.maximumComplexity,this.complexity):new r.GraphQLError((e=this.options.maximumComplexity,t=this.complexity,`The query exceeds the maximum complexity of ${e}. Actual complexity is ${t}`))}nodeComplexity(e,t){if(e.selectionSet){let a,n={};(t instanceof r.GraphQLObjectType||t instanceof r.GraphQLInterfaceType)&&(n=t.getFields()),a=(0,r.isAbstractType)(t)?this.context.getSchema().getPossibleTypes(t).map(e=>e.name):[t.name];let i=e.selectionSet.selections.reduce((e,i)=>{let s=e,p=!0,c=!1;for(let e of i.directives??[])switch(e.name.value){case"include":{let e=(0,o.getDirectiveValues)(this.includeDirectiveDef,i,this.variableValues||{});"boolean"==typeof e.if&&(p=e.if);break}case"skip":{let e=(0,o.getDirectiveValues)(this.skipDirectiveDef,i,this.variableValues||{});"boolean"==typeof e.if&&(c=e.if)}}if(!p||c)return e;switch(i.kind){case r.Kind.FIELD:{let p,c=n[i.name.value];if(!c)break;let u=(0,r.getNamedType)(c.type);try{p=(0,o.getArgumentValues)(c,i,this.variableValues||{})}catch(t){return this.context.reportError(t),e}let y=0;(0,r.isCompositeType)(u)&&(y=this.nodeComplexity(i,u));let d={type:t,args:p,childComplexity:y,context:this.requestContext,field:c,node:i};if(!this.estimators.find(t=>{let r=t(d);return!("number"!=typeof r||isNaN(r))&&(s=l(r,e,a),!0)}))return this.context.reportError(new r.GraphQLError(`No complexity could be calculated for field ${t.name}.${c.name}. At least one complexity estimator has to return a complexity score.`)),e;break}case r.Kind.FRAGMENT_SPREAD:{let t=this.context.getFragment(i.name.value);if(!t)break;let a=this.context.getSchema().getType(t.typeCondition.name.value);if(!(0,r.isCompositeType)(a))break;let n=this.nodeComplexity(t,a);s=(0,r.isAbstractType)(a)?l(n,e,this.context.getSchema().getPossibleTypes(a).map(e=>e.name)):l(n,e,[a.name]);break}case r.Kind.INLINE_FRAGMENT:{let a=t;if(i.typeCondition&&i.typeCondition.name&&(a=this.context.getSchema().getType(i.typeCondition.name.value),!(0,r.isCompositeType)(a)))break;let n=this.nodeComplexity(i,a);s=(0,r.isAbstractType)(a)?l(n,e,this.context.getSchema().getPossibleTypes(a).map(e=>e.name)):l(n,e,[a.name]);break}default:s=l(this.nodeComplexity(i,t),e,a)}return s},{});return i?Math.max(...Object.values(i),0):NaN}return 0}onOperationDefinitionEnter(e){if("string"==typeof this.options.operationName&&this.options.operationName!==e.name.value)return;let{coerced:t,errors:a}=(0,o.getVariableValues)(this.context.getSchema(),e.variableDefinitions?[...e.variableDefinitions]:[],this.options.variables??{});if(a&&a.length)return void a.forEach(e=>this.context.reportError(e));switch(this.variableValues=t,e.operation){case"mutation":this.complexity+=this.nodeComplexity(e,this.context.getSchema().getMutationType());break;case"query":this.complexity+=this.nodeComplexity(e,this.context.getSchema().getQueryType());break;case"subscription":this.complexity+=this.nodeComplexity(e,this.context.getSchema().getSubscriptionType());break;default:throw Error(`Query complexity could not be calculated for operation of type ${e.operation}`)}}onOperationDefinitionLeave(e){if(("string"!=typeof this.options.operationName||this.options.operationName===e.name.value)&&(this.options.onComplete&&this.options.onComplete(this.complexity),this.complexity>this.options.maximumComplexity))return this.context.reportError(this.createError())}}function l(e,t,a){for(let r of a)Object.prototype.hasOwnProperty.call(t,r)?t[r]+=e:t[r]=e;return t}let s=()=>e=>{if(e.field.extensions){if("number"==typeof e.field.extensions.complexity)return e.childComplexity+e.field.extensions.complexity;else if("function"==typeof e.field.extensions.complexity)return e.field.extensions.complexity(e)}},p=e=>{let t=e&&"number"==typeof e.defaultComplexity?e.defaultComplexity:1;return e=>t+e.childComplexity};var c=a(50026),u=a(50876);let y=["0","1","2","3","4","5","6","7","8","9"],d=e=>{let t=String(e),a=t.substring(0,1);return y.indexOf(a)>-1&&(t=`_${t}`),t.normalize("NFKD").replace(/[\u0300-\u036f]/g,"").replace(/\./g,"_").replace(/-|\//g,"_").replace(/\+/g,"_").replace(/,/g,"_").replace(/\(/g,"_").replace(/\)/g,"_").replace(/'/g,"_").replace(/ /g,"")||"_"},h=(e,t)=>{let a={...e};return t.forEach(({slug:e})=>{let t={...a[e]||{}};delete a[e],a[d(e)]=t}),a},f=e=>new r.GraphQLEnumType({name:"FallbackLocaleInputType",values:[...e.localeCodes,"none"].reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})}),m=e=>new r.GraphQLEnumType({name:"LocaleInputType",values:e.localeCodes.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})});var g=a(47177);let L=require("graphql/language/index.js");function b(e){return e}function Q(e){if("object"!=typeof e||null===e||Array.isArray(e))throw TypeError(`JSONObject cannot represent non-object value: ${e}`);return e}function w(e,t,a){let r=Object.create(null);return t.fields.forEach(t=>{r[t.name.value]=x(e,t.value,a)}),r}function x(e,t,a){switch(t.kind){case L.Kind.BOOLEAN:case L.Kind.STRING:return t.value;case L.Kind.FLOAT:case L.Kind.INT:return parseFloat(t.value);case L.Kind.LIST:return t.values.map(t=>x(e,t,a));case L.Kind.NULL:return null;case L.Kind.OBJECT:return w(e,t,a);case L.Kind.VARIABLE:return a?a[t.name.value]:void 0;default:throw TypeError(`${e} cannot represent value: ${(0,L.print)(t)}`)}}let v=new r.GraphQLScalarType({name:"JSON",description:"The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).",parseLiteral:(e,t)=>x("JSON",e,t),parseValue:b,serialize:b,specifiedByURL:"http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf"}),T=new r.GraphQLScalarType({name:"JSONObject",description:"The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).",parseLiteral:(e,t)=>{if(e.kind!==L.Kind.OBJECT)throw TypeError(`JSONObject cannot represent non-object value: ${(0,L.print)(e)}`);return w("JSONObject",e,t)},parseValue:Q,serialize:Q,specifiedByURL:"http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf"}),q=(e,t)=>t.reduce((t,a)=>{if(!a.hidden&&"ui"!==a.type){if(a.name){let n=d(a.name),o=["create","read","update","delete"].reduce((t,a)=>{let o=a.charAt(0).toUpperCase()+a.slice(1);return{...t,[a]:{type:new r.GraphQLObjectType({name:`${e}_${n}_${o}`,fields:{permission:{type:new r.GraphQLNonNull(r.GraphQLBoolean)}}})}}},{});return a.fields&&(o.fields={type:new r.GraphQLObjectType({name:`${e}_${n}_Fields`,fields:q(`${e}_${n}`,a.fields)})}),{...t,[d(a.name)]:{type:new r.GraphQLObjectType({name:`${e}_${n}`,fields:o})}}}if(!a.name&&a.fields){let r=q(e,a.fields);return{...t,...r}}if("tabs"===a.type)return a.tabs.reduce((t,a)=>({...t,...q(e,a.fields)}),{...t})}return t},{}),G=e=>{let{name:t,entityFields:a,operations:n,scope:o}=e,i=(0,g.x4)(`${t}-${o||""}-Fields`,!0),l={fields:{type:new r.GraphQLObjectType({name:i,fields:q(i,a)})}};return n.forEach(e=>{let a=(0,g.x4)(`${t}-${e}-${o||"Access"}`,!0);l[e]={type:new r.GraphQLObjectType({name:a,fields:{permission:{type:new r.GraphQLNonNull(r.GraphQLBoolean)},where:{type:T}}})}}),l};function k(e){let{type:t,entity:a,scope:n,typeSuffix:o}=e,{slug:i,fields:l,graphQL:s,versions:p}=a,c=[];if(!1===s)return null;if("collection"===t){c=["create","read","update","delete"],a.auth&&"object"==typeof a.auth&&void 0!==a.auth.maxLoginAttempts&&0!==a.auth.maxLoginAttempts&&c.push("unlock"),p&&c.push("readVersions");let e=d(`${i}${o||""}`);return new r.GraphQLObjectType({name:e,fields:G({name:i,entityFields:l,operations:c,scope:n})})}c=["read","update"],a.versions&&c.push("readVersions");let u=d(`${global?.graphQL?.name||i}${o||""}`);return new r.GraphQLObjectType({name:u,fields:G({name:a.graphQL&&a?.graphQL?.name||i,entityFields:a.fields,operations:c,scope:n})})}var N=a(27457),I=a(17791),S=a(69512),A=a(89132),D=a(33164),E=a(49324),O=a(23604),j=a(14814),$=a(6004),C=a(87775),_=a(86802),R=a(19211),M=a(82602),z=a(54966),P=a(64035),F=a(33832),B=a(96911),V=a(59748),J=a(18255),U=a(8082),Z=a(43588),K=a(88580),H=a(49386),W=a(29929),X=a(55301),Y=a(87086);let ee=(e,t)=>d(`${e?`${e}_`:""}${t}`),et=e=>"type"in e&&"group"===e.type?e.fields.some(e=>(0,S.Z7)(e)&&"required"in e&&e.required||et(e)):"fields"in e&&"name"in e&&e.fields.some(e=>et(e)),ea=({type:e,field:t,forceNullable:a,parentIsLocalized:n})=>{let o=t.access&&t.access.read,i=t.admin&&t.admin.condition,l="createdAt"===t.name||"updatedAt"===t.name;return!a&&"required"in t&&t.required&&(!t.localized||n)&&!i&&!o&&!l?new r.GraphQLNonNull(e):e},er={number:r.GraphQLInt,text:r.GraphQLString},en=(e,t)=>{let a=(0,N.L)(t.fields).find(e=>(0,S.Z7)(e)&&"id"===e.name);return a?er[a.type]:er[e]};function eo({name:e,config:t,fields:a,forceNullable:n=!1,graphqlResult:o,parentIsLocalized:i,parentName:l}){let s={array:(e,a)=>{let s=ee(l,(0,g.x4)(a.name,!0)),p=eo({name:s,config:t,fields:a.fields,graphqlResult:o,parentIsLocalized:i||a.localized,parentName:s});return p?(p=new r.GraphQLList(ea({type:p,field:a,forceNullable:n,parentIsLocalized:i})),{...e,[d(a.name)]:{type:p}}):e},blocks:(e,t)=>({...e,[d(t.name)]:{type:v}}),checkbox:(e,t)=>({...e,[d(t.name)]:{type:r.GraphQLBoolean}}),code:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:i})}}),collapsible:(e,t)=>t.fields.reduce((e,t)=>{let a=s[t.type];return a?a(e,t):e},e),date:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:i})}}),email:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:i})}}),group:(e,a)=>{if(!(0,S.Z7)(a))return a.fields.reduce((e,t)=>{let a=s[t.type];return a?a(e,t):e},e);{let n=et(a),s=ee(l,(0,g.x4)(a.name,!0)),p=eo({name:s,config:t,fields:a.fields,graphqlResult:o,parentIsLocalized:i||a.localized,parentName:s});return p?(n&&(p=new r.GraphQLNonNull(p)),{...e,[d(a.name)]:{type:p}}):e}},json:(e,t)=>({...e,[d(t.name)]:{type:ea({type:v,field:t,forceNullable:n,parentIsLocalized:i})}}),number:(e,t)=>{let a="id"===t.name?r.GraphQLInt:r.GraphQLFloat;return{...e,[d(t.name)]:{type:ea({type:!0===t.hasMany?new r.GraphQLList(a):a,field:t,forceNullable:n,parentIsLocalized:i})}}},point:(e,t)=>({...e,[d(t.name)]:{type:ea({type:new r.GraphQLList(r.GraphQLFloat),field:t,forceNullable:n,parentIsLocalized:i})}}),radio:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:i})}}),relationship:(e,a)=>{let n,{relationTo:i}=a;if(Array.isArray(i)){let e=`${ee(l,(0,g.x4)(a.name,!0))}RelationshipInput`;n=new r.GraphQLInputObjectType({name:e,fields:{relationTo:{type:new r.GraphQLEnumType({name:`${e}RelationTo`,values:i.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})})},value:{type:v}}})}else n=en(t.db.defaultIDType,o.collections[i].config);return{...e,[d(a.name)]:{type:a.hasMany?new r.GraphQLList(n):n}}},richText:(e,t)=>({...e,[d(t.name)]:{type:ea({type:v,field:t,forceNullable:n,parentIsLocalized:i})}}),row:(e,t)=>t.fields.reduce((e,t)=>{let a=s[t.type];return a?a(e,t):e},e),select:(e,t)=>{let a=`${ee(l,t.name)}_MutationInput`,o=new r.GraphQLEnumType({name:a,values:t.options.reduce((e,t)=>(0,S.vs)(t)?{...e,[d(t.value)]:{value:t.value}}:{...e,[d(t)]:{value:t}},{})});return o=ea({type:o=t.hasMany?new r.GraphQLList(o):o,field:t,forceNullable:n,parentIsLocalized:i}),{...e,[d(t.name)]:{type:o}}},tabs:(e,a)=>a.tabs.reduce((e,n)=>{if((0,S.pz)(n)){let s=ee(l,(0,g.x4)(n.name,!0)),p=et(a),c=eo({name:s,config:t,fields:n.fields,graphqlResult:o,parentIsLocalized:i||n.localized,parentName:s});return c?(p&&(c=new r.GraphQLNonNull(c)),{...e,[n.name]:{type:c}}):e}return{...e,...n.fields.reduce((e,t)=>{let a=s[t.type];return a?a(e,t):e},e)}},e),text:(e,t)=>({...e,[d(t.name)]:{type:ea({type:!0===t.hasMany?new r.GraphQLList(r.GraphQLString):r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:i})}}),textarea:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:i})}}),upload:(e,a)=>{let n,{relationTo:i}=a;if(Array.isArray(i)){let e=`${ee(l,(0,g.x4)(a.name,!0))}RelationshipInput`;n=new r.GraphQLInputObjectType({name:e,fields:{relationTo:{type:new r.GraphQLEnumType({name:`${e}RelationTo`,values:i.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})})},value:{type:v}}})}else n=en(t.db.defaultIDType,o.collections[i].config);return{...e,[d(a.name)]:{type:a.hasMany?new r.GraphQLList(n):n}}}},p=d(e),c=a.reduce((e,t)=>{let a=s[t.type];return"function"!=typeof a||0===Object.keys(a(e,t)).length?e:{...e,...a(e,t)}},{});return 0===Object.keys(c).length?null:new r.GraphQLInputObjectType({name:`mutation${p}Input`,fields:c})}function ei(e,t){return r.versionInfo.major>=17?new r.GraphQLError(e,t):new r.GraphQLError(e,null==t?void 0:t.nodes,null==t?void 0:t.source,null==t?void 0:t.positions,null==t?void 0:t.path,null==t?void 0:t.originalError,null==t?void 0:t.extensions)}let el=e=>new Date(e),es=e=>e%4==0&&e%100!=0||e%400==0,ep=e=>(e=null==e?void 0:e.toUpperCase(),/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\.\d{1,})?(([Z])|([+|-]([01][0-9]|2[0-3]):[0-5][0-9]))$/.test(e)),ec=e=>{if(!/^(\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01]))$/.test(e))return!1;let t=Number(e.substr(0,4)),a=Number(e.substr(5,2)),r=Number(e.substr(8,2));switch(a){case 2:if(es(t)&&r>29||!es(t)&&r>28)return!1;break;case 4:case 6:case 9:case 11:if(r>30)return!1}return!0},eu=e=>{if(e=null==e?void 0:e.toUpperCase(),!/^(\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60))(\.\d{1,})?(([Z])|([+|-]([01][0-9]|2[0-3]):[0-5][0-9]))$/.test(e))return!1;let t=Date.parse(e);if(t!=t)return!1;let a=e.indexOf("T"),r=e.substr(0,a),n=e.substr(a+1);return ec(r)&&ep(n)},ey=e=>{let t=e.getTime();return t==t},ed=new r.GraphQLScalarType({name:"DateTime",description:"A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.",serialize(e){if(e instanceof Date){if(ey(e))return e;throw ei("DateTime cannot represent an invalid Date instance")}if("string"==typeof e){if(eu(e))return el(e);throw ei(`DateTime cannot represent an invalid date-time-string ${e}.`)}if("number"==typeof e)try{return new Date(e)}catch(t){throw ei("DateTime cannot represent an invalid Unix timestamp "+e)}throw ei("DateTime cannot be serialized from a non string, non numeric or non Date type "+JSON.stringify(e))},parseValue(e){if(e instanceof Date){if(ey(e))return e;throw ei("DateTime cannot represent an invalid Date instance")}if("string"==typeof e){if(eu(e))return el(e);throw ei(`DateTime cannot represent an invalid date-time-string ${e}.`)}throw ei(`DateTime cannot represent non string or Date type ${JSON.stringify(e)}`)},parseLiteral(e){if(e.kind!==r.Kind.STRING)throw ei(`DateTime cannot represent non string or Date type ${"value"in e&&e.value}`,{nodes:e});let{value:t}=e;if(eu(t))return el(t);throw ei(`DateTime cannot represent an invalid date-time-string ${String(t)}.`,{nodes:e})},extensions:{codegenScalarType:"Date | string",jsonSchema:{type:"string",format:"date-time"}}}),eh=(e,t)=>{if("string"!=typeof e)throw ei(`Value is not string: ${e}`,{nodes:t});if(!/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e))throw ei(`Value is not a valid email address: ${e}`,{nodes:t});return e},ef="https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address",em=new r.GraphQLScalarType({name:"EmailAddress",description:"A field whose value conforms to the standard internet email address format as specified in HTML Spec: https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address.",serialize:eh,parseValue:eh,parseLiteral(e){if(e.kind!==r.Kind.STRING)throw ei(`Can only validate strings as email addresses but got a: ${e.kind}`,{nodes:e});return eh(e.value,e)},specifiedByURL:ef,specifiedByUrl:ef,extensions:{codegenScalarType:"string",jsonSchema:{type:"string",format:"email"}}});var eg=a(72259),eL=a(52345),eb=a(24284);let eQ=e=>e.options.reduce((e,t)=>"object"==typeof t?{...e,[d(t.value)]:{value:t.value}}:{...e,[d(t)]:{value:t}},{}),ew=({field:e,forceNullable:t,parentIsLocalized:a})=>{let r=e.access&&e.access.read,n=e.admin&&e.admin.condition;return!(t&&(0,S.Z7)(e)&&"required"in e&&e.required&&(!e.localized||a)&&!n&&!r)};function ex({field:e,...t}){return"name"in e&&d(e.name)!==e.name?{...t,resolve:t=>t[e.name]}:t}let ev={array:({config:e,field:t,forceNullable:a,graphqlResult:n,objectTypeConfig:o,parentIsLocalized:i,parentName:l})=>{let s=t?.interfaceName||ee(l,(0,g.x4)(t.name,!0));if(!n.types.arrayTypes[s]){let r=eT({name:s,config:e,fields:t.fields,forceNullable:ew({field:t,forceNullable:a,parentIsLocalized:i}),graphqlResult:n,parentIsLocalized:t.localized||i,parentName:s});Object.keys(r.getFields()).length&&(n.types.arrayTypes[s]=r)}if(!n.types.arrayTypes[s])return o;let p=new r.GraphQLList(new r.GraphQLNonNull(n.types.arrayTypes[s]));return{...o,[d(t.name)]:ex({type:ea({type:p,field:t,parentIsLocalized:i}),field:t})}},blocks:({config:e,field:t,forceNullable:a,graphqlResult:n,objectTypeConfig:o,parentIsLocalized:i,parentName:l})=>{let s=(t.blockReferences??t.blocks).reduce((t,r)=>{let o="string"==typeof r?r:r.slug;if(!n.types.blockTypes[o]){let t="string"==typeof r?e.blocks.find(e=>e.slug===r):r,o=t?.interfaceName||t?.graphQL?.singularName||(0,g.x4)(t.slug,!0),l=eT({name:o,config:e,fields:[...t.fields,{name:"blockType",type:"text"}],forceNullable:a,graphqlResult:n,parentIsLocalized:i,parentName:o});Object.keys(l.getFields()).length&&(n.types.blockTypes[t.slug]=l)}return n.types.blockTypes[o]&&t.push(n.types.blockTypes[o]),t},[]);if(0===s.length)return o;let p=ee(l,(0,g.x4)(t.name,!0)),c=new r.GraphQLList(new r.GraphQLNonNull(new r.GraphQLUnionType({name:p,resolveType:e=>n.types.blockTypes[e.blockType].name,types:s})));return{...o,[d(t.name)]:ex({type:ea({type:c,field:t,parentIsLocalized:i}),field:t})}},checkbox:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:r.GraphQLBoolean,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),code:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:r.GraphQLString,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),collapsible:({config:e,field:t,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:o,parentIsLocalized:i,parentName:l})=>t.fields.reduce((t,o)=>{let s=ev[o.type];return s?s({config:e,field:o,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:t,parentIsLocalized:i,parentName:l}):t},o),date:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:r})=>({...a,[d(e.name)]:ex({type:ea({type:ed,field:e,forceNullable:t,parentIsLocalized:r}),field:e})}),email:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:r})=>({...a,[d(e.name)]:ex({type:ea({type:em,field:e,forceNullable:t,parentIsLocalized:r}),field:e})}),group:({config:e,field:t,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:o,parentIsLocalized:i,parentName:l})=>{if(!(0,S.Z7)(t))return t.fields.reduce((t,o)=>{let s=ev[o.type];return s?s({config:e,field:o,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:t,parentIsLocalized:i,parentName:l}):t},o);{let n=t?.interfaceName||ee(l,(0,g.x4)(t.name,!0));if(!r.types.groupTypes[n]){let o=eT({name:n,config:e,fields:t.fields,forceNullable:ew({field:t,forceNullable:a,parentIsLocalized:i}),graphqlResult:r,parentIsLocalized:t.localized||i,parentName:n});Object.keys(o.getFields()).length&&(r.types.groupTypes[n]=o)}return r.types.groupTypes[n]?{...o,[d(t.name)]:{type:r.types.groupTypes[n],resolve:(e,a,r)=>({...e[t.name],_id:e._id??e.id})}}:o}},join:({collectionSlug:e,field:t,graphqlResult:a,objectTypeConfig:n,parentName:o})=>{let i=ee(o,(0,g.x4)(t.name,!0)),l={type:new r.GraphQLObjectType({name:i,fields:{docs:{type:new r.GraphQLNonNull(Array.isArray(t.collection)?v:new r.GraphQLList(new r.GraphQLNonNull(a.collections[t.collection].graphQL.type)))},hasNextPage:{type:new r.GraphQLNonNull(r.GraphQLBoolean)}}}),args:{limit:{type:r.GraphQLInt},page:{type:r.GraphQLInt},sort:{type:r.GraphQLString},where:{type:Array.isArray(t.collection)?v:a.collections[t.collection].graphQL.whereInputType}},extensions:{complexity:"number"==typeof t?.graphQL?.complexity?t.graphQL.complexity:10},async resolve(a,r,n){let{collection:o}=t,{limit:i,page:l,sort:s,where:p}=r,{req:c}=n,u=!!(r.draft??n.req.query?.draft),y=t.targetField,d=(0,eg.m)(p,Array.isArray(y.relationTo)?{[t.on]:{equals:{relationTo:e,value:a._id??a.id}}}:{[t.on]:{equals:a._id??a.id}});if(Array.isArray(o))throw Error("GraphQL with array of join.field.collection is not implemented");let{docs:h}=await c.payload.find({collection:o,depth:0,draft:u,fallbackLocale:c.fallbackLocale,limit:"number"==typeof i&&i>0?i+1:0,locale:c.locale,overrideAccess:!1,page:l,pagination:!1,req:c,sort:s,where:d}),f=!1;return"number"==typeof i&&0!==i&&i<h.length&&(f=!0),{docs:f?h.slice(0,-1):h,hasNextPage:0!==i&&i<h.length}}};return{...n,[d(t.name)]:l}},json:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:r})=>({...a,[d(e.name)]:ex({type:ea({type:v,field:e,forceNullable:t,parentIsLocalized:r}),field:e})}),number:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>{let o=e?.name==="id"?r.GraphQLInt:r.GraphQLFloat;return{...a,[d(e.name)]:ex({type:ea({type:e?.hasMany===!0?new r.GraphQLList(new r.GraphQLNonNull(o)):o,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}},point:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:new r.GraphQLList(new r.GraphQLNonNull(r.GraphQLFloat)),field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),radio:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n,parentName:o})=>({...a,[d(e.name)]:ex({type:ea({type:new r.GraphQLEnumType({name:ee(o,e.name),values:eQ(e)}),field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),relationship:({config:e,field:t,forceNullable:a,graphqlResult:n,newlyCreatedBlockType:o,objectTypeConfig:i,parentIsLocalized:l,parentName:s})=>{let p,{relationTo:c}=t,u=Array.isArray(c),y=t.hasMany,h=ee(s,(0,g.x4)(t.name,!0)),f=null,m=e.collections.filter(e=>!1!==e.graphQL);if(Array.isArray(c)){f=new r.GraphQLEnumType({name:`${h}_RelationTo`,values:c.filter(e=>m.some(t=>t.slug===e)).reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})});let e=c.filter(e=>m.some(t=>t.slug===e)).map(e=>n.collections[e]?.graphQL.type);p=new r.GraphQLObjectType({name:`${h}_Relationship`,fields:{relationTo:{type:f},value:{type:new r.GraphQLUnionType({name:h,resolveType:e=>n.collections[e.collection].graphQL.type.name,types:e})}}})}else({type:p}=n.collections[c].graphQL);p=p||o;let L={};(Array.isArray(c)?c:[c]).filter(e=>m.some(t=>t.slug===e)).some(e=>n.collections[e].config.versions?.drafts)&&(L.draft={type:r.GraphQLBoolean}),e.localization&&(L.locale={type:n.types.localeInputType},L.fallbackLocale={type:n.types.fallbackLocaleInputType});let b={type:ea({type:y?new r.GraphQLList(new r.GraphQLNonNull(p)):p,field:t,forceNullable:a,parentIsLocalized:l}),args:L,extensions:{complexity:"number"==typeof t?.graphQL?.complexity?t.graphQL.complexity:10},async resolve(e,a,r){let n=e[t.name],o=a.locale||r.req.locale,i=a.fallbackLocale||r.req.fallbackLocale,l=t.relationTo,s=!!(a.draft??r.req.query?.draft);if(y){let e=[],a=[],l=async(a,n)=>{let l=a,p=t.relationTo;if(u?m.some(e=>p.includes(e.slug)):m.some(e=>p===e.slug)){u&&(p=a.relationTo,l=a.value);let t=await r.req.payloadDataLoader.load((0,eL.h)({collectionSlug:p,currentDepth:0,depth:0,docID:l,draft:s,fallbackLocale:i,locale:o,overrideAccess:!1,showHiddenFields:!1,transactionID:r.req.transactionID}));t&&(u?e[n]={relationTo:p,value:{...t,collection:p}}:e[n]=t)}};return n&&n.forEach((e,t)=>{a.push(l(e,t))}),await Promise.all(a),e}let p=n;if(u&&n&&(p=n.value,l=n.relationTo),p&&m.some(e=>e.slug===l)){let e=await r.req.payloadDataLoader.load((0,eL.h)({collectionSlug:l,currentDepth:0,depth:0,docID:p,draft:s,fallbackLocale:i,locale:o,overrideAccess:!1,showHiddenFields:!1,transactionID:r.req.transactionID}));if(e)return u?{relationTo:l,value:{...e,collection:l}}:e}return null}};return{...i,[d(t.name)]:b}},richText:({config:e,field:t,forceNullable:a,objectTypeConfig:n,parentIsLocalized:o})=>({...n,[d(t.name)]:{type:ea({type:v,field:t,forceNullable:a,parentIsLocalized:o}),args:{depth:{type:r.GraphQLInt}},async resolve(a,r,n){let i=e.defaultDepth;if(void 0!==r.depth&&(i=r.depth),!t?.editor)throw new eb.X(t);if("function"==typeof t?.editor)throw Error("Attempted to access unsanitized rich text editor.");let l=t?.editor;if(l?.graphQLPopulationPromises){let e=[],s=[],p=t?.maxDepth!==void 0&&t?.maxDepth<i?t?.maxDepth:i;l?.graphQLPopulationPromises({context:n,depth:p,draft:r.draft,field:t,fieldPromises:e,findMany:!1,flattenLocales:!1,overrideAccess:!1,parentIsLocalized:o,populationPromises:s,req:n.req,showHiddenFields:!1,siblingDoc:a}),await Promise.all(e),await Promise.all(s)}return a[t.name]}}}),row:({field:e,objectTypeConfig:t,...a})=>e.fields.reduce((e,t)=>{let r=ev[t.type];return r?r({field:t,objectTypeConfig:e,...a}):e},t),select:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n,parentName:o})=>{let i=ee(o,e.name),l=new r.GraphQLEnumType({name:i,values:eQ(e)});return l=ea({type:l=e.hasMany?new r.GraphQLList(new r.GraphQLNonNull(l)):l,field:e,forceNullable:t,parentIsLocalized:n}),{...a,[d(e.name)]:ex({type:l,field:e})}},tabs:({config:e,field:t,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:o,parentIsLocalized:i,parentName:l})=>t.tabs.reduce((t,o)=>{if((0,S.pz)(o)){let n=o?.interfaceName||ee(l,(0,g.x4)(o.name,!0));if(!r.types.groupTypes[n]){let t=eT({name:n,config:e,fields:o.fields,forceNullable:a,graphqlResult:r,parentIsLocalized:o.localized||i,parentName:n});Object.keys(t.getFields()).length&&(r.types.groupTypes[n]=t)}return r.types.groupTypes[n]?{...t,[o.name]:{type:r.types.groupTypes[n],resolve:(e,t,a)=>({...e[o.name],_id:e._id??e.id})}}:t}return{...t,...o.fields.reduce((t,o)=>{let s=ev[o.type];return s?s({config:e,field:o,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:t,parentIsLocalized:i,parentName:l}):t},t)}},o),text:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:!0===e.hasMany?new r.GraphQLList(new r.GraphQLNonNull(r.GraphQLString)):r.GraphQLString,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),textarea:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:r.GraphQLString,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),upload:({config:e,field:t,forceNullable:a,graphqlResult:n,newlyCreatedBlockType:o,objectTypeConfig:i,parentIsLocalized:l,parentName:s})=>{let p,{relationTo:c}=t,u=Array.isArray(c),y=t.hasMany,h=ee(s,(0,g.x4)(t.name,!0)),f=null;if(Array.isArray(c)){f=new r.GraphQLEnumType({name:`${h}_RelationTo`,values:c.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})});let e=c.map(e=>n.collections[e].graphQL.type);p=new r.GraphQLObjectType({name:`${h}_Relationship`,fields:{relationTo:{type:f},value:{type:new r.GraphQLUnionType({name:h,resolveType:e=>n.collections[e.collection].graphQL.type.name,types:e})}}})}else({type:p}=n.collections[c].graphQL);p=p||o;let m={};(Array.isArray(c)?c:[c]).some(e=>n.collections[e].config.versions?.drafts)&&(m.draft={type:r.GraphQLBoolean}),e.localization&&(m.locale={type:n.types.localeInputType},m.fallbackLocale={type:n.types.fallbackLocaleInputType});let L={type:ea({type:y?new r.GraphQLList(new r.GraphQLNonNull(p)):p,field:t,forceNullable:a,parentIsLocalized:l}),args:m,extensions:{complexity:"number"==typeof t?.graphQL?.complexity?t.graphQL.complexity:10},async resolve(e,a,r){let n=e[t.name],o=a.locale||r.req.locale,i=a.fallbackLocale||r.req.fallbackLocale,l=t.relationTo,s=!!(a.draft??r.req.query?.draft);if(y){let e=[],a=[],l=async(a,n)=>{let l=a,p=t.relationTo;u&&(p=a.relationTo,l=a.value);let c=await r.req.payloadDataLoader.load((0,eL.h)({collectionSlug:p,currentDepth:0,depth:0,docID:l,draft:s,fallbackLocale:i,locale:o,overrideAccess:!1,showHiddenFields:!1,transactionID:r.req.transactionID}));c&&(u?e[n]={relationTo:p,value:{...c,collection:p}}:e[n]=c)};return n&&n.forEach((e,t)=>{a.push(l(e,t))}),await Promise.all(a),e}let p=n;if(u&&n&&(p=n.value,l=n.relationTo),p){let e=await r.req.payloadDataLoader.load((0,eL.h)({collectionSlug:l,currentDepth:0,depth:0,docID:p,draft:s,fallbackLocale:i,locale:o,overrideAccess:!1,showHiddenFields:!1,transactionID:r.req.transactionID}));if(e)return u?{relationTo:l,value:{...e,collection:l}}:e}return null}};return{...i,[d(t.name)]:L}}};function eT({name:e,baseFields:t={},collectionSlug:a,config:n,fields:o,forceNullable:i,graphqlResult:l,parentIsLocalized:s,parentName:p}){let c=new r.GraphQLObjectType({name:e,fields:()=>o.reduce((e,t)=>{let r=ev[t.type];return"function"!=typeof r?e:{...e,...r({collectionSlug:a,config:n,field:t,forceNullable:i,graphqlResult:l,newlyCreatedBlockType:c,objectTypeConfig:e,parentIsLocalized:s,parentName:p})}},t)});return c}let eq=(e,t)=>new r.GraphQLObjectType({name:e,fields:{docs:{type:new r.GraphQLNonNull(new r.GraphQLList(new r.GraphQLNonNull(t)))},hasNextPage:{type:new r.GraphQLNonNull(r.GraphQLBoolean)},hasPrevPage:{type:new r.GraphQLNonNull(r.GraphQLBoolean)},limit:{type:new r.GraphQLNonNull(r.GraphQLInt)},nextPage:{type:r.GraphQLInt},offset:{type:r.GraphQLInt},page:{type:new r.GraphQLNonNull(r.GraphQLInt)},pagingCounter:{type:new r.GraphQLNonNull(r.GraphQLInt)},prevPage:{type:r.GraphQLInt},totalDocs:{type:new r.GraphQLNonNull(r.GraphQLInt)},totalPages:{type:new r.GraphQLNonNull(r.GraphQLInt)}}}),eG=({field:e,nestedFieldName2:t,parentName:a})=>{let r=((0,S.Z7)(e)?e.name:void 0)||t;return"tabs"===e.type?e.tabs.reduce((e,t)=>(e.push(...eG({field:{...t,type:"name"in t?"group":"row"},nestedFieldName2:r,parentName:a})),e),[]):e.fields.reduce((e,t)=>{if(!(0,S.aO)(t)){if(!(0,S.Z7)(t))return[...e,...eG({field:t,nestedFieldName2:r,parentName:a})];let n=(0,S.Z7)(t)?`${r?`${r}__`:""}${t.name}`:void 0,o=eE({nestedFieldName:r,parentName:a})[t.type];if(o){let a=o({...t,name:n});return Array.isArray(a)?[...e,...a]:[...e,{type:a,key:n}]}}return e},[])},ek={comparison:["greater_than_equal","greater_than","less_than_equal","less_than"],contains:["in","not_in","all"],equality:["equals","not_equals"],geo:["near"],geojson:["within","intersects"],partial:["like","contains"]},eN=new r.GraphQLInputObjectType({name:"GeoJSONObject",fields:{type:{type:r.GraphQLString},coordinates:{type:v}}}),eI={checkbox:{operators:[...ek.equality.map(e=>({name:e,type:r.GraphQLBoolean}))]},code:{operators:[...[...ek.equality,...ek.partial].map(e=>({name:e,type:r.GraphQLString}))]},date:{operators:[...[...ek.equality,...ek.comparison,"like"].map(e=>({name:e,type:ed}))]},email:{operators:[...[...ek.equality,...ek.partial,...ek.contains].map(e=>({name:e,type:em}))]},json:{operators:[...[...ek.equality,...ek.partial,...ek.geojson].map(e=>({name:e,type:v}))]},number:{operators:[...[...ek.equality,...ek.comparison].map(e=>({name:e,type:e=>e?.name==="id"?r.GraphQLInt:r.GraphQLFloat}))]},point:{operators:[...[...ek.equality,...ek.comparison,...ek.geo].map(e=>({name:e,type:new r.GraphQLList(r.GraphQLFloat)})),...ek.geojson.map(e=>({name:e,type:eN}))]},radio:{operators:[...[...ek.equality,...ek.partial].map(e=>({name:e,type:(e,t)=>new r.GraphQLEnumType({name:`${ee(t,e.name)}_Input`,values:e.options.reduce((e,t)=>(0,S.vs)(t)?{...e,[d(t.value)]:{value:t.value}}:{...e,[d(t)]:{value:t}},{})})}))]},relationship:{operators:[...[...ek.equality,...ek.contains].map(e=>({name:e,type:v}))]},richText:{operators:[...[...ek.equality,...ek.partial].map(e=>({name:e,type:v}))]},select:{operators:[...[...ek.equality,...ek.contains].map(e=>({name:e,type:(e,t)=>new r.GraphQLEnumType({name:`${ee(t,e.name)}_Input`,values:e.options.reduce((e,t)=>(0,S.vs)(t)?{...e,[d(t.value)]:{value:t.value}}:{...e,[d(t)]:{value:t}},{})})}))]},text:{operators:[...[...ek.equality,...ek.partial,...ek.contains].map(e=>({name:e,type:r.GraphQLString}))]},textarea:{operators:[...[...ek.equality,...ek.partial].map(e=>({name:e,type:r.GraphQLString}))]},upload:{operators:[...[...ek.equality,...ek.contains].map(e=>({name:e,type:v}))]}},eS=["in","not_in","all"],eA={},eD=(e,t)=>{if(!eI?.[e.type])throw Error(`Error: ${e.type} has no defaults configured.`);let a=`${ee(t,e.name)}_operator`,n=[...eI[e.type].operators];return"required"in e&&e.required||n.push({name:"exists",type:n[0].type}),new r.GraphQLInputObjectType({name:a,fields:n.reduce((a,n)=>{let o="function"==typeof n.type?n.type(e,t):n.type;return"function"==typeof n.type&&"name"in o&&(eA[o.name]?o=eA[o.name]:eA[o.name]=o),eS.includes(n.name)?o=new r.GraphQLList(o):"exists"===n.name&&(o=r.GraphQLBoolean),{...a,[n.name]:{type:o}}},{})})},eE=({nestedFieldName:e,parentName:t})=>({array:a=>eG({field:a,nestedFieldName2:e,parentName:t}),checkbox:e=>({type:eD(e,t)}),code:e=>({type:eD(e,t)}),collapsible:a=>eG({field:a,nestedFieldName2:e,parentName:t}),date:e=>({type:eD(e,t)}),email:e=>({type:eD(e,t)}),group:a=>eG({field:a,nestedFieldName2:e,parentName:t}),json:e=>({type:eD(e,t)}),number:e=>({type:eD(e,t)}),point:e=>({type:eD(e,t)}),radio:e=>({type:eD(e,t)}),relationship:e=>Array.isArray(e.relationTo)?{type:new r.GraphQLInputObjectType({name:`${ee(t,e.name)}_Relation`,fields:{relationTo:{type:new r.GraphQLEnumType({name:`${ee(t,e.name)}_Relation_RelationTo`,values:e.relationTo.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})})},value:{type:v}}})}:{type:eD(e,t)},richText:e=>({type:eD(e,t)}),row:a=>eG({field:a,nestedFieldName2:e,parentName:t}),select:e=>({type:eD(e,t)}),tabs:a=>eG({field:a,nestedFieldName2:e,parentName:t}),text:e=>({type:eD(e,t)}),textarea:e=>({type:eD(e,t)}),upload:e=>Array.isArray(e.relationTo)?{type:new r.GraphQLInputObjectType({name:`${ee(t,e.name)}_Relation`,fields:{relationTo:{type:new r.GraphQLEnumType({name:`${ee(t,e.name)}_Relation_RelationTo`,values:e.relationTo.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})})},value:{type:v}}})}:{type:eD(e,t)}}),eO=({name:e,fields:t,parentName:a})=>{let n=(0,N.L)(t).find(e=>(0,S.Z7)(e)&&"id"===e.name),o=t.reduce((e,t)=>{if(!(0,S.aO)(t)&&!t.hidden){let r=eE({parentName:a})[t.type];if(r){let a=r(t);return(0,S.sd)(t)||"tabs"===t.type?{...e,...a.reduce((e,t)=>({...e,[d(t.key)]:t.type}),{})}:{...e,[d(t.name)]:a}}}return e},{});n||(o.id={type:eD({name:"id",type:"text"},a)});let i=d(e),l={AND:{type:new r.GraphQLList(new r.GraphQLInputObjectType({name:`${i}_where_and`,fields:()=>({...o,...l})}))},OR:{type:new r.GraphQLList(new r.GraphQLInputObjectType({name:`${i}_where_or`,fields:()=>({...o,...l})}))}};return new r.GraphQLInputObjectType({name:`${i}_where`,fields:{...o,...l}})};var ej=a(29392),e$=a(15796),eC=a(55920),e_=a(989),eR=a(21725),eM=a(19324),ez=a(56960),eP=a(16436);let{singular:eF}=ej;function eB(e){for(let t in e)e[t].resolve&&(e[t].resolve=function(e){return(t,a,r,n)=>e(t,a,{...r,req:(0,c.i)(r.req,"transactionID")},n)}(e[t].resolve));return e}let eV=e=>({Field(t){("__schema"===t.name.value||"__type"===t.name.value)&&e.reportError(new r.GraphQLError("GraphQL introspection is not allowed, but the query contained __schema or __type",{nodes:[t]}))}});function eJ(e){return"object"==typeof e&&null!==e}function eU(e){if(!Array.isArray(e)||"string"!=typeof e[0]&&null!==e[0]||!eJ(e[1]))return!1;let t=e[1];return(!t.status||"number"==typeof t.status)&&(!t.statusText||"string"==typeof t.statusText)&&(!t.headers||!!eJ(t.headers))}async function eZ(e){var t,a;let r=e.method;if("GET"!==r&&"POST"!==r)return[null,{status:405,statusText:"Method Not Allowed",headers:{allow:"GET, POST"}}];let[n,o="charset=utf-8"]=(eH(e,"content-type")||"").replace(/\s/g,"").toLowerCase().split(";"),i={};switch(!0){case"GET"===r:try{let[,r]=e.url.split("?"),n=new URLSearchParams(r);i.operationName=null!=(t=n.get("operationName"))?t:void 0,i.query=null!=(a=n.get("query"))?a:void 0;let o=n.get("variables");o&&(i.variables=JSON.parse(o));let l=n.get("extensions");l&&(i.extensions=JSON.parse(l))}catch(e){throw Error("Unparsable URL")}break;case"POST"===r&&"application/json"===n&&"charset=utf-8"===o:{let t;if(!e.body)throw Error("Missing body");try{let a="function"==typeof e.body?await e.body():e.body;t="string"==typeof a?JSON.parse(a):a}catch(e){throw Error("Unparsable JSON body")}if(!eJ(t))throw Error("JSON body must be an object");i.operationName=t.operationName,i.query=t.query,i.variables=t.variables,i.extensions=t.extensions;break}default:return[null,{status:415,statusText:"Unsupported Media Type"}]}if(null==i.query)throw Error("Missing query");if("string"!=typeof i.query)throw Error("Invalid query");if(null!=i.variables&&("object"!=typeof i.variables||Array.isArray(i.variables)))throw Error("Invalid variables");if(null!=i.operationName&&"string"!=typeof i.operationName)throw Error("Invalid operationName");if(null!=i.extensions&&("object"!=typeof i.extensions||Array.isArray(i.extensions)))throw Error("Invalid extensions");return i}function eK(e,t,a){if(e instanceof Error&&!eX(e))return[JSON.stringify({errors:[a(e)]},eY),{status:400,statusText:"Bad Request",headers:{"content-type":"application/json; charset=utf-8"}}];let r=eX(e)?[e]:eW(e)?e:null;return r?[JSON.stringify({errors:r.map(a)},eY),Object.assign(Object.assign({},"application/json"===t?{status:200,statusText:"OK"}:{status:400,statusText:"Bad Request"}),{headers:{"content-type":"application/json"===t?"application/json; charset=utf-8":"application/graphql-response+json; charset=utf-8"}})]:[JSON.stringify("errors"in e&&e.errors?Object.assign(Object.assign({},e),{errors:e.errors.map(a)}):e,eY),{status:200,statusText:"OK",headers:{"content-type":"application/json"===t?"application/json; charset=utf-8":"application/graphql-response+json; charset=utf-8"}}]}function eH(e,t){return"function"==typeof e.headers.get?e.headers.get(t):Object(e.headers)[t]}function eW(e){return Array.isArray(e)&&e.length>0&&e.some(eX)}function eX(e){return e instanceof r.GraphQLError}function eY(e,t){return t instanceof Error&&!eX(t)?{message:t.message}:t}var e0=a(98607),e1=a(30713),e4=a(54546),e9=a(75171),e7=a(76657),e2=a(94334),e8=a(43675);let e3=async({err:e,payload:t,req:a})=>{let r=e.originalError.status||e0.h.INTERNAL_SERVER_ERROR,n=e.message;(0,e1.v)({err:e,payload:t}),t.config.debug||r!==e0.h.INTERNAL_SERVER_ERROR||(n="Something went wrong.");let o={extensions:{name:e?.originalError?.name||void 0,data:e&&e.originalError&&e.originalError.data||void 0,stack:t.config.debug?e.stack:void 0,statusCode:r},locations:e.locations,message:n,path:e.path};return await t.config.hooks.afterError?.reduce(async(t,r)=>{await t;let n=await r({context:a.context,error:e,graphqlResult:o,req:a});n&&(o=n.graphqlResult||o)},Promise.resolve()),o},e5=global._payload_graphql;e5||(e5=global._payload_graphql={graphql:null,promise:null});let e6=async e=>{if(e5.graphql)return e5.graphql;if(!e5.promise){let t=await e;e5.promise=new Promise(e=>{let a=function(e){let t={collections:e.collections.reduce((e,t)=>(e[t.slug]={config:t},e),{}),globals:{config:e.globals},Mutation:{name:"Mutation",fields:{}},Query:{name:"Query",fields:{}},types:{arrayTypes:{},blockInputTypes:{},blockTypes:{},groupTypes:{},tabTypes:{}}};if(e.localization&&(t.types.localeInputType=m(e.localization),t.types.fallbackLocaleInputType=f(e.localization)),!function({config:e,graphqlResult:t}){Object.keys(t.collections).forEach(a=>{var n;let o,i,l=t.collections[a],{config:s,config:{fields:p,graphQL:u={},versions:y}}=l;if(!u)return;let h=(0,g.EI)(l.config.slug);(o=u.singularName?(0,g.x4)(u.singularName,!0):h.singular)===(i=u.pluralName?(0,g.x4)(u.pluralName,!0):h.plural)&&(i=`all${o}`),l.graphQL={};let f=(0,N.L)(p).findIndex(e=>(0,S.Z7)(e)&&"id"===e.name)>-1,m=en(e.db.defaultIDType,s),L={},b=[...p];f||(L.id={type:new r.GraphQLNonNull(m)},b.push({name:"id",type:e.db.defaultIDType}));let Q=!!y?.drafts;l.graphQL.type=eT({name:o,baseFields:L,collectionSlug:s.slug,config:e,fields:p,forceNullable:Q,graphqlResult:t,parentName:o}),l.graphQL.paginatedType=eq(i,l.graphQL.type),l.graphQL.whereInputType=eO({name:o,fields:b,parentName:o});let w=[...p];s.auth&&(!s.auth.disableLocalStrategy||"object"==typeof s.auth.disableLocalStrategy&&s.auth.disableLocalStrategy.optionalPassword)&&w.push({name:"password",type:"text",label:"Password",required:!("object"==typeof s.auth.disableLocalStrategy&&s.auth.disableLocalStrategy.optionalPassword)});let x=w;e.db.allowIDOnCreate&&!s.flattenedFields.some(e=>"id"===e.name)&&(x=[...x,{name:"id",type:e.db.defaultIDType}]);let v=eo({name:o,config:e,fields:x,graphqlResult:t,parentIsLocalized:!1,parentName:o});v&&(l.graphQL.mutationInputType=new r.GraphQLNonNull(v));let T=eo({name:`${o}Update`,config:e,fields:w.filter(e=>!((0,S.Z7)(e)&&"id"===e.name)),forceNullable:!0,graphqlResult:t,parentIsLocalized:!1,parentName:`${o}Update`});T&&(l.graphQL.updateMutationInputType=new r.GraphQLNonNull(T));let q="object"!=typeof s.graphQL||!s.graphQL.disableQueries,G="object"!=typeof s.graphQL||!s.graphQL.disableMutations;if(q&&(t.Query.fields[o]={type:l.graphQL.type,args:{id:{type:new r.GraphQLNonNull(m)},draft:{type:r.GraphQLBoolean},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let i=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof i&&(r.query.draft=String(i)),a.req=r;let s={id:t.id,collection:l,depth:0,draft:t.draft,req:(0,c.i)(r,"transactionID")};return await (0,K.$)(s)}},t.Query.fields[i]={type:eq(i,l.graphQL.type),args:{draft:{type:r.GraphQLBoolean},where:{type:l.graphQL.whereInputType},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{},limit:{type:r.GraphQLInt},page:{type:r.GraphQLInt},pagination:{type:r.GraphQLBoolean},sort:{type:r.GraphQLString}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;(r=(0,c.i)(r,["locale","fallbackLocale","transactionID"])).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let i=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof i&&(r.query.draft=String(i)),a.req=r;let s={collection:l,depth:0,draft:t.draft,limit:t.limit,page:t.page,pagination:t.pagination,req:r,sort:t.sort,where:t.where};return await (0,Z.L)(s)}},t.Query.fields[`count${i}`]={type:new r.GraphQLObjectType({name:`count${i}`,fields:{totalDocs:{type:r.GraphQLInt}}}),args:{draft:{type:r.GraphQLBoolean},where:{type:l.graphQL.whereInputType},...e.localization?{locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=o,a.req=r;let i={collection:l,req:(0,c.i)(r,"transactionID"),where:t.where};return await (0,F.R)(i)}},t.Query.fields[`docAccess${o}`]={type:k({type:"collection",entity:s,scope:"docAccess",typeSuffix:"DocAccess"}),args:{id:{type:new r.GraphQLNonNull(m)}},resolve:async function(e,t,a){return(0,J.O)({id:t.id,collection:l,req:(0,c.i)(a.req,"transactionID")})}}),G&&(t.Mutation.fields[`create${o}`]={type:l.graphQL.type,args:{...v?{data:{type:l.graphQL.mutationInputType}}:{},draft:{type:r.GraphQLBoolean},...e.localization?{locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){return t.locale&&(a.req.locale=t.locale),await (0,B.k)({collection:l,data:t.data,depth:0,draft:t.draft,req:(0,c.i)(a.req,"transactionID")})}},t.Mutation.fields[`update${o}`]={type:l.graphQL.type,args:{id:{type:new r.GraphQLNonNull(m)},autosave:{type:r.GraphQLBoolean},...T?{data:{type:l.graphQL.updateMutationInputType}}:{},draft:{type:r.GraphQLBoolean},...e.localization?{locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let i=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof i&&(r.query.draft=String(i)),a.req=r;let s={id:t.id,autosave:t.autosave,collection:l,data:t.data,depth:0,draft:t.draft,req:(0,c.i)(r,"transactionID")};return await (0,Y.Z)(s)}},t.Mutation.fields[`delete${o}`]={type:l.graphQL.type,args:{id:{type:new r.GraphQLNonNull(m)}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let i=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof i&&(r.query.draft=String(i)),a.req=r;let s={id:t.id,collection:l,depth:0,req:(0,c.i)(r,"transactionID")};return await (0,V.l)(s)}},!0!==s.disableDuplicate&&(t.Mutation.fields[`duplicate${o}`]={type:l.graphQL.type,args:{id:{type:new r.GraphQLNonNull(m)},...v?{data:{type:l.graphQL.mutationInputType}}:{}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;return r.locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,a.req=r,await (0,U.h)({id:t.id,collection:l,data:t.data,depth:0,draft:t.draft,req:(0,c.i)(r,"transactionID")})}})),s.versions){let a="text"===e.db.defaultIDType?r.GraphQLString:r.GraphQLInt,n=[...(0,I.c)(e,s),{name:"id",type:e.db.defaultIDType},{name:"createdAt",type:"date",label:"Created At"},{name:"updatedAt",type:"date",label:"Updated At"}];l.graphQL.versionType=eT({name:`${o}Version`,collectionSlug:s.slug,config:e,fields:n,forceNullable:Q,graphqlResult:t,parentName:`${o}Version`}),q&&(t.Query.fields[`version${d(o)}`]={type:l.graphQL.versionType,args:{id:{type:a},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,a.req=r;let i={id:t.id,collection:l,depth:0,req:(0,c.i)(r,"transactionID")};return await (0,H.L)(i)}},t.Query.fields[`versions${i}`]={type:eq(`versions${d(i)}`,l.graphQL.versionType),args:{where:{type:eO({name:`versions${o}`,fields:n,parentName:`versions${o}`})},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{},limit:{type:r.GraphQLInt},page:{type:r.GraphQLInt},pagination:{type:r.GraphQLBoolean},sort:{type:r.GraphQLString}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let i=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof i&&(r.query.draft=String(i)),a.req=r;let s={collection:l,depth:0,limit:t.limit,page:t.page,pagination:t.pagination,req:(0,c.i)(r,"transactionID"),sort:t.sort,where:t.where};return await (0,W.s)(s)}}),G&&(t.Mutation.fields[`restoreVersion${d(o)}`]={type:l.graphQL.type,args:{id:{type:a},draft:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let r={id:t.id,collection:l,depth:0,draft:t.draft,req:(0,c.i)(a.req,"transactionID")};return await (0,X.c)(r)}})}if(s.auth){let i=s.auth.disableLocalStrategy||s.auth.loginWithUsername&&!s.auth.loginWithUsername.allowEmailLogin&&!s.auth.loginWithUsername.requireEmail?[]:[{name:"email",type:"email",required:!0}];if((l.graphQL.JWT=eT({name:d(`${a}JWT`),config:e,fields:[...s.fields.filter(e=>(0,S.Z7)(e)&&e.saveToJWT),...i,{name:"collection",type:"text",required:!0}],graphqlResult:t,parentName:d(`${a}JWT`)}),q&&(t.Query.fields[`me${o}`]={type:new r.GraphQLObjectType({name:d(`${a}Me`),fields:{collection:{type:r.GraphQLString},exp:{type:r.GraphQLInt},strategy:{type:r.GraphQLString},token:{type:r.GraphQLString},user:{type:l.graphQL.type}}}),resolve:async function(e,t,a){let r={collection:l,currentToken:(0,C.p)(a.req),depth:0,req:(0,c.i)(a.req,"transactionID")},n=await (0,_.M)(r);return l.config.auth.removeTokenFromResponses&&delete n.token,n}},t.Query.fields[`initialized${o}`]={type:r.GraphQLBoolean,resolve:(n=l.config.slug,async function(e,t,a){let r={collection:n,req:(0,c.i)(a.req,"transactionID")};return(0,E.M)(r)})}),G)&&(t.Mutation.fields[`refreshToken${o}`]={type:new r.GraphQLObjectType({name:d(`${a}Refreshed${o}`),fields:{exp:{type:r.GraphQLInt},refreshedToken:{type:r.GraphQLString},strategy:{type:r.GraphQLString},user:{type:l.graphQL.JWT}}}),resolve:async function(e,t,a){let r={collection:l,depth:0,req:(0,c.i)(a.req,"transactionID")},n=await (0,R.r)(r),o=(0,j.IS)({collectionAuthConfig:l.config.auth,cookiePrefix:a.req.payload.config.cookiePrefix,token:n.refreshedToken});return a.headers["Set-Cookie"]=o,l.config.auth.removeTokenFromResponses&&delete n.refreshedToken,n}},t.Mutation.fields[`logout${o}`]={type:r.GraphQLString,args:{allSessions:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let r={allSessions:t.allSessions,collection:l,req:(0,c.i)(a.req,"transactionID")},n=await (0,$.u)(r),o=(0,j.DN)({collectionAuthConfig:l.config.auth,config:a.req.payload.config,cookiePrefix:a.req.payload.config.cookiePrefix});return a.headers["Set-Cookie"]=o,n}},!s.auth.disableLocalStrategy)){let e={},{canLoginWithEmail:n,canLoginWithUsername:i}=(0,A.Y)(s.auth.loginWithUsername);n&&(e.email={type:new r.GraphQLNonNull(r.GraphQLString)}),i&&(e.username={type:new r.GraphQLNonNull(r.GraphQLString)}),s.auth.maxLoginAttempts>0&&(t.Mutation.fields[`unlock${o}`]={type:new r.GraphQLNonNull(r.GraphQLBoolean),args:e,resolve:async function(e,t,a){let r={collection:l,data:{email:t.email,username:t.username},req:(0,c.i)(a.req,"transactionID")};return await (0,z.k)(r)}}),t.Mutation.fields[`login${o}`]={type:new r.GraphQLObjectType({name:d(`${a}LoginResult`),fields:{exp:{type:r.GraphQLInt},token:{type:r.GraphQLString},user:{type:l.graphQL.type}}}),args:{...e,password:{type:r.GraphQLString}},resolve:async function(e,t,a){let r={collection:l,data:{email:t.email,password:t.password,username:t.username},depth:0,req:(0,c.i)(a.req,"transactionID")},n=await (0,O.k)(r),o=(0,j.IS)({collectionAuthConfig:l.config.auth,cookiePrefix:a.req.payload.config.cookiePrefix,token:n.token});return a.headers["Set-Cookie"]=o,l.config.auth.removeTokenFromResponses&&delete n.token,n}},t.Mutation.fields[`forgotPassword${o}`]={type:new r.GraphQLNonNull(r.GraphQLBoolean),args:{disableEmail:{type:r.GraphQLBoolean},expiration:{type:r.GraphQLInt},...e},resolve:async function(e,t,a){let r={collection:l,data:{email:t.email,username:t.username},disableEmail:t.disableEmail,expiration:t.expiration,req:(0,c.i)(a.req,"transactionID")};return await (0,D.g)(r),!0}},t.Mutation.fields[`resetPassword${o}`]={type:new r.GraphQLObjectType({name:d(`${a}ResetPassword`),fields:{token:{type:r.GraphQLString},user:{type:l.graphQL.type}}}),args:{password:{type:r.GraphQLString},token:{type:r.GraphQLString}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let r={api:"GraphQL",collection:l,data:t,depth:0,req:(0,c.i)(a.req,"transactionID")},n=await (0,M.q)(r),o=(0,j.IS)({collectionAuthConfig:l.config.auth,cookiePrefix:a.req.payload.config.cookiePrefix,token:n.token});return a.headers["Set-Cookie"]=o,l.config.auth.removeTokenFromResponses&&delete n.token,n}},t.Mutation.fields[`verifyEmail${o}`]={type:r.GraphQLBoolean,args:{token:{type:r.GraphQLString}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let r={api:"GraphQL",collection:l,req:(0,c.i)(a.req,"transactionID"),token:t.token};return await (0,P.p)(r)}}}}})}({config:e,graphqlResult:t}),!function({config:e,graphqlResult:t}){Object.keys(t.globals.config).forEach(a=>{let n=t.globals.config[a],{fields:o,graphQL:i,versions:l}=n;if(!1===i)return;let s=i?.name?i.name:eF((0,g.x4)(n.slug,!0)),p=!!l?.drafts;t.globals.graphQL||(t.globals.graphQL={});let u=eo({name:s,config:e,fields:o,graphqlResult:t,parentIsLocalized:!1,parentName:s});t.globals.graphQL[a]={type:eT({name:s,config:e,fields:o,forceNullable:p,graphqlResult:t,parentName:s}),mutationInputType:u?new r.GraphQLNonNull(u):null};let y="object"!=typeof n.graphQL||!n.graphQL.disableQueries,h="object"!=typeof n.graphQL||!n.graphQL.disableMutations;if(y&&(t.Query.fields[s]={type:t.globals.graphQL[a].type,args:{draft:{type:r.GraphQLBoolean},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let{slug:r}=n,o={slug:r,depth:0,draft:t.draft,globalConfig:n,req:(0,c.i)(a.req,"transactionID")};return await (0,e_.j)(o)}},t.Query.fields[`docAccess${s}`]={type:k({type:"global",entity:n,scope:"docAccess",typeSuffix:"DocAccess"}),resolve:async function(e,t){return(0,eC.O)({globalConfig:n,req:(0,c.i)(t.req,"transactionID")})}}),h&&(t.Mutation.fields[`update${s}`]={type:t.globals.graphQL[a].type,args:{...u?{data:{type:t.globals.graphQL[a].mutationInputType}}:{},draft:{type:r.GraphQLBoolean},...e.localization?{locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let{slug:r}=n,o={slug:r,data:t.data,depth:0,draft:t.draft,globalConfig:n,req:(0,c.i)(a.req,"transactionID")};return await (0,eP.L)(o)}}),n.versions){let o="number"===e.db.defaultIDType?r.GraphQLInt:r.GraphQLString,i=[...(0,e$.p)(e,n),{name:"id",type:e.db.defaultIDType},{name:"createdAt",type:"date",label:"Created At"},{name:"updatedAt",type:"date",label:"Updated At"}];t.globals.graphQL[a].versionType=eT({name:`${s}Version`,config:e,fields:i,forceNullable:p,graphqlResult:t,parentName:`${s}Version`}),y&&(t.Query.fields[`version${d(s)}`]={type:t.globals.graphQL[a].versionType,args:{id:{type:o},draft:{type:r.GraphQLBoolean},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let r={id:t.id,depth:0,draft:t.draft,globalConfig:n,req:(0,c.i)(a.req,"transactionID")};return await (0,eR.L)(r)}},t.Query.fields[`versions${s}`]={type:eq(`versions${d(s)}`,t.globals.graphQL[a].versionType),args:{where:{type:eO({name:`versions${s}`,fields:i,parentName:`versions${s}`})},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{},limit:{type:r.GraphQLInt},page:{type:r.GraphQLInt},pagination:{type:r.GraphQLBoolean},sort:{type:r.GraphQLString}},resolve:async function(e,t,a){let r={depth:0,globalConfig:n,limit:t.limit,page:t.page,pagination:t.pagination,req:(0,c.i)(a.req,"transactionID"),sort:t.sort,where:t.where};return await (0,eM.s)(r)}}),h&&(t.Mutation.fields[`restoreVersion${d(s)}`]={type:t.globals.graphQL[a].type,args:{id:{type:o},draft:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let r={id:t.id,depth:0,draft:t.draft,globalConfig:n,req:(0,c.i)(a.req,"transactionID")};return await (0,ez.c)(r)}})}})}({config:e,graphqlResult:t}),t.Query.fields.Access={type:function(e){let t={canAccessAdmin:{type:new r.GraphQLNonNull(r.GraphQLBoolean)}};return Object.values(e.collections).forEach(e=>{if(!1===e.graphQL)return;let a=k({type:"collection",entity:e,typeSuffix:"Access"});t[d(e.slug)]={type:a}}),Object.values(e.globals).forEach(e=>{if(!1===e.graphQL)return;let a=k({type:"global",entity:e,typeSuffix:"Access"});t[d(e.slug)]={type:a}}),new r.GraphQLObjectType({name:"Access",fields:t})}(e),resolve:async function(t,a,r){let n={req:(0,c.i)(r.req,"transactionID")},o=await (0,u.m)(n);return{...o,...h(o.collections,e.collections),...h(o.globals,e.globals)}}},"function"==typeof e.graphQL.queries){let a=e.graphQL.queries(n,{...t,config:e});t.Query={...t.Query,fields:{...t.Query.fields,...eB(a||{})}}}if("function"==typeof e.graphQL.mutations){let a=e.graphQL.mutations(n,{...t,config:e});t.Mutation={...t.Mutation,fields:{...t.Mutation.fields,...eB(a||{})}}}let a=new r.GraphQLObjectType(t.Query),o=new r.GraphQLObjectType(t.Mutation);return{schema:new r.GraphQLSchema({mutation:o,query:a}),validationRules:t=>{var a;return[(a={estimators:[s(),p({defaultComplexity:1})],maximumComplexity:e.graphQL.maxComplexity,variables:t.variableValues},e=>new i(e,a)),...e.graphQL.disableIntrospectionInProduction?[eV]:[],..."function"==typeof e?.graphQL?.validationRules?e.graphQL.validationRules(t):[]]}}}(t);e(e5.graphql||a)})}try{e5.graphql=await e5.promise}catch(e){throw e5.promise=null,e}return e5.graphql},te=e=>async t=>{let a=t.clone(),n=await (0,e4.o)({canSetHeaders:!0,config:e,request:t});await (0,e9.z)(n),(0,e7.r)(n);let{schema:o,validationRules:i}=await e6(e),{payload:l}=n,s={},p=await (function(e,t={}){let a={Response:t.Response||Response,TextEncoder:t.TextEncoder||TextEncoder,ReadableStream:t.ReadableStream||ReadableStream},n=function(e){let{schema:t,context:a,validate:n=r.validate,validationRules:o=[],execute:i=r.execute,parse:l=r.parse,getOperationAST:s=r.getOperationAST,rootValue:p,onSubscribe:c,onOperation:u,formatError:y=e=>e,parseRequestParams:d=eZ}=e;return async function(e){let h,f,m,g=null;for(let t of(eH(e,"accept")||"*/*").replace(/\s/g,"").toLowerCase().split(",")){let[e,...a]=t.split(";"),r=(null==a?void 0:a.find(e=>e.includes("charset=")))||"charset=utf-8";if("application/graphql-response+json"===e&&"charset=utf-8"===r){g="application/graphql-response+json";break}if(("application/json"===e||"application/*"===e||"*/*"===e)&&("charset=utf-8"===r||"charset=utf8"===r)){g="application/json";break}}if(!g)return[null,{status:406,statusText:"Not Acceptable",headers:{accept:"application/graphql-response+json; charset=utf-8, application/json; charset=utf-8"}}];try{let t=await d(e);if(t||(t=await eZ(e)),eU(t))return t;h=t}catch(e){return eK(e,g,y)}let L=await (null==c?void 0:c(e,h));if(eU(L))return L;if(eJ(L)&&("data"in L||"data"in L&&null==L.data&&"errors"in L)||eW(L))return eK(L,g,y);if(L)f=L;else{let i;if(!t)throw Error("The GraphQL schema is not provided");let{operationName:s,query:p,variables:c}=h;try{i=l(p)}catch(e){return eK(e,g,y)}let u="function"==typeof a?await a(e,h):a;if(eU(u))return u;let d={operationName:s,document:i,variableValues:c,contextValue:u};if("function"==typeof t){let a=await t(e,d);if(eU(a))return a;f=Object.assign(Object.assign({},d),{schema:a})}else f=Object.assign(Object.assign({},d),{schema:t});let m=r.specifiedRules;m="function"==typeof o?await o(e,f,r.specifiedRules):[...m,...o];let L=n(f.schema,f.document,m);if(L.length)return eK(L,g,y)}try{let e=s(f.document,f.operationName);if(!e)throw null;m=e.operation}catch(e){return eK(new r.GraphQLError("Unable to detect operation AST"),g,y)}if("subscription"===m)return eK(new r.GraphQLError("Subscriptions are not supported"),g,y);if("mutation"===m&&"GET"===e.method)return[JSON.stringify({errors:[new r.GraphQLError("Cannot perform mutations over GET")]}),{status:405,statusText:"Method Not Allowed",headers:{allow:"POST"}}];if("rootValue"in f||(f.rootValue=p),!("contextValue"in f)){let t="function"==typeof a?await a(e,h):a;if(eU(t))return t;f.contextValue=t}let b=await i(f),Q=await (null==u?void 0:u(e,f,b));return eU(Q)?Q:(Q&&(b=Q),"function"==typeof Object(b)[Symbol.asyncIterator]?eK(new r.GraphQLError("Subscriptions are not supported"),g,y):eK(b,g,y))}}(e);return async function(e){try{let[t,r]=await n(function(e,t={}){return{method:e.method,url:e.url,headers:e.headers,body:()=>e.text(),raw:e,context:{Response:t.Response||Response,TextEncoder:t.TextEncoder||TextEncoder,ReadableStream:t.ReadableStream||ReadableStream}}}(e,a));return new a.Response(t,r)}catch(e){return console.error("Internal error occurred during request handling. Please check your implementation.",e),new a.Response(null,{status:500})}}})({context:{headers:s,req:n},onOperation:async(e,t,a)=>{let r="function"==typeof l.extensions?await l.extensions({args:t,req:e,result:a}):a;if(r.errors){let e=await Promise.all(a.errors.map(e=>e3({err:e,payload:l,req:n})));return{...r,errors:e}}return r},schema:o,validationRules:(e,t,a)=>a.concat(i(t))})(a),c=(0,e2.y)({headers:new Headers(p.headers),req:n});for(let e in s)c.append(e,s[e]);return new Response(p.body,{headers:n.responseHeaders?(0,e8.l)(n.responseHeaders,c):c,status:p.status})}},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},62149:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{OPTIONS:()=>p,POST:()=>s});var n=a(68117),o=a(40278),i=a(41862),l=e([n]);n=(l.then?(await l)():l)[0];let s=(0,o.L)(n.A),p=(0,i.lw)(n.A);r()}catch(e){r(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83725:e=>{"use strict";e.exports=import("next/dist/compiled/@vercel/og/index.node.js")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},93077:()=>{},94735:e=>{"use strict";e.exports=require("events")},98995:e=>{"use strict";e.exports=require("node:module")},99750:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{patchFetch:()=>p,routeModule:()=>c,serverHooks:()=>d,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>y});var n=a(70293),o=a(32498),i=a(83889),l=a(62149),s=e([l]);l=(s.then?(await s)():s)[0];let c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/(payload)/api/graphql/route",pathname:"/api/graphql",filename:"route",bundlePath:"app/(payload)/api/graphql/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql\\route.ts",nextConfigOutput:"",userland:l}),{workAsyncStorage:u,workUnitAsyncStorage:y,serverHooks:d}=c;function p(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:y})}r()}catch(e){r(e)}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[3889,2481,7852,8170,8754],()=>a(99750));module.exports=r})();