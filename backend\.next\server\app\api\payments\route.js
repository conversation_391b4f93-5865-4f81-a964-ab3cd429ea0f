(()=>{var e={};e.id=4131,e.ids=[4131],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1317:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.d(r,{w:()=>n,x:()=>c});var i=t(10724),o=t(68117),u=t(32481),a=e([o]);async function n(e){try{let r=e.headers.get("x-clerk-user-id"),t=e.headers.get("x-user-email");if(!r||!t)return console.log("Missing Clerk user headers"),null;let s=await (0,i.nm0)({config:o.A}),a={id:r,email:t};console.log("Syncing Clerk user with Payload:",a);let n=await (0,u.kw)(s,a);return console.log("Synced Payload user:",n),{payload:s,user:n}}catch(e){return console.error("Payload authentication error:",e),null}}async function c(e,r,t,s={}){let{payload:i,user:o}=e;switch(t){case"find":return await i.find({collection:r,user:o,...s});case"findByID":return await i.findByID({collection:r,user:o,...s});case"create":return await i.create({collection:r,user:o,...s});case"update":return await i.update({collection:r,user:o,...s});case"delete":return await i.delete({collection:r,user:o,...s});default:throw Error(`Unsupported operation: ${t}`)}}o=(a.then?(await a)():a)[0],s()}catch(e){s(e)}})},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},32481:(e,r,t)=>{"use strict";async function s(e,r,t="front-desk"){try{let s=await e.find({collection:"users",where:{clerkId:{equals:r.id}},limit:1});if(!(s.docs.length>0))return await e.create({collection:"users",data:{role:t,clerkId:r.id}});{let r=s.docs[0];return await e.update({collection:"users",id:r.id,data:{}})}}catch(e){throw console.error("Error syncing Clerk user with Payload:",e),Error("Failed to sync user authentication")}}t.d(r,{kw:()=>s})},32785:e=>{"use strict";e.exports=import("prettier")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},34869:()=>{},35672:e=>{"use strict";e.exports=require("dns/promises")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},37830:e=>{"use strict";e.exports=require("node:stream/web")},38522:e=>{"use strict";e.exports=require("node:zlib")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},66983:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{GET:()=>a,POST:()=>n});var i=t(27492),o=t(1317),u=e([o]);async function a(e){try{let r=await (0,o.w)(e);if(!r)return i.NextResponse.json({error:"Authentication required"},{status:401});let t=new URL(e.url),s=parseInt(t.searchParams.get("limit")||"10"),u=parseInt(t.searchParams.get("page")||"1"),a=parseInt(t.searchParams.get("depth")||"2"),n=t.searchParams.get("billId"),c=t.searchParams.get("patientId"),p=t.searchParams.get("paymentMethod"),d=t.searchParams.get("paymentStatus"),l={};n&&(l.bill={equals:n}),c&&(l.patient={equals:c}),p&&(l.paymentMethod={equals:p}),d&&(l.paymentStatus={equals:d});let x=await (0,o.x)(r,"payments","find",{limit:s,page:u,depth:a,where:Object.keys(l).length>0?l:void 0,sort:"-paymentDate"});return i.NextResponse.json(x)}catch(e){return console.error("Error fetching payments:",e),i.NextResponse.json({error:"Failed to fetch payments"},{status:500})}}async function n(e){try{let r=await (0,o.w)(e);if(!r)return i.NextResponse.json({error:"Authentication required"},{status:401});let t=await e.json();if(t.receivedBy=r.user.id,!t.bill)return i.NextResponse.json({error:"Bill is required"},{status:400});if(!t.patient)return i.NextResponse.json({error:"Patient is required"},{status:400});if(!t.amount||t.amount<=0)return i.NextResponse.json({error:"Valid payment amount is required"},{status:400});if(!t.paymentMethod)return i.NextResponse.json({error:"Payment method is required"},{status:400});let s=await (0,o.x)(r,"payments","create",{data:t});return i.NextResponse.json(s)}catch(e){return console.error("Error creating payment:",e),i.NextResponse.json({error:"Failed to create payment"},{status:500})}}o=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},67952:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>c,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var i=t(70293),o=t(32498),u=t(83889),a=t(66983),n=e([a]);a=(n.then?(await n)():n)[0];let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/payments/route",pathname:"/api/payments",filename:"route",bundlePath:"app/api/payments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=p;function c(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},93077:()=>{},94735:e=>{"use strict";e.exports=require("events")},98995:e=>{"use strict";e.exports=require("node:module")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[3889,2481,9556,8754],()=>t(67952));module.exports=s})();