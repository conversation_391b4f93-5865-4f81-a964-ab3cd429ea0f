try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="70700e26-7f4c-47ab-ba8d-89eb9ed3fac4",e._sentryDebugIdIdentifier="sentry-dbid-70700e26-7f4c-47ab-ba8d-89eb9ed3fac4")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2023],{25977:(e,t,r)=>{"use strict";r.d(t,{AreaGraph:()=>h});var a=r(52880),n=r(7964),s=r(51163),o=r(68412),l=r(50487),d=r(98215),i=r(86540),c=r(47882);let u=[{month:"January",desktop:186,mobile:80},{month:"February",desktop:305,mobile:200},{month:"March",desktop:237,mobile:120},{month:"April",desktop:73,mobile:190},{month:"May",desktop:209,mobile:130},{month:"June",desktop:214,mobile:140}],f={visitors:{label:"Visitors"},desktop:{label:"Desktop",color:"var(--primary)"},mobile:{label:"Mobile",color:"var(--primary)"}};function h(){return(0,a.jsxs)(i.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-component":"AreaGraph","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"area-graph.tsx",children:"Area Chart - Stacked"}),(0,a.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"area-graph.tsx",children:"Showing total visitors for the last 6 months"})]}),(0,a.jsx)(i.Wu,{className:"px-2 pt-4 sm:px-6 sm:pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"area-graph.tsx",children:(0,a.jsx)(c.at,{config:f,className:"aspect-auto h-[250px] w-full","data-sentry-element":"ChartContainer","data-sentry-source-file":"area-graph.tsx",children:(0,a.jsxs)(s.Q,{data:u,margin:{left:12,right:12},"data-sentry-element":"AreaChart","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsxs)("defs",{"data-sentry-element":"defs","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsxs)("linearGradient",{id:"fillDesktop",x1:"0",y1:"0",x2:"0",y2:"1","data-sentry-element":"linearGradient","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsx)("stop",{offset:"5%",stopColor:"var(--color-desktop)",stopOpacity:1,"data-sentry-element":"stop","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)("stop",{offset:"95%",stopColor:"var(--color-desktop)",stopOpacity:.1,"data-sentry-element":"stop","data-sentry-source-file":"area-graph.tsx"})]}),(0,a.jsxs)("linearGradient",{id:"fillMobile",x1:"0",y1:"0",x2:"0",y2:"1","data-sentry-element":"linearGradient","data-sentry-source-file":"area-graph.tsx",children:[(0,a.jsx)("stop",{offset:"5%",stopColor:"var(--color-mobile)",stopOpacity:.8,"data-sentry-element":"stop","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)("stop",{offset:"95%",stopColor:"var(--color-mobile)",stopOpacity:.1,"data-sentry-element":"stop","data-sentry-source-file":"area-graph.tsx"})]})]}),(0,a.jsx)(o.d,{vertical:!1,"data-sentry-element":"CartesianGrid","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)(l.W,{dataKey:"month",tickLine:!1,axisLine:!1,tickMargin:8,minTickGap:32,tickFormatter:e=>e.slice(0,3),"data-sentry-element":"XAxis","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)(c.II,{cursor:!1,content:(0,a.jsx)(c.Nt,{indicator:"dot"}),"data-sentry-element":"ChartTooltip","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)(d.G,{dataKey:"mobile",type:"natural",fill:"url(#fillMobile)",stroke:"var(--color-mobile)",stackId:"a","data-sentry-element":"Area","data-sentry-source-file":"area-graph.tsx"}),(0,a.jsx)(d.G,{dataKey:"desktop",type:"natural",fill:"url(#fillDesktop)",stroke:"var(--color-desktop)",stackId:"a","data-sentry-element":"Area","data-sentry-source-file":"area-graph.tsx"})]})})}),(0,a.jsx)(i.wL,{"data-sentry-element":"CardFooter","data-sentry-source-file":"area-graph.tsx",children:(0,a.jsx)("div",{className:"flex w-full items-start gap-2 text-sm",children:(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 leading-none font-medium",children:["Trending up by 5.2% this month"," ",(0,a.jsx)(n.A,{className:"h-4 w-4","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"area-graph.tsx"})]}),(0,a.jsx)("div",{className:"text-muted-foreground flex items-center gap-2 leading-none",children:"January - June 2024"})]})})})]})}},47882:(e,t,r)=>{"use strict";r.d(t,{II:()=>h,Nt:()=>m,at:()=>u});var a=r(52880),n=r(99004),s=r(15998),o=r(67892),l=r(92942),d=r(54651);let i={light:"",dark:".dark"},c=n.createContext(null);function u(e){let{id:t,className:r,children:o,config:l,...i}=e,u=n.useId(),h="chart-".concat(t||u.replace(/:/g,""));return(0,a.jsx)(c.Provider,{value:{config:l},"data-sentry-element":"ChartContext.Provider","data-sentry-component":"ChartContainer","data-sentry-source-file":"chart.tsx",children:(0,a.jsxs)("div",{"data-slot":"chart","data-chart":h,className:(0,d.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",r),...i,children:[(0,a.jsx)(f,{id:h,config:l,"data-sentry-element":"ChartStyle","data-sentry-source-file":"chart.tsx"}),(0,a.jsx)(s.u,{debounce:2e3,"data-sentry-element":"RechartsPrimitive.ResponsiveContainer","data-sentry-source-file":"chart.tsx",children:o})]})})}let f=e=>{let{id:t,config:r}=e,n=Object.entries(r).filter(e=>{let[,t]=e;return t.theme||t.color});return n.length?(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(i).map(e=>{let[r,a]=e;return"\n".concat(a," [data-chart=").concat(t,"] {\n").concat(n.map(e=>{var t;let[a,n]=e,s=(null==(t=n.theme)?void 0:t[r])||n.color;return s?"  --color-".concat(a,": ").concat(s,";"):null}).join("\n"),"\n}\n")}).join("\n")},"data-sentry-component":"ChartStyle","data-sentry-source-file":"chart.tsx"}):null},h=o.m;function m(e){let{active:t,payload:r,className:s,indicator:o="dot",hideLabel:l=!1,hideIndicator:i=!1,label:u,labelFormatter:f,labelClassName:h,formatter:m,color:y,nameKey:x,labelKey:g}=e,{config:b}=function(){let e=n.useContext(c);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),v=n.useMemo(()=>{var e;if(l||!(null==r?void 0:r.length))return null;let[t]=r,n="".concat(g||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),s=p(b,t,n),o=g||"string"!=typeof u?null==s?void 0:s.label:(null==(e=b[u])?void 0:e.label)||u;return f?(0,a.jsx)("div",{className:(0,d.cn)("font-medium",h),children:f(o,r)}):o?(0,a.jsx)("div",{className:(0,d.cn)("font-medium",h),children:o}):null},[u,f,r,l,h,b,g]);if(!t||!(null==r?void 0:r.length))return null;let j=1===r.length&&"dot"!==o;return(0,a.jsxs)("div",{className:(0,d.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",s),"data-sentry-component":"ChartTooltipContent","data-sentry-source-file":"chart.tsx",children:[j?null:v,(0,a.jsx)("div",{className:"grid gap-1.5",children:r.map((e,t)=>{let r="".concat(x||e.name||e.dataKey||"value"),n=p(b,e,r),s=y||e.payload.fill||e.color;return(0,a.jsx)("div",{className:(0,d.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===o&&"items-center"),children:m&&(null==e?void 0:e.value)!==void 0&&e.name?m(e.value,e.name,e,t,e.payload):(0,a.jsxs)(a.Fragment,{children:[(null==n?void 0:n.icon)?(0,a.jsx)(n.icon,{}):!i&&(0,a.jsx)("div",{className:(0,d.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===o,"w-1":"line"===o,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===o,"my-0.5":j&&"dashed"===o}),style:{"--color-bg":s,"--color-border":s}}),(0,a.jsxs)("div",{className:(0,d.cn)("flex flex-1 justify-between leading-none",j?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[j?v:null,(0,a.jsx)("span",{className:"text-muted-foreground",children:(null==n?void 0:n.label)||e.name})]}),e.value&&(0,a.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function p(e,t,r){if("object"!=typeof t||null===t)return;let a="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,n=r;return r in t&&"string"==typeof t[r]?n=t[r]:a&&r in a&&"string"==typeof a[r]&&(n=a[r]),n in e?e[n]:e[r]}l.s},53916:(e,t,r)=>{Promise.resolve().then(r.bind(r,25977))},54651:(e,t,r)=>{"use strict";r.d(t,{Jv:()=>i,cn:()=>s,fw:()=>d,r6:()=>l,z3:()=>o});var a=r(97921),n=r(56309);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function o(e){var t,r;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:n=0,sizeType:s="normal"}=a;if(0===e)return"0 Byte";let o=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,o)).toFixed(n)," ").concat("accurate"===s?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][o])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][o])?r:"Bytes")}function l(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function d(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let r=Math.floor(t/60);if(r<60)return"".concat(r,"分钟前");let a=Math.floor(r/60);if(a<24)return"".concat(a,"小时前");let n=Math.floor(a/24);if(n<7)return"".concat(n,"天前");let s=Math.floor(n/7);if(s<4)return"".concat(s,"周前");let o=Math.floor(n/30);if(o<12)return"".concat(o,"个月前");let l=Math.floor(n/365);return"".concat(l,"年前")}function i(e){return("string"==typeof e?new Date(e):e)<new Date}},86540:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,X9:()=>i,ZB:()=>l,Zp:()=>s,aR:()=>o,wL:()=>u});var a=r(52880);r(99004);var n=r(54651);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...r,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function u(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,2826,2530,9535,9641,9442,4579,9253,7358],()=>t(53916)),_N_E=e.O()}]);