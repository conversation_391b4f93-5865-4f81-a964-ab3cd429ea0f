try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="6831e5a8-5baf-4bf2-a24d-5d5b2ae0f4c8",e._sentryDebugIdIdentifier="sentry-dbid-6831e5a8-5baf-4bf2-a24d-5d5b2ae0f4c8")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1087],{25192:(e,t,a)=>{"use strict";a.d(t,{T:()=>s});var n=a(52880);a(99004);var r=a(54651);function s(e){let{className:t,...a}=e;return(0,n.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},26368:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var n=(0,a(49202).A)("outline","edit","IconEdit",[["path",{d:"M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1",key:"svg-0"}],["path",{d:"M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z",key:"svg-1"}],["path",{d:"M16 5l3 3",key:"svg-2"}]])},42094:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var n=a(52880);a(99004);var r=a(54651);function s(e){let{className:t,type:a,...s}=e;return(0,n.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},46129:(e,t,a)=>{Promise.resolve().then(a.bind(a,93177)),Promise.resolve().then(a.bind(a,90917))},46937:(e,t,a)=>{"use strict";a.d(t,{UC:()=>T,VY:()=>O,ZD:()=>F,ZL:()=>I,bL:()=>A,hE:()=>z,hJ:()=>S,rc:()=>_});var n=a(99004),r=a(38774),s=a(39552),o=a(88749),i=a(84732),l=a(50516),d=a(52880),c="AlertDialog",[u,m]=(0,r.A)(c,[o.Hs]),f=(0,o.Hs)(),x=e=>{let{__scopeAlertDialog:t,...a}=e,n=f(t);return(0,d.jsx)(o.bL,{...n,...a,modal:!0})};x.displayName=c,n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,r=f(a);return(0,d.jsx)(o.l9,{...r,...n,ref:t})}).displayName="AlertDialogTrigger";var p=e=>{let{__scopeAlertDialog:t,...a}=e,n=f(t);return(0,d.jsx)(o.ZL,{...n,...a})};p.displayName="AlertDialogPortal";var g=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,r=f(a);return(0,d.jsx)(o.hJ,{...r,...n,ref:t})});g.displayName="AlertDialogOverlay";var y="AlertDialogContent",[v,h]=u(y),j=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:r,...c}=e,u=f(a),m=n.useRef(null),x=(0,s.s)(t,m),p=n.useRef(null);return(0,d.jsx)(o.G$,{contentName:y,titleName:b,docsSlug:"alert-dialog",children:(0,d.jsx)(v,{scope:a,cancelRef:p,children:(0,d.jsxs)(o.UC,{role:"alertdialog",...u,...c,ref:x,onOpenAutoFocus:(0,i.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=p.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(l.xV,{children:r}),(0,d.jsx)(C,{contentRef:m})]})})})});j.displayName=y;var b="AlertDialogTitle",w=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,r=f(a);return(0,d.jsx)(o.hE,{...r,...n,ref:t})});w.displayName=b;var D="AlertDialogDescription",N=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,r=f(a);return(0,d.jsx)(o.VY,{...r,...n,ref:t})});N.displayName=D;var M=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,r=f(a);return(0,d.jsx)(o.bm,{...r,...n,ref:t})});M.displayName="AlertDialogAction";var P="AlertDialogCancel",k=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,{cancelRef:r}=h(P,a),i=f(a),l=(0,s.s)(t,r);return(0,d.jsx)(o.bm,{...i,...n,ref:l})});k.displayName=P;var C=e=>{let{contentRef:t}=e,a="`".concat(y,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(y,"` by passing a `").concat(D,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(y,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(a)},[a,t]),null},A=x,I=p,S=g,T=j,_=M,F=k,z=w,O=N},47889:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var n=(0,a(49202).A)("outline","stethoscope","IconStethoscope",[["path",{d:"M6 4h-1a2 2 0 0 0 -2 2v3.5h0a5.5 5.5 0 0 0 11 0v-3.5a2 2 0 0 0 -2 -2h-1",key:"svg-0"}],["path",{d:"M8 15a6 6 0 1 0 12 0v-3",key:"svg-1"}],["path",{d:"M11 3v2",key:"svg-2"}],["path",{d:"M6 3v2",key:"svg-3"}],["path",{d:"M20 10m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-4"}]])},60171:(e,t,a)=>{"use strict";a.d(t,{I:()=>c,SQ:()=>d,_2:()=>u,hO:()=>m,lp:()=>f,mB:()=>x,rI:()=>i,ty:()=>l});var n=a(52880);a(99004);var r=a(83028),s=a(90502),o=a(54651);function i(e){let{...t}=e;return(0,n.jsx)(r.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function l(e){let{...t}=e;return(0,n.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function d(e){let{className:t,sideOffset:a=4,...s}=e;return(0,n.jsx)(r.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,n.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...s,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,n.jsx)(r.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:a,variant:s="default",...i}=e;return(0,n.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":s,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,children:a,checked:i,...l}=e;return(0,n.jsxs)(r.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:i,...l,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,n.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(r.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,n.jsx)(s.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),a]})}function f(e){let{className:t,inset:a,...s}=e;return(0,n.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...s,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function x(e){let{className:t,...a}=e;return(0,n.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",t),...a,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},66444:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var n=(0,a(49202).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]])},77362:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>f,L3:()=>x,c7:()=>m,lG:()=>i,rr:()=>p,zM:()=>l});var n=a(52880);a(99004);var r=a(88749),s=a(48086),o=a(54651);function i(e){let{...t}=e;return(0,n.jsx)(r.bL,{"data-slot":"dialog",...t,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function l(e){let{...t}=e;return(0,n.jsx)(r.l9,{"data-slot":"dialog-trigger",...t,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d(e){let{...t}=e;return(0,n.jsx)(r.ZL,{"data-slot":"dialog-portal",...t,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c(e){let{className:t,...a}=e;return(0,n.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u(e){let{className:t,children:a,...i}=e;return(0,n.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,n.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,n.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[a,(0,n.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,n.jsx)(s.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function f(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function x(e){let{className:t,...a}=e;return(0,n.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...a,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function p(e){let{className:t,...a}=e;return(0,n.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},84692:(e,t,a)=>{"use strict";a.d(t,{J:()=>o});var n=a(52880);a(99004);var r=a(71002),s=a(54651);function o(e){let{className:t,...a}=e;return(0,n.jsx)(r.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},90917:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,ScrollArea:()=>o});var n=a(52880);a(99004);var r=a(71359),s=a(54651);function o(e){let{className:t,children:a,...o}=e;return(0,n.jsxs)(r.bL,{"data-slot":"scroll-area",className:(0,s.cn)("relative",t),...o,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,n.jsx)(r.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:a}),(0,n.jsx)(i,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,n.jsx)(r.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function i(e){let{className:t,orientation:a="vertical",...o}=e;return(0,n.jsx)(r.VM,{"data-slot":"scroll-area-scrollbar",orientation:a,className:(0,s.cn)("flex touch-none p-px transition-colors select-none","vertical"===a&&"h-full w-2.5 border-l border-l-transparent","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent",t),...o,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,n.jsx)(r.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},93177:(e,t,a)=>{"use strict";a.d(t,{TreatmentsList:()=>M});var n=a(52880),r=a(99004),s=a(62054),o=a(29980),i=a(47889),l=a(66444),d=a(26368),c=a(93900),u=a(87378),m=a(38406),f=a(90290),x=a(73259),p=a(77362),g=a(56420),y=a(42094),v=a(25192),h=a(4629),j=a(26230);let b=x.Ik({name:x.Yj().min(2,"治疗名称至少需要2个字符").max(100,"治疗名称不能超过100个字符").regex(/^[a-zA-Z0-9\s\-&()]+$/,"治疗名称只能包含字母、数字、空格、连字符和括号"),description:x.Yj().max(1e3,"描述不能超过1000个字符").optional(),defaultPrice:x.ai().min(0,"价格必须为0元或更高").max(1e4,"价格不能超过10,000元").multipleOf(.01,"价格必须是有效的货币金额（例如：99.99）"),defaultDurationInMinutes:x.ai().min(5,"时长至少需要5分钟").max(480,"时长不能超过8小时（480分钟）").int("时长必须是整数分钟")});function w(e){let{open:t,onOpenChange:a,treatment:o,onSuccess:i}=e,[l,d]=(0,r.useState)(!1),c=!!o,x=(0,m.mN)({resolver:(0,f.u)(b),defaultValues:{name:(null==o?void 0:o.name)||"",description:(null==o?void 0:o.description)||"",defaultPrice:(null==o?void 0:o.defaultPrice)||0,defaultDurationInMinutes:(null==o?void 0:o.defaultDurationInMinutes)||30}});r.useEffect(()=>{t&&x.reset({name:(null==o?void 0:o.name)||"",description:(null==o?void 0:o.description)||"",defaultPrice:(null==o?void 0:o.defaultPrice)||0,defaultDurationInMinutes:(null==o?void 0:o.defaultDurationInMinutes)||30})},[t,o,x]);let w=async e=>{d(!0);try{let t={name:e.name,description:e.description||void 0,defaultPrice:e.defaultPrice,defaultDurationInMinutes:e.defaultDurationInMinutes};c?(await u._M.update(o.id,t),h.toast.success("Treatment updated successfully")):(await u._M.create(t),h.toast.success("Treatment created successfully")),null==i||i(),a(!1),x.reset()}catch(e){console.error("Failed to save treatment:",e),h.toast.error("Failed to ".concat(c?"update":"create"," treatment"))}finally{d(!1)}};return(0,n.jsx)(p.lG,{open:t,onOpenChange:a,"data-sentry-element":"Dialog","data-sentry-component":"TreatmentFormDialog","data-sentry-source-file":"treatment-form-dialog.tsx",children:(0,n.jsxs)(p.Cf,{className:"sm:max-w-[500px]","data-sentry-element":"DialogContent","data-sentry-source-file":"treatment-form-dialog.tsx",children:[(0,n.jsxs)(p.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"treatment-form-dialog.tsx",children:[(0,n.jsx)(p.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"treatment-form-dialog.tsx",children:c?(0,j.t)("treatments.editTreatment"):(0,j.t)("treatments.newTreatment")}),(0,n.jsx)(p.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"treatment-form-dialog.tsx",children:c?"更新下方的治疗信息。":"填写治疗详细信息以创建新服务。"})]}),(0,n.jsx)(g.lV,{...x,"data-sentry-element":"Form","data-sentry-source-file":"treatment-form-dialog.tsx",children:(0,n.jsxs)("form",{onSubmit:x.handleSubmit(w),className:"space-y-4",children:[(0,n.jsx)(g.zB,{control:x.control,name:"name",render:e=>{let{field:t}=e;return(0,n.jsxs)(g.eI,{children:[(0,n.jsxs)(g.lR,{children:[(0,j.t)("treatments.form.name")," *"]}),(0,n.jsx)(g.MJ,{children:(0,n.jsx)(y.p,{placeholder:(0,j.t)("treatments.form.namePlaceholder"),...t})}),(0,n.jsx)(g.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"treatment-form-dialog.tsx"}),(0,n.jsx)(g.zB,{control:x.control,name:"description",render:e=>{let{field:t}=e;return(0,n.jsxs)(g.eI,{children:[(0,n.jsx)(g.lR,{children:(0,j.t)("treatments.form.description")}),(0,n.jsx)(g.MJ,{children:(0,n.jsx)(v.T,{placeholder:(0,j.t)("treatments.form.descriptionPlaceholder"),className:"min-h-[80px]",...t})}),(0,n.jsx)(g.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"treatment-form-dialog.tsx"}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsx)(g.zB,{control:x.control,name:"defaultPrice",render:e=>{let{field:t}=e;return(0,n.jsxs)(g.eI,{children:[(0,n.jsxs)(g.lR,{children:[(0,j.t)("treatments.form.price")," (\xa5) *"]}),(0,n.jsx)(g.MJ,{children:(0,n.jsx)(y.p,{type:"number",step:"0.01",placeholder:(0,j.t)("treatments.form.pricePlaceholder"),...t,onChange:e=>t.onChange(parseFloat(e.target.value)||0)})}),(0,n.jsx)(g.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"treatment-form-dialog.tsx"}),(0,n.jsx)(g.zB,{control:x.control,name:"defaultDurationInMinutes",render:e=>{let{field:t}=e;return(0,n.jsxs)(g.eI,{children:[(0,n.jsxs)(g.lR,{children:[(0,j.t)("treatments.form.duration")," (分钟) *"]}),(0,n.jsx)(g.MJ,{children:(0,n.jsx)(y.p,{type:"number",placeholder:(0,j.t)("treatments.form.durationPlaceholder"),...t,onChange:e=>t.onChange(parseInt(e.target.value)||0)})}),(0,n.jsx)(g.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"treatment-form-dialog.tsx"})]}),(0,n.jsxs)(p.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"treatment-form-dialog.tsx",children:[(0,n.jsx)(s.$,{type:"button",variant:"outline",onClick:()=>a(!1),disabled:l,"data-sentry-element":"Button","data-sentry-source-file":"treatment-form-dialog.tsx",children:(0,j.t)("common.actions.cancel")}),(0,n.jsx)(s.$,{type:"submit",disabled:l,"data-sentry-element":"Button","data-sentry-source-file":"treatment-form-dialog.tsx",children:l?"保存中...":c?"更新治疗":"创建治疗"})]})]})})]})})}var D=a(65674),N=a(60171);function M(){let{hasPermission:e}=(0,o.It)(),[t,a]=(0,r.useState)([]),[m,f]=(0,r.useState)(!0),[x,p]=(0,r.useState)(null),[g,y]=(0,r.useState)(!1),[v,b]=(0,r.useState)(),[M,P]=(0,r.useState)(!1),[k,C]=(0,r.useState)(),[A,I]=(0,r.useState)(!1),S=async()=>{try{f(!0),p(null);let e=await u._M.getAll({limit:100});a(e.docs)}catch(e){console.error("Failed to fetch treatments:",e),p("Failed to load treatments. Please try again later.")}finally{f(!1)}};(0,r.useEffect)(()=>{S()},[]);let T=()=>{b(void 0),y(!0)},_=e=>{b(e),y(!0)},F=async()=>{if(k){I(!0);try{await u._M.delete(k.id),h.toast.success("Treatment deleted successfully"),P(!1),C(void 0),S()}catch(e){console.error("Failed to delete treatment:",e),h.toast.error("Failed to delete treatment")}finally{I(!1)}}},z=()=>{y(!1),b(void 0),S()};return m?(0,n.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:(0,j.t)("treatments.loadingTreatments")})]})}):x?(0,n.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-red-600 mb-4",children:x}),(0,n.jsx)(s.$,{onClick:()=>window.location.reload(),variant:"outline",children:"重试"})]})}):0===t.length?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(i.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium mb-2",children:(0,j.t)("treatments.noTreatments")}),(0,n.jsx)("p",{className:"text-muted-foreground mb-4",children:"开始添加您的第一个治疗项目。"}),(0,n.jsx)(o.Bk,{permission:"canCreateTreatments",children:(0,n.jsxs)(s.$,{onClick:T,children:[(0,n.jsx)(l.A,{className:"h-4 w-4 mr-2"}),(0,j.t)("treatments.newTreatment")]})})]})}),(0,n.jsx)(w,{open:g,onOpenChange:y,treatment:v,onSuccess:z})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("div",{children:(0,n.jsxs)("h3",{className:"text-lg font-medium",children:[t.length," ",(0,j.t)("treatments.treatmentsCount")]})}),(0,n.jsx)(o.Bk,{permission:"canCreateTreatments","data-sentry-element":"PermissionGate","data-sentry-source-file":"treatments-list.tsx",children:(0,n.jsxs)(s.$,{onClick:T,"data-sentry-element":"Button","data-sentry-source-file":"treatments-list.tsx",children:[(0,n.jsx)(l.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"treatments-list.tsx"}),(0,j.t)("treatments.newTreatment")]})})]}),(0,n.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:t.map(e=>{var t;return(0,n.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsx)("h4",{className:"font-medium text-lg",children:e.name}),e.description&&(0,n.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:e.description})]}),(0,n.jsx)(o.Bk,{permission:"canEditTreatments",children:(0,n.jsxs)(N.rI,{children:[(0,n.jsx)(N.ty,{asChild:!0,children:(0,n.jsx)(s.$,{variant:"ghost",size:"sm",children:(0,n.jsx)(d.A,{className:"h-4 w-4"})})}),(0,n.jsxs)(N.SQ,{children:[(0,n.jsxs)(N._2,{onClick:()=>_(e),children:[(0,n.jsx)(d.A,{className:"h-4 w-4 mr-2"}),(0,j.t)("common.actions.edit")]}),(0,n.jsxs)(N._2,{onClick:()=>{C(e),P(!0)},className:"text-red-600",children:[(0,n.jsx)(c.A,{className:"h-4 w-4 mr-2"}),(0,j.t)("common.actions.delete")]})]})]})})]}),(0,n.jsx)("div",{className:"flex items-center justify-between text-sm",children:(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"价格:"}),(0,n.jsxs)("span",{className:"font-medium",children:["\xa5",(null==(t=e.defaultPrice)?void 0:t.toFixed(2))||"未设置"]})]}),e.defaultDurationInMinutes&&(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-muted-foreground",children:"时长:"}),(0,n.jsxs)("span",{children:[e.defaultDurationInMinutes," 分钟"]})]})]})})]},e.id)})})]}),(0,n.jsx)(w,{open:g,onOpenChange:y,treatment:v,onSuccess:z,"data-sentry-element":"TreatmentFormDialog","data-sentry-source-file":"treatments-list.tsx"}),(0,n.jsx)(D.K,{open:M,onOpenChange:P,title:"删除治疗项目",description:'您确定要删除"'.concat(null==k?void 0:k.name,'"吗？此操作无法撤销。'),confirmText:"删除",variant:"destructive",onConfirm:F,loading:A,"data-sentry-element":"ConfirmationDialog","data-sentry-source-file":"treatments-list.tsx"})]})}},93900:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var n=(0,a(49202).A)("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,7905,1359,4089,4629,7131,2090,2350,290,3028,229,3571,9442,4579,9253,7358],()=>t(46129)),_N_E=e.O()}]);