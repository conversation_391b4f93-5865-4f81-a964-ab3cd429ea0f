{"version": 3, "file": "../app/dashboard/product/[productId]/page.js", "mappings": "ubAAA,ysBCAA,oDCAA,kJCCe,gBAAqB,WAAW,QAAU,cAAc,CAAC,CAAC,OAAO,CAAC,EAAI,4CAA6C,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,eAAgB,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,YAAa,KAAM,CAAO,OAAC,CAAC,CAAC,wDCDlO,eAEP,uDACA,8BACA,sBACA,sBACA,oBACA,sBACA,oBACA,qCACA,uCACA,sCACA,uCACA,sBACA,uCACA,uCACA,gCACA,6CACA,oBACA,+CACA,uCACA,mCACA,oCACA,sBACA,qCACA,mCACA,qCACA,wCACA,yBACA,uBACA,wBACA,wBACA,sEACA,kCACA,oCACA,oBACA,kDACA,sBACA,mCACA,+CACA,yCACA,gCACA,4BACA,oCACA,yBACA,qBACA,4CACA,yBACA,kCACA,gCACA,sCACA,8CACA,sCACA,+CACA,oBACA,0BACA,sBACA,gCACA,gDACA,gDACA,2CACA,uCACA,4BACA,mCACA,gCACA,iCACA,0CACA,8BACA,sCACA,2CACA,mCACA,8BACA,gCACA,8BACA,6CACA,oBACA,sCACA,6CACA,8BACA,mCACA,oCACA,8CACA,0BACA,sCACA,4BACA,8BACA,iBACA,wCACA,wCACA,wCACA,wCACA,wCACA,0DACA,8DACA,4CACA,sBACA,uCACA,mCACA,wCACA,4BACA,4BACA,4BACA,4BACA,4BACA,kBACA,8BACA,iCACA,kCACA,2CACA,4BACA,+BACA,gCACA,+CACA,wCACA,uCACA,oCACA,oCACA,mCACA,0BACA,yBACA,yCACA,qCACA,gCACA,uCACA,oBACA,8BACA,sCACA,sCACA,yBACA,iEACA,sCACA,2BACA,mCACA,qCACA,kDACA,iDACA,kDACA,kDACA,yCACA,+BACA,sCACA,2BACA,yBACA,kDACA,sBACA,kCACA,+BACA,mCACA,sBACA,8BACA,mBACA,qCACA,mCACA,+BACA,qCACA,yCACA,gDACA,4BACA,mDACA,2BACA,sCACA,mCACA,mBACA,iCACA,mBACA,8BACA,yBACA,8BACA,iCACA,mBACA,gCACA,qCACA,gCACA,yCACA,wCACA,8BACA,kCACA,iCACA,gCACA,sCACA,wCACA,2CACA,2BACA,uCACA,qBACA,sCACA,qCACA,wCACA,uCACA,mBACA,iCACA,qCACA,gEACA,oCACA,qCACA,yBACA,0BACA,mCACA,wCACA,mCACA,uCACA,mCACA,8BACA,6BACA,4DACA,mFACA,6BACA,4DACA,mFACA,iCACA,kCACA,wBACA,2BACA,6BACA,gCACA,mCACA,8BACA,wBACA,6BACA,oCACA,6BACA,4BACA,mCACA,wBACA,wBACA,wBACA,uCACA,iCACA,mCACA,0CACA,0CACA,0CACA,kCACA,uCACA,uCACA,kCACA,oCACA,mCACA,oBACA,yBACA,gCACA,0CACA,mCACA,kCACA,wCACA,iCACA,gCACA,gCACA,uCACA,yCACA,oCACA,uCACA,wBACA,4BACA,8BACA,mCACA,0BACA,8BACA,qBACA,uCACA,kCACA,sCACA,wCACA,uBACA,oBACA,yBACA,yBACA,iCACA,mDACA,mCACA,8BACA,8BACA,uDACA,0CACA,iCACA,0BACA,2BACA,2BACA,2BACA,2BACA,6BACA,sBACA,wBACA,sBACA,yCACA,sBACA,oCACA,gCACA,uBACA,oCACA,sCACA,qDACA,yBACA,wBACA,uCACA,wCACA,wBACA,wCACA,8DACA,wBACA,oCACA,qCACA,qCACA,kCACA,qBACA,mCACA,yCACA,6BACA,wCACA,uCACA,wBACA,gDACA,kCACA,mCACA,4CACA,wCACA,wCACA,sCACA,oBACA,kDACA,4BACA,2BACA,8BACA,8BACA,sCACA,+BACA,qCACA,8BACA,iCACA,iCACA,4BACA,sCACA,4CACA,0CACA,iCACA,yCACA,qDACA,uDACA,8BACA,8CACA,wBACA,2BACA,0BACA,kCACA,0BACA,4BACA,iBACA,sBACA,sBACA,sBACA,kCACA,gCACA,qCACA,uCACA,4BACA,sBACA,gCACA,sBACA,gCACA,uBACA,qCACA,kBACA,8BACA,6BACA,mCACA,mCACA,iCACA,mCACA,sBACA,2BACA,sCACA,oBACA,qBACA,wCACA,0CACA,2CACA,mCACA,qCACA,kCACA,qCACA,uBACA,wBACA,oBACA,wBACA,kDACA,sBACA,mCACA,qCACA,qBACA,yCACA,qDACA,mCACA,4CACA,iCACA,oBACA,qBACA,gCACA,kCACA,iDACA,iDACA,8BACA,iDACA,gDACA,kDACA,sCACA,sDACA,8BACA,0CACA,0CACA,2CACA,qBACA,8BACA,mCACA,8CACA,8BACA,qBACA,gCACA,oBACA,kCACA,sBACA,wCACA,8CACA,oBACA,qBACA,sBACA,oBACA,qBACA,qBACA,qBACA,sBACA,oBACA,oBACA,oBACA,gCACA,4BACA,8BACA,iCAEA,8BACA,qCACA,mBACA,oBACA,sBACA,sBACA,oBACA,sBACA,sBACA,sBACA,qBACA,wCACA,mCACA,kCACA,6CACA,uCACA,uCACA,+CACA,2CACA,gCACA,gCACA,sCACA,yCACA,yCACA,uCACA,sCACA,kCACA,oBACA,sBACA,kCACA,oCACA,oCACA,yCACA,gCACA,6DACA,kEACA,4CACA,qBACA,8BACA,mCACA,gDACA,sBACA,0CACA,yCACA,kCACA,oCACA,qBACA,mCACA,mCACA,iCACA,sCACA,qBACA,sCACA,iCACA,wCACA,mCACA,qBACA,qBACA,qBACA,qBACA,qBACA,yCACA,sBACA,0BACA,4BACA,kCACA,sBACA,oCACA,oCACA,2BACA,iCACA,gCACA,mCACA,uCACA,uCACA,qBACA,mCACA,2BACA,mCACA,6BACA,oCACA,iCACA,qCACA,4BACA,sCACA,8BACA,gCACA,uBACA,iCACA,4BACA,mBACA,oBACA,sBACA,sCACA,wCACA,gCACA,+BACA,oCACA,iDACA,2CACA,qBACA,sBACA,4BACA,8BACA,0BACA,oBACA,qBACA,iCACA,4BACA,2BACA,0BACA,2BACA,2BACA,oCACA,+CACA,+BACA,sBACA,yCACA,sBACA,gCACA,0CACA,gCACA,0BACA,8BACA,qBACA,sBACA,qBACA,oBACA,qBACA,2BACA,qBACA,4BACA,6CACA,+BACA,qBACA,sBACA,qBACA,qBACA,sBACA,+CACA,4CACA,6CACA,qCACA,qCACA,sCACA,qCACA,2BACA,mCACA,oBACA,+CACA,uCACA,gCACA,oCACA,qCACA,qBACA,mCACA,qCACA,mCACA,mCACA,uCACA,oBACA,wBACA,mCACA,mCACA,sDACA,oCACA,6CACA,+BACA,0BACA,6CACA,4BACA,8BACA,uCACA,4BACA,0DACA,iBACA,iCACA,yCACA,8BACA,mCACA,qBACA,8CACA,gCACA,4CACA,kCACA,6CACA,0CACA,uCACA,4BACA,6BACA,mCACA,sCACA,+BACA,+BACA,qDACA,4BACA,yCACA,yCACA,wCACA,iCACA,6CACA,oBACA,0BACA,sDACA,mDACA,qDACA,+DACA,sDACA,mDACA,yDACA,0DACA,yDACA,kDACA,oBACA,6BACA,oBACA,oBACA,0BACA,kCACA,iCACA,iCACA,iCACA,kCACA,wCACA,uBACA,gCACA,qBACA,qBACA,iDACA,+DACA,iDACA,4DACA,mBACA,+DACA,sDACA,4DACA,mEACA,kEACA,2DACA,uCACA,uCACA,8BACA,4BACA,kDACA,sBACA,wCACA,2CACA,iCACA,iCACA,0CACA,sCACA,2BACA,+BACA,+BACA,4CACA,+CACA,wBACA,oCACA,wCACA,kCACA,wCACA,iCACA,iCACA,qCACA,uBACA,uCACA,sBACA,8BACA,4BACA,0BACA,uCACA,mCACA,mCACA,mCACA,iCACA,+BACA,mCACA,kCACA,0BACA,kCACA,mCACA,mCACA,0CACA,oCACA,uBACA,mCACA,8BACA,uCACA,0CACA,4BACA,4CACA,qCACA,sCACA,8BACA,4BACA,oCACA,oBACA,kCACA,+CACA,wCACA,sEACA,iFACA,wCACA,+DACA,mCACA,kCACA,wCACA,mEACA,kFACA,iCACA,sEACA,qFACA,+BACA,8BACA,0CACA,iCACA,uCACA,gCACA,4CACA,kCACA,uCACA,mCACA,wBACA,qCACA,oCACA,0CACA,2CACA,2CACA,2CACA,2CACA,mCACA,mCACA,gDACA,yBACA,4CACA,4CACA,4CACA,4CACA,4CACA,4CACA,2BACA,+BACA,iCACA,qCACA,4BACA,6BACA,sDACA,8BACA,0CACA,uCACA,0CACA,wCACA,sBACA,kCACA,wBACA,8CACA,wCACA,yCACA,8CACA,8BACA,qBACA,sCACA,gDACA,4CACA,8CACA,0BACA,+BACA,sBACA,sCACA,sCACA,+CACA,8CACA,kCACA,sCACA,8BACA,qCACA,8BACA,0CACA,8BACA,mBACA,wBACA,iCACA,qCACA,gCACA,mBACA,oBACA,4CACA,uBACA,gCACA,8CACA,mCACA,0CACA,sCACA,uCACA,uBACA,gCACA,4CACA,4CACA,+CACA,2CACA,2CACA,0BACA,8CACA,mCACA,kCACA,qCACA,gCACA,gCACA,gCACA,mCACA,qCACA,6CACA,gDACA,qDACA,qDACA,uCACA,qBACA,oBACA,qDACA,oBACA,qBACA,0BACA,8BACA,qBACA,8BACA,sBACA,8BACA,8BACA,oCACA,qBACA,sBACA,0CACA,2CACA,gCACA,kCACA,4BACA,+BACA,+BACA,+BACA,+BACA,+DACA,8EACA,qBACA,oBACA,uCACA,qCACA,6CACA,4CACA,2BACA,4BACA,sBACA,8CACA,sBACA,iCACA,kCACA,2CACA,qBACA,6CACA,qCACA,8BACA,uCACA,sCACA,oBACA,4BACA,oCACA,+BACA,8BACA,yCACA,gCACA,2CACA,oCACA,gCACA,mCACA,8CACA,gDACA,gDACA,iCACA,mDACA,kCACA,oBACA,0BACA,+BACA,0BACA,oCACA,kDACA,uBACA,yBACA,gCACA,uCACA,wCACA,oCACA,kCACA,sCACA,8BACA,wBACA,yBACA,iCACA,wCACA,6CACA,mCACA,uCACA,uCACA,gDACA,0CACA,uCACA,yCACA,mBACA,kCACA,oBACA,mCACA,oDACA,gCACA,4BACA,sCACA,4BACA,wCACA,4CACA,8BACA,oCACA,4BACA,iCACA,oCACA,sBACA,iCACA,gCACA,wBACA,sBACA,4BACA,0CACA,qBACA,sBACA,2BACA,yCACA,4BACA,uCACA,+CACA,mCACA,oBACA,kCACA,4BACA,mCACA,oBACA,uCACA,oCACA,0BACA,mBACA,sBACA,gCACA,6CACA,8CACA,2CACA,qCACA,qBACA,2CACA,mCACA,oDACA,2BACA,uCACA,6BACA,wCACA,+BACA,gCACA,8BACA,iCACA,qCACA,oCACA,wBACA,yBACA,yBACA,8BACA,gCACA,oCACA,yBACA,+BACA,oCACA,oCACA,iCACA,4BACA,iCACA,gCACA,4BACA,4BACA,wCACA,6BACA,+BACA,gCACA,qCACA,qCACA,kCACA,6BACA,kCACA,iCACA,6BACA,6BACA,yCACA,8BACA,gCACA,4CACA,oCACA,2CACA,mCACA,yCACA,yDACA,uBACA,+BACA,uBACA,uCACA,2BACA,8BACA,uCACA,4BACA,uCACA,oCACA,yBACA,+BACA,yCACA,yBACA,8CACA,uCACA,sBACA,gCACA,8BACA,gCACA,gCACA,gCACA,yCACA,mBACA,wBACA,oCACA,iCACA,6BACA,wCACA,mCACA,4BACA,sBACA,yBACA,8BACA,gDACA,8BACA,mCACA,mCACA,6BACA,sBACA,iDACA,sBACA,4CACA,sBACA,oCACA,6BACA,mCACA,uBACA,yBACA,+BACA,oBACA,2BACA,4BACA,kCACA,2CACA,yBACA,yBACA,mCACA,qBACA,uBACA,8BACA,sCACA,iCACA,mCACA,8BACA,gCACA,qBACA,8BACA,gCACA,wCACA,mCACA,yBACA,wBACA,iCACA,6BACA,0BACA,2BACA,yBACA,uCACA,8CACA,4CACA,gCACA,wCACA,+BACA,mCACA,iCACA,qDACA,0BACA,oCACA,mCACA,oCACA,wCACA,wCACA,iCACA,8CACA,kCACA,gCACA,0CACA,sCACA,gCACA,gCACA,kCACA,+BACA,yBACA,2BACA,mCACA,0DACA,mCACA,gCACA,mCACA,mCACA,iEACA,0DACA,6EACA,mCACA,6DACA,gFACA,mCACA,kBACA,0BACA,kCACA,oCACA,8BACA,kCACA,gCACA,0BACA,iCACA,yCACA,2CACA,2CACA,0BACA,0BACA,gCACA,qCACA,gCACA,0CACA,6BACA,8BACA,8BACA,yBACA,0BACA,qBACA,4BACA,8BACA,oBACA,0BACA,+BACA,gCACA,gCACA,gCACA,gCACA,gCACA,gCACA,gCACA,gCACA,yCACA,0BACA,8BACA,+BACA,qDACA,2BACA,EACO,kBACP,eAyBA,GACA,SAAY,GAAO,EAEnB,GADA,4BACA,SACA,mBACA,oBACA,UACA,IACA,gCACA,QACA,YACA,gBACA,aACA,CAAa,CAEb,CACA,QACA,EA1CA,GACA,oBAAY,GAAqB,EACjC,qBACA,EAIA,+BACA,EACA,KAAmB,OAAU,EAc7B,MAbA,YAAsC,KAAtC,QACA,cAEA,YACA,kCACA,QACA,YACA,gBACA,aACA,CAAS,EAGT,sBACA,CACA,CAmBA,kBACA,2BACA,QACA,YACA,gBACA,aACA,CAAK,CACL,CCpuCA,OAEA,YACA,YACA,CA+BA,cACA,kCACA,CA8BA,cACA,0CACA,CAKA,cACA,YACA,SAEA,SAEA,YAAoB,WAAkB,KACtC,WACA,SACA,CACA,QACA,CAEA,cACA,yCACA,YAEA,kCAIA,iBACA,KAEA,MACA,CAOA,gBACA,MAAW,eAAS,gCACpB,MAOA,2EACA,sCACA,YACA,eAAmC,GAAM,gBAIzC,eACA,wBAEA,OADA,WACuB,EAAc,EACrC,CACA,CACA,oBACA,MACA,eAA+B,GAAM,gBAGrC,OADoB,EAAc,+CAElC,CAAK,CACL,CAEA,cACA,MAAW,eAAS,gCACpB,mCAoCA,GACA,MAAW,eAAS,gCACpB,2BACA,WAEA,EAD4B,EAAc,cAE1C,CAAa,KACb,IACA,CAAa,CACb,CAAS,CACT,CAAK,CACL,EA/CA,EACA,CAAK,CACL,CAEA,cACA,uBACA,2BACA,UAyBA,SAxBA,IAGA,iBAA0C,eAAS,gCACnD,YAUA,CACA,4BACA,UAEA,GACA,MAbA,IACA,2BACA,IACA,CACA,SACA,IACA,CAQA,CAAa,MACb,IACA,CAAa,CACb,GAEA,CAAK,CACL,gBE/KA,cAAmC,gBAMnC,GAAmC,iCANA,aAInC,GAAkC,qGAJC,qBAEH,0JAFG,CAQnC,gBAA2C,qBAAgC,iCAAoC,sCAAoD,2BAA6D,wDAAiE,oBAAsC,SAEvU,cAAiC,YAAgB,mBAAsB,KAAO,yCAAuD,wCAA6D,YAA4C,mIAAoK,gEAAmF,EAAK,SAE1e,kBAAoM,EAAxJ,YAAkB,2BAAkC,kDAAoE,EAAY,GAAP,CAAO,GAAoB,EAIpM,gBAAkC,gBAUlC,GAAgC,8BAVE,aAQlC,KAAyC,IAAgL,IAAhL,+EAA0G,YAAwB,SAAe,KAAe,KAA4B,IAAZ,IAAkB,YAAwB,yBAA4C,gBAAqB,kBAAlC,MAAkC,CAAuC,SAAc,KAAW,KAAY,OAAU,IAAM,+BAAmD,OAAU,cAAsB,WARjd,cAElC,WAA8B,+JAFI,CAIlC,gBAAkD,MAAgB,oCAAgE,oDAAqH,CAA7D,sDAA6D,0CAAsD,sFAE7S,gBAAuC,oCAAuD,uBAAuC,IAAS,IAAO,UAAoB,SAOzK,yBAAqB,EAAQ,EAA0B,EAAG,OAAgB,CAiBnE,CAjBqE,CAiBrE,WACP,gEACA,eACA,iDACA,OACA,KApBO,oBAqBP,sCACA,CACA,EACO,cACP,OACA,KAzBO,iBA0BP,yEACA,CACA,EACO,cACP,OACA,KA9BO,iBA+BP,0EACA,CACA,EACO,GACP,KAlCO,iBAmCP,wBACA,EAYO,gBACP,gDACA,sBAEO,kBACP,aACA,gBACA,4BACA,2BACA,EAAM,sCAAqG,uCAG3G,gBAGA,cACA,cACA,CA4CO,oBACP,0CACA,yBACI,yBACJ,eAKO,qBACP,eAMA,2DACA,+CACA,CAAG,EAPH,4BAQA,CAKO,cACP,kBACA,CAyBO,aACP,0CAAqE,IAAa,IAClF,kBAGA,mBACA,kDAA+F,IAAe,IAC9G,oBAGA,0BAKA,MAJA,UACA,8BAGA,IACA,CAAK,CACL,CACA,CA0GO,cACP,8GACA,CAMO,cACP,2BACA,CCxUA,mBACA,WACA,+GACA,kCAUA,SAAS,EAAc,KAAW,OAAO,EAAlB,OAUd,CAAe,EAAQ,KAVwB,IAUxB,KAAR,EAAQ,cAVwB,IAAS,SAQxD,CAAqB,IAAW,IAAgL,IAAhL,CAR6C,CAQ7C,6EAA0G,YAAwB,SAAe,KAAe,KAA4B,IAAM,CAAlB,GAAkB,YAAwB,yBAA4C,gBAAqB,kBAAlC,MAAkC,CAAuC,SAAc,KAAW,KAAY,OAAU,IAAM,+BAAmD,OAAU,cAAsB,WAR7Z,MAAY,EAA2B,MAAY,SAEhI,EAAqB,OAF2H,GAA5B,MAE/F,+IAF2H,CAIzJ,SAAS,EAA2B,KAAc,MAAgB,gBAA9B,IAA8B,SAAkC,EAAiB,KAAa,YAAb,CAAa,uCAAqH,CAA7D,sDAA6D,0CAAsD,8EAAoF,EAAiB,MAElZ,SAAS,EAFyY,CAExX,IAAa,YAAb,EAAa,sBAAuD,uBAAuC,IAAS,IAAO,UAAoB,SAMzK,SAAS,EAAO,KAA2B,GAA3B,CAA2B,iBAAgC,iCAAoC,sCAAoD,2BAA6D,wDAAiE,oBAAsC,SAEvU,SAAS,EAAa,GAAW,YAAgB,mBAAsB,KAAO,yCAAuD,IAAQ,EAAO,kCAA8C,EAAe,UAA6B,KAA7B,IAA6B,yFAAmH,EAAO,+BAA0C,gEAAmF,EAAK,SAE1e,SAAS,EAAe,OAA4K,EAAxJ,MAApB,IAAoB,EAAkB,2BAAkC,kDAAoE,EAAY,GAAP,CAAO,GAAoB,EAEpM,gBAAsD,oBAA+B,IAA8D,IAA9D,WAErF,KAA2D,oBAA+B,IAAuD,IAAvD,KAAiB,iBAAkD,QAAY,WAAuB,IAAO,OAAqB,kBAA0C,WAA6B,UAF9M,KAA0E,QAAZ,EAAY,uBAAoC,sCAA6D,QAAY,WAA6B,IAAO,QAA2B,mBAA0C,kDAAwE,WAA+B,SAwB5d,OAA4B,gBAAU,eACtC,iBAGA,aA8XA,gEAEA,EAA4B,EAAc,EAAa,EAAG,QAC1D,CADyC,CACzC,CADuD,CACvD,OACA,aACA,sBACA,YACA,YACA,aACA,aACA,gBACA,gBACA,eACA,WACA,mBACA,mBACA,uBACA,qBACA,mBACA,cACA,0BACA,YACA,eACA,WACA,yBACA,YACA,cAEA,EAAmB,aAAO,mBDpM1B,ECqMiC,GDpMjC,eCoMiC,GDpMjC,qBACA,aACA,OACA,OAEA,8BACA,CAAK,KACL,mBACA,iBACA,CAAK,kBC4LL,CAAG,MACH,EAAoB,aAAO,YAC3B,ODlPA,ECkPkC,GDxNlC,EAEA,gBCsNkC,IDrNlC,OA5BA,eCiPkC,GDjPlC,mBACA,aACA,OACA,OAEA,KAYA,OAVA,OACA,4MACA,MAGA,+BACA,wFACA,MAGA,CACA,CAAK,uBACL,aACA,OACA,OAEA,aAA2C,MAAU,KAAoB,MACzE,CAAK,GAAI,CAKT,CAAK,ECoN6B,CAClC,CAAG,MACH,EAA2B,aAAO,YAClC,+BACA,CAAG,MACH,EAA6B,aAAO,YACpC,+BACA,CAAG,MAMH,EAAgB,YAAM,OACtB,GAAiB,YAAM,OAGvB,GAAqB,EADD,cACe,CADf,CAAU,QACK,GACnC,SACA,SAEA,gBACA,yBACA,GAA4B,YAAM,wDDpRlC,eCoRgH,QDpRhH,QCsRA,SAFyI,KAAK,CAI9I,gBACA,sBACA,aACA,iBAEA,SACA,IACA,kBACA,CAAa,EACb,KAGA,CAAO,KAEP,EAEE,eAAS,YAEX,OADA,uCACA,WACA,yCACA,CACA,CAAG,eACH,OAAuB,YAAM,KAE7B,eACA,0CAKA,mBACA,cACA,EAEE,eAAS,YAMX,OALA,IACA,qCAA4C,EAAkB,IAC9D,YAD8D,aAC9D,gBAGA,WACA,IACA,wCAAiD,GACjD,eADmE,aACnE,YAEA,CACA,CAAG,QAED,CAFsC,EAEtC,YAAS,YAKX,MAJA,kBACA,kBAGA,YACA,CAAG,UACH,OAAgB,iBAAW,aAC3B,EACA,KAGA,gBAEA,CAAG,MACH,GAAsB,iBAAW,iBA9iBN,EA+iB3B,mBAEA,CAF4B,CAE5B,UACA,MACA,qBA7iBA,SAAS,CAAkB,EAAQ,QANyB,CAMzB,QAAR,GAAQ,OAA+B,EAAiB,IANxD,EAmjB8B,KAnjBtB,KAmjBsB,CA7iB0B,EANd,SAI5D,CAAgB,EAAS,MAJmD,SAI5D,CAAS,qFAJmD,IAAS,EAA2B,IAAS,SAEzH,EAAuB,SAFoH,GAA3B,IAEzF,EAAL,qIAAK,IAijByB,YAEjD,EAAc,IACtB,QADsB,OACtB,wBACA,IAAY,EAAoB,OAIhC,ID9cO,EACP,EACA,EACA,CCucgC,CDtchC,EACA,EACA,EACA,ECucA,WACA,QD9cA,CC8c4C,CD9c5C,CADO,EC+cqD,CAC5D,QACA,GAF4D,IAE5D,EACA,UACA,UACA,WACA,WACA,WACA,CAAS,EDtdT,MACA,WACA,YACA,YACA,aACA,aACA,cAEA,oDAIA,oBACA,IAEA,EADA,EADA,OACA,EACA,IAIA,EADA,EADA,SACA,EACA,IAEA,cACA,eACA,CAAG,GCicH,IACA,eACA,aAHA,QAIA,gBACA,sBACA,CAAS,EAET,GACA,KAEA,CAAO,oBACP,YACA,CAAO,CAEP,CAAG,yBACH,GAAqB,iBAAW,aAChC,mBACA,YACA,MACA,MAAmB,EAAc,GAEjC,SAFiC,WAEjC,CACA,IACA,gCACA,CAAQ,UASR,OAJA,MACA,KAGA,EACA,CAAG,QACH,GAAsB,iBAAW,aACjC,mBACA,YACA,MAEA,KAF4B,CAE5B,8BACA,uCACA,CAAK,EAGL,CAHQ,CAGR,mBAEA,SACA,cAGA,cAEA,eAIA,IACA,uBACA,gBACA,gBACA,eACA,CAAK,EAEG,EAAc,OACtB,KADsB,CAGnB,UACH,GAAiB,iBAAW,eAC5B,SACA,KACA,sBACA,IACA,EAA2B,EADD,EAAY,KACG,GACzC,EAFsC,CAEtC,CADyC,EACzC,CACA,OAGA,EAA4B,EADD,EAAa,OACE,GAC1C,CAFwC,CACE,CAC1C,IACA,OAEA,cAEA,YACA,cACQ,CACR,YAEA,GACA,gBAGA,QACA,OACA,4BACA,QACA,CAAW,CACX,CAAS,CACT,CACA,CAAK,EAEL,wCAEA,sBACA,QACA,OACA,QAAmB,EAAwB,CAClC,CACT,CAAO,EACP,aAGA,IAN2C,cAO3C,EACA,iBACA,wBACA,eACA,CAAK,EAEL,GACA,SAGA,eACA,OAGA,eACA,MAEA,CAAG,yBACH,GAAiB,iBAAW,aAC5B,mBAEA,CAF4B,CAE5B,UACA,MACA,cAEQ,EAAc,IACtB,QADsB,OACtB,0BACY,EAAoB,QAIhC,OACA,CAAO,oBACP,YACA,CAAO,EAGP,IACA,YACA,CAAK,CACL,CAAG,cAEH,CAFoE,EAE7C,iBAAW,YAGlC,eACA,IACA,iBACA,CAAO,EACP,IAMA,kBAN4B,OAM5B,CAJA,CACA,WACA,OACA,GACA,iBACA,WACA,CAAO,mBACP,WACA,IACA,kBACA,CAAS,CACT,CAAO,oBDldP,2CCodmB,EDpdnB,6BCqdA,KACA,IACA,kBACA,CAAW,GD7cX,gFC+cA,cAGA,KAH+C,KAG/C,EACA,sBACA,oBAEA,4KAGA,KAEA,CAAO,EACP,MACA,CAEA,aACA,IACA,iBACA,CAAO,EACP,IACA,sBACA,mBAEA,CAAG,uBAEH,CAFsH,EAElG,iBAAW,aAE/B,4CAIA,iEACA,mBACA,KAEA,CAAG,SAEH,CAFiC,EAEf,iBAAW,YAC7B,IACA,YACA,CAAK,CACL,CAAG,KACH,GAAiB,iBAAW,YAC5B,IACA,WACA,CAAK,CACL,CAAG,KAEH,CAFU,EAEQ,iBAAW,YAC7B,IDloBO,UCyoBW,CDxoBlB,wFACA,OATA,oDAIA,KAKA,EALA,gBAMA,ICuoBA,iBAEA,KAEA,CAAG,SAEH,eACA,eACA,EAEA,eACA,mBACA,EAEA,eACA,mBACA,EAEA,eACA,GACA,mBAEA,EAEA,GAAqB,aAAO,YAC5B,kBACA,+DAAwF,CACxF,WAEA,SACA,cACA,YACA,WACA,YACA,gBACA,eACA,gBACA,WACA,SAEA,OAAa,EAAc,EAAc,EAAe,CACxD,QAD0B,EAAc,GACE,CADc,CACM,OAC9D,WAAwC,EAAoB,OAC5D,UAAuC,CADqB,CACD,OAC3D,WAD2D,EACP,OACpD,WADoD,CACpD,GAAwC,EAAoB,OAC5D,WAD4D,GACrB,EAAoB,OAC3D,WAD2D,CAC3D,GAAwC,EAAoB,OAC5D,UAAmC,CADyB,CACL,OACvD,WADuD,IACvD,iCACA,CAAO,CAtBP,mBAsBO,QAEC,EAAI,CAFL,CACP,UACA,EAAQ,CAAI,EACZ,CACA,CAAG,oCACH,GAA4B,iBAAW,aACvC,mBACA,CAAG,KACH,GAAsB,aAAO,YAC7B,kBACA,+DAAwF,CACxF,WAEA,aACA,YACA,SAuBA,OAAa,EAAc,EAAa,EAAG,CArBpB,EAAe,CACtC,KAoB0B,EAAc,EAnBxC,KAFsC,IAEtC,EACA,YACA,OACA,SACA,wBACA,sBACA,aACA,uBACA,kBACA,UACA,oBACA,YACA,mBACA,CAAS,CACT,YAAiC,EAAoB,OACrD,WADqD,EACD,OACpD,WADoD,CAE7C,CAxBP,mBAwBO,KAEoC,EAC3C,CACA,CAAG,gBACH,OAAS,EAAc,EAAa,EAAG,OAAY,EAA7B,EAAc,QACpC,OACA,gBACA,iBACA,UACA,YACA,WACA,CAAG,CACH,EAv2BA,QAGA,SACA,SAQA,MANE,yBAAmB,cACrB,OACA,MACA,CACA,CAAG,MAEmB,CAFR,CAEQ,aAAmB,CAAC,UAAQ,QAAiB,EAAc,EAAa,EAAG,MAAY,EAC7G,CADgF,EAAc,EAC9F,CACA,CAAG,GACH,CAAC,EACD,0BAEA,CAFmC,GAEnC,IACA,YACA,kBH5DO,CG4Dc,QH5Dd,CG4DuB,EH3D9B,MAAW,eAAS,oCAgBpB,EAfA,SAaA,EAbA,gBACA,gBA8BA,KACA,MAAW,eAAS,gCAGpB,YACA,iBACA,iCAGA,WACA,EAGA,EAuCA,cACA,2BACA,KACA,0BACA,IACA,EA7CA,+BAEA,CACA,oBACA,OAAyB,EAAc,IACvC,CAAK,CACL,EAhDA,IA8CuC,UA9CvC,SAEA,GAaA,EADA,EAZA,IAaA,YAZA,OAkBA,EAlBA,EAkBA,qBAAkD,EAAc,WAhBhE,CAgBgE,KAhBhE,oEACA,SAkBA,GACA,MAAW,eAAS,gCAEpB,MADA,2CACA,OAAiC,EAAc,GAC/C,CAAK,CACL,EAvBA,GAEA,GACK,CACL,EGgDA,YACA,UACA,YACA,WACA,yBACA,WACA,cACA,UACA,wBACA,eACA,kBACA,YACA,EACA,mBACA,cAgBA,SAAY,MAAc,CAS1B,OAAU,UAAkB,CAAC,SAAiB,CAAC,QAAgB,GAK/D,SAAY,MAAc,CAK1B,sBAAyB,MAAc,CAKvC,QAAW,MAAc,CAMzB,WAAc,MAAc,CAK5B,OAAU,MAAc,CAKxB,qBAAwB,MAAc,CAKtC,QAAW,QAAgB,CAK3B,QAAW,QAAgB,CAM3B,SAAY,QAAgB,CAK5B,SAAY,MAAc,CAO1B,kBAAqB,MAAc,CAKnC,mBAAsB,MAAc,CAKpC,iBAAoB,MAAc,CAMlC,eAAkB,MAAc,CAKhC,UAAa,MAAc,CAO3B,YAAe,MAAc,CAO7B,YAAe,MAAc,CAO7B,WAAc,MAAc,CAgC5B,OAAU,MAAc,CASxB,eAAkB,MAAc,CAShC,eAAkB,MAAc,CAOhC,QAAW,MAAc,CAOzB,UAAa,MAAc,EAwE3B,QACA,aACA,sBACA,gBACA,gBACA,gBACA,iBACA,mBA8jBA,iBAEA,eACA,YACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,OACxC,EACA,CAAO,CAEP,YACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,OACxC,EACA,CAAO,CAEP,kBACA,OAAa,EAAc,EAAa,EAAG,OAAmB,EAApC,EAAc,iBACxC,EACA,CAAO,CAEP,mBACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,gBACxC,EACA,CAAO,CAEP,uBACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,UACxC,eACA,4BACA,4BACO,CAEP,gBACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,WACxC,gBACA,gCACA,2BACA,CAAO,CAEP,aACA,OAAa,EAAa,EAAG,IAE7B,OAF0B,EAG1B,QACA,CACA,CAEA,kEC18BM,GAAgB,WAIhB,CAAC,GAAuB,GAAmB,CAAI,QAAkB,CAAC,IAIlE,CAAC,CAJ0C,EAIxB,GAAkB,CACzC,CALmF,EAKvC,IAUxC,GAAiB,IAXoB,EACgB,MAUpC,CACrB,CAAC,EAAmC,SAwGV,EAIE,EA3G1B,GAAM,IAuGqC,EAIE,WA1G3C,EAsGgE,EAIE,IAzG3D,EAAY,KACnB,IAAK,gBACL,EAAgB,GAChB,GAAG,EACL,CAAI,CAEJ,EAAK,GAAW,MAAY,EAAM,CAAC,GAAiB,IAClD,GADyD,GAAG,EACpD,MAAM,GAAmB,GAAG,EAAO,EA+FxC,CA/F4C,EAAJ,QAAc,CAAC,uBA+FpB,SAAS,oBAAoB,aAAa,8DAAoF,GA5FtK,IAAM,EAAM,EA4F+I,CA5F9H,GAAW,EAhCxB,EAgCoB,CAEhC,EA0FkK,GA5FpH,GAExB,GAAC,GAAmB,EAAW,GAAG,CAC1D,EAD6D,MACrD,MAAM,GAAqB,GAAG,EAAS,EA6F5C,CA7FgD,IAAJ,MAAc,CAAC,yBA6FtB,SAAS,oBAAoB,aAAa;;;;;wBAE7B,GA5FvD,IAAM,EAAQ,GAAmB,EAAW,GAAG,EAAgB,KACzD,EAAa,GAAS,GAAS,EAAc,EAAO,GAAG,KAAI,EAEjE,MACE,UAAC,IAAiB,MAAO,QAAiB,MAAc,EACtD,mBAAC,KAAS,CAAC,IAAV,CACC,gBAAe,EACf,gBAAe,EACf,gBAAe,GAAS,GAAS,EAAJ,KAAY,EACzC,iBAAgB,EAChB,KAAK,cACL,aAAY,GAAiB,EAAO,GAAG,aAC3B,GAAS,OACrB,WAAU,EACT,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,GAGF,GAAS,YAAc,GAMvB,IAAM,GAAiB,oBAKjB,GAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,iBAAE,EAAiB,GAAG,EAAe,CAAI,EACzC,EAAU,GAAmB,GAAgB,EADR,CAE3C,MACE,MAFgE,EAEhE,EAAC,KAAS,CAAC,IAAV,CACC,aAAY,GAAiB,EAAQ,MAAO,EAAQ,GAAG,EACvD,aAAY,EAAQ,OAAS,OAC7B,WAAU,EAAQ,IACjB,GAAG,EACJ,IAAK,GAGX,GAOF,SAAS,GAAqB,EAAe,GAAa,MACjD,GAAG,KAAK,MAAO,EAAQ,EAAO,GAAG,CAAC,IAG3C,SAAS,GAAiB,EAAkC,GAC1D,OAAgB,MAAT,EAAgB,gBAAkB,IAAU,EAAW,WAAa,SAC7E,CAEA,SAAS,GAAS,GAA6B,MACrB,UAAjB,OAAO,CAChB,CAEA,SAAS,GAAiB,GAAyB,OAG/C,GAAS,GAAG,CACZ,CAAC,MAAM,GAAG,CACV,EAAM,CAEV,CAEA,SAAS,GAAmB,EAAY,GAEtC,OACE,GAAS,IACT,CADc,MACP,IACP,CADY,EACH,GACT,GAAS,CAEb,CAjCA,GAAkB,YAAc,mBC7FhC,SAASA,GAAS,CAChBC,WAAS,EADMD,KAEfE,CAAK,CACL,GAAGC,EACiD,EACpD,MAAO,UAACC,GAAsB,aAAW,WAAWH,UAAWI,CAAAA,EAAAA,GAAAA,EAAAA,CAAEA,CAAC,iEAAkEJ,GAAa,GAAGE,CAAK,CAAEG,sBAAoB,yBAAyBC,wBAAsB,WAAWC,0BAAwB,wBAC7P,UAACJ,GAA2B,CAACK,KAAD,OAAW,qBAAqBR,UAAU,iDAAiDS,MAAO,CAC9HC,UAAW,CAAC,YAAY,EAAE,KAAOT,CAAAA,GAAS,EAAG,EAAE,CAAC,EAC/CI,sBAAoB,8BAA8BE,0BAAwB,kBAEjF,6BEyEO,SAASI,GAAaT,CAAwB,EACnD,GAAM,CACJD,MAAOW,CAAS,eAChBC,CAAa,UACbC,CAAQ,YACRC,CAAU,QACVC,EAAS,CACP,UAAW,EAAE,CACd,SACDC,EAAU,OAAO,OAAO,GACxBC,EAAW,CAAC,CACZC,YAAW,CAAK,UAChBC,GAAW,CAAK,WAChBpB,CAAS,CACT,GAAGqB,EACJ,CAAGnB,EACE,CAACoB,EAAOC,EAAS,CAAGC,SD3FnBA,MACPC,CAAI,IC0F0CD,SDzF9CE,CAAW,UACXC,EAAW,KAAO,CAAC,CACW,EAC9B,GAAM,CAACC,EAAkBC,EAAoB,CAkB/C,SAASC,CACPJ,aAAW,UACXC,CAAQ,CACoC,EAC5C,IAAMI,EAAoBC,EAAAA,QAAc,CAAgBN,GAClD,CAACzB,EAAM,CAAG8B,EACVE,EAAeD,EAAAA,MAAY,CAAC/B,GAC5BiC,EAAeC,CAAAA,EAAAA,GAAAA,CAAAA,CAAcA,CAACR,GAOpC,OANAK,EAAAA,SAAe,CAAC,KACVC,EAAaG,OAAO,GAAKnC,IAC3BiC,EAAajC,CADqB,EAElCgC,EAAaG,OAAO,CAAGnC,EAE3B,EAAG,CAACA,EAAOgC,EAAcC,EAAa,EAC/BH,CACT,EAjCuE,aACnEL,WACAC,CACF,GACMU,OAAwBC,IAATb,EACfxB,EAAQoC,EAAeZ,EAAOG,EAC9BM,EAAeC,CAAAA,EAAAA,GAAAA,CAAAA,CAAcA,CAACR,GAUpC,MAAO,CAAC1B,EAT8D+B,EAAAA,WAAiB,CAACO,IACtF,GAAIF,EAAc,CAEhB,IAAMpC,EAA6B,YAArB,OAAOsC,EADNA,EACwCd,GAAQc,EAC3DtC,IAAUwB,GAAMS,EAAajC,EACnC,MACE4B,CADK,CACeU,EAExB,EAAG,CAACF,EAAcZ,EAAMI,EAAqBK,EAAa,EAClC,ECsEuB,CAC7CT,KAAMb,EACNe,SAAUd,CACZ,GACM2B,EAASR,EAAAA,WAAiB,CAAC,CAACS,EAAuBC,KACvD,GAAI,CAACvB,GAAyB,IAAbD,GAAkBuB,EAAcE,MAAM,CAAG,EAAG,YAC3DC,GAAAA,KAAKA,CAACC,KAAK,CAAC,4CAGd,GAAI,CAACvB,GAAOqB,SAAU,EAAKF,EAAcE,MAAM,CAAGzB,EAAU,YAC1D0B,GAAAA,KAAKA,CAACC,KAAK,CAAC,CAAC,wBAAwB,EAAE3B,EAAS,MAAM,CAAC,EAGzD,IAAM4B,EAAWL,EAAcM,GAAG,CAACC,GAAQC,OAAOC,MAAM,CAACF,EAAM,CAC7DG,QAASC,IAAIC,eAAe,CAACL,EAC/B,IACMM,EAAehC,EAAQ,IAAIA,KAAUwB,EAAS,CAAGA,EASvD,GARAvB,EAAS+B,GACLZ,EAAcC,MAAM,CAAG,GAAG,EACdY,OAAO,CAAC,CAAC,MACrBP,CAAI,CACL,IACCJ,GAAAA,KAAKA,CAACC,KAAK,CAAC,CAAC,KAAK,EAAEG,EAAKQ,IAAI,CAAC,aAAa,CAAC,CAC9C,GAEE1C,GAAYwC,EAAaX,MAAM,CAAG,GAAKW,EAAaX,MAAM,EAAIzB,EAAU,CAC1E,IAAMuC,EAASH,EAAaX,MAAM,CAAG,EAAI,GAAGW,EAAaX,MAAM,CAAC,MAAM,CAAC,CAAG,CAAC,IAAI,CAAC,CAChFC,GAAAA,KAAKA,CAACc,OAAO,CAAC5C,EAASwC,GAAe,CACpCK,QAAS,CAAC,UAAU,EAAEF,EAAO,GAAG,CAAC,CACjCG,QAAS,KACPrC,EAAS,EAAE,EACJ,GAAGkC,EAAO,SAAS,CAAC,EAE7BZ,MAAO,CAAC,iBAAiB,EAAEY,EAAAA,CAAQ,EAEvC,CACF,EAAG,CAACnC,EAAOJ,EAAUC,EAAUL,EAAUS,EAAS,EASlDS,EAAAA,SAAe,CAAC,IACP,KACAV,GACLA,EAAMiC,EADM,KACC,CAACP,IACRa,GAAkBb,IACpBI,GAD2B,CACvBU,eAAe,CAACd,EAAKG,OAAO,CAEpC,EACF,EAEC,EAAE,EACL,IAAMY,EAAa3C,GAAY,CAACE,GAAOqB,QAAU,IAAMzB,EACvD,MAAO,WAAC8C,MAAAA,CAAIhE,UAAU,+CAA+CM,wBAAsB,eAAeC,0BAAwB,8BAC9H,UAAC0D,GAAQA,CAACzB,GAADyB,CJ0HQ,EAAC,CI1HAzB,EAAQxB,OAAQA,EAAQC,QAASA,EAASC,SAAUA,EAAUC,SAAUD,EAAW,GAAKC,EAAUC,SAAU2C,EAAY1D,sBAAoB,WAAWE,0BAAwB,6BAC9L,CAAC,cACF2D,CAAY,eACZC,CAAa,cACbC,CAAY,CACb,GAAK,WAACJ,MAAAA,CAAK,GAAGE,GAAc,CAAElE,UAAWI,CAAAA,EAAAA,GAAAA,EAAAA,CAAEA,CAAC,sLAAuL,+HAAgIgE,GAAgB,6BAA8BL,GAAc,iCAAkC/D,GAAa,GAAGqB,CAAa,WACzd,UAACgD,QAAAA,CAAO,GAAGF,GAAe,GACzBC,EAAe,WAACJ,MAAAA,CAAIhE,UAAU,oEAC3B,UAACgE,MAAAA,CAAIhE,UAAU,iDACb,UAACsE,EAAUA,CAACtE,OAADsE,GAAW,+BAA+BC,cAAY,WAEnE,UAACC,IAAAA,CAAExE,UAAU,6CAAoC,2BAG1C,WAACgE,MAAAA,CAAIhE,UAAU,oEACtB,UAACgE,MAAAA,CAAIhE,UAAU,iDACb,UAACsE,EAAUA,CAACtE,OAADsE,GAAW,+BAA+BC,cAAY,WAEnE,WAACP,MAAAA,CAAIhE,UAAU,uBACb,WAACwE,IAAAA,CAAExE,UAAU,8CAAoC,QACzC,CAAC,GAAG,CAAC,CAAC,gDAEd,WAACwE,IAAAA,CAAExE,UAAU,6CAAmC,iBAE7CkB,EAAW,EAAI,CAAC,CAAC,EAAEA,IAAauD,IAAW,WAAavD,SAAS;mCACnD,EAAEwD,CAAAA,EAAAA,GAAAA,EAAAA,CAAWA,CAACzD,GAAS,MAAM,CAAC,CAAG,CAAC,aAAa,EAAEyD,CAAAA,EAAAA,GAAAA,EAAAA,CAAWA,CAACzD,GAAAA,CAAU,eAMnGK,GAAOqB,OAAS,UAACgC,GAAAA,UAAUA,CAAAA,CAAC3E,UAAU,6BACnC,UAACgE,MAAAA,CAAIhE,UAAU,8BACZsB,GAAOyB,IAAI,CAACC,EAAM4B,IAAU,UAACC,GAAAA,CAAqB7B,KAAMA,EAAM8B,SAAU,IAAMA,CAtDzF,SAAkBF,CAAa,EAC7B,GAAI,CAACtD,EAAO,OACZ,IAAMwB,EAAWxB,EAAMyD,MAAM,CAAC,CAACC,EAAGC,IAAMA,IAAML,GAC9CrD,EAASuB,GACTjC,IAAgBiC,GAClB,EAiDkG8B,GAAQM,SAAUnE,GAAY,CAACiC,EAAKQ,IAAI,CAAC,EAArFoB,QAEhC,OAExB,CAMA,SAASC,GAAS,MAChB7B,CAAI,UACJkC,CAAQ,UACRJ,CAAQ,CACM,EACd,MAAO,WAACd,MAAAA,CAAIhE,UAAU,uCAAuCM,wBAAsB,WAAWC,0BAAwB,8BAClH,WAACyD,MAAAA,CAAIhE,UAAU,kCACZ6D,GAAkBb,GAAQ,UAACmC,EAAAA,OAAKA,CAAAA,CAACC,IAAKpC,EAAKG,OAAO,CAAEkC,IAAKrC,EAAKQ,IAAI,CAAE8B,MAAO,GAAIC,OAAQ,GAAI5B,QAAQ,OAAO3D,UAAU,mDAAsD,KAC3K,WAACgE,MAAAA,CAAIhE,UAAU,uCACb,WAACgE,MAAAA,CAAIhE,UAAU,uBACb,UAACwE,IAAAA,CAAExE,UAAU,+DACVgD,EAAKQ,IAAI,GAEZ,UAACgB,IAAAA,CAAExE,UAAU,yCACV0E,CAAAA,EAAAA,GAAAA,EAAAA,CAAWA,CAAC1B,EAAKwC,IAAI,OAGzBN,EAAW,UAACnF,GAAQA,CAACE,MAAOiF,IAAe,GAAvBnF,QAGzB,UAACiE,MAAAA,CAAIhE,UAAU,mCACb,WAACyF,GAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQH,KAAK,OAAOI,QAASd,EAAU1D,SAAU8D,YAA0BA,EAAW,IAAKlF,UAAU,sBAAsBK,sBAAoB,SAASE,0BAAwB,8BAC5M,UAACsF,EAAAA,CAAKA,CAAAA,CAAC7F,UAAU,wBAAwBK,sBAAoB,QAAQE,0BAAwB,sBAC7F,UAACuF,OAAAA,CAAK9F,UAAU,mBAAU,uBAIpC,CACA,SAAS6D,GAAkBb,CAAU,EAGnC,MAAO,YAAaA,GAAgC,UAAxB,OAAOA,EAAKG,OAAO,qGC9NjD,IAAM4C,GAAuB,CAAC,aAAc,YAAa,YAAa,aAAa,CAC7EC,GAAaC,GAAAA,EAAQ,CAAC,CAC1BC,MAAOD,GAAAA,EAAK,GAAGE,MAAM,CAAC7E,GAASA,GAAOqB,QAAU,EAAG,sBAAsBwD,MAAM,CAAC7E,GAASA,GAAO,CAAC,EAAE,EAAEkE,MAHjF,EAGyFY,EAAe,CAAC,qBAAqB,CAAC,EAAED,MAAM,CAAC7E,GAASyE,GAAqBM,QAAQ,CAAC/E,GAAO,CAAC,EAAE,EAAEoE,MAAO,mDACtNlC,KAAMyC,GAAAA,EAAQ,GAAGK,GAAG,CAAC,EAAG,CACtBC,QAAS,6CACX,GACAC,SAAUP,GAAAA,EAAQ,GAClBQ,MAAOR,GAAAA,EAAQ,GACfS,YAAaT,GAAAA,EAAQ,GAAGK,GAAG,CAAC,GAAI,CAC9BC,QAAS,6CACX,EACF,GACe,SAASI,GAAY,aAClCC,CAAW,CACXC,WAAS,CAIV,EACC,IAAMC,EAAgB,CACpBtD,KAAMoD,GAAapD,MAAQ,GAC3BgD,SAAUI,GAAaJ,UAAY,GACnCC,MAAOG,GAAaH,OAAS,EAC7BC,YAAaE,GAAaF,aAAe,EAC3C,EACMK,EAAOC,CAAAA,EAAAA,GAAAA,EAAAA,CAAOA,CAA6B,CAC/CC,SAAUC,CAAAA,EAAAA,GAAAA,CAAAA,CAAWA,CAAClB,IACtBmB,OAAQL,CACV,GAIA,MAAO,WAACM,GAAAA,EAAIA,CAAAA,CAACpH,UAAU,iBAAiBK,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,6BAC3H,UAAC8G,GAAAA,EAAUA,CAAAA,CAAChH,sBAAoB,aAAaE,0BAAwB,4BACnE,UAAC+G,GAAAA,EAASA,CAAAA,CAACtH,UAAU,+BAA+BK,sBAAoB,YAAYE,0BAAwB,4BACzGsG,MAGL,UAACU,GAAAA,EAAWA,CAAAA,CAAClH,sBAAoB,cAAcE,0BAAwB,4BACrE,UAACiH,GAAAA,EAAIA,CAAAA,CAAE,GAAGT,CAAI,CAAE1G,sBAAoB,OAAOE,0BAAwB,4BACjE,WAACwG,OAAAA,CAAKU,SAAUV,EAAKW,YAAY,CAACD,SAXjCA,CAA2C,EAEpD,GASqDzH,UAAU,sBACrD,UAAC2H,GAAAA,EAASA,CAAAA,CAACC,QAASb,EAAKa,OAAO,CAAEpE,KAAK,QAAQqE,OAAQ,CAAC,OACxDC,CAAK,CACN,GAAK,UAAC9D,MAAAA,CAAIhE,UAAU,qBACb,WAAC+H,GAAAA,EAAQA,CAAAA,CAAC/H,UAAU,mBAClB,UAACgI,GAAAA,EAASA,CAAAA,UAAC,WACX,UAACC,GAAAA,EAAWA,CAAAA,UACV,UAACtH,GAAYA,CAACV,MAAO6H,EAAM7H,KAAK,CAAEY,cAAeiH,EAAMnG,QAAQ,CAAET,SAAU,EAAGD,QAAS,IAAI,OAAO,CAQpG,UAACiH,GAAAA,EAAWA,CAAAA,CAAAA,QAER7H,sBAAoB,YAAYE,0BAAwB,qBAEpE,WAACyD,MAAAA,CAAIhE,UAAU,kDACb,UAAC2H,GAAAA,EAASA,CAAAA,CAACC,QAASb,EAAKa,OAAO,CAAEpE,KAAK,OAAOqE,OAAQ,CAAC,OACvDC,CAAK,CACN,GAAK,WAACC,GAAAA,EAAQA,CAAAA,WACP,UAACC,GAAAA,EAASA,CAAAA,UAAC,iBACX,UAACC,GAAAA,EAAWA,CAAAA,UACV,UAACE,GAAAA,CAAKA,CAAAA,CAACC,YAAY,qBAAsB,GAAGN,CAAK,KAEnD,UAACI,GAAAA,EAAWA,CAAAA,CAAAA,MACD7H,sBAAoB,YAAYE,0BAAwB,qBACzE,UAACoH,GAAAA,EAASA,CAAAA,CAACC,QAASb,EAAKa,OAAO,CAAEpE,KAAK,WAAWqE,OAAQ,CAAC,OAC3DC,CAAK,CACN,GAAK,WAACC,GAAAA,EAAQA,CAAAA,WACP,UAACC,GAAAA,EAASA,CAAAA,UAAC,aACX,WAACK,GAAAA,EAAMA,CAAAA,CAACxH,cAAeZ,GAAS6H,EAAMnG,QAAQ,CAAC1B,GAAQA,MAAO6H,EAAM7H,KAAK,CAAC6H,EAAM7H,KAAK,CAAC0C,MAAM,CAAG,EAAE,WAC/F,UAACsF,GAAAA,EAAWA,CAAAA,UACV,UAACK,GAAAA,EAAaA,CAAAA,UACZ,UAACC,GAAAA,EAAWA,CAAAA,CAACH,YAAY,0BAG7B,WAACI,GAAAA,EAAaA,CAAAA,WACZ,UAACC,GAAAA,EAAUA,CAAAA,CAACxI,MAAM,kBAAS,oBAC3B,UAACwI,GAAAA,EAAUA,CAAAA,CAACxI,MAAM,uBAAc,gBAChC,UAACwI,GAAAA,EAAUA,CAAAA,CAACxI,MAAM,oBAAW,aAC7B,UAACwI,GAAAA,EAAUA,CAAAA,CAACxI,MAAM,gBAAO,kBACzB,UAACwI,GAAAA,EAAUA,CAAAA,CAACxI,MAAM,kBAAS,4BAK/B,UAACiI,GAAAA,EAAWA,CAAAA,CAAAA,MACD7H,sBAAoB,YAAYE,0BAAwB,qBACzE,UAACoH,GAAAA,EAASA,CAAAA,CAACC,QAASb,EAAKa,OAAO,CAAEpE,KAAK,QAAQqE,OAAQ,CAAC,CACxDC,OAAK,CACN,GAAK,WAACC,GAAAA,EAAQA,CAAAA,WACP,UAACC,GAAAA,EAASA,CAAAA,UAAC,UACX,UAACC,GAAAA,EAAWA,CAAAA,UACV,UAACE,GAAAA,CAAKA,CAAAA,CAACzC,KAAK,SAASgD,KAAK,OAAON,YAAY,cAAe,GAAGN,CAAK,KAEtE,UAACI,GAAAA,EAAWA,CAAAA,CAAAA,MACD7H,sBAAoB,YAAYE,0BAAwB,wBAE3E,UAACoH,GAAAA,EAASA,CAAAA,CAACC,QAASb,EAAKa,OAAO,CAAEpE,KAAK,cAAcqE,OAAQ,CAAC,OAC9DC,CAAK,CACN,GAAK,WAACC,GAAAA,EAAQA,CAAAA,WACP,UAACC,GAAAA,EAASA,CAAAA,UAAC,gBACX,UAACC,GAAAA,EAAWA,CAAAA,UACV,UAACU,GAAAA,CAAQA,CAAAA,CAACP,YAAY,4BAA4BpI,UAAU,cAAe,GAAG8H,CAAK,KAErF,UAACI,GAAAA,EAAWA,CAAAA,CAAAA,MACD7H,sBAAoB,YAAYE,0BAAwB,qBACzE,UAACkF,GAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASrF,sBAAoB,SAASE,0BAAwB,4BAAmB,yBAK1G,gECvHA,SAAS4B,EAAwDyG,CAAuB,EACtF,IAAMC,EAAc7G,EAAAA,MAAY,CAAC4G,GAMjC,OALA5G,EAAAA,SAAe,CAAC,KACd6G,EAAYzG,OAAO,CAAGwG,CACxB,GAGO5G,EAAAA,OAAa,CAAC,IAAO,CAAC,GAAG8G,IAASD,EAAYzG,OAAO,MAAM0G,GAAa,EAAE,CACnF,0BClBA,mECAA,0GCAA,wDCEA,aAAkB,IAElB,SAAe,eACf,SACA,sCAEA,gBACA,SAGA,iBACA,6BACA,wBACA,0BACA,mCAEA,kBACA,4BACQ,iBAER,0BAGA,KACA,CAAK,CACL,CAEA,QACA,yBC9BA,0CCAA,uCAAiK,CAEjK,uCAA+K,6KCM/K,IAAMtB,EAAOuB,EAAAA,EAAYA,CAInBC,EAAmBhH,EAAAA,aAAmB,CAAwB,CAAC,GAC/D2F,EAAY,CAAkH,CAClI,GAAGzH,EACkC,GAC9B,UAAC8I,EAAiBC,QAAQ,EAAChJ,MAAO,CACvCuD,KAAMtD,EAAMsD,IAAI,EACfnD,sBAAoB,4BAA4BC,wBAAsB,YAAYC,0BAAwB,oBACzG,UAAC2I,EAAAA,EAAUA,CAAAA,CAAE,GAAGhJ,CAAK,CAAEG,sBAAoB,aAAaE,0BAAwB,eAGhF4I,EAAe,KACnB,IAAMC,EAAepH,EAAAA,UAAgB,CAACgH,GAChCK,EAAcrH,EAAAA,UAAgB,CAACsH,GAC/B,eACJC,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,GACZC,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAYA,CAAC,CAC7BlG,KAAM4F,EAAa5F,IAAI,GAEnBmG,EAAaJ,EAAcH,EAAa5F,IAAI,CAAEiG,GACpD,GAAI,CAACL,EACH,MAAUQ,MAAM,kDAElB,GAAM,IACJC,CAAE,CACH,CAAGR,EACJ,MAAO,CACLQ,KACArG,KAAM4F,EAAa5F,IAAI,CACvBsG,WAAY,GAAGD,EAAG,UAAU,CAAC,CAC7BE,kBAAmB,GAAGF,EAAG,sBAAsB,CAAC,CAChDG,cAAe,GAAGH,EAAG,kBAAkB,CAAC,CACxC,GAAGF,CAAU,CAEjB,EAIML,EAAkBtH,EAAAA,aAAmB,CAAuB,CAAC,GACnE,SAAS+F,EAAS,WAChB/H,CAAS,CACT,GAAGE,EACyB,EAC5B,IAAM2J,EAAK7H,EAAAA,KAAW,GACtB,MAAO,UAACsH,EAAgBL,QAAQ,EAAChJ,MAAO,IACtC4J,CACF,EAAGxJ,sBAAoB,2BAA2BC,wBAAsB,WAAWC,0BAAwB,oBACvG,UAACyD,MAAAA,CAAIxD,YAAU,YAAYR,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,aAAcJ,GAAa,GAAGE,CAAK,IAElF,CACA,SAAS8H,EAAU,WACjBhI,CAAS,CACT,GAAGE,EAC8C,EACjD,GAAM,OACJ2C,CAAK,YACLiH,CAAU,CACX,CAAGX,IACJ,MAAO,UAACc,EAAAA,CAAKA,CAAAA,CAACzJ,YAAU,aAAa0J,aAAY,CAAC,CAACrH,EAAO7C,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCJ,GAAYmK,QAASL,EAAa,GAAG5J,CAAK,CAAEG,sBAAoB,QAAQC,wBAAsB,YAAYC,0BAAwB,YAClP,CACA,SAAS0H,EAAY,CACnB,GAAG/H,EAC+B,EAClC,GAAM,OACJ2C,CAAK,YACLiH,CAAU,mBACVC,CAAiB,eACjBC,CAAa,CACd,CAAGb,IACJ,MAAO,UAACiB,EAAAA,EAAIA,CAAAA,CAAC5J,YAAU,eAAeqJ,GAAIC,EAAYO,mBAAkB,EAAkC,GAAGN,EAAkB,CAAC,EAAEC,EAAAA,CAAe,CAAhE,GAAGD,EAAAA,CAAmB,CAA4CO,eAAc,CAAC,CAACzH,EAAQ,GAAG3C,CAAK,CAAEG,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,YAC9Q,CACA,SAASgK,EAAgB,WACvBvK,CAAS,CACT,GAAGE,EACuB,EAC1B,GAAM,CACJ6J,mBAAiB,CAClB,CAAGZ,IACJ,MAAO,UAAC3E,IAAAA,CAAEhE,YAAU,mBAAmBqJ,GAAIE,EAAmB/J,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGE,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,YACtM,CACA,SAAS2H,EAAY,WACnBlI,CAAS,CACT,GAAGE,EACuB,EAC1B,GAAM,CACJ2C,OAAK,eACLmH,CAAa,CACd,CAAGb,IACEqB,EAAO3H,EAAQ4H,OAAO5H,GAAO0D,SAAW,IAAMrG,EAAMwK,QAAQ,QAClE,EAGO,EAHH,CAGG,CAHI,CAGJ,KAAClG,IAAAA,CAAEhE,YAAU,eAAeqJ,GAAIG,EAAehK,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2BAA4BJ,GAAa,GAAGE,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,oBAC9KiK,IAHI,IAKX,0BC3GA,oDCAA,kDCAA,gDCAA,uGCAA,sLCEA,SAASpD,EAAK,CACZpH,WAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAAC8D,MAAAA,CAAIxD,YAAU,OAAOR,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFJ,GAAa,GAAGE,CAAK,CAAEI,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAAS8G,EAAW,WAClBrH,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAAC8D,MAAAA,CAAIxD,YAAU,cAAcR,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JJ,GAAa,GAAGE,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAAS+G,EAAU,WACjBtH,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAAC8D,MAAAA,CAAIxD,YAAU,aAAaR,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BJ,GAAa,GAAGE,CAAK,CAAEI,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASoK,EAAgB,WACvB3K,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAAC8D,MAAAA,CAAIxD,YAAU,mBAAmBR,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGE,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,YACjL,CACA,SAASqK,EAAW,WAClB5K,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAAC8D,MAAAA,CAAIxD,YAAU,cAAcR,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iEAAkEJ,GAAa,GAAGE,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACxM,CACA,SAASgH,EAAY,WACnBvH,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAAC8D,MAAAA,CAAIxD,YAAU,eAAeR,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQJ,GAAa,GAAGE,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASsK,EAAW,WAClB7K,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAAC8D,MAAAA,CAAIxD,YAAU,cAAcR,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CJ,GAAa,GAAGE,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,kDCAA,iECAA,uDCAA,sDCAA,uDCAA,iDCAA,uCAAiK,CAEjK,sCAA+K,iFCKxK,IAAMuK,EAAQ,GACnB,IAAIC,QAAQ,GAAaC,WAAWC,EAASC,IAelCC,CAfuC,CAexB,CAC1BC,QAAS,EAAE,CAGXC,aACE,IAAMC,EAA4B,EAAE,CA4BpC,IAAK,IAAIrG,EAAI,EAAGA,GAAK,GAAIA,IAAK,SACbsG,IAAI,CAhBZ,CACL1B,EAAAA,CAb+BA,EAAU,EAczCrG,KAAMgI,EAAAA,CAAKA,CAACC,QAAQ,CAACC,WAAW,GAChChF,YAAa8E,EAAAA,CAAKA,CAACC,QAAQ,CAACE,kBAAkB,GAC9CC,WAAYJ,EAAAA,CAAKA,CAACK,IAAI,CACnBC,OAAO,CAAC,CAAEC,KAAM,aAAcC,GAAI,YAAa,GAC/CC,WAAW,GACdxF,MAAOyF,WAAWV,EAAAA,CAAKA,CAACC,QAAQ,CAAChF,KAAK,CAAC,CAAEH,IAAK,EAAG6F,IAAK,IAAKC,IAAK,CAAE,IAClEC,UAAW,CAAC,oDAAoD,EAAExC,EAAG,IAAI,CAAC,CAC1ErD,SAAUgF,EAAAA,CAAKA,CAACc,OAAO,CAACC,YAAY,CApBnB,CACjB,cACA,YACA,WACA,OACA,YACA,QACA,UACA,kBACD,EAYCC,WAAYhB,EAAAA,CAAKA,CAACK,IAAI,CAACY,MAAM,GAAGR,WAAW,EAC7C,EAK8ChH,CAGhD,IAAI,CAACmG,OAAO,CAAGE,CACjB,EAGA,MAAMoB,OAAO,YACXC,EAAa,EAAE,QACfC,CAAM,CAIP,EACC,IAAIC,EAAW,IAAI,IAAI,CAACzB,OAAO,CAAC,CAgBhC,OAbIuB,EAAWhK,MAAM,CAAG,GAAG,CACzBkK,EAAWA,EAAS9H,MAAM,CAAC,GACzB4H,EAAWtG,QAAQ,CAACyG,EAAQtG,QAAQ,IAKpCoG,IACFC,EAAWE,CAAAA,CADD,CACCA,EAAAA,EAAAA,CAAWA,CAACF,EAAUD,EAAQ,CACvCI,KAAM,CAAC,OAAQ,cAAe,WAAW,EAC3C,EAGKH,CACT,EAGA,MAAMI,YAAY,MAChBC,EAAO,CAAC,OACRC,EAAQ,EAAE,YACVR,CAAU,QACVC,CAAM,CAMP,EACC,MAAM9B,EAAM,KACZ,IAAMsC,EAAkBT,EAAaA,EAAWU,KAAK,CAAC,KAAO,EAAE,CACzDC,EAAc,MAAM,IAAI,CAACZ,MAAM,CAAC,CACpCC,WAAYS,SACZR,CACF,GACMW,EAAgBD,EAAY3K,MAAM,CAGlC6K,EAAS,CAACN,EAAO,GAAKC,EACtBM,EAAoBH,EAAYI,KAAK,CAACF,EAAQA,EAASL,GAM7D,MAAO,CACLvJ,SAAS,EACT+J,KALkB,CAKZC,GALgBC,OAAO5B,WAAW,GAMxC1F,QAAS,gDACTuH,eAAgBP,EAChBC,eACAL,EACAN,SAAUY,CACZ,CACF,EAGA,MAAMM,eAAelE,CAAU,EAC7B,MAAMiB,EAAM,KAGZ,EAHmB,EAGbgC,EAAU,IAAI,CAAC1B,OAAO,CAAC4C,EAHS,EAGL,CAAC,GAAalB,EAAQjD,EAAE,GAAKA,UAE9D,EAUO,CACLjG,CAXE,KAAU,EAWH,GACT+J,KAJkB,CAIZC,GAJgBC,OAAO5B,WAAW,GAKxC1F,QAAS,CAAC,gBAAgB,EAAEsD,EAAG,MAAM,CAAC,SACtCiD,CACF,EAdS,CACLlJ,SAAS,EACT2C,QAAS,CAAC,gBAAgB,EAAEsD,EAAG,UAAU,CAAC,CAahD,CACF,EAAE,EAGWwB,UAAU,2BC5JvB,oDCAA,uECAA,oDCAA,kECAA,yDCAA,uDCAA,uHGmBI,sBAAsB,8LFbX,eAAe4C,EAAgB,WAC5CC,CAAS,CACa,EACtB,IAAIpB,EAAU,KACVjG,EAAY,qBAShB,MARkB,OAAO,CAArBqH,IAEFpB,CACKA,EADKqB,CADG,MAEC,EAFKhD,CAAYA,CAAC4C,cAAc,CAACK,OAAOF,GAAAA,EACvCpB,OAAAA,GAEbuB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GAEVxH,EAAY,CAAC,YAAY,CAAC,EAErB,UAACF,EAAAA,OAAWA,CAAAA,CAACC,YAAakG,EAASjG,UAAWA,EAAWxG,sBAAoB,cAAcC,wBAAsB,kBAAkBC,0BAAwB,yBACpK,oBChBa+N,EAAW,CACtBC,KADsB,CACf,0BACT,EAMe,eAAeC,EAAKtO,CAAgB,EACjD,IAAMuO,EAAS,IAATA,EAAevO,EAAMuO,GAANvO,GAAY,CACjC,MAAOwO,CAAAA,EAAAA,EAAAA,GAAAA,CAACC,CAAAA,EAAAA,CAAAA,CAAAA,CAAcC,UAAU,IAACvO,qBAAoB,iBAAgBC,uBAAsB,QAAOC,yBAAwB,YACtH,SAAAmO,CAAAA,EAAAA,EAAAA,GAAAA,CAAC1K,CAAAA,KAAAA,CAAAA,CAAIhE,SAAU,oBACb,SAAA0O,CAAAA,EAAAA,EAAAA,GAAAA,CAACG,CAAAA,EAAAA,QAAAA,CAAAA,CAASC,QAAAA,CAAUJ,CAAAA,EAAAA,EAAAA,GAAAA,CAACK,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAqB1O,qBAAoB,YAAWE,yBAAwB,YAC/F,SAAAmO,CAAAA,EAAAA,EAAAA,GAAAA,CAACT,CAAAA,EAAAA,CAAgBC,SAAAA,CAAWO,EAA3BR,SAA2C,CAAE5N,qBAAoB,mBAAkBE,yBAAwB,mBAItH,CCbA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAPZyO,EAO8B,CAClD,CARiD,IAQ5C,CAAE,CADa,EACM,EAAS,CADa,GACT,CAAN,IAC3B,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,MAD4B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,gCAAgC,CAChD,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,EAOF,OAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,qDCAA,4DCAA,kDCAA,wDCAA,iECAA,uDCAA,qDCAA,0DCAA,iDCAA,0DCAA,4DCAA,iDCAA,yDCAA,yVCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,UACA,cACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,uCAAgJ,CAuBpK,+GAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QAzCA,IAAsB,uCAA4H,CAyClJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QA1DA,IAAsB,uCAAiH,CA0DvI,gFACA,gBA1DA,IAAsB,uCAAuH,CA0D7I,sFACA,aA1DA,IAAsB,sCAAoH,CA0D1I,mFACA,WA1DA,IAAsB,4CAAgF,CA0DtG,+CACA,cA1DA,IAAsB,4CAAmF,CA0DzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,kHAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,2CACA,0CAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,yBCrGD", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconUpload.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/file-selector@2.1.2/node_modules/file-selector/dist/es2015/file.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/file-selector@2.1.2/node_modules/file-selector/dist/es2015/file-selector.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/file-selector@2.1.2/node_modules/file-selector/dist/es2015/index.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/react-dropzone@14.3.8_react@19.0.0/node_modules/react-dropzone/dist/es/utils/index.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/react-dropzone@14.3.8_react@19.0.0/node_modules/react-dropzone/dist/es/index.js", "webpack://next-shadcn-dashboard-starter/../src/progress.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/progress.tsx", "webpack://next-shadcn-dashboard-starter/./src/hooks/use-controllable-state.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/file-uploader.tsx", "webpack://next-shadcn-dashboard-starter/./src/features/products/components/product-form.tsx", "webpack://next-shadcn-dashboard-starter/./src/hooks/use-callback-ref.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/attr-accept@2.2.5/node_modules/attr-accept/dist/es/index.js", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/?3afc", "webpack://next-shadcn-dashboard-starter/./src/components/ui/form.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/card.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/?2e97", "webpack://next-shadcn-dashboard-starter/./src/constants/mock-api.ts", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/./src/features/products/components/product-view-page.tsx", "webpack://next-shadcn-dashboard-starter/src/app/dashboard/product/[productId]/page.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/?6e8e", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'upload', 'IconUpload', [[\"path\",{\"d\":\"M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M7 9l5 -5l5 5\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M12 4l0 12\",\"key\":\"svg-2\"}]]);", "export const COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\nexport function toFileWithPath(file, path, h) {\n    const f = withMimeType(file);\n    const { webkitRelativePath } = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\nfunction withMimeType(file) {\n    const { name } = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop().toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\nfunction setObjProp(f, key, value) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    });\n}\n//# sourceMappingURL=file.js.map", "import { __awaiter } from \"tslib\";\nimport { toFileWithPath } from './file';\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport function fromEvent(evt) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n            return getDataTransferFiles(evt.dataTransfer, evt.type);\n        }\n        else if (isChangeEvt(evt)) {\n            return getInputFiles(evt);\n        }\n        else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n            return getFsHandleFiles(evt);\n        }\n        return [];\n    });\n}\nfunction isDataTransfer(value) {\n    return isObject(value);\n}\nfunction isChangeEvt(value) {\n    return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n    return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n    return fromList(evt.target.files).map(file => toFileWithPath(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const files = yield Promise.all(handles.map(h => h.getFile()));\n        return files.map(file => toFileWithPath(file));\n    });\n}\nfunction getDataTransferFiles(dt, type) {\n    return __awaiter(this, void 0, void 0, function* () {\n        // IE11 does not support dataTransfer.items\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n        if (dt.items) {\n            const items = fromList(dt.items)\n                .filter(item => item.kind === 'file');\n            // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n            // only 'dragstart' and 'drop' has access to the data (source node)\n            if (type !== 'drop') {\n                return items;\n            }\n            const files = yield Promise.all(items.map(toFilePromises));\n            return noIgnoredFiles(flatten(files));\n        }\n        return noIgnoredFiles(fromList(dt.files)\n            .map(file => toFileWithPath(file)));\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    if (items === null) {\n        return [];\n    }\n    const files = [];\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n    const entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\nfunction fromDataTransferItem(item, entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n        // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n        //\n        // See:\n        // - https://issues.chromium.org/issues/40186242\n        // - https://github.com/react-dropzone/react-dropzone/issues/1397\n        if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n            const h = yield item.getAsFileSystemHandle();\n            if (h === null) {\n                throw new Error(`${item} is not a File`);\n            }\n            // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n            // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n            if (h !== undefined) {\n                const file = yield h.getFile();\n                file.handle = h;\n                return toFileWithPath(file);\n            }\n        }\n        const file = item.getAsFile();\n        if (!file) {\n            throw new Error(`${item} is not a File`);\n        }\n        const fwp = toFileWithPath(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n        return fwp;\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    const reader = entry.createReader();\n    return new Promise((resolve, reject) => {\n        const entries = [];\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries((batch) => __awaiter(this, void 0, void 0, function* () {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = yield Promise.all(entries);\n                        resolve(files);\n                    }\n                    catch (err) {\n                        reject(err);\n                    }\n                }\n                else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n                    // Continue reading\n                    readEntries();\n                }\n            }), (err) => {\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            entry.file((file) => {\n                const fwp = toFileWithPath(file, entry.fullPath);\n                resolve(fwp);\n            }, (err) => {\n                reject(err);\n            });\n        });\n    });\n}\n//# sourceMappingURL=file-selector.js.map", "export { fromEvent } from './file-selector';\n//# sourceMappingURL=index.js.map", "function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport _accepts from \"attr-accept\";\nvar accepts = typeof _accepts === \"function\" ? _accepts : _accepts.default; // Error codes\n\nexport var FILE_INVALID_TYPE = \"file-invalid-type\";\nexport var FILE_TOO_LARGE = \"file-too-large\";\nexport var FILE_TOO_SMALL = \"file-too-small\";\nexport var TOO_MANY_FILES = \"too-many-files\";\nexport var ErrorCode = {\n  FileInvalidType: FILE_INVALID_TYPE,\n  FileTooLarge: FILE_TOO_LARGE,\n  FileTooSmall: FILE_TOO_SMALL,\n  TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */\n\nexport var getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n  var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var acceptArr = accept.split(\",\");\n  var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n  return {\n    code: FILE_INVALID_TYPE,\n    message: \"File type must be \".concat(msg)\n  };\n};\nexport var getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n  return {\n    code: FILE_TOO_LARGE,\n    message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n  return {\n    code: FILE_TOO_SMALL,\n    message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var TOO_MANY_FILES_REJECTION = {\n  code: TOO_MANY_FILES,\n  message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */\n\nexport function fileAccepted(file, accept) {\n  var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n  return [isAcceptable, isAcceptable ? null : getInvalidTypeRejectionErr(accept)];\n}\nexport function fileMatchSize(file, minSize, maxSize) {\n  if (isDefined(file.size)) {\n    if (isDefined(minSize) && isDefined(maxSize)) {\n      if (file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n      if (file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];\n    } else if (isDefined(minSize) && file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];else if (isDefined(maxSize) && file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n  }\n\n  return [true, null];\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */\n\n\nexport function allFilesAccepted(_ref) {\n  var files = _ref.files,\n      accept = _ref.accept,\n      minSize = _ref.minSize,\n      maxSize = _ref.maxSize,\n      multiple = _ref.multiple,\n      maxFiles = _ref.maxFiles,\n      validator = _ref.validator;\n\n  if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n    return false;\n  }\n\n  return files.every(function (file) {\n    var _fileAccepted = fileAccepted(file, accept),\n        _fileAccepted2 = _slicedToArray(_fileAccepted, 1),\n        accepted = _fileAccepted2[0];\n\n    var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n        _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1),\n        sizeMatch = _fileMatchSize2[0];\n\n    var customErrors = validator ? validator(file) : null;\n    return accepted && sizeMatch && !customErrors;\n  });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\n\nexport function isPropagationStopped(event) {\n  if (typeof event.isPropagationStopped === \"function\") {\n    return event.isPropagationStopped();\n  } else if (typeof event.cancelBubble !== \"undefined\") {\n    return event.cancelBubble;\n  }\n\n  return false;\n}\nexport function isEvtWithFiles(event) {\n  if (!event.dataTransfer) {\n    return !!event.target && !!event.target.files;\n  } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n\n\n  return Array.prototype.some.call(event.dataTransfer.types, function (type) {\n    return type === \"Files\" || type === \"application/x-moz-file\";\n  });\n}\nexport function isKindFile(item) {\n  return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\n\nexport function onDocumentDragOver(event) {\n  event.preventDefault();\n}\n\nfunction isIe(userAgent) {\n  return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\n\nfunction isEdge(userAgent) {\n  return userAgent.indexOf(\"Edge/\") !== -1;\n}\n\nexport function isIeOrEdge() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n  return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */\n\nexport function composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    return fns.some(function (fn) {\n      if (!isPropagationStopped(event) && fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n\n      return isPropagationStopped(event);\n    });\n  };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */\n\nexport function canUseFileSystemAccessAPI() {\n  return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */\n\nexport function pickerOptionsFromAccept(accept) {\n  if (isDefined(accept)) {\n    var acceptForPicker = Object.entries(accept).filter(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n          mimeType = _ref3[0],\n          ext = _ref3[1];\n\n      var ok = true;\n\n      if (!isMIMEType(mimeType)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.\"));\n        ok = false;\n      }\n\n      if (!Array.isArray(ext) || !ext.every(isExt)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because an invalid file extension was provided.\"));\n        ok = false;\n      }\n\n      return ok;\n    }).reduce(function (agg, _ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n          mimeType = _ref5[0],\n          ext = _ref5[1];\n\n      return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n    }, {});\n    return [{\n      // description is required due to https://crbug.com/1264708\n      description: \"Files\",\n      accept: acceptForPicker\n    }];\n  }\n\n  return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */\n\nexport function acceptPropAsAcceptAttr(accept) {\n  if (isDefined(accept)) {\n    return Object.entries(accept).reduce(function (a, _ref6) {\n      var _ref7 = _slicedToArray(_ref6, 2),\n          mimeType = _ref7[0],\n          ext = _ref7[1];\n\n      return [].concat(_toConsumableArray(a), [mimeType], _toConsumableArray(ext));\n    }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n    .filter(function (v) {\n      return isMIMEType(v) || isExt(v);\n    }).join(\",\");\n  }\n\n  return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */\n\nexport function isAbort(v) {\n  return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */\n\nexport function isSecurityError(v) {\n  return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */\n\nexport function isMIMEType(v) {\n  return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */\n\nexport function isExt(v) {\n  return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * @typedef {Object.<string, string[]>} AcceptProp\n */\n\n/**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */\n\n/**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n */", "var _excluded = [\"children\"],\n    _excluded2 = [\"open\"],\n    _excluded3 = [\"refKey\", \"role\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onClick\", \"onDragEnter\", \"onDragOver\", \"onDragLeave\", \"onDrop\"],\n    _excluded4 = [\"refKey\", \"onChange\", \"onClick\"];\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n/* eslint prefer-template: 0 */\nimport React, { forwardRef, Fragment, useCallback, useEffect, useImperativeHandle, useMemo, useReducer, useRef } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { fromEvent } from \"file-selector\";\nimport { acceptPropAsAcceptAttr, allFilesAccepted, composeEventHandlers, fileAccepted, fileMatchSize, canUseFileSystemAccessAPI, isAbort, isEvtWithFiles, isIeOrEdge, isPropagationStopped, isSecurityError, onDocumentDragOver, pickerOptionsFromAccept, TOO_MANY_FILES_REJECTION } from \"./utils/index.js\";\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */\n\nvar Dropzone = /*#__PURE__*/forwardRef(function (_ref, ref) {\n  var children = _ref.children,\n      params = _objectWithoutProperties(_ref, _excluded);\n\n  var _useDropzone = useDropzone(params),\n      open = _useDropzone.open,\n      props = _objectWithoutProperties(_useDropzone, _excluded2);\n\n  useImperativeHandle(ref, function () {\n    return {\n      open: open\n    };\n  }, [open]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n\n  return /*#__PURE__*/React.createElement(Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n    open: open\n  })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\n\nvar defaultProps = {\n  disabled: false,\n  getFilesFromEvent: fromEvent,\n  maxSize: Infinity,\n  minSize: 0,\n  multiple: true,\n  maxFiles: 0,\n  preventDropOnDocument: true,\n  noClick: false,\n  noKeyboard: false,\n  noDrag: false,\n  noDragEventsBubbling: false,\n  validator: null,\n  useFsAccessApi: false,\n  autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n  /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */\n  children: PropTypes.func,\n\n  /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */\n  accept: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),\n\n  /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */\n  multiple: PropTypes.bool,\n\n  /**\n   * If false, allow dropped items to take over the current browser window\n   */\n  preventDropOnDocument: PropTypes.bool,\n\n  /**\n   * If true, disables click to open the native file selection dialog\n   */\n  noClick: PropTypes.bool,\n\n  /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */\n  noKeyboard: PropTypes.bool,\n\n  /**\n   * If true, disables drag 'n' drop\n   */\n  noDrag: PropTypes.bool,\n\n  /**\n   * If true, stops drag event propagation to parents\n   */\n  noDragEventsBubbling: PropTypes.bool,\n\n  /**\n   * Minimum file size (in bytes)\n   */\n  minSize: PropTypes.number,\n\n  /**\n   * Maximum file size (in bytes)\n   */\n  maxSize: PropTypes.number,\n\n  /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */\n  maxFiles: PropTypes.number,\n\n  /**\n   * Enable/disable the dropzone\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  getFilesFromEvent: PropTypes.func,\n\n  /**\n   * Cb for when closing the file dialog with no selection\n   */\n  onFileDialogCancel: PropTypes.func,\n\n  /**\n   * Cb for when opening the file dialog\n   */\n  onFileDialogOpen: PropTypes.func,\n\n  /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */\n  useFsAccessApi: PropTypes.bool,\n\n  /**\n   * Set to true to focus the root element on render\n   */\n  autoFocus: PropTypes.bool,\n\n  /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */\n  onDragEnter: PropTypes.func,\n\n  /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragLeave: PropTypes.func,\n\n  /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragOver: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  onDrop: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */\n  onDropAccepted: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */\n  onDropRejected: PropTypes.func,\n\n  /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */\n  onError: PropTypes.func,\n\n  /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */\n  validator: PropTypes.func\n};\nexport default Dropzone;\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */\n\n/**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */\n\n/**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */\n\nvar initialState = {\n  isFocused: false,\n  isFileDialogActive: false,\n  isDragActive: false,\n  isDragAccept: false,\n  isDragReject: false,\n  acceptedFiles: [],\n  fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */\n\nexport function useDropzone() {\n  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n      accept = _defaultProps$props.accept,\n      disabled = _defaultProps$props.disabled,\n      getFilesFromEvent = _defaultProps$props.getFilesFromEvent,\n      maxSize = _defaultProps$props.maxSize,\n      minSize = _defaultProps$props.minSize,\n      multiple = _defaultProps$props.multiple,\n      maxFiles = _defaultProps$props.maxFiles,\n      onDragEnter = _defaultProps$props.onDragEnter,\n      onDragLeave = _defaultProps$props.onDragLeave,\n      onDragOver = _defaultProps$props.onDragOver,\n      onDrop = _defaultProps$props.onDrop,\n      onDropAccepted = _defaultProps$props.onDropAccepted,\n      onDropRejected = _defaultProps$props.onDropRejected,\n      onFileDialogCancel = _defaultProps$props.onFileDialogCancel,\n      onFileDialogOpen = _defaultProps$props.onFileDialogOpen,\n      useFsAccessApi = _defaultProps$props.useFsAccessApi,\n      autoFocus = _defaultProps$props.autoFocus,\n      preventDropOnDocument = _defaultProps$props.preventDropOnDocument,\n      noClick = _defaultProps$props.noClick,\n      noKeyboard = _defaultProps$props.noKeyboard,\n      noDrag = _defaultProps$props.noDrag,\n      noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling,\n      onError = _defaultProps$props.onError,\n      validator = _defaultProps$props.validator;\n\n  var acceptAttr = useMemo(function () {\n    return acceptPropAsAcceptAttr(accept);\n  }, [accept]);\n  var pickerTypes = useMemo(function () {\n    return pickerOptionsFromAccept(accept);\n  }, [accept]);\n  var onFileDialogOpenCb = useMemo(function () {\n    return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n  }, [onFileDialogOpen]);\n  var onFileDialogCancelCb = useMemo(function () {\n    return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n  }, [onFileDialogCancel]);\n  /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */\n\n  var rootRef = useRef(null);\n  var inputRef = useRef(null);\n\n  var _useReducer = useReducer(reducer, initialState),\n      _useReducer2 = _slicedToArray(_useReducer, 2),\n      state = _useReducer2[0],\n      dispatch = _useReducer2[1];\n\n  var isFocused = state.isFocused,\n      isFileDialogActive = state.isFileDialogActive;\n  var fsAccessApiWorksRef = useRef(typeof window !== \"undefined\" && window.isSecureContext && useFsAccessApi && canUseFileSystemAccessAPI()); // Update file dialog active state when the window is focused on\n\n  var onWindowFocus = function onWindowFocus() {\n    // Execute the timeout only if the file dialog is opened in the browser\n    if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n      setTimeout(function () {\n        if (inputRef.current) {\n          var files = inputRef.current.files;\n\n          if (!files.length) {\n            dispatch({\n              type: \"closeDialog\"\n            });\n            onFileDialogCancelCb();\n          }\n        }\n      }, 300);\n    }\n  };\n\n  useEffect(function () {\n    window.addEventListener(\"focus\", onWindowFocus, false);\n    return function () {\n      window.removeEventListener(\"focus\", onWindowFocus, false);\n    };\n  }, [inputRef, isFileDialogActive, onFileDialogCancelCb, fsAccessApiWorksRef]);\n  var dragTargetsRef = useRef([]);\n\n  var onDocumentDrop = function onDocumentDrop(event) {\n    if (rootRef.current && rootRef.current.contains(event.target)) {\n      // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n      return;\n    }\n\n    event.preventDefault();\n    dragTargetsRef.current = [];\n  };\n\n  useEffect(function () {\n    if (preventDropOnDocument) {\n      document.addEventListener(\"dragover\", onDocumentDragOver, false);\n      document.addEventListener(\"drop\", onDocumentDrop, false);\n    }\n\n    return function () {\n      if (preventDropOnDocument) {\n        document.removeEventListener(\"dragover\", onDocumentDragOver);\n        document.removeEventListener(\"drop\", onDocumentDrop);\n      }\n    };\n  }, [rootRef, preventDropOnDocument]); // Auto focus the root when autoFocus is true\n\n  useEffect(function () {\n    if (!disabled && autoFocus && rootRef.current) {\n      rootRef.current.focus();\n    }\n\n    return function () {};\n  }, [rootRef, autoFocus, disabled]);\n  var onErrCb = useCallback(function (e) {\n    if (onError) {\n      onError(e);\n    } else {\n      // Let the user know something's gone wrong if they haven't provided the onError cb.\n      console.error(e);\n    }\n  }, [onError]);\n  var onDragEnterCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [event.target]);\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        var fileCount = files.length;\n        var isDragAccept = fileCount > 0 && allFilesAccepted({\n          files: files,\n          accept: acceptAttr,\n          minSize: minSize,\n          maxSize: maxSize,\n          multiple: multiple,\n          maxFiles: maxFiles,\n          validator: validator\n        });\n        var isDragReject = fileCount > 0 && !isDragAccept;\n        dispatch({\n          isDragAccept: isDragAccept,\n          isDragReject: isDragReject,\n          isDragActive: true,\n          type: \"setDraggedFiles\"\n        });\n\n        if (onDragEnter) {\n          onDragEnter(event);\n        }\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n  }, [getFilesFromEvent, onDragEnter, onErrCb, noDragEventsBubbling, acceptAttr, minSize, maxSize, multiple, maxFiles, validator]);\n  var onDragOverCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event);\n    var hasFiles = isEvtWithFiles(event);\n\n    if (hasFiles && event.dataTransfer) {\n      try {\n        event.dataTransfer.dropEffect = \"copy\";\n      } catch (_unused) {}\n      /* eslint-disable-line no-empty */\n\n    }\n\n    if (hasFiles && onDragOver) {\n      onDragOver(event);\n    }\n\n    return false;\n  }, [onDragOver, noDragEventsBubbling]);\n  var onDragLeaveCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n\n    var targets = dragTargetsRef.current.filter(function (target) {\n      return rootRef.current && rootRef.current.contains(target);\n    }); // Make sure to remove a target present multiple times only once\n    // (Firefox may fire dragenter/dragleave multiple times on the same element)\n\n    var targetIdx = targets.indexOf(event.target);\n\n    if (targetIdx !== -1) {\n      targets.splice(targetIdx, 1);\n    }\n\n    dragTargetsRef.current = targets;\n\n    if (targets.length > 0) {\n      return;\n    }\n\n    dispatch({\n      type: \"setDraggedFiles\",\n      isDragActive: false,\n      isDragAccept: false,\n      isDragReject: false\n    });\n\n    if (isEvtWithFiles(event) && onDragLeave) {\n      onDragLeave(event);\n    }\n  }, [rootRef, onDragLeave, noDragEventsBubbling]);\n  var setFiles = useCallback(function (files, event) {\n    var acceptedFiles = [];\n    var fileRejections = [];\n    files.forEach(function (file) {\n      var _fileAccepted = fileAccepted(file, acceptAttr),\n          _fileAccepted2 = _slicedToArray(_fileAccepted, 2),\n          accepted = _fileAccepted2[0],\n          acceptError = _fileAccepted2[1];\n\n      var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n          _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2),\n          sizeMatch = _fileMatchSize2[0],\n          sizeError = _fileMatchSize2[1];\n\n      var customErrors = validator ? validator(file) : null;\n\n      if (accepted && sizeMatch && !customErrors) {\n        acceptedFiles.push(file);\n      } else {\n        var errors = [acceptError, sizeError];\n\n        if (customErrors) {\n          errors = errors.concat(customErrors);\n        }\n\n        fileRejections.push({\n          file: file,\n          errors: errors.filter(function (e) {\n            return e;\n          })\n        });\n      }\n    });\n\n    if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n      // Reject everything and empty accepted files\n      acceptedFiles.forEach(function (file) {\n        fileRejections.push({\n          file: file,\n          errors: [TOO_MANY_FILES_REJECTION]\n        });\n      });\n      acceptedFiles.splice(0);\n    }\n\n    dispatch({\n      acceptedFiles: acceptedFiles,\n      fileRejections: fileRejections,\n      isDragReject: fileRejections.length > 0,\n      type: \"setFiles\"\n    });\n\n    if (onDrop) {\n      onDrop(acceptedFiles, fileRejections, event);\n    }\n\n    if (fileRejections.length > 0 && onDropRejected) {\n      onDropRejected(fileRejections, event);\n    }\n\n    if (acceptedFiles.length > 0 && onDropAccepted) {\n      onDropAccepted(acceptedFiles, event);\n    }\n  }, [dispatch, multiple, acceptAttr, minSize, maxSize, maxFiles, onDrop, onDropAccepted, onDropRejected, validator]);\n  var onDropCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [];\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        setFiles(files, event);\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n\n    dispatch({\n      type: \"reset\"\n    });\n  }, [getFilesFromEvent, setFiles, onErrCb, noDragEventsBubbling]); // Fn for opening the file dialog programmatically\n\n  var openFileDialog = useCallback(function () {\n    // No point to use FS access APIs if context is not secure\n    // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n    if (fsAccessApiWorksRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n\n      var opts = {\n        multiple: multiple,\n        types: pickerTypes\n      };\n      window.showOpenFilePicker(opts).then(function (handles) {\n        return getFilesFromEvent(handles);\n      }).then(function (files) {\n        setFiles(files, null);\n        dispatch({\n          type: \"closeDialog\"\n        });\n      }).catch(function (e) {\n        // AbortError means the user canceled\n        if (isAbort(e)) {\n          onFileDialogCancelCb(e);\n          dispatch({\n            type: \"closeDialog\"\n          });\n        } else if (isSecurityError(e)) {\n          fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n          // Try using the input\n\n          if (inputRef.current) {\n            inputRef.current.value = null;\n            inputRef.current.click();\n          } else {\n            onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n          }\n        } else {\n          onErrCb(e);\n        }\n      });\n      return;\n    }\n\n    if (inputRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb();\n      inputRef.current.value = null;\n      inputRef.current.click();\n    }\n  }, [dispatch, onFileDialogOpenCb, onFileDialogCancelCb, useFsAccessApi, setFiles, onErrCb, pickerTypes, multiple]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n\n  var onKeyDownCb = useCallback(function (event) {\n    // Ignore keyboard events bubbling up the DOM tree\n    if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n      return;\n    }\n\n    if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n      event.preventDefault();\n      openFileDialog();\n    }\n  }, [rootRef, openFileDialog]); // Update focus state for the dropzone\n\n  var onFocusCb = useCallback(function () {\n    dispatch({\n      type: \"focus\"\n    });\n  }, []);\n  var onBlurCb = useCallback(function () {\n    dispatch({\n      type: \"blur\"\n    });\n  }, []); // Cb to open the file dialog when click occurs on the dropzone\n\n  var onClickCb = useCallback(function () {\n    if (noClick) {\n      return;\n    } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n    // to ensure React can handle state changes\n    // See: https://github.com/react-dropzone/react-dropzone/issues/450\n\n\n    if (isIeOrEdge()) {\n      setTimeout(openFileDialog, 0);\n    } else {\n      openFileDialog();\n    }\n  }, [noClick, openFileDialog]);\n\n  var composeHandler = function composeHandler(fn) {\n    return disabled ? null : fn;\n  };\n\n  var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n    return noKeyboard ? null : composeHandler(fn);\n  };\n\n  var composeDragHandler = function composeDragHandler(fn) {\n    return noDrag ? null : composeHandler(fn);\n  };\n\n  var stopPropagation = function stopPropagation(event) {\n    if (noDragEventsBubbling) {\n      event.stopPropagation();\n    }\n  };\n\n  var getRootProps = useMemo(function () {\n    return function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref2$refKey = _ref2.refKey,\n          refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey,\n          role = _ref2.role,\n          onKeyDown = _ref2.onKeyDown,\n          onFocus = _ref2.onFocus,\n          onBlur = _ref2.onBlur,\n          onClick = _ref2.onClick,\n          onDragEnter = _ref2.onDragEnter,\n          onDragOver = _ref2.onDragOver,\n          onDragLeave = _ref2.onDragLeave,\n          onDrop = _ref2.onDrop,\n          rest = _objectWithoutProperties(_ref2, _excluded3);\n\n      return _objectSpread(_objectSpread(_defineProperty({\n        onKeyDown: composeKeyboardHandler(composeEventHandlers(onKeyDown, onKeyDownCb)),\n        onFocus: composeKeyboardHandler(composeEventHandlers(onFocus, onFocusCb)),\n        onBlur: composeKeyboardHandler(composeEventHandlers(onBlur, onBlurCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onClickCb)),\n        onDragEnter: composeDragHandler(composeEventHandlers(onDragEnter, onDragEnterCb)),\n        onDragOver: composeDragHandler(composeEventHandlers(onDragOver, onDragOverCb)),\n        onDragLeave: composeDragHandler(composeEventHandlers(onDragLeave, onDragLeaveCb)),\n        onDrop: composeDragHandler(composeEventHandlers(onDrop, onDropCb)),\n        role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n      }, refKey, rootRef), !disabled && !noKeyboard ? {\n        tabIndex: 0\n      } : {}), rest);\n    };\n  }, [rootRef, onKeyDownCb, onFocusCb, onBlurCb, onClickCb, onDragEnterCb, onDragOverCb, onDragLeaveCb, onDropCb, noKeyboard, noDrag, disabled]);\n  var onInputElementClick = useCallback(function (event) {\n    event.stopPropagation();\n  }, []);\n  var getInputProps = useMemo(function () {\n    return function () {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref3$refKey = _ref3.refKey,\n          refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey,\n          onChange = _ref3.onChange,\n          onClick = _ref3.onClick,\n          rest = _objectWithoutProperties(_ref3, _excluded4);\n\n      var inputProps = _defineProperty({\n        accept: acceptAttr,\n        multiple: multiple,\n        type: \"file\",\n        style: {\n          border: 0,\n          clip: \"rect(0, 0, 0, 0)\",\n          clipPath: \"inset(50%)\",\n          height: \"1px\",\n          margin: \"0 -1px -1px 0\",\n          overflow: \"hidden\",\n          padding: 0,\n          position: \"absolute\",\n          width: \"1px\",\n          whiteSpace: \"nowrap\"\n        },\n        onChange: composeHandler(composeEventHandlers(onChange, onDropCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onInputElementClick)),\n        tabIndex: -1\n      }, refKey, inputRef);\n\n      return _objectSpread(_objectSpread({}, inputProps), rest);\n    };\n  }, [inputRef, accept, multiple, onDropCb, disabled]);\n  return _objectSpread(_objectSpread({}, state), {}, {\n    isFocused: isFocused && !disabled,\n    getRootProps: getRootProps,\n    getInputProps: getInputProps,\n    rootRef: rootRef,\n    inputRef: inputRef,\n    open: composeHandler(openFileDialog)\n  });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */\n\nfunction reducer(state, action) {\n  /* istanbul ignore next */\n  switch (action.type) {\n    case \"focus\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: true\n      });\n\n    case \"blur\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: false\n      });\n\n    case \"openDialog\":\n      return _objectSpread(_objectSpread({}, initialState), {}, {\n        isFileDialogActive: true\n      });\n\n    case \"closeDialog\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFileDialogActive: false\n      });\n\n    case \"setDraggedFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isDragActive: action.isDragActive,\n        isDragAccept: action.isDragAccept,\n        isDragReject: action.isDragReject\n      });\n\n    case \"setFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        acceptedFiles: action.acceptedFiles,\n        fileRejections: action.fileRejections,\n        isDragReject: action.isDragReject\n      });\n\n    case \"reset\":\n      return _objectSpread({}, initialState);\n\n    default:\n      return state;\n  }\n}\n\nfunction noop() {}\n\nexport { ErrorCode } from \"./utils/index.js\";", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ElementRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n", "'use client';\n\nimport * as React from 'react';\nimport * as ProgressPrimitive from '@radix-ui/react-progress';\nimport { cn } from '@/lib/utils';\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return <ProgressPrimitive.Root data-slot='progress' className={cn('bg-primary/20 relative h-2 w-full overflow-hidden rounded-full', className)} {...props} data-sentry-element=\"ProgressPrimitive.Root\" data-sentry-component=\"Progress\" data-sentry-source-file=\"progress.tsx\">\r\n      <ProgressPrimitive.Indicator data-slot='progress-indicator' className='bg-primary h-full w-full flex-1 transition-all' style={{\n      transform: `translateX(-${100 - (value || 0)}%)`\n    }} data-sentry-element=\"ProgressPrimitive.Indicator\" data-sentry-source-file=\"progress.tsx\" />\r\n    </ProgressPrimitive.Root>;\n}\nexport { Progress };", "import * as React from 'react';\nimport { useCallbackRef } from '@/hooks/use-callback-ref';\n\n/**\r\n * @see https://github.com/radix-ui/primitives/blob/main/packages/react/use-controllable-state/src/useControllableState.tsx\r\n */\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\ntype SetStateFn<T> = (prevState?: T) => T;\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {}\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(nextValue => {\n    if (isControlled) {\n      const setter = nextValue as SetStateFn<T>;\n      const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n      if (value !== prop) handleChange(value as T);\n    } else {\n      setUncontrolledProp(nextValue);\n    }\n  }, [isControlled, prop, setUncontrolledProp, handleChange]);\n  return [value, setValue] as const;\n}\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\nexport { useControllableState };", "'use client';\n\nimport { IconX, IconUpload } from '@tabler/icons-react';\nimport Image from 'next/image';\nimport * as React from 'react';\nimport Dropzone, { type DropzoneProps, type FileRejection } from 'react-dropzone';\nimport { toast } from 'sonner';\nimport { Button } from '@/components/ui/button';\nimport { Progress } from '@/components/ui/progress';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { useControllableState } from '@/hooks/use-controllable-state';\nimport { cn, formatBytes } from '@/lib/utils';\ninterface FileUploaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  /**\r\n   * Value of the uploader.\r\n   * @type File[]\r\n   * @default undefined\r\n   * @example value={files}\r\n   */\n  value?: File[];\n\n  /**\r\n   * Function to be called when the value changes.\r\n   * @type React.Dispatch<React.SetStateAction<File[]>>\r\n   * @default undefined\r\n   * @example onValueChange={(files) => setFiles(files)}\r\n   */\n  onValueChange?: React.Dispatch<React.SetStateAction<File[]>>;\n\n  /**\r\n   * Function to be called when files are uploaded.\r\n   * @type (files: File[]) => Promise<void>\r\n   * @default undefined\r\n   * @example onUpload={(files) => uploadFiles(files)}\r\n   */\n  onUpload?: (files: File[]) => Promise<void>;\n\n  /**\r\n   * Progress of the uploaded files.\r\n   * @type Record<string, number> | undefined\r\n   * @default undefined\r\n   * @example progresses={{ \"file1.png\": 50 }}\r\n   */\n  progresses?: Record<string, number>;\n\n  /**\r\n   * Accepted file types for the uploader.\r\n   * @type { [key: string]: string[]}\r\n   * @default\r\n   * ```ts\r\n   * { \"image/*\": [] }\r\n   * ```\r\n   * @example accept={[\"image/png\", \"image/jpeg\"]}\r\n   */\n  accept?: DropzoneProps['accept'];\n\n  /**\r\n   * Maximum file size for the uploader.\r\n   * @type number | undefined\r\n   * @default 1024 * 1024 * 2 // 2MB\r\n   * @example maxSize={1024 * 1024 * 2} // 2MB\r\n   */\n  maxSize?: DropzoneProps['maxSize'];\n\n  /**\r\n   * Maximum number of files for the uploader.\r\n   * @type number | undefined\r\n   * @default 1\r\n   * @example maxFiles={5}\r\n   */\n  maxFiles?: DropzoneProps['maxFiles'];\n\n  /**\r\n   * Whether the uploader should accept multiple files.\r\n   * @type boolean\r\n   * @default false\r\n   * @example multiple\r\n   */\n  multiple?: boolean;\n\n  /**\r\n   * Whether the uploader is disabled.\r\n   * @type boolean\r\n   * @default false\r\n   * @example disabled\r\n   */\n  disabled?: boolean;\n}\nexport function FileUploader(props: FileUploaderProps) {\n  const {\n    value: valueProp,\n    onValueChange,\n    onUpload,\n    progresses,\n    accept = {\n      'image/*': []\n    },\n    maxSize = 1024 * 1024 * 2,\n    maxFiles = 1,\n    multiple = false,\n    disabled = false,\n    className,\n    ...dropzoneProps\n  } = props;\n  const [files, setFiles] = useControllableState({\n    prop: valueProp,\n    onChange: onValueChange\n  });\n  const onDrop = React.useCallback((acceptedFiles: File[], rejectedFiles: FileRejection[]) => {\n    if (!multiple && maxFiles === 1 && acceptedFiles.length > 1) {\n      toast.error('Cannot upload more than 1 file at a time');\n      return;\n    }\n    if ((files?.length ?? 0) + acceptedFiles.length > maxFiles) {\n      toast.error(`Cannot upload more than ${maxFiles} files`);\n      return;\n    }\n    const newFiles = acceptedFiles.map(file => Object.assign(file, {\n      preview: URL.createObjectURL(file)\n    }));\n    const updatedFiles = files ? [...files, ...newFiles] : newFiles;\n    setFiles(updatedFiles);\n    if (rejectedFiles.length > 0) {\n      rejectedFiles.forEach(({\n        file\n      }) => {\n        toast.error(`File ${file.name} was rejected`);\n      });\n    }\n    if (onUpload && updatedFiles.length > 0 && updatedFiles.length <= maxFiles) {\n      const target = updatedFiles.length > 0 ? `${updatedFiles.length} files` : `file`;\n      toast.promise(onUpload(updatedFiles), {\n        loading: `Uploading ${target}...`,\n        success: () => {\n          setFiles([]);\n          return `${target} uploaded`;\n        },\n        error: `Failed to upload ${target}`\n      });\n    }\n  }, [files, maxFiles, multiple, onUpload, setFiles]);\n  function onRemove(index: number) {\n    if (!files) return;\n    const newFiles = files.filter((_, i) => i !== index);\n    setFiles(newFiles);\n    onValueChange?.(newFiles);\n  }\n\n  // Revoke preview url when component unmounts\n  React.useEffect(() => {\n    return () => {\n      if (!files) return;\n      files.forEach(file => {\n        if (isFileWithPreview(file)) {\n          URL.revokeObjectURL(file.preview);\n        }\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const isDisabled = disabled || (files?.length ?? 0) >= maxFiles;\n  return <div className='relative flex flex-col gap-6 overflow-hidden' data-sentry-component=\"FileUploader\" data-sentry-source-file=\"file-uploader.tsx\">\r\n      <Dropzone onDrop={onDrop} accept={accept} maxSize={maxSize} maxFiles={maxFiles} multiple={maxFiles > 1 || multiple} disabled={isDisabled} data-sentry-element=\"Dropzone\" data-sentry-source-file=\"file-uploader.tsx\">\r\n        {({\n        getRootProps,\n        getInputProps,\n        isDragActive\n      }) => <div {...getRootProps()} className={cn('group border-muted-foreground/25 hover:bg-muted/25 relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-dashed px-5 py-2.5 text-center transition', 'ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden', isDragActive && 'border-muted-foreground/50', isDisabled && 'pointer-events-none opacity-60', className)} {...dropzoneProps}>\r\n            <input {...getInputProps()} />\r\n            {isDragActive ? <div className='flex flex-col items-center justify-center gap-4 sm:px-5'>\r\n                <div className='rounded-full border border-dashed p-3'>\r\n                  <IconUpload className='text-muted-foreground size-7' aria-hidden='true' />\r\n                </div>\r\n                <p className='text-muted-foreground font-medium'>\r\n                  Drop the files here\r\n                </p>\r\n              </div> : <div className='flex flex-col items-center justify-center gap-4 sm:px-5'>\r\n                <div className='rounded-full border border-dashed p-3'>\r\n                  <IconUpload className='text-muted-foreground size-7' aria-hidden='true' />\r\n                </div>\r\n                <div className='space-y-px'>\r\n                  <p className='text-muted-foreground font-medium'>\r\n                    Drag {`'n'`} drop files here, or click to select files\r\n                  </p>\r\n                  <p className='text-muted-foreground/70 text-sm'>\r\n                    You can upload\r\n                    {maxFiles > 1 ? ` ${maxFiles === Infinity ? 'multiple' : maxFiles}\n                      files (up to ${formatBytes(maxSize)} each)` : ` a file with ${formatBytes(maxSize)}`}\r\n                  </p>\r\n                </div>\r\n              </div>}\r\n          </div>}\r\n      </Dropzone>\r\n      {files?.length ? <ScrollArea className='h-fit w-full px-3'>\r\n          <div className='max-h-48 space-y-4'>\r\n            {files?.map((file, index) => <FileCard key={index} file={file} onRemove={() => onRemove(index)} progress={progresses?.[file.name]} />)}\r\n          </div>\r\n        </ScrollArea> : null}\r\n    </div>;\n}\ninterface FileCardProps {\n  file: File;\n  onRemove: () => void;\n  progress?: number;\n}\nfunction FileCard({\n  file,\n  progress,\n  onRemove\n}: FileCardProps) {\n  return <div className='relative flex items-center space-x-4' data-sentry-component=\"FileCard\" data-sentry-source-file=\"file-uploader.tsx\">\r\n      <div className='flex flex-1 space-x-4'>\r\n        {isFileWithPreview(file) ? <Image src={file.preview} alt={file.name} width={48} height={48} loading='lazy' className='aspect-square shrink-0 rounded-md object-cover' /> : null}\r\n        <div className='flex w-full flex-col gap-2'>\r\n          <div className='space-y-px'>\r\n            <p className='text-foreground/80 line-clamp-1 text-sm font-medium'>\r\n              {file.name}\r\n            </p>\r\n            <p className='text-muted-foreground text-xs'>\r\n              {formatBytes(file.size)}\r\n            </p>\r\n          </div>\r\n          {progress ? <Progress value={progress} /> : null}\r\n        </div>\r\n      </div>\r\n      <div className='flex items-center gap-2'>\r\n        <Button type='button' variant='ghost' size='icon' onClick={onRemove} disabled={progress !== undefined && progress < 100} className='size-8 rounded-full' data-sentry-element=\"Button\" data-sentry-source-file=\"file-uploader.tsx\">\r\n          <IconX className='text-muted-foreground' data-sentry-element=\"IconX\" data-sentry-source-file=\"file-uploader.tsx\" />\r\n          <span className='sr-only'>Remove file</span>\r\n        </Button>\r\n      </div>\r\n    </div>;\n}\nfunction isFileWithPreview(file: File): file is File & {\n  preview: string;\n} {\n  return 'preview' in file && typeof file.preview === 'string';\n}", "'use client';\n\nimport { FileUploader } from '@/components/file-uploader';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Product } from '@/constants/mock-api';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { useForm } from 'react-hook-form';\nimport * as z from 'zod';\nconst MAX_FILE_SIZE = 5000000;\nconst ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\nconst formSchema = z.object({\n  image: z.any().refine(files => files?.length == 1, 'Image is required.').refine(files => files?.[0]?.size <= MAX_FILE_SIZE, `Max file size is 5MB.`).refine(files => ACCEPTED_IMAGE_TYPES.includes(files?.[0]?.type), '.jpg, .jpeg, .png and .webp files are accepted.'),\n  name: z.string().min(2, {\n    message: 'Product name must be at least 2 characters.'\n  }),\n  category: z.string(),\n  price: z.number(),\n  description: z.string().min(10, {\n    message: 'Description must be at least 10 characters.'\n  })\n});\nexport default function ProductForm({\n  initialData,\n  pageTitle\n}: {\n  initialData: Product | null;\n  pageTitle: string;\n}) {\n  const defaultValues = {\n    name: initialData?.name || '',\n    category: initialData?.category || '',\n    price: initialData?.price || 0,\n    description: initialData?.description || ''\n  };\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    values: defaultValues\n  });\n  function onSubmit(values: z.infer<typeof formSchema>) {\n    // Form submission logic would be implemented here\n  }\n  return <Card className='mx-auto w-full' data-sentry-element=\"Card\" data-sentry-component=\"ProductForm\" data-sentry-source-file=\"product-form.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"product-form.tsx\">\r\n        <CardTitle className='text-left text-2xl font-bold' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"product-form.tsx\">\r\n          {pageTitle}\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"product-form.tsx\">\r\n        <Form {...form} data-sentry-element=\"Form\" data-sentry-source-file=\"product-form.tsx\">\r\n          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>\r\n            <FormField control={form.control} name='image' render={({\n            field\n          }) => <div className='space-y-6'>\r\n                  <FormItem className='w-full'>\r\n                    <FormLabel>Images</FormLabel>\r\n                    <FormControl>\r\n                      <FileUploader value={field.value} onValueChange={field.onChange} maxFiles={4} maxSize={4 * 1024 * 1024}\n                // disabled={loading}\n                // progresses={progresses}\n                // pass the onUpload function here for direct upload\n                // onUpload={uploadFiles}\n                // disabled={isUploading}\n                />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                </div>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n\r\n            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>\r\n              <FormField control={form.control} name='name' render={({\n              field\n            }) => <FormItem>\r\n                    <FormLabel>Product Name</FormLabel>\r\n                    <FormControl>\r\n                      <Input placeholder='Enter product name' {...field} />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n              <FormField control={form.control} name='category' render={({\n              field\n            }) => <FormItem>\r\n                    <FormLabel>Category</FormLabel>\r\n                    <Select onValueChange={value => field.onChange(value)} value={field.value[field.value.length - 1]}>\r\n                      <FormControl>\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder='Select categories' />\r\n                        </SelectTrigger>\r\n                      </FormControl>\r\n                      <SelectContent>\r\n                        <SelectItem value='beauty'>Beauty Products</SelectItem>\r\n                        <SelectItem value='electronics'>Electronics</SelectItem>\r\n                        <SelectItem value='clothing'>Clothing</SelectItem>\r\n                        <SelectItem value='home'>Home & Garden</SelectItem>\r\n                        <SelectItem value='sports'>\r\n                          Sports & Outdoors\r\n                        </SelectItem>\r\n                      </SelectContent>\r\n                    </Select>\r\n                    <FormMessage />\r\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n              <FormField control={form.control} name='price' render={({\n              field\n            }) => <FormItem>\r\n                    <FormLabel>Price</FormLabel>\r\n                    <FormControl>\r\n                      <Input type='number' step='0.01' placeholder='Enter price' {...field} />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n            </div>\r\n            <FormField control={form.control} name='description' render={({\n            field\n          }) => <FormItem>\r\n                  <FormLabel>Description</FormLabel>\r\n                  <FormControl>\r\n                    <Textarea placeholder='Enter product description' className='resize-none' {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n            <Button type='submit' data-sentry-element=\"Button\" data-sentry-source-file=\"product-form.tsx\">Add Product</Button>\r\n          </form>\r\n        </Form>\r\n      </CardContent>\r\n    </Card>;\n}", "import * as React from 'react';\n\n/**\r\n * @see https://github.com/radix-ui/primitives/blob/main/packages/react/use-callback-ref/src/useCallbackRef.tsx\r\n */\n\n/**\r\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\r\n * prop or avoid re-executing effects when passed as a dependency\r\n */\nfunction useCallbackRef<T extends (...args: never[]) => unknown>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\nexport { useCallbackRef };", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "\"use strict\";\n\nexports.__esModule = true;\n\nexports.default = function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n\n    if (acceptedFilesArray.length === 0) {\n      return true;\n    }\n\n    var fileName = file.name || '';\n    var mimeType = (file.type || '').toLowerCase();\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim().toLowerCase();\n\n      if (validType.charAt(0) === '.') {\n        return fileName.toLowerCase().endsWith(validType);\n      } else if (validType.endsWith('/*')) {\n        // This is something like a image/* mime type\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      }\n\n      return mimeType === validType;\n    });\n  }\n\n  return true;\n};", "module.exports = require(\"os\");", "import(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\products\\\\components\\\\product-form.tsx\");\n", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport { Controller, FormProvider, useFormContext, useFormState, type ControllerProps, type FieldPath, type FieldValues } from 'react-hook-form';\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\nconst Form = FormProvider;\ntype FormFieldContextValue<TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>> = {\n  name: TName;\n};\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\nconst FormField = <TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return <FormFieldContext.Provider value={{\n    name: props.name\n  }} data-sentry-element=\"FormFieldContext.Provider\" data-sentry-component=\"FormField\" data-sentry-source-file=\"form.tsx\">\r\n      <Controller {...props} data-sentry-element=\"Controller\" data-sentry-source-file=\"form.tsx\" />\r\n    </FormFieldContext.Provider>;\n};\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const {\n    getFieldState\n  } = useFormContext();\n  const formState = useFormState({\n    name: fieldContext.name\n  });\n  const fieldState = getFieldState(fieldContext.name, formState);\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n  const {\n    id\n  } = itemContext;\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\ntype FormItemContextValue = {\n  id: string;\n};\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\nfunction FormItem({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  const id = React.useId();\n  return <FormItemContext.Provider value={{\n    id\n  }} data-sentry-element=\"FormItemContext.Provider\" data-sentry-component=\"FormItem\" data-sentry-source-file=\"form.tsx\">\r\n      <div data-slot='form-item' className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>;\n}\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const {\n    error,\n    formItemId\n  } = useFormField();\n  return <Label data-slot='form-label' data-error={!!error} className={cn('data-[error=true]:text-destructive', className)} htmlFor={formItemId} {...props} data-sentry-element=\"Label\" data-sentry-component=\"FormLabel\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormControl({\n  ...props\n}: React.ComponentProps<typeof Slot>) {\n  const {\n    error,\n    formItemId,\n    formDescriptionId,\n    formMessageId\n  } = useFormField();\n  return <Slot data-slot='form-control' id={formItemId} aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`} aria-invalid={!!error} {...props} data-sentry-element=\"Slot\" data-sentry-component=\"FormControl\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormDescription({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    formDescriptionId\n  } = useFormField();\n  return <p data-slot='form-description' id={formDescriptionId} className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"FormDescription\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormMessage({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    error,\n    formMessageId\n  } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n  if (!body) {\n    return null;\n  }\n  return <p data-slot='form-message' id={formMessageId} className={cn('text-destructive text-sm', className)} {...props} data-sentry-component=\"FormMessage\" data-sentry-source-file=\"form.tsx\">\r\n      {body}\r\n    </p>;\n}\nexport { useFormField, Form, FormItem, FormLabel, FormControl, FormDescription, FormMessage, FormField };", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "import(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\products\\\\components\\\\product-form.tsx\");\n", "////////////////////////////////////////////////////////////////////////////////\r\n// 🛑 Nothing in here has anything to do with Nextjs, it's just a fake database\r\n////////////////////////////////////////////////////////////////////////////////\r\n\r\nimport { faker } from '@faker-js/faker';\r\nimport { matchSorter } from 'match-sorter'; // For filtering\r\n\r\nexport const delay = (ms: number) =>\r\n  new Promise((resolve) => setTimeout(resolve, ms));\r\n\r\n// Define the shape of Product data\r\nexport type Product = {\r\n  photo_url: string;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  price: number;\r\n  id: number;\r\n  category: string;\r\n  updated_at: string;\r\n};\r\n\r\n// Mock product data store\r\nexport const fakeProducts = {\r\n  records: [] as Product[], // Holds the list of product objects\r\n\r\n  // Initialize with sample data\r\n  initialize() {\r\n    const sampleProducts: Product[] = [];\r\n    function generateRandomProductData(id: number): Product {\r\n      const categories = [\r\n        'Electronics',\r\n        'Furniture',\r\n        'Clothing',\r\n        'Toys',\r\n        'Groceries',\r\n        'Books',\r\n        'Jewelry',\r\n        'Beauty Products'\r\n      ];\r\n\r\n      return {\r\n        id,\r\n        name: faker.commerce.productName(),\r\n        description: faker.commerce.productDescription(),\r\n        created_at: faker.date\r\n          .between({ from: '2022-01-01', to: '2023-12-31' })\r\n          .toISOString(),\r\n        price: parseFloat(faker.commerce.price({ min: 5, max: 500, dec: 2 })),\r\n        photo_url: `https://api.slingacademy.com/public/sample-products/${id}.png`,\r\n        category: faker.helpers.arrayElement(categories),\r\n        updated_at: faker.date.recent().toISOString()\r\n      };\r\n    }\r\n\r\n    // Generate remaining records\r\n    for (let i = 1; i <= 20; i++) {\r\n      sampleProducts.push(generateRandomProductData(i));\r\n    }\r\n\r\n    this.records = sampleProducts;\r\n  },\r\n\r\n  // Get all products with optional category filtering and search\r\n  async getAll({\r\n    categories = [],\r\n    search\r\n  }: {\r\n    categories?: string[];\r\n    search?: string;\r\n  }) {\r\n    let products = [...this.records];\r\n\r\n    // Filter products based on selected categories\r\n    if (categories.length > 0) {\r\n      products = products.filter((product) =>\r\n        categories.includes(product.category)\r\n      );\r\n    }\r\n\r\n    // Search functionality across multiple fields\r\n    if (search) {\r\n      products = matchSorter(products, search, {\r\n        keys: ['name', 'description', 'category']\r\n      });\r\n    }\r\n\r\n    return products;\r\n  },\r\n\r\n  // Get paginated results with optional category filtering and search\r\n  async getProducts({\r\n    page = 1,\r\n    limit = 10,\r\n    categories,\r\n    search\r\n  }: {\r\n    page?: number;\r\n    limit?: number;\r\n    categories?: string;\r\n    search?: string;\r\n  }) {\r\n    await delay(1000);\r\n    const categoriesArray = categories ? categories.split('.') : [];\r\n    const allProducts = await this.getAll({\r\n      categories: categoriesArray,\r\n      search\r\n    });\r\n    const totalProducts = allProducts.length;\r\n\r\n    // Pagination logic\r\n    const offset = (page - 1) * limit;\r\n    const paginatedProducts = allProducts.slice(offset, offset + limit);\r\n\r\n    // Mock current time\r\n    const currentTime = new Date().toISOString();\r\n\r\n    // Return paginated response\r\n    return {\r\n      success: true,\r\n      time: currentTime,\r\n      message: 'Sample data for testing and learning purposes',\r\n      total_products: totalProducts,\r\n      offset,\r\n      limit,\r\n      products: paginatedProducts\r\n    };\r\n  },\r\n\r\n  // Get a specific product by its ID\r\n  async getProductById(id: number) {\r\n    await delay(1000); // Simulate a delay\r\n\r\n    // Find the product by its ID\r\n    const product = this.records.find((product) => product.id === id);\r\n\r\n    if (!product) {\r\n      return {\r\n        success: false,\r\n        message: `Product with ID ${id} not found`\r\n      };\r\n    }\r\n\r\n    // Mock current time\r\n    const currentTime = new Date().toISOString();\r\n\r\n    return {\r\n      success: true,\r\n      time: currentTime,\r\n      message: `Product with ID ${id} found`,\r\n      product\r\n    };\r\n  }\r\n};\r\n\r\n// Initialize sample products\r\nfakeProducts.initialize();\r\n", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import { fakeProducts, Product } from '@/constants/mock-api';\nimport { notFound } from 'next/navigation';\nimport ProductForm from './product-form';\ntype TProductViewPageProps = {\n  productId: string;\n};\nexport default async function ProductViewPage({\n  productId\n}: TProductViewPageProps) {\n  let product = null;\n  let pageTitle = 'Create New Product';\n  if (productId !== 'new') {\n    const data = await fakeProducts.getProductById(Number(productId));\n    product = data.product as Product;\n    if (!product) {\n      notFound();\n    }\n    pageTitle = `Edit Product`;\n  }\n  return <ProductForm initialData={product} pageTitle={pageTitle} data-sentry-element=\"ProductForm\" data-sentry-component=\"ProductViewPage\" data-sentry-source-file=\"product-view-page.tsx\" />;\n}", "import FormCardSkeleton from '@/components/form-card-skeleton';\nimport Page<PERSON>ontainer from '@/components/layout/page-container';\nimport { Suspense } from 'react';\nimport ProductViewPage from '@/features/products/components/product-view-page';\nexport const metadata = {\n  title: 'Dashboard : Product View'\n};\ntype PageProps = {\n  params: Promise<{\n    productId: string;\n  }>;\n};\nexport default async function Page(props: PageProps) {\n  const params = await props.params;\n  return <PageContainer scrollable data-sentry-element=\"PageContainer\" data-sentry-component=\"Page\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex-1 space-y-4'>\r\n        <Suspense fallback={<FormCardSkeleton />} data-sentry-element=\"Suspense\" data-sentry-source-file=\"page.tsx\">\r\n          <ProductViewPage productId={params.productId} data-sentry-element=\"ProductViewPage\" data-sentry-source-file=\"page.tsx\" />\r\n        </Suspense>\r\n      </div>\r\n    </PageContainer>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/product/[productId]',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/product/[productId]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/product/[productId]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/product/[productId]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\product\\\\[productId]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'product',\n        {\n        children: [\n        '[productId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\product\\\\[productId]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\product\\\\[productId]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/product/[productId]/page\",\n        pathname: \"/dashboard/product/[productId]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"events\");"], "names": ["Progress", "className", "value", "props", "ProgressPrimitive", "cn", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "data-slot", "style", "transform", "FileUploader", "valueProp", "onValueChange", "onUpload", "progresses", "accept", "maxSize", "maxFiles", "multiple", "disabled", "dropzoneProps", "files", "setFiles", "useControllableState", "prop", "defaultProp", "onChange", "uncontrolledProp", "setUncontrolledProp", "useUncontrolledState", "uncontrolledState", "React", "prevValueRef", "handleChange", "useCallbackRef", "current", "isControlled", "undefined", "nextValue", "onDrop", "acceptedFiles", "rejectedFiles", "length", "toast", "error", "newFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "updatedFiles", "for<PERSON>ach", "name", "target", "promise", "loading", "success", "isFileWithPreview", "revokeObjectURL", "isDisabled", "div", "Dropzone", "getRootProps", "getInputProps", "isDragActive", "input", "IconUpload", "aria-hidden", "p", "Infinity", "formatBytes", "ScrollArea", "index", "FileCard", "onRemove", "filter", "_", "i", "progress", "Image", "src", "alt", "width", "height", "size", "<PERSON><PERSON>", "type", "variant", "onClick", "IconX", "span", "ACCEPTED_IMAGE_TYPES", "formSchema", "z", "image", "refine", "MAX_FILE_SIZE", "includes", "min", "message", "category", "price", "description", "ProductForm", "initialData", "pageTitle", "defaultValues", "form", "useForm", "resolver", "zodResolver", "values", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "Form", "onSubmit", "handleSubmit", "FormField", "control", "render", "field", "FormItem", "FormLabel", "FormControl", "FormMessage", "Input", "placeholder", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "step", "Textarea", "callback", "callback<PERSON><PERSON>", "args", "FormProvider", "FormFieldContext", "Provider", "Controller", "useFormField", "fieldContext", "itemContext", "FormItemContext", "getFieldState", "useFormContext", "formState", "useFormState", "fieldState", "Error", "id", "formItemId", "formDescriptionId", "formMessageId", "Label", "data-error", "htmlFor", "Slot", "aria-<PERSON><PERSON>", "aria-invalid", "FormDescription", "body", "String", "children", "CardDescription", "CardAction", "<PERSON><PERSON><PERSON>er", "delay", "Promise", "setTimeout", "resolve", "ms", "fakeProducts", "records", "initialize", "sampleProducts", "push", "faker", "commerce", "productName", "productDescription", "created_at", "date", "between", "from", "to", "toISOString", "parseFloat", "max", "dec", "photo_url", "helpers", "arrayElement", "updated_at", "recent", "getAll", "categories", "search", "products", "product", "matchSorter", "keys", "getProducts", "page", "limit", "categoriesArray", "split", "allProducts", "totalProducts", "offset", "paginatedProducts", "slice", "time", "currentTime", "Date", "total_products", "getProductById", "find", "ProductViewPage", "productId", "data", "Number", "notFound", "metadata", "title", "Page", "params", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "scrollable", "Suspense", "fallback", "FormCardSkeleton", "serverComponentModule.default"], "sourceRoot": ""}