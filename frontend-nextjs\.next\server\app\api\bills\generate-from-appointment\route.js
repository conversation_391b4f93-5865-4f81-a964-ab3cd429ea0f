try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="4da610f0-bac6-4118-9cc7-4421a62f3332",e._sentryDebugIdIdentifier="sentry-dbid-4da610f0-bac6-4118-9cc7-4421a62f3332")}catch(e){}(()=>{var e={};e.id=5467,e.ids=[5467],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7760:()=>{},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45962:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=45962,e.exports=r},48161:e=>{"use strict";e.exports=require("node:os")},49616:()=>{},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56229:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>w,serverHooks:()=>T,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>k});var s={};t.r(s),t.d(s,{DELETE:()=>g,GET:()=>f,HEAD:()=>v,OPTIONS:()=>b,PATCH:()=>m,POST:()=>h,PUT:()=>y});var o=t(86047),i=t(85544),n=t(36135),u=t(63033),a=t(35886),p=t(79615),c=t(19761);async function d(e){try{let{userId:r}=await (0,p.j)();if(!r)return a.NextResponse.json({error:"Authentication required"},{status:401});let t=await fetch(`https://api.clerk.com/v1/users/${r}`,{headers:{Authorization:`Bearer ${process.env.CLERK_SECRET_KEY}`}}).then(e=>e.json()),s=await e.json(),o=await fetch("http://localhost:8002/api/bills/generate-from-appointment",{method:"POST",headers:{"Content-Type":"application/json","x-clerk-user-id":r,"x-user-email":t.email_addresses[0]?.email_address||""},body:JSON.stringify(s)}),i=await o.json();if(!o.ok)return a.NextResponse.json(i,{status:o.status});return a.NextResponse.json(i)}catch(e){return console.error("Error proxying bills generate-from-appointment request:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let l={...u},x="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;function q(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=x?.getStore();o=e?.headers}catch(e){}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/bills/generate-from-appointment",headers:o}).apply(t,s)}})}let f=q(void 0,"GET"),h=q(d,"POST"),y=q(void 0,"PUT"),m=q(void 0,"PATCH"),g=q(void 0,"DELETE"),v=q(void 0,"HEAD"),b=q(void 0,"OPTIONS"),w=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/bills/generate-from-appointment/route",pathname:"/api/bills/generate-from-appointment",filename:"route",bundlePath:"app/api/bills/generate-from-appointment/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bills\\generate-from-appointment\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:k,serverHooks:T}=w;function _(){return(0,n.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:k})}},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86047:(e,r,t)=>{"use strict";e.exports=t(44870)},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[55,3738,1950,5886,9615],()=>t(56229));module.exports=s})();
//# sourceMappingURL=route.js.map