try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f9481eb7-4cea-4b7f-928e-286c07e32829",e._sentryDebugIdIdentifier="sentry-dbid-f9481eb7-4cea-4b7f-928e-286c07e32829")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3769],{40548:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>i,TN:()=>c,XL:()=>o});var n=r(52880);r(99004);var a=r(85017),l=r(54651);let s=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(s({variant:r}),t),...a,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"alert-title",className:(0,l.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function c(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},49202:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(99004),a={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let l=(e,t,r,l)=>{let s=(0,n.forwardRef)((r,s)=>{let{color:i="currentColor",size:o=24,stroke:c=2,title:d,className:u,children:f,...g}=r;return(0,n.createElement)("svg",{ref:s,...a[e],width:o,height:o,className:["tabler-icon","tabler-icon-".concat(t),u].join(" "),..."filled"===e?{fill:i}:{strokeWidth:c,stroke:i},...g},[d&&(0,n.createElement)("title",{key:"svg-title"},d),...l.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(f)?f:[f]])});return s.displayName="".concat(r),s}},54651:(e,t,r)=>{"use strict";r.d(t,{Jv:()=>c,cn:()=>l,fw:()=>o,r6:()=>i,z3:()=>s});var n=r(97921),a=r(56309);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function s(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:l="normal"}=n;if(0===e)return"0 Byte";let s=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,s)).toFixed(a)," ").concat("accurate"===l?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][s])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][s])?r:"Bytes")}function i(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function o(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let r=Math.floor(t/60);if(r<60)return"".concat(r,"分钟前");let n=Math.floor(r/60);if(n<24)return"".concat(n,"小时前");let a=Math.floor(n/24);if(a<7)return"".concat(a,"天前");let l=Math.floor(a/7);if(l<4)return"".concat(l,"周前");let s=Math.floor(a/30);if(s<12)return"".concat(s,"个月前");let i=Math.floor(a/365);return"".concat(i,"年前")}function c(e){return("string"==typeof e?new Date(e):e)<new Date}},71674:(e,t,r)=>{Promise.resolve().then(r.bind(r,91258))},85017:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var n=r(97921);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,s=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:i}=t,o=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let l=a(t)||a(n);return s[e][l]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return l(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},91258:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(52880),a=r(40548),l=r(92708);function s(e){let{error:t}=e;return(0,n.jsxs)(a.Fc,{variant:"destructive","data-sentry-element":"Alert","data-sentry-component":"PieStatsError","data-sentry-source-file":"error.tsx",children:[(0,n.jsx)(l.A,{className:"h-4 w-4","data-sentry-element":"IconAlertCircle","data-sentry-source-file":"error.tsx"}),(0,n.jsx)(a.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,n.jsxs)(a.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load pie statistics: ",t.message]})]})}},92708:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var n=(0,r(49202).A)("outline","alert-circle","IconAlertCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,9442,4579,9253,7358],()=>t(71674)),_N_E=e.O()}]);