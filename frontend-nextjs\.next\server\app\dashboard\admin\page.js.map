{"version": 3, "file": "../app/dashboard/admin/page.js", "mappings": "ubAAA,6GCAA,wICIA,IAAMA,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRC,QAAS,CACPC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfL,QAAS,SACX,CACF,GACA,SAASM,EAAM,CACbC,WAAS,SACTP,CAAO,CACPQ,UAAU,EAAK,CACf,GAAGC,EAGJ,EACC,IAAMC,EAAOF,EAAUG,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAKE,YAAU,QAAQL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAChB,EAAc,SACzDG,CACF,GAAIO,GAAa,GAAGE,CAAK,CAAEK,sBAAoB,OAAOC,wBAAsB,QAAQC,0BAAwB,aAC9G,0BC7BA,8FCAA,uCAAiI,yBCAjI,mECAA,0GCAA,qDCAA,gDCAA,oDCAA,kDCAA,+CCAA,wGCAA,2ECmBI,sBAAsB,onBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,MAD4B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,kBAAkB,CAClC,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACxB,CACA,CAAG,CAAC,CA/BoBC,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,yHC9E9B,SAASC,EAAK,WACZX,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,OAAOL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASI,EAAW,WAClBb,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASK,EAAU,WACjBd,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,aAAaL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASM,EAAgB,WACvBf,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,mBAAmBL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,kBAAkBC,0BAAwB,YACjL,CACA,SAASO,EAAW,CAClBhB,WAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iEAAkEN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,aAAaC,0BAAwB,YACxM,CACA,SAASQ,EAAY,WACnBjB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,eAAeL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASS,EAAW,WAClBlB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACU,MAAAA,CAAIP,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,aAAaC,0BAAwB,YACjL,iDC1CO,IAAMU,EAAe,CAE1BC,IAAK,CACHC,UAAW,MACXC,aAAc,OACdC,SAAU,OACVC,WAAY,OACZC,MAAO,OACPC,QAAS,KACTC,QAAS,OACTC,MAAO,KACPC,SAAU,IACZ,EAGAR,UAAW,CACTS,MAAO,qBACPC,SAAU,eACVC,QAAS,CACPC,kBAAmB,OACnBC,eAAgB,OAChBC,cAAe,OACfC,iBAAkB,OAClBC,kBAAmB,OACnBC,8BAA+B,UAC/BC,oBAAqB,QACrBC,8BAA+B,YAC/BC,wBAAyB,SACzBC,wBAAyB,UACzBC,0BAA2B,SAC3BC,mBAAoB,SACpBC,OAAQ,KACRC,UAAW,OACXC,QAAS,OACTC,UAAW,IACb,EACAC,OAAQ,CACNC,iBAAkB,WAClBC,oBAAqB,WACvB,CACF,EAGA7B,aAAc,CACZQ,MAAO,OACPC,SAAU,YACVqB,eAAgB,OAChBC,gBAAiB,OACjBC,mBAAoB,OACpBC,kBAAmB,MACnBC,oBAAqB,WACrBC,eAAgB,OAChBC,QAAS,CACPC,IAAK,KACLC,MAAO,KACPC,SAAU,KACVC,UAAW,KACXC,OAAQ,KACRC,UAAW,MACb,EACAD,OAAQ,CACNE,UAAW,MACXC,UAAW,MACXC,WAAY,MACZC,UAAW,MACXC,UAAW,MACXC,OAAQ,KACV,EACAC,KAAM,CACJC,QAAS,KACTC,cAAe,OACfC,UAAW,OACXC,gBAAiB,SACjBC,KAAM,KACNC,KAAM,KACNC,MAAO,KACPC,iBAAkB,WAClBhB,OAAQ,IACV,CACF,EAGAxC,SAAU,CACRO,MAAO,OACPC,SAAU,YACViD,WAAY,OACZC,YAAa,OACbC,eAAgB,OAChBC,cAAe,MACfC,gBAAiB,WACjBC,WAAY,OACZC,kBAAmB,gBACnBf,KAAM,CACJgB,SAAU,KACVC,oBAAqB,UACrBC,MAAO,KACPC,iBAAkB,UAClBC,MAAO,KACPC,iBAAkB,cAClBC,aAAc,OACdC,wBAAyB,aAC3B,CACF,EAGAtE,WAAY,CACVM,MAAO,OACPC,SAAU,WACVgE,aAAc,OACdC,cAAe,OACfC,iBAAkB,OAClBC,gBAAiB,QACjBC,kBAAmB,aACnBC,aAAc,SACd7B,KAAM,CACJ8B,KAAM,OACNC,gBAAiB,UACjBC,YAAa,OACbC,uBAAwB,UACxBC,SAAU,OACVC,oBAAqB,cACrBC,MAAO,KACPC,iBAAkB,OACpB,CACF,EAGAnF,MAAO,CACLK,MAAO,OACPC,SAAU,cACV8E,eAAgB,OAChBC,eAAgB,OAChBC,eAAgB,OAChBC,MAAO,KACPC,MAAO,CACLxF,MAAO,MACPyF,OAAQ,KACRC,UAAW,IACb,CACF,EAGAC,OAAQ,CAENC,QAAS,CACPC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,OAAQ,KACRC,MAAO,KACPC,OAAQ,KACRC,MAAO,KACPC,QAAS,KACTC,KAAM,KACNC,KAAM,MACNC,SAAU,MACVC,IAAK,KACLC,OAAQ,KACRC,OAAQ,KACRC,OAAQ,IACV,EAGAxE,OAAQ,CACNyE,QAAS,SACTC,QAAS,KACTC,MAAO,KACPC,QAAS,KACTC,KAAM,KACNC,QAAS,MACThG,OAAQ,KACRiG,SAAU,MACVC,QAAS,MACTC,SAAU,KACZ,EAGAnE,KAAM,CACJjB,MAAO,KACPqF,UAAW,KACXC,SAAU,KACVrF,SAAU,KACVsF,SAAU,KACVC,SAAU,KACVtF,UAAW,KACXuF,UAAW,KACXC,UAAW,KACXC,SAAU,KACVC,SAAU,KACVC,SAAU,IACZ,EAGAC,cAAe,CACb5H,MAAO,OACP6H,YAAa,OACbC,cAAe,uBACfC,YAAa,OACbC,cAAe,qBACfC,UAAW,OACXC,YAAa,cACf,CACF,EAGAC,WAAY,CACVC,SAAU,UACVvE,MAAO,aACPF,MAAO,aACP0E,UAAW,iBACXC,UAAW,iBACXC,OAAQ,WACRC,SAAU,QACV1F,KAAM,WACNC,KAAM,UACR,EAGA5B,OAAQ,CACNsH,QAAS,gBACTC,QAAS,mBACTC,aAAc,aACdC,SAAU,WACVC,YAAa,cACbC,gBAAiB,WACjBC,WAAY,SACZC,WAAY,SACZC,aAAc,SACdC,aAAc,SACdC,aAAc,QAChB,EAGAxC,QAAS,CACPyC,MAAO,OACPC,QAAS,OACTC,QAAS,OACTC,QAAS,OACTC,KAAM,OACNC,SAAU,OACVC,WAAY,MACd,CACF,EAAW,SAGKC,EAAEC,CAAW,CAAEC,CAAwC,EACrE,IAAMC,EAAOF,EAAIG,KAAK,CAAC,KACnBC,EAAa3K,EAEjB,IAAK,IAAM4K,KAAKH,EACd,GADoB,CAChBE,GAA0B,UAAjB,OAAOA,IAAsBC,MAAKD,CAAAA,EAI7C,CAJoD,MAGpDE,QAAQC,IAAI,CAAC,CAAC,2BAA2B,EAAEP,EAAAA,CAAK,EACzCA,KAAK,EAHZI,EAAQA,CAAK,CAACC,EAAE,OAOpB,KAJoC,KAIhC,OAAOD,GACTE,QAAQC,IAAI,CAAC,CAAC,mCAAmC,EAAEP,EAAAA,CAAK,EACjDA,GAILC,EACKG,EAAMI,IADH,GACU,CAAC,aAAc,CAACC,EAAOC,IAClCT,CAAM,CAACS,EAAS,EAAEC,YAAcF,GAIpCL,CACT,0BCnRA,mNCCe,SAAqB,WAAW,QAAU,cAAc,CAAC,CAAC,OAAO,CAAC,EAAI,qFAAsF,KAAM,CAAO,OAAC,CAAC,CAAC,aCA5K,SAAqB,WAAW,YAAc,iBAAiB,CAAC,CAAC,OAAO,CAAC,EAAI,mCAAoC,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,2BAA4B,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,iBAAkB,KAAM,CAAO,OAAC,CAAC,CAAC,8CCyBzO,SAASQ,IACtB,GAAM,QACJC,CAAM,UACNC,CAAQ,CACT,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,GACL,CACJC,KAAMC,CAAW,aACjBC,CAAW,CACZ,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAAC7F,EAAO8F,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,EAAE,EAC9C,CAACvE,EAASwE,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACrE,EAAOuE,EAAS,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5C,CAACG,EAAgBC,EAAkB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAGhEP,GAAY,CAACD,GACfa,CAAAA,EAAAA,EADuB,QACvBA,CAAQA,CAAC,iBAIX,IAAMC,EAAa,UACjB,GAAKV,CAAD,CACJ,GAAI,CACFK,GAAW,GACX,CAHgB,GAGVM,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAACZ,GACpCa,EAAY,MAAMF,EAAcG,QAAQ,CAAC,CAC7CC,MAAO,GACT,GACAZ,EAASU,EAASG,IAAI,EAAI,EAAE,EAC5BV,EAAS,KACX,CAAE,MAAOW,EAAK,CACZ5B,QAAQtD,KAAK,CAAC,yBAA0BkF,GACxCX,EAAS,gDACX,QAAU,CACRD,EAAW,GACb,CACF,EAMMa,EAAmB,MAAOtB,EAAgBuB,KAC9C,GAAKnB,CAAD,CACJ,GAAI,CACFQ,EAAkBZ,GAClB,EAHgB,EAGVe,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAACZ,EAC1C,OAAMW,EAAcS,UAAU,CAACxB,EAAQ,CACrCyB,KAAMF,CACR,GAGAhB,EAAS9F,EAAMiH,GAAG,CAACvB,GAAQA,EAAKwB,EAAE,GAAK3B,EAAS,CAC9C,GAAGG,CAAI,CACPsB,KAAMF,CACR,EAAIpB,IACJyB,EAAAA,KAAKA,CAAC1F,OAAO,CAAC,kCAGVkE,EAAYyB,aAAa,GAAK7B,GAChC,KADwC,CAClCK,GAEV,CAAE,MAAOlE,EAAO,CACdsD,QAAQtD,KAAK,CAAC,8BAA+BA,GAC7CyF,EAAAA,KAAKA,CAACzF,KAAK,CAAC,6BACd,QAAU,CACRyE,EAAkB,KACpB,CACF,QACA,CAAKX,GAAYhE,EACR,OADiB,CACjB,EAAC6F,EAAAA,CAAaA,CAAAA,UACjB,UAACzN,MAAAA,CAAIZ,UAAU,iDACb,WAACY,MAAAA,CAAIZ,UAAU,wBACb,UAACY,MAAAA,CAAIZ,UAAU,6EACf,UAACsO,IAAAA,CAAEtO,UAAU,iCAAwB,sBAKxC,UAACuO,EAAAA,EAAQA,CAAAA,CAACtH,MAAM,QAAQuH,SAAU,UAACH,EAAAA,CAAaA,CAAAA,UAC/C,UAACzN,MAAAA,CAAIZ,UAAU,iDACb,WAACY,MAAAA,CAAIZ,UAAU,wBACb,UAACyO,EAAUA,CAACzO,OAADyO,GAAW,+CACtB,UAACC,KAAAA,CAAG1O,UAAU,sCAA6B,UAC3C,UAACsO,IAAAA,CAAEtO,UAAU,iCAAwB,4BAKzBO,sBAAoB,WAAWC,wBAAsB,YAAYC,0BAAwB,oBAC7G,UAAC4N,EAAAA,CAAaA,CAAAA,CAAC9N,sBAAoB,gBAAgBE,0BAAwB,oBACzE,WAACG,MAAAA,CAAIZ,UAAU,2CAEb,UAACY,MAAAA,CAAIZ,UAAU,6CACb,WAACY,MAAAA,WACC,WAAC+N,KAAAA,CAAG3O,UAAU,sEACZ,UAAC4O,EAAAA,CAASA,CAAAA,CAAC5O,UAAU,SAASO,sBAAoB,YAAYE,0BAAwB,aACrFgL,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,kBAEL,UAAC6C,IAAAA,CAAEtO,UAAU,iCACVyL,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,yBAMT,WAAC9K,EAAAA,EAAIA,CAAAA,CAACJ,sBAAoB,OAAOE,0BAAwB,qBACvD,WAACI,EAAAA,EAAUA,CAAAA,CAACN,sBAAoB,aAAaE,0BAAwB,qBACnE,WAACK,EAAAA,EAASA,CAAAA,CAACd,UAAU,0BAA0BO,sBAAoB,YAAYE,0BAAwB,qBACrG,UAACoO,EAAaA,CAAC7O,UAAD6O,SAAoBtO,sBAAoB,gBAAgBE,0BAAwB,aAAa,UAG7G,UAACM,EAAAA,EAAeA,CAAAA,CAACR,sBAAoB,kBAAkBE,0BAAwB,oBAAW,iBAI5F,UAACQ,EAAAA,EAAWA,CAAAA,CAACV,sBAAoB,cAAcE,0BAAwB,oBACrE,WAACG,MAAAA,CAAIZ,UAAU,oCACb,WAACY,MAAAA,WACC,UAAC0N,IAAAA,CAAEtO,UAAU,uBAAe2M,GAAahH,QACzC,WAAC2I,IAAAA,CAAEtO,UAAU,0CACV2M,GAAamC,UAAU,IAAEnC,GAAaoC,eAG3C,UAAChP,EAAAA,CAAKA,CAAAA,CAACC,UAAWgP,CAAAA,EAAAA,EAAAA,EAAAA,CAAiBA,CAACrC,GAAaqB,MAAQ,cAAezN,sBAAoB,QAAQE,0BAAwB,oBACzHwO,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACtC,GAAaqB,MAAQ,wBAOhDtF,EAAQ,UAAC/H,EAAAA,EAAIA,CAAAA,UACV,UAACM,EAAAA,EAAWA,CAAAA,CAACjB,UAAU,gBACrB,WAACY,MAAAA,CAAIZ,UAAU,6BACb,UAACsO,IAAAA,CAAEtO,UAAU,6BAAqB0I,IAClC,UAACwG,EAAAA,CAAMA,CAAAA,CAACC,QAAS9B,WAAY,qBAGzB,WAAC1M,EAAAA,EAAIA,CAAAA,WACb,WAACE,EAAAA,EAAUA,CAAAA,WACT,WAACC,EAAAA,EAASA,CAAAA,WAAC,cAAYkG,EAAMoI,MAAM,CAAC,OACpC,UAACrO,EAAAA,EAAeA,CAAAA,UAAC,yCAInB,UAACE,EAAAA,EAAWA,CAAAA,UACV,WAACL,MAAAA,CAAIZ,UAAU,sBACZgH,EAAMiH,GAAG,CAACvB,GAAQ,WAAC9L,MAAAA,CAAkBZ,UAAU,oEAC5C,UAACY,MAAAA,CAAIZ,UAAU,kBACb,UAACY,MAAAA,CAAIZ,UAAU,mCACb,WAACY,MAAAA,WACC,UAAC0N,IAAAA,CAAEtO,UAAU,uBAAe0M,EAAK/G,KAAK,GACtC,WAAC2I,IAAAA,CAAEtO,UAAU,0CACV0M,EAAKoC,SAAS,CAAC,IAAEpC,EAAKqC,QAAQ,IAEjC,WAACT,IAAAA,CAAEtO,UAAU,0CAAgC,WAClC,IAAIqP,KAAK3C,EAAK4C,SAAS,EAAEC,kBAAkB,aAM5D,WAAC3O,MAAAA,CAAIZ,UAAU,oCACb,UAACD,EAAAA,CAAKA,CAAAA,CAACC,UAAWgP,CAAAA,EAAAA,EAAAA,EAAAA,CAAiBA,CAACtC,EAAKsB,IAAI,WAC1CiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACvC,EAAKsB,IAAI,IAG/B,WAACwB,EAAAA,EAAMA,CAAAA,CAAC1D,MAAOY,EAAKsB,IAAI,CAAEyB,cAAe,GAAuB5B,EAAiBnB,EAAKwB,EAAE,CAAEJ,GAAU9E,SAAUkE,IAAmBR,EAAKwB,EAAE,WACtI,UAACwB,EAAAA,EAAaA,CAAAA,CAAC1P,UAAU,gBACvB,UAAC2P,EAAAA,EAAWA,CAAAA,CAAAA,KAEd,WAACC,EAAAA,EAAaA,CAAAA,WACZ,UAACC,EAAAA,EAAUA,CAAAA,CAAC/D,MAAM,iBAAQ,kBAC1B,UAAC+D,EAAAA,EAAUA,CAAAA,CAAC/D,MAAM,kBAAS,WAC3B,UAAC+D,EAAAA,EAAUA,CAAAA,CAAC/D,MAAM,sBAAa,0BA3BZY,EAAKwB,EAAE,GAiClB,IAAjBlH,EAAMoI,MAAM,EAAU,WAACxO,MAAAA,CAAIZ,UAAU,6BAClC,UAAC4O,EAAAA,CAASA,CAAAA,CAAC5O,UAAU,+CACrB,UAACsO,IAAAA,CAAEtO,UAAU,iCAAwB,oCAQ3D,yBCzNA,kECAA,uDCAA,sDCAA,uDCAA,wDCAA,8CCAA,uCAAiI,yBCAjI,sECAA,oDCAA,kECAA,yDCAA,uDCAA,uMCKA,SAAS8P,EAAW,CAClB9P,WAAS,UACT+P,CAAQ,CACR,GAAG7P,EACmD,EACtD,MAAO,WAAC8P,EAAAA,EAAwB,EAAC3P,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,WAAYN,GAAa,GAAGE,CAAK,CAAEK,sBAAoB,2BAA2BC,wBAAsB,aAAaC,0BAAwB,4BAChN,UAACuP,EAAAA,EAA4B,EAAC3P,YAAU,uBAAuBL,UAAU,qJAAqJO,sBAAoB,+BAA+BE,0BAAwB,2BACtSsP,IAEH,UAACE,EAAAA,CAAU1P,sBAAoB,YAAYE,0BAAwB,oBACnE,UAACuP,EAAAA,EAA0B,EAACzP,sBAAoB,6BAA6BE,0BAAwB,sBAE3G,CACA,SAASwP,EAAU,WACjBjQ,CAAS,aACTkQ,EAAc,UAAU,CACxB,GAAGhQ,EACkE,EACrE,MAAO,UAAC8P,EAAAA,EAAuC,EAAC3P,YAAU,wBAAwB6P,YAAaA,EAAalQ,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qDAAsE,aAAhB4P,GAA8B,6CAA8D,eAAhBA,GAAgC,+CAAgDlQ,GAAa,GAAGE,CAAK,CAAEK,sBAAoB,0CAA0CC,wBAAsB,YAAYC,0BAAwB,2BACvd,UAACuP,EAAAA,EAAmC,EAAC3P,YAAU,oBAAoBL,UAAU,yCAAyCO,sBAAoB,sCAAsCE,0BAAwB,qBAE9M,uXExBe,SAAS4N,EAAc,UACpC0B,CAAQ,YACRI,GAAa,CAAI,CAIlB,EACC,MAAO,+BACFA,EAAa,UAACL,EAAAA,UAAUA,CAAAA,CAAC9P,UAAU,iCAChC,UAACY,MAAAA,CAAIZ,UAAU,mCAA2B+P,MAC5B,UAACnP,MAAAA,CAAIZ,UAAU,mCAA2B+P,KAElE,0BCdA,qDCAA,4DCAA,iDCAA,yDCAA,iECAA,uDCAA,sDCAA,uKCQA,IAAM,EAAyB,iBAAM,CAA2D,IAAI,EAE7F,SAAS,EAAwB,aACtC,WACA,EACF,EAGG,OACM,kBAAC,EAAuB,SAAvB,CAAgC,MAAO,GAAc,EAC/D,CAsCO,KAvCiE,IAuCxD,IACd,IAAM,EAAgB,WADa,GACb,CAAS,CAAC,EAC1B,EAAmB,cAAM,CAAW,GAEtC,EAAe,SACf,GAAoB,KAHwC,IAG9B,IAChC,EAAe,OAAM,CAAI,EAAgB,EAOrC,GACK,QAAO,CAAC,EAIV,CALY,EAKZ,KAAc,CAAC,EAI1B,UAJsC,eC1EtC,kDCAA,2DCAA,2DCAA,gDCAA,0DCAA,4DCAA,iVCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,QACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAiI,CAoBrJ,gGAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAA4H,CAgClJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EAEA,CAAO,CACP,CACA,QAjDA,IAAsB,uCAAiH,CAiDvI,gFACA,gBAjDA,IAAsB,uCAAuH,CAiD7I,sFACA,aAjDA,IAAsB,sCAAoH,CAiD1I,mFACA,WAjDA,IAAsB,4CAAgF,CAiDtG,+CACA,cAjDA,IAAsB,4CAAmF,CAiDzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,mGAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,6BACA,4BAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,gDC/EM,OAAMK,EAGXC,YAAY3D,CAAuB,CAAE,CACnC,IAAI,CAACA,IAAI,CAAGA,CACd,CAOA,MAAc4D,YACZC,CAAgB,CAChBC,EAAiC,CAAC,CAAC,CACvB,CACZ,IAKIC,EACAC,EANE,QAAEC,EAAS,KAAK,MAAEC,CAAI,QAAEjF,CAAM,CAAE,CAAG6E,EAavC,GAFAC,EAAM,GAAGI,WAAW,IAAI,UAAEN,GAAU,CAEhC5E,EAAQ,CACV,IAAMmF,EAAe,IAAIC,gBACzBC,OAAOC,OAAO,CAACtF,GAAQuF,OAAO,CAAC,CAAC,CAACxF,EAAKI,EAAM,UACtCA,GACFgF,EAAaK,KADDC,CACO,CAAC1F,EAAKI,EAAMO,OADNP,CACc,GAE3C,GACIgF,EAAazE,CAJsB,MAAM,CAIpB,IAAI,CAC3BoE,GAAO,CAAC,CAAC,EAAEK,EAAazE,QAAQ,KAEpC,CAGAqE,EAAiB,QACfC,EACAU,QAAS,CACP,eAAgB,mBAChB,kBAAmB,IAAI,CAAC3E,IAAI,CAAC4E,OAAO,CACpC,eAAgB,IAAI,CAAC5E,IAAI,CAAC/G,KAAK,CAEnC,EAwBEiL,GAAmB,OAAO,CAAlBD,IACVD,EAAeE,IAAI,CAAGW,KAAKC,SAAS,CAACZ,EAAAA,EAGvC,GAAI,CACF,IAAMpD,EAAW,MAAMiE,MAAMhB,EAAKC,GAElC,GAAI,CAAClD,EAASkE,EAAE,CAAE,CAChB,IAAMC,EAAY,MAAMnE,EAASoE,IAAI,EACrC,OAAM,MACJ,CAAC,oBAAoB,EAAEpE,EAASzJ,MAAM,CAAC,CAAC,EAAEyJ,EAASqE,UAAU,CAAC,GAAG,EAAEF,EAAAA,CAAW,CAElF,CAEA,OAAO,MAAMnE,EAASsE,IAAI,EAC5B,CAAE,MAAOpJ,EAAO,CAEd,MADAsD,QAAQtD,KAAK,CAAC,CAAC,uBAAuB,EAAE6H,EAAS,CAAC,CAAC,CAAE7H,GAC/CA,CACR,CACF,CAKA,MAAMqJ,gBAAgBpG,CAIrB,CAAE,CACD,OAAO,IAAI,CAAC2E,WAAW,CAAC,gBAAiB,CACvC3E,OAAQ,CACNqG,MAAO,IACP,GAAGrG,CAAM,CAEb,EACF,CAEA,MAAMsG,eAAe/D,CAAU,CAAE,CAC/B,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,cAAc,EAAEpC,EAAAA,CAAI,CAAE,CAC7CvC,OAAQ,CAAEqG,MAAO,GAAI,CACvB,EACF,CAEA,MAAME,kBAAkBC,CAAS,CAAE,CACjC,OAAO,IAAI,CAAC7B,WAAW,CAAC,gBAAiB,CACvCK,OAAQ,OACRC,KAAMuB,CACR,EACF,CAEA,MAAMC,kBAAkBlE,CAAU,CAAEiE,CAAS,CAAE,CAC7C,OAAO,IAAI,CAAC7B,WAAW,CAAC,CAAC,cAAc,EAAEpC,EAAAA,CAAI,CAAE,CAC7CyC,OAAQ,QACRC,KAAMuB,CACR,EACF,CAEA,MAAME,kBAAkBnE,CAAU,CAAE,CAClC,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,cAAc,EAAEpC,EAAAA,CAAI,CAAE,CAC7CyC,OAAQ,QACV,EACF,CAKA,MAAM2B,YAAY3G,CAIjB,CAAE,CACD,IAAM4G,EAAmB,CAAEP,MAAO,IAAK,GAAGrG,CAAM,EAUhD,OAPIA,GAAQhE,QAAQ,CAClB4K,CAAW,CAAC,mCAAmC,CAAG5G,EAAOhE,MAAM,CAC/D4K,CAAW,CAAC,gCAAgC,CAAG5G,EAAOhE,MAAM,CAC5D4K,CAAW,CAAC,gCAAgC,CAAG5G,EAAOhE,MAAM,CAC5D,OAAO4K,EAAY5K,MAAM,EAAE,IAGlB,CAAC2I,WAAW,CAAC,WAHiC,CAGpB,CAAE3E,OAAQ4G,CAAY,EAC7D,CAEA,MAAMC,WAAWtE,CAAU,CAAE,CAC3B,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,UAAU,EAAEpC,EAAAA,CAAI,CAAE,CACzCvC,OAAQ,CAAEqG,MAAO,GAAI,CACvB,EACF,CAEA,MAAMS,cAAcN,CAAS,CAAE,CAC7B,OAAO,IAAI,CAAC7B,WAAW,CAAC,YAAa,CACnCK,OAAQ,OACRC,KAAMuB,CACR,EACF,CAEA,MAAMO,cAAcxE,CAAU,CAAEiE,CAAS,CAAE,CACzC,OAAO,IAAI,CAAC7B,WAAW,CAAC,CAAC,UAAU,EAAEpC,EAAAA,CAAI,CAAE,CACzCyC,OAAQ,QACRC,KAAMuB,CACR,EACF,CAEA,MAAMQ,cAAczE,CAAU,CAAE,CAC9B,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,UAAU,EAAEpC,EAAAA,CAAI,CAAE,CACzCyC,OAAQ,QACV,EACF,CAKA,MAAMiC,cAAcjH,CAGnB,CAAE,CACD,OAAO,IAAI,CAAC2E,WAAW,CAAC,cAAe,CACrC3E,OAAQ,CAAEqG,MAAO,IAAK,GAAGrG,CAAM,CACjC,EACF,CAEA,MAAMkH,aAAa3E,CAAU,CAAE,CAC7B,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,YAAY,EAAEpC,EAAAA,CAAI,CAC7C,CAEA,MAAM4E,gBAAgBX,CAAS,CAAE,CAC/B,OAAO,IAAI,CAAC7B,WAAW,CAAC,cAAe,CACrCK,OAAQ,OACRC,KAAMuB,CACR,EACF,CAEA,MAAMY,gBAAgB7E,CAAU,CAAEiE,CAAS,CAAE,CAC3C,OAAO,IAAI,CAAC7B,WAAW,CAAC,CAAC,YAAY,EAAEpC,EAAAA,CAAI,CAAE,CAC3CyC,OAAQ,QACRC,KAAMuB,CACR,EACF,CAEA,MAAMa,gBAAgB9E,CAAU,CAAE,CAChC,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,YAAY,EAAEpC,EAAAA,CAAI,CAAE,CAC3CyC,OAAQ,QACV,EACF,CAKA,MAAMlD,SAAS9B,CAGd,CAAE,CACD,OAAO,IAAI,CAAC2E,WAAW,CAAC,SAAU,CAChC3E,OAAQ,CAAEqG,MAAO,IAAK,GAAGrG,CAAO,CAClC,EACF,CAEA,MAAMoC,WAAWxB,CAAc,CAAE4F,CAAS,CAAE,CAC1C,OAAO,IAAI,CAAC7B,WAAW,CAAC,CAAC,OAAO,EAAE/D,EAAAA,CAAQ,CAAE,CAC1CoE,OAAQ,QACRC,KAAMuB,CACR,EACF,CAEA,MAAMc,iBAAkB,CAEtB,OAAO,IAAI,CAAC3C,WAAW,CAAC,cAAe,CACrCK,OAAQ,OACRC,KAAM,CACJU,QAAS,IAAI,CAAC5E,IAAI,CAAC4E,OAAO,CAC1B3L,MAAO,IAAI,CAAC+G,IAAI,CAAC/G,KAAK,CACtBmJ,UAAW,IAAI,CAACpC,IAAI,CAACoC,SAAS,CAC9BC,SAAU,IAAI,CAACrC,IAAI,CAACqC,QAAQ,CAEhC,EACF,CAEA,MAAMmE,SAASC,CAKd,CAAE,CAED,GAAI,CAEF,IAAMC,EAAgB,MAAM,IAAI,CAAC9C,WAAW,CAAM,SAAU,CAC1D3E,OAAQ,CACN0H,MAAO9B,KAAKC,SAAS,CAAC,CACpBF,QAAS,CAAEgC,OAAQH,EAAS7B,OAAO,CACrC,GACA5D,MAAO,CACT,CACF,GAEA,IAAI0F,EAAczF,IAAI,IAAIyF,EAAczF,IAAI,CAACyB,MAAM,EAAG,EA0BpD,OAXgB,MAAM,IAAI,CAACkB,WAAW,CAAM,SAAU,CACpDK,OAAQ,OACRC,KAAM,CACJjL,MAAOwN,EAASxN,KAAK,CACrB2L,QAAS6B,EAAS7B,OAAO,CACzBxC,UAAWqE,EAASrE,SAAS,CAC7BC,SAAUoE,EAASpE,QAAQ,CAC3Bf,KAAM,aACNuF,UAAW,IAAIlE,OAAOmE,WAAW,EACnC,CACF,EAzBuD,EAEvD,IAAMC,EAAeL,EAAczF,IAAI,CAAC,EAAE,CAU1C,OAToB,MAAM,IAAI,CAAC2C,WAAW,CAAM,CAAC,OAAO,EAAEmD,EAAavF,EAAE,EAAE,CAAE,CAC3EyC,OAAQ,QACRC,KAAM,CACJjL,MAAOwN,EAASxN,KAAK,CACrBmJ,UAAWqE,EAASrE,SAAS,CAC7BC,SAAUoE,EAASpE,QAAQ,CAC3BwE,UAAW,IAAIlE,OAAOmE,WAAW,EACnC,CACF,EAEF,CAeF,CAAE,KAfO,CAeA9K,EAAO,CAGd,OAFAsD,QAAQtD,KAAK,CAAC,mCAAoCA,GAE3C,CACLwF,GAAI,UACJvI,MAAOwN,EAASxN,KAAK,CACrB2L,QAAS6B,EAAS7B,OAAO,CACzBtD,KAAM,aACNc,UAAWqE,EAASrE,SAAS,CAC7BC,SAAUoE,EAASpE,QAAQ,CAE/B,CACF,CAKA,MAAM2E,uBAAuB/H,CAK5B,CAAE,CACD,OAAO,IAAI,CAAC2E,WAAW,CAAC,wBAAyB,CAC/C3E,OAAQ,CACNqG,MAAO,IACP,GAAGrG,CAAM,CAEb,EACF,CAEA,MAAMgI,sBAAsBzF,CAAU,CAAE,CACtC,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,sBAAsB,EAAEpC,EAAAA,CAAI,CAAE,CACrDvC,OAAQ,CAAEqG,MAAO,GAAI,CACvB,EACF,CAEA,MAAM4B,yBAAyBzB,CAAS,CAAE,CACxC,OAAO,IAAI,CAAC7B,WAAW,CAAC,wBAAyB,CAC/CK,OAAQ,OACRC,KAAMuB,CACR,EACF,CAEA,MAAM0B,yBAAyB3F,CAAU,CAAEiE,CAAS,CAAE,CACpD,OAAO,IAAI,CAAC7B,WAAW,CAAC,CAAC,sBAAsB,EAAEpC,EAAAA,CAAI,CAAE,CACrDyC,OAAQ,QACRC,KAAMuB,CACR,EACF,CAEA,MAAM2B,yBAAyB5F,CAAU,CAAE,CACzC,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,sBAAsB,EAAEpC,EAAAA,CAAI,CAAE,CACrDyC,OAAQ,QACV,EACF,CAKA,MAAMoD,gBAAgBpI,CAKrB,CAAE,CACD,OAAO,IAAI,CAAC2E,WAAW,CAAC,iBAAkB,CACxC3E,OAAQ,CACNqG,MAAO,IACP,GAAGrG,CACL,CACF,EACF,CAEA,MAAMqI,eAAe9F,CAAU,CAAE,CAC/B,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,eAAe,EAAEpC,EAAAA,CAAI,CAAE,CAC9CvC,OAAQ,CAAEqG,MAAO,GAAI,CACvB,EACF,CAEA,MAAMiC,kBAAkB9B,CAAS,CAAE,CACjC,OAAO,IAAI,CAAC7B,WAAW,CAAC,iBAAkB,CACxCK,OAAQ,OACRC,KAAMuB,CACR,EACF,CAEA,MAAM+B,kBAAkBhG,CAAU,CAAEiE,CAAS,CAAE,CAC7C,OAAO,IAAI,CAAC7B,WAAW,CAAC,CAAC,eAAe,EAAEpC,EAAAA,CAAI,CAAE,CAC9CyC,OAAQ,QACRC,KAAMuB,CACR,EACF,CAEA,MAAMgC,kBAAkBjG,CAAU,CAAE,CAClC,OAAO,IAAI,CAACoC,WAAW,CAAC,CAAC,eAAe,EAAEpC,EAAAA,CAAI,CAAE,CAC9CyC,OAAQ,QACV,EACF,CAKA,MAAMyD,gCAAgCC,CAAiB,CAAE1I,CAMxD,CAAE,CACD,OAAO,IAAI,CAAC2E,WAAW,CAAC,CAAC,UAAU,EAAE+D,EAAU,aAAa,CAAC,CAAE,CAC7D1I,OAAQ,CACNqG,MAAO,IACP,GAAGrG,CAAM,CAEb,EACF,CAEA,MAAM2I,yBAAyBD,CAAiB,CAAE1I,CAOjD,CAAE,CACD,OAAO,IAAI,CAAC2E,WAAW,CAAC,CAAC,UAAU,EAAE+D,EAAU,MAAM,CAAC,CAAE,CACtD1I,OAAQ,CACNqG,MAAO,IACP,GAAGrG,CAAM,CAEb,EACF,CAEA,MAAM4I,mBAAmBF,CAAiB,CAAE1I,CAI3C,CAAE,CACD,OAAO,IAAI,CAAC2E,WAAW,CAAC,CAAC,UAAU,EAAE+D,EAAU,SAAS,CAAC,CAAE,CACzD1I,OAAQ,CACNqG,MAAO,IACP,GAAGrG,CAAM,CAEb,EACF,CACF,CAKO,SAAS4B,EAAoBb,CAAuB,EACzD,OAAO,IAAI0D,EAAc1D,EAC3B", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/badge.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/?2f48", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/./src/components/ui/card.tsx", "webpack://next-shadcn-dashboard-starter/./src/lib/translations.ts", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconShield.ts", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconUserCheck.ts", "webpack://next-shadcn-dashboard-starter/./src/app/dashboard/admin/page.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/?6342", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/scroll-area.tsx", "webpack://next-shadcn-dashboard-starter/?b8d0", "webpack://next-shadcn-dashboard-starter/./src/components/layout/page-container.tsx", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/../../../src/client-boundary/PromisifiedAuthProvider.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\"", "webpack://next-shadcn-dashboard-starter/?59be", "webpack://next-shadcn-dashboard-starter/./src/lib/payload-client.ts"], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/admin',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/admin',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/admin',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/admin',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "// 中文翻译文件\nexport const translations = {\n  // 导航菜单\n  nav: {\n    dashboard: '仪表板',\n    appointments: '预约管理',\n    patients: '患者管理',\n    treatments: '治疗项目',\n    admin: '系统管理',\n    account: '账户',\n    profile: '个人资料',\n    login: '登录',\n    overview: '概览'\n  },\n\n  // 仪表板\n  dashboard: {\n    title: '诊所控制台 🏥',\n    subtitle: '欢迎使用您的诊所管理系统',\n    metrics: {\n      todayAppointments: '今日预约',\n      recentPatients: '近期患者',\n      totalPatients: '患者总数',\n      activetreatments: '可用治疗',\n      scheduledForToday: '今日安排',\n      appointmentsScheduledForToday: '今日安排的预约',\n      newPatientsThisWeek: '本周新患者',\n      patientsRegisteredInLast7Days: '过去7天注册的患者',\n      totalRegisteredPatients: '注册患者总数',\n      completePatientDatabase: '完整患者数据库',\n      treatmentOptionsAvailable: '可用治疗选项',\n      fullServiceCatalog: '完整服务目录',\n      active: '活跃',\n      last7Days: '过去7天',\n      allTime: '全部时间',\n      available: '可用'\n    },\n    errors: {\n      loadingDashboard: '加载仪表板时出错',\n      failedToLoadMetrics: '无法加载仪表板数据'\n    }\n  },\n\n  // 预约管理\n  appointments: {\n    title: '预约管理',\n    subtitle: '管理患者预约和排程',\n    newAppointment: '新建预约',\n    editAppointment: '编辑预约',\n    appointmentDetails: '预约详情',\n    appointmentsCount: '个预约',\n    loadingAppointments: '加载预约中...',\n    noAppointments: '暂无预约',\n    filters: {\n      all: '全部',\n      today: '今天',\n      thisWeek: '本周',\n      thisMonth: '本月',\n      status: '状态',\n      dateRange: '日期范围'\n    },\n    status: {\n      scheduled: '已安排',\n      confirmed: '已确认',\n      inProgress: '进行中',\n      completed: '已完成',\n      cancelled: '已取消',\n      noShow: '未到场'\n    },\n    form: {\n      patient: '患者',\n      selectPatient: '选择患者',\n      treatment: '治疗项目',\n      selectTreatment: '选择治疗项目',\n      date: '日期',\n      time: '时间',\n      notes: '备注',\n      notesPlaceholder: '预约备注（可选）',\n      status: '状态'\n    }\n  },\n\n  // 患者管理\n  patients: {\n    title: '患者管理',\n    subtitle: '管理患者信息和病历',\n    newPatient: '新建患者',\n    editPatient: '编辑患者',\n    patientDetails: '患者详情',\n    patientsCount: '位患者',\n    loadingPatients: '加载患者中...',\n    noPatients: '暂无患者',\n    searchPlaceholder: '按姓名、电话或邮箱搜索患者',\n    form: {\n      fullName: '姓名',\n      fullNamePlaceholder: '请输入患者姓名',\n      phone: '电话',\n      phonePlaceholder: '请输入电话号码',\n      email: '邮箱',\n      emailPlaceholder: '请输入邮箱地址（可选）',\n      medicalNotes: '病历备注',\n      medicalNotesPlaceholder: '请输入病历备注（可选）'\n    }\n  },\n\n  // 治疗项目\n  treatments: {\n    title: '治疗项目',\n    subtitle: '管理诊所治疗服务',\n    newTreatment: '新建治疗',\n    editTreatment: '编辑治疗',\n    treatmentDetails: '治疗详情',\n    treatmentsCount: '个治疗项目',\n    loadingTreatments: '加载治疗项目中...',\n    noTreatments: '暂无治疗项目',\n    form: {\n      name: '治疗名称',\n      namePlaceholder: '请输入治疗名称',\n      description: '治疗描述',\n      descriptionPlaceholder: '请输入治疗描述',\n      duration: '治疗时长',\n      durationPlaceholder: '请输入治疗时长（分钟）',\n      price: '价格',\n      pricePlaceholder: '请输入价格'\n    }\n  },\n\n  // 系统管理\n  admin: {\n    title: '系统管理',\n    subtitle: '管理用户权限和系统设置',\n    userManagement: '用户管理',\n    roleManagement: '角色管理',\n    systemSettings: '系统设置',\n    users: '用户',\n    roles: {\n      admin: '管理员',\n      doctor: '医生',\n      frontDesk: '前台'\n    }\n  },\n\n  // 通用文本\n  common: {\n    // 操作按钮\n    actions: {\n      save: '保存',\n      cancel: '取消',\n      edit: '编辑',\n      delete: '删除',\n      view: '查看',\n      search: '搜索',\n      filter: '筛选',\n      reset: '重置',\n      submit: '提交',\n      close: '关闭',\n      confirm: '确认',\n      back: '返回',\n      next: '下一步',\n      previous: '上一步',\n      add: '添加',\n      remove: '移除',\n      update: '更新',\n      create: '创建'\n    },\n\n    // 状态\n    status: {\n      loading: '加载中...',\n      success: '成功',\n      error: '错误',\n      warning: '警告',\n      info: '信息',\n      pending: '待处理',\n      active: '活跃',\n      inactive: '非活跃',\n      enabled: '已启用',\n      disabled: '已禁用'\n    },\n\n    // 时间相关\n    time: {\n      today: '今天',\n      yesterday: '昨天',\n      tomorrow: '明天',\n      thisWeek: '本周',\n      lastWeek: '上周',\n      nextWeek: '下周',\n      thisMonth: '本月',\n      lastMonth: '上月',\n      nextMonth: '下月',\n      thisYear: '今年',\n      lastYear: '去年',\n      nextYear: '明年'\n    },\n\n    // 确认对话框\n    confirmDialog: {\n      title: '确认操作',\n      deleteTitle: '确认删除',\n      deleteMessage: '您确定要删除这个项目吗？此操作无法撤销。',\n      cancelTitle: '确认取消',\n      cancelMessage: '您确定要取消吗？未保存的更改将丢失。',\n      saveTitle: '确认保存',\n      saveMessage: '您确定要保存这些更改吗？'\n    }\n  },\n\n  // 表单验证\n  validation: {\n    required: '此字段为必填项',\n    email: '请输入有效的邮箱地址',\n    phone: '请输入有效的电话号码',\n    minLength: '至少需要 {min} 个字符',\n    maxLength: '最多允许 {max} 个字符',\n    number: '请输入有效的数字',\n    positive: '请输入正数',\n    date: '请选择有效的日期',\n    time: '请选择有效的时间'\n  },\n\n  // 错误消息\n  errors: {\n    general: '发生了未知错误，请稍后重试',\n    network: '网络连接错误，请检查您的网络连接',\n    unauthorized: '您没有权限执行此操作',\n    notFound: '请求的资源未找到',\n    serverError: '服务器错误，请稍后重试',\n    validationError: '输入数据验证失败',\n    loadFailed: '加载数据失败',\n    saveFailed: '保存数据失败',\n    deleteFailed: '删除数据失败',\n    updateFailed: '更新数据失败',\n    createFailed: '创建数据失败'\n  },\n\n  // 成功消息\n  success: {\n    saved: '保存成功',\n    deleted: '删除成功',\n    updated: '更新成功',\n    created: '创建成功',\n    sent: '发送成功',\n    uploaded: '上传成功',\n    downloaded: '下载成功'\n  }\n} as const;\n\n// 翻译工具函数\nexport function t(key: string, params?: Record<string, string | number>): string {\n  const keys = key.split('.');\n  let value: any = translations;\n  \n  for (const k of keys) {\n    if (value && typeof value === 'object' && k in value) {\n      value = value[k];\n    } else {\n      console.warn(`Translation key not found: ${key}`);\n      return key; // 返回原始key作为fallback\n    }\n  }\n  \n  if (typeof value !== 'string') {\n    console.warn(`Translation value is not a string: ${key}`);\n    return key;\n  }\n  \n  // 处理参数替换\n  if (params) {\n    return value.replace(/\\{(\\w+)\\}/g, (match, paramKey) => {\n      return params[paramKey]?.toString() || match;\n    });\n  }\n  \n  return value;\n}\n\n// 类型定义\nexport type TranslationKey = keyof typeof translations;\n", "module.exports = require(\"path\");", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'shield', 'IconShield', [[\"path\",{\"d\":\"M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3\",\"key\":\"svg-0\"}]]);", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'user-check', 'IconUserCheck', [[\"path\",{\"d\":\"M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M6 21v-2a4 4 0 0 1 4 -4h4\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M15 19l2 2l4 -4\",\"key\":\"svg-2\"}]]);", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '@clerk/nextjs';\nimport { useRole, RoleGate } from '@/lib/role-context';\nimport { redirect } from 'next/navigation';\nimport PageContainer from '@/components/layout/page-container';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { IconUsers, IconShield, IconUserCheck } from '@tabler/icons-react';\nimport { createPayloadClient } from '@/lib/payload-client';\nimport { getRoleDisplayName, getRoleBadgeColor, UserRole } from '@/lib/role-utils';\nimport { toast } from 'sonner';\nimport { t } from '@/lib/translations';\ninterface PayloadUser {\n  id: string;\n  email: string;\n  role: UserRole;\n  clerkId: string;\n  firstName?: string;\n  lastName?: string;\n  createdAt: string;\n  updatedAt: string;\n}\nexport default function AdminPage() {\n  const {\n    userId,\n    isLoaded\n  } = useAuth();\n  const {\n    user: currentUser,\n    refreshUser\n  } = useRole();\n  const [users, setUsers] = useState<PayloadUser[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [updatingUserId, setUpdatingUserId] = useState<string | null>(null);\n\n  // Redirect if not authenticated\n  if (isLoaded && !userId) {\n    redirect('/auth/sign-in');\n  }\n\n  // Fetch users data\n  const fetchUsers = async () => {\n    if (!currentUser) return;\n    try {\n      setLoading(true);\n      const payloadClient = createPayloadClient(currentUser);\n      const response = (await payloadClient.getUsers({\n        limit: 100\n      })) as any;\n      setUsers(response.docs || []);\n      setError(null);\n    } catch (err) {\n      console.error('Failed to fetch users:', err);\n      setError('Failed to load users. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (currentUser && currentUser.role === 'admin') {\n      fetchUsers();\n    }\n  }, [currentUser]);\n  const handleRoleChange = async (userId: string, newRole: UserRole) => {\n    if (!currentUser) return;\n    try {\n      setUpdatingUserId(userId);\n      const payloadClient = createPayloadClient(currentUser);\n      await payloadClient.updateUser(userId, {\n        role: newRole\n      });\n\n      // Update local state\n      setUsers(users.map(user => user.id === userId ? {\n        ...user,\n        role: newRole\n      } : user));\n      toast.success('User role updated successfully');\n\n      // Refresh current user if they updated their own role\n      if (currentUser.payloadUserId === userId) {\n        await refreshUser();\n      }\n    } catch (error) {\n      console.error('Failed to update user role:', error);\n      toast.error('Failed to update user role');\n    } finally {\n      setUpdatingUserId(null);\n    }\n  };\n  if (!isLoaded || loading) {\n    return <PageContainer>\n        <div className='flex items-center justify-center h-64'>\n          <div className='text-center'>\n            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>\n            <p className='text-muted-foreground'>加载管理面板中...</p>\n          </div>\n        </div>\n      </PageContainer>;\n  }\n  return <RoleGate roles=\"admin\" fallback={<PageContainer>\n          <div className='flex items-center justify-center h-64'>\n            <div className='text-center'>\n              <IconShield className='size-12 mx-auto text-muted-foreground mb-4' />\n              <h3 className='text-lg font-semibold mb-2'>访问被拒绝</h3>\n              <p className='text-muted-foreground'>\n                您需要管理员权限才能访问此页面。\n              </p>\n            </div>\n          </div>\n        </PageContainer>} data-sentry-element=\"RoleGate\" data-sentry-component=\"AdminPage\" data-sentry-source-file=\"page.tsx\">\n      <PageContainer data-sentry-element=\"PageContainer\" data-sentry-source-file=\"page.tsx\">\n        <div className='flex flex-1 flex-col space-y-4'>\n          {/* Header */}\n          <div className='flex items-center justify-between'>\n            <div>\n              <h2 className='text-2xl font-bold tracking-tight flex items-center gap-2'>\n                <IconUsers className='size-6' data-sentry-element=\"IconUsers\" data-sentry-source-file=\"page.tsx\" />\n                {t('admin.title')}\n              </h2>\n              <p className='text-muted-foreground'>\n                {t('admin.subtitle')}\n              </p>\n            </div>\n          </div>\n\n          {/* Current User Info */}\n          <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\n            <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\n              <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">\n                <IconUserCheck className=\"size-5\" data-sentry-element=\"IconUserCheck\" data-sentry-source-file=\"page.tsx\" />\n                当前用户\n              </CardTitle>\n              <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"page.tsx\">\n                您当前的角色和权限\n              </CardDescription>\n            </CardHeader>\n            <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\n              <div className=\"flex items-center gap-4\">\n                <div>\n                  <p className=\"font-medium\">{currentUser?.email}</p>\n                  <p className=\"text-sm text-muted-foreground\">\n                    {currentUser?.firstName} {currentUser?.lastName}\n                  </p>\n                </div>\n                <Badge className={getRoleBadgeColor(currentUser?.role || 'front-desk')} data-sentry-element=\"Badge\" data-sentry-source-file=\"page.tsx\">\n                  {getRoleDisplayName(currentUser?.role || 'front-desk')}\n                </Badge>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Users List */}\n          {error ? <Card>\n              <CardContent className=\"pt-6\">\n                <div className='text-center py-8'>\n                  <p className='text-red-600 mb-4'>{error}</p>\n                  <Button onClick={fetchUsers}>Try Again</Button>\n                </div>\n              </CardContent>\n            </Card> : <Card>\n              <CardHeader>\n                <CardTitle>All Users ({users.length})</CardTitle>\n                <CardDescription>\n                  Manage roles for all system users\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {users.map(user => <div key={user.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-3\">\n                          <div>\n                            <p className=\"font-medium\">{user.email}</p>\n                            <p className=\"text-sm text-muted-foreground\">\n                              {user.firstName} {user.lastName}\n                            </p>\n                            <p className=\"text-xs text-muted-foreground\">\n                              Joined: {new Date(user.createdAt).toLocaleDateString()}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex items-center gap-3\">\n                        <Badge className={getRoleBadgeColor(user.role)}>\n                          {getRoleDisplayName(user.role)}\n                        </Badge>\n                        \n                        <Select value={user.role} onValueChange={(newRole: UserRole) => handleRoleChange(user.id, newRole)} disabled={updatingUserId === user.id}>\n                          <SelectTrigger className=\"w-32\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"admin\">Administrator</SelectItem>\n                            <SelectItem value=\"doctor\">Doctor</SelectItem>\n                            <SelectItem value=\"front-desk\">Front Desk</SelectItem>\n                          </SelectContent>\n                        </Select>\n                      </div>\n                    </div>)}\n                  \n                  {users.length === 0 && <div className=\"text-center py-8\">\n                      <IconUsers className=\"size-12 mx-auto text-muted-foreground mb-4\" />\n                      <p className=\"text-muted-foreground\">No users found</p>\n                    </div>}\n                </div>\n              </CardContent>\n            </Card>}\n        </div>\n      </PageContainer>\n    </RoleGate>;\n}", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\");\n", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "'use client';\n\nimport * as React from 'react';\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';\nimport { cn } from '@/lib/utils';\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return <ScrollAreaPrimitive.Root data-slot='scroll-area' className={cn('relative', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.Root\" data-sentry-component=\"ScrollArea\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.Viewport data-slot='scroll-area-viewport' className='focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1' data-sentry-element=\"ScrollAreaPrimitive.Viewport\" data-sentry-source-file=\"scroll-area.tsx\">\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar data-sentry-element=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n      <ScrollAreaPrimitive.Corner data-sentry-element=\"ScrollAreaPrimitive.Corner\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.Root>;\n}\nfunction ScrollBar({\n  className,\n  orientation = 'vertical',\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return <ScrollAreaPrimitive.ScrollAreaScrollbar data-slot='scroll-area-scrollbar' orientation={orientation} className={cn('flex touch-none p-px transition-colors select-none', orientation === 'vertical' && 'h-full w-2.5 border-l border-l-transparent', orientation === 'horizontal' && 'h-2.5 flex-col border-t border-t-transparent', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaScrollbar\" data-sentry-component=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.ScrollAreaThumb data-slot='scroll-area-thumb' className='bg-border relative flex-1 rounded-full' data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaThumb\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>;\n}\nexport { ScrollArea, ScrollBar };", "\nexport { deleteKeylessAction as \"7f7b45347fd50452ee6e2850ded1018991a7b086f0\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { syncKeylessConfigAction as \"7f909588461cb83e855875f4939d6f26e4ae81b49e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { createOrReadKeylessAction as \"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { invalidateCacheAction as \"7f22efd92a3b59d43d3d12fe480e87910640e1db9e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\server-actions.js\"\n", "import React from 'react';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nexport default function PageContainer({\n  children,\n  scrollable = true\n}: {\n  children: React.ReactNode;\n  scrollable?: boolean;\n}) {\n  return <>\r\n      {scrollable ? <ScrollArea className='h-[calc(100dvh-52px)]'>\r\n          <div className='flex flex-1 p-4 md:px-6'>{children}</div>\r\n        </ScrollArea> : <div className='flex flex-1 p-4 md:px-6'>{children}</div>}\r\n    </>;\n}", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "'use client';\n\nimport { useAuth } from '@clerk/clerk-react';\nimport { useDerivedAuth } from '@clerk/clerk-react/internal';\nimport type { InitialState } from '@clerk/types';\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nconst PromisifiedAuthContext = React.createContext<Promise<InitialState> | InitialState | null>(null);\n\nexport function PromisifiedAuthProvider({\n  authPromise,\n  children,\n}: {\n  authPromise: Promise<InitialState> | InitialState;\n  children: React.ReactNode;\n}) {\n  return <PromisifiedAuthContext.Provider value={authPromise}>{children}</PromisifiedAuthContext.Provider>;\n}\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data during SSR\n * simply by using the `ClerkProvider`.\n *\n * @example\n * import { useAuth } from '@clerk/nextjs'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * This page will be fully rendered during SSR.\n *\n * ```tsx\n * import { useAuth } from '@clerk/nextjs'\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n * ```\n */\nexport function usePromisifiedAuth() {\n  const isPagesRouter = useRouter();\n  const valueFromContext = React.useContext(PromisifiedAuthContext);\n\n  let resolvedData = valueFromContext;\n  if (valueFromContext && 'then' in valueFromContext) {\n    resolvedData = React.use(valueFromContext);\n  }\n\n  // At this point we should have a usable auth object\n\n  if (typeof window === 'undefined') {\n    // Pages router should always use useAuth as it is able to grab initial auth state from context during SSR.\n    if (isPagesRouter) {\n      return useAuth();\n    }\n\n    // We don't need to deal with Clerk being loaded here\n    return useDerivedAuth(resolvedData);\n  } else {\n    return useAuth(resolvedData);\n  }\n}\n", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/admin/page\",\n        pathname: \"/dashboard/admin\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "// Payload CMS client with authentication\nimport { AuthenticatedUser } from './auth-middleware';\n\nexport interface PayloadRequestOptions {\n  method?: 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE';\n  body?: any;\n  params?: Record<string, string | number>;\n}\n\n/**\n * Enhanced Payload CMS client with proper authentication\n * Routes through frontend API routes which proxy to backend\n */\nexport class PayloadClient {\n  private user: AuthenticatedUser;\n\n  constructor(user: AuthenticatedUser) {\n    this.user = user;\n  }\n\n  /**\n   * Make authenticated request to backend API\n   * When called from server-side (API routes), goes directly to backend\n   * When called from client-side, goes through frontend API routes\n   */\n  private async makeRequest<T>(\n    endpoint: string,\n    options: PayloadRequestOptions = {}\n  ): Promise<T> {\n    const { method = 'GET', body, params } = options;\n\n    // Determine if we're running on server or client\n    const isServer = typeof window === 'undefined';\n\n    let url: string;\n    let requestOptions: RequestInit;\n\n    if (isServer) {\n      // Server-side: make direct request to backend\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n      url = `${backendUrl}/api${endpoint}`;\n\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        if (searchParams.toString()) {\n          url += `?${searchParams.toString()}`;\n        }\n      }\n\n      // Include Clerk user headers for backend authentication\n      requestOptions = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'X-Clerk-User-Id': this.user.clerkId,\n          'X-User-Email': this.user.email,\n        },\n      };\n    } else {\n      // Client-side: use frontend API routes\n      url = `/api${endpoint}`;\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        if (searchParams.toString()) {\n          url += `?${searchParams.toString()}`;\n        }\n      }\n\n      requestOptions = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      };\n    }\n\n    if (body && method !== 'GET') {\n      requestOptions.body = JSON.stringify(body);\n    }\n\n    try {\n      const response = await fetch(url, requestOptions);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(\n          `API request failed: ${response.status} ${response.statusText} - ${errorText}`\n        );\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Appointments API methods\n   */\n  async getAppointments(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n  }) {\n    return this.makeRequest('/appointments', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getAppointment(id: string) {\n    return this.makeRequest(`/appointments/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createAppointment(data: any) {\n    return this.makeRequest('/appointments', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updateAppointment(id: string, data: any) {\n    return this.makeRequest(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deleteAppointment(id: string) {\n    return this.makeRequest(`/appointments/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patients API methods\n   */\n  async getPatients(params?: {\n    limit?: number;\n    page?: number;\n    search?: string;\n  }) {\n    const queryParams: any = { depth: '1', ...params };\n    \n    // Add search functionality\n    if (params?.search) {\n      queryParams['where[or][0][fullName][contains]'] = params.search;\n      queryParams['where[or][1][phone][contains]'] = params.search;\n      queryParams['where[or][2][email][contains]'] = params.search;\n      delete queryParams.search; // Remove search from params\n    }\n\n    return this.makeRequest('/patients', { params: queryParams });\n  }\n\n  async getPatient(id: string) {\n    return this.makeRequest(`/patients/${id}`, {\n      params: { depth: '1' },\n    });\n  }\n\n  async createPatient(data: any) {\n    return this.makeRequest('/patients', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatient(id: string, data: any) {\n    return this.makeRequest(`/patients/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatient(id: string) {\n    return this.makeRequest(`/patients/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Treatments API methods\n   */\n  async getTreatments(params?: {\n    limit?: number;\n    page?: number;\n  }) {\n    return this.makeRequest('/treatments', {\n      params: { depth: '1', ...params },\n    });\n  }\n\n  async getTreatment(id: string) {\n    return this.makeRequest(`/treatments/${id}`);\n  }\n\n  async createTreatment(data: any) {\n    return this.makeRequest('/treatments', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updateTreatment(id: string, data: any) {\n    return this.makeRequest(`/treatments/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deleteTreatment(id: string) {\n    return this.makeRequest(`/treatments/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Users API methods (for user management)\n   */\n  async getUsers(params?: {\n    limit?: number;\n    page?: number;\n  }) {\n    return this.makeRequest('/users', {\n      params: { depth: '1', ...params },\n    });\n  }\n\n  async updateUser(userId: string, data: any) {\n    return this.makeRequest(`/users/${userId}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async syncCurrentUser() {\n    // This would sync the current Clerk user with Payload CMS\n    return this.makeRequest('/users/sync', {\n      method: 'POST',\n      body: {\n        clerkId: this.user.clerkId,\n        email: this.user.email,\n        firstName: this.user.firstName,\n        lastName: this.user.lastName,\n      },\n    });\n  }\n\n  async syncUser(userData: {\n    clerkId: string;\n    email: string;\n    firstName?: string;\n    lastName?: string;\n  }) {\n    // Sync a specific user with Payload CMS and return user with role\n    try {\n      // First, try to find existing user\n      const existingUsers = await this.makeRequest<any>('/users', {\n        params: {\n          where: JSON.stringify({\n            clerkId: { equals: userData.clerkId }\n          }),\n          limit: 1,\n        },\n      });\n\n      if (existingUsers.docs && existingUsers.docs.length > 0) {\n        // Update existing user\n        const existingUser = existingUsers.docs[0];\n        const updatedUser = await this.makeRequest<any>(`/users/${existingUser.id}`, {\n          method: 'PATCH',\n          body: {\n            email: userData.email,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            lastLogin: new Date().toISOString(),\n          },\n        });\n        return updatedUser;\n      } else {\n        // Create new user with default role\n        const newUser = await this.makeRequest<any>('/users', {\n          method: 'POST',\n          body: {\n            email: userData.email,\n            clerkId: userData.clerkId,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            role: 'front-desk', // Default role for new users\n            lastLogin: new Date().toISOString(),\n          },\n        });\n        return newUser;\n      }\n    } catch (error) {\n      console.error('Error syncing user with Payload:', error);\n      // Return a default user object if sync fails\n      return {\n        id: 'temp-id',\n        email: userData.email,\n        clerkId: userData.clerkId,\n        role: 'front-desk',\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n      };\n    }\n  }\n\n  /**\n   * Patient Interactions API methods\n   */\n  async getPatientInteractions(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n    sort?: string;\n  }) {\n    return this.makeRequest('/patient-interactions', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getPatientInteraction(id: string) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createPatientInteraction(data: any) {\n    return this.makeRequest('/patient-interactions', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatientInteraction(id: string, data: any) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatientInteraction(id: string) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patient Tasks API methods\n   */\n  async getPatientTasks(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n    sort?: string;\n  }) {\n    return this.makeRequest('/patient-tasks', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTask(id: string) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createPatientTask(data: any) {\n    return this.makeRequest('/patient-tasks', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatientTask(id: string, data: any) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatientTask(id: string) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patient-specific CRM methods\n   */\n  async getPatientInteractionsByPatient(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    interactionType?: string;\n    status?: string;\n    priority?: string;\n  }) {\n    return this.makeRequest(`/patients/${patientId}/interactions`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTasksByPatient(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    taskType?: string;\n    status?: string;\n    priority?: string;\n    assignedTo?: string;\n  }) {\n    return this.makeRequest(`/patients/${patientId}/tasks`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTimeline(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    type?: 'interaction' | 'task';\n  }) {\n    return this.makeRequest(`/patients/${patientId}/timeline`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n}\n\n/**\n * Factory function to create PayloadClient instance\n */\nexport function createPayloadClient(user: AuthenticatedUser): PayloadClient {\n  return new PayloadClient(user);\n}\n"], "names": ["badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "className", "<PERSON><PERSON><PERSON><PERSON>", "props", "Comp", "Slot", "data-slot", "cn", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "serverComponentModule.default", "Card", "div", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "CardAction", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "translations", "nav", "dashboard", "appointments", "patients", "treatments", "admin", "account", "profile", "login", "overview", "title", "subtitle", "metrics", "todayAppointments", "recentPatients", "totalPatients", "activetreatments", "scheduledForToday", "appointmentsScheduledForToday", "newPatientsThisWeek", "patientsRegisteredInLast7Days", "totalRegisteredPatients", "completePatientDatabase", "treatmentOptionsAvailable", "fullServiceCatalog", "active", "last7Days", "allTime", "available", "errors", "loadingDashboard", "failedToLoadMetrics", "newAppointment", "editAppointment", "appointmentDetails", "appointmentsCount", "loadingAppointments", "noAppointments", "filters", "all", "today", "thisWeek", "thisMonth", "status", "date<PERSON><PERSON><PERSON>", "scheduled", "confirmed", "inProgress", "completed", "cancelled", "noShow", "form", "patient", "selectPatient", "treatment", "selectTreatment", "date", "time", "notes", "notesPlaceholder", "newPatient", "editPatient", "patientDetails", "patientsCount", "loadingPatients", "noPatients", "searchPlaceholder", "fullName", "fullNamePlaceholder", "phone", "phonePlaceholder", "email", "emailPlaceholder", "medicalNotes", "medicalNotesPlaceholder", "newTreatment", "editTreatment", "treatmentDetails", "treatmentsCount", "loadingTreatments", "noTreatments", "name", "namePlaceholder", "description", "descriptionPlaceholder", "duration", "durationPlaceholder", "price", "pricePlaceholder", "userManagement", "roleManagement", "systemSettings", "users", "roles", "doctor", "frontDesk", "common", "actions", "save", "cancel", "edit", "delete", "view", "search", "filter", "reset", "submit", "close", "confirm", "back", "next", "previous", "add", "remove", "update", "create", "loading", "success", "error", "warning", "info", "pending", "inactive", "enabled", "disabled", "yesterday", "tomorrow", "lastWeek", "nextWeek", "lastM<PERSON>h", "nextMonth", "thisYear", "lastYear", "nextYear", "confirmDialog", "deleteTitle", "deleteMessage", "cancelTitle", "cancelMessage", "saveTitle", "saveMessage", "validation", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "number", "positive", "general", "network", "unauthorized", "notFound", "serverError", "validationError", "loadFailed", "saveFailed", "deleteFailed", "updateFailed", "createFailed", "saved", "deleted", "updated", "created", "sent", "uploaded", "downloaded", "t", "key", "params", "keys", "split", "value", "k", "console", "warn", "replace", "match", "<PERSON><PERSON><PERSON><PERSON>", "toString", "AdminPage", "userId", "isLoaded", "useAuth", "user", "currentUser", "refreshUser", "useRole", "setUsers", "useState", "setLoading", "setError", "updatingUserId", "setUpdatingUserId", "redirect", "fetchUsers", "payloadClient", "createPayloadClient", "response", "getUsers", "limit", "docs", "err", "handleRoleChange", "newRole", "updateUser", "role", "map", "id", "toast", "payloadUserId", "<PERSON><PERSON><PERSON><PERSON>", "p", "RoleGate", "fallback", "IconShield", "h3", "h2", "IconUsers", "IconUserCheck", "firstName", "lastName", "getRoleBadgeColor", "getRoleDisplayName", "<PERSON><PERSON>", "onClick", "length", "Date", "createdAt", "toLocaleDateString", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "ScrollArea", "children", "ScrollAreaPrimitive", "<PERSON><PERSON>Bar", "orientation", "scrollable", "PayloadClient", "constructor", "makeRequest", "endpoint", "options", "url", "requestOptions", "method", "body", "backendUrl", "searchParams", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "append", "undefined", "headers", "clerkId", "JSON", "stringify", "fetch", "ok", "errorText", "text", "statusText", "json", "getAppointments", "depth", "getAppointment", "createAppointment", "data", "updateAppointment", "deleteAppointment", "getPatients", "queryParams", "getPatient", "createPatient", "updatePatient", "deletePatient", "getTreatments", "getTreatment", "createTreatment", "updateTreatment", "deleteTreatment", "syncCurrentUser", "syncUser", "userData", "existingUsers", "where", "equals", "lastLogin", "toISOString", "existingUser", "getPatientInteractions", "getPatientInteraction", "createPatientInteraction", "updatePatientInteraction", "deletePatientInteraction", "getPatientTasks", "getPatientTask", "createPatientTask", "updatePatientTask", "deletePatientTask", "getPatientInteractionsByPatient", "patientId", "getPatientTasksByPatient", "getPatientTimeline"], "sourceRoot": ""}