(()=>{var e={};e.id=4942,e.ids=[4942],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1317:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{w:()=>a,x:()=>c});var o=r(10724),i=r(68117),u=r(32481),n=e([i]);async function a(e){try{let t=e.headers.get("x-clerk-user-id"),r=e.headers.get("x-user-email");if(!t||!r)return console.log("Missing Clerk user headers"),null;let s=await (0,o.nm0)({config:i.A}),n={id:t,email:r};console.log("Syncing Clerk user with Payload:",n);let a=await (0,u.kw)(s,n);return console.log("Synced Payload user:",a),{payload:s,user:a}}catch(e){return console.error("Payload authentication error:",e),null}}async function c(e,t,r,s={}){let{payload:o,user:i}=e;switch(r){case"find":return await o.find({collection:t,user:i,...s});case"findByID":return await o.findByID({collection:t,user:i,...s});case"create":return await o.create({collection:t,user:i,...s});case"update":return await o.update({collection:t,user:i,...s});case"delete":return await o.delete({collection:t,user:i,...s});default:throw Error(`Unsupported operation: ${r}`)}}i=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},32481:(e,t,r)=>{"use strict";async function s(e,t,r="front-desk"){try{let s=await e.find({collection:"users",where:{clerkId:{equals:t.id}},limit:1});if(!(s.docs.length>0))return await e.create({collection:"users",data:{role:r,clerkId:t.id}});{let t=s.docs[0];return await e.update({collection:"users",id:t.id,data:{}})}}catch(e){throw console.error("Error syncing Clerk user with Payload:",e),Error("Failed to sync user authentication")}}r.d(t,{kw:()=>s})},32785:e=>{"use strict";e.exports=import("prettier")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},34869:()=>{},35672:e=>{"use strict";e.exports=require("dns/promises")},36060:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var o=r(70293),i=r(32498),u=r(83889),n=r(79054),a=e([n]);n=(a.then?(await a)():a)[0];let p=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/bills/bulk-operations/route",pathname:"/api/bills/bulk-operations",filename:"route",bundlePath:"app/api/bills/bulk-operations/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\bulk-operations\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=p;function c(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},37830:e=>{"use strict";e.exports=require("node:stream/web")},38522:e=>{"use strict";e.exports=require("node:zlib")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79054:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{POST:()=>n});var o=r(27492),i=r(1317),u=e([i]);async function n(e){try{let t=await (0,i.w)(e);if(!t)return o.NextResponse.json({error:"Authentication required"},{status:401});if(!["admin","front-desk"].includes(t.user.role))return o.NextResponse.json({error:"Insufficient permissions for bulk operations"},{status:403});let{operation:r,billIds:s,data:u}=await e.json();if(!r||!s||!Array.isArray(s)||0===s.length)return o.NextResponse.json({error:"Operation type and bill IDs are required"},{status:400});let n={successful:[],failed:[],summary:{total:s.length,success:0,failed:0}};switch(r){case"updateStatus":if(!u.status)return o.NextResponse.json({error:"Status is required for status update operation"},{status:400});for(let e of s)try{let r=await (0,i.x)(t,"bills","update",{id:e,data:{status:u.status,notes:u.notes?`${u.notes} (Bulk update by user ${t.user.id})`:void 0}});n.successful.push({billId:e,bill:r}),n.summary.success++}catch(t){n.failed.push({billId:e,error:t.message||"Unknown error"}),n.summary.failed++}break;case"applyDiscount":if(!u.discountAmount&&!u.discountRate)return o.NextResponse.json({error:"Discount amount or rate is required"},{status:400});for(let e of s)try{let r=await (0,i.x)(t,"bills","findByID",{id:e});if(!r){n.failed.push({billId:e,error:"Bill not found"}),n.summary.failed++;continue}let s=r.discountAmount||0;u.discountAmount?s=u.discountAmount:u.discountRate&&(s=(r.subtotal||0)*(u.discountRate/100));let o=(r.subtotal||0)-s+(r.taxAmount||0),a=o-(r.paidAmount||0),c=await (0,i.x)(t,"bills","update",{id:e,data:{discountAmount:s,totalAmount:o,remainingAmount:a,notes:`${r.notes||""}
折扣应用: ${u.discountAmount?`$${u.discountAmount}`:`${u.discountRate}%`} (User ${t.user.id})`.trim()}});n.successful.push({billId:e,bill:c}),n.summary.success++}catch(t){n.failed.push({billId:e,error:t.message||"Unknown error"}),n.summary.failed++}break;case"sendReminders":for(let e of s)try{let r=await (0,i.x)(t,"bills","findByID",{id:e,depth:2});if(!r){n.failed.push({billId:e,error:"Bill not found"}),n.summary.failed++;continue}console.log(`Reminder sent for bill ${r.billNumber} to patient ${r.patient?.fullName||"Unknown"}`);let s=await (0,i.x)(t,"bills","update",{id:e,data:{notes:`${r.notes||""}
提醒已发送: ${new Date().toISOString()} (User ${t.user.id})`.trim()}});n.successful.push({billId:e,bill:s,reminderSent:!0,patientEmail:r.patient.email,patientPhone:r.patient.phone}),n.summary.success++}catch(t){n.failed.push({billId:e,error:t.message||"Unknown error"}),n.summary.failed++}break;case"generateReports":try{let e=(await Promise.all(s.map(e=>(0,i.x)(t,"bills","findByID",{id:e,depth:2})))).filter(Boolean).map(e=>e),r={totalBills:e.length,totalAmount:e.reduce((e,t)=>e+(t.totalAmount||0),0),totalPaid:e.reduce((e,t)=>e+(t.paidAmount||0),0),totalOutstanding:e.reduce((e,t)=>e+(t.remainingAmount||0),0),statusBreakdown:{},typeBreakdown:{},patientBreakdown:{}};e.forEach(e=>{let t=e.status||"unknown";r.statusBreakdown[t]||(r.statusBreakdown[t]={count:0,amount:0}),r.statusBreakdown[t].count++,r.statusBreakdown[t].amount+=e.totalAmount;let s=e.billType||"unknown";r.typeBreakdown[s]||(r.typeBreakdown[s]={count:0,amount:0}),r.typeBreakdown[s].count++,r.typeBreakdown[s].amount+=e.totalAmount;let o=e.patient?.fullName||"Unknown";r.patientBreakdown[o]||(r.patientBreakdown[o]={count:0,amount:0,outstanding:0}),r.patientBreakdown[o].count++,r.patientBreakdown[o].amount+=e.totalAmount,r.patientBreakdown[o].outstanding+=e.remainingAmount||0}),n.successful.push({operation:"generateReports",report:r}),n.summary.success=1}catch(e){n.failed.push({operation:"generateReports",error:e.message||"Unknown error"}),n.summary.failed=1}break;default:return o.NextResponse.json({error:`Unsupported operation: ${r}`},{status:400})}return o.NextResponse.json({success:!0,operation:r,results:n,message:`Bulk operation completed. ${n.summary.success} successful, ${n.summary.failed} failed.`})}catch(e){return console.error("Error in bulk operations:",e),o.NextResponse.json({error:"Failed to perform bulk operation"},{status:500})}}i=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},93077:()=>{},94735:e=>{"use strict";e.exports=require("events")},98995:e=>{"use strict";e.exports=require("node:module")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[3889,2481,9556,8754],()=>r(36060));module.exports=s})();