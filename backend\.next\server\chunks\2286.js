"use strict";exports.id=2286,exports.ids=[2286],exports.modules={2286:(e,l,a)=>{a.r(l),a.d(l,{UploadComponent:()=>f});var t=a(39321),s=a(5331),o=a(42148),i=a(90420),d=a(79097),r=a(1062),n=a(16826),u=a(43232),c=a(28709),p=a(93779),m=a(47186),b=a(26620),h="lexical-upload",g={depth:0},x=e=>{let{data:{fields:l,relationTo:a,value:x},nodeKey:f}=e;if("object"==typeof x)throw Error("Upload value should be a string or number. The Lexical Upload component should not receive the populated value object.");let{config:{routes:{api:_},serverURL:j},getEntityConfig:v}=(0,u.b)(),w=(0,b.useRef)(null),{uuid:$}=(0,i.b)(),D=(0,c.useEditDepth)(),[N]=(0,r.DF)(),{editorConfig:C,fieldProps:{readOnly:S,schemaPath:y}}=(0,i.b)(),{i18n:P,t:R}=(0,p.d)(),[U,k]=(0,b.useReducer)(e=>e+1,0),[E]=(0,b.useState)(()=>v({collectionSlug:a})),B=(0,b.useId)(),F=(0,c.formatDrawerSlug)({slug:"lexical-upload-drawer-"+$+B,depth:D}),{toggleDrawer:L}=(0,s.a)(F,!0),{closeDocumentDrawer:T,DocumentDrawer:K,DocumentDrawerToggler:I}=(0,o.a)({id:x,collectionSlug:E.slug}),[{data:z},{setParams:A}]=(0,c.usePayloadAPI)(`${j}${_}/${E.slug}/${x}`,{initialParams:g}),M=z?.thumbnailURL||z?.url,O=(0,b.useCallback)(()=>{N.update(()=>{(0,m.nsf)(f)?.remove()})},[N,f]),q=(0,b.useCallback)(e=>{A({...g,cacheBust:U}),k(),T()},[A,U,T]),G=C?.resolvedFeatureMap?.get("upload")?.sanitizedClientFeatureProps.collections?.[E.slug]?.hasExtraFields,H=(0,b.useCallback)((e,l)=>{N.update(()=>{let e=(0,m.nsf)(f);if(e){let a={...e.getData(),fields:l};e.setData(a)}})},[N,f]);return(0,d.jsxs)("div",{className:h,contentEditable:!1,ref:w,children:[(0,d.jsxs)("div",{className:`${h}__card`,children:[(0,d.jsxs)("div",{className:`${h}__topRow`,children:[(0,d.jsx)("div",{className:`${h}__thumbnail`,children:M?(0,d.jsx)("img",{alt:z?.filename,"data-lexical-upload-id":x,"data-lexical-upload-relation-to":a,src:M}):(0,d.jsx)(c.File,{})}),(0,d.jsxs)("div",{className:`${h}__topRowRightPanel`,children:[(0,d.jsx)("div",{className:`${h}__collectionLabel`,children:(0,n.s)(E.labels.singular,P)}),N.isEditable()&&(0,d.jsxs)("div",{className:`${h}__actions`,children:[G?(0,d.jsx)(c.Button,{buttonStyle:"icon-label",className:`${h}__upload-drawer-toggler`,disabled:S,el:"button",icon:"edit",onClick:()=>{L()},round:!0,tooltip:R("fields:editRelationship")}):null,(0,d.jsx)(c.Button,{buttonStyle:"icon-label",className:`${h}__swap-drawer-toggler`,disabled:S,el:"button",icon:"swap",onClick:()=>{N.dispatchCommand(t.c,{replace:{nodeKey:f}})},round:!0,tooltip:R("fields:swapUpload")}),(0,d.jsx)(c.Button,{buttonStyle:"icon-label",className:`${h}__removeButton`,disabled:S,icon:"x",onClick:e=>{e.preventDefault(),O()},round:!0,tooltip:R("fields:removeUpload")})]})]})]}),(0,d.jsx)("div",{className:`${h}__bottomRow`,children:(0,d.jsx)(I,{className:`${h}__doc-drawer-toggler`,children:(0,d.jsx)("strong",{children:z?.filename})})})]}),x?(0,d.jsx)(K,{onSave:q}):null,G?(0,d.jsx)(t.a,{data:l,drawerSlug:F,drawerTitle:R("general:editLabel",{label:(0,n.s)(E.labels.singular,P)}),featureKey:"upload",handleDrawerSubmit:H,schemaPath:y,schemaPathSuffix:E.slug}):null]})},f=e=>(0,d.jsx)(t.b,{...e,uploads:!0,children:(0,d.jsx)(x,{...e})})}};