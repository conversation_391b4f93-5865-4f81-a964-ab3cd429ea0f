try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="506e5d2d-747e-459a-a003-51c4b6e993f9",e._sentryDebugIdIdentifier="sentry-dbid-506e5d2d-747e-459a-a003-51c4b6e993f9")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2718],{20938:(e,t,r)=>{Promise.resolve().then(r.bind(r,96113))},36962:(e,t,r)=>{"use strict";r.d(t,{c:()=>a});var n=r(99004);function a(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},38774:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,q:()=>o});var n=r(99004),a=r(52880);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,l=n.useMemo(()=>o,Object.values(o));return(0,a.jsx)(r.Provider,{value:l,children:t})};return o.displayName=e+"Provider",[o,function(a){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function l(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let a=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:a}}),[r,a])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),i=r.length;r=[...r,o];let u=t=>{let{scope:r,children:o,...u}=t,s=r?.[e]?.[i]||l,c=n.useMemo(()=>u,Object.values(u));return(0,a.jsx)(s.Provider,{value:c,children:o})};return u.displayName=t+"Provider",[u,function(r,a){let u=a?.[e]?.[i]||l,s=n.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=r.reduce((t,{useScope:r,scopeName:n})=>{let a=r(e)[`__scope${n}`];return{...t,...a}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return r.scopeName=t.scopeName,r}(o,...t)]}},39552:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,t:()=>o});var n=r(99004);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function l(...e){return n.useCallback(o(...e),e)}},50516:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,xV:()=>u});var n=r(99004),a=r(39552),o=r(52880),l=n.forwardRef((e,t)=>{let{children:r,...a}=e,l=n.Children.toArray(r),u=l.find(s);if(u){let e=u.props.children,r=l.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(i,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,o.jsx)(i,{...a,ref:t,children:r})});l.displayName="Slot";var i=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),l=function(e,t){let r={...t};for(let n in t){let a=e[n],o=t[n];/^on[A-Z]/.test(n)?a&&o?r[n]=(...e)=>{o(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...o}:"className"===n&&(r[n]=[a,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(l.ref=t?(0,a.t)(t,e):e),n.cloneElement(r,l)}return n.Children.count(r)>1?n.Children.only(null):null});i.displayName="SlotClone";var u=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function s(e){return n.isValidElement(e)&&e.type===u}},51452:(e,t,r)=>{"use strict";r.d(t,{hO:()=>u,sG:()=>i});var n=r(99004),a=r(32909),o=r(50516),l=r(52880),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...a}=e,i=n?o.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(i,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function u(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},54651:(e,t,r)=>{"use strict";r.d(t,{Jv:()=>s,cn:()=>o,fw:()=>u,r6:()=>i,z3:()=>l});var n=r(97921),a=r(56309);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function l(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:o="normal"}=n;if(0===e)return"0 Byte";let l=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,l)).toFixed(a)," ").concat("accurate"===o?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][l])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][l])?r:"Bytes")}function i(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function u(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let r=Math.floor(t/60);if(r<60)return"".concat(r,"分钟前");let n=Math.floor(r/60);if(n<24)return"".concat(n,"小时前");let a=Math.floor(n/24);if(a<7)return"".concat(a,"天前");let o=Math.floor(a/7);if(o<4)return"".concat(o,"周前");let l=Math.floor(a/30);if(l<12)return"".concat(l,"个月前");let i=Math.floor(a/365);return"".concat(i,"年前")}function s(e){return("string"==typeof e?new Date(e):e)<new Date}},55955:(e,t,r)=>{"use strict";r.d(t,{H4:()=>x,_V:()=>b,bL:()=>w});var n=r(99004),a=r(38774),o=r(36962),l=r(88072),i=r(51452),u=r(52880),s="Avatar",[c,f]=(0,a.A)(s),[d,m]=c(s),p=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...a}=e,[o,l]=n.useState("idle");return(0,u.jsx)(d,{scope:r,imageLoadingStatus:o,onImageLoadingStatusChange:l,children:(0,u.jsx)(i.sG.span,{...a,ref:t})})});p.displayName=s;var v="AvatarImage",y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:a,onLoadingStatusChange:s=()=>{},...c}=e,f=m(v,r),d=function(e,t){let[r,a]=n.useState("idle");return(0,l.N)(()=>{if(!e)return void a("error");let r=!0,n=new window.Image,o=e=>()=>{r&&a(e)};return a("loading"),n.onload=o("loaded"),n.onerror=o("error"),n.src=e,t&&(n.referrerPolicy=t),()=>{r=!1}},[e,t]),r}(a,c.referrerPolicy),p=(0,o.c)(e=>{s(e),f.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==d&&p(d)},[d,p]),"loaded"===d?(0,u.jsx)(i.sG.img,{...c,ref:t,src:a}):null});y.displayName=v;var g="AvatarFallback",h=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:a,...o}=e,l=m(g,r),[s,c]=n.useState(void 0===a);return n.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>c(!0),a);return()=>window.clearTimeout(e)}},[a]),s&&"loaded"!==l.imageLoadingStatus?(0,u.jsx)(i.sG.span,{...o,ref:t}):null});h.displayName=g;var w=p,b=y,x=h},88072:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var n=r(99004),a=globalThis?.document?n.useLayoutEffect:()=>{}},96113:(e,t,r)=>{"use strict";r.d(t,{Avatar:()=>l,AvatarFallback:()=>u,AvatarImage:()=>i});var n=r(52880);r(99004);var a=r(55955),o=r(54651);function l(e){let{className:t,...r}=e;return(0,n.jsx)(a.bL,{"data-slot":"avatar",className:(0,o.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...r,"data-sentry-element":"AvatarPrimitive.Root","data-sentry-component":"Avatar","data-sentry-source-file":"avatar.tsx"})}function i(e){let{className:t,...r}=e;return(0,n.jsx)(a._V,{"data-slot":"avatar-image",className:(0,o.cn)("aspect-square size-full",t),...r,"data-sentry-element":"AvatarPrimitive.Image","data-sentry-component":"AvatarImage","data-sentry-source-file":"avatar.tsx"})}function u(e){let{className:t,...r}=e;return(0,n.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,o.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...r,"data-sentry-element":"AvatarPrimitive.Fallback","data-sentry-component":"AvatarFallback","data-sentry-source-file":"avatar.tsx"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,9442,4579,9253,7358],()=>t(20938)),_N_E=e.O()}]);