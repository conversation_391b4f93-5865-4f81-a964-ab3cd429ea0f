(()=>{var e={};e.id=4058,e.ids=[4058],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16230:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>u,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var a=t(70293),n=t(32498),o=t(83889),i=t(33744),p=e([i]);i=(p.then?(await p)():p)[0];let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/reset-db/route",pathname:"/api/reset-db",filename:"route",bundlePath:"app/api/reset-db/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reset-db\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=c;function u(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33744:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{POST:()=>i});var a=t(27492),n=t(64939),o=e([n]);async function i(){try{let e=new n.Pool({connectionString:process.env.DATABASE_URI}),r=await e.connect(),t=(await r.query(`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public'
    `)).rows.map(e=>e.tablename);for(let e of t)await r.query(`DROP TABLE IF EXISTS "${e}" CASCADE`);return r.release(),await e.end(),a.NextResponse.json({message:"Database reset successfully",dropped_tables:t})}catch(e){return console.error("Database reset error:",e),a.NextResponse.json({message:"Database reset failed",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}n=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})},34869:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},93077:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[3889,9556],()=>t(16230));module.exports=s})();