"use strict";exports.id=1955,exports.ids=[1955],exports.modules={61955:(e,t,a)=>{a.r(t),a.d(t,{bg:()=>y,default:()=>w});let r={lessThanXSeconds:{one:"по-малко от секунда",other:"по-малко от {{count}} секунди"},xSeconds:{one:"1 секунда",other:"{{count}} секунди"},halfAMinute:"половин минута",lessThanXMinutes:{one:"по-малко от минута",other:"по-малко от {{count}} минути"},xMinutes:{one:"1 минута",other:"{{count}} минути"},aboutXHours:{one:"около час",other:"около {{count}} часа"},xHours:{one:"1 час",other:"{{count}} часа"},xDays:{one:"1 ден",other:"{{count}} дни"},aboutXWeeks:{one:"около седмица",other:"около {{count}} седмици"},xWeeks:{one:"1 седмица",other:"{{count}} седмици"},aboutXMonths:{one:"около месец",other:"около {{count}} месеца"},xMonths:{one:"1 месец",other:"{{count}} месеца"},aboutXYears:{one:"около година",other:"около {{count}} години"},xYears:{one:"1 година",other:"{{count}} години"},overXYears:{one:"над година",other:"над {{count}} години"},almostXYears:{one:"почти година",other:"почти {{count}} години"}};var i=a(36478);let n={date:(0,i.k)({formats:{full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd.MM.yyyy"},defaultWidth:"full"}),time:(0,i.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,i.k)({formats:{any:"{{date}} {{time}}"},defaultWidth:"any"})};var o=a(88105),s=a(36678);let d=["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"];function u(e){let t=d[e];return 2===e?"'във "+t+" в' p":"'в "+t+" в' p"}let l={lastWeek:(e,t,a)=>{let r=(0,s.a)(e),i=r.getDay();return(0,o.R)(r,t,a)?u(i):function(e){let t=d[e];switch(e){case 0:case 3:case 6:return"'миналата "+t+" в' p";case 1:case 2:case 4:case 5:return"'миналия "+t+" в' p"}}(i)},yesterday:"'вчера в' p",today:"'днес в' p",tomorrow:"'утре в' p",nextWeek:(e,t,a)=>{let r=(0,s.a)(e),i=r.getDay();return(0,o.R)(r,t,a)?u(i):function(e){let t=d[e];switch(e){case 0:case 3:case 6:return"'следващата "+t+" в' p";case 1:case 2:case 4:case 5:return"'следващия "+t+" в' p"}}(i)},other:"P"};var c=a(4596);function h(e,t,a,r,i){return e+"-"+("quarter"===t?i:"year"===t||"week"===t||"minute"===t||"second"===t?r:a)}let f={ordinalNumber:(e,t)=>{let a=Number(e),r=t?.unit;if(0===a)return h(0,r,"ев","ева","ево");if(a%1e3==0)return h(a,r,"ен","на","но");if(a%100==0)return h(a,r,"тен","тна","тно");let i=a%100;if(i>20||i<10)switch(i%10){case 1:return h(a,r,"ви","ва","во");case 2:return h(a,r,"ри","ра","ро");case 7:case 8:return h(a,r,"ми","ма","мо")}return h(a,r,"ти","та","то")},era:(0,c.o)({values:{narrow:["пр.н.е.","н.е."],abbreviated:["преди н. е.","н. е."],wide:["преди новата ера","новата ера"]},defaultWidth:"wide"}),quarter:(0,c.o)({values:{narrow:["1","2","3","4"],abbreviated:["1-во тримес.","2-ро тримес.","3-то тримес.","4-то тримес."],wide:["1-во тримесечие","2-ро тримесечие","3-то тримесечие","4-то тримесечие"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,c.o)({values:{abbreviated:["яну","фев","мар","апр","май","юни","юли","авг","сеп","окт","ное","дек"],wide:["януари","февруари","март","април","май","юни","юли","август","септември","октомври","ноември","декември"]},defaultWidth:"wide"}),day:(0,c.o)({values:{narrow:["Н","П","В","С","Ч","П","С"],short:["нд","пн","вт","ср","чт","пт","сб"],abbreviated:["нед","пон","вто","сря","чет","пет","съб"],wide:["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"]},defaultWidth:"wide"}),dayPeriod:(0,c.o)({values:{wide:{am:"преди обяд",pm:"след обяд",midnight:"в полунощ",noon:"на обяд",morning:"сутринта",afternoon:"следобед",evening:"вечерта",night:"през нощта"}},defaultWidth:"wide"})};var m=a(19026);let y={code:"bg",formatDistance:(e,t,a)=>{let i,n=r[e];if(i="string"==typeof n?n:1===t?n.one:n.other.replace("{{count}}",String(t)),a?.addSuffix)if(a.comparison&&a.comparison>0)return"след "+i;else return"преди "+i;return i},formatLong:n,formatRelative:(e,t,a,r)=>{let i=l[e];return"function"==typeof i?i(t,a,r):i},localize:f,match:{ordinalNumber:(0,a(92078).K)({matchPattern:/^(\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,m.A)({matchPatterns:{narrow:/^((пр)?н\.?\s?е\.?)/i,abbreviated:/^((пр)?н\.?\s?е\.?)/i,wide:/^(преди новата ера|новата ера|нова ера)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^п/i,/^н/i]},defaultParseWidth:"any"}),quarter:(0,m.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^[1234](-?[врт]?o?)? тримес.?/i,wide:/^[1234](-?[врт]?о?)? тримесечие/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,m.A)({matchPatterns:{abbreviated:/^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,wide:/^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^май/i,/^юн/i,/^юл/i,/^ав/i,/^се/i,/^окт/i,/^но/i,/^де/i]},defaultParseWidth:"any"}),day:(0,m.A)({matchPatterns:{narrow:/^[нпвсч]/i,short:/^(нд|пн|вт|ср|чт|пт|сб)/i,abbreviated:/^(нед|пон|вто|сря|чет|пет|съб)/i,wide:/^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н[ед]/i,/^п[он]/i,/^вт/i,/^ср/i,/^ч[ет]/i,/^п[ет]/i,/^с[ъб]/i]},defaultParseWidth:"any"}),dayPeriod:(0,m.A)({matchPatterns:{any:/^(преди о|след о|в по|на о|през|веч|сут|следо)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^преди о/i,pm:/^след о/i,midnight:/^в пол/i,noon:/^на об/i,morning:/^сут/i,afternoon:/^следо/i,evening:/^веч/i,night:/^през н/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:1}},w=y},88105:(e,t,a)=>{a.d(t,{R:()=>n});var r=a(54094),i=a(69286);function n(e,t,a){let[n,o]=(0,r.x)(a?.in,e,t);return+(0,i.k)(n,a)==+(0,i.k)(o,a)}}};