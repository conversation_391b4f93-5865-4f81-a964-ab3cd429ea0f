{"version": 3, "file": "125.js", "mappings": "wbAAA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,sJCLA,SAA8C,4BAAiB,gBCI/D,oBACA,QACA,EACA,IACA,YAA0B,QAAgB,GAC1C,EAAiC,QAAS,GAAU,IAAS,2BAC7D,EAAkB,QAAuB,GACzC,CAAI,SACJ,MAAe,QAAqB,IACpC,OAEA,CACA,MAA+D,MAA/D,GAA+D,MAA/D,GAA8B,EAAiC,mDAC/D,gEACW,OAA4B,IAE9B,OAA4B,GAAG,CACxC,ECrBA,mBACE,EAAQ,KAAa,EACvB,UADS,CACC,GAAS,MAAQ,OAAI,UAC/B,EAGA,OAAgB,GAAW,mBAF3B,IAGA,gBCcO,eAAeA,IACpB,GAAI,CACF,GAAM,QAAEC,CAAM,CAAE,CAAG,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,GAE7B,GAAI,CAACD,EACH,MADW,CAETE,QAAS,GACTC,MAAO,iCACPC,SAAUC,EAAAA,YAAYA,CAACC,IAAI,CACzB,CAAEH,MAAO,yBAA0B,EACnC,CAAEI,OAAQ,GAAI,EAElB,EAIF,IAAMC,EAAO,MAAMC,IAEnB,GAAI,CAACD,EACH,CAH4BC,GAEnB,EACF,CACLP,SAAS,EACTC,MAAO,+BACPC,SAAUC,EAAAA,YAAYA,CAACC,IAAI,CACzB,CAAEH,MAAO,wBAAyB,EAClC,CAAEI,OAAQ,GAAI,EAElB,EAGF,IAAMG,EAAQF,EAAKG,cAAc,EAAE,CAAC,EAAE,EAAEC,aAClCC,EAAYL,EAAKK,SAAS,CAC1BC,EAAWN,EAAKM,QAAQ,CAE9B,GAAI,CAACJ,EAEH,KAFU,EACVK,QAAQC,GAAG,CAAC,2BAA4BR,EAAKS,EAAE,EACxC,CACLf,QAAS,GACTC,MAAO,gCACPC,SAAUC,EAAAA,YAAYA,CAACC,IAAI,CACzB,CAAEH,MAAO,sBAAuB,EAChC,CAAEI,OAAQ,GAAI,EAElB,EAGF,MAAO,CACLL,SAAS,EACTM,KAAM,CACJU,QAASlB,QACTU,EACAG,UAAWA,GAAaM,OACxBL,SAAUA,GAAYK,MACxB,CACF,CACF,CAAE,MAAOhB,EAAO,CAEd,OADAY,QAAQZ,KAAK,CAAC,mCAAoCA,GAC3C,CACLD,SAAS,EACTC,MAAO,mCACPC,SAAUC,EAAAA,YAAYA,CAACC,IAAI,CACzB,CAAEH,MAAO,sBAAuB,EAChC,CAAEI,OAAQ,GAAI,EAElB,CACF,CACF,CAmBO,SAASa,EACdC,CAAe,CACfd,EAAiB,GAAG,CACpBe,CAAa,EAEb,IAAMC,EAAgB,CACpBpB,MAAOkB,EACPG,UAAW,IAAIC,OAAOC,WAAW,GACjC,GAAIJ,GAAW,SAAEA,CAAQ,CAAC,EAI5B,OADAP,QAAQZ,KAAK,CAAC,aAAcoB,GACrBlB,EAAAA,YAAYA,CAACC,IAAI,CAACiB,EAAe,QAAEhB,CAAO,EACnD,CAKO,SAASoB,EAAsBC,CAAS,CAAErB,EAAiB,GAAG,EACnE,OAAOF,EAAAA,YAAYA,CAACC,IAAI,CAACsB,EAAM,CAAErB,QAAO,EAC1C,CAcO,SAASsB,EACdC,CAAgG,EAEhG,OAAO,MAAOC,EAAsBC,KAClC,IAAMC,EAAa,MAAMlC,IAEzB,GAAI,CAACkC,EAAW/B,OAAO,CACrB,CADuB,MAChB+B,EAAW7B,QAAQ,CAG5B,GAAI,CACF,GAAI4B,EACF,OADW,MACEF,EAAQG,EAAWzB,IAAI,CAAGuB,EAASC,GAEhD,OAAO,MAAMF,EAAQG,EAAWzB,IAAI,CAAGuB,EAE3C,CAAE,MAAO5B,EAAO,CAEd,OADAY,QAAQZ,KAAK,CAAC,qBAAsBA,GAC7BiB,EAAoB,wBAC7B,CACF,CACF,gDCtJO,OAAMc,EAGXC,YAAY3B,CAAuB,CAAE,CACnC,IAAI,CAACA,IAAI,CAAGA,CACd,CAOA,MAAc4B,YACZC,CAAgB,CAChBC,EAAiC,CAAC,CAAC,CACvB,CACZ,IAKIC,EACAC,EANE,QAAEC,EAAS,KAAK,CAAEC,MAAI,QAAEC,CAAM,CAAE,CAAGL,EAavC,GAFAC,EAAM,GAAGK,WAAW,IAAI,UAAEP,GAAU,CAEhCM,EAAQ,CACV,IAAME,EAAe,IAAIC,gBACzBC,OAAOC,OAAO,CAACL,GAAQM,OAAO,CAAC,CAAC,CAACC,EAAKC,EAAM,UACtCA,GACFN,EAAaO,KADDjC,CACO,CAAC+B,EAAKC,EAAME,OADNF,CACc,GAE3C,GACIN,EAAaQ,CAJsB,MAAM,CAIpB,IAAI,CAC3Bd,GAAO,CAAC,CAAC,EAAEM,EAAaQ,QAAQ,IAAI,CAExC,CAGAb,EAAiB,QACfC,EACAa,QAAS,CACP,eAAgB,mBAChB,kBAAmB,IAAI,CAAC9C,IAAI,CAACU,OAAO,CACpC,eAAgB,IAAI,CAACV,IAAI,CAACE,KAC5B,CACF,EAwBEgC,GAAmB,OAAO,CAAlBD,IACVD,EAAeE,IAAI,CAAGa,KAAKC,SAAS,CAACd,EAAAA,EAGvC,GAAI,CACF,IAAMtC,EAAW,MAAMqD,MAAMlB,EAAKC,GAElC,GAAI,CAACpC,EAASsD,EAAE,CAAE,CAChB,IAAMC,EAAY,MAAMvD,EAASwD,IAAI,EACrC,OAAM,MACJ,CAAC,oBAAoB,EAAExD,EAASG,MAAM,CAAC,CAAC,EAAEH,EAASyD,UAAU,CAAC,GAAG,EAAEF,EAAAA,CAAW,CAElF,CAEA,OAAO,MAAMvD,EAASE,IAAI,EAC5B,CAAE,MAAOH,EAAO,CAEd,MADAY,QAAQZ,KAAK,CAAC,CAAC,uBAAuB,EAAEkC,EAAS,CAAC,CAAC,CAAElC,GAC/CA,CACR,CACF,CAKA,MAAM2D,gBAAgBnB,CAIrB,CAAE,CACD,OAAO,IAAI,CAACP,WAAW,CAAC,gBAAiB,CACvCO,OAAQ,CACNoB,MAAO,IACP,GAAGpB,CAAM,CAEb,EACF,CAEA,MAAMqB,eAAe/C,CAAU,CAAE,CAC/B,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,cAAc,EAAEnB,EAAAA,CAAI,CAAE,CAC7C0B,OAAQ,CAAEoB,MAAO,GAAI,CACvB,EACF,CAEA,MAAME,kBAAkBrC,CAAS,CAAE,CACjC,OAAO,IAAI,CAACQ,WAAW,CAAC,gBAAiB,CACvCK,OAAQ,OACRC,KAAMd,CACR,EACF,CAEA,MAAMsC,kBAAkBjD,CAAU,CAAEW,CAAS,CAAE,CAC7C,OAAO,IAAI,CAACQ,WAAW,CAAC,CAAC,cAAc,EAAEnB,EAAAA,CAAI,CAAE,CAC7CwB,OAAQ,QACRC,KAAMd,CACR,EACF,CAEA,MAAMuC,kBAAkBlD,CAAU,CAAE,CAClC,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,cAAc,EAAEnB,EAAAA,CAAI,CAAE,CAC7CwB,OAAQ,QACV,EACF,CAKA,MAAM2B,YAAYzB,CAIjB,CAAE,CACD,IAAM0B,EAAmB,CAAEN,MAAO,IAAK,GAAGpB,CAAM,EAUhD,OAPIA,GAAQ2B,QAAQ,CAClBD,CAAW,CAAC,mCAAmC,CAAG1B,EAAO2B,MAAM,CAC/DD,CAAW,CAAC,gCAAgC,CAAG1B,EAAO2B,MAAM,CAC5DD,CAAW,CAAC,gCAAgC,CAAG1B,EAAO2B,MAAM,CAC5D,OAAOD,EAAYC,MAAM,EAAE,IAGlB,CAAClC,WAAW,CAAC,WAHiC,CAGpB,CAAEO,OAAQ0B,CAAY,EAC7D,CAEA,MAAME,WAAWtD,CAAU,CAAE,CAC3B,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,UAAU,EAAEnB,EAAAA,CAAI,CAAE,CACzC0B,OAAQ,CAAEoB,MAAO,GAAI,CACvB,EACF,CAEA,MAAMS,cAAc5C,CAAS,CAAE,CAC7B,OAAO,IAAI,CAACQ,WAAW,CAAC,YAAa,CACnCK,OAAQ,OACRC,KAAMd,CACR,EACF,CAEA,MAAM6C,cAAcxD,CAAU,CAAEW,CAAS,CAAE,CACzC,OAAO,IAAI,CAACQ,WAAW,CAAC,CAAC,UAAU,EAAEnB,EAAAA,CAAI,CAAE,CACzCwB,OAAQ,QACRC,KAAMd,CACR,EACF,CAEA,MAAM8C,cAAczD,CAAU,CAAE,CAC9B,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,UAAU,EAAEnB,EAAAA,CAAI,CAAE,CACzCwB,OAAQ,QACV,EACF,CAKA,MAAMkC,cAAchC,CAGnB,CAAE,CACD,OAAO,IAAI,CAACP,WAAW,CAAC,cAAe,CACrCO,OAAQ,CAAEoB,MAAO,IAAK,GAAGpB,CAAM,CACjC,EACF,CAEA,MAAMiC,aAAa3D,CAAU,CAAE,CAC7B,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,YAAY,EAAEnB,EAAAA,CAAI,CAC7C,CAEA,MAAM4D,gBAAgBjD,CAAS,CAAE,CAC/B,OAAO,IAAI,CAACQ,WAAW,CAAC,cAAe,CACrCK,OAAQ,OACRC,KAAMd,CACR,EACF,CAEA,MAAMkD,gBAAgB7D,CAAU,CAAEW,CAAS,CAAE,CAC3C,OAAO,IAAI,CAACQ,WAAW,CAAC,CAAC,YAAY,EAAEnB,EAAAA,CAAI,CAAE,CAC3CwB,OAAQ,QACRC,KAAMd,CACR,EACF,CAEA,MAAMmD,gBAAgB9D,CAAU,CAAE,CAChC,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,YAAY,EAAEnB,EAAAA,CAAI,CAAE,CAC3CwB,OAAQ,QACV,EACF,CAKA,MAAMuC,SAASrC,CAGd,CAAE,CACD,OAAO,IAAI,CAACP,WAAW,CAAC,SAAU,CAChCO,OAAQ,CAAEoB,MAAO,IAAK,GAAGpB,CAAM,CACjC,EACF,CAEA,MAAMsC,WAAWjF,CAAc,CAAE4B,CAAS,CAAE,CAC1C,OAAO,IAAI,CAACQ,WAAW,CAAC,CAAC,OAAO,EAAEpC,EAAAA,CAAQ,CAAE,CAC1CyC,OAAQ,QACRC,KAAMd,CACR,EACF,CAEA,MAAMsD,iBAAkB,CAEtB,OAAO,IAAI,CAAC9C,WAAW,CAAC,cAAe,CACrCK,OAAQ,OACRC,KAAM,CACJxB,QAAS,IAAI,CAACV,IAAI,CAACU,OAAO,CAC1BR,MAAO,IAAI,CAACF,IAAI,CAACE,KAAK,CACtBG,UAAW,IAAI,CAACL,IAAI,CAACK,SAAS,CAC9BC,SAAU,IAAI,CAACN,IAAI,CAACM,QAAQ,CAEhC,EACF,CAEA,MAAMqE,SAASC,CAKd,CAAE,CAED,GAAI,CAEF,IAAMC,EAAgB,MAAM,IAAI,CAACjD,WAAW,CAAM,SAAU,CAC1DO,OAAQ,CACN2C,MAAO/B,KAAKC,SAAS,CAAC,CACpBtC,QAAS,CAAEqE,OAAQH,EAASlE,OAAO,CACrC,GACAsE,MAAO,CACT,CACF,GAEA,IAAIH,EAAcI,IAAI,IAAIJ,EAAcI,IAAI,CAACC,MAAM,EAAG,EA0BpD,OAXgB,MAAM,IAAI,CAACtD,WAAW,CAAM,SAAU,CACpDK,OAAQ,OACRC,KAAM,CACJhC,MAAO0E,EAAS1E,KAAK,CACrBQ,QAASkE,EAASlE,OAAO,CACzBL,UAAWuE,EAASvE,SAAS,CAC7BC,SAAUsE,EAAStE,QAAQ,CAC3B6E,KAAM,aACNC,UAAW,IAAInE,OAAOC,WAAW,EACnC,CACF,EAzBuD,EAEvD,IAAMmE,EAAeR,EAAcI,IAAI,CAAC,EAAE,CAU1C,OAAOK,MATmB,IAAI,CAAC1D,WAAW,CAAM,CAAC,OAAO,EAAEyD,EAAa5E,EAAE,EAAE,CAAE,CAC3EwB,OAAQ,QACRC,KAAM,CACJhC,MAAO0E,EAAS1E,KAAK,CACrBG,UAAWuE,EAASvE,SAAS,CAC7BC,SAAUsE,EAAStE,QAAQ,CAC3B8E,UAAW,IAAInE,OAAOC,WAAW,EACnC,CACF,EAEF,CAeF,CAAE,KAfO,CAeAvB,EAAO,CAGd,OAFAY,QAAQZ,KAAK,CAAC,mCAAoCA,GAE3C,CACLc,GAAI,UACJP,MAAO0E,EAAS1E,KAAK,CACrBQ,QAASkE,EAASlE,OAAO,CACzByE,KAAM,aACN9E,UAAWuE,EAASvE,SAAS,CAC7BC,SAAUsE,EAAStE,QAAQ,CAE/B,CACF,CAKA,MAAMiF,uBAAuBpD,CAK5B,CAAE,CACD,OAAO,IAAI,CAACP,WAAW,CAAC,wBAAyB,CAC/CO,OAAQ,CACNoB,MAAO,IACP,GAAGpB,CACL,CACF,EACF,CAEA,MAAMqD,sBAAsB/E,CAAU,CAAE,CACtC,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,sBAAsB,EAAEnB,EAAAA,CAAI,CAAE,CACrD0B,OAAQ,CAAEoB,MAAO,GAAI,CACvB,EACF,CAEA,MAAMkC,yBAAyBrE,CAAS,CAAE,CACxC,OAAO,IAAI,CAACQ,WAAW,CAAC,wBAAyB,CAC/CK,OAAQ,OACRC,KAAMd,CACR,EACF,CAEA,MAAMsE,yBAAyBjF,CAAU,CAAEW,CAAS,CAAE,CACpD,OAAO,IAAI,CAACQ,WAAW,CAAC,CAAC,sBAAsB,EAAEnB,EAAAA,CAAI,CAAE,CACrDwB,OAAQ,QACRC,KAAMd,CACR,EACF,CAEA,MAAMuE,yBAAyBlF,CAAU,CAAE,CACzC,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,sBAAsB,EAAEnB,EAAAA,CAAI,CAAE,CACrDwB,OAAQ,QACV,EACF,CAKA,MAAM2D,gBAAgBzD,CAKrB,CAAE,CACD,OAAO,IAAI,CAACP,WAAW,CAAC,iBAAkB,CACxCO,OAAQ,CACNoB,MAAO,IACP,GAAGpB,CACL,CACF,EACF,CAEA,MAAM0D,eAAepF,CAAU,CAAE,CAC/B,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,eAAe,EAAEnB,EAAAA,CAAI,CAAE,CAC9C0B,OAAQ,CAAEoB,MAAO,GAAI,CACvB,EACF,CAEA,MAAMuC,kBAAkB1E,CAAS,CAAE,CACjC,OAAO,IAAI,CAACQ,WAAW,CAAC,iBAAkB,CACxCK,OAAQ,OACRC,KAAMd,CACR,EACF,CAEA,MAAM2E,kBAAkBtF,CAAU,CAAEW,CAAS,CAAE,CAC7C,OAAO,IAAI,CAACQ,WAAW,CAAC,CAAC,eAAe,EAAEnB,EAAAA,CAAI,CAAE,CAC9CwB,OAAQ,QACRC,KAAMd,CACR,EACF,CAEA,MAAM4E,kBAAkBvF,CAAU,CAAE,CAClC,OAAO,IAAI,CAACmB,WAAW,CAAC,CAAC,eAAe,EAAEnB,EAAAA,CAAI,CAAE,CAC9CwB,OAAQ,QACV,EACF,CAKA,MAAMgE,gCAAgCC,CAAiB,CAAE/D,CAMxD,CAAE,CACD,OAAO,IAAI,CAACP,WAAW,CAAC,CAAC,UAAU,EAAEsE,EAAU,aAAa,CAAC,CAAE,CAC7D/D,OAAQ,CACNoB,MAAO,IACP,GAAGpB,CAAM,CAEb,EACF,CAEA,MAAMgE,yBAAyBD,CAAiB,CAAE/D,CAOjD,CAAE,CACD,OAAO,IAAI,CAACP,WAAW,CAAC,CAAC,UAAU,EAAEsE,EAAU,MAAM,CAAC,CAAE,CACtD/D,OAAQ,CACNoB,MAAO,IACP,GAAGpB,CAAM,CAEb,EACF,CAEA,MAAMiE,mBAAmBF,CAAiB,CAAE/D,CAI3C,CAAE,CACD,OAAO,IAAI,CAACP,WAAW,CAAC,CAAC,UAAU,EAAEsE,EAAU,SAAS,CAAC,CAAE,CACzD/D,OAAQ,CACNoB,MAAO,IACP,GAAGpB,CAAM,CAEb,EACF,CACF,CAKO,SAASkE,EAAoBrG,CAAuB,EACzD,OAAO,IAAI0B,EAAc1B,EAC3B,qDChcA,YAyFA,sGAxFA,QACA,cACI,QAAY,SACZ,QAAY,qCACZ,QAAY,QANhB,MAOA,CACA,oBACA,IAAS,QAAY,WACrB,SAEA,iBACA,EAAgB,QAAe,wBAC/B,EAAkB,QAAY,gBAC9B,OACA,OACA,GAAW,QAAY,WACvB,KACA,EACA,qBAA2B,QAAY,2BACvC,CAEA,GADA,OAAoD,QAAY,SAChE,CACA,MAA2B,QAAY,UACvC,aACA,qBAA2B,QAAY,2BACvC,CACA,SACA,CACA,EACA,cACA,cACA,cAKA,cACA,IAAU,0BAAqC,EAC/C,GACA,KACA,MAEA,sBACA,aACA,KACA,KACK,sBAEL,EACA,aACA,2BAA2C,QAAY,iBACvD,EAGA,cAFA,EAGA,EACA,aACA,8BACA,SAEA,0BACA,MACA,SAEA,IACA,aAGA,OAFA,eACA,gBACA,EACA,CAAI,SAMJ,OALA,2BACA,wEACA,YACA,aAAyB,QAAY,UAErC,EACA,CACA,EAGA,OACA,eACA,gBAIA,sCACA,EAEA,QACA,eACI,QAAY,SACZ,QAAY,SACZ,QAAY,SACZ,QAAY,UAAoB,EAChC,QAAY,YACZ,QAAY,SACZ,QAAY,SAChB,+CACA,4CACA,wBACA,kBACA,mBACA,CAAK,EACL,2CAGM,QAAY,yCAFZ,QAAY,yBAId,QAAY,mBACZ,QAAY,iCACZ,QAAY,6CAChB,MAAsB,QAAmB,kBACzC,KACM,QAAY,sCAElB,cACM,QAAY,gDAEd,QAAY,cAChB,CACA,wBACoB,gBAAZ,QAAY,uBAGZ,QAAY,gDAA8D,QAAQ,wCAG1F,yDAIA,CACA,cACA,MAAW,QAAY,6CAA2D,QAAQ,mCAC1F,CACA,UACA,MAA4B,QAAe,wCACvC,QAAe,gCACV,QAAe,8CAGpB,QAAY,iBACZ,QAAe,sBACnB,CACA,EACA,cACA,cACA,cACA,cACA,cACA,cACA,gBACA,sCAA4C,QAAe,yBAC3D,EACA,gBACA,0BAEA,EADA,IAAoC,QAAY,4CAIhD,CAAU,QAAY,4BACtB,EACA,aACA,yCACI,QAAe,sBAInB,GADuB,QAAY,iBAA0B,QAAY,uBACzE,CACQ,QAAY,UACpB,yEACa,QAAY,UAErB,QAAe,sBACnB,MACA,CACM,QAAY,WAGlB,+BACI,QAAY,iCACV,QAAe,qBACrB,CAAK,GAED,QAAY,wBACV,QAAe,qBACrB,CAAK,KAEL,EACA,aACA,0BAA6B,QAAY,oBACzC,cAEA,qBACA,OAAc,QAAY,QAC1B,CAAK,EACL,SACA,iCACA,CACA,CAAG,8BACC,QAAY,WAChB,CAAG,mBACH,EAIA,gBACA,eAGA,iCACA,8CACA,eACA,oBAEA,qCAEA,EAMA,aACA,OACA,KAAU,QAAY,aACtB,QAAa,QAAY,qBAKzB,MAHA,0CACA,IAAoB,+CAEpB,CACA,EAIA,gBACA,MAAsB,QAAe,sBACrC,OACA,QACA,GAAQ,QAAY,0BACpB,GAAQ,QAAY,0BACpB,WACA,eACA,GAAO,QAAY,yBAAqC,GAAI,QAAY,yBAAmC,EAAI,CAC/G,GAAO,QAAY,oBAAgC,GAAI,QAAY,oBAA8B,EAAI,CACrG,SACA,CACA,EE5PmB,OAAgB,CAAC,IAAW,iBCC/C,OACA,UAAa,IAAU,CACvB,eAAkB,IAAe,CACjC,OAAU,IAAO,CACjB,WAAc,IAAW,CACzB,aAAgB,gBAAgB,GAAG,EACnC,QAD6C,CACjC,IAAS,CACrB,OAAU,IAAM,CAChB,YAAe,IAAY,CAC3B,YAAe,IAAY,CAC3B,WACA,SAAc,IAAkB,CAChC,MAAW,IAAe,CAE1B,EACA,KAAkD,CDflD,YACA,OAAiB,MACjB,EAAoB,QAAsB,IAC1C,EAAuB,QAAyB,EAAG,sBAA0B,EAC7E,MAAwB,EAAkB,CAC1C,eACA,gCACA,sBACA,gBACA,kBAA4B,yDAAmE,GAC5F,EACH,OACA,KACA,KACA,WACA,CACA,GCDmE,CAAG,UAA0C,gCCAhG,kBAAoF", "sources": ["webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/middleware-storage.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/clerkClient.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/server/currentUser.js", "webpack://next-shadcn-dashboard-starter/./src/lib/auth-middleware.ts", "webpack://next-shadcn-dashboard-starter/./src/lib/payload-client.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-L6WULBPV.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/telemetry.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/index.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/createClerkClient.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/route-modules/app-route/module.compiled.js"], "sourcesContent": ["function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 45962;\nmodule.exports = webpackEmptyContext;", "import \"../chunk-BUSYA2B4.js\";\nimport { AsyncLocalStorage } from \"node:async_hooks\";\nconst clerkMiddlewareRequestDataStore = /* @__PURE__ */ new Map();\nconst clerkMiddlewareRequestDataStorage = new AsyncLocalStorage();\nexport {\n  clerkMiddlewareRequestDataStorage,\n  clerkMiddlewareRequestDataStore\n};\n//# sourceMappingURL=middleware-storage.js.map", "import \"../chunk-BUSYA2B4.js\";\nimport { constants } from \"@clerk/backend/internal\";\nimport { buildRequestLike, isPrerenderingBailout } from \"../app-router/server/utils\";\nimport { createClerkClientWithOptions } from \"./createClerkClient\";\nimport { getHeader } from \"./headers-utils\";\nimport { clerkMiddlewareRequestDataStorage } from \"./middleware-storage\";\nimport { decryptClerkRequestData } from \"./utils\";\nconst clerkClient = async () => {\n  var _a, _b;\n  let requestData;\n  try {\n    const request = await buildRequestLike();\n    const encryptedRequestData = getHeader(request, constants.Headers.ClerkRequestData);\n    requestData = decryptClerkRequestData(encryptedRequestData);\n  } catch (err) {\n    if (err && isPrerenderingBailout(err)) {\n      throw err;\n    }\n  }\n  const options = (_b = (_a = clerkMiddlewareRequestDataStorage.getStore()) == null ? void 0 : _a.get(\"requestData\")) != null ? _b : requestData;\n  if ((options == null ? void 0 : options.secretKey) || (options == null ? void 0 : options.publishableKey)) {\n    return createClerkClientWithOptions(options);\n  }\n  return createClerkClientWithOptions({});\n};\nexport {\n  clerkClient\n};\n//# sourceMappingURL=clerkClient.js.map", "import \"../../chunk-BUSYA2B4.js\";\nimport { clerkClient } from \"../../server/clerkClient\";\nimport { auth } from \"./auth\";\nasync function currentUser() {\n  require(\"server-only\");\n  const { userId } = await auth();\n  if (!userId) {\n    return null;\n  }\n  return (await clerkClient()).users.getUser(userId);\n}\nexport {\n  currentUser\n};\n//# sourceMappingURL=currentUser.js.map", "// Authentication middleware for API routes\nimport { auth, currentUser } from '@clerk/nextjs/server';\nimport { NextRequest, NextResponse } from 'next/server';\n\nexport interface AuthenticatedUser {\n  clerkId: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  role?: 'admin' | 'front-desk' | 'doctor';\n  payloadUserId?: string;\n}\n\nexport interface AuthMiddlewareResult {\n  success: boolean;\n  user?: AuthenticatedUser;\n  error?: string;\n  response?: NextResponse;\n}\n\n/**\n * Middleware to validate Clerk authentication in API routes\n * Returns user data if authenticated, error response if not\n */\nexport async function validateAuthentication(): Promise<AuthMiddlewareResult> {\n  try {\n    const { userId } = await auth();\n\n    if (!userId) {\n      return {\n        success: false,\n        error: 'Unauthorized - No user session',\n        response: NextResponse.json(\n          { error: 'Authentication required' },\n          { status: 401 }\n        ),\n      };\n    }\n\n    // Get the current user with full profile information\n    const user = await currentUser();\n\n    if (!user) {\n      return {\n        success: false,\n        error: 'Unable to fetch user profile',\n        response: NextResponse.json(\n          { error: 'User profile not found' },\n          { status: 401 }\n        ),\n      };\n    }\n\n    const email = user.emailAddresses?.[0]?.emailAddress;\n    const firstName = user.firstName;\n    const lastName = user.lastName;\n\n    if (!email) {\n      console.log('No email found for user:', user.id);\n      return {\n        success: false,\n        error: 'Invalid user - No email found',\n        response: NextResponse.json(\n          { error: 'User email not found' },\n          { status: 401 }\n        ),\n      };\n    }\n\n    return {\n      success: true,\n      user: {\n        clerkId: userId,\n        email,\n        firstName: firstName || undefined,\n        lastName: lastName || undefined,\n      },\n    };\n  } catch (error) {\n    console.error('Authentication validation error:', error);\n    return {\n      success: false,\n      error: 'Authentication validation failed',\n      response: NextResponse.json(\n        { error: 'Authentication error' },\n        { status: 500 }\n      ),\n    };\n  }\n}\n\n/**\n * Create authenticated request headers for Payload CMS\n * This will include a service token or user context\n */\nexport function createPayloadHeaders(user: AuthenticatedUser): HeadersInit {\n  return {\n    'Content-Type': 'application/json',\n    'X-Clerk-User-Id': user.clerkId,\n    'X-User-Email': user.email,\n    // In production, you might want to include a service token here\n    // 'Authorization': `Bearer ${process.env.PAYLOAD_SERVICE_TOKEN}`,\n  };\n}\n\n/**\n * Enhanced error handling for API responses\n */\nexport function createErrorResponse(\n  message: string,\n  status: number = 500,\n  details?: any\n): NextResponse {\n  const errorResponse = {\n    error: message,\n    timestamp: new Date().toISOString(),\n    ...(details && { details }),\n  };\n\n  console.error('API Error:', errorResponse);\n  return NextResponse.json(errorResponse, { status });\n}\n\n/**\n * Success response helper\n */\nexport function createSuccessResponse(data: any, status: number = 200): NextResponse {\n  return NextResponse.json(data, { status });\n}\n\n/**\n * Wrapper function for API route handlers with authentication\n * Overloaded to support both regular routes and dynamic routes with params\n */\nexport function withAuthentication<T>(\n  handler: (user: AuthenticatedUser, request: NextRequest) => Promise<NextResponse>\n): (request: NextRequest) => Promise<NextResponse>;\n\nexport function withAuthentication<T>(\n  handler: (user: AuthenticatedUser, request: NextRequest, context: any) => Promise<NextResponse>\n): (request: NextRequest, context: any) => Promise<NextResponse>;\n\nexport function withAuthentication<T>(\n  handler: (user: AuthenticatedUser, request: NextRequest, context?: any) => Promise<NextResponse>\n) {\n  return async (request: NextRequest, context?: any): Promise<NextResponse> => {\n    const authResult = await validateAuthentication();\n\n    if (!authResult.success) {\n      return authResult.response!;\n    }\n\n    try {\n      if (context) {\n        return await handler(authResult.user!, request, context);\n      } else {\n        return await handler(authResult.user!, request);\n      }\n    } catch (error) {\n      console.error('API handler error:', error);\n      return createErrorResponse('Internal server error');\n    }\n  };\n}\n", "// Payload CMS client with authentication\nimport { AuthenticatedUser } from './auth-middleware';\n\nexport interface PayloadRequestOptions {\n  method?: 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE';\n  body?: any;\n  params?: Record<string, string | number>;\n}\n\n/**\n * Enhanced Payload CMS client with proper authentication\n * Routes through frontend API routes which proxy to backend\n */\nexport class PayloadClient {\n  private user: AuthenticatedUser;\n\n  constructor(user: AuthenticatedUser) {\n    this.user = user;\n  }\n\n  /**\n   * Make authenticated request to backend API\n   * When called from server-side (API routes), goes directly to backend\n   * When called from client-side, goes through frontend API routes\n   */\n  private async makeRequest<T>(\n    endpoint: string,\n    options: PayloadRequestOptions = {}\n  ): Promise<T> {\n    const { method = 'GET', body, params } = options;\n\n    // Determine if we're running on server or client\n    const isServer = typeof window === 'undefined';\n\n    let url: string;\n    let requestOptions: RequestInit;\n\n    if (isServer) {\n      // Server-side: make direct request to backend\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n      url = `${backendUrl}/api${endpoint}`;\n\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        if (searchParams.toString()) {\n          url += `?${searchParams.toString()}`;\n        }\n      }\n\n      // Include Clerk user headers for backend authentication\n      requestOptions = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'X-Clerk-User-Id': this.user.clerkId,\n          'X-User-Email': this.user.email,\n        },\n      };\n    } else {\n      // Client-side: use frontend API routes\n      url = `/api${endpoint}`;\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        if (searchParams.toString()) {\n          url += `?${searchParams.toString()}`;\n        }\n      }\n\n      requestOptions = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      };\n    }\n\n    if (body && method !== 'GET') {\n      requestOptions.body = JSON.stringify(body);\n    }\n\n    try {\n      const response = await fetch(url, requestOptions);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(\n          `API request failed: ${response.status} ${response.statusText} - ${errorText}`\n        );\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Appointments API methods\n   */\n  async getAppointments(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n  }) {\n    return this.makeRequest('/appointments', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getAppointment(id: string) {\n    return this.makeRequest(`/appointments/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createAppointment(data: any) {\n    return this.makeRequest('/appointments', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updateAppointment(id: string, data: any) {\n    return this.makeRequest(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deleteAppointment(id: string) {\n    return this.makeRequest(`/appointments/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patients API methods\n   */\n  async getPatients(params?: {\n    limit?: number;\n    page?: number;\n    search?: string;\n  }) {\n    const queryParams: any = { depth: '1', ...params };\n    \n    // Add search functionality\n    if (params?.search) {\n      queryParams['where[or][0][fullName][contains]'] = params.search;\n      queryParams['where[or][1][phone][contains]'] = params.search;\n      queryParams['where[or][2][email][contains]'] = params.search;\n      delete queryParams.search; // Remove search from params\n    }\n\n    return this.makeRequest('/patients', { params: queryParams });\n  }\n\n  async getPatient(id: string) {\n    return this.makeRequest(`/patients/${id}`, {\n      params: { depth: '1' },\n    });\n  }\n\n  async createPatient(data: any) {\n    return this.makeRequest('/patients', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatient(id: string, data: any) {\n    return this.makeRequest(`/patients/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatient(id: string) {\n    return this.makeRequest(`/patients/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Treatments API methods\n   */\n  async getTreatments(params?: {\n    limit?: number;\n    page?: number;\n  }) {\n    return this.makeRequest('/treatments', {\n      params: { depth: '1', ...params },\n    });\n  }\n\n  async getTreatment(id: string) {\n    return this.makeRequest(`/treatments/${id}`);\n  }\n\n  async createTreatment(data: any) {\n    return this.makeRequest('/treatments', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updateTreatment(id: string, data: any) {\n    return this.makeRequest(`/treatments/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deleteTreatment(id: string) {\n    return this.makeRequest(`/treatments/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Users API methods (for user management)\n   */\n  async getUsers(params?: {\n    limit?: number;\n    page?: number;\n  }) {\n    return this.makeRequest('/users', {\n      params: { depth: '1', ...params },\n    });\n  }\n\n  async updateUser(userId: string, data: any) {\n    return this.makeRequest(`/users/${userId}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async syncCurrentUser() {\n    // This would sync the current Clerk user with Payload CMS\n    return this.makeRequest('/users/sync', {\n      method: 'POST',\n      body: {\n        clerkId: this.user.clerkId,\n        email: this.user.email,\n        firstName: this.user.firstName,\n        lastName: this.user.lastName,\n      },\n    });\n  }\n\n  async syncUser(userData: {\n    clerkId: string;\n    email: string;\n    firstName?: string;\n    lastName?: string;\n  }) {\n    // Sync a specific user with Payload CMS and return user with role\n    try {\n      // First, try to find existing user\n      const existingUsers = await this.makeRequest<any>('/users', {\n        params: {\n          where: JSON.stringify({\n            clerkId: { equals: userData.clerkId }\n          }),\n          limit: 1,\n        },\n      });\n\n      if (existingUsers.docs && existingUsers.docs.length > 0) {\n        // Update existing user\n        const existingUser = existingUsers.docs[0];\n        const updatedUser = await this.makeRequest<any>(`/users/${existingUser.id}`, {\n          method: 'PATCH',\n          body: {\n            email: userData.email,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            lastLogin: new Date().toISOString(),\n          },\n        });\n        return updatedUser;\n      } else {\n        // Create new user with default role\n        const newUser = await this.makeRequest<any>('/users', {\n          method: 'POST',\n          body: {\n            email: userData.email,\n            clerkId: userData.clerkId,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            role: 'front-desk', // Default role for new users\n            lastLogin: new Date().toISOString(),\n          },\n        });\n        return newUser;\n      }\n    } catch (error) {\n      console.error('Error syncing user with Payload:', error);\n      // Return a default user object if sync fails\n      return {\n        id: 'temp-id',\n        email: userData.email,\n        clerkId: userData.clerkId,\n        role: 'front-desk',\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n      };\n    }\n  }\n\n  /**\n   * Patient Interactions API methods\n   */\n  async getPatientInteractions(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n    sort?: string;\n  }) {\n    return this.makeRequest('/patient-interactions', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getPatientInteraction(id: string) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createPatientInteraction(data: any) {\n    return this.makeRequest('/patient-interactions', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatientInteraction(id: string, data: any) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatientInteraction(id: string) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patient Tasks API methods\n   */\n  async getPatientTasks(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n    sort?: string;\n  }) {\n    return this.makeRequest('/patient-tasks', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTask(id: string) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createPatientTask(data: any) {\n    return this.makeRequest('/patient-tasks', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatientTask(id: string, data: any) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatientTask(id: string) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patient-specific CRM methods\n   */\n  async getPatientInteractionsByPatient(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    interactionType?: string;\n    status?: string;\n    priority?: string;\n  }) {\n    return this.makeRequest(`/patients/${patientId}/interactions`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTasksByPatient(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    taskType?: string;\n    status?: string;\n    priority?: string;\n    assignedTo?: string;\n  }) {\n    return this.makeRequest(`/patients/${patientId}/tasks`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTimeline(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    type?: 'interaction' | 'task';\n  }) {\n    return this.makeRequest(`/patients/${patientId}/timeline`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n}\n\n/**\n * Factory function to create PayloadClient instance\n */\nexport function createPayloadClient(user: AuthenticatedUser): PayloadClient {\n  return new PayloadClient(user);\n}\n", "import {\n  isTruthy\n} from \"./chunk-RWYTRAIK.mjs\";\nimport {\n  parsePublishableKey\n} from \"./chunk-G3VP5PJE.mjs\";\nimport {\n  __privateAdd,\n  __privateGet,\n  __privateMethod,\n  __privateSet\n} from \"./chunk-7ELT755Q.mjs\";\n\n// src/telemetry/throttler.ts\nvar DEFAULT_CACHE_TTL_MS = 864e5;\nvar _storageKey, _cacheTtl, _TelemetryEventThrottler_instances, generateKey_fn, cache_get, isValidBrowser_get;\nvar TelemetryEventThrottler = class {\n  constructor() {\n    __privateAdd(this, _TelemetryEventThrottler_instances);\n    __privateAdd(this, _storageKey, \"clerk_telemetry_throttler\");\n    __privateAdd(this, _cacheTtl, DEFAULT_CACHE_TTL_MS);\n  }\n  isEventThrottled(payload) {\n    if (!__privateGet(this, _TelemetryEventThrottler_instances, isValidBrowser_get)) {\n      return false;\n    }\n    const now = Date.now();\n    const key = __privateMethod(this, _TelemetryEventThrottler_instances, generateKey_fn).call(this, payload);\n    const entry = __privateGet(this, _TelemetryEventThrottler_instances, cache_get)?.[key];\n    if (!entry) {\n      const updatedCache = {\n        ...__privateGet(this, _TelemetryEventThrottler_instances, cache_get),\n        [key]: now\n      };\n      localStorage.setItem(__privateGet(this, _storageKey), JSON.stringify(updatedCache));\n    }\n    const shouldInvalidate = entry && now - entry > __privateGet(this, _cacheTtl);\n    if (shouldInvalidate) {\n      const updatedCache = __privateGet(this, _TelemetryEventThrottler_instances, cache_get);\n      delete updatedCache[key];\n      localStorage.setItem(__privateGet(this, _storageKey), JSON.stringify(updatedCache));\n    }\n    return !!entry;\n  }\n};\n_storageKey = new WeakMap();\n_cacheTtl = new WeakMap();\n_TelemetryEventThrottler_instances = new WeakSet();\n/**\n * Generates a consistent unique key for telemetry events by sorting payload properties.\n * This ensures that payloads with identical content in different orders produce the same key.\n */\ngenerateKey_fn = function(event) {\n  const { sk: _sk, pk: _pk, payload, ...rest } = event;\n  const sanitizedEvent = {\n    ...payload,\n    ...rest\n  };\n  return JSON.stringify(\n    Object.keys({\n      ...payload,\n      ...rest\n    }).sort().map((key) => sanitizedEvent[key])\n  );\n};\ncache_get = function() {\n  const cacheString = localStorage.getItem(__privateGet(this, _storageKey));\n  if (!cacheString) {\n    return {};\n  }\n  return JSON.parse(cacheString);\n};\nisValidBrowser_get = function() {\n  if (typeof window === \"undefined\") {\n    return false;\n  }\n  const storage = window.localStorage;\n  if (!storage) {\n    return false;\n  }\n  try {\n    const testKey = \"test\";\n    storage.setItem(testKey, testKey);\n    storage.removeItem(testKey);\n    return true;\n  } catch (err) {\n    const isQuotaExceededError = err instanceof DOMException && // Check error names for different browsers\n    (err.name === \"QuotaExceededError\" || err.name === \"NS_ERROR_DOM_QUOTA_REACHED\");\n    if (isQuotaExceededError && storage.length > 0) {\n      storage.removeItem(__privateGet(this, _storageKey));\n    }\n    return false;\n  }\n};\n\n// src/telemetry/collector.ts\nvar DEFAULT_CONFIG = {\n  samplingRate: 1,\n  maxBufferSize: 5,\n  // Production endpoint: https://clerk-telemetry.com\n  // Staging endpoint: https://staging.clerk-telemetry.com\n  // Local: http://localhost:8787\n  endpoint: \"https://clerk-telemetry.com\"\n};\nvar _config, _eventThrottler, _metadata, _buffer, _pendingFlush, _TelemetryCollector_instances, shouldRecord_fn, shouldBeSampled_fn, scheduleFlush_fn, flush_fn, logEvent_fn, getSDKMetadata_fn, preparePayload_fn;\nvar TelemetryCollector = class {\n  constructor(options) {\n    __privateAdd(this, _TelemetryCollector_instances);\n    __privateAdd(this, _config);\n    __privateAdd(this, _eventThrottler);\n    __privateAdd(this, _metadata, {});\n    __privateAdd(this, _buffer, []);\n    __privateAdd(this, _pendingFlush);\n    __privateSet(this, _config, {\n      maxBufferSize: options.maxBufferSize ?? DEFAULT_CONFIG.maxBufferSize,\n      samplingRate: options.samplingRate ?? DEFAULT_CONFIG.samplingRate,\n      disabled: options.disabled ?? false,\n      debug: options.debug ?? false,\n      endpoint: DEFAULT_CONFIG.endpoint\n    });\n    if (!options.clerkVersion && typeof window === \"undefined\") {\n      __privateGet(this, _metadata).clerkVersion = \"\";\n    } else {\n      __privateGet(this, _metadata).clerkVersion = options.clerkVersion ?? \"\";\n    }\n    __privateGet(this, _metadata).sdk = options.sdk;\n    __privateGet(this, _metadata).sdkVersion = options.sdkVersion;\n    __privateGet(this, _metadata).publishableKey = options.publishableKey ?? \"\";\n    const parsedKey = parsePublishableKey(options.publishableKey);\n    if (parsedKey) {\n      __privateGet(this, _metadata).instanceType = parsedKey.instanceType;\n    }\n    if (options.secretKey) {\n      __privateGet(this, _metadata).secretKey = options.secretKey.substring(0, 16);\n    }\n    __privateSet(this, _eventThrottler, new TelemetryEventThrottler());\n  }\n  get isEnabled() {\n    if (__privateGet(this, _metadata).instanceType !== \"development\") {\n      return false;\n    }\n    if (__privateGet(this, _config).disabled || typeof process !== \"undefined\" && isTruthy(process.env.CLERK_TELEMETRY_DISABLED)) {\n      return false;\n    }\n    if (typeof window !== \"undefined\" && !!window?.navigator?.webdriver) {\n      return false;\n    }\n    return true;\n  }\n  get isDebug() {\n    return __privateGet(this, _config).debug || typeof process !== \"undefined\" && isTruthy(process.env.CLERK_TELEMETRY_DEBUG);\n  }\n  record(event) {\n    const preparedPayload = __privateMethod(this, _TelemetryCollector_instances, preparePayload_fn).call(this, event.event, event.payload);\n    __privateMethod(this, _TelemetryCollector_instances, logEvent_fn).call(this, preparedPayload.event, preparedPayload);\n    if (!__privateMethod(this, _TelemetryCollector_instances, shouldRecord_fn).call(this, preparedPayload, event.eventSamplingRate)) {\n      return;\n    }\n    __privateGet(this, _buffer).push(preparedPayload);\n    __privateMethod(this, _TelemetryCollector_instances, scheduleFlush_fn).call(this);\n  }\n};\n_config = new WeakMap();\n_eventThrottler = new WeakMap();\n_metadata = new WeakMap();\n_buffer = new WeakMap();\n_pendingFlush = new WeakMap();\n_TelemetryCollector_instances = new WeakSet();\nshouldRecord_fn = function(preparedPayload, eventSamplingRate) {\n  return this.isEnabled && !this.isDebug && __privateMethod(this, _TelemetryCollector_instances, shouldBeSampled_fn).call(this, preparedPayload, eventSamplingRate);\n};\nshouldBeSampled_fn = function(preparedPayload, eventSamplingRate) {\n  const randomSeed = Math.random();\n  const toBeSampled = randomSeed <= __privateGet(this, _config).samplingRate && (typeof eventSamplingRate === \"undefined\" || randomSeed <= eventSamplingRate);\n  if (!toBeSampled) {\n    return false;\n  }\n  return !__privateGet(this, _eventThrottler).isEventThrottled(preparedPayload);\n};\nscheduleFlush_fn = function() {\n  if (typeof window === \"undefined\") {\n    __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    return;\n  }\n  const isBufferFull = __privateGet(this, _buffer).length >= __privateGet(this, _config).maxBufferSize;\n  if (isBufferFull) {\n    if (__privateGet(this, _pendingFlush)) {\n      const cancel = typeof cancelIdleCallback !== \"undefined\" ? cancelIdleCallback : clearTimeout;\n      cancel(__privateGet(this, _pendingFlush));\n    }\n    __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    return;\n  }\n  if (__privateGet(this, _pendingFlush)) {\n    return;\n  }\n  if (\"requestIdleCallback\" in window) {\n    __privateSet(this, _pendingFlush, requestIdleCallback(() => {\n      __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    }));\n  } else {\n    __privateSet(this, _pendingFlush, setTimeout(() => {\n      __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    }, 0));\n  }\n};\nflush_fn = function() {\n  fetch(new URL(\"/v1/event\", __privateGet(this, _config).endpoint), {\n    method: \"POST\",\n    // TODO: We send an array here with that idea that we can eventually send multiple events.\n    body: JSON.stringify({\n      events: __privateGet(this, _buffer)\n    }),\n    headers: {\n      \"Content-Type\": \"application/json\"\n    }\n  }).catch(() => void 0).then(() => {\n    __privateSet(this, _buffer, []);\n  }).catch(() => void 0);\n};\n/**\n * If running in debug mode, log the event and its payload to the console.\n */\nlogEvent_fn = function(event, payload) {\n  if (!this.isDebug) {\n    return;\n  }\n  if (typeof console.groupCollapsed !== \"undefined\") {\n    console.groupCollapsed(\"[clerk/telemetry]\", event);\n    console.log(payload);\n    console.groupEnd();\n  } else {\n    console.log(\"[clerk/telemetry]\", event, payload);\n  }\n};\n/**\n * If in browser, attempt to lazily grab the SDK metadata from the Clerk singleton, otherwise fallback to the initially passed in values.\n *\n * This is necessary because the sdkMetadata can be set by the host SDK after the TelemetryCollector is instantiated.\n */\ngetSDKMetadata_fn = function() {\n  let sdkMetadata = {\n    name: __privateGet(this, _metadata).sdk,\n    version: __privateGet(this, _metadata).sdkVersion\n  };\n  if (typeof window !== \"undefined\" && window.Clerk) {\n    sdkMetadata = { ...sdkMetadata, ...window.Clerk.constructor.sdkMetadata };\n  }\n  return sdkMetadata;\n};\n/**\n * Append relevant metadata from the Clerk singleton to the event payload.\n */\npreparePayload_fn = function(event, payload) {\n  const sdkMetadata = __privateMethod(this, _TelemetryCollector_instances, getSDKMetadata_fn).call(this);\n  return {\n    event,\n    cv: __privateGet(this, _metadata).clerkVersion ?? \"\",\n    it: __privateGet(this, _metadata).instanceType ?? \"\",\n    sdk: sdkMetadata.name,\n    sdkv: sdkMetadata.version,\n    ...__privateGet(this, _metadata).publishableKey ? { pk: __privateGet(this, _metadata).publishableKey } : {},\n    ...__privateGet(this, _metadata).secretKey ? { sk: __privateGet(this, _metadata).secretKey } : {},\n    payload\n  };\n};\n\n// src/telemetry/events/component-mounted.ts\nvar EVENT_COMPONENT_MOUNTED = \"COMPONENT_MOUNTED\";\nvar EVENT_COMPONENT_OPENED = \"COMPONENT_OPENED\";\nvar EVENT_SAMPLING_RATE = 0.1;\nfunction createPrebuiltComponentEvent(event) {\n  return function(component, props, additionalPayload) {\n    return {\n      event,\n      eventSamplingRate: EVENT_SAMPLING_RATE,\n      payload: {\n        component,\n        appearanceProp: Boolean(props?.appearance),\n        baseTheme: Boolean(props?.appearance?.baseTheme),\n        elements: Boolean(props?.appearance?.elements),\n        variables: Boolean(props?.appearance?.variables),\n        ...additionalPayload\n      }\n    };\n  };\n}\nfunction eventPrebuiltComponentMounted(component, props, additionalPayload) {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_MOUNTED)(component, props, additionalPayload);\n}\nfunction eventPrebuiltComponentOpened(component, props, additionalPayload) {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_OPENED)(component, props, additionalPayload);\n}\nfunction eventComponentMounted(component, props = {}) {\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      ...props\n    }\n  };\n}\n\n// src/telemetry/events/method-called.ts\nvar EVENT_METHOD_CALLED = \"METHOD_CALLED\";\nfunction eventMethodCalled(method, payload) {\n  return {\n    event: EVENT_METHOD_CALLED,\n    payload: {\n      method,\n      ...payload\n    }\n  };\n}\n\n// src/telemetry/events/framework-metadata.ts\nvar EVENT_FRAMEWORK_METADATA = \"FRAMEWORK_METADATA\";\nvar EVENT_SAMPLING_RATE2 = 0.1;\nfunction eventFrameworkMetadata(payload) {\n  return {\n    event: EVENT_FRAMEWORK_METADATA,\n    eventSamplingRate: EVENT_SAMPLING_RATE2,\n    payload\n  };\n}\n\nexport {\n  TelemetryCollector,\n  eventPrebuiltComponentMounted,\n  eventPrebuiltComponentOpened,\n  eventComponentMounted,\n  eventMethodCalled,\n  eventFrameworkMetadata\n};\n//# sourceMappingURL=chunk-L6WULBPV.mjs.map", "import {\n  TelemetryCollector,\n  eventComponentMounted,\n  eventFrameworkMetadata,\n  eventMethodCalled,\n  eventPrebuiltComponentMounted,\n  eventPrebuiltComponentOpened\n} from \"./chunk-L6WULBPV.mjs\";\nimport \"./chunk-RWYTRAIK.mjs\";\nimport \"./chunk-G3VP5PJE.mjs\";\nimport \"./chunk-TETGTEI2.mjs\";\nimport \"./chunk-KOH7GTJO.mjs\";\nimport \"./chunk-I6MTSTOF.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  TelemetryCollector,\n  eventComponentMounted,\n  eventFrameworkMetadata,\n  eventMethodCalled,\n  eventPrebuiltComponentMounted,\n  eventPrebuiltComponentOpened\n};\n//# sourceMappingURL=telemetry.mjs.map", "import {\n  createAuthenticateRequest,\n  createBackendApiClient,\n  verifyToken\n} from \"./chunk-H5XWF6TY.mjs\";\nimport {\n  withLegacyReturn\n} from \"./chunk-P263NW7Z.mjs\";\nimport \"./chunk-AT3FJU3M.mjs\";\nimport \"./chunk-5JS2VYLU.mjs\";\n\n// src/index.ts\nimport { TelemetryCollector } from \"@clerk/shared/telemetry\";\nvar verifyToken2 = withLegacyReturn(verifyToken);\nfunction createClerkClient(options) {\n  const opts = { ...options };\n  const apiClient = createBackendApiClient(opts);\n  const requestState = createAuthenticateRequest({ options: opts, apiClient });\n  const telemetry = new TelemetryCollector({\n    ...options.telemetry,\n    publishableKey: opts.publishableKey,\n    secretKey: opts.secretKey,\n    samplingRate: 0.1,\n    ...opts.sdkMetadata ? { sdk: opts.sdkMetadata.name, sdkVersion: opts.sdkMetadata.version } : {}\n  });\n  return {\n    ...apiClient,\n    ...requestState,\n    telemetry\n  };\n}\nexport {\n  createClerkClient,\n  verifyToken2 as verifyToken\n};\n//# sourceMappingURL=index.mjs.map", "import \"../chunk-BUSYA2B4.js\";\nimport { createClerkClient } from \"@clerk/backend\";\nimport {\n  API_URL,\n  API_VERSION,\n  DOMAIN,\n  IS_SATELLITE,\n  PROXY_URL,\n  PUBLISHABLE_KEY,\n  SDK_METADATA,\n  SECRET_KEY,\n  TELEMETRY_DEBUG,\n  TELEMETRY_DISABLED\n} from \"./constants\";\nconst clerkClientDefaultOptions = {\n  secretKey: SECRET_KEY,\n  publishableKey: PUBLISHABLE_KEY,\n  apiUrl: API_URL,\n  apiVersion: API_VERSION,\n  userAgent: `${\"@clerk/nextjs\"}@${\"6.12.12\"}`,\n  proxyUrl: PROXY_URL,\n  domain: DOMAIN,\n  isSatellite: IS_SATELLITE,\n  sdkMetadata: SDK_METADATA,\n  telemetry: {\n    disabled: TELEMETRY_DISABLED,\n    debug: TELEMETRY_DEBUG\n  }\n};\nconst createClerkClientWithOptions = (options) => createClerkClient({ ...clerkClientDefaultOptions, ...options });\nexport {\n  createClerkClientWithOptions\n};\n//# sourceMappingURL=createClerkClient.js.map", "\"use strict\";\nif (process.env.NEXT_RUNTIME === 'edge') {\n    module.exports = require('next/dist/server/route-modules/app-route/module.js');\n} else {\n    if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n        if (process.env.NODE_ENV === 'development') {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo-experimental.runtime.dev.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.dev.js');\n            }\n        } else {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo-experimental.runtime.prod.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.prod.js');\n            }\n        }\n    } else {\n        if (process.env.NODE_ENV === 'development') {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo.runtime.dev.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route.runtime.dev.js');\n            }\n        } else {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo.runtime.prod.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route.runtime.prod.js');\n            }\n        }\n    }\n}\n\n//# sourceMappingURL=module.compiled.js.map"], "names": ["validateAuthentication", "userId", "auth", "success", "error", "response", "NextResponse", "json", "status", "user", "currentUser", "email", "emailAddresses", "emailAddress", "firstName", "lastName", "console", "log", "id", "clerkId", "undefined", "createErrorResponse", "message", "details", "errorResponse", "timestamp", "Date", "toISOString", "createSuccessResponse", "data", "withAuthentication", "handler", "request", "context", "authResult", "PayloadClient", "constructor", "makeRequest", "endpoint", "options", "url", "requestOptions", "method", "body", "params", "backendUrl", "searchParams", "URLSearchParams", "Object", "entries", "for<PERSON>ach", "key", "value", "append", "toString", "headers", "JSON", "stringify", "fetch", "ok", "errorText", "text", "statusText", "getAppointments", "depth", "getAppointment", "createAppointment", "updateAppointment", "deleteAppointment", "getPatients", "queryParams", "search", "getPatient", "createPatient", "updatePatient", "deletePatient", "getTreatments", "getTreatment", "createTreatment", "updateTreatment", "deleteTreatment", "getUsers", "updateUser", "syncCurrentUser", "syncUser", "userData", "existingUsers", "where", "equals", "limit", "docs", "length", "role", "lastLogin", "existingUser", "updatedUser", "getPatientInteractions", "getPatientInteraction", "createPatientInteraction", "updatePatientInteraction", "deletePatientInteraction", "getPatientTasks", "getPatientTask", "createPatientTask", "updatePatientTask", "deletePatientTask", "getPatientInteractionsByPatient", "patientId", "getPatientTasksByPatient", "getPatientTimeline", "createPayloadClient"], "sourceRoot": ""}