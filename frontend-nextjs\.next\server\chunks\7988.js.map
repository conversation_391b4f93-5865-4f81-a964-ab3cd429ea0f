{"version": 3, "file": "7988.js", "mappings": "gcAIA,4BAA0D,CAC1D,cACA,eACA,iCAEA,CAAC,EAAC,IACF,EAAoB,EAAQ,KAAsC,YAAvC,kuUGDpB,SAAS,EAAkB,GAAkD,IATpF,EAyBE,OAdM,OAAkB,GADP,8BAAyB,CAAC,CACnB,CAAS,KAAT,EAAa,WAAW,eAczC,EAAM,EAdqD,MAepE,iCCvBO,SAASA,EAAyBC,CAAc,EACrD,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAQE,MAAM,CAAED,IAAK,CACvC,IAAME,EAASH,CAAO,CAACC,EAAE,CACzB,GAAsB,YAAY,OAAvBE,EACT,MAAM,qBAEL,CAFK,MACJ,CAAC,2DAA2D,EAAE,OAAOA,EAAO;AAAA,oEAAuE,CAAC,EADhJ,+DAEN,EAEJ,CACF,6BATgBJ,qCAAAA,46BGOZK,yIAEYC,mBAAmB,mBAAnBA,GA0CAC,OAAO,mBAAPA,GAXAC,OAAO,mBAAPA,GA6HMC,sBAAsB,mBAAtBA,GAxCNC,gCAAgC,mBAAhCA,GApBAC,kBAAkB,mBAAlBA,GAnCAC,8BAA8B,mBAA9BA,GAzCAC,kBAAkB,mBAAlBA,aA1Be,WACE,WACA,OAI1B,SAASP,EACdQ,CAAiD,EAEjD,IAAMC,EAAQ,IAAIC,WAAWF,GACvBG,EAAMF,EAAMG,UAAU,CAK5B,GAAID,EAAM,MACR,CADe,MACRE,OAAOC,YAAY,CAACC,KAAK,CAAC,KAAMN,GAGzC,IAAIO,EAAS,GACb,IAAK,IAAIpB,EAAI,EAAGA,EAAIe,EAAKf,IAAK,GAClBiB,OAAOC,YAAY,CAACL,CAAK,CAACb,EAAE,EAExC,OAAOoB,CACT,CAEO,SAAST,EAAmBS,CAAc,EAC/C,IAAML,EAAMK,EAAOnB,MAAM,CACnBoB,EAAM,IAAIP,WAAWC,GAE3B,IAAK,IAAIf,EAAI,EAAGA,EAAIe,EAAKf,IAAK,CACzB,CAACA,EAAE,CAAGoB,EAAOE,UAAU,CAACtB,GAG7B,OAAOqB,CACT,CAEO,SAASf,EAAQiB,CAAc,CAAEC,CAAc,CAAEC,CAAgB,EACtE,OAAOC,OAAOC,MAAM,CAACrB,OAAO,CAC1B,CACEsB,KAAM,UACNJ,IACF,EACAD,EACAE,EAEJ,CAEO,SAASpB,EAAQkB,CAAc,CAAEC,CAAc,CAAEC,CAAgB,EACtE,OAAOC,OAAOC,MAAM,CAACtB,OAAO,CAC1B,CACEuB,KAAM,aACNJ,CACF,EACAD,EACAE,EAEJ,CAMA,IAAMI,EAAoCC,OAAOC,GAAG,CAClD,gCAGK,SAASrB,EAA+B,MAC7CsB,CAAI,yBACJC,CAAuB,uBACvBC,CAAqB,iBACrBC,CAAe,CAYhB,MAEyCC,EAAxC,IAAMC,EAEL,MAFKA,CAAAA,EAAkCD,UAAU,CAChDP,EAAAA,EACD,OAFuCO,EAErCC,+BAA+B,WAKxB,CAACR,EAAkC,CAAG,CAC9CQ,gCAAiC,CAC/B,GAAGA,CAA+B,CAClC,CAACC,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBN,GAAM,CAAEC,CAC5B,wBACAC,kBACAC,CACF,CACF,CAEO,SAAS1B,IACd,IAAM8B,EAAkCH,UAAkB,CACxDP,EACD,CAUD,GAAI,CAACU,EACH,MAAM,qBAA0D,CAA1D,EAD6B,EACzBC,EAAAA,cAAc,CAAC,wCAAnB,+DAAyD,GAGjE,OAAOD,EAA+BJ,eAAe,CAGhD,SAAS3B,IACd,IAAM+B,EAAkCH,UAAkB,CACxDP,EACD,CAMD,GAAI,CAACU,EACH,MAAM,qBAA0D,CAA1D,EAD6B,EACzBC,EAAAA,cAAc,CAAC,wCAAnB,+DAAyD,GAGjE,GAAM,iCAAEH,CAA+B,CAAE,CAAGE,EACtCE,EAAYC,EAAAA,gBAAgB,CAACC,QAAQ,GAE3C,GAAI,CAACF,EASIG,KAkDTP,EAlDuCA,EATvB,IA+DVQ,EAA2BC,OAAOC,MAAM,CAC5CV,GAGIW,EAA+D,CACnEC,CAPD,aAOgB,CAAC,EAChBC,qBAAsB,CAAC,EACvBC,iBAAkB,CAAC,CACrB,EAEA,IAAK,IAAMlB,KAA2BY,EACpCG,EAA8BC,aAAa,CAAG,CAC5C,GAAGD,EAA8BC,CAF2B,YAEd,CAC9C,GAAGhB,EAAwBgB,aAAa,EAE1CD,EAA8BE,oBAAoB,CAAG,CACnD,GAAGF,EAA8BE,oBAAoB,CACrD,GAAGjB,EAAwBiB,oBAAoB,EAEjDF,EAA8BG,gBAAgB,CAAG,CAC/C,GAAGH,EAA8BG,gBAAgB,CACjD,GAAGlB,EAAwBkB,gBAAgB,EAI/C,OAAOH,CA/EgCX,CAGvC,IAAMJ,EACJI,CAA+B,CAACI,EAAUW,KAAK,CAAC,CAElD,GAAI,CAACnB,EACH,MAAM,iBADsB,IAG3B,CAFK,IAAIO,EAAAA,cAAc,CACtB,CAAC,sCAAsC,EAAEC,EAAUW,KAAK,CAAC,CAAC,CAAC,EADvD,+DAEN,GAGF,OAAOnB,CACT,CAEO,eAAe1B,IACpB,GAAIJ,EACF,OAAOA,EAGT,IAAMoC,EAAkCH,SAJV,CAI4B,CACxDP,EACD,CAID,GAAI,CAACU,EACH,MAAM,qBAA0D,CAA1D,EAD6B,EACzBC,EAAAA,cAAc,CAAC,wCAAnB,+DAAyD,GAGjE,IAAMa,EACJC,QAAQC,GAAG,CAACC,kCAAkC,EAC9CjB,EAA+BL,qBAAqB,CAACuB,aAAa,CAEpE,QAAeC,IAAXL,EACF,KADwB,CAClB,qBAA+D,CAA/D,IAAIb,EAAAA,cAAc,CAAC,6CAAnB,+DAA8D,GAWtE,OAAOrC,EARoB,MAAMuB,OAAOC,MAAM,CAACgC,SAAS,CACtD,MACAhD,EAAmBiD,KAAKP,IACxB,UACA,GACA,CAAC,UAAW,UAAU,CAI1B,8xOCtMA,MAAqB,eAAiB,EAAG,4BAA8B,4BCAvE,sBACA,oBAEA,UADA,cACA,UAIA,OAFA,0BADA,0CAEA,gDACA,WACA,CACA,mBACA,sBACA,MACA,SAAc,IAAwB,CAEtC,eAFkC,EAAE,EAAE,MAEtC,wCACA,aACA,SAAY,EAAoB,EAAE,EAAK,EAEvC,wBAKA,EAJA,IAAO,GAAa,CACpB,OAEA,gBAEA,IACA,GACA,uBAA2D,GAE3D,CAAI,MACJ,QACA,CACA,QACA,CC3BA,eAAeQ,EAAwBC,CAAI,EACzC,GAAM,UAAEC,CAAQ,gBAAEC,CAAc,WAAEC,CAAS,WAAEC,CAAS,CAAE,CAAGJ,EACrDK,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GAC3BC,EAAU,IAAIC,QAAQ,0BAA2B,CAAEC,QAAS,MAAMA,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,EAAG,GAC5EC,EAAU,MAAMC,EAAsB,IAC1C,IAAIC,EACJ,OAAO,EAFkCD,KAEjCC,EAAKP,EAAYQ,GAAG,CAAC/C,EAAAA,CAAI,CAAa,KAAK,EAAI8C,EAAGE,KAC5D,GACMC,EAAW,CAAY,MAAXL,EAAkB,KAAK,EAAIA,EAAQR,cAAAA,IAAoBA,EACnEc,EAAW,CAAY,MAAXN,EAAkB,KAAK,EAAIA,EAAQP,SAAAA,IAAeA,EACpE,KAAIY,IAAYC,CAAAA,GAAU,CAG1BX,EAAYY,GAAG,CAAC,MAAMC,IAAwBC,KAAKC,SAAS,CAAC,CAAnBF,SAAqBjB,iBAAUC,YAAgBC,CAAU,GAAI,CACrGkB,OAAQ,GACRC,SAAU,EACZ,GACIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAqBA,CAAChB,IAAU,WAClCiB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,8BAA8B,EAAEpB,EAAAA,CAAW,CAAEqB,EAAAA,YAAYA,CAACC,OAAO,CAI/E,CACA,eAAeC,IACb,GAAI,CAACC,EAAAA,CAAaA,CAChB,CADkB,MACX,KAET,IAAMC,EAAS,MAAM,+BAAmC,CAACC,IAAI,CAAC,GAAOC,EAAEC,mBAAmB,IAAIC,KAAK,CAAC,IAAM,MAC1G,GAAI,CAACJ,EAEH,MAFW,CACXK,EAAaC,UAADD,qBAAgC,GACrC,KAET,GAAM,uBAAEE,CAAqB,0BAAEC,CAAwB,CAAE,CAAG,MAAM,+BAAwC,OAC1GD,GAAyCA,EAAJ,GAA6B,CAAC,CACjEE,SAAUT,EAAO3B,cAAc,CAC/BqC,IAAKF,EAAyBR,EAChC,GACA,GAAM,CAAE5B,UAAQ,gBAAEC,CAAc,WAAEC,CAAS,YAAEqC,CAAU,CAAE,CAAGX,EAK5D,MAJK,CAAC,MAAMvB,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,EAAAA,CAAC,CAAGW,GAAG,CAAC,MAAMC,IAAwBC,KAAKC,SAAS,CAAC,CAAnBF,SAAqBjB,iBAAUC,YAAgBC,CAAU,GAAI,CAChHkB,QAAQ,EACRC,UAAU,CACZ,GACO,UACLrB,iBACAC,EACAsC,YACF,CACF,CACA,eAAeC,IACRb,EAAAA,CAAaA,EAAE,MAGd,+BAAmC,CAACE,IAAI,CAAC,GAAOC,EAAEW,aAAa,IAAIT,KAAK,CAAC,KAC/E,EAEF,iBAEEN,EACAc,EACA1C,IAFA4B,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,CAAAA,EAAAA,6CAAAA,MACAc,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,CAAAA,EAAAA,6CAAAA,MACA1C,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,CAAAA,EAAAA,6CAAAA,uBCjEF,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAKF,cACA,0CACA,cACA,SACK,CACL,EACA,GACA,kCACA,QACA,CAAK,CACL,kCACA,QACA,CACA,CAAC,EACD,EAAQ,KAAa,EACrB,MAAoB,EAAQ,EADrB,GAC2D,EAClE,EAAoB,EAAQ,KAAsC,CADvC,CAE3B,EAA8B,EAAQ,KAAyC,CADpD,CAE3B,EAAyB,EAAQ,KAAoB,CADhB,CAErC,EAAsC,EAAQ,KAAoC,CADlD,CAEhC,EAA0B,EAAQ,KAAqB,CADV,CAE7C,UADiC,CAEjC,GACA,0BACA,SACA,CACA,EALsD,EAAQ,KAAO,GAOrE,SAP6D,SAQ7D,kBAGA,sBACA,2CACA,cACA,6HACA,YACA,cACA,eACA,CAAS,EAGT,cACA,gBACA,cACA,2FACA,oBACA,4GACA,aACA,cACA,eACA,CAAS,EAET,wBACA,CAIA,sBACA,2CACA,cACA,6HACA,YACA,cACA,eACA,CAAS,EAGT,yBACA,2DACA,0CACA,yCACA,2CACA,CACA,+CACA,kBAAY,GAAgB,yCAG5B,UACA,6BACA,SACA,oCACA,sFAEA,+DACA,SACA,WACA,wBAIA,IAGA,KAGA,kDACA,CACA,CAAK,EAGL,GACA,KAOA,QAEA,MACA,cAEA,2CACA,oCACA,MACA,6FACA,KACA,SAEA,+CACA,wBACA,mBAGA,OAFA,qBACA,uCACA,CACA,CAAC,EACD,sBACA,IAEA,EAFA,UACA,oCAEA,MACA,gDACA,uCACA,oCACA,iGAEA,uBACA,eACA,qBACA,uCAEA,EAAM,IACN,eAEA,yBAAY,sBAAyC,yCA6BrD,OA3BA,yDACA,SACA,uBACA,sCAGA,uBACA,UAEA,uDACA,OACA,CAAqB,EAGrB,SAEA,CACA,CAAK,GACL,wBAIA,mBACA,YACA,0CACA,CACA,CAAK,CAEL,2HCjLA,IAAM,EAAyB,iBAAM,CAA2D,IAAI,EAE7F,SAAS,EAAwB,aACtC,WACA,EACF,EAGG,OACM,kBAAC,EAAuB,SAAvB,CAAgC,MAAO,GAAc,EAC/D,CAsCO,KAvCiE,IAuCxD,IACd,IAAM,EAAgB,WADa,GACb,CAAS,CAAC,EAC1B,EAAmB,cAAM,CAAW,GAEtC,EAAe,SACf,GAAoB,KAHwC,IAG9B,GAChC,GAAe,OAAM,CAAI,EAAgB,EADS,GASzC,QAAO,CAAC,EAIV,CALY,EAKZ,KAAc,CAAC,EAI1B,UAJsC,+iDCrEtC,qBACA,MAAc,UAAY,IAC1B,CAAU,eAAgB,OAAc,GACxC,SAAU,cAAoB,CAAE,OAAU,GACpC,QAAuB,IAG3B,aAAe,MACjB,sBAGA,MAFA,OAKA,0BACA,OACA,WAA2C,EAAK,sBAAwB,EAAK,6BAC7E,MACA;AACA,cAAc,EAAU;;AAExB,UAAU,EAAK;AACf,qEAAqE,EAAY,wCAAwC,EAAU;;AAEnI,UAAU,EAAU,sEAAsE,EAAK;AAC/F,0NAA0N,EAAK;AAC/N,EAEA,EAuBA,OAtBA,EACA,oCACA,IAGA,eAKA,EAHA,GADA,aACA,eAIA,IACA,SAAyB,uBAAuB,EAAE,yBAAyB,GAAG,EAAU,wBAAwB,WAAW,EAC3H,iBAAmC,gBAAmB,CACtD,CAAU,MACV,CACA,iCACA,IAEA,KAGA,KACA,aACA,SAEA,CACA,CAAG,KACH,EC3DA,OACA,MAAkB,UAAY,GAC9B,CAAU,eAAc,CAAE,MAAc,GACxC,KACA,aACA,sBAGA,OADA,kDACA,UAGA,MAAsB,qBAAsC,EACxC,mBAAoC,EACxD,UACA,2BACA,uBAAwD,+CACxD,WAGA,eAA0B,wCAAuE,EAFjG,WChBA,qBACA,MAAe,IACf,EAAuB,QAAe,WAAyB,EAAM,EAErE,OADE,EAAuB,iBACzB,CACA,CCqBO,EDvBkB,ECuBZ,EAAsC,OAAO,OACxD,GACS,kBAAC,IAAe,CAAf,CAAiB,GAAG,EAA8B,cAAe,EAAK,CAAG,EAAH,CAE9E,GAAG,IAFsD,GAShD,EAAsD,OAAO,OACvE,GACQ,kBAAC,IAAuB,CAAvB,CAAyB,GAAG,EAA8B,sBAAuB,EAAK,CAAG,EAAhC,CAEjE,GAAG,OAGM,EAAU,GACd,kBAAC,IAAU,CAAV,CAAY,GAAG,EAA8B,SAAU,GAAO,EAAK,CAAG,EAAH,EAGvD,GACb,KAJ6C,YAI7C,CAAC,IAAU,CAAV,CAAY,GAAG,EAA8B,SAAU,GAAO,EAAK,CAAG,EAAH,UAAvB", "sources": ["webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js", "webpack://next-shadcn-dashboard-starter/../../../src/client-boundary/controlComponents.ts", "webpack://next-shadcn-dashboard-starter/../../../src/client-boundary/hooks.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/app-router/client/keyless-cookie-sync.tsx", "webpack://next-shadcn-dashboard-starter/../../../../../src/build/webpack/loaders/next-flight-loader/action-validate.ts", "webpack://next-shadcn-dashboard-starter/../../../../src/app-router/client/ClerkProvider.tsx", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+clerk-react@5.25.5_r_56537f0715afcaf96bac8665918531ef/node_modules/@clerk/clerk-react/dist/errors.mjs", "webpack://next-shadcn-dashboard-starter/../../../src/server/app-render/encryption-utils.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/errorThrower.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/keyless.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/app-render/encryption.js", "webpack://next-shadcn-dashboard-starter/../../../src/client-boundary/PromisifiedAuthProvider.tsx", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks/useEnforceCatchAllRoute.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks/usePathnameWithoutCatchAll.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks/useEnforceRoutingProps.js", "webpack://next-shadcn-dashboard-starter/../../../src/client-boundary/uiComponents.tsx"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */ \"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"registerServerReference\", {\n    enumerable: true,\n    get: function() {\n        return _serveredge.registerServerReference;\n    }\n});\nconst _serveredge = require(\"react-server-dom-webpack/server.edge\");\n\n//# sourceMappingURL=server-reference.js.map", "'use client';\n\nexport {\n  <PERSON>Loa<PERSON>,\n  ClerkLoading,\n  SignedOut,\n  SignedIn,\n  Protect,\n  RedirectToSignIn,\n  RedirectToSignUp,\n  RedirectToUserProfile,\n  AuthenticateWithRedirectCallback,\n  RedirectToCreateOrganization,\n  RedirectToOrganizationProfile,\n} from '@clerk/clerk-react';\n\nexport { MultisessionAppSupport } from '@clerk/clerk-react/internal';\n", "'use client';\n\nexport {\n  useClerk,\n  useEmailLink,\n  useOrganization,\n  useOrganizationList,\n  useSession,\n  useSessionList,\n  useSignIn,\n  useSignUp,\n  useUser,\n  useReverification,\n} from '@clerk/clerk-react';\n\nexport {\n  isClerkAPIResponseError,\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isMetamaskError,\n  isReverificationCancelledError,\n  EmailLinkErrorCode,\n  EmailLinkErrorCodeStatus,\n} from '@clerk/clerk-react/errors';\n\nexport { usePromisifiedAuth as useAuth } from './PromisifiedAuthProvider';\n", "'use client';\n\nimport type { AccountlessApplication } from '@clerk/backend';\nimport { useSelectedLayoutSegments } from 'next/navigation';\nimport type { PropsWithChildren } from 'react';\nimport { useEffect } from 'react';\n\nimport { canUseKeyless } from '../../utils/feature-flags';\n\nexport function KeylessCookieSync(props: PropsWithChildren<AccountlessApplication>) {\n  const segments = useSelectedLayoutSegments();\n  const isNotFoundRoute = segments[0]?.startsWith('/_not-found') || false;\n\n  useEffect(() => {\n    if (canUseKeyless && !isNotFoundRoute) {\n      void import('../keyless-actions.js').then(m =>\n        m.syncKeylessConfigAction({\n          ...props,\n          // Preserve the current url and return back, once keys are synced in the middleware\n          returnUrl: window.location.href,\n        }),\n      );\n    }\n  }, [isNotFoundRoute]);\n\n  return props.children;\n}\n", "// This function ensures that all the exported values are valid server actions,\n// during the runtime. By definition all actions are required to be async\n// functions, but here we can only check that they are functions.\nexport function ensureServerEntryExports(actions: any[]) {\n  for (let i = 0; i < actions.length; i++) {\n    const action = actions[i]\n    if (typeof action !== 'function') {\n      throw new Error(\n        `A \"use server\" file can only export async functions, found ${typeof action}.\\nRead more: https://nextjs.org/docs/messages/invalid-use-server-value`\n      )\n    }\n  }\n}\n", "'use client';\nimport { <PERSON><PERSON><PERSON><PERSON> as ReactClerkProvider } from '@clerk/clerk-react';\nimport { inBrowser } from '@clerk/shared/browser';\nimport { logger } from '@clerk/shared/logger';\nimport dynamic from 'next/dynamic';\nimport { useRouter } from 'next/navigation';\nimport nextPackage from 'next/package.json';\nimport React, { useEffect, useTransition } from 'react';\n\nimport { useSafeLayoutEffect } from '../../client-boundary/hooks/useSafeLayoutEffect';\nimport { ClerkNextOptionsProvider, useClerkNextOptions } from '../../client-boundary/NextOptionsContext';\nimport type { NextClerkProviderProps } from '../../types';\nimport { ClerkJSScript } from '../../utils/clerk-js-script';\nimport { canUseKeyless } from '../../utils/feature-flags';\nimport { mergeNextClerkPropsWithEnv } from '../../utils/mergeNextClerkPropsWithEnv';\nimport { RouterTelemetry } from '../../utils/router-telemetry';\nimport { isNextWithUnstableServerActions } from '../../utils/sdk-versions';\nimport { invalidateCacheAction } from '../server-actions';\nimport { useAwaitablePush } from './useAwaitablePush';\nimport { useAwaitableReplace } from './useAwaitableReplace';\n\n/**\n * LazyCreateKeylessApplication should only be loaded if the conditions below are met.\n * Note: Using lazy() with Suspense instead of dynamic is not possible as React will throw a hydration error when `ClerkProvider` wraps `<html><body>...`\n */\nconst LazyCreateKeylessApplication = dynamic(() =>\n  import('./keyless-creator-reader.js').then(m => m.KeylessCreatorOrReader),\n);\n\nconst NextClientClerkProvider = (props: NextClerkProviderProps) => {\n  if (isNextWithUnstableServerActions) {\n    const deprecationWarning = `Clerk:\\nYour current Next.js version (${nextPackage.version}) will be deprecated in the next major release of \"@clerk/nextjs\". Please upgrade to next@14.1.0 or later.`;\n    if (inBrowser()) {\n      logger.warnOnce(deprecationWarning);\n    } else {\n      logger.logOnce(`\\n\\x1b[43m----------\\n${deprecationWarning}\\n----------\\x1b[0m\\n`);\n    }\n  }\n\n  const { __unstable_invokeMiddlewareOnAuthStateChange = true, children } = props;\n  const router = useRouter();\n  const push = useAwaitablePush();\n  const replace = useAwaitableReplace();\n  const [isPending, startTransition] = useTransition();\n\n  // Avoid rendering nested ClerkProviders by checking for the existence of the ClerkNextOptions context provider\n  const isNested = Boolean(useClerkNextOptions());\n  if (isNested) {\n    return props.children;\n  }\n\n  useEffect(() => {\n    if (!isPending) {\n      window.__clerk_internal_invalidateCachePromise?.();\n    }\n  }, [isPending]);\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onBeforeSetActive = intent => {\n      /**\n       * We need to invalidate the cache in case the user is navigating to a page that\n       * was previously cached using the auth state that was active at the time.\n       *\n       *  We also need to await for the invalidation to happen before we navigate,\n       * otherwise the navigation will use the cached page.\n       *\n       * For example, if we did not invalidate the flow, the following scenario would be broken:\n       * - The middleware is configured in such a way that it redirects you back to the same page if a certain condition is true (eg, you need to pick an org)\n       * - The user has a <Link href=/> component in the page\n       * - The UB is mounted with afterSignOutUrl=/\n       * - The user clicks the Link. A nav to / happens, a 307 to the current page is returned so a navigation does not take place. The / navigation is now cached as a 307 to the current page\n       * - The user clicks sign out\n       * - We call router.refresh()\n       * - We navigate to / but its cached and instead, we 'redirect' to the current page\n       *\n       *  For more information on cache invalidation, see:\n       * https://nextjs.org/docs/app/building-your-application/caching#invalidation-1\n       */\n      return new Promise(resolve => {\n        window.__clerk_internal_invalidateCachePromise = resolve;\n\n        const nextVersion = window?.next?.version || '';\n\n        // ATTENTION: Avoid using wrapping code with `startTransition` on versions >= 14\n        // otherwise the fetcher of `useReverification()` will be pending indefinitely when called within `startTransition`.\n        if (nextVersion.startsWith('13')) {\n          startTransition(() => {\n            router.refresh();\n          });\n        }\n        // On Next.js v15 calling a server action that returns a 404 error when deployed on Vercel is prohibited, failing with 405 status code.\n        // When a user transitions from \"signed in\" to \"singed out\", we clear the `__session` cookie, then we call `__unstable__onBeforeSetActive`.\n        // If we were to call `invalidateCacheAction` while the user is already signed out (deleted cookie), any page protected by `auth.protect()`\n        // will result to the server action returning a 404 error (this happens because server actions inherit the protection rules of the page they are called from).\n        // SOLUTION:\n        // To mitigate this, since the router cache on version 15 is much less aggressive, we can treat this as a noop and simply resolve the promise.\n        // Once `setActive` performs the navigation, `__unstable__onAfterSetActive` will kick in and perform a router.refresh ensuring shared layouts will also update with the correct authentication context.\n        else if (nextVersion.startsWith('15') && intent === 'sign-out') {\n          resolve(); // noop\n        } else {\n          void invalidateCacheAction().then(() => resolve());\n        }\n      });\n    };\n\n    window.__unstable__onAfterSetActive = () => {\n      if (__unstable_invokeMiddlewareOnAuthStateChange) {\n        return router.refresh();\n      }\n    };\n  }, []);\n\n  const mergedProps = mergeNextClerkPropsWithEnv({\n    ...props,\n    // @ts-expect-error Error because of the stricter types of internal `push`\n    routerPush: push,\n    // @ts-expect-error Error because of the stricter types of internal `replace`\n    routerReplace: replace,\n  });\n\n  return (\n    <ClerkNextOptionsProvider options={mergedProps}>\n      <ReactClerkProvider {...mergedProps}>\n        <RouterTelemetry />\n        <ClerkJSScript router='app' />\n        {children}\n      </ReactClerkProvider>\n    </ClerkNextOptionsProvider>\n  );\n};\n\nexport const ClientClerkProvider = (props: NextClerkProviderProps & { disableKeyless?: boolean }) => {\n  const { children, disableKeyless = false, ...rest } = props;\n  const safePublishableKey = mergeNextClerkPropsWithEnv(rest).publishableKey;\n\n  if (safePublishableKey || !canUseKeyless || disableKeyless) {\n    return <NextClientClerkProvider {...rest}>{children}</NextClientClerkProvider>;\n  }\n\n  return (\n    <LazyCreateKeylessApplication>\n      <NextClientClerkProvider {...rest}>{children}</NextClientClerkProvider>\n    </LazyCreateKeylessApplication>\n  );\n};\n", "import \"./chunk-OANWQR3B.mjs\";\n\n// src/errors.ts\nimport {\n  isClerkAPIResponseError,\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isMetamaskError,\n  isReverificationCancelledError,\n  EmailLinkErrorCode,\n  EmailLinkErrorCodeStatus\n} from \"@clerk/shared/error\";\nexport {\n  EmailLinkErrorCode,\n  EmailLinkErrorCodeStatus,\n  isClerkAPIResponseError,\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isMetamaskError,\n  isReverificationCancelledError\n};\n//# sourceMappingURL=errors.mjs.map", "import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport type {\n  ClientReferenceManifest,\n  ClientReferenceManifestForRsc,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { workAsyncStorage } from './work-async-storage.external'\n\nlet __next_loaded_action_key: CryptoKey\n\nexport function arrayBufferToString(\n  buffer: ArrayBuffer | Uint8Array<ArrayBufferLike>\n) {\n  const bytes = new Uint8Array(buffer)\n  const len = bytes.byteLength\n\n  // @anonrig: V8 has a limit of 65535 arguments in a function.\n  // For len < 65535, this is faster.\n  // https://github.com/vercel/next.js/pull/56377#pullrequestreview-1656181623\n  if (len < 65535) {\n    return String.fromCharCode.apply(null, bytes as unknown as number[])\n  }\n\n  let binary = ''\n  for (let i = 0; i < len; i++) {\n    binary += String.fromCharCode(bytes[i])\n  }\n  return binary\n}\n\nexport function stringToUint8Array(binary: string) {\n  const len = binary.length\n  const arr = new Uint8Array(len)\n\n  for (let i = 0; i < len; i++) {\n    arr[i] = binary.charCodeAt(i)\n  }\n\n  return arr\n}\n\nexport function encrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.encrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\nexport function decrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.decrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\n// This is a global singleton that is used to encode/decode the action bound args from\n// the closure. This can't be using a AsyncLocalStorage as it might happen on the module\n// level. Since the client reference manifest won't be mutated, let's use a global singleton\n// to keep it.\nconst SERVER_ACTION_MANIFESTS_SINGLETON = Symbol.for(\n  'next.server.action-manifests'\n)\n\nexport function setReferenceManifestsSingleton({\n  page,\n  clientReferenceManifest,\n  serverActionsManifest,\n  serverModuleMap,\n}: {\n  page: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  serverActionsManifest: DeepReadonly<ActionManifest>\n  serverModuleMap: {\n    [id: string]: {\n      id: string\n      chunks: string[]\n      name: string\n    }\n  }\n}) {\n  // @ts-expect-error\n  const clientReferenceManifestsPerPage = globalThis[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ]?.clientReferenceManifestsPerPage as\n    | undefined\n    | DeepReadonly<Record<string, ClientReferenceManifest>>\n\n  // @ts-expect-error\n  globalThis[SERVER_ACTION_MANIFESTS_SINGLETON] = {\n    clientReferenceManifestsPerPage: {\n      ...clientReferenceManifestsPerPage,\n      [normalizeAppPath(page)]: clientReferenceManifest,\n    },\n    serverActionsManifest,\n    serverModuleMap,\n  }\n}\n\nexport function getServerModuleMap() {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverModuleMap: {\n      [id: string]: {\n        id: string\n        chunks: string[]\n        name: string\n      }\n    }\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  return serverActionsManifestSingleton.serverModuleMap\n}\n\nexport function getClientReferenceManifestForRsc(): DeepReadonly<ClientReferenceManifestForRsc> {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    clientReferenceManifestsPerPage: DeepReadonly<\n      Record<string, ClientReferenceManifest>\n    >\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const { clientReferenceManifestsPerPage } = serverActionsManifestSingleton\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workStore) {\n    // If there's no work store defined, we can assume that a client reference\n    // manifest is needed during module evaluation, e.g. to create a server\n    // action using a higher-order function. This might also use client\n    // components which need to be serialized by Flight, and therefore client\n    // references need to be resolvable. To make this work, we're returning a\n    // merged manifest across all pages. This is fine as long as the module IDs\n    // are not page specific, which they are not for Webpack. TODO: Fix this in\n    // Turbopack.\n    return mergeClientReferenceManifests(clientReferenceManifestsPerPage)\n  }\n\n  const clientReferenceManifest =\n    clientReferenceManifestsPerPage[workStore.route]\n\n  if (!clientReferenceManifest) {\n    throw new InvariantError(\n      `Missing Client Reference Manifest for ${workStore.route}.`\n    )\n  }\n\n  return clientReferenceManifest\n}\n\nexport async function getActionEncryptionKey() {\n  if (__next_loaded_action_key) {\n    return __next_loaded_action_key\n  }\n\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverActionsManifest: DeepReadonly<ActionManifest>\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const rawKey =\n    process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY ||\n    serverActionsManifestSingleton.serverActionsManifest.encryptionKey\n\n  if (rawKey === undefined) {\n    throw new InvariantError('Missing encryption key for Server Actions')\n  }\n\n  __next_loaded_action_key = await crypto.subtle.importKey(\n    'raw',\n    stringToUint8Array(atob(rawKey)),\n    'AES-GCM',\n    true,\n    ['encrypt', 'decrypt']\n  )\n\n  return __next_loaded_action_key\n}\n\nfunction mergeClientReferenceManifests(\n  clientReferenceManifestsPerPage: DeepReadonly<\n    Record<string, ClientReferenceManifest>\n  >\n): ClientReferenceManifestForRsc {\n  const clientReferenceManifests = Object.values(\n    clientReferenceManifestsPerPage as Record<string, ClientReferenceManifest>\n  )\n\n  const mergedClientReferenceManifest: ClientReferenceManifestForRsc = {\n    clientModules: {},\n    edgeRscModuleMapping: {},\n    rscModuleMapping: {},\n  }\n\n  for (const clientReferenceManifest of clientReferenceManifests) {\n    mergedClientReferenceManifest.clientModules = {\n      ...mergedClientReferenceManifest.clientModules,\n      ...clientReferenceManifest.clientModules,\n    }\n    mergedClientReferenceManifest.edgeRscModuleMapping = {\n      ...mergedClientReferenceManifest.edgeRscModuleMapping,\n      ...clientReferenceManifest.edgeRscModuleMapping,\n    }\n    mergedClientReferenceManifest.rscModuleMapping = {\n      ...mergedClientReferenceManifest.rscModuleMapping,\n      ...clientReferenceManifest.rscModuleMapping,\n    }\n  }\n\n  return mergedClientReferenceManifest\n}\n", "import \"../chunk-BUSYA2B4.js\";\nimport { buildErrorThrower } from \"@clerk/shared/error\";\nconst errorThrower = buildErrorThrower({ packageName: \"@clerk/nextjs\" });\nexport {\n  errorThrower\n};\n//# sourceMappingURL=errorThrower.js.map", "import \"../chunk-BUSYA2B4.js\";\nimport { canUseKeyless } from \"../utils/feature-flags\";\nconst keylessCookiePrefix = `__clerk_keys_`;\nasync function hashString(str) {\n  const encoder = new TextEncoder();\n  const data = encoder.encode(str);\n  const hashBuffer = await crypto.subtle.digest(\"SHA-256\", data);\n  const hashArray = Array.from(new Uint8Array(hashBuffer));\n  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, \"0\")).join(\"\");\n  return hashHex.slice(0, 16);\n}\nasync function getKeylessCookieName() {\n  const PATH = process.env.PWD;\n  if (!PATH) {\n    return `${keylessCookiePrefix}${0}`;\n  }\n  const lastThreeDirs = PATH.split(\"/\").filter(Boolean).slice(-3).reverse().join(\"/\");\n  const hash = await hashString(lastThreeDirs);\n  return `${keylessCookiePrefix}${hash}`;\n}\nasync function getKeylessCookieValue(getter) {\n  if (!canUseKeyless) {\n    return void 0;\n  }\n  const keylessCookieName = await getKeylessCookieName();\n  let keyless;\n  try {\n    if (keylessCookieName) {\n      keyless = JSON.parse(getter(keylessCookieName) || \"{}\");\n    }\n  } catch {\n    keyless = void 0;\n  }\n  return keyless;\n}\nexport {\n  getKeylessCookieName,\n  getKeylessCookieValue\n};\n//# sourceMappingURL=keyless.js.map", "\"use server\";\nimport { cookies, headers } from \"next/headers\";\nimport { redirect, RedirectType } from \"next/navigation\";\nimport { errorThrower } from \"../server/errorThrower\";\nimport { detectClerkMiddleware } from \"../server/headers-utils\";\nimport { getKeylessCookieName, getKeylessCookieValue } from \"../server/keyless\";\nimport { canUseKeyless } from \"../utils/feature-flags\";\nasync function syncKeylessConfigAction(args) {\n  const { claimUrl, publishableKey, secretKey, returnUrl } = args;\n  const cookieStore = await cookies();\n  const request = new Request(\"https://placeholder.com\", { headers: await headers() });\n  const keyless = await getKeylessCookieValue((name) => {\n    var _a;\n    return (_a = cookieStore.get(name)) == null ? void 0 : _a.value;\n  });\n  const pksMatch = (keyless == null ? void 0 : keyless.publishableKey) === publishableKey;\n  const sksMatch = (keyless == null ? void 0 : keyless.secretKey) === secretKey;\n  if (pksMatch && sksMatch) {\n    return;\n  }\n  cookieStore.set(await getKeylessCookieName(), JSON.stringify({ claimUrl, publishableKey, secretKey }), {\n    secure: true,\n    httpOnly: true\n  });\n  if (detectClerkMiddleware(request)) {\n    redirect(`/clerk-sync-keyless?returnUrl=${returnUrl}`, RedirectType.replace);\n    return;\n  }\n  return;\n}\nasync function createOrReadKeylessAction() {\n  if (!canUseKeyless) {\n    return null;\n  }\n  const result = await import(\"../server/keyless-node.js\").then((m) => m.createOrReadKeyless()).catch(() => null);\n  if (!result) {\n    errorThrower.throwMissingPublishableKeyError();\n    return null;\n  }\n  const { clerkDevelopmentCache, createKeylessModeMessage } = await import(\"../server/keyless-log-cache.js\");\n  clerkDevelopmentCache == null ? void 0 : clerkDevelopmentCache.log({\n    cacheKey: result.publishableKey,\n    msg: createKeylessModeMessage(result)\n  });\n  const { claimUrl, publishableKey, secretKey, apiKeysUrl } = result;\n  void (await cookies()).set(await getKeylessCookieName(), JSON.stringify({ claimUrl, publishableKey, secretKey }), {\n    secure: false,\n    httpOnly: false\n  });\n  return {\n    claimUrl,\n    publishableKey,\n    apiKeysUrl\n  };\n}\nasync function deleteKeylessAction() {\n  if (!canUseKeyless) {\n    return;\n  }\n  await import(\"../server/keyless-node.js\").then((m) => m.removeKeyless()).catch(() => {\n  });\n  return;\n}\nexport {\n  createOrReadKeylessAction,\n  deleteKeylessAction,\n  syncKeylessConfigAction\n};\n", "/* eslint-disable import/no-extraneous-dependencies */ \"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    decryptActionBoundArgs: null,\n    encryptActionBoundArgs: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    decryptActionBoundArgs: function() {\n        return decryptActionBoundArgs;\n    },\n    encryptActionBoundArgs: function() {\n        return encryptActionBoundArgs;\n    }\n});\nrequire(\"server-only\");\nconst _serveredge = require(\"react-server-dom-webpack/server.edge\");\nconst _clientedge = require(\"react-server-dom-webpack/client.edge\");\nconst _nodewebstreamshelper = require(\"../stream-utils/node-web-streams-helper\");\nconst _encryptionutils = require(\"./encryption-utils\");\nconst _workunitasyncstorageexternal = require(\"./work-unit-async-storage.external\");\nconst _dynamicrendering = require(\"./dynamic-rendering\");\nconst _react = /*#__PURE__*/ _interop_require_default(require(\"react\"));\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge';\nconst textEncoder = new TextEncoder();\nconst textDecoder = new TextDecoder();\n/**\n * Decrypt the serialized string with the action id as the salt.\n */ async function decodeActionBoundArg(actionId, arg) {\n    const key = await (0, _encryptionutils.getActionEncryptionKey)();\n    if (typeof key === 'undefined') {\n        throw Object.defineProperty(new Error(`Missing encryption key for Server Action. This is a bug in Next.js`), \"__NEXT_ERROR_CODE\", {\n            value: \"E65\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Get the iv (16 bytes) and the payload from the arg.\n    const originalPayload = atob(arg);\n    const ivValue = originalPayload.slice(0, 16);\n    const payload = originalPayload.slice(16);\n    const decrypted = textDecoder.decode(await (0, _encryptionutils.decrypt)(key, (0, _encryptionutils.stringToUint8Array)(ivValue), (0, _encryptionutils.stringToUint8Array)(payload)));\n    if (!decrypted.startsWith(actionId)) {\n        throw Object.defineProperty(new Error('Invalid Server Action payload: failed to decrypt.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E191\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return decrypted.slice(actionId.length);\n}\n/**\n * Encrypt the serialized string with the action id as the salt. Add a prefix to\n * later ensure that the payload is correctly decrypted, similar to a checksum.\n */ async function encodeActionBoundArg(actionId, arg) {\n    const key = await (0, _encryptionutils.getActionEncryptionKey)();\n    if (key === undefined) {\n        throw Object.defineProperty(new Error(`Missing encryption key for Server Action. This is a bug in Next.js`), \"__NEXT_ERROR_CODE\", {\n            value: \"E65\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Get 16 random bytes as iv.\n    const randomBytes = new Uint8Array(16);\n    _workunitasyncstorageexternal.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(randomBytes));\n    const ivValue = (0, _encryptionutils.arrayBufferToString)(randomBytes.buffer);\n    const encrypted = await (0, _encryptionutils.encrypt)(key, randomBytes, textEncoder.encode(actionId + arg));\n    return btoa(ivValue + (0, _encryptionutils.arrayBufferToString)(encrypted));\n}\nconst encryptActionBoundArgs = _react.default.cache(async function encryptActionBoundArgs(actionId, ...args) {\n    const { clientModules } = (0, _encryptionutils.getClientReferenceManifestForRsc)();\n    // Create an error before any asynchronous calls, to capture the original\n    // call stack in case we need it when the serialization errors.\n    const error = new Error();\n    Error.captureStackTrace(error, encryptActionBoundArgs);\n    let didCatchError = false;\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    const hangingInputAbortSignal = (workUnitStore == null ? void 0 : workUnitStore.type) === 'prerender' ? (0, _dynamicrendering.createHangingInputAbortSignal)(workUnitStore) : undefined;\n    // Using Flight to serialize the args into a string.\n    const serialized = await (0, _nodewebstreamshelper.streamToString)((0, _serveredge.renderToReadableStream)(args, clientModules, {\n        signal: hangingInputAbortSignal,\n        onError (err) {\n            if (hangingInputAbortSignal == null ? void 0 : hangingInputAbortSignal.aborted) {\n                return;\n            }\n            // We're only reporting one error at a time, starting with the first.\n            if (didCatchError) {\n                return;\n            }\n            didCatchError = true;\n            // Use the original error message together with the previously created\n            // stack, because err.stack is a useless Flight Server call stack.\n            error.message = err instanceof Error ? err.message : String(err);\n        }\n    }), // We pass the abort signal to `streamToString` so that no chunks are\n    // included that are emitted after the signal was already aborted. This\n    // ensures that we can encode hanging promises.\n    hangingInputAbortSignal);\n    if (didCatchError) {\n        if (process.env.NODE_ENV === 'development') {\n            // Logging the error is needed for server functions that are passed to the\n            // client where the decryption is not done during rendering. Console\n            // replaying allows us to still show the error dev overlay in this case.\n            console.error(error);\n        }\n        throw error;\n    }\n    if (!workUnitStore) {\n        return encodeActionBoundArg(actionId, serialized);\n    }\n    const prerenderResumeDataCache = (0, _workunitasyncstorageexternal.getPrerenderResumeDataCache)(workUnitStore);\n    const renderResumeDataCache = (0, _workunitasyncstorageexternal.getRenderResumeDataCache)(workUnitStore);\n    const cacheKey = actionId + serialized;\n    const cachedEncrypted = (prerenderResumeDataCache == null ? void 0 : prerenderResumeDataCache.encryptedBoundArgs.get(cacheKey)) ?? (renderResumeDataCache == null ? void 0 : renderResumeDataCache.encryptedBoundArgs.get(cacheKey));\n    if (cachedEncrypted) {\n        return cachedEncrypted;\n    }\n    const cacheSignal = workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined;\n    cacheSignal == null ? void 0 : cacheSignal.beginRead();\n    const encrypted = await encodeActionBoundArg(actionId, serialized);\n    cacheSignal == null ? void 0 : cacheSignal.endRead();\n    prerenderResumeDataCache == null ? void 0 : prerenderResumeDataCache.encryptedBoundArgs.set(cacheKey, encrypted);\n    return encrypted;\n});\nasync function decryptActionBoundArgs(actionId, encryptedPromise) {\n    const encrypted = await encryptedPromise;\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\n    let decrypted;\n    if (workUnitStore) {\n        const cacheSignal = workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined;\n        const prerenderResumeDataCache = (0, _workunitasyncstorageexternal.getPrerenderResumeDataCache)(workUnitStore);\n        const renderResumeDataCache = (0, _workunitasyncstorageexternal.getRenderResumeDataCache)(workUnitStore);\n        decrypted = (prerenderResumeDataCache == null ? void 0 : prerenderResumeDataCache.decryptedBoundArgs.get(encrypted)) ?? (renderResumeDataCache == null ? void 0 : renderResumeDataCache.decryptedBoundArgs.get(encrypted));\n        if (!decrypted) {\n            cacheSignal == null ? void 0 : cacheSignal.beginRead();\n            decrypted = await decodeActionBoundArg(actionId, encrypted);\n            cacheSignal == null ? void 0 : cacheSignal.endRead();\n            prerenderResumeDataCache == null ? void 0 : prerenderResumeDataCache.decryptedBoundArgs.set(encrypted, decrypted);\n        }\n    } else {\n        decrypted = await decodeActionBoundArg(actionId, encrypted);\n    }\n    const { edgeRscModuleMapping, rscModuleMapping } = (0, _encryptionutils.getClientReferenceManifestForRsc)();\n    // Using Flight to deserialize the args from the string.\n    const deserialized = await (0, _clientedge.createFromReadableStream)(new ReadableStream({\n        start (controller) {\n            controller.enqueue(textEncoder.encode(decrypted));\n            if ((workUnitStore == null ? void 0 : workUnitStore.type) === 'prerender') {\n                // Explicitly don't close the stream here (until prerendering is\n                // complete) so that hanging promises are not rejected.\n                if (workUnitStore.renderSignal.aborted) {\n                    controller.close();\n                } else {\n                    workUnitStore.renderSignal.addEventListener('abort', ()=>controller.close(), {\n                        once: true\n                    });\n                }\n            } else {\n                controller.close();\n            }\n        }\n    }), {\n        serverConsumerManifest: {\n            // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n            // to be added to the current execution. Instead, we'll wait for any ClientReference\n            // to be emitted which themselves will handle the preloading.\n            moduleLoading: null,\n            moduleMap: isEdgeRuntime ? edgeRscModuleMapping : rscModuleMapping,\n            serverModuleMap: (0, _encryptionutils.getServerModuleMap)()\n        }\n    });\n    return deserialized;\n}\n\n//# sourceMappingURL=encryption.js.map", "'use client';\n\nimport { useAuth } from '@clerk/clerk-react';\nimport { useDerivedAuth } from '@clerk/clerk-react/internal';\nimport type { InitialState } from '@clerk/types';\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nconst PromisifiedAuthContext = React.createContext<Promise<InitialState> | InitialState | null>(null);\n\nexport function PromisifiedAuthProvider({\n  authPromise,\n  children,\n}: {\n  authPromise: Promise<InitialState> | InitialState;\n  children: React.ReactNode;\n}) {\n  return <PromisifiedAuthContext.Provider value={authPromise}>{children}</PromisifiedAuthContext.Provider>;\n}\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data during SSR\n * simply by using the `ClerkProvider`.\n *\n * @example\n * import { useAuth } from '@clerk/nextjs'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * This page will be fully rendered during SSR.\n *\n * ```tsx\n * import { useAuth } from '@clerk/nextjs'\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n * ```\n */\nexport function usePromisifiedAuth() {\n  const isPagesRouter = useRouter();\n  const valueFromContext = React.useContext(PromisifiedAuthContext);\n\n  let resolvedData = valueFromContext;\n  if (valueFromContext && 'then' in valueFromContext) {\n    resolvedData = React.use(valueFromContext);\n  }\n\n  // At this point we should have a usable auth object\n\n  if (typeof window === 'undefined') {\n    // Pages router should always use useAuth as it is able to grab initial auth state from context during SSR.\n    if (isPagesRouter) {\n      return useAuth();\n    }\n\n    // We don't need to deal with Clerk being loaded here\n    return useDerivedAuth(resolvedData);\n  } else {\n    return useAuth(resolvedData);\n  }\n}\n", "import \"../../chunk-BUSYA2B4.js\";\nimport { isProductionEnvironment } from \"@clerk/shared/utils\";\nimport React from \"react\";\nimport { useSession } from \"../hooks\";\nimport { usePagesRouter } from \"./usePagesRouter\";\nconst useEnforceCatchAllRoute = (component, path, routing, requireSessionBeforeCheck = true) => {\n  const ref = React.useRef(0);\n  const { pagesRouter } = usePagesRouter();\n  const { session, isLoaded } = useSession();\n  if (isProductionEnvironment()) {\n    return;\n  }\n  React.useEffect(() => {\n    if (!isLoaded || routing && routing !== \"path\") {\n      return;\n    }\n    if (requireSessionBeforeCheck && !session) {\n      return;\n    }\n    const ac = new AbortController();\n    const error = () => {\n      const correctPath = pagesRouter ? `${path}/[[...index]].tsx` : `${path}/[[...rest]]/page.tsx`;\n      throw new Error(\n        `\nClerk: The <${component}/> component is not configured correctly. The most likely reasons for this error are:\n\n1. The \"${path}\" route is not a catch-all route.\nIt is recommended to convert this route to a catch-all route, eg: \"${correctPath}\". Alternatively, you can update the <${component}/> component to use hash-based routing by setting the \"routing\" prop to \"hash\".\n\n2. The <${component}/> component is mounted in a catch-all route, but all routes under \"${path}\" are protected by the middleware.\nTo resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the \"createRouteMatcher\" helper, consider adding \"(.*)\" to the end of the route pattern, eg: \"${path}(.*)\". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher\n`\n      );\n    };\n    if (pagesRouter) {\n      if (!pagesRouter.pathname.match(/\\[\\[\\.\\.\\..+]]/)) {\n        error();\n      }\n    } else {\n      const check = async () => {\n        ref.current++;\n        if (ref.current > 1) {\n          return;\n        }\n        let res;\n        try {\n          const url = `${window.location.origin}${window.location.pathname}/${component}_clerk_catchall_check_${Date.now()}`;\n          res = await fetch(url, { signal: ac.signal });\n        } catch {\n        }\n        if ((res == null ? void 0 : res.status) === 404) {\n          error();\n        }\n      };\n      void check();\n    }\n    return () => {\n      if (ref.current > 1) {\n        ac.abort();\n      }\n    };\n  }, [isLoaded]);\n};\nexport {\n  useEnforceCatchAllRoute\n};\n//# sourceMappingURL=useEnforceCatchAllRoute.js.map", "import \"../../chunk-BUSYA2B4.js\";\nimport React from \"react\";\nimport { usePagesRouter } from \"./usePagesRouter\";\nconst usePathnameWithoutCatchAll = () => {\n  const pathRef = React.useRef();\n  const { pagesRouter } = usePagesRouter();\n  if (pagesRouter) {\n    if (pathRef.current) {\n      return pathRef.current;\n    } else {\n      pathRef.current = pagesRouter.pathname.replace(/\\/\\[\\[\\.\\.\\..*/, \"\");\n      return pathRef.current;\n    }\n  }\n  const usePathname = require(\"next/navigation\").usePathname;\n  const useParams = require(\"next/navigation\").useParams;\n  const pathname = usePathname() || \"\";\n  const pathParts = pathname.split(\"/\").filter(Boolean);\n  const catchAllParams = Object.values(useParams() || {}).filter((v) => Array.isArray(v)).flat(Infinity);\n  if (pathRef.current) {\n    return pathRef.current;\n  } else {\n    pathRef.current = `/${pathParts.slice(0, pathParts.length - catchAllParams.length).join(\"/\")}`;\n    return pathRef.current;\n  }\n};\nexport {\n  usePathnameWithoutCatchAll\n};\n//# sourceMappingURL=usePathnameWithoutCatchAll.js.map", "import \"../../chunk-BUSYA2B4.js\";\nimport { useRoutingProps } from \"@clerk/clerk-react/internal\";\nimport { useEnforceCatchAllRoute } from \"./useEnforceCatchAllRoute\";\nimport { usePathnameWithoutCatchAll } from \"./usePathnameWithoutCatchAll\";\nfunction useEnforceCorrectRoutingProps(componentName, props, requireSessionBeforeCheck = true) {\n  const path = usePathnameWithoutCatchAll();\n  const routingProps = useRoutingProps(componentName, props, { path });\n  useEnforceCatchAllRoute(componentName, path, routingProps.routing, requireSessionBeforeCheck);\n  return routingProps;\n}\nexport {\n  useEnforceCorrectRoutingProps\n};\n//# sourceMappingURL=useEnforceRoutingProps.js.map", "'use client';\n\nimport {\n  OrganizationProfile as BaseOrganizationProfile,\n  SignIn as BaseSignIn,\n  SignUp as BaseSignUp,\n  UserProfile as BaseUserProfile,\n} from '@clerk/clerk-react';\nimport type { ComponentProps } from 'react';\nimport React from 'react';\n\nimport { useEnforceCorrectRoutingProps } from './hooks/useEnforceRoutingProps';\n\nexport {\n  CreateOrganization,\n  OrganizationList,\n  OrganizationSwitcher,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUpButton,\n  UserButton,\n  GoogleOneTap,\n  Waitlist,\n} from '@clerk/clerk-react';\n\n// The assignment of UserProfile with BaseUserProfile props is used\n// to support the CustomPage functionality (eg UserProfile.Page)\n// Also the `typeof BaseUserProfile` is used to resolve the following error:\n// \"The inferred type of 'UserProfile' cannot be named without a reference to ...\"\nexport const UserProfile: typeof BaseUserProfile = Object.assign(\n  (props: ComponentProps<typeof BaseUserProfile>) => {\n    return <BaseUserProfile {...useEnforceCorrectRoutingProps('UserProfile', props)} />;\n  },\n  { ...BaseUserProfile },\n);\n\n// The assignment of OrganizationProfile with BaseOrganizationProfile props is used\n// to support the CustomPage functionality (eg OrganizationProfile.Page)\n// Also the `typeof BaseOrganizationProfile` is used to resolved the following error:\n// \"The inferred type of 'OrganizationProfile' cannot be named without a reference to ...\"\nexport const OrganizationProfile: typeof BaseOrganizationProfile = Object.assign(\n  (props: ComponentProps<typeof BaseOrganizationProfile>) => {\n    return <BaseOrganizationProfile {...useEnforceCorrectRoutingProps('OrganizationProfile', props)} />;\n  },\n  { ...BaseOrganizationProfile },\n);\n\nexport const SignIn = (props: ComponentProps<typeof BaseSignIn>) => {\n  return <BaseSignIn {...useEnforceCorrectRoutingProps('SignIn', props, false)} />;\n};\n\nexport const SignUp = (props: ComponentProps<typeof BaseSignUp>) => {\n  return <BaseSignUp {...useEnforceCorrectRoutingProps('SignUp', props, false)} />;\n};\n"], "names": ["ensureServerEntryExports", "actions", "i", "length", "action", "__next_loaded_action_key", "arrayBufferToString", "decrypt", "encrypt", "getActionEncryptionKey", "getClientReferenceManifestForRsc", "getServerModuleMap", "setReferenceManifestsSingleton", "stringToUint8Array", "buffer", "bytes", "Uint8Array", "len", "byteLength", "String", "fromCharCode", "apply", "binary", "arr", "charCodeAt", "key", "iv", "data", "crypto", "subtle", "name", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "clientReferenceManifestsPerPage", "normalizeAppPath", "serverActionsManifestSingleton", "InvariantError", "workStore", "workAsyncStorage", "getStore", "mergeClientReferenceManifests", "clientReferenceManifests", "Object", "values", "mergedClientReferenceManifest", "clientModules", "edgeRscModuleMapping", "rscModuleMapping", "route", "<PERSON><PERSON><PERSON>", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "undefined", "importKey", "atob", "syncKeylessConfigAction", "args", "claimUrl", "publishableKey", "secret<PERSON>ey", "returnUrl", "cookieStore", "cookies", "request", "Request", "headers", "keyless", "getKeylessCookieValue", "_a", "get", "value", "pksMatch", "sksMatch", "set", "getKeylessCookieName", "JSON", "stringify", "secure", "httpOnly", "detectClerkMiddleware", "redirect", "RedirectType", "replace", "createOrReadKeylessAction", "canUse<PERSON><PERSON><PERSON>", "result", "then", "m", "createOrReadKeyless", "catch", "errorThrower", "throwMissingPublishableKeyError", "clerkDevelopmentCache", "createKeylessModeMessage", "cache<PERSON>ey", "msg", "apiKeysUrl", "deleteKeylessAction", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}