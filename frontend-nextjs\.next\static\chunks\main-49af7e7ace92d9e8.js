try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="403da326-3356-4353-a72b-6aa9d5ec8e14",e._sentryDebugIdIdentifier="sentry-dbid-403da326-3356-4353-a72b-6aa9d5ec8e14")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8792],{181:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return k},CACHE_ONE_YEAR:function(){return w},DOT_NEXT_ALIAS:function(){return C},ESLINT_DEFAULT_DIRS:function(){return K},GSP_NO_RETURNED_VALUE:function(){return q},GSSP_COMPONENT_MEMBER_ERROR:function(){return G},GSSP_NO_RETURNED_VALUE:function(){return z},INFINITE_CACHE:function(){return R},INSTRUMENTATION_HOOK_FILENAME:function(){return O},MATCHED_PATH_HEADER:function(){return i},MIDDLEWARE_FILENAME:function(){return P},MIDDLEWARE_LOCATION_REGEXP:function(){return T},NEXT_BODY_SUFFIX:function(){return h},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return S},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return g},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return _},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return E},NEXT_CACHE_TAGS_HEADER:function(){return m},NEXT_CACHE_TAG_MAX_ITEMS:function(){return v},NEXT_CACHE_TAG_MAX_LENGTH:function(){return b},NEXT_DATA_SUFFIX:function(){return f},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return p},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return y},NON_STANDARD_NODE_ENV:function(){return V},PAGES_DIR_ALIAS:function(){return x},PRERENDER_REVALIDATE_HEADER:function(){return a},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return U},ROOT_DIR_ALIAS:function(){return I},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return L},RSC_ACTION_ENCRYPTION_ALIAS:function(){return D},RSC_ACTION_PROXY_ALIAS:function(){return N},RSC_ACTION_VALIDATE_ALIAS:function(){return A},RSC_CACHE_WRAPPER_ALIAS:function(){return j},RSC_MOD_REF_PROXY_ALIAS:function(){return M},RSC_PREFETCH_SUFFIX:function(){return s},RSC_SEGMENTS_DIR_SUFFIX:function(){return l},RSC_SEGMENT_SUFFIX:function(){return u},RSC_SUFFIX:function(){return c},SERVER_PROPS_EXPORT_ERROR:function(){return W},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return B},SERVER_PROPS_SSG_CONFLICT:function(){return H},SERVER_RUNTIME:function(){return J},SSG_FALLBACK_EXPORT_ERROR:function(){return Y},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return F},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return $},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return X},WEBPACK_LAYERS:function(){return Z},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",n="nxtI",i="x-matched-path",a="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s=".prefetch.rsc",l=".segments",u=".segment.rsc",c=".rsc",d=".action",f=".json",p=".meta",h=".body",m="x-next-cache-tags",g="x-next-revalidated-tags",_="x-next-revalidate-tag-token",y="next-resume",v=128,b=256,E=1024,S="_N_T_",w=31536e3,R=0xfffffffe,P="middleware",T=`(?:src/)?${P}`,O="instrumentation",x="private-next-pages",C="private-dot-next",I="private-next-root-dir",k="private-next-app-dir",M="private-next-rsc-mod-ref-proxy",A="private-next-rsc-action-validate",N="private-next-rsc-server-reference",j="private-next-rsc-cache-wrapper",D="private-next-rsc-action-encryption",L="private-next-rsc-action-client-wrapper",U="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",F="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",B="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",H="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",$="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",W="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",q="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",z="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",X="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",G="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",V='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',Y="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",K=["app","pages","components","lib","src"],J={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},Q={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Z={...Q,GROUP:{builtinReact:[Q.reactServerComponents,Q.actionBrowser],serverOnly:[Q.reactServerComponents,Q.actionBrowser,Q.instrument,Q.middleware],neutralTarget:[Q.apiNode,Q.apiEdge],clientOnly:[Q.serverSideRendering,Q.appPagesBrowser],bundled:[Q.reactServerComponents,Q.actionBrowser,Q.serverSideRendering,Q.appPagesBrowser,Q.shared,Q.instrument,Q.middleware],appPages:[Q.reactServerComponents,Q.serverSideRendering,Q.appPagesBrowser,Q.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},319:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return s},urlObjectKeys:function(){return o}});let n=r(98781)._(r(5257)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",o=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||i.test(a))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return a(e)}},3236:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return a}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function i(e){return["async","defer","noModule"].includes(e)}function a(e,t){for(let[a,o]of Object.entries(t)){if(!t.hasOwnProperty(a)||n.includes(a)||void 0===o)continue;let s=r[a]||a.toLowerCase();"SCRIPT"===e.tagName&&i(s)?e[s]=!!o:e.setAttribute(s,String(o)),(!1===o||"SCRIPT"===e.tagName&&i(s)&&(!o||"false"===o))&&(e.setAttribute(s,""),e.removeAttribute(s))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3510:e=>{!function(){var t={229:function(e){var t,r,n,i=e.exports={};function a(){throw Error("setTimeout has not been defined")}function o(){throw Error("clearTimeout has not been defined")}try{t="function"==typeof setTimeout?setTimeout:a}catch(e){t=a}try{r="function"==typeof clearTimeout?clearTimeout:o}catch(e){r=o}function s(e){if(t===setTimeout)return setTimeout(e,0);if((t===a||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}var l=[],u=!1,c=-1;function d(){u&&n&&(u=!1,n.length?l=n.concat(l):c=-1,l.length&&f())}function f(){if(!u){var e=s(d);u=!0;for(var t=l.length;t;){for(n=l,l=[];++c<t;)n&&n[c].run();c=-1,t=l.length}n=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===o||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function h(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new p(e,t)),1!==l.length||u||s(f)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var a=r[e]={exports:{}},o=!0;try{t[e](a,a.exports,n),o=!1}finally{o&&delete r[e]}return a.exports}n.ab="//",e.exports=n(229)}()},4091:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let n=r(73920),i=r(14688);function a(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4184:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return o}});let n=r(48319),i=r(84563),a=r(76073);function o(e,t){var r,o;let{basePath:s,i18n:l,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},c={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};s&&(0,a.pathHasPrefix)(c.pathname,s)&&(c.pathname=(0,i.removePathPrefix)(c.pathname,s),c.basePath=s);let d=c.pathname;if(c.pathname.startsWith("/_next/data/")&&c.pathname.endsWith(".json")){let e=c.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");c.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(c.pathname=d)}if(l){let e=t.i18nProvider?t.i18nProvider.analyze(c.pathname):(0,n.normalizeLocalePath)(c.pathname,l.locales);c.locale=e.detectedLocale,c.pathname=null!=(o=e.pathname)?o:c.pathname,!e.detectedLocale&&c.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,n.normalizeLocalePath)(d,l.locales)).detectedLocale&&(c.locale=e.detectedLocale)}return c}},5257:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},6097:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return _},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function o(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class _ extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},6855:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},8742:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(6097),i=r(25620);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,i.hasBasePath)(r.pathname)}catch(e){return!1}}},9688:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},10618:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(14688);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10773:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},13886:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(83447),i=r(81414),a=r(36415),o=r(48319),s=r(69476),l=r(39106);function u(e,t,r,u,c,d){let f,p=!1,h=!1,m=(0,l.parseRelativeUrl)(e),g=(0,a.removeTrailingSlash)((0,o.normalizeLocalePath)((0,s.removeBasePath)(m.pathname),d).pathname),_=r=>{let l=(0,n.getPathMatch)(r.source+"",{removeUnnamedParams:!0,strict:!0})(m.pathname);if((r.has||r.missing)&&l){let e=(0,i.matchHas)({headers:{host:document.location.hostname,"user-agent":navigator.userAgent},cookies:document.cookie.split("; ").reduce((e,t)=>{let[r,...n]=t.split("=");return e[r]=n.join("="),e},{})},m.query,r.has,r.missing);e?Object.assign(l,e):l=!1}if(l){if(!r.destination)return h=!0,!0;let n=(0,i.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:l,query:u});if(m=n.parsedDestination,e=n.newUrl,Object.assign(u,n.parsedDestination.query),g=(0,a.removeTrailingSlash)((0,o.normalizeLocalePath)((0,s.removeBasePath)(e),d).pathname),t.includes(g))return p=!0,f=g,!0;if((f=c(g))!==e&&t.includes(f))return p=!0,!0}},y=!1;for(let e=0;e<r.beforeFiles.length;e++)_(r.beforeFiles[e]);if(!(p=t.includes(g))){if(!y){for(let e=0;e<r.afterFiles.length;e++)if(_(r.afterFiles[e])){y=!0;break}}if(y||(f=c(g),y=p=t.includes(f)),!y){for(let e=0;e<r.fallback.length;e++)if(_(r.fallback[e])){y=!0;break}}}return{asPath:e,parsedAs:m,matchedPage:p,resolvedHref:f,externalDest:h}}},14688:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let n=r(36415),i=r(66673),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:a}=(0,i.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16288:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17416:(e,t)=>{"use strict";function r(e,t){let r=Object.keys(e);if(r.length!==Object.keys(t).length)return!1;for(let n=r.length;n--;){let i=r[n];if("query"===i){let r=Object.keys(e.query);if(r.length!==Object.keys(t.query).length)return!1;for(let n=r.length;n--;){let i=r[n];if(!t.query.hasOwnProperty(i)||e.query[i]!==t.query[i])return!1}}else if(!t.hasOwnProperty(i)||e[i]!==t[i])return!1}return!0}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"compareRouterStates",{enumerable:!0,get:function(){return r}})},18987:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(51532)._(r(44109)).default.createContext(null)},19822:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},21196:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return i},RedirectType:function(){return a},isRedirectError:function(){return o}});let n=r(51226),i="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===a||"push"===a)&&"string"==typeof o&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21440:(e,t,r)=>{"use strict";let n,i,a,o,s,l,u,c,d,f,p,h,m,g,_,y,v,b,E,S,w,R,P,T,O;r.r(t),r.d(t,{onRouterTransitionStart:()=>lc});let x=globalThis,C="9.19.0";function I(){return k(x),x}function k(e){let t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||C,t[C]=t[C]||{}}function M(e,t,r=x){let n=r.__SENTRY__=r.__SENTRY__||{},i=n[C]=n[C]||{};return i[e]||(i[e]=t())}let A=["debug","info","warn","error","log","assert","trace"],N={};function j(e){if(!("console"in x))return e();let t=x.console,r={},n=Object.keys(N);n.forEach(e=>{let n=N[e];r[e]=t[e],t[e]=n});try{return e()}finally{n.forEach(e=>{t[e]=r[e]})}}let D=M("logger",function(){let e=!1,t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return A.forEach(e=>{t[e]=()=>void 0}),t});function L(e,t,r=[t],n="npm"){let i=e._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${t}`,packages:r.map(e=>({name:`${n}:@sentry/${e}`,version:C})),version:C}),e._metadata=i}let U=Object.prototype.toString;function F(e){switch(U.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return Y(e,Error)}}function B(e,t){return U.call(e)===`[object ${t}]`}function H(e){return B(e,"ErrorEvent")}function $(e){return B(e,"DOMError")}function W(e){return B(e,"String")}function q(e){return"object"==typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function z(e){return null===e||q(e)||"object"!=typeof e&&"function"!=typeof e}function X(e){return B(e,"Object")}function G(e){return"undefined"!=typeof Event&&Y(e,Event)}function V(e){return!!(e?.then&&"function"==typeof e.then)}function Y(e,t){try{return e instanceof t}catch(e){return!1}}function K(e){return!!("object"==typeof e&&null!==e&&(e.__isVue||e._isVue))}function J(e){return"undefined"!=typeof Request&&Y(e,Request)}function Q(e,t={}){if(!e)return"<unknown>";try{let r,n=e,i=[],a=0,o=0,s=Array.isArray(t)?t:t.keyAttrs,l=!Array.isArray(t)&&t.maxStringLength||80;for(;n&&a++<5&&(r=function(e,t){let r=[];if(!e?.tagName)return"";if(x.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}r.push(e.tagName.toLowerCase());let n=t?.length?t.filter(t=>e.getAttribute(t)).map(t=>[t,e.getAttribute(t)]):null;if(n?.length)n.forEach(e=>{r.push(`[${e[0]}="${e[1]}"]`)});else{e.id&&r.push(`#${e.id}`);let t=e.className;if(t&&W(t))for(let e of t.split(/\s+/))r.push(`.${e}`)}for(let t of["aria-label","type","name","title","alt"]){let n=e.getAttribute(t);n&&r.push(`[${t}="${n}"]`)}return r.join("")}(n,s),"html"!==r&&(!(a>1)||!(o+3*i.length+r.length>=l)));)i.push(r),o+=r.length,n=n.parentNode;return i.reverse().join(" > ")}catch(e){return"<unknown>"}}function Z(){try{return x.document.location.href}catch(e){return""}}function ee(e){if(!x.HTMLElement)return null;let t=e;for(let e=0;e<5&&t;e++){if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}function et(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function er(e,t){if(!Array.isArray(e))return"";let r=[];for(let t=0;t<e.length;t++){let n=e[t];try{K(n)?r.push("[VueViewModel]"):r.push(String(n))}catch(e){r.push("[value cannot be serialized]")}}return r.join(t)}function en(e,t=[],r=!1){return t.some(t=>(function(e,t,r=!1){return!!W(e)&&(B(t,"RegExp")?t.test(e):!!W(t)&&(r?e===t:e.includes(t)))})(e,t,r))}function ei(e,t,r){if(!(t in e))return;let n=e[t];if("function"!=typeof n)return;let i=r(n);"function"==typeof i&&eo(i,n);try{e[t]=i}catch{}}function ea(e,t,r){try{Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0})}catch(e){}}function eo(e,t){try{let r=t.prototype||{};e.prototype=t.prototype=r,ea(e,"__sentry_original__",t)}catch(e){}}function es(e){return e.__sentry_original__}function el(e){if(F(e))return{message:e.message,name:e.name,stack:e.stack,...ec(e)};if(!G(e))return e;{let t={type:e.type,target:eu(e.target),currentTarget:eu(e.currentTarget),...ec(e)};return"undefined"!=typeof CustomEvent&&Y(e,CustomEvent)&&(t.detail=e.detail),t}}function eu(e){try{return"undefined"!=typeof Element&&Y(e,Element)?Q(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}function ec(e){if("object"!=typeof e||null===e)return{};{let t={};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}}function ed(e=x.crypto||x.msCrypto){let t=()=>16*Math.random();try{if(e?.randomUUID)return e.randomUUID().replace(/-/g,"");e?.getRandomValues&&(t=()=>{let t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(e){}return"10000000100040008000100000000000".replace(/[018]/g,e=>(e^(15&t())>>e/4).toString(16))}function ef(e){return e.exception?.values?.[0]}function ep(e,t,r){let n=e.exception=e.exception||{},i=n.values=n.values||[],a=i[0]=i[0]||{};a.value||(a.value=t||""),a.type||(a.type=r||"Error")}function eh(e,t){let r=ef(e);if(!r)return;let n=r.mechanism;if(r.mechanism={type:"generic",handled:!0,...n,...t},t&&"data"in t){let e={...n?.data,...t.data};r.mechanism.data=e}}function em(e){if(function(e){try{return e.__sentry_captured__}catch{}}(e))return!0;try{ea(e,"__sentry_captured__",!0)}catch(e){}return!1}function eg(){return Date.now()/1e3}let e_=function(){let{performance:e}=x;if(!e?.now)return eg;let t=Date.now()-e.now(),r=void 0==e.timeOrigin?t:e.timeOrigin;return()=>(r+e.now())/1e3}();function ey(){return n||(n=function(){let{performance:e}=x;if(!e?.now)return[void 0,"none"];let t=e.now(),r=Date.now(),n=e.timeOrigin?Math.abs(e.timeOrigin+t-r):36e5,i=e.timing?.navigationStart,a="number"==typeof i?Math.abs(i+t-r):36e5;if(n<36e5||a<36e5)if(n<=a)return[e.timeOrigin,"timeOrigin"];else return[i,"navigationStart"];return[r,"dateNow"]}()),n[0]}function ev(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||e_(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:ed()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{let t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function eb(e,t,r=2){if(!t||"object"!=typeof t||r<=0)return t;if(e&&0===Object.keys(t).length)return e;let n={...e};for(let e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=eb(n[e],t[e],r-1));return n}let eE="_sentrySpan";function eS(e,t){t?ea(e,eE,t):delete e[eE]}function ew(){return ed().substring(16)}class eR{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:ed(),sampleRand:Math.random()}}clone(){let e=new eR;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,eS(e,this[eE]),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&ev(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let t="function"==typeof e?e(this):e,{tags:r,extra:n,user:i,contexts:a,level:o,fingerprint:s=[],propagationContext:l}=(t instanceof eR?t.getScopeData():X(t)?e:void 0)||{};return this._tags={...this._tags,...r},this._extra={...this._extra,...n},this._contexts={...this._contexts,...a},i&&Object.keys(i).length&&(this._user=i),o&&(this._level=o),s.length&&(this._fingerprint=s),l&&(this._propagationContext=l),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,eS(this,void 0),this._attachments=[],this.setPropagationContext({traceId:ed(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){let r="number"==typeof t?t:100;if(r<=0)return this;let n={timestamp:eg(),...e,message:e.message?et(e.message,2048):e.message};return this._breadcrumbs.push(n),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),this._client?.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:this[eE]}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=eb(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,t){let r=t?.event_id||ed();if(!this._client)return D.warn("No client configured on scope - will not capture exception!"),r;let n=Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:n,...t,event_id:r},this),r}captureMessage(e,t,r){let n=r?.event_id||ed();if(!this._client)return D.warn("No client configured on scope - will not capture message!"),n;let i=Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:i,...r,event_id:n},this),n}captureEvent(e,t){let r=t?.event_id||ed();return this._client?this._client.captureEvent(e,{...t,event_id:r},this):D.warn("No client configured on scope - will not capture event!"),r}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}class eP{constructor(e,t){let r,n;r=e||new eR,n=t||new eR,this._stack=[{scope:r}],this._isolationScope=n}withScope(e){let t,r=this._pushScope();try{t=e(r)}catch(e){throw this._popScope(),e}return V(t)?t.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),t)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function eT(){let e=k(I());return e.stack=e.stack||new eP(M("defaultCurrentScope",()=>new eR),M("defaultIsolationScope",()=>new eR))}function eO(e){return eT().withScope(e)}function ex(e,t){let r=eT();return r.withScope(()=>(r.getStackTop().scope=e,t(e)))}function eC(e){return eT().withScope(()=>e(eT().getIsolationScope()))}function eI(e){let t=k(e);return t.acs?t.acs:{withIsolationScope:eC,withScope:eO,withSetScope:ex,withSetIsolationScope:(e,t)=>eC(t),getCurrentScope:()=>eT().getScope(),getIsolationScope:()=>eT().getIsolationScope()}}function ek(){return eI(I()).getCurrentScope()}function eM(){return eI(I()).getIsolationScope()}function eA(...e){let t=eI(I());if(2===e.length){let[r,n]=e;return r?t.withSetScope(r,n):t.withScope(n)}return t.withScope(e[0])}function eN(){return ek().getClient()}let ej="production";function eD(e){return new eU(t=>{t(e)})}function eL(e){return new eU((t,r)=>{r(e)})}!function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"}(nm||(nm={}));class eU{constructor(e){this._state=nm.PENDING,this._handlers=[],this._runExecutor(e)}then(e,t){return new eU((r,n)=>{this._handlers.push([!1,t=>{if(e)try{r(e(t))}catch(e){n(e)}else r(t)},e=>{if(t)try{r(t(e))}catch(e){n(e)}else n(e)}]),this._executeHandlers()})}catch(e){return this.then(e=>e,e)}finally(e){return new eU((t,r)=>{let n,i;return this.then(t=>{i=!1,n=t,e&&e()},t=>{i=!0,n=t,e&&e()}).then(()=>{if(i)return void r(n);t(n)})})}_executeHandlers(){if(this._state===nm.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(e=>{e[0]||(this._state===nm.RESOLVED&&e[1](this._value),this._state===nm.REJECTED&&e[2](this._value),e[0]=!0)})}_runExecutor(e){let t=(e,t)=>{if(this._state===nm.PENDING){if(V(t))return void t.then(r,n);this._state=e,this._value=t,this._executeHandlers()}},r=e=>{t(nm.RESOLVED,e)},n=e=>{t(nm.REJECTED,e)};try{e(r,n)}catch(e){n(e)}}}let eF=/\(error: (.*)\)/,eB=/captureMessage|captureException/;function eH(...e){let t=e.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,r=0,n=0)=>{let i=[],a=e.split("\n");for(let e=r;e<a.length;e++){let r=a[e];if(r.length>1024)continue;let o=eF.test(r)?r.replace(eF,"$1"):r;if(!o.match(/\S*Error: /)){for(let e of t){let t=e(o);if(t){i.push(t);break}}if(i.length>=50+n)break}}var o=i.slice(n);if(!o.length)return[];let s=Array.from(o);return/sentryWrapped/.test(e$(s).function||"")&&s.pop(),s.reverse(),eB.test(e$(s).function||"")&&(s.pop(),eB.test(e$(s).function||"")&&s.pop()),s.slice(0,50).map(e=>({...e,filename:e.filename||e$(s).filename,function:e.function||"?"}))}}function e$(e){return e[e.length-1]||{}}let eW="<anonymous>";function eq(e){try{if(!e||"function"!=typeof e)return eW;return e.name||eW}catch(e){return eW}}function ez(e){let t=e.exception;if(t){let e=[];try{return t.values.forEach(t=>{t.stacktrace.frames&&e.push(...t.stacktrace.frames)}),e}catch(e){}}}function eX(e,t=100,r=Infinity){try{return function e(t,r,n=Infinity,i=Infinity,a=function(){let e=new WeakSet;return[function(t){return!!e.has(t)||(e.add(t),!1)},function(t){e.delete(t)}]}()){let[o,s]=a;if(null==r||["boolean","string"].includes(typeof r)||"number"==typeof r&&Number.isFinite(r))return r;let l=function(e,t){try{if("domain"===e&&t&&"object"==typeof t&&t._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if("undefined"!=typeof global&&t===global)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if(K(t))return"[VueViewModel]";if(X(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t)return"[SyntheticEvent]";if("number"==typeof t&&!Number.isFinite(t))return`[${t}]`;if("function"==typeof t)return`[Function: ${eq(t)}]`;if("symbol"==typeof t)return`[${String(t)}]`;if("bigint"==typeof t)return`[BigInt: ${String(t)}]`;let r=function(e){let t=Object.getPrototypeOf(e);return t?.constructor?t.constructor.name:"null prototype"}(t);if(/^HTML(\w*)Element$/.test(r))return`[HTMLElement: ${r}]`;return`[object ${r}]`}catch(e){return`**non-serializable** (${e})`}}(t,r);if(!l.startsWith("[object "))return l;if(r.__sentry_skip_normalization__)return r;let u="number"==typeof r.__sentry_override_normalization_depth__?r.__sentry_override_normalization_depth__:n;if(0===u)return l.replace("object ","");if(o(r))return"[Circular ~]";if(r&&"function"==typeof r.toJSON)try{let t=r.toJSON();return e("",t,u-1,i,a)}catch(e){}let c=Array.isArray(r)?[]:{},d=0,f=el(r);for(let t in f){if(!Object.prototype.hasOwnProperty.call(f,t))continue;if(d>=i){c[t]="[MaxProperties ~]";break}let r=f[t];c[t]=e(t,r,u-1,i,a),d++}return s(r),c}("",e,t,r)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}let eG="sentry.source",eV="sentry.sample_rate",eY="sentry.previous_trace_sample_rate",eK="sentry.op",eJ="sentry.origin",eQ="sentry.idle_span_finish_reason",eZ="sentry.measurement_unit",e0="sentry.measurement_value",e1="sentry.custom_span_name",e2="sentry.profile_id",e3="sentry.exclusive_time";function e5(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let t=e||eN()?.getOptions();return!!t&&(null!=t.tracesSampleRate||!!t.tracesSampler)}function e4(e,t){e.setAttribute("http.response.status_code",t);let r=function(e){if(e<400&&e>=100)return{code:1};if(e>=400&&e<500)switch(e){case 401:return{code:2,message:"unauthenticated"};case 403:return{code:2,message:"permission_denied"};case 404:return{code:2,message:"not_found"};case 409:return{code:2,message:"already_exists"};case 413:return{code:2,message:"failed_precondition"};case 429:return{code:2,message:"resource_exhausted"};case 499:return{code:2,message:"cancelled"};default:return{code:2,message:"invalid_argument"}}if(e>=500&&e<600)switch(e){case 501:return{code:2,message:"unimplemented"};case 503:return{code:2,message:"unavailable"};case 504:return{code:2,message:"deadline_exceeded"};default:return{code:2,message:"internal_error"}}return{code:2,message:"unknown_error"}}(t);"unknown_error"!==r.message&&e.setStatus(r)}let e8="_sentryScope",e6="_sentryIsolationScope";function e7(e){return{scope:e[e8],isolationScope:e[e6]}}function e9(e){if("boolean"==typeof e)return Number(e);let t="string"==typeof e?parseFloat(e):e;if(!("number"!=typeof t||isNaN(t))&&!(t<0)&&!(t>1))return t}let te="sentry-",tt=/^sentry-/;function tr(e){let t=tn(e);if(!t)return;let r=Object.entries(t).reduce((e,[t,r])=>(t.match(tt)&&(e[t.slice(te.length)]=r),e),{});return Object.keys(r).length>0?r:void 0}function tn(e){if(e&&(W(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce((e,t)=>(Object.entries(ti(t)).forEach(([t,r])=>{e[t]=r}),e),{}):ti(e)}function ti(e){return e.split(",").map(e=>e.split("=").map(e=>{try{return decodeURIComponent(e.trim())}catch{return}})).reduce((e,[t,r])=>(t&&r&&(e[t]=r),e),{})}let ta=RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function to(e=ed(),t=ew(),r){let n="";return void 0!==r&&(n=r?"-1":"-0"),`${e}-${t}${n}`}let ts=!1;function tl(e){return e&&e.length>0?e.map(({context:{spanId:e,traceId:t,traceFlags:r,...n},attributes:i})=>({span_id:e,trace_id:t,sampled:1===r,attributes:i,...n})):void 0}function tu(e){return"number"==typeof e?tc(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?tc(e.getTime()):e_()}function tc(e){return e>0x2540be3ff?e/1e3:e}function td(e){var t;if("function"==typeof e.getSpanJSON)return e.getSpanJSON();let{spanId:r,traceId:n}=e.spanContext();if((t=e).attributes&&t.startTime&&t.name&&t.endTime&&t.status){let{attributes:t,startTime:i,name:a,endTime:o,status:s,links:l}=e;return{span_id:r,trace_id:n,data:t,description:a,parent_span_id:"parentSpanId"in e?e.parentSpanId:"parentSpanContext"in e?e.parentSpanContext?.spanId:void 0,start_timestamp:tu(i),timestamp:tu(o)||void 0,status:tp(s),op:t[eK],origin:t[eJ],links:tl(l)}}return{span_id:r,trace_id:n,start_timestamp:0,data:{}}}function tf(e){let{traceFlags:t}=e.spanContext();return 1===t}function tp(e){if(e&&0!==e.code)return 1===e.code?"ok":e.message||"unknown_error"}let th="_sentryChildSpans",tm="_sentryRootSpan";function tg(e,t){let r=e[tm]||e;ea(t,tm,r),e[th]?e[th].add(t):ea(e,th,new Set([t]))}function t_(e){let t=new Set;return!function e(r){if(!t.has(r)&&tf(r))for(let n of(t.add(r),r[th]?Array.from(r[th]):[]))e(n)}(e),Array.from(t)}function ty(e){return e[tm]||e}function tv(){let e=eI(I());return e.getActiveSpan?e.getActiveSpan():ek()[eE]}function tb(){ts||(j(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),ts=!0)}let tE="_frozenDsc";function tS(e,t){let r=t.getOptions(),{publicKey:n}=t.getDsn()||{},i={environment:r.environment||ej,release:r.release,public_key:n,trace_id:e};return t.emit("createDsc",i),i}function tw(e,t){let r=t.getPropagationContext();return r.dsc||tS(r.traceId,e)}function tR(e){let t=eN();if(!t)return{};let r=ty(e),n=td(r),i=n.data,a=r.spanContext().traceState,o=a?.get("sentry.sample_rate")??i[eV]??i[eY];function s(e){return("number"==typeof o||"string"==typeof o)&&(e.sample_rate=`${o}`),e}let l=r[tE];if(l)return s(l);let u=a?.get("sentry.dsc"),c=u&&tr(u);if(c)return s(c);let d=tS(e.spanContext().traceId,t),f=i[eG],p=n.description;return"url"!==f&&p&&(d.transaction=p),e5()&&(d.sampled=String(tf(r)),d.sample_rand=a?.get("sentry.sample_rand")??e7(r).scope?.getPropagationContext().sampleRand.toString()),s(d),t.emit("createDsc",d,r),d}function tP(e,t){let{extra:r,tags:n,user:i,contexts:a,level:o,sdkProcessingMetadata:s,breadcrumbs:l,fingerprint:u,eventProcessors:c,attachments:d,propagationContext:f,transactionName:p,span:h}=t;tT(e,"extra",r),tT(e,"tags",n),tT(e,"user",i),tT(e,"contexts",a),e.sdkProcessingMetadata=eb(e.sdkProcessingMetadata,s,2),o&&(e.level=o),p&&(e.transactionName=p),h&&(e.span=h),l.length&&(e.breadcrumbs=[...e.breadcrumbs,...l]),u.length&&(e.fingerprint=[...e.fingerprint,...u]),c.length&&(e.eventProcessors=[...e.eventProcessors,...c]),d.length&&(e.attachments=[...e.attachments,...d]),e.propagationContext={...e.propagationContext,...f}}function tT(e,t,r){e[t]=eb(e[t],r,1)}function tO(e,t,r,n,s,l){var u,c,d,f,p,h;let{normalizeDepth:m=3,normalizeMaxBreadth:g=1e3}=e,_={...t,event_id:t.event_id||r.event_id||ed(),timestamp:t.timestamp||eg()},y=r.integrations||e.integrations.map(e=>e.name);(function(e,t){let{environment:r,release:n,dist:i,maxValueLength:a=250}=t;e.environment=e.environment||r||ej,!e.release&&n&&(e.release=n),!e.dist&&i&&(e.dist=i);let o=e.request;o?.url&&(o.url=et(o.url,a))})(_,e),u=_,(c=y).length>0&&(u.sdk=u.sdk||{},u.sdk.integrations=[...u.sdk.integrations||[],...c]),s&&s.emit("applyFrameMetadata",t),void 0===t.type&&function(e,t){let r=function(e){let t=x._sentryDebugIds;if(!t)return{};let r=Object.keys(t);return o&&r.length===a?o:(a=r.length,o=r.reduce((r,n)=>{i||(i={});let a=i[n];if(a)r[a[0]]=a[1];else{let a=e(n);for(let e=a.length-1;e>=0;e--){let o=a[e],s=o?.filename,l=t[n];if(s&&l){r[s]=l,i[n]=[s,l];break}}}return r},{}))}(t);e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.filename&&(e.debug_id=r[e.filename])})})}(_,e.stackParser);let v=function(e,t){if(!t)return e;let r=e?e.clone():new eR;return r.update(t),r}(n,r.captureContext);r.mechanism&&eh(_,r.mechanism);let b=s?s.getEventProcessors():[],E=M("globalScope",()=>new eR).getScopeData();l&&tP(E,l.getScopeData()),v&&tP(E,v.getScopeData());let S=[...r.attachments||[],...E.attachments];S.length&&(r.attachments=S);let{fingerprint:w,span:R,breadcrumbs:P,sdkProcessingMetadata:T}=E;return function(e,t){let{extra:r,tags:n,user:i,contexts:a,level:o,transactionName:s}=t;Object.keys(r).length&&(e.extra={...r,...e.extra}),Object.keys(n).length&&(e.tags={...n,...e.tags}),Object.keys(i).length&&(e.user={...i,...e.user}),Object.keys(a).length&&(e.contexts={...a,...e.contexts}),o&&(e.level=o),s&&"transaction"!==e.type&&(e.transaction=s)}(_,E),R&&function(e,t){e.contexts={trace:function(e){let{spanId:t,traceId:r,isRemote:n}=e.spanContext(),i=n?t:td(e).parent_span_id,a=e7(e).scope;return{parent_span_id:i,span_id:n?a?.getPropagationContext().propagationSpanId||ew():t,trace_id:r}}(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:tR(t),...e.sdkProcessingMetadata};let r=td(ty(t)).description;r&&!e.transaction&&"transaction"===e.type&&(e.transaction=r)}(_,R),d=_,f=w,d.fingerprint=d.fingerprint?Array.isArray(d.fingerprint)?d.fingerprint:[d.fingerprint]:[],f&&(d.fingerprint=d.fingerprint.concat(f)),d.fingerprint.length||delete d.fingerprint,function(e,t){let r=[...e.breadcrumbs||[],...t];e.breadcrumbs=r.length?r:void 0}(_,P),p=_,h=T,p.sdkProcessingMetadata={...p.sdkProcessingMetadata,...h},(function e(t,r,n,i=0){return new eU((a,o)=>{let s=t[i];if(null===r||"function"!=typeof s)a(r);else{let l=s({...r},n);V(l)?l.then(r=>e(t,r,n,i+1).then(a)).then(null,o):e(t,l,n,i+1).then(a).then(null,o)}})})([...b,...E.eventProcessors],_,r).then(e=>(e&&function(e){let t={};if(e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})}),0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];let r=e.debug_meta.images;Object.entries(t).forEach(([e,t])=>{r.push({type:"sourcemap",code_file:e,debug_id:t})})}(e),"number"==typeof m&&m>0)?function(e,t,r){if(!e)return null;let n={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(e=>({...e,...e.data&&{data:eX(e.data,t,r)}}))},...e.user&&{user:eX(e.user,t,r)},...e.contexts&&{contexts:eX(e.contexts,t,r)},...e.extra&&{extra:eX(e.extra,t,r)}};return e.contexts?.trace&&n.contexts&&(n.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(n.contexts.trace.data=eX(e.contexts.trace.data,t,r))),e.spans&&(n.spans=e.spans.map(e=>({...e,...e.data&&{data:eX(e.data,t,r)}}))),e.contexts?.flags&&n.contexts&&(n.contexts.flags=eX(e.contexts.flags,3,r)),n}(e,m,g):e)}let tx=["user","level","extra","contexts","tags","fingerprint","propagationContext"];function tC(e,t){return ek().captureEvent(e,t)}function tI(e,t){eM().setContext(e,t)}function tk(e){eM().addEventProcessor(e)}function tM(e){let t=eM(),r=ek(),{userAgent:n}=x.navigator||{},i=function(e){let t=e_(),r={sid:ed(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>{var e;return e=r,{sid:`${e.sid}`,init:e.init,started:new Date(1e3*e.started).toISOString(),timestamp:new Date(1e3*e.timestamp).toISOString(),status:e.status,errors:e.errors,did:"number"==typeof e.did||"string"==typeof e.did?`${e.did}`:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}}}};return e&&ev(r,e),r}({user:r.getUser()||t.getUser(),...n&&{userAgent:n},...e}),a=t.getSession();return a?.status==="ok"&&ev(a,{status:"exited"}),tA(),t.setSession(i),i}function tA(){let e,t=eM(),r=ek().getSession()||t.getSession();r&&(e={},"ok"===r.status&&(e={status:"exited"}),ev(r,e)),tN(),t.setSession()}function tN(){let e=eM(),t=eN(),r=e.getSession();r&&t&&t.captureSession(r)}function tj(e=!1){if(e)return void tA();tN()}let tD=[];function tL(e,t){for(let r of t)r?.afterAllSetup&&r.afterAllSetup(e)}function tU(e,t,r){if(!r[t.name]){if(r[t.name]=t,-1===tD.indexOf(t.name)&&"function"==typeof t.setupOnce&&(t.setupOnce(),tD.push(t.name)),t.setup&&"function"==typeof t.setup&&t.setup(e),"function"==typeof t.preprocessEvent){let r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(t,n)=>r(t,n,e))}if("function"==typeof t.processEvent){let r=t.processEvent.bind(t),n=Object.assign((t,n)=>r(t,n,e),{id:t.name});e.addEventProcessor(n)}}}let tF=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],tB=(e={})=>{let t;return{name:"EventFilters",setup(r){t=t$(e,r.getOptions())},processEvent:(r,n,i)=>(t||(t=t$(e,i.getOptions())),!function(e,t){if(e.type){if("transaction"===e.type&&function(e,t){if(!t?.length)return!1;let r=e.transaction;return!!r&&en(r,t)}(e,t.ignoreTransactions))return!0}else{var r,n,i;if(r=e,n=t.ignoreErrors,n?.length&&(function(e){let t=[];e.message&&t.push(e.message);try{let r=e.exception.values[e.exception.values.length-1];r?.value&&(t.push(r.value),r.type&&t.push(`${r.type}: ${r.value}`))}catch(e){}return t})(r).some(e=>en(e,n)))return!0;if(i=e,i.exception?.values?.length&&!i.message&&!i.exception.values.some(e=>e.stacktrace||e.type&&"Error"!==e.type||e.value)||function(e,t){if(!t?.length)return!1;let r=tW(e);return!!r&&en(r,t)}(e,t.denyUrls)||!function(e,t){if(!t?.length)return!0;let r=tW(e);return!r||en(r,t)}(e,t.allowUrls))return!0}return!1}(r,t)?r:null)}},tH=(e={})=>({...tB(e),name:"InboundFilters"});function t$(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:tF],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]]}}function tW(e){try{let t=[...e.exception?.values??[]].reverse().find(e=>e.mechanism?.parent_id===void 0&&e.stacktrace?.frames?.length),r=t?.stacktrace?.frames;return r?function(e=[]){for(let t=e.length-1;t>=0;t--){let r=e[t];if(r&&"<anonymous>"!==r.filename&&"[native code]"!==r.filename)return r.filename||null}return null}(r):null}catch(e){return null}}let tq=new WeakMap,tz=()=>({name:"FunctionToString",setupOnce(){s=Function.prototype.toString;try{Function.prototype.toString=function(...e){let t=es(this),r=tq.has(eN())&&void 0!==t?t:this;return s.apply(r,e)}}catch{}},setup(e){tq.set(e,!0)}}),tX=()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{var r,n;if(r=t,(n=e)&&(function(e,t){let r=e.message,n=t.message;return(!!r||!!n)&&(!r||!!n)&&(!!r||!n)&&r===n&&!!tV(e,t)&&!!tG(e,t)&&!0}(r,n)||function(e,t){let r=tY(t),n=tY(e);return!!r&&!!n&&r.type===n.type&&r.value===n.value&&!!tV(e,t)&&!!tG(e,t)}(r,n)))return null}catch(e){}return e=t}}};function tG(e,t){let r=ez(e),n=ez(t);if(!r&&!n)return!0;if(r&&!n||!r&&n||n.length!==r.length)return!1;for(let e=0;e<n.length;e++){let t=n[e],i=r[e];if(t.filename!==i.filename||t.lineno!==i.lineno||t.colno!==i.colno||t.function!==i.function)return!1}return!0}function tV(e,t){let r=e.fingerprint,n=t.fingerprint;if(!r&&!n)return!0;if(r&&!n||!r&&n)return!1;try{return r.join("")===n.join("")}catch(e){return!1}}function tY(e){return e.exception?.values&&e.exception.values[0]}function tK(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}let tJ=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function tQ(e,t=!1){let{host:r,path:n,pass:i,port:a,projectId:o,protocol:s,publicKey:l}=e;return`${s}://${l}${t&&i?`:${i}`:""}@${r}${a?`:${a}`:""}/${n?`${n}/`:n}${o}`}function tZ(e){let t=tJ.exec(e);if(!t)return void j(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});let[r,n,i="",a="",o="",s=""]=t.slice(1),l="",u=s,c=u.split("/");if(c.length>1&&(l=c.slice(0,-1).join("/"),u=c.pop()),u){let e=u.match(/^\d+/);e&&(u=e[0])}return t0({host:a,pass:i,path:l,projectId:u,port:o,protocol:r,publicKey:n})}function t0(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function t1(e,t=[]){return[e,t]}function t2(e,t){for(let r of e[1]){let e=r[0].type;if(t(r,e))return!0}return!1}function t3(e){let t=k(x);return t.encodePolyfill?t.encodePolyfill(e):new TextEncoder().encode(e)}let t5={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function t4(e){if(!e?.sdk)return;let{name:t,version:r}=e.sdk;return{name:t,version:r}}function t8(e,t,r,n){let i=e.sdkProcessingMetadata?.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!r&&n&&{dsn:tQ(n)},...i&&{trace:i}}}let t6=Symbol.for("SentryInternalError"),t7=Symbol.for("SentryDoNotSendEventError");function t9(e){return{message:e,[t6]:!0}}function re(e){return{message:e,[t7]:!0}}class rt{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn&&(this._dsn=function(e){var t;let r="string"==typeof e?tZ(e):t0(e);if(r&&(t=0,1))return r}(e.dsn)),this._dsn){let t=function(e,t,r){return t||`${function(e){let t=e.protocol?`${e.protocol}:`:"",r=e.port?`:${e.port}`:"";return`${t}//${e.host}${r}${e.path?`/${e.path}`:""}/api/`}(e)}${e.projectId}/envelope/?${function(e,t){let r={sentry_version:"7"};return e.publicKey&&(r.sentry_key=e.publicKey),t&&(r.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(r).toString()}(e,r)}`}(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:t})}}captureException(e,t,r){let n=ed();if(em(e))return n;let i={event_id:n,...t};return this._process(this.eventFromException(e,i).then(e=>this._captureEvent(e,i,r))),i.event_id}captureMessage(e,t,r,n){let i={event_id:ed(),...r},a=q(e)?e:String(e),o=z(e)?this.eventFromMessage(a,t,i):this.eventFromException(e,i);return this._process(o.then(e=>this._captureEvent(e,i,n))),i.event_id}captureEvent(e,t,r){let n=ed();if(t?.originalException&&em(t.originalException))return n;let i={event_id:n,...t},a=e.sdkProcessingMetadata||{},o=a.capturedSpanScope,s=a.capturedSpanIsolationScope;return this._process(this._captureEvent(e,i,o||r,s)),i.event_id}captureSession(e){this.sendSession(e),ev(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>t.flush(e).then(e=>r&&e))):eD(!0)}close(e){return this.flush(e).then(e=>(this.getOptions().enabled=!1,this.emit("close"),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){let t=this._integrations[e.name];tU(this,e,this._integrations),t||tL(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let r=function(e,t,r,n){var i;let a=t4(r),o=e.type&&"replay_event"!==e.type?e.type:"event";(i=r?.sdk)&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||i.name,e.sdk.version=e.sdk.version||i.version,e.sdk.integrations=[...e.sdk.integrations||[],...i.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...i.packages||[]]);let s=t8(e,a,n,t);return delete e.sdkProcessingMetadata,t1(s,[[{type:o},e]])}(e,this._dsn,this._options._metadata,this._options.tunnel);for(let e of t.attachments||[])r=function(e,t){let[r,n]=e;return[r,[...n,t]]}(r,function(e){let t="string"==typeof e.data?t3(e.data):e.data;return[{type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType},t]}(e));let n=this.sendEnvelope(r);n&&n.then(t=>this.emit("afterSendEvent",e,t),null)}sendSession(e){let{release:t,environment:r=ej}=this._options;if("aggregates"in e){let n=e.attrs||{};if(!n.release&&!t)return;n.release=n.release||t,n.environment=n.environment||r,e.attrs=n}else{if(!e.release&&!t)return;e.release=e.release||t,e.environment=e.environment||r}this.emit("beforeSendSession",e);let n=function(e,t,r,n){let i=t4(r);return t1({sent_at:new Date().toISOString(),...i&&{sdk:i},...!!n&&t&&{dsn:tQ(t)}},["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(n)}recordDroppedEvent(e,t,r=1){if(this._options.sendClientReports){let n=`${e}:${t}`;this._outcomes[n]=(this._outcomes[n]||0)+r}}on(e,t){let r=this._hooks[e]=this._hooks[e]||[];return r.push(t),()=>{let e=r.indexOf(t);e>-1&&r.splice(e,1)}}emit(e,...t){let r=this._hooks[e];r&&r.forEach(e=>e(...t))}sendEnvelope(e){return(this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport)?this._transport.send(e).then(null,e=>e):eD({})}_setupIntegrations(){let{integrations:e}=this._options;this._integrations=function(e,t){let r={};return t.forEach(t=>{t&&tU(e,t,r)}),r}(this,e),tL(this,e)}_updateSessionFromEvent(e,t){let r="fatal"===t.level,n=!1,i=t.exception?.values;if(i)for(let e of(n=!0,i)){let t=e.mechanism;if(t?.handled===!1){r=!0;break}}let a="ok"===e.status;(a&&0===e.errors||a&&r)&&(ev(e,{...r&&{status:"crashed"},errors:e.errors||Number(n||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new eU(t=>{let r=0,n=setInterval(()=>{0==this._numProcessing?(clearInterval(n),t(!0)):(r+=1,e&&r>=e&&(clearInterval(n),t(!1)))},1)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,r,n){let i=this.getOptions(),a=Object.keys(this._integrations);return!t.integrations&&a?.length&&(t.integrations=a),this.emit("preprocessEvent",e,t),e.type||n.setLastEventId(e.event_id||t.event_id),tO(i,e,t,r,this,n).then(e=>(null===e||(this.emit("postprocessEvent",e,t),e.contexts={trace:function(e){let{traceId:t,parentSpanId:r,propagationSpanId:n}=e.getPropagationContext(),i={trace_id:t,span_id:n||ew()};return r&&(i.parent_span_id=r),i}(r),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:tw(this,r),...e.sdkProcessingMetadata}),e))}_captureEvent(e,t={},r=ek(),n=eM()){return this._processEvent(e,t,r,n).then(e=>e.event_id,e=>{})}_processEvent(e,t,r,n){let i=this.getOptions(),{sampleRate:a}=i,o=rn(e),s=rr(e),l=e.type||"error",u=`before send for type \`${l}\``,c=void 0===a?void 0:e9(a);if(s&&"number"==typeof c&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error"),eL(re(`Discarding event because it's not included in the random sample (sampling rate = ${a})`));let d="replay_event"===l?"replay":l;return this._prepareEvent(e,t,r,n).then(e=>{if(null===e)throw this.recordDroppedEvent("event_processor",d),re("An event processor returned `null`, will not send event.");return t.data&&!0===t.data.__sentry__?e:function(e,t){let r=`${t} must return \`null\` or a valid event.`;if(V(e))return e.then(e=>{if(!X(e)&&null!==e)throw t9(r);return e},e=>{throw t9(`${t} rejected with ${e}`)});if(!X(e)&&null!==e)throw t9(r);return e}(function(e,t,r,n){let{beforeSend:i,beforeSendTransaction:a,beforeSendSpan:o}=t,s=r;if(rr(s)&&i)return i(s,n);if(rn(s)){if(o){let e=o(function(e){let{trace_id:t,parent_span_id:r,span_id:n,status:i,origin:a,data:o,op:s}=e.contexts?.trace??{};return{data:o??{},description:e.transaction,op:s,parent_span_id:r,span_id:n??"",start_timestamp:e.start_timestamp??0,status:i,timestamp:e.timestamp,trace_id:t??"",origin:a,profile_id:o?.[e2],exclusive_time:o?.[e3],measurements:e.measurements,is_segment:!0}}(s));if(e)s=eb(r,{type:"transaction",timestamp:e.timestamp,start_timestamp:e.start_timestamp,transaction:e.description,contexts:{trace:{trace_id:e.trace_id,span_id:e.span_id,parent_span_id:e.parent_span_id,op:e.op,status:e.status,origin:e.origin,data:{...e.data,...e.profile_id&&{[e2]:e.profile_id},...e.exclusive_time&&{[e3]:e.exclusive_time}}}},measurements:e.measurements});else tb();if(s.spans){let e=[];for(let t of s.spans){let r=o(t);r?e.push(r):(tb(),e.push(t))}s.spans=e}}if(a){if(s.spans){let e=s.spans.length;s.sdkProcessingMetadata={...r.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return a(s,n)}}return s}(0,i,e,t),u)}).then(i=>{if(null===i){if(this.recordDroppedEvent("before_send",d),o){let t=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",t)}throw re(`${u} returned \`null\`, will not send event.`)}let a=r.getSession()||n.getSession();if(s&&a&&this._updateSessionFromEvent(a,i),o){let e=(i.sdkProcessingMetadata?.spanCountBeforeProcessing||0)-(i.spans?i.spans.length:0);e>0&&this.recordDroppedEvent("before_send","span",e)}let l=i.transaction_info;return o&&l&&i.transaction!==e.transaction&&(i.transaction_info={...l,source:"custom"}),this.sendEvent(i,t),i}).then(null,e=>{var t,r;if((t=e)&&"object"==typeof t&&t7 in t||(r=e)&&"object"==typeof r&&t6 in r)throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),t9(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e}`)})}_process(e){this._numProcessing++,e.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.entries(e).map(([e,t])=>{let[r,n]=e.split(":");return{reason:r,category:n,quantity:t}})}_flushOutcomes(){var e;let t=this._clearOutcomes();if(0===t.length||!this._dsn)return;let r=t1((e=this._options.tunnel&&tQ(this._dsn))?{dsn:e}:{},[[{type:"client_report"},{timestamp:eg(),discarded_events:t}]]);this.sendEnvelope(r)}}function rr(e){return void 0===e.type}function rn(e){return"transaction"===e.type}function ri(e,t){let r=t??function(e){return x._sentryClientToLogBufferMap?.get(e)}(e)??[];if(0===r.length)return;let n=e.getOptions(),i=function(e,t,r,n){let i={};return t?.sdk&&(i.sdk={name:t.sdk.name,version:t.sdk.version}),r&&n&&(i.dsn=tQ(n)),t1(i,[[{type:"log",item_count:e.length,content_type:"application/vnd.sentry.items.log+json"},{items:e}]])}(r,n._metadata,n.tunnel,e.getDsn());x._sentryClientToLogBufferMap?.set(e,[]),e.emit("flushLogs"),e.sendEnvelope(i)}x._sentryClientToLogBufferMap=new WeakMap;function ra(e){e.user?.ip_address===void 0&&(e.user={...e.user,ip_address:"{{auto}}"})}function ro(e){"aggregates"in e?e.attrs?.ip_address===void 0&&(e.attrs={...e.attrs,ip_address:"{{auto}}"}):void 0===e.ipAddress&&(e.ipAddress="{{auto}}")}function rs(e,t){let r=ru(e,t),n={type:function(e){let t=e?.name;return!t&&rd(e)?e.message&&Array.isArray(e.message)&&2==e.message.length?e.message[0]:"WebAssembly.Exception":t}(t),value:function(e){let t=e?.message;return rd(e)?Array.isArray(e.message)&&2==e.message.length?e.message[1]:"wasm exception":t?t.error&&"string"==typeof t.error.message?t.error.message:t:"No error message"}(t)};return r.length&&(n.stacktrace={frames:r}),void 0===n.type&&""===n.value&&(n.value="Unrecoverable error caught"),n}function rl(e,t){return{exception:{values:[rs(e,t)]}}}function ru(e,t){var r,n;let i=t.stacktrace||t.stack||"",a=(r=t)&&rc.test(r.message)?1:0,o="number"==typeof(n=t).framesToPop?n.framesToPop:0;try{return e(i,a,o)}catch(e){}return[]}let rc=/Minified React error #\d+;/i;function rd(e){return"undefined"!=typeof WebAssembly&&void 0!==WebAssembly.Exception&&e instanceof WebAssembly.Exception}function rf(e,t,r,n,i){let a;if(H(t)&&t.error)return rl(e,t.error);if($(t)||B(t,"DOMException")){if("stack"in t)a=rl(e,t);else{let i=t.name||($(t)?"DOMError":"DOMException"),o=t.message?`${i}: ${t.message}`:i;ep(a=rp(e,o,r,n),o)}return"code"in t&&(a.tags={...a.tags,"DOMException.code":`${t.code}`}),a}return F(t)?rl(e,t):(X(t)||G(t)?eh(a=function(e,t,r,n){let i=eN(),a=i?.getOptions().normalizeDepth,o=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let r=e[t];if(r instanceof Error)return r}}(t),s={__serialized__:function e(t,r=3,n=102400){let i=eX(t,r);return~-encodeURI(JSON.stringify(i)).split(/%..|./).length>n?e(t,r-1,n):i}(t,a)};if(o)return{exception:{values:[rs(e,o)]},extra:s};let l={exception:{values:[{type:G(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:function(e,{isUnhandledRejection:t}){let r=function(e,t=40){let r=Object.keys(el(e));r.sort();let n=r[0];if(!n)return"[object has no keys]";if(n.length>=t)return et(n,t);for(let e=r.length;e>0;e--){let n=r.slice(0,e).join(", ");if(!(n.length>t)){if(e===r.length)return n;return et(n,t)}}return""}(e),n=t?"promise rejection":"exception";if(H(e))return`Event \`ErrorEvent\` captured as ${n} with message \`${e.message}\``;if(G(e)){let t=function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e);return`Event \`${t}\` (type=${e.type}) captured as ${n}`}return`Object captured as ${n} with keys: ${r}`}(t,{isUnhandledRejection:n})}]},extra:s};if(r){let t=ru(e,r);t.length&&(l.exception.values[0].stacktrace={frames:t})}return l}(e,t,r,i),{synthetic:!0}):(ep(a=rp(e,t,r,n),`${t}`,void 0),eh(a,{synthetic:!0})),a)}function rp(e,t,r,n){let i={};if(n&&r){let n=ru(e,r);n.length&&(i.exception={values:[{value:t,stacktrace:{frames:n}}]}),eh(i,{synthetic:!0})}if(q(t)){let{__sentry_template_string__:e,__sentry_template_values__:r}=t;return i.logentry={message:e,params:r},i}return i.message=t,i}let rh=0;function rm(e,t={}){if("function"!=typeof e)return e;try{let t=e.__sentry_wrapped__;if(t)if("function"==typeof t)return t;else return e;if(es(e))return e}catch(t){return e}let r=function(...r){try{let n=r.map(e=>rm(e,t));return e.apply(this,n)}catch(e){throw rh++,setTimeout(()=>{rh--}),eA(n=>{var i,a;n.addEventProcessor(e=>(t.mechanism&&(ep(e,void 0,void 0),eh(e,t.mechanism)),e.extra={...e.extra,arguments:r},e)),i=e,ek().captureException(i,function(e){if(e){var t;return(t=e)instanceof eR||"function"==typeof t||Object.keys(e).some(e=>tx.includes(e))?{captureContext:e}:e}}(void 0))}),e}};try{for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}catch{}eo(r,e),ea(e,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get:()=>e.name})}catch{}return r}class rg extends rt{constructor(e){let t={parentSpanIsAlwaysRootSpan:!0,...e};L(t,"browser",["browser"],x.SENTRY_SDK_SOURCE||"npm"),super(t);let r=this,{sendDefaultPii:n,_experiments:i}=r._options,a=i?.enableLogs;t.sendClientReports&&x.document&&x.document.addEventListener("visibilitychange",()=>{"hidden"===x.document.visibilityState&&(this._flushOutcomes(),a&&ri(r))}),a&&(r.on("flush",()=>{ri(r)}),r.on("afterCaptureLog",()=>{r._logFlushIdleTimeout&&clearTimeout(r._logFlushIdleTimeout),r._logFlushIdleTimeout=setTimeout(()=>{ri(r)},5e3)})),n&&(r.on("postprocessEvent",ra),r.on("beforeSendSession",ro))}eventFromException(e,t){return function(e,t,r,n){let i=rf(e,t,r?.syntheticException||void 0,n);return eh(i),i.level="error",r?.event_id&&(i.event_id=r.event_id),eD(i)}(this._options.stackParser,e,t,this._options.attachStacktrace)}eventFromMessage(e,t="info",r){return function(e,t,r="info",n,i){let a=rp(e,t,n?.syntheticException||void 0,i);return a.level=r,n?.event_id&&(a.event_id=n.event_id),eD(a)}(this._options.stackParser,e,t,r,this._options.attachStacktrace)}_prepareEvent(e,t,r,n){return e.platform=e.platform||"javascript",super._prepareEvent(e,t,r,n)}}let r_={},ry={};function rv(e,t){r_[e]=r_[e]||[],r_[e].push(t)}function rb(e,t){if(!ry[e]){ry[e]=!0;try{t()}catch(e){}}}function rE(e,t){let r=e&&r_[e];if(r)for(let e of r)try{e(t)}catch(e){}}function rS(){"console"in x&&A.forEach(function(e){e in x.console&&ei(x.console,e,function(t){return N[e]=t,function(...t){rE("console",{args:t,level:e});let r=N[e];r?.apply(x.console,t)}})})}function rw(e,t){let r="fetch";rv(r,e),rb(r,()=>rR(void 0,t))}function rR(e,t=!1){(!t||function(){if("string"==typeof EdgeRuntime)return!0;if(!function(){if(!("fetch"in x))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(e){return!1}}())return!1;if(tK(x.fetch))return!0;let e=!1,t=x.document;if(t&&"function"==typeof t.createElement)try{let r=t.createElement("iframe");r.hidden=!0,t.head.appendChild(r),r.contentWindow?.fetch&&(e=tK(r.contentWindow.fetch)),t.head.removeChild(r)}catch(e){}return e}())&&ei(x,"fetch",function(t){return function(...r){let n=Error(),{method:i,url:a}=function(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){let[t,r]=e;return{url:rx(t),method:rO(r,"method")?String(r.method).toUpperCase():"GET"}}let t=e[0];return{url:rx(t),method:rO(t,"method")?String(t.method).toUpperCase():"GET"}}(r),o={args:r,fetchData:{method:i,url:a},startTimestamp:1e3*e_(),virtualError:n,headers:function(e){let[t,r]=e;try{if("object"==typeof r&&null!==r&&"headers"in r&&r.headers)return new Headers(r.headers);if(J(t))return new Headers(t.headers)}catch{}}(r)};return e||rE("fetch",{...o}),t.apply(x,r).then(async t=>(e?e(t):rE("fetch",{...o,endTimestamp:1e3*e_(),response:t}),t),e=>{if(rE("fetch",{...o,endTimestamp:1e3*e_(),error:e}),F(e)&&void 0===e.stack&&(e.stack=n.stack,ea(e,"framesToPop",1)),e instanceof TypeError&&("Failed to fetch"===e.message||"Load failed"===e.message||"NetworkError when attempting to fetch resource."===e.message))try{let t=new URL(o.fetchData.url);e.message=`${e.message} (${t.host})`}catch{}throw e})}})}async function rP(e,t){if(e?.body){let r=e.body,n=r.getReader(),i=setTimeout(()=>{r.cancel().then(null,()=>{})},9e4),a=!0;for(;a;){let e;try{e=setTimeout(()=>{r.cancel().then(null,()=>{})},5e3);let{done:i}=await n.read();clearTimeout(e),i&&(t(),a=!1)}catch(e){a=!1}finally{clearTimeout(e)}}clearTimeout(i),n.releaseLock(),r.cancel().then(null,()=>{})}}function rT(e){let t;try{t=e.clone()}catch{return}rP(t,()=>{rE("fetch-body-resolved",{endTimestamp:1e3*e_(),response:e})})}function rO(e,t){return!!e&&"object"==typeof e&&!!e[t]}function rx(e){return"string"==typeof e?e:e?rO(e,"url")?e.url:e.toString?e.toString():"":""}function rC(e,t){let r=eN(),n=eM();if(!r)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:a=100}=r.getOptions();if(a<=0)return;let o={timestamp:eg(),...e},s=i?j(()=>i(o,t)):o;null!==s&&(r.emit&&r.emit("beforeAddBreadcrumb",s,t),n.addBreadcrumb(s,a))}function rI(e){if(void 0!==e)return e>=400&&e<500?"warning":e>=500?"error":void 0}function rk(e){return"isRelative"in e}function rM(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let r=t[6]||"",n=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:r,hash:n,relative:t[5]+r+n}}function rA(e){return e.split(/[?#]/,1)[0]}function rN(e){rv("dom",e),rb("dom",rj)}function rj(){if(!x.document)return;let e=rE.bind(null,"dom"),t=rD(e,!0);x.document.addEventListener("click",t,!1),x.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(t=>{let r=x[t]?.prototype;r?.hasOwnProperty?.("addEventListener")&&(ei(r,"addEventListener",function(t){return function(r,n,i){if("click"===r||"keypress"==r)try{let n=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},a=n[r]=n[r]||{refCount:0};if(!a.handler){let n=rD(e);a.handler=n,t.call(this,r,n,i)}a.refCount++}catch(e){}return t.call(this,r,n,i)}}),ei(r,"removeEventListener",function(e){return function(t,r,n){if("click"===t||"keypress"==t)try{let r=this.__sentry_instrumentation_handlers__||{},i=r[t];i&&(i.refCount--,i.refCount<=0&&(e.call(this,t,i.handler,n),i.handler=void 0,delete r[t]),0===Object.keys(r).length&&delete this.__sentry_instrumentation_handlers__)}catch(e){}return e.call(this,t,r,n)}}))})}function rD(e,t=!1){return r=>{var n;if(!r||r._sentryCaptured)return;let i=function(e){try{return e.target}catch(e){return null}}(r);if(n=r.type,"keypress"===n&&(!i?.tagName||"INPUT"!==i.tagName&&"TEXTAREA"!==i.tagName&&!i.isContentEditable&&1))return;ea(r,"_sentryCaptured",!0),i&&!i._sentryId&&ea(i,"_sentryId",ed());let a="keypress"===r.type?"input":r.type;!function(e){if(e.type!==u)return!1;try{if(!e.target||e.target._sentryId!==c)return!1}catch(e){}return!0}(r)&&(e({event:r,name:a,global:t}),u=r.type,c=i?i._sentryId:void 0),clearTimeout(l),l=x.setTimeout(()=>{c=void 0,u=void 0},1e3)}}let rL="__sentry_xhr_v3__";function rU(e){rv("xhr",e),rb("xhr",rF)}function rF(){if(!x.XMLHttpRequest)return;let e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(e,t,r){let n=Error(),i=1e3*e_(),a=W(r[0])?r[0].toUpperCase():void 0,o=function(e){if(W(e))return e;try{return e.toString()}catch{}}(r[1]);if(!a||!o)return e.apply(t,r);t[rL]={method:a,url:o,request_headers:{}},"POST"===a&&o.match(/sentry_key/)&&(t.__sentry_own_request__=!0);let s=()=>{let e=t[rL];if(e&&4===t.readyState){try{e.status_code=t.status}catch(e){}rE("xhr",{endTimestamp:1e3*e_(),startTimestamp:i,xhr:t,virtualError:n})}};return"onreadystatechange"in t&&"function"==typeof t.onreadystatechange?t.onreadystatechange=new Proxy(t.onreadystatechange,{apply:(e,t,r)=>(s(),e.apply(t,r))}):t.addEventListener("readystatechange",s),t.setRequestHeader=new Proxy(t.setRequestHeader,{apply(e,t,r){let[n,i]=r,a=t[rL];return a&&W(n)&&W(i)&&(a.request_headers[n.toLowerCase()]=i),e.apply(t,r)}}),e.apply(t,r)}}),e.send=new Proxy(e.send,{apply(e,t,r){let n=t[rL];return n&&(void 0!==r[0]&&(n.body=r[0]),rE("xhr",{startTimestamp:1e3*e_(),xhr:t})),e.apply(t,r)}})}function rB(e){let t="history";rv(t,e),rb(t,rH)}function rH(){function e(e){return function(...t){let r=t.length>2?t[2]:void 0;if(r){let n=d,i=String(r);if(d=i,n===i)return e.apply(this,t);rE("history",{from:n,to:i})}return e.apply(this,t)}}x.addEventListener("popstate",()=>{let e=x.location.href,t=d;d=e,t!==e&&rE("history",{from:t,to:e})}),"history"in x&&x.history&&(ei(x.history,"pushState",e),ei(x.history,"replaceState",e))}let r$=(e={})=>{let t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:"Breadcrumbs",setup(e){var r,n,i,a,o,s,l;t.console&&function(e){let t="console";rv(t,e),rb(t,rS)}((r=e,function(e){var t;if(eN()!==r)return;let n={category:"console",data:{arguments:e.args,logger:"console"},level:"warn"===(t=e.level)?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log",message:er(e.args," ")};if("assert"===e.level)if(!1!==e.args[0])return;else n.message=`Assertion failed: ${er(e.args.slice(1)," ")||"console.assert"}`,n.data.arguments=e.args.slice(1);rC(n,{input:e.args,level:e.level})})),t.dom&&rN((n=e,i=t.dom,function(e){let t,r;if(eN()!==n)return;let a="object"==typeof i?i.serializeAttribute:void 0,o="object"==typeof i&&"number"==typeof i.maxStringLength?i.maxStringLength:void 0;o&&o>1024&&(o=1024),"string"==typeof a&&(a=[a]);try{var s;let n=e.event,i=(s=n)&&s.target?n.target:n;t=Q(i,{keyAttrs:a,maxStringLength:o}),r=ee(i)}catch(e){t="<unknown>"}if(0===t.length)return;let l={category:`ui.${e.name}`,message:t};r&&(l.data={"ui.component_name":r}),rC(l,{event:e.event,name:e.name,global:e.global})})),t.xhr&&rU((a=e,function(e){if(eN()!==a)return;let{startTimestamp:t,endTimestamp:r}=e,n=e.xhr[rL];if(!t||!r||!n)return;let{method:i,url:o,status_code:s,body:l}=n,u={xhr:e.xhr,input:l,startTimestamp:t,endTimestamp:r},c={category:"xhr",data:{method:i,url:o,status_code:s},type:"http",level:rI(s)};a.emit("beforeOutgoingRequestBreadcrumb",c,u),rC(c,u)})),t.fetch&&rw((o=e,function(e){if(eN()!==o)return;let{startTimestamp:t,endTimestamp:r}=e;if(r&&(!e.fetchData.url.match(/sentry_key/)||"POST"!==e.fetchData.method))if(e.fetchData.method,e.fetchData.url,e.error){let n=e.fetchData,i={data:e.error,input:e.args,startTimestamp:t,endTimestamp:r},a={category:"fetch",data:n,level:"error",type:"http"};o.emit("beforeOutgoingRequestBreadcrumb",a,i),rC(a,i)}else{let n=e.response,i={...e.fetchData,status_code:n?.status};e.fetchData.request_body_size,e.fetchData.response_body_size,n?.status;let a={input:e.args,response:n,startTimestamp:t,endTimestamp:r},s={category:"fetch",data:i,type:"http",level:rI(i.status_code)};o.emit("beforeOutgoingRequestBreadcrumb",s,a),rC(s,a)}})),t.history&&rB((s=e,function(e){if(eN()!==s)return;let t=e.from,r=e.to,n=rM(x.location.href),i=t?rM(t):void 0,a=rM(r);i?.path||(i=n),n.protocol===a.protocol&&n.host===a.host&&(r=a.relative),n.protocol===i.protocol&&n.host===i.host&&(t=i.relative),rC({category:"navigation",data:{from:t,to:r}})})),t.sentry&&e.on("beforeSendEvent",(l=e,function(e){eN()===l&&rC({category:`sentry.${"transaction"===e.type?"transaction":"event"}`,event_id:e.event_id,level:e.level,message:function(e){let{message:t,event_id:r}=e;if(t)return t;let n=ef(e);return n?n.type&&n.value?`${n.type}: ${n.value}`:n.type||n.value||r||"<unknown>":r||"<unknown>"}(e)},{event:e})}))}}},rW=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],rq=(e={})=>{let t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:"BrowserApiErrors",setupOnce(){t.setTimeout&&ei(x,"setTimeout",rz),t.setInterval&&ei(x,"setInterval",rz),t.requestAnimationFrame&&ei(x,"requestAnimationFrame",rX),t.XMLHttpRequest&&"XMLHttpRequest"in x&&ei(XMLHttpRequest.prototype,"send",rG);let e=t.eventTarget;e&&(Array.isArray(e)?e:rW).forEach(rV)}}};function rz(e){return function(...t){let r=t[0];return t[0]=rm(r,{mechanism:{data:{function:eq(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function rX(e){return function(t){return e.apply(this,[rm(t,{mechanism:{data:{function:"requestAnimationFrame",handler:eq(e)},handled:!1,type:"instrument"}})])}}function rG(e){return function(...t){let r=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(e=>{e in r&&"function"==typeof r[e]&&ei(r,e,function(t){let r={mechanism:{data:{function:e,handler:eq(t)},handled:!1,type:"instrument"}},n=es(t);return n&&(r.mechanism.data.handler=eq(n)),rm(t,r)})}),e.apply(this,t)}}function rV(e){let t=x[e]?.prototype;t?.hasOwnProperty?.("addEventListener")&&(ei(t,"addEventListener",function(t){return function(r,n,i){try{var a;a=n,"function"==typeof a.handleEvent&&(n.handleEvent=rm(n.handleEvent,{mechanism:{data:{function:"handleEvent",handler:eq(n),target:e},handled:!1,type:"instrument"}}))}catch{}return t.apply(this,[r,rm(n,{mechanism:{data:{function:"addEventListener",handler:eq(n),target:e},handled:!1,type:"instrument"}}),i])}}),ei(t,"removeEventListener",function(e){return function(t,r,n){try{let i=r.__sentry_wrapped__;i&&e.call(this,t,i,n)}catch(e){}return e.call(this,t,r,n)}}))}let rY=()=>({name:"BrowserSession",setupOnce(){void 0!==x.document&&(tM({ignoreDuration:!0}),tj(),rB(({from:e,to:t})=>{void 0!==e&&e!==t&&(tM({ignoreDuration:!0}),tj())}))}}),rK=null;function rJ(e){let t="error";rv(t,e),rb(t,rQ)}function rQ(){rK=x.onerror,x.onerror=function(e,t,r,n,i){return rE("error",{column:n,error:i,line:r,msg:e,url:t}),!!rK&&rK.apply(this,arguments)},x.onerror.__SENTRY_INSTRUMENTED__=!0}let rZ=null;function r0(e){let t="unhandledrejection";rv(t,e),rb(t,r1)}function r1(){rZ=x.onunhandledrejection,x.onunhandledrejection=function(e){return rE("unhandledrejection",e),!rZ||rZ.apply(this,arguments)},x.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let r2=(e={})=>{let t={onerror:!0,onunhandledrejection:!0,...e};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(e){var r,n;t.onerror&&(r=e,rJ(e=>{let{stackParser:t,attachStacktrace:n}=r5();if(eN()!==r||rh>0)return;let{msg:i,url:a,line:o,column:s,error:l}=e,u=function(e,t,r,n){let i=e.exception=e.exception||{},a=i.values=i.values||[],o=a[0]=a[0]||{},s=o.stacktrace=o.stacktrace||{},l=s.frames=s.frames||[],u=W(t)&&t.length>0?t:Z();return 0===l.length&&l.push({colno:n,filename:u,function:"?",in_app:!0,lineno:r}),e}(rf(t,l||i,void 0,n,!1),a,o,s);u.level="error",tC(u,{originalException:l,mechanism:{handled:!1,type:"onerror"}})})),t.onunhandledrejection&&(n=e,r0(e=>{var t;let{stackParser:r,attachStacktrace:i}=r5();if(eN()!==n||rh>0)return;let a=function(e){if(z(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch{}return e}(e),o=z(a)?(t=a,{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(t)}`}]}}):rf(r,a,void 0,i,!0);o.level="error",tC(o,{originalException:a,mechanism:{handled:!1,type:"onunhandledrejection"}})}))}}};function r3(e){}function r5(){let e=eN();return e?.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}let r4=()=>({name:"HttpContext",preprocessEvent(e){if(!x.navigator&&!x.location&&!x.document)return;let t=e.request?.url||Z(),{referrer:r}=x.document||{},{userAgent:n}=x.navigator||{},i={...e.request?.headers,...r&&{Referer:r},...n&&{"User-Agent":n}},a={...e.request,...t&&{url:t},headers:i};e.request=a}});function r8(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,..."AggregateError"===e.type&&{is_exception_group:!0},exception_id:t}}function r6(e,t,r,n){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:r,parent_id:n}}let r7=(e={})=>{let t=e.limit||5,r=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(e,n,i){!function(e,t,r,n,i,a){if(!i.exception?.values||!a||!Y(a.originalException,Error))return;let o=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;o&&(i.exception.values=function e(t,r,n,i,a,o,s,l){if(o.length>=n+1)return o;let u=[...o];if(Y(i[a],Error)){r8(s,l);let o=t(r,i[a]),c=u.length;r6(o,a,c,l),u=e(t,r,n,i[a],a,[o,...u],o,c)}return Array.isArray(i.errors)&&i.errors.forEach((i,o)=>{if(Y(i,Error)){r8(s,l);let c=t(r,i),d=u.length;r6(c,`errors[${o}]`,d,l),u=e(t,r,n,i,a,[c,...u],c,d)}}),u}(e,t,n,a.originalException,r,i.exception.values,o,0))}(rs,i.getOptions().stackParser,r,t,e,n)}}};function r9(e,t,r,n){let i={filename:e,function:"<anonymous>"===t?"?":t,in_app:!0};return void 0!==r&&(i.lineno=r),void 0!==n&&(i.colno=n),i}let ne=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,nt=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,nr=/\((\S*)(?::(\d+))(?::(\d+))\)/,nn=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,ni=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,na=eH([30,e=>{let t=ne.exec(e);if(t){let[,e,r,n]=t;return r9(e,"?",+r,+n)}let r=nt.exec(e);if(r){if(r[2]&&0===r[2].indexOf("eval")){let e=nr.exec(r[2]);e&&(r[2]=e[1],r[3]=e[2],r[4]=e[3])}let[e,t]=no(r[1]||"?",r[2]);return r9(t,e,r[3]?+r[3]:void 0,r[4]?+r[4]:void 0)}}],[50,e=>{let t=nn.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){let e=ni.exec(t[3]);e&&(t[1]=t[1]||"eval",t[3]=e[1],t[4]=e[2],t[5]="")}let e=t[3],r=t[1]||"?";return[r,e]=no(r,e),r9(e,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),no=(e,t)=>{let r=-1!==e.indexOf("safari-extension"),n=-1!==e.indexOf("safari-web-extension");return r||n?[-1!==e.indexOf("@")?e.split("@")[0]:"?",r?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},ns=Symbol.for("SentryBufferFullError");function nl(e,t,r=Date.now()){return(e[t]||e.all||0)>r}function nu(e,{statusCode:t,headers:r},n=Date.now()){let i={...e},a=r?.["x-sentry-rate-limits"],o=r?.["retry-after"];if(a)for(let e of a.trim().split(",")){let[t,r,,,a]=e.split(":",5),o=parseInt(t,10),s=(isNaN(o)?60:o)*1e3;if(r)for(let e of r.split(";"))"metric_bucket"===e?(!a||a.split(";").includes("custom"))&&(i[e]=n+s):i[e]=n+s;else i.all=n+s}else o?i.all=n+function(e,t=Date.now()){let r=parseInt(`${e}`,10);if(!isNaN(r))return 1e3*r;let n=Date.parse(`${e}`);return isNaN(n)?6e4:n-t}(o,n):429===t&&(i.all=n+6e4);return i}let nc={};function nd(e){let t=nc[e];if(t)return t;let r=x[e];if(tK(r))return nc[e]=r.bind(x);let n=x.document;if(n&&"function"==typeof n.createElement)try{let t=n.createElement("iframe");t.hidden=!0,n.head.appendChild(t);let i=t.contentWindow;i?.[e]&&(r=i[e]),n.head.removeChild(t)}catch(e){}return r?nc[e]=r.bind(x):r}function nf(...e){return nd("setTimeout")(...e)}function np(e,t=nd("fetch")){let r=0,n=0;return function(e,t,r=function(e){let t=[];function r(e){return t.splice(t.indexOf(e),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(n){if(!(void 0===e||t.length<e))return eL(ns);let i=n();return -1===t.indexOf(i)&&t.push(i),i.then(()=>r(i)).then(null,()=>r(i).then(null,()=>{})),i},drain:function(e){return new eU((r,n)=>{let i=t.length;if(!i)return r(!0);let a=setTimeout(()=>{e&&e>0&&r(!1)},e);t.forEach(e=>{eD(e).then(()=>{--i||(clearTimeout(a),r(!0))},n)})})}}}(e.bufferSize||64)){let n={};return{send:function(i){let a=[];if(t2(i,(t,r)=>{let i=t5[r];nl(n,i)?e.recordDroppedEvent("ratelimit_backoff",i):a.push(t)}),0===a.length)return eD({});let o=t1(i[0],a),s=t=>{t2(o,(r,n)=>{e.recordDroppedEvent(t,t5[n])})};return r.add(()=>t({body:function(e){let[t,r]=e,n=JSON.stringify(t);function i(e){"string"==typeof n?n="string"==typeof e?n+e:[t3(n),e]:n.push("string"==typeof e?t3(e):e)}for(let e of r){let[t,r]=e;if(i(`
${JSON.stringify(t)}
`),"string"==typeof r||r instanceof Uint8Array)i(r);else{let e;try{e=JSON.stringify(r)}catch(t){e=JSON.stringify(eX(r))}i(e)}}return"string"==typeof n?n:function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}(n)}(o)}).then(e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode),n=nu(n,e),e),e=>{throw s("network_error"),e})).then(e=>e,e=>{if(e===ns)return s("queue_overflow"),eD({});throw e})},flush:e=>r.drain(e)}}(e,function(i){let a=i.body.length;r+=a,n++;let o={body:i.body,method:"POST",referrerPolicy:"strict-origin",headers:e.headers,keepalive:r<=6e4&&n<15,...e.fetchOptions};if(!t)return nc.fetch=void 0,eL("No fetch implementation available");try{return t(e.url,o).then(e=>(r-=a,n--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}}))}catch(e){return nc.fetch=void 0,r-=a,n--,eL(e)}})}function nh(e){return[tH(),tz(),rq(),r$(),r2(),r7(),tX(),r4(),rY()]}var nm,ng,n_=r(44109),ny=r(57177);class nv{constructor(e={}){this._traceId=e.traceId||ed(),this._spanId=e.spanId||ew()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(e){}setAttribute(e,t){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,t,r){return this}addLink(e){return this}addLinks(e){return this}recordException(e,t){}}function nb(e){if(!e||0===e.length)return;let t={};return e.forEach(e=>{let r=e.attributes||{},n=r[eZ],i=r[e0];"string"==typeof n&&"number"==typeof i&&(t[e.name]={value:i,unit:n})}),t}class nE{constructor(e={}){this._traceId=e.traceId||ed(),this._spanId=e.spanId||ew(),this._startTime=e.startTimestamp||e_(),this._links=e.links,this._attributes={},this.setAttributes({[eJ]:"manual",[eK]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,t){}spanContext(){let{_spanId:e,_traceId:t,_sampled:r}=this;return{spanId:e,traceId:t,traceFlags:+!!r}}setAttribute(e,t){return void 0===t?delete this._attributes[e]:this._attributes[e]=t,this}setAttributes(e){return Object.keys(e).forEach(t=>this.setAttribute(t,e[t])),this}updateStartTime(e){this._startTime=tu(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(eG,"custom"),this}end(e){var t;this._endTime||(this._endTime=tu(e),t=0,this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[eK],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:tp(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[eJ],profile_id:this._attributes[e2],exclusive_time:this._attributes[e3],measurements:nb(this._events),is_segment:this._isStandaloneSpan&&ty(this)===this||void 0,segment_id:this._isStandaloneSpan?ty(this).spanContext().spanId:void 0,links:tl(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,t,r){let n=nS(t)?t:r||e_(),i=nS(t)?{}:t||{},a={name:e,time:tu(n),attributes:i};return this._events.push(a),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){let e=eN();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===ty(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(e){let t=eN();if(!t)return;let r=e[1];if(!r||0===r.length)return t.recordDroppedEvent("before_send","span");t.sendEnvelope(e)}(function(e,t){let r=tR(e[0]),n=t?.getDsn(),i=t?.getOptions().tunnel,a={sent_at:new Date().toISOString(),...!!r.trace_id&&!!r.public_key&&{trace:r},...!!i&&n&&{dsn:tQ(n)}},o=t?.getOptions().beforeSendSpan,s=o?e=>{let t=td(e),r=o(t);return r||(tb(),t)}:td,l=[];for(let t of e){let e=s(t);e&&l.push([{type:"span"},e])}return t1(a,l)}([this],e)):e&&e.recordDroppedEvent("sample_rate","span"));let t=this._convertSpanToTransaction();t&&(e7(this).scope||ek()).captureEvent(t)}_convertSpanToTransaction(){if(!nw(td(this)))return;this._name||(this._name="<unlabeled transaction>");let{scope:e,isolationScope:t}=e7(this);if(!0!==this._sampled)return;let r=t_(this).filter(e=>{var t;return e!==this&&!((t=e)instanceof nE&&t.isStandaloneSpan())}).map(e=>td(e)).filter(nw),n=this._attributes[eG];delete this._attributes[e1],r.forEach(e=>{delete e.data[e1]});let i={contexts:{trace:function(e){let{spanId:t,traceId:r}=e.spanContext(),{data:n,op:i,parent_span_id:a,status:o,origin:s,links:l}=td(e);return{parent_span_id:a,span_id:t,trace_id:r,data:n,op:i,status:o,origin:s,links:l}}(this)},spans:r.length>1e3?r.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):r,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:t,dynamicSamplingContext:tR(this)},...n&&{transaction_info:{source:n}}},a=nb(this._events);return a&&Object.keys(a).length&&(i.measurements=a),i}}function nS(e){return e&&"number"==typeof e||e instanceof Date||Array.isArray(e)}function nw(e){return!!e.start_timestamp&&!!e.timestamp&&!!e.span_id&&!!e.trace_id}let nR="__SENTRY_SUPPRESS_TRACING__";function nP(e){let t=function(){return eI(I())}();if(t.startInactiveSpan)return t.startInactiveSpan(e);let r=function(e){let t={isStandalone:(e.experimental||{}).standalone,...e};if(e.startTime){let r={...t};return r.startTimestamp=tu(e.startTime),delete r.startTime,r}return t}(e),{forceTransaction:n,parentSpan:i}=e;return(e.scope?t=>eA(e.scope,t):void 0!==i?e=>nT(i,e):e=>e())(()=>{let t=ek(),i=function(e){let t=e[eE];if(!t)return;let r=eN();return(r?r.getOptions():{}).parentSpanIsAlwaysRootSpan?ty(t):t}(t);return e.onlyIfParent&&!i?new nv:function({parentSpan:e,spanArguments:t,forceTransaction:r,scope:n}){var i,a,o,s;let l;if(!e5()){let n=new nv;if(r||!e){let e={sampled:"false",sample_rate:"0",transaction:t.name,...tR(n)};ea(n,tE,e)}return n}let u=eM();if(e&&!r)l=function(e,t,r){let{spanId:n,traceId:i}=e.spanContext(),a=!t.getScopeData().sdkProcessingMetadata[nR]&&tf(e),o=a?new nE({...r,parentSpanId:n,traceId:i,sampled:a}):new nv({traceId:i});tg(e,o);let s=eN();return s&&(s.emit("spanStart",o),r.endTimestamp&&s.emit("spanEnd",o)),o}(e,n,t),tg(e,l);else if(e){let r=tR(e),{traceId:i,spanId:a}=e.spanContext(),o=tf(e);ea(l=nx({traceId:i,parentSpanId:a,...t},n,o),tE,r)}else{let{traceId:e,dsc:r,parentSpanId:i,sampled:a}={...u.getPropagationContext(),...n.getPropagationContext()};l=nx({traceId:e,parentSpanId:i,...t},n,a),r&&ea(l,tE,r)}return s=0,(o=l)&&(ea(o,e6,u),ea(o,e8,n)),l}({parentSpan:i,spanArguments:r,forceTransaction:n,scope:t})})}function nT(e,t){let r=function(){return eI(I())}();return r.withActiveSpan?r.withActiveSpan(e,t):eA(r=>(eS(r,e||void 0),t(r)))}function nO(){return eI(I())}function nx(e,t,r){let n=eN(),i=n?.getOptions()||{},{name:a=""}=e,o={spanAttributes:{...e.attributes},spanName:a,parentSampled:r};n?.emit("beforeSampling",o,{decision:!1});let s=o.parentSampled??r,l=o.spanAttributes,u=t.getPropagationContext(),[c,d,f]=t.getScopeData().sdkProcessingMetadata[nR]?[!1]:function(e,t,r){let n,i;if(!e5(e))return[!1];"function"==typeof e.tracesSampler?(n=e.tracesSampler({...t,inheritOrSampleWith:e=>"number"==typeof t.parentSampleRate?t.parentSampleRate:"boolean"==typeof t.parentSampled?Number(t.parentSampled):e}),i=!0):void 0!==t.parentSampled?n=t.parentSampled:void 0!==e.tracesSampleRate&&(n=e.tracesSampleRate,i=!0);let a=e9(n);if(void 0===a)return[!1];if(!a)return[!1,a,i];let o=r<a;return[o,a,i]}(i,{name:a,parentSampled:s,attributes:l,parentSampleRate:e9(u.dsc?.sample_rate)},u.sampleRand),p=new nE({...e,attributes:{[eG]:"custom",[eV]:void 0!==d&&f?d:void 0,...l},sampled:c});return!c&&n&&n.recordDroppedEvent("sample_rate","transaction"),n&&n.emit("spanStart",p),p}let nC={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3};function nI(e,t={}){let r,n=new Map,i=!1,a="externalFinish",o=!t.disableAutoFinish,s=[],{idleTimeout:l=nC.idleTimeout,finalTimeout:u=nC.finalTimeout,childSpanTimeout:c=nC.childSpanTimeout,beforeSpanEnd:d}=t,f=eN();if(!f||!e5()){let e=new nv,t={sample_rate:"0",sampled:"false",...tR(e)};return ea(e,tE,t),e}let p=ek(),h=tv(),m=function(e){let t=nP(e);return eS(ek(),t),t}(e);function g(){r&&(clearTimeout(r),r=void 0)}function _(e){g(),r=setTimeout(()=>{!i&&0===n.size&&o&&(a="idleTimeout",m.end(e))},l)}function y(e){r=setTimeout(()=>{!i&&o&&(a="heartbeatFailed",m.end(e))},c)}function v(e){i=!0,n.clear(),s.forEach(e=>e()),eS(p,h);let t=td(m),{start_timestamp:r}=t;if(!r)return;t.data[eQ]||m.setAttribute(eQ,a),D.log(`[Tracing] Idle span "${t.op}" finished`);let o=t_(m).filter(e=>e!==m),c=0;o.forEach(t=>{t.isRecording()&&(t.setStatus({code:2,message:"cancelled"}),t.end(e));let{timestamp:r=0,start_timestamp:n=0}=td(t),i=n<=e;(!(r-n<=(u+l)/1e3)||!i)&&(m[th]&&m[th].delete(t),c++)}),c>0&&m.setAttribute("sentry.idle_span_discarded_spans",c)}return m.end=new Proxy(m.end,{apply(e,t,r){if(d&&d(m),t instanceof nv)return;let[n,...i]=r,a=tu(n||e_()),o=t_(m).filter(e=>e!==m);if(!o.length)return v(a),Reflect.apply(e,t,[a,...i]);let s=o.map(e=>td(e).timestamp).filter(e=>!!e),l=s.length?Math.max(...s):void 0,c=td(m).start_timestamp,f=Math.min(c?c+u/1e3:1/0,Math.max(c||-1/0,Math.min(a,l||1/0)));return v(f),Reflect.apply(e,t,[f,...i])}}),s.push(f.on("spanStart",e=>{var t;i||e===m||td(e).timestamp||t_(m).includes(e)&&(t=e.spanContext().spanId,g(),n.set(t,!0),y(e_()+c/1e3))})),s.push(f.on("spanEnd",e=>{if(!i){var t;t=e.spanContext().spanId,n.has(t)&&n.delete(t),0===n.size&&_(e_()+l/1e3)}})),s.push(f.on("idleSpanEnableAutoFinish",e=>{e===m&&(o=!0,_(),n.size&&y())})),t.disableAutoFinish||_(),setTimeout(()=>{i||(m.setStatus({code:2,message:"deadline_exceeded"}),a="finalTimeout",m.end())},u),m}let nk=!1;function nM(){let e=tv(),t=e&&ty(e);t&&t.setStatus({code:2,message:"internal_error"})}nM.tag="sentry_tracingErrorCallback";let nA=(e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good",nN=(e,t,r,n)=>{let i,a;return o=>{t.value>=0&&(o||n)&&((a=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=a,t.rating=nA(t.value,r),e(t))}},nj=()=>`v4-${Date.now()}-${Math.floor(Math.random()*(9e12-1))+1e12}`,nD=(e=!0)=>{let t=x.performance?.getEntriesByType?.("navigation")[0];if(!e||t&&t.responseStart>0&&t.responseStart<performance.now())return t},nL=()=>{let e=nD();return e?.activationStart||0},nU=(e,t)=>{let r=nD(),n="navigate";return r&&(x.document?.prerendering||nL()>0?n="prerender":x.document?.wasDiscarded?n="restore":r.type&&(n=r.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:nj(),navigationType:n}},nF=(e,t,r)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){let n=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return n.observe(Object.assign({type:e,buffered:!0},r||{})),n}}catch(e){}},nB=e=>{let t=t=>{("pagehide"===t.type||x.document?.visibilityState==="hidden")&&e(t)};x.document&&(addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0))},nH=e=>{let t=!1;return()=>{t||(e(),t=!0)}},n$=-1,nW=()=>"hidden"!==x.document.visibilityState||x.document.prerendering?1/0:0,nq=e=>{"hidden"===x.document.visibilityState&&n$>-1&&(n$="visibilitychange"===e.type?e.timeStamp:0,nX())},nz=()=>{addEventListener("visibilitychange",nq,!0),addEventListener("prerenderingchange",nq,!0)},nX=()=>{removeEventListener("visibilitychange",nq,!0),removeEventListener("prerenderingchange",nq,!0)},nG=()=>(x.document&&n$<0&&(n$=nW(),nz()),{get firstHiddenTime(){return n$}}),nV=e=>{x.document?.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},nY=[1800,3e3],nK=(e,t={})=>{nV(()=>{let r,n=nG(),i=nU("FCP"),a=nF("paint",e=>{e.forEach(e=>{"first-contentful-paint"===e.name&&(a.disconnect(),e.startTime<n.firstHiddenTime&&(i.value=Math.max(e.startTime-nL(),0),i.entries.push(e),r(!0)))})});a&&(r=nN(e,i,nY,t.reportAllChanges))})},nJ=[.1,.25],nQ=(e,t={})=>{nK(nH(()=>{let r,n=nU("CLS",0),i=0,a=[],o=e=>{e.forEach(e=>{if(!e.hadRecentInput){let t=a[0],r=a[a.length-1];i&&t&&r&&e.startTime-r.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,a.push(e)):(i=e.value,a=[e])}}),i>n.value&&(n.value=i,n.entries=a,r())},s=nF("layout-shift",o);s&&(r=nN(e,n,nJ,t.reportAllChanges),nB(()=>{o(s.takeRecords()),r(!0)}),setTimeout(r,0))}))},nZ=[100,300],n0=(e,t={})=>{nV(()=>{let r,n=nG(),i=nU("FID"),a=e=>{e.startTime<n.firstHiddenTime&&(i.value=e.processingStart-e.startTime,i.entries.push(e),r(!0))},o=e=>{e.forEach(a)},s=nF("first-input",o);r=nN(e,i,nZ,t.reportAllChanges),s&&nB(nH(()=>{o(s.takeRecords()),s.disconnect()}))})},n1=0,n2=1/0,n3=0,n5=e=>{e.forEach(e=>{e.interactionId&&(n2=Math.min(n2,e.interactionId),n1=(n3=Math.max(n3,e.interactionId))?(n3-n2)/7+1:0)})},n4=()=>f?n1:performance.interactionCount||0,n8=()=>{"interactionCount"in performance||f||(f=nF("event",n5,{type:"event",buffered:!0,durationThreshold:0}))},n6=[],n7=new Map,n9=()=>n4()-0,ie=()=>{let e=Math.min(n6.length-1,Math.floor(n9()/50));return n6[e]},it=[],ir=e=>{if(it.forEach(t=>t(e)),!(e.interactionId||"first-input"===e.entryType))return;let t=n6[n6.length-1],r=n7.get(e.interactionId);if(r||n6.length<10||t&&e.duration>t.latency){if(r)e.duration>r.latency?(r.entries=[e],r.latency=e.duration):e.duration===r.latency&&e.startTime===r.entries[0]?.startTime&&r.entries.push(e);else{let t={id:e.interactionId,latency:e.duration,entries:[e]};n7.set(t.id,t),n6.push(t)}n6.sort((e,t)=>t.latency-e.latency),n6.length>10&&n6.splice(10).forEach(e=>n7.delete(e.id))}},ii=e=>{let t=x.requestIdleCallback||x.setTimeout,r=-1;return e=nH(e),x.document?.visibilityState==="hidden"?e():(r=t(e),nB(e)),r},ia=[200,500],io=(e,t={})=>{"PerformanceEventTiming"in x&&"interactionId"in PerformanceEventTiming.prototype&&nV(()=>{let r;n8();let n=nU("INP"),i=e=>{ii(()=>{e.forEach(ir);let t=ie();t&&t.latency!==n.value&&(n.value=t.latency,n.entries=t.entries,r())})},a=nF("event",i,{durationThreshold:null!=t.durationThreshold?t.durationThreshold:40});r=nN(e,n,ia,t.reportAllChanges),a&&(a.observe({type:"first-input",buffered:!0}),nB(()=>{i(a.takeRecords()),r(!0)}))})},is=[2500,4e3],il={},iu=(e,t={})=>{nV(()=>{let r,n=nG(),i=nU("LCP"),a=e=>{t.reportAllChanges||(e=e.slice(-1)),e.forEach(e=>{e.startTime<n.firstHiddenTime&&(i.value=Math.max(e.startTime-nL(),0),i.entries=[e],r())})},o=nF("largest-contentful-paint",a);if(o){r=nN(e,i,is,t.reportAllChanges);let n=nH(()=>{il[i.id]||(a(o.takeRecords()),o.disconnect(),il[i.id]=!0,r(!0))});["keydown","click"].forEach(e=>{x.document&&addEventListener(e,()=>ii(n),{once:!0,capture:!0})}),nB(n)}})},ic=[800,1800],id=e=>{x.document?.prerendering?nV(()=>id(e)):x.document?.readyState!=="complete"?addEventListener("load",()=>id(e),!0):setTimeout(e,0)},ip=(e,t={})=>{let r=nU("TTFB"),n=nN(e,r,ic,t.reportAllChanges);id(()=>{let e=nD();e&&(r.value=Math.max(e.responseStart-nL(),0),r.entries=[e],n(!0))})},ih={},im={};function ig(e,t=!1){return iO("cls",e,iS,p,t)}function i_(e,t=!1){return iO("lcp",e,iR,m,t)}function iy(e){return iO("fid",e,iw,h)}function iv(e){return iO("inp",e,iT,_)}function ib(e,t){return ix(e,t),im[e]||(function(e){let t={};"event"===e&&(t.durationThreshold=0),nF(e,t=>{iE(e,{entries:t})},t)}(e),im[e]=!0),iC(e,t)}function iE(e,t){let r=ih[e];if(r?.length)for(let e of r)try{e(t)}catch(e){}}function iS(){return nQ(e=>{iE("cls",{metric:e}),p=e},{reportAllChanges:!0})}function iw(){return n0(e=>{iE("fid",{metric:e}),h=e})}function iR(){return iu(e=>{iE("lcp",{metric:e}),m=e},{reportAllChanges:!0})}function iP(){return ip(e=>{iE("ttfb",{metric:e}),g=e})}function iT(){return io(e=>{iE("inp",{metric:e}),_=e})}function iO(e,t,r,n,i=!1){let a;return ix(e,t),im[e]||(a=r(),im[e]=!0),n&&t({metric:n}),iC(e,t,i?a:void 0)}function ix(e,t){ih[e]=ih[e]||[],ih[e].push(t)}function iC(e,t,r){return()=>{r&&r();let n=ih[e];if(!n)return;let i=n.indexOf(t);-1!==i&&n.splice(i,1)}}function iI(e){return"number"==typeof e&&isFinite(e)}function ik(e,t,r,{...n}){let i=td(e).start_timestamp;return i&&i>t&&"function"==typeof e.updateStartTime&&e.updateStartTime(t),nT(e,()=>{let e=nP({startTime:t,...n});return e&&e.end(r),e})}function iM(e){let t,r=eN();if(!r)return;let{name:n,transaction:i,attributes:a,startTime:o}=e,{release:s,environment:l,sendDefaultPii:u}=r.getOptions(),c=r.getIntegrationByName("Replay"),d=c?.getReplayId(),f=ek(),p=f.getUser(),h=void 0!==p?p.email||p.id||p.ip_address:void 0;try{t=f.getScopeData().contexts.profile.profile_id}catch{}return nP({name:n,attributes:{release:s,environment:l,user:h||void 0,profile_id:t||void 0,replay_id:d||void 0,transaction:i,"user_agent.original":x.navigator?.userAgent,"client.address":u?"{{auto}}":void 0,...a},startTime:o,experimental:{standalone:!0}})}function iA(){return x.addEventListener&&x.performance}function iN(e){return e/1e3}function ij(e){let t="unknown",r="unknown",n="";for(let i of e){if("/"===i){[t,r]=e.split("/");break}if(!isNaN(Number(i))){t="h"===n?"http":n,r=e.split(n)[1];break}n+=i}return n===e&&(t=n),{name:t,version:r}}let iD=0,iL={};function iU(e,t,r,n,i=r){var a;let o=t["secureConnection"===(a=r)?"connectEnd":"fetch"===a?"domainLookupStart":`${a}End`],s=t[`${r}Start`];s&&o&&ik(e,n+iN(s),n+iN(o),{op:`browser.${i}`,name:t.name,attributes:{[eJ]:"auto.ui.browser.metrics",..."redirect"===r&&null!=t.redirectCount?{"http.redirect_count":t.redirectCount}:{}}})}function iF(e,t,r,n){let i=t[r];null!=i&&i<0x7fffffff&&(e[n]=i)}let iB=[],iH=new Map,i$={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"},iW="sentry_previous_trace";function iq(e){return 1===e.traceFlags}function iz(e={}){let t=eN();if(!function(){let e=eN();return e?.getOptions().enabled!==!1&&!!e?.getTransport()}()||!t)return{};let r=eI(I());if(r.getTraceData)return r.getTraceData(e);let n=ek(),i=e.span||tv(),a=i?function(e){let{traceId:t,spanId:r}=e.spanContext();return to(t,r,tf(e))}(i):function(e){let{traceId:t,sampled:r,propagationSpanId:n}=e.getPropagationContext();return to(t,n,r)}(n),o=function(e){if(e){var t=Object.entries(e).reduce((e,[t,r])=>(r&&(e[`${te}${t}`]=r),e),{});return 0!==Object.keys(t).length?Object.entries(t).reduce((e,[t,r],n)=>{let i=`${encodeURIComponent(t)}=${encodeURIComponent(r)}`,a=0===n?i:`${e},${i}`;return a.length>8192?e:a},""):void 0}}(i?tR(i):tw(t,n));return ta.test(a)?{"sentry-trace":a,baggage:o}:(D.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}function iX(e){return e.split(",").some(e=>e.trim().startsWith(te))}let iG=new WeakMap,iV=new Map,iY={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function iK(e){let{url:t}=td(e).data;if(!t||"string"!=typeof t)return;let r=ib("resource",({entries:n})=>{n.forEach(n=>{"resource"===n.entryType&&"initiatorType"in n&&"string"==typeof n.nextHopProtocol&&("fetch"===n.initiatorType||"xmlhttprequest"===n.initiatorType)&&n.name.endsWith(t)&&((function(e){let{name:t,version:r}=ij(e.nextHopProtocol),n=[];return(n.push(["network.protocol.version",r],["network.protocol.name",t]),ey())?[...n,["http.request.redirect_start",iJ(e.redirectStart)],["http.request.fetch_start",iJ(e.fetchStart)],["http.request.domain_lookup_start",iJ(e.domainLookupStart)],["http.request.domain_lookup_end",iJ(e.domainLookupEnd)],["http.request.connect_start",iJ(e.connectStart)],["http.request.secure_connection_start",iJ(e.secureConnectionStart)],["http.request.connection_end",iJ(e.connectEnd)],["http.request.request_start",iJ(e.requestStart)],["http.request.response_start",iJ(e.responseStart)],["http.request.response_end",iJ(e.responseEnd)]]:n})(n).forEach(t=>e.setAttribute(...t)),setTimeout(r))})})}function iJ(e=0){return((ey()||performance.timeOrigin)+e)/1e3}function iQ(e){try{return new URL(e,x.location.origin).href}catch{return}}let iZ={...nC,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...iY},i0=!1,i1=(e={})=>{i0&&j(()=>{console.warn("Multiple browserTracingIntegration instances are not supported.")}),i0=!0;let t=x.document;nk||(nk=!0,rJ(nM),r0(nM));let{enableInp:r,enableLongTask:n,enableLongAnimationFrame:i,_experiments:{enableInteractions:a,enableStandaloneClsSpans:o},beforeStartSpan:s,idleTimeout:l,finalTimeout:u,childSpanTimeout:c,markBackgroundSpan:d,traceFetch:f,traceXHR:p,trackFetchStreamPerformance:h,shouldCreateSpanForRequest:m,enableHTTPTimings:_,instrumentPageLoad:b,instrumentNavigation:E,linkPreviousTrace:S,consistentTraceSampling:w,onRequestSpanStart:R}={...iZ,...e},P=function({recordClsStandaloneSpans:e}){let t=iA();if(t&&ey()){t.mark&&x.performance.mark("sentry-tracing-init");let r=iy(({metric:e})=>{let t=e.entries[e.entries.length-1];if(!t)return;let r=iN(ey()),n=iN(t.startTime);iL.fid={value:e.value,unit:"millisecond"},iL["mark.fid"]={value:r+n,unit:"second"}}),n=i_(({metric:e})=>{let t=e.entries[e.entries.length-1];t&&(iL.lcp={value:e.value,unit:"millisecond"},y=t)},!0),i=iO("ttfb",({metric:e})=>{e.entries[e.entries.length-1]&&(iL.ttfb={value:e.value,unit:"millisecond"})},iP,g),a=e?function(){let e,t,r=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch{return!1}}())return;let n=!1;function i(){n||(n=!0,t&&function(e,t,r){let n=iN((ey()||0)+(t?.startTime||0)),i=ek().getScopeData().transactionName,a=iM({name:t?Q(t.sources[0]?.node):"Layout shift",transaction:i,attributes:{[eJ]:"auto.http.browser.cls",[eK]:"ui.webvital.cls",[e3]:t?.duration||0,"sentry.pageload.span_id":r},startTime:n});a&&(a.addEvent("cls",{[eZ]:"",[e0]:e}),a.end(n))}(r,e,t),a())}let a=ig(({metric:t})=>{let n=t.entries[t.entries.length-1];n&&(r=t.value,e=n)},!0);nB(()=>{i()}),setTimeout(()=>{let e=eN();if(!e)return;let r=e.on("startNavigationSpan",()=>{i(),r?.()}),n=tv();if(n){let e=ty(n);"pageload"===td(e).op&&(t=e.spanContext().spanId)}},0)}():ig(({metric:e})=>{let t=e.entries[e.entries.length-1];t&&(iL.cls={value:e.value,unit:""},v=t)},!0);return()=>{r(),n(),i(),a?.()}}return()=>void 0}({recordClsStandaloneSpans:o||!1});r&&function(){if(iA()&&ey()){let e=iv(({metric:e})=>{if(void 0==e.value)return;let t=e.entries.find(t=>t.duration===e.value&&i$[t.name]);if(!t)return;let{interactionId:r}=t,n=i$[t.name],i=iN(ey()+t.startTime),a=iN(e.value),o=tv(),s=o?ty(o):void 0,l=(null!=r?iH.get(r):void 0)||s,u=l?td(l).description:ek().getScopeData().transactionName,c=iM({name:Q(t.target),transaction:u,attributes:{[eJ]:"auto.http.browser.inp",[eK]:`ui.interaction.${n}`,[e3]:t.duration},startTime:i});c&&(c.addEvent("inp",{[eZ]:"millisecond",[e0]:e.value}),c.end(i+a))});()=>{e()}}}(),i&&x.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(e=>{let t=tv();if(t)for(let r of e.getEntries()){if(!r.scripts[0])continue;let e=iN(ey()+r.startTime),{start_timestamp:n,op:i}=td(t);if("navigation"===i&&n&&e<n)continue;let a=iN(r.duration),o={[eJ]:"auto.ui.browser.metrics"},{invoker:s,invokerType:l,sourceURL:u,sourceFunctionName:c,sourceCharPosition:d}=r.scripts[0];o["browser.script.invoker"]=s,o["browser.script.invoker_type"]=l,u&&(o["code.filepath"]=u),c&&(o["code.function"]=c),-1!==d&&(o["browser.script.source_char_position"]=d),ik(t,e,e+a,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:o})}}).observe({type:"long-animation-frame",buffered:!0}):n&&ib("longtask",({entries:e})=>{let t=tv();if(!t)return;let{op:r,start_timestamp:n}=td(t);for(let i of e){let e=iN(ey()+i.startTime),a=iN(i.duration);"navigation"===r&&n&&e<n||ik(t,e,e+a,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[eJ]:"auto.ui.browser.metrics"}})}}),a&&ib("event",({entries:e})=>{let t=tv();if(t){for(let r of e)if("click"===r.name){let e=iN(ey()+r.startTime),n=iN(r.duration),i={name:Q(r.target),op:`ui.interaction.${r.name}`,startTime:e,attributes:{[eJ]:"auto.ui.browser.metrics"}},a=ee(r.target);a&&(i.attributes["ui.component_name"]=a),ik(t,e,e+n,i)}}});let T={name:void 0,source:void 0};function O(e,r){let n="pageload"===r.op,i=s?s(r):r,a=i.attributes||{};r.name!==i.name&&(a[eG]="custom",i.attributes=a),T.name=i.name,T.source=a[eG];let d=nI(i,{idleTimeout:l,finalTimeout:u,childSpanTimeout:c,disableAutoFinish:n,beforeSpanEnd:t=>{P(),function(e,t){let r=iA(),n=ey();if(!r?.getEntries||!n)return;let i=iN(n),a=r.getEntries(),{op:o,start_timestamp:s}=td(e);if(a.slice(iD).forEach(t=>{let r=iN(t.startTime),n=iN(Math.max(0,t.duration));if("navigation"!==o||!s||!(i+r<s))switch(t.entryType){case"navigation":var a,l,u;a=e,l=t,u=i,["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(e=>{iU(a,l,e,u)}),iU(a,l,"secureConnection",u,"TLS/SSL"),iU(a,l,"fetch",u,"cache"),iU(a,l,"domainLookup",u,"DNS"),function(e,t,r){let n=r+iN(t.requestStart),i=r+iN(t.responseEnd),a=r+iN(t.responseStart);t.responseEnd&&(ik(e,n,i,{op:"browser.request",name:t.name,attributes:{[eJ]:"auto.ui.browser.metrics"}}),ik(e,a,i,{op:"browser.response",name:t.name,attributes:{[eJ]:"auto.ui.browser.metrics"}}))}(a,l,u);break;case"mark":case"paint":case"measure":{!function(e,t,r,n,i){let a=nD(!1),o=i+Math.max(r,iN(a?a.requestStart:0)),s=i+r,l=s+n,u={[eJ]:"auto.resource.browser.metrics"};o!==s&&(u["sentry.browser.measure_happened_before_request"]=!0,u["sentry.browser.measure_start_time"]=o),o<=l&&ik(e,o,l,{name:t.name,op:t.entryType,attributes:u})}(e,t,r,n,i);let a=nG(),o=t.startTime<a.firstHiddenTime;"first-paint"===t.name&&o&&(iL.fp={value:t.startTime,unit:"millisecond"}),"first-contentful-paint"===t.name&&o&&(iL.fcp={value:t.startTime,unit:"millisecond"});break}case"resource":!function(e,t,r,n,i,a){if("xmlhttprequest"===t.initiatorType||"fetch"===t.initiatorType)return;let o=rM(r),s={[eJ]:"auto.resource.browser.metrics"};iF(s,t,"transferSize","http.response_transfer_size"),iF(s,t,"encodedBodySize","http.response_content_length"),iF(s,t,"decodedBodySize","http.decoded_response_content_length");let l=t.deliveryType;null!=l&&(s["http.response_delivery_type"]=l);let u=t.renderBlockingStatus;u&&(s["resource.render_blocking_status"]=u),o.protocol&&(s["url.scheme"]=o.protocol.split(":").pop()),o.host&&(s["server.address"]=o.host),s["url.same_origin"]=r.includes(x.location.origin);let{name:c,version:d}=ij(t.nextHopProtocol);s["network.protocol.name"]=c,s["network.protocol.version"]=d;let f=a+n;ik(e,f,f+i,{name:r.replace(x.location.origin,""),op:t.initiatorType?`resource.${t.initiatorType}`:"resource.other",attributes:s})}(e,t,t.name,r,n,i)}}),iD=Math.max(a.length-1,0),function(e){let t=x.navigator;if(!t)return;let r=t.connection;r&&(r.effectiveType&&e.setAttribute("effectiveConnectionType",r.effectiveType),r.type&&e.setAttribute("connectionType",r.type),iI(r.rtt)&&(iL["connection.rtt"]={value:r.rtt,unit:"millisecond"})),iI(t.deviceMemory)&&e.setAttribute("deviceMemory",`${t.deviceMemory} GB`),iI(t.hardwareConcurrency)&&e.setAttribute("hardwareConcurrency",String(t.hardwareConcurrency))}(e),"pageload"===o){var l;!function(e){let t=nD(!1);if(!t)return;let{responseStart:r,requestStart:n}=t;n<=r&&(e["ttfb.requestTime"]={value:r-n,unit:"millisecond"})}(iL);let r=iL["mark.fid"];r&&iL.fid&&(ik(e,r.value,r.value+iN(iL.fid.value),{name:"first input delay",op:"ui.action",attributes:{[eJ]:"auto.ui.browser.metrics"}}),delete iL["mark.fid"]),"fcp"in iL&&t.recordClsOnPageloadSpan||delete iL.cls,Object.entries(iL).forEach(([e,t])=>{!function(e,t,r,n=tv()){let i=n&&ty(n);i&&i.addEvent(e,{[e0]:t,[eZ]:r})}(e,t.value,t.unit)}),e.setAttribute("performance.timeOrigin",i),e.setAttribute("performance.activationStart",nL()),l=e,y&&(y.element&&l.setAttribute("lcp.element",Q(y.element)),y.id&&l.setAttribute("lcp.id",y.id),y.url&&l.setAttribute("lcp.url",y.url.trim().slice(0,200)),null!=y.loadTime&&l.setAttribute("lcp.loadTime",y.loadTime),null!=y.renderTime&&l.setAttribute("lcp.renderTime",y.renderTime),l.setAttribute("lcp.size",y.size)),v?.sources&&v.sources.forEach((e,t)=>l.setAttribute(`cls.source.${t+1}`,Q(e.node)))}y=void 0,v=void 0,iL={}}(t,{recordClsOnPageloadSpan:!o}),ea(e,i4,void 0);let r=ek(),n=r.getPropagationContext();r.setPropagationContext({...n,traceId:d.spanContext().traceId,sampled:tf(d),dsc:tR(t)})}});function f(){t&&["interactive","complete"].includes(t.readyState)&&e.emit("idleSpanEnableAutoFinish",d)}ea(e,i4,d),n&&t&&(t.addEventListener("readystatechange",()=>{f()}),f())}return{name:"BrowserTracing",afterAllSetup(e){var t,n,i,o,s;let g,y=Z();function v(){let t=e[i4];t&&!td(t).timestamp&&(t.setAttribute(eQ,"cancelled"),t.end())}if(e.on("startNavigationSpan",t=>{eN()===e&&(v(),eM().setPropagationContext({traceId:ed(),sampleRand:Math.random()}),ek().setPropagationContext({traceId:ed(),sampleRand:Math.random()}),O(e,{op:"navigation",...t}))}),e.on("startPageLoadSpan",(t,r={})=>{if(eN()!==e)return;v();let n=function(e,t){let r=function(e){let t;if(!e)return;let r=e.match(ta);if(r)return"1"===r[3]?t=!0:"0"===r[3]&&(t=!1),{traceId:r[1],parentSampled:t,parentSpanId:r[2]}}(e),n=tr(t);if(!r?.traceId)return{traceId:ed(),sampleRand:Math.random()};let i=function(e,t){let r=e9(t?.sample_rand);if(void 0!==r)return r;let n=e9(t?.sample_rate);return n&&e?.parentSampled!==void 0?e.parentSampled?Math.random()*n:n+Math.random()*(1-n):Math.random()}(r,n);n&&(n.sample_rand=i.toString());let{traceId:a,parentSpanId:o,parentSampled:s}=r;return{traceId:a,parentSpanId:o,sampled:s,dsc:n||{},sampleRand:i}}(r.sentryTrace||i5("sentry-trace"),r.baggage||i5("baggage"));ek().setPropagationContext(n),O(e,{op:"pageload",...t})}),"off"!==S&&function(e,{linkPreviousTrace:t,consistentTraceSampling:r}){let n="session-storage"===t,i=n?function(){try{let e=x.sessionStorage?.getItem(iW);return JSON.parse(e)}catch(e){return}}():void 0;e.on("spanStart",e=>{if(ty(e)!==e)return;let t=ek().getPropagationContext();i=function(e,t,r){let n=td(t),i={spanContext:t.spanContext(),startTimestamp:n.start_timestamp,sampleRate:function(){try{return Number(r.dsc?.sample_rate)??Number(n.data?.[eV])}catch{return 0}}(),sampleRand:r.sampleRand};if(!e)return i;let a=e.spanContext;return a.traceId===n.trace_id?e:(Date.now()/1e3-e.startTimestamp<=3600&&(t.addLink({context:a,attributes:{"sentry.link.type":"previous_trace"}}),t.setAttribute("sentry.previous_trace",`${a.traceId}-${a.spanId}-${+!!iq(a)}`)),i)}(i,e,t),n&&function(e){try{x.sessionStorage.setItem(iW,JSON.stringify(e))}catch(e){}}(i)});let a=!0;r&&e.on("beforeSampling",e=>{if(!i)return;let t=ek(),r=t.getPropagationContext();if(a&&r.parentSpanId){a=!1;return}t.setPropagationContext({...r,dsc:{...r.dsc,sample_rate:String(i.sampleRate),sampled:String(iq(i.spanContext))},sampleRand:i.sampleRand}),e.parentSampled=iq(i.spanContext),e.parentSampleRate=i.sampleRate,e.spanAttributes={...e.spanAttributes,[eY]:i.sampleRate}})}(e,{linkPreviousTrace:S,consistentTraceSampling:w}),x.location){if(b){let t=ey();i2(e,{name:x.location.pathname,startTime:t?t/1e3:void 0,attributes:{[eG]:"url",[eJ]:"auto.pageload.browser"}})}E&&rB(({to:t,from:r})=>{if(void 0===r&&y?.indexOf(t)!==-1){y=void 0;return}r!==t&&(y=void 0,i3(e,{name:x.location.pathname,attributes:{[eG]:"url",[eJ]:"auto.navigation.browser"}}))})}d&&x.document&&x.document.addEventListener("visibilitychange",()=>{let e=tv();if(!e)return;let t=ty(e);if(x.document.hidden&&t){let{op:e,status:r}=td(t);r||t.setStatus({code:2,message:"cancelled"}),t.setAttribute("sentry.cancellation_reason","document.hidden"),t.end()}}),a&&(t=e,n=l,i=u,o=c,s=T,x.document&&addEventListener("click",()=>{let e=function(e){return e[i4]}(t);if(!(e&&["navigation","pageload"].includes(td(e).op)))g&&(g.setAttribute(eQ,"interactionInterrupted"),g.end(),g=void 0),s.name&&(g=nI({name:s.name,op:"ui.action.click",attributes:{[eG]:s.source||"url"}},{idleTimeout:n,finalTimeout:i,childSpanTimeout:o}))},{once:!1,capture:!0})),r&&function(){let e=({entries:e})=>{let t=tv(),r=t&&ty(t);e.forEach(e=>{if(!("duration"in e)||!r)return;let t=e.interactionId;if(null!=t&&!iH.has(t)){if(iB.length>10){let e=iB.shift();iH.delete(e)}iB.push(t),iH.set(t,r)}})};ib("event",e),ib("first-input",e)}(),function(e,t){let{traceFetch:r,traceXHR:n,trackFetchStreamPerformance:i,shouldCreateSpanForRequest:a,enableHTTPTimings:o,tracePropagationTargets:s,onRequestSpanStart:l}={...iY,...t},u="function"==typeof a?a:e=>!0,c=e=>(function(e,t){let r=Z();if(r){let n,i;try{n=new URL(e,r),i=new URL(r).origin}catch(e){return!1}let a=n.origin===i;return t?en(n.toString(),t)||a&&en(n.pathname,t):a}{let r=!!e.match(/^\/(?!\/)/);return t?en(e,t):r}})(e,s),d={};r&&(e.addEventProcessor(e=>("transaction"===e.type&&e.spans&&e.spans.forEach(e=>{if("http.client"===e.op){let t=iV.get(e.span_id);t&&(e.timestamp=t/1e3,iV.delete(e.span_id))}}),e)),i&&function(e){let t="fetch-body-resolved";rv(t,e),rb(t,()=>rR(rT))}(e=>{if(e.response){let t=iG.get(e.response);t&&e.endTimestamp&&iV.set(t,e.endTimestamp)}}),rw(e=>{let t=function(e,t,r,n,i="auto.http.browser"){if(!e.fetchData)return;let{method:a,url:o}=e.fetchData,s=e5()&&t(o);if(e.endTimestamp&&s){let t=e.fetchData.__span;if(!t)return;let r=n[t];r&&(function(e,t){if(t.response){e4(e,t.response.status);let r=t.response?.headers&&t.response.headers.get("content-length");if(r){let t=parseInt(r);t>0&&e.setAttribute("http.response_content_length",t)}}else t.error&&e.setStatus({code:2,message:"internal_error"});e.end()}(r,e),delete n[t]);return}let l=!!tv(),u=s&&l?nP(function(e,t,r){let n=function(e,t){let r=0>=e.indexOf("://")&&0!==e.indexOf("//"),n=(void 0)??(r?"thismessage:/":void 0);try{if("canParse"in URL&&!URL.canParse(e,n))return;let t=new URL(e,n);if(r)return{isRelative:r,pathname:t.pathname,search:t.search,hash:t.hash};return t}catch{}}(e);return{name:n?`${t} ${function(e){if(rk(e))return e.pathname;let t=new URL(e);return t.search="",t.hash="",["80","443"].includes(t.port)&&(t.port=""),t.password&&(t.password="%filtered%"),t.username&&(t.username="%filtered%"),t.toString()}(n)}`:t,attributes:function(e,t,r,n){let i={url:e,type:"fetch","http.method":r,[eJ]:n,[eK]:"http.client"};return t&&(rk(t)||(i["http.url"]=t.href,i["server.address"]=t.host),t.search&&(i["http.query"]=t.search),t.hash&&(i["http.fragment"]=t.hash)),i}(e,n,t,r)}}(o,a,i)):new nv;if(e.fetchData.__span=u.spanContext().spanId,n[u.spanContext().spanId]=u,r(e.fetchData.url)){let t=e.args[0],r=e.args[1]||{},n=function(e,t,r){var n;let i=iz({span:r}),a=i["sentry-trace"],o=i.baggage;if(!a)return;let s=t.headers||(J(e)?e.headers:void 0);if(!s)return{...i};if(n=s,"undefined"!=typeof Headers&&Y(n,Headers)){let e=new Headers(s);if(e.get("sentry-trace")||e.set("sentry-trace",a),o){let t=e.get("baggage");t?iX(t)||e.set("baggage",`${t},${o}`):e.set("baggage",o)}return e}if(Array.isArray(s)){let e=[...s];s.find(e=>"sentry-trace"===e[0])||e.push(["sentry-trace",a]);let t=s.find(e=>"baggage"===e[0]&&iX(e[1]));return o&&!t&&e.push(["baggage",o]),e}{let e="sentry-trace"in s?s["sentry-trace"]:void 0,t="baggage"in s?s.baggage:void 0,r=t?Array.isArray(t)?[...t]:[t]:[],n=t&&(Array.isArray(t)?t.find(e=>iX(e)):iX(t));return o&&!n&&r.push(o),{...s,"sentry-trace":e??a,baggage:r.length>0?r.join(","):void 0}}}(t,r,e5()&&l?u:void 0);n&&(e.args[1]=r,r.headers=n)}let c=eN();if(c){let t={input:e.args,response:e.response,startTimestamp:e.startTimestamp,endTimestamp:e.endTimestamp};c.emit("beforeOutgoingRequestSpan",u,t)}return u}(e,u,c,d);if(e.response&&e.fetchData.__span&&iG.set(e.response,e.fetchData.__span),t){let r=iQ(e.fetchData.url),n=r?rM(r).host:void 0;t.setAttributes({"http.url":r,"server.address":n}),o&&iK(t),l?.(t,{headers:e.headers})}})),n&&rU(e=>{let t=function(e,t,r,n){let i=e.xhr,a=i?.[rL];if(!i||i.__sentry_own_request__||!a)return;let{url:o,method:s}=a,l=e5()&&t(o);if(e.endTimestamp&&l){let e=i.__sentry_xhr_span_id__;if(!e)return;let t=n[e];t&&void 0!==a.status_code&&(e4(t,a.status_code),t.end(),delete n[e]);return}let u=iQ(o),c=u?rM(u):rM(o),d=rA(o),f=!!tv(),p=l&&f?nP({name:`${s} ${d}`,attributes:{url:o,type:"xhr","http.method":s,"http.url":u,"server.address":c?.host,[eJ]:"auto.http.browser",[eK]:"http.client",...c?.search&&{"http.query":c?.search},...c?.hash&&{"http.fragment":c?.hash}}}):new nv;i.__sentry_xhr_span_id__=p.spanContext().spanId,n[i.__sentry_xhr_span_id__]=p,r(o)&&function(e,t){let{"sentry-trace":r,baggage:n}=iz({span:t});r&&function(e,t,r){let n=e.__sentry_xhr_v3__?.request_headers;if(!n?.["sentry-trace"])try{if(e.setRequestHeader("sentry-trace",t),r){let t=n?.baggage;t&&t.split(",").some(e=>e.trim().startsWith("sentry-"))||e.setRequestHeader("baggage",r)}}catch(e){}}(e,r,n)}(i,e5()&&f?p:void 0);let h=eN();return h&&h.emit("beforeOutgoingRequestSpan",p,e),p}(e,u,c,d);if(t){let r;o&&iK(t);try{r=new Headers(e.xhr.__sentry_xhr_v3__?.request_headers)}catch{}l?.(t,{headers:r})}})}(e,{traceFetch:f,traceXHR:p,trackFetchStreamPerformance:h,tracePropagationTargets:e.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:m,enableHTTPTimings:_,onRequestSpanStart:R})}}};function i2(e,t,r){return e.emit("startPageLoadSpan",t,r),ek().setTransactionName(t.name),e[i4]}function i3(e,t){return e.emit("startNavigationSpan",t),ek().setTransactionName(t.name),e[i4]}function i5(e){let t=x.document,r=t?.querySelector(`meta[name=${e}]`);return r?.getAttribute("content")||void 0}let i4="_sentry_idleSpan",i8="incomplete-app-router-transaction",i6="router-patch",i7={current:void 0};function i9(e){try{return new URL(e,"http://example.com/").pathname}catch{return"/"}}let ae=new WeakSet;function at(e,t,r){ae.has(t)||(ae.add(t),["back","forward","push","replace"].forEach(n=>{t?.[n]&&(t[n]=new Proxy(t[n],{apply(t,i,a){if("router-patch"!==i6)return t.apply(i,a);let o=i8,s={[eK]:"navigation",[eJ]:"auto.navigation.nextjs.app_router_instrumentation",[eG]:"url"};return"push"===n?(o=i9(a[0]),s["navigation.type"]="router.push"):"replace"===n?(o=i9(a[0]),s["navigation.type"]="router.replace"):"back"===n?s["navigation.type"]="router.back":"forward"===n&&(s["navigation.type"]="router.forward"),r.current=i3(e,{name:o,attributes:s}),t.apply(i,a)}}))}))}var ar=r(37110);let an=ar.events?ar:ar.default,ai=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function aa(...e){let t="",r=!1;for(let n=e.length-1;n>=-1&&!r;n--){let i=n>=0?e[n]:"/";i&&(t=`${i}/${t}`,r="/"===i.charAt(0))}return t=(function(e,t){let r=0;for(let t=e.length-1;t>=0;t--){let n=e[t];"."===n?e.splice(t,1):".."===n?(e.splice(t,1),r++):r&&(e.splice(t,1),r--)}if(t)for(;r--;)e.unshift("..");return e})(t.split("/").filter(e=>!!e),!r).join("/"),(r?"/":"")+t||"."}function ao(e){let t=0;for(;t<e.length&&""===e[t];t++);let r=e.length-1;for(;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}let as=(e={})=>{let t=e.root,r=e.prefix||"app:///",n="window"in x&&!!x.window,i=e.iteratee||function({isBrowser:e,root:t,prefix:r}){return n=>{if(!n.filename)return n;let i=/^[a-zA-Z]:\\/.test(n.filename)||n.filename.includes("\\")&&!n.filename.includes("/"),a=/^\//.test(n.filename);if(e){if(t){let e=n.filename;0===e.indexOf(t)&&(n.filename=e.replace(t,r))}}else if(i||a){let e=i?n.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):n.filename,a=t?function(e,t){e=aa(e).slice(1),t=aa(t).slice(1);let r=ao(e.split("/")),n=ao(t.split("/")),i=Math.min(r.length,n.length),a=i;for(let e=0;e<i;e++)if(r[e]!==n[e]){a=e;break}let o=[];for(let e=a;e<r.length;e++)o.push("..");return(o=o.concat(n.slice(a))).join("/")}(t,e):function(e){let t=e.length>1024?`<truncated>${e.slice(-1024)}`:e,r=ai.exec(t);return r?r.slice(1):[]}(e)[2]||"";n.filename=`${r}${a}`}return n}}({isBrowser:n,root:t,prefix:r});return{name:"RewriteFrames",processEvent(e){let t=e;return e.exception&&Array.isArray(e.exception.values)&&(t=function(e){try{return{...e,exception:{...e.exception,values:e.exception.values.map(e=>{var t;return{...e,...e.stacktrace&&{stacktrace:{...t=e.stacktrace,frames:t?.frames&&t.frames.map(e=>i(e))}}}})}}}catch(t){return e}}(t)),t}}},al=({assetPrefix:e,basePath:t,rewriteFramesAssetPrefixPath:r,experimentalThirdPartyOriginStackFrames:n})=>({...as({iteratee:i=>{if(n){let r="undefined"!=typeof window&&window.location?window.location.origin:"";if(i.filename?.startsWith(r)&&!i.filename.endsWith(".js"))return i;if(e)i.filename?.startsWith(e)&&(i.filename=i.filename.replace(e,"app://"));else if(t)try{let{origin:e}=new URL(i.filename);e===r&&(i.filename=i.filename?.replace(e,"app://").replace(t,""))}catch(e){}}else try{let{origin:e}=new URL(i.filename);i.filename=i.filename?.replace(e,"app://").replace(r,"")}catch(e){}return n?(i.filename?.includes("/_next")&&(i.filename=decodeURI(i.filename)),i.filename?.match(/\/_next\/static\/chunks\/(main-|main-app-|polyfills-|webpack-|framework-|framework\.)[0-9a-f]+\.js$/)&&(i.in_app=!1)):(i.filename?.startsWith("app:///_next")&&(i.filename=decodeURI(i.filename)),i.filename?.match(/^app:\/\/\/_next\/static\/chunks\/(main-|main-app-|polyfills-|webpack-|framework-|framework\.)[0-9a-f]+\.js$/)&&(i.in_app=!1)),i}}),name:"NextjsClientStackFrameNormalization"});var au=r(57177);let ac=!1;function ad(e){return"/"===e[e.length-1]?e.slice(0,-1):e}var af=r(57177);function ap(){return"undefined"!=typeof window&&(!(!("undefined"!=typeof __SENTRY_BROWSER_BUNDLE__&&__SENTRY_BROWSER_BUNDLE__)&&"[object process]"===Object.prototype.toString.call(void 0!==af?af:0))||function(){let e=x.process;return e?.type==="renderer"}())}function ah(e){return new URLSearchParams(e).toString()}function am(e,t=D){try{if("string"==typeof e)return[e];if(e instanceof URLSearchParams)return[e.toString()];if(e instanceof FormData)return[ah(e)];if(!e)return[void 0]}catch(e){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}function ag(e=[]){if(2===e.length&&"object"==typeof e[1])return e[1].body}let a_="sentryReplaySession",ay="Unable to send Replay";var av=Object.defineProperty,ab=(e,t,r)=>t in e?av(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,aE=(e,t,r)=>ab(e,"symbol"!=typeof t?t+"":t,r),aS=(e=>(e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment",e))(aS||{});function aw(e){let t=e?.host;return t?.shadowRoot===e}function aR(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function aP(e){try{var t;let r=e.rules||e.cssRules;return r?((t=Array.from(r,aT).join("")).includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;")),t):null}catch(e){return null}}function aT(e){let t;if("styleSheet"in e)try{t=aP(e.styleSheet)||function(e){let{cssText:t}=e;if(t.split('"').length<3)return t;let r=["@import",`url(${JSON.stringify(e.href)})`];return""===e.layerName?r.push("layer"):e.layerName&&r.push(`layer(${e.layerName})`),e.supportsText&&r.push(`supports(${e.supportsText})`),e.media.length&&r.push(e.media.mediaText),r.join(" ")+";"}(e)}catch(e){}else if("selectorText"in e){let t=e.cssText,r=e.selectorText.includes(":"),n="string"==typeof e.style.all&&e.style.all;if(n&&(t=function(e){let t="";for(let r=0;r<e.style.length;r++){let n=e.style,i=n[r],a=n.getPropertyPriority(i);t+=`${i}:${n.getPropertyValue(i)}${a?" !important":""};`}return`${e.selectorText} { ${t} }`}(e)),r&&(t=t.replace(/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,"$1\\$2")),r||n)return t}return t||e.cssText}class aO{constructor(){aE(this,"idNodeMap",new Map),aE(this,"nodeMetaMap",new WeakMap)}getId(e){return e?this.getMeta(e)?.id??-1:-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){let t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach(e=>this.removeNodeFromMap(e))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){let r=t.id;this.idNodeMap.set(r,e),this.nodeMetaMap.set(e,t)}replace(e,t){let r=this.getNode(e);if(r){let e=this.nodeMetaMap.get(r);e&&this.nodeMetaMap.set(t,e)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function ax({maskInputOptions:e,tagName:t,type:r}){return"OPTION"===t&&(t="SELECT"),!!(e[t.toLowerCase()]||r&&e[r]||"password"===r||"INPUT"===t&&!r&&e.text)}function aC({isMasked:e,element:t,value:r,maskInputFn:n}){let i=r||"";return e?(n&&(i=n(i,t)),"*".repeat(i.length)):i}function aI(e){return e.toLowerCase()}function ak(e){return e.toUpperCase()}let aM="__rrweb_original__";function aA(e){let t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?aI(t):null}function aN(e,t,r){return"INPUT"===t&&("radio"===r||"checkbox"===r)?e.getAttribute("value")||"":e.value}function aj(e,t){let r;try{r=new URL(e,t??window.location.href)}catch(e){return null}let n=r.pathname.match(/\.([0-9a-z]+)(?:$)/i);return n?.[1]??null}let aD={};function aL(e){let t=aD[e];if(t)return t;let r=window.document,n=window[e];if(r&&"function"==typeof r.createElement)try{let t=r.createElement("iframe");t.hidden=!0,r.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(t)}catch(e){}return aD[e]=n.bind(window)}function aU(...e){return aL("setTimeout")(...e)}function aF(...e){return aL("clearTimeout")(...e)}function aB(e){try{return e.contentDocument}catch(e){}}let aH=1,a$=RegExp("[^a-z0-9-_:]");function aW(){return aH++}let aq=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,az=/^(?:[a-z+]+:)?\/\//i,aX=/^www\..*/i,aG=/^(data:)([^,]*),(.*)/i;function aV(e,t){return(e||"").replace(aq,(e,r,n,i,a,o)=>{let s=n||a||o,l=r||i||"";if(!s)return e;if(az.test(s)||aX.test(s)||aG.test(s))return`url(${l}${s}${l})`;if("/"===s[0]){let e;return`url(${l}${(t.indexOf("//")>-1?t.split("/").slice(0,3).join("/"):t.split("/")[0]).split("?")[0]+s}${l})`}let u=t.split("/"),c=s.split("/");for(let e of(u.pop(),c))if("."===e)continue;else".."===e?u.pop():u.push(e);return`url(${l}${u.join("/")}${l})`})}let aY=/^[^ \t\n\r\u000c]+/,aK=/^[, \t\n\r\u000c]+/,aJ=new WeakMap;function aQ(e,t){return t&&""!==t.trim()?aZ(e,t):t}function aZ(e,t){let r=aJ.get(e);if(r||(r=e.createElement("a"),aJ.set(e,r)),t){if(t.startsWith("blob:")||t.startsWith("data:"))return t}else t="";return r.setAttribute("href",t),r.href}function a0(e,t,r,n,i,a){if(!n)return n;if("src"===r||"href"===r&&("use"!==t||"#"!==n[0])||"xlink:href"===r&&"#"!==n[0])return aQ(e,n);if("background"===r&&("table"===t||"td"===t||"th"===t))return aQ(e,n);if("srcset"===r)return function(e,t){if(""===t.trim())return t;let r=0;function n(e){let n,i=e.exec(t.substring(r));return i?(n=i[0],r+=n.length,n):""}let i=[];for(;n(aK),!(r>=t.length);){let a=n(aY);if(","===a.slice(-1))a=aQ(e,a.substring(0,a.length-1)),i.push(a);else{let n="";a=aQ(e,a);let o=!1;for(;;){let e=t.charAt(r);if(""===e){i.push((a+n).trim());break}if(o)")"===e&&(o=!1);else if(","===e){r+=1,i.push((a+n).trim());break}else"("===e&&(o=!0);n+=e,r+=1}}}return i.join(", ")}(e,n);if("style"===r)return aV(n,aZ(e));else if("object"===t&&"data"===r)return aQ(e,n);return"function"==typeof a?a(r,n,i):n}function a1(e,t,r){return("video"===e||"audio"===e)&&"autoplay"===t}function a2(e,t,r,n){try{if(n&&e.matches(n))return!1;if("string"==typeof t){if(e.classList.contains(t))return!0}else for(let r=e.classList.length;r--;){let n=e.classList[r];if(t.test(n))return!0}if(r)return e.matches(r)}catch(e){}return!1}function a3(e,t,r=1/0,n=0){return!e||e.nodeType!==e.ELEMENT_NODE||n>r?-1:t(e)?n:a3(e.parentNode,t,r,n+1)}function a5(e,t){return r=>{if(null===r)return!1;try{if(e){if("string"==typeof e){if(r.matches(`.${e}`))return!0}else if(function(e,t){for(let r=e.classList.length;r--;){let n=e.classList[r];if(t.test(n))return!0}return!1}(r,e))return!0}if(t&&r.matches(t))return!0;return!1}catch{return!1}}}function a4(e,t,r,n,i,a){try{let o=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===o)return!1;if("INPUT"===o.tagName){let e=o.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(e))return!0}let s=-1,l=-1;if(a){if((l=a3(o,a5(n,i)))<0)return!0;s=a3(o,a5(t,r),l>=0?l:1/0)}else{if((s=a3(o,a5(t,r)))<0)return!1;l=a3(o,a5(n,i),s>=0?s:1/0)}return s>=0?!(l>=0)||s<=l:!(l>=0)&&!!a}catch(e){}return!!a}function a8(e){return null==e?"":e.toLowerCase()}function a6(e,t){let r,{doc:n,mirror:i,blockClass:a,blockSelector:o,unblockSelector:s,maskAllText:l,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:f,skipChild:p=!1,inlineStylesheet:h=!0,maskInputOptions:m={},maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:v,dataURLOptions:S={},inlineImages:w=!1,recordCanvas:R=!1,onSerialize:P,onIframeLoad:T,iframeLoadTimeout:O=5e3,onStylesheetLoad:x,stylesheetLoadTimeout:C=5e3,keepIframeSrcFn:I=()=>!1,newlyAddedElement:k=!1}=t,{preserveWhiteSpace:M=!0}=t,A=function(e,t){let{doc:r,mirror:n,blockClass:i,blockSelector:a,unblockSelector:o,maskAllText:s,maskAttributeFn:l,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:f,inlineStylesheet:p,maskInputOptions:h={},maskTextFn:m,maskInputFn:g,dataURLOptions:_={},inlineImages:y,recordCanvas:v,keepIframeSrcFn:S,newlyAddedElement:w=!1}=t,R=function(e,t){if(!t.hasNode(e))return;let r=t.getId(e);return 1===r?void 0:r}(r,n);switch(e.nodeType){case e.DOCUMENT_NODE:if("CSS1Compat"!==e.compatMode)return{type:aS.Document,childNodes:[],compatMode:e.compatMode};return{type:aS.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:aS.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:R};case e.ELEMENT_NODE:return function(e,t){let r,{doc:n,blockClass:i,blockSelector:a,unblockSelector:o,inlineStylesheet:s,maskInputOptions:l={},maskAttributeFn:u,maskInputFn:c,dataURLOptions:d={},inlineImages:f,recordCanvas:p,keepIframeSrcFn:h,newlyAddedElement:m=!1,rootId:g,maskTextClass:_,unmaskTextClass:y,maskTextSelector:v,unmaskTextSelector:S}=t,w=a2(e,i,a,o),R=function(e){if(e instanceof HTMLFormElement)return"form";let t=aI(e.tagName);return a$.test(t)?"div":t}(e),P={},T=e.attributes.length;for(let t=0;t<T;t++){let r=e.attributes[t];r.name&&!a1(R,r.name,r.value)&&(P[r.name]=a0(n,R,aI(r.name),r.value,e,u))}if("link"===R&&s){let t=Array.from(n.styleSheets).find(t=>t.href===e.href),r=null;t&&(r=aP(t)),r&&(P.rel=null,P.href=null,P.crossorigin=null,P._cssText=aV(r,t.href))}if("style"===R&&e.sheet&&!(e.innerText||e.textContent||"").trim().length){let t=aP(e.sheet);t&&(P._cssText=aV(t,aZ(n)))}if("input"===R||"textarea"===R||"select"===R||"option"===R){let t=aA(e),r=aN(e,ak(R),t),n=e.checked;if("submit"!==t&&"button"!==t&&r){let n=a4(e,_,v,y,S,ax({type:t,tagName:ak(R),maskInputOptions:l}));P.value=aC({isMasked:n,element:e,value:r,maskInputFn:c})}n&&(P.checked=n)}if("option"===R&&(e.selected&&!l.select?P.selected=!0:delete P.selected),"canvas"===R&&p){if("2d"===e.__context)!function(e){let t=e.getContext("2d");if(!t)return!0;for(let r=0;r<e.width;r+=50)for(let n=0;n<e.height;n+=50){let i=t.getImageData;if(new Uint32Array((aM in i?i[aM]:i).call(t,r,n,Math.min(50,e.width-r),Math.min(50,e.height-n)).data.buffer).some(e=>0!==e))return!1}return!0}(e)&&(P.rr_dataURL=e.toDataURL(d.type,d.quality));else if(!("__context"in e)){let t=e.toDataURL(d.type,d.quality),r=n.createElement("canvas");r.width=e.width,r.height=e.height,t!==r.toDataURL(d.type,d.quality)&&(P.rr_dataURL=t)}}if("img"===R&&f){b||(E=(b=n.createElement("canvas")).getContext("2d"));let t=e.currentSrc||e.getAttribute("src")||"<unknown-src>",r=e.crossOrigin,i=()=>{e.removeEventListener("load",i);try{b.width=e.naturalWidth,b.height=e.naturalHeight,E.drawImage(e,0,0),P.rr_dataURL=b.toDataURL(d.type,d.quality)}catch(r){if("anonymous"!==e.crossOrigin){e.crossOrigin="anonymous",e.complete&&0!==e.naturalWidth?i():e.addEventListener("load",i);return}console.warn(`Cannot inline img src=${t}! Error: ${r}`)}"anonymous"===e.crossOrigin&&(r?P.crossOrigin=r:e.removeAttribute("crossorigin"))};e.complete&&0!==e.naturalWidth?i():e.addEventListener("load",i)}if(("audio"===R||"video"===R)&&(P.rr_mediaState=e.paused?"paused":"played",P.rr_mediaCurrentTime=e.currentTime),!m&&(e.scrollLeft&&(P.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(P.rr_scrollTop=e.scrollTop)),w){let{width:t,height:r}=e.getBoundingClientRect();P={class:P.class,rr_width:`${t}px`,rr_height:`${r}px`}}"iframe"!==R||h(P.src)||(w||aB(e)||(P.rr_src=P.src),delete P.src);try{customElements.get(R)&&(r=!0)}catch(e){}return{type:aS.Element,tagName:R,attributes:P,childNodes:[],isSVG:!!("svg"===e.tagName||e.ownerSVGElement)||void 0,needBlock:w,rootId:g,isCustom:r}}(e,{doc:r,blockClass:i,blockSelector:a,unblockSelector:o,inlineStylesheet:p,maskAttributeFn:l,maskInputOptions:h,maskInputFn:g,dataURLOptions:_,inlineImages:y,recordCanvas:v,keepIframeSrcFn:S,newlyAddedElement:w,rootId:R,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:f});case e.TEXT_NODE:return function(e,t){let{maskAllText:r,maskTextClass:n,unmaskTextClass:i,maskTextSelector:a,unmaskTextSelector:o,maskTextFn:s,maskInputOptions:l,maskInputFn:u,rootId:c}=t,d=e.parentNode&&e.parentNode.tagName,f=e.textContent,p="STYLE"===d||void 0,h="SCRIPT"===d||void 0,m="TEXTAREA"===d||void 0;if(p&&f){try{e.nextSibling||e.previousSibling||e.parentNode.sheet?.cssRules&&(f=aP(e.parentNode.sheet))}catch(t){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${t}`,e)}f=aV(f,aZ(t.doc))}h&&(f="SCRIPT_PLACEHOLDER");let g=a4(e,n,a,i,o,r);return p||h||m||!f||!g||(f=s?s(f,e.parentElement):f.replace(/[\S]/g,"*")),m&&f&&(l.textarea||g)&&(f=u?u(f,e.parentNode):f.replace(/[\S]/g,"*")),"OPTION"===d&&f&&(f=aC({isMasked:a4(e,n,a,i,o,ax({type:null,tagName:d,maskInputOptions:l})),element:e,value:f,maskInputFn:u})),{type:aS.Text,textContent:f||"",isStyle:p,rootId:c}}(e,{doc:r,maskAllText:s,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:f,maskTextFn:m,maskInputOptions:h,maskInputFn:g,rootId:R});case e.CDATA_SECTION_NODE:return{type:aS.CDATA,textContent:"",rootId:R};case e.COMMENT_NODE:return{type:aS.Comment,textContent:e.textContent||"",rootId:R};default:return!1}}(e,{doc:n,mirror:i,blockClass:a,blockSelector:o,maskAllText:l,unblockSelector:s,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:f,inlineStylesheet:h,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,dataURLOptions:S,inlineImages:w,recordCanvas:R,keepIframeSrcFn:I,newlyAddedElement:k});if(!A)return console.warn(e,"not serialized"),null;r=i.hasNode(e)?i.getId(e):!function(e,t){if(t.comment&&e.type===aS.Comment)return!0;if(e.type===aS.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"==typeof e.attributes.href&&"js"===aj(e.attributes.href)))return!0;else if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(a8(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===a8(e.attributes.name)||"icon"===a8(e.attributes.rel)||"apple-touch-icon"===a8(e.attributes.rel)||"shortcut icon"===a8(e.attributes.rel))))return!0;else if("meta"===e.tagName){if(t.headMetaDescKeywords&&a8(e.attributes.name).match(/^description|keywords$/))return!0;else if(t.headMetaSocial&&(a8(e.attributes.property).match(/^(og|twitter|fb):/)||a8(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===a8(e.attributes.name)))return!0;else if(t.headMetaRobots&&("robots"===a8(e.attributes.name)||"googlebot"===a8(e.attributes.name)||"bingbot"===a8(e.attributes.name)))return!0;else if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;else if(t.headMetaAuthorship&&("author"===a8(e.attributes.name)||"generator"===a8(e.attributes.name)||"framework"===a8(e.attributes.name)||"publisher"===a8(e.attributes.name)||"progid"===a8(e.attributes.name)||a8(e.attributes.property).match(/^article:/)||a8(e.attributes.property).match(/^product:/)))return!0;else if(t.headMetaVerification&&("google-site-verification"===a8(e.attributes.name)||"yandex-verification"===a8(e.attributes.name)||"csrf-token"===a8(e.attributes.name)||"p:domain_verify"===a8(e.attributes.name)||"verify-v1"===a8(e.attributes.name)||"verification"===a8(e.attributes.name)||"shopify-checkout-api-token"===a8(e.attributes.name)))return!0}}return!1}(A,v)&&(M||A.type!==aS.Text||A.isStyle||A.textContent.replace(/^\s+|\s+$/gm,"").length)?aW():-2;let N=Object.assign(A,{id:r});if(i.add(e,N),-2===r)return null;P&&P(e);let j=!p;if(N.type===aS.Element){j=j&&!N.needBlock,delete N.needBlock;let t=e.shadowRoot;t&&aR(t)&&(N.isShadowHost=!0)}if((N.type===aS.Document||N.type===aS.Element)&&j){v.headWhitespace&&N.type===aS.Element&&"head"===N.tagName&&(M=!1);let t={doc:n,mirror:i,blockClass:a,blockSelector:o,maskAllText:l,unblockSelector:s,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:f,skipChild:p,inlineStylesheet:h,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:v,dataURLOptions:S,inlineImages:w,recordCanvas:R,preserveWhiteSpace:M,onSerialize:P,onIframeLoad:T,iframeLoadTimeout:O,onStylesheetLoad:x,stylesheetLoadTimeout:C,keepIframeSrcFn:I};for(let r of Array.from(e.childNodes)){let e=a6(r,t);e&&N.childNodes.push(e)}if(e.nodeType===e.ELEMENT_NODE&&e.shadowRoot)for(let r of Array.from(e.shadowRoot.childNodes)){let n=a6(r,t);n&&(aR(e.shadowRoot)&&(n.isShadow=!0),N.childNodes.push(n))}}return e.parentNode&&aw(e.parentNode)&&aR(e.parentNode)&&(N.isShadow=!0),N.type!==aS.Element||"iframe"!==N.tagName||a2(e,a,o,s)||function(e,t,r){let n,i=e.contentWindow;if(!i)return;let a=!1;try{n=i.document.readyState}catch(e){return}if("complete"!==n){let n=aU(()=>{a||(t(),a=!0)},r);e.addEventListener("load",()=>{aF(n),a=!0,t()});return}let o="about:blank";if(i.location.href!==o||e.src===o||""===e.src)return aU(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}(e,()=>{let t=aB(e);if(t&&T){let r=a6(t,{doc:t,mirror:i,blockClass:a,blockSelector:o,unblockSelector:s,maskAllText:l,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:f,skipChild:!1,inlineStylesheet:h,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:v,dataURLOptions:S,inlineImages:w,recordCanvas:R,preserveWhiteSpace:M,onSerialize:P,onIframeLoad:T,iframeLoadTimeout:O,onStylesheetLoad:x,stylesheetLoadTimeout:C,keepIframeSrcFn:I});r&&T(e,r)}},O),N.type===aS.Element&&"link"===N.tagName&&"string"==typeof N.attributes.rel&&("stylesheet"===N.attributes.rel||"preload"===N.attributes.rel&&"string"==typeof N.attributes.href&&"css"===aj(N.attributes.href))&&function(e,t,r){let n,i=!1;try{n=e.sheet}catch(e){return}if(n)return;let a=aU(()=>{i||(t(),i=!0)},r);e.addEventListener("load",()=>{aF(a),i=!0,t()})}(e,()=>{if(x){let t=a6(e,{doc:n,mirror:i,blockClass:a,blockSelector:o,unblockSelector:s,maskAllText:l,maskTextClass:u,unmaskTextClass:c,maskTextSelector:d,unmaskTextSelector:f,skipChild:!1,inlineStylesheet:h,maskInputOptions:m,maskAttributeFn:g,maskTextFn:_,maskInputFn:y,slimDOMOptions:v,dataURLOptions:S,inlineImages:w,recordCanvas:R,preserveWhiteSpace:M,onSerialize:P,onIframeLoad:T,iframeLoadTimeout:O,onStylesheetLoad:x,stylesheetLoadTimeout:C,keepIframeSrcFn:I});t&&x(e,t)}},C),N}function a7(e,t,r=document){let n={capture:!0,passive:!0};return r.addEventListener(e,t,n),()=>r.removeEventListener(e,t,n)}let a9="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",oe={map:{},getId:()=>(console.error(a9),-1),getNode:()=>(console.error(a9),null),removeNodeFromMap(){console.error(a9)},has:()=>(console.error(a9),!1),reset(){console.error(a9)}};function ot(e,t,r={}){let n=null,i=0;return function(...a){let o=Date.now();i||!1!==r.leading||(i=o);let s=t-(o-i),l=this;s<=0||s>t?(n&&(function(...e){oy("clearTimeout")(...e)}(n),n=null),i=o,e.apply(l,a)):n||!1===r.trailing||(n=ov(()=>{i=!1===r.leading?0:Date.now(),n=null,e.apply(l,a)},s))}}function or(e,t,r){try{if(!(t in e))return()=>{};let n=e[t],i=r(n);return"function"==typeof i&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:n}})),e[t]=i,()=>{e[t]=n}}catch{return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(oe=new Proxy(oe,{get:(e,t,r)=>("map"===t&&console.error(a9),Reflect.get(e,t,r))}));let on=Date.now;function oi(e){let t=e.document;return{left:t.scrollingElement?t.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:t?.documentElement.scrollLeft||t?.body?.parentElement?.scrollLeft||t?.body?.scrollLeft||0,top:t.scrollingElement?t.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:t?.documentElement.scrollTop||t?.body?.parentElement?.scrollTop||t?.body?.scrollTop||0}}function oa(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function oo(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function os(e){if(!e)return null;try{return e.nodeType===e.ELEMENT_NODE?e:e.parentElement}catch(e){return null}}function ol(e,t,r,n,i){if(!e)return!1;let a=os(e);if(!a)return!1;let o=a5(t,r);if(!i){let e=n&&a.matches(n);return o(a)&&!e}let s=a3(a,o),l=-1;return!(s<0)&&(n&&(l=a3(a,a5(null,n))),s>-1&&l<0||s<l)}function ou(e,t){return -2===t.getId(e)}function oc(e){return!!e.changedTouches}function od(e,t){return!!("IFRAME"===e.nodeName&&t.getMeta(e))}function of(e,t){return!!("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function op(e){return!!e?.shadowRoot}/[1-9][0-9]{12}/.test(Date.now().toString())||(on=()=>new Date().getTime());class oh{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(e){return this.styleIDMap.get(e)??-1}has(e){return this.styleIDMap.has(e)}add(e,t){let r;return this.has(e)?this.getId(e):(r=void 0===t?this.id++:t,this.styleIDMap.set(e,r),this.idStyleMap.set(r,e),r)}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function om(e){let t=null;return e.getRootNode?.()?.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&e.getRootNode().host&&(t=e.getRootNode().host),t}function og(e){let t=e.ownerDocument;return!!t&&(t.contains(e)||function(e){let t=e.ownerDocument;if(!t)return!1;let r=function(e){let t,r=e;for(;t=om(r);)r=t;return r}(e);return t.contains(r)}(e))}let o_={};function oy(e){let t=o_[e];if(t)return t;let r=window.document,n=window[e];if(r&&"function"==typeof r.createElement)try{let t=r.createElement("iframe");t.hidden=!0,r.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(t)}catch(e){}return o_[e]=n.bind(window)}function ov(...e){return oy("setTimeout")(...e)}var ob=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(ob||{}),oE=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(oE||{}),oS=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(oS||{}),ow=(e=>(e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e))(ow||{}),oR=(e=>(e[e.Play=0]="Play",e[e.Pause=1]="Pause",e[e.Seeked=2]="Seeked",e[e.VolumeChange=3]="VolumeChange",e[e.RateChange=4]="RateChange",e))(oR||{});function oP(e){try{return e.contentDocument}catch(e){}}class oT{constructor(){this.length=0,this.head=null,this.tail=null}get(e){if(e>=this.length)throw Error("Position outside of list range");let t=this.head;for(let r=0;r<e;r++)t=t?.next||null;return t}addNode(e){let t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&"__ln"in e.previousSibling){let r=e.previousSibling.__ln.next;t.next=r,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,r&&(r.previous=t)}else if(e.nextSibling&&"__ln"in e.nextSibling&&e.nextSibling.__ln.previous){let r=e.nextSibling.__ln.previous;t.previous=r,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,r&&(r.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}removeNode(e){let t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}let oO=(e,t)=>`${e}@${t}`;class ox{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=e=>{e.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;let e=[],t=new Set,r=new oT,n=e=>{let t=e,r=-2;for(;-2===r;)r=(t=t&&t.nextSibling)&&this.mirror.getId(t);return r},i=i=>{if(!i.parentNode||!og(i))return;let a=aw(i.parentNode)?this.mirror.getId(om(i)):this.mirror.getId(i.parentNode),o=n(i);if(-1===a||-1===o)return r.addNode(i);let s=a6(i,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:e=>{od(e,this.mirror)&&!ol(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&this.iframeManager.addIframe(e),of(e,this.mirror)&&this.stylesheetManager.trackLinkElement(e),op(i)&&this.shadowDomManager.addShadowRoot(i.shadowRoot,this.doc)},onIframeLoad:(e,t)=>{ol(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(this.iframeManager.attachIframe(e,t),e.contentWindow&&this.canvasManager.addWindow(e.contentWindow),this.shadowDomManager.observeAttachShadow(e))},onStylesheetLoad:(e,t)=>{this.stylesheetManager.attachLinkElement(e,t)}});s&&(e.push({parentId:a,nextId:o,node:s}),t.add(s.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(let e of this.movedSet)(!oI(this.removes,e,this.mirror)||this.movedSet.has(e.parentNode))&&i(e);for(let e of this.addedSet)ok(this.droppedSet,e)||oI(this.removes,e,this.mirror)?ok(this.movedSet,e)?i(e):this.droppedSet.add(e):i(e);let a=null;for(;r.length;){let e=null;if(a){let t=this.mirror.getId(a.value.parentNode),r=n(a.value);-1!==t&&-1!==r&&(e=a)}if(!e){let t=r.tail;for(;t;){let r=t;if(t=t.previous,r){let t=this.mirror.getId(r.value.parentNode);if(-1===n(r.value))continue;if(-1!==t){e=r;break}{let t=r.value;if(t.parentNode&&t.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){let n=t.parentNode.host;if(-1!==this.mirror.getId(n)){e=r;break}}}}}}if(!e){for(;r.head;)r.removeNode(r.head.value);break}a=e.previous,r.removeNode(e.value),i(e.value)}let o={texts:this.texts.map(e=>({id:this.mirror.getId(e.node),value:e.value})).filter(e=>!t.has(e.id)).filter(e=>this.mirror.has(e.id)),attributes:this.attributes.map(e=>{let{attributes:t}=e;if("string"==typeof t.style){let r=JSON.stringify(e.styleDiff),n=JSON.stringify(e._unchangedStyles);r.length<t.style.length&&(r+n).split("var(").length===t.style.split("var(").length&&(t.style=e.styleDiff)}return{id:this.mirror.getId(e.node),attributes:t}}).filter(e=>!t.has(e.id)).filter(e=>this.mirror.has(e.id)),removes:this.removes,adds:e};(o.texts.length||o.attributes.length||o.removes.length||o.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(o))},this.processMutation=e=>{if(!ou(e.target,this.mirror))switch(e.type){case"characterData":{let t=e.target.textContent;ol(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||t===e.oldValue||this.texts.push({value:a4(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&t?this.maskTextFn?this.maskTextFn(t,os(e.target)):t.replace(/[\S]/g,"*"):t,node:e.target});break}case"attributes":{let t=e.target,r=e.attributeName,n=e.target.getAttribute(r);if("value"===r){let r=aA(t),i=t.tagName;n=aN(t,i,r);let a=ax({maskInputOptions:this.maskInputOptions,tagName:i,type:r});n=aC({isMasked:a4(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,a),element:t,value:n,maskInputFn:this.maskInputFn})}if(ol(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||n===e.oldValue)return;let i=this.attributeMap.get(e.target);if("IFRAME"===t.tagName&&"src"===r&&!this.keepIframeSrcFn(n)){if(oP(t))return;r="rr_src"}if(i||(i={node:e.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(i),this.attributeMap.set(e.target,i)),"type"===r&&"INPUT"===t.tagName&&"password"===(e.oldValue||"").toLowerCase()&&t.setAttribute("data-rr-is-password","true"),!a1(t.tagName,r)&&(i.attributes[r]=a0(this.doc,aI(t.tagName),aI(r),n,t,this.maskAttributeFn),"style"===r)){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(e){this.unattachedDoc=this.doc}let r=this.unattachedDoc.createElement("span");for(let n of(e.oldValue&&r.setAttribute("style",e.oldValue),Array.from(t.style))){let e=t.style.getPropertyValue(n),a=t.style.getPropertyPriority(n);e!==r.style.getPropertyValue(n)||a!==r.style.getPropertyPriority(n)?""===a?i.styleDiff[n]=e:i.styleDiff[n]=[e,a]:i._unchangedStyles[n]=[e,a]}for(let e of Array.from(r.style))""===t.style.getPropertyValue(e)&&(i.styleDiff[e]=!1)}break}case"childList":if(ol(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;e.addedNodes.forEach(t=>this.genAdds(t,e.target)),e.removedNodes.forEach(t=>{let r=this.mirror.getId(t),n=aw(e.target)?this.mirror.getId(e.target.host):this.mirror.getId(e.target);ol(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||ou(t,this.mirror)||-1===this.mirror.getId(t)||(this.addedSet.has(t)?(oC(this.addedSet,t),this.droppedSet.add(t)):this.addedSet.has(e.target)&&-1===r||function e(t,r){if(aw(t))return!1;let n=r.getId(t);return!r.has(n)||(!t.parentNode||t.parentNode.nodeType!==t.DOCUMENT_NODE)&&(!t.parentNode||e(t.parentNode,r))}(e.target,this.mirror)||(this.movedSet.has(t)&&this.movedMap[oO(r,n)]?oC(this.movedSet,t):this.removes.push({parentId:n,id:r,isShadow:!!(aw(e.target)&&aR(e.target))||void 0})),this.mapRemoves.push(t))})}},this.genAdds=(e,t)=>{if(!this.processedNodeManager.inOtherBuffer(e,this)&&!(this.addedSet.has(e)||this.movedSet.has(e))){if(this.mirror.hasNode(e)){if(ou(e,this.mirror))return;this.movedSet.add(e);let r=null;t&&this.mirror.hasNode(t)&&(r=this.mirror.getId(t)),r&&-1!==r&&(this.movedMap[oO(this.mirror.getId(e),r)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);!ol(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&(e.childNodes.forEach(e=>this.genAdds(e)),op(e)&&e.shadowRoot.childNodes.forEach(t=>{this.processedNodeManager.add(t,this),this.genAdds(t,e)}))}}}init(e){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach(t=>{this[t]=e[t]})}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function oC(e,t){e.delete(t),t.childNodes.forEach(t=>oC(e,t))}function oI(e,t,r){return 0!==e.length&&function(e,t,r){let n=t.parentNode;for(;n;){let t=r.getId(n);if(e.some(e=>e.id===t))return!0;n=n.parentNode}return!1}(e,t,r)}function ok(e,t){return 0!==e.size&&function e(t,r){let{parentNode:n}=r;return!!n&&(!!t.has(n)||e(t,n))}(e,t)}let oM=e=>S?(...t)=>{try{return e(...t)}catch(e){if(S&&!0===S(e))return()=>{};throw e}}:e,oA=[];function oN(e){try{if("composedPath"in e){let t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch{}return e&&e.target}function oj(e,t){let r=new ox;oA.push(r),r.init(e);let n=window.MutationObserver||window.__rrMutationObserver,i=window?.Zone?.__symbol__?.("MutationObserver");i&&window[i]&&(n=window[i]);let a=new n(oM(t=>{e.onMutation&&!1===e.onMutation(t)||r.processMutations.bind(r)(t)}));return a.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),a}function oD({scrollCb:e,doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,sampling:o}){return a7("scroll",oM(ot(oM(o=>{let s=oN(o);if(!s||ol(s,n,i,a,!0))return;let l=r.getId(s);if(s===t&&t.defaultView){let r=oi(t.defaultView);e({id:l,x:r.left,y:r.top})}else e({id:l,x:s.scrollLeft,y:s.scrollTop})}),o.scroll||100)),t)}let oL=["INPUT","TEXTAREA","SELECT"],oU=new WeakMap;function oF(e){var t=[];if(oW("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||oW("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||oW("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||oW("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule){let r=Array.from(e.parentRule.cssRules).indexOf(e);t.unshift(r)}else if(e.parentStyleSheet){let r=Array.from(e.parentStyleSheet.cssRules).indexOf(e);t.unshift(r)}return t}function oB(e,t,r){let n,i;return e?(e.ownerNode?n=t.getId(e.ownerNode):i=r.getId(e),{styleId:i,id:n}):{}}function oH({mirror:e,stylesheetManager:t},r){let n=null;n="#document"===r.nodeName?e.getId(r):e.getId(r.host);let i="#document"===r.nodeName?r.defaultView?.Document:r.ownerDocument?.defaultView?.ShadowRoot,a=i?.prototype?Object.getOwnPropertyDescriptor(i?.prototype,"adoptedStyleSheets"):void 0;return null!==n&&-1!==n&&i&&a?(Object.defineProperty(r,"adoptedStyleSheets",{configurable:a.configurable,enumerable:a.enumerable,get(){return a.get?.call(this)},set(e){let r=a.set?.call(this,e);if(null!==n&&-1!==n)try{t.adoptStyleSheets(e,n)}catch(e){}return r}}),oM(()=>{Object.defineProperty(r,"adoptedStyleSheets",{configurable:a.configurable,enumerable:a.enumerable,get:a.get,set:a.set})})):()=>{}}function o$(e,t={}){let r,n=e.doc.defaultView;if(!n)return()=>{};e.recordDOM&&(r=oj(e,e.doc));let i=function({mousemoveCb:e,sampling:t,doc:r,mirror:n}){let i;if(!1===t.mousemove)return()=>{};let a="number"==typeof t.mousemove?t.mousemove:50,o="number"==typeof t.mousemoveCallback?t.mousemoveCallback:500,s=[],l=ot(oM(t=>{let r=Date.now()-i;e(s.map(e=>(e.timeOffset-=r,e)),t),s=[],i=null}),o),u=oM(ot(oM(e=>{let t=oN(e),{clientX:r,clientY:a}=oc(e)?e.changedTouches[0]:e;i||(i=on()),s.push({x:r,y:a,id:n.getId(t),timeOffset:on()-i}),l("undefined"!=typeof DragEvent&&e instanceof DragEvent?oE.Drag:e instanceof MouseEvent?oE.MouseMove:oE.TouchMove)}),a,{trailing:!1})),c=[a7("mousemove",u,r),a7("touchmove",u,r),a7("drag",u,r)];return oM(()=>{c.forEach(e=>e())})}(e),a=function({mouseInteractionCb:e,doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,sampling:o}){if(!1===o.mouseInteraction)return()=>{};let s=!0===o.mouseInteraction||void 0===o.mouseInteraction?{}:o.mouseInteraction,l=[],u=null,c=t=>o=>{let s=oN(o);if(ol(s,n,i,a,!0))return;let l=null,c=t;if("pointerType"in o){switch(o.pointerType){case"mouse":l=ow.Mouse;break;case"touch":l=ow.Touch;break;case"pen":l=ow.Pen}l===ow.Touch?oS[t]===oS.MouseDown?c="TouchStart":oS[t]===oS.MouseUp&&(c="TouchEnd"):ow.Pen}else oc(o)&&(l=ow.Touch);null!==l?(u=l,(c.startsWith("Touch")&&l===ow.Touch||c.startsWith("Mouse")&&l===ow.Mouse)&&(l=null)):oS[t]===oS.Click&&(l=u,u=null);let d=oc(o)?o.changedTouches[0]:o;if(!d)return;let f=r.getId(s),{clientX:p,clientY:h}=d;oM(e)({type:oS[c],id:f,x:p,y:h,...null!==l&&{pointerType:l}})};return Object.keys(oS).filter(e=>Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==s[e]).forEach(e=>{let r=aI(e),n=c(e);if(window.PointerEvent)switch(oS[e]){case oS.MouseDown:case oS.MouseUp:r=r.replace("mouse","pointer");break;case oS.TouchStart:case oS.TouchEnd:return}l.push(a7(r,n,t))}),oM(()=>{l.forEach(e=>e())})}(e),o=oD(e),s=function({viewportResizeCb:e},{win:t}){let r=-1,n=-1;return a7("resize",oM(ot(oM(()=>{let t=oa(),i=oo();(r!==t||n!==i)&&(e({width:Number(i),height:Number(t)}),r=t,n=i)}),200)),t)}(e,{win:n}),l=function({inputCb:e,doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,ignoreClass:o,ignoreSelector:s,maskInputOptions:l,maskInputFn:u,sampling:c,userTriggeredOnInput:d,maskTextClass:f,unmaskTextClass:p,maskTextSelector:h,unmaskTextSelector:m}){function g(e){let r=oN(e),c=e.isTrusted,g=r&&ak(r.tagName);if("OPTION"===g&&(r=r.parentElement),!r||!g||0>oL.indexOf(g)||ol(r,n,i,a,!0))return;let y=r;if(y.classList.contains(o)||s&&y.matches(s))return;let v=aA(r),b=aN(y,g,v),E=!1,S=ax({maskInputOptions:l,tagName:g,type:v}),w=a4(r,f,h,p,m,S);("radio"===v||"checkbox"===v)&&(E=r.checked),b=aC({isMasked:w,element:r,value:b,maskInputFn:u}),_(r,d?{text:b,isChecked:E,userTriggered:c}:{text:b,isChecked:E});let R=r.name;"radio"===v&&R&&E&&t.querySelectorAll(`input[type="radio"][name="${R}"]`).forEach(e=>{if(e!==r){let t=aC({isMasked:w,element:e,value:aN(e,g,v),maskInputFn:u});_(e,d?{text:t,isChecked:!E,userTriggered:!1}:{text:t,isChecked:!E})}})}function _(t,n){let i=oU.get(t);if(!i||i.text!==n.text||i.isChecked!==n.isChecked){oU.set(t,n);let i=r.getId(t);oM(e)({...n,id:i})}}let y=("last"===c.input?["change"]:["input","change"]).map(e=>a7(e,oM(g),t)),v=t.defaultView;if(!v)return()=>{y.forEach(e=>e())};let b=v.Object.getOwnPropertyDescriptor(v.HTMLInputElement.prototype,"value"),E=[[v.HTMLInputElement.prototype,"value"],[v.HTMLInputElement.prototype,"checked"],[v.HTMLSelectElement.prototype,"value"],[v.HTMLTextAreaElement.prototype,"value"],[v.HTMLSelectElement.prototype,"selectedIndex"],[v.HTMLOptionElement.prototype,"selected"]];return b&&b.set&&y.push(...E.map(e=>(function e(t,r,n,i,a=window){let o=a.Object.getOwnPropertyDescriptor(t,r);return a.Object.defineProperty(t,r,i?n:{set(e){ov(()=>{n.set.call(this,e)},0),o&&o.set&&o.set.call(this,e)}}),()=>e(t,r,o||{},!0)})(e[0],e[1],{set(){oM(g)({target:this,isTrusted:!1})}},!1,v))),oM(()=>{y.forEach(e=>e())})}(e),u=function({mediaInteractionCb:e,blockClass:t,blockSelector:r,unblockSelector:n,mirror:i,sampling:a,doc:o}){let s=oM(o=>ot(oM(a=>{let s=oN(a);if(!s||ol(s,t,r,n,!0))return;let{currentTime:l,volume:u,muted:c,playbackRate:d}=s;e({type:o,id:i.getId(s),currentTime:l,volume:u,muted:c,playbackRate:d})}),a.media||500)),l=[a7("play",s(oR.Play),o),a7("pause",s(oR.Pause),o),a7("seeked",s(oR.Seeked),o),a7("volumechange",s(oR.VolumeChange),o),a7("ratechange",s(oR.RateChange),o)];return oM(()=>{l.forEach(e=>e())})}(e),c=()=>{},d=()=>{},f=()=>{},p=()=>{};e.recordDOM&&(c=function({styleSheetRuleCb:e,mirror:t,stylesheetManager:r},{win:n}){let i,a;if(!n.CSSStyleSheet||!n.CSSStyleSheet.prototype)return()=>{};let o=n.CSSStyleSheet.prototype.insertRule;n.CSSStyleSheet.prototype.insertRule=new Proxy(o,{apply:oM((n,i,a)=>{let[o,s]=a,{id:l,styleId:u}=oB(i,t,r.styleMirror);return(l&&-1!==l||u&&-1!==u)&&e({id:l,styleId:u,adds:[{rule:o,index:s}]}),n.apply(i,a)})});let s=n.CSSStyleSheet.prototype.deleteRule;n.CSSStyleSheet.prototype.deleteRule=new Proxy(s,{apply:oM((n,i,a)=>{let[o]=a,{id:s,styleId:l}=oB(i,t,r.styleMirror);return(s&&-1!==s||l&&-1!==l)&&e({id:s,styleId:l,removes:[{index:o}]}),n.apply(i,a)})}),n.CSSStyleSheet.prototype.replace&&(i=n.CSSStyleSheet.prototype.replace,n.CSSStyleSheet.prototype.replace=new Proxy(i,{apply:oM((n,i,a)=>{let[o]=a,{id:s,styleId:l}=oB(i,t,r.styleMirror);return(s&&-1!==s||l&&-1!==l)&&e({id:s,styleId:l,replace:o}),n.apply(i,a)})})),n.CSSStyleSheet.prototype.replaceSync&&(a=n.CSSStyleSheet.prototype.replaceSync,n.CSSStyleSheet.prototype.replaceSync=new Proxy(a,{apply:oM((n,i,a)=>{let[o]=a,{id:s,styleId:l}=oB(i,t,r.styleMirror);return(s&&-1!==s||l&&-1!==l)&&e({id:s,styleId:l,replaceSync:o}),n.apply(i,a)})}));let l={};oq("CSSGroupingRule")?l.CSSGroupingRule=n.CSSGroupingRule:(oq("CSSMediaRule")&&(l.CSSMediaRule=n.CSSMediaRule),oq("CSSConditionRule")&&(l.CSSConditionRule=n.CSSConditionRule),oq("CSSSupportsRule")&&(l.CSSSupportsRule=n.CSSSupportsRule));let u={};return Object.entries(l).forEach(([n,i])=>{u[n]={insertRule:i.prototype.insertRule,deleteRule:i.prototype.deleteRule},i.prototype.insertRule=new Proxy(u[n].insertRule,{apply:oM((n,i,a)=>{let[o,s]=a,{id:l,styleId:u}=oB(i.parentStyleSheet,t,r.styleMirror);return(l&&-1!==l||u&&-1!==u)&&e({id:l,styleId:u,adds:[{rule:o,index:[...oF(i),s||0]}]}),n.apply(i,a)})}),i.prototype.deleteRule=new Proxy(u[n].deleteRule,{apply:oM((n,i,a)=>{let[o]=a,{id:s,styleId:l}=oB(i.parentStyleSheet,t,r.styleMirror);return(s&&-1!==s||l&&-1!==l)&&e({id:s,styleId:l,removes:[{index:[...oF(i),o]}]}),n.apply(i,a)})})}),oM(()=>{n.CSSStyleSheet.prototype.insertRule=o,n.CSSStyleSheet.prototype.deleteRule=s,i&&(n.CSSStyleSheet.prototype.replace=i),a&&(n.CSSStyleSheet.prototype.replaceSync=a),Object.entries(l).forEach(([e,t])=>{t.prototype.insertRule=u[e].insertRule,t.prototype.deleteRule=u[e].deleteRule})})}(e,{win:n}),d=oH(e,e.doc),f=function({styleDeclarationCb:e,mirror:t,ignoreCSSAttributes:r,stylesheetManager:n},{win:i}){let a=i.CSSStyleDeclaration.prototype.setProperty;i.CSSStyleDeclaration.prototype.setProperty=new Proxy(a,{apply:oM((i,o,s)=>{let[l,u,c]=s;if(r.has(l))return a.apply(o,[l,u,c]);let{id:d,styleId:f}=oB(o.parentRule?.parentStyleSheet,t,n.styleMirror);return(d&&-1!==d||f&&-1!==f)&&e({id:d,styleId:f,set:{property:l,value:u,priority:c},index:oF(o.parentRule)}),i.apply(o,s)})});let o=i.CSSStyleDeclaration.prototype.removeProperty;return i.CSSStyleDeclaration.prototype.removeProperty=new Proxy(o,{apply:oM((i,a,s)=>{let[l]=s;if(r.has(l))return o.apply(a,[l]);let{id:u,styleId:c}=oB(a.parentRule?.parentStyleSheet,t,n.styleMirror);return(u&&-1!==u||c&&-1!==c)&&e({id:u,styleId:c,remove:{property:l},index:oF(a.parentRule)}),i.apply(a,s)})}),oM(()=>{i.CSSStyleDeclaration.prototype.setProperty=a,i.CSSStyleDeclaration.prototype.removeProperty=o})}(e,{win:n}),e.collectFonts&&(p=function({fontCb:e,doc:t}){let r=t.defaultView;if(!r)return()=>{};let n=[],i=new WeakMap,a=r.FontFace;r.FontFace=function(e,t,r){let n=new a(e,t,r);return i.set(n,{family:e,buffer:"string"!=typeof t,descriptors:r,fontSource:"string"==typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),n};let o=or(t.fonts,"add",function(t){return function(r){return ov(oM(()=>{let t=i.get(r);t&&(e(t),i.delete(r))}),0),t.apply(this,[r])}});return n.push(()=>{r.FontFace=a}),n.push(o),oM(()=>{n.forEach(e=>e())})}(e)));let h=function(e){let{doc:t,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,selectionCb:o}=e,s=!0,l=oM(()=>{let e=t.getSelection();if(!e||s&&e?.isCollapsed)return;s=e.isCollapsed||!1;let l=[],u=e.rangeCount||0;for(let t=0;t<u;t++){let{startContainer:o,startOffset:s,endContainer:u,endOffset:c}=e.getRangeAt(t);ol(o,n,i,a,!0)||ol(u,n,i,a,!0)||l.push({start:r.getId(o),startOffset:s,end:r.getId(u),endOffset:c})}o({ranges:l})});return l(),a7("selectionchange",l)}(e),m=function({doc:e,customElementCb:t}){let r=e.defaultView;return r&&r.customElements?or(r.customElements,"define",function(e){return function(r,n,i){try{t({define:{name:r}})}catch(e){}return e.apply(this,[r,n,i])}}):()=>{}}(e),g=[];for(let t of e.plugins)g.push(t.observer(t.callback,n,t.options));return oM(()=>{oA.forEach(e=>e.reset()),r?.disconnect(),i(),a(),o(),s(),l(),u(),c(),d(),f(),p(),h(),m(),g.forEach(e=>e())})}function oW(e){return void 0!==window[e]}function oq(e){return!!(void 0!==window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}class oz{constructor(e){this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(e,t,r,n){let i=r||this.getIdToRemoteIdMap(e),a=n||this.getRemoteIdToIdMap(e),o=i.get(t);return o||(o=this.generateIdFn(),i.set(t,o),a.set(o,t)),o}getIds(e,t){let r=this.getIdToRemoteIdMap(e),n=this.getRemoteIdToIdMap(e);return t.map(t=>this.getId(e,t,r,n))}getRemoteId(e,t,r){let n=r||this.getRemoteIdToIdMap(e);if("number"!=typeof t)return t;let i=n.get(t);return i||-1}getRemoteIds(e,t){let r=this.getRemoteIdToIdMap(e);return t.map(t=>this.getRemoteId(e,t,r))}reset(e){if(!e){this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap;return}this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){let t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}getRemoteIdToIdMap(e){let t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}class oX{constructor(){this.crossOriginIframeMirror=new oz(aW),this.crossOriginIframeRootIdMap=new WeakMap}addIframe(){}addLoadListener(){}attachIframe(){}}class oG{constructor(e){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new oz(aW),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new oz(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}addLoadListener(e){this.loadListener=e}attachIframe(e,t){this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),this.recordCrossOriginIframes&&e.contentWindow?.addEventListener("message",this.handleMessage.bind(this)),this.loadListener?.(e);let r=oP(e);r&&r.adoptedStyleSheets&&r.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(r.adoptedStyleSheets,this.mirror.getId(r))}handleMessage(e){if("rrweb"!==e.data.type||e.origin!==e.data.origin||!e.source)return;let t=this.crossOriginIframeMap.get(e.source);if(!t)return;let r=this.transformCrossOriginEvent(t,e.data.event);r&&this.wrappedEmit(r,e.data.isCheckout)}transformCrossOriginEvent(e,t){switch(t.type){case ob.FullSnapshot:{this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(t.data.node,e);let r=t.data.node.id;return this.crossOriginIframeRootIdMap.set(e,r),this.patchRootIdOnNode(t.data.node,r),{timestamp:t.timestamp,type:ob.IncrementalSnapshot,data:{source:oE.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:t.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case ob.Meta:case ob.Load:case ob.DomContentLoaded:break;case ob.Plugin:return t;case ob.Custom:return this.replaceIds(t.data.payload,e,["id","parentId","previousId","nextId"]),t;case ob.IncrementalSnapshot:switch(t.data.source){case oE.Mutation:return t.data.adds.forEach(t=>{this.replaceIds(t,e,["parentId","nextId","previousId"]),this.replaceIdOnNode(t.node,e);let r=this.crossOriginIframeRootIdMap.get(e);r&&this.patchRootIdOnNode(t.node,r)}),t.data.removes.forEach(t=>{this.replaceIds(t,e,["parentId","id"])}),t.data.attributes.forEach(t=>{this.replaceIds(t,e,["id"])}),t.data.texts.forEach(t=>{this.replaceIds(t,e,["id"])}),t;case oE.Drag:case oE.TouchMove:case oE.MouseMove:return t.data.positions.forEach(t=>{this.replaceIds(t,e,["id"])}),t;case oE.ViewportResize:return!1;case oE.MediaInteraction:case oE.MouseInteraction:case oE.Scroll:case oE.CanvasMutation:case oE.Input:return this.replaceIds(t.data,e,["id"]),t;case oE.StyleSheetRule:case oE.StyleDeclaration:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleId"]),t;case oE.Font:return t;case oE.Selection:return t.data.ranges.forEach(t=>{this.replaceIds(t,e,["start","end"])}),t;case oE.AdoptedStyleSheet:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleIds"]),t.data.styles?.forEach(t=>{this.replaceStyleIds(t,e,["styleId"])}),t}}return!1}replace(e,t,r,n){for(let i of n)(Array.isArray(t[i])||"number"==typeof t[i])&&(Array.isArray(t[i])?t[i]=e.getIds(r,t[i]):t[i]=e.getId(r,t[i]));return t}replaceIds(e,t,r){return this.replace(this.crossOriginIframeMirror,e,t,r)}replaceStyleIds(e,t,r){return this.replace(this.crossOriginIframeStyleMirror,e,t,r)}replaceIdOnNode(e,t){this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach(e=>{this.replaceIdOnNode(e,t)})}patchRootIdOnNode(e,t){e.type===aS.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach(e=>{this.patchRootIdOnNode(e,t)})}}class oV{init(){}addShadowRoot(){}observeAttachShadow(){}reset(){}}class oY{constructor(e){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(e,t){if(!aR(e)||this.shadowDoms.has(e))return;this.shadowDoms.add(e),this.bypassOptions.canvasManager.addShadowRoot(e);let r=oj({...this.bypassOptions,doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this},e);this.restoreHandlers.push(()=>r.disconnect()),this.restoreHandlers.push(oD({...this.bypassOptions,scrollCb:this.scrollCb,doc:e,mirror:this.mirror})),ov(()=>{e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,this.mirror.getId(e.host)),this.restoreHandlers.push(oH({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},e))},0)}observeAttachShadow(e){let t=oP(e),r=function(e){try{return e.contentWindow}catch(e){}}(e);t&&r&&this.patchAttachShadow(r.Element,t)}patchAttachShadow(e,t){let r=this;this.restoreHandlers.push(or(e.prototype,"attachShadow",function(e){return function(n){let i=e.call(this,n);return this.shadowRoot&&og(this)&&r.addShadowRoot(this.shadowRoot,t),i}}))}reset(){this.restoreHandlers.forEach(e=>{try{e()}catch(e){}}),this.restoreHandlers=[],this.shadowDoms=new WeakSet,this.bypassOptions.canvasManager.resetShadowRoots()}}for(var oK="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",oJ="undefined"==typeof Uint8Array?[]:new Uint8Array(256),oQ=0;oQ<oK.length;oQ++)oJ[oK.charCodeAt(oQ)]=oQ;class oZ{reset(){}freeze(){}unfreeze(){}lock(){}unlock(){}snapshot(){}addWindow(){}addShadowRoot(){}resetShadowRoots(){}}class o0{constructor(e){this.trackedLinkElements=new WeakSet,this.styleMirror=new oh,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,t){if(0===e.length)return;let r={id:t,styleIds:[]},n=[];for(let t of e){let e;this.styleMirror.has(t)?e=this.styleMirror.getId(t):(e=this.styleMirror.add(t),n.push({styleId:e,rules:Array.from(t.rules||CSSRule,(e,t)=>({rule:aT(e),index:t}))})),r.styleIds.push(e)}n.length>0&&(r.styles=n),this.adoptedStyleSheetCb(r)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}class o1{constructor(){this.nodeMap=new WeakMap,this.active=!1}inOtherBuffer(e,t){let r=this.nodeMap.get(e);return r&&Array.from(r).some(e=>e!==t)}add(e,t){this.active||(this.active=!0,function(...e){oy("requestAnimationFrame")(...e)}(()=>{this.nodeMap=new WeakMap,this.active=!1})),this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}destroy(){}}try{if(2!==Array.from([1],e=>2*e)[0]){let e=document.createElement("iframe");document.body.appendChild(e),Array.from=e.contentWindow?.Array.from||Array.from,document.body.removeChild(e)}}catch(e){console.debug("Unable to override Array.from",e)}let o2=new aO;function o3(e={}){let t,{emit:r,checkoutEveryNms:n,checkoutEveryNth:i,blockClass:a="rr-block",blockSelector:o=null,unblockSelector:s=null,ignoreClass:l="rr-ignore",ignoreSelector:u=null,maskAllText:c=!1,maskTextClass:d="rr-mask",unmaskTextClass:f=null,maskTextSelector:p=null,unmaskTextSelector:h=null,inlineStylesheet:m=!0,maskAllInputs:g,maskInputOptions:_,slimDOMOptions:y,maskAttributeFn:v,maskInputFn:b,maskTextFn:E,maxCanvasSize:P=null,packFn:T,sampling:O={},dataURLOptions:x={},mousemoveWait:C,recordDOM:I=!0,recordCanvas:k=!1,recordCrossOriginIframes:M=!1,recordAfter:A="DOMContentLoaded"===e.recordAfter?e.recordAfter:"load",userTriggeredOnInput:N=!1,collectFonts:j=!1,inlineImages:D=!1,plugins:L,keepIframeSrcFn:U=()=>!1,ignoreCSSAttributes:F=new Set([]),errorHandler:B,onMutation:H,getCanvasManager:$}=e;S=B;let W=!M||window.parent===window,q=!1;if(!W)try{window.parent.document&&(q=!1)}catch(e){q=!0}if(W&&!r)throw Error("emit function is required");if(!W&&!q)return()=>{};void 0!==C&&void 0===O.mousemove&&(O.mousemove=C),o2.reset();let z=!0===g?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==_?_:{},X=!0===y||"all"===y?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===y,headMetaDescKeywords:"all"===y}:y||{};!function(e=window){"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...e)=>{let t=e[0];if(!(0 in e))throw TypeError("1 argument is required");do if(this===t)return!0;while(t=t&&t.parentNode);return!1})}();let G=0,V=e=>{for(let t of L||[])t.eventProcessor&&(e=t.eventProcessor(e));return T&&!q&&(e=T(e)),e};w=(e,a)=>{if(e.timestamp=on(),oA[0]?.isFrozen()&&e.type!==ob.FullSnapshot&&(e.type!==ob.IncrementalSnapshot||e.data.source!==oE.Mutation)&&oA.forEach(e=>e.unfreeze()),W)r?.(V(e),a);else if(q){let t={type:"rrweb",event:V(e),origin:window.location.origin,isCheckout:a};window.parent.postMessage(t,"*")}if(e.type===ob.FullSnapshot)t=e,G=0;else if(e.type===ob.IncrementalSnapshot){if(e.data.source===oE.Mutation&&e.data.isAttachIframe)return;G++;let r=i&&G>=i,a=n&&t&&e.timestamp-t.timestamp>n;(r||a)&&en(!0)}};let Y=e=>{w({type:ob.IncrementalSnapshot,data:{source:oE.Mutation,...e}})},K=e=>w({type:ob.IncrementalSnapshot,data:{source:oE.Scroll,...e}}),J=e=>w({type:ob.IncrementalSnapshot,data:{source:oE.CanvasMutation,...e}}),Q=new o0({mutationCb:Y,adoptedStyleSheetCb:e=>w({type:ob.IncrementalSnapshot,data:{source:oE.AdoptedStyleSheet,...e}})}),Z="boolean"==typeof __RRWEB_EXCLUDE_IFRAME__&&__RRWEB_EXCLUDE_IFRAME__?new oX:new oG({mirror:o2,mutationCb:Y,stylesheetManager:Q,recordCrossOriginIframes:M,wrappedEmit:w});for(let e of L||[])e.getMirror&&e.getMirror({nodeMirror:o2,crossOriginIframeMirror:Z.crossOriginIframeMirror,crossOriginIframeStyleMirror:Z.crossOriginIframeStyleMirror});let ee=new o1,et=function(e,t){try{return e?e(t):new oZ}catch{return console.warn("Unable to initialize CanvasManager"),new oZ}}($,{mirror:o2,win:window,mutationCb:e=>w({type:ob.IncrementalSnapshot,data:{source:oE.CanvasMutation,...e}}),recordCanvas:k,blockClass:a,blockSelector:o,unblockSelector:s,maxCanvasSize:P,sampling:O.canvas,dataURLOptions:x,errorHandler:B}),er="boolean"==typeof __RRWEB_EXCLUDE_SHADOW_DOM__&&__RRWEB_EXCLUDE_SHADOW_DOM__?new oV:new oY({mutationCb:Y,scrollCb:K,bypassOptions:{onMutation:H,blockClass:a,blockSelector:o,unblockSelector:s,maskAllText:c,maskTextClass:d,unmaskTextClass:f,maskTextSelector:p,unmaskTextSelector:h,inlineStylesheet:m,maskInputOptions:z,dataURLOptions:x,maskAttributeFn:v,maskTextFn:E,maskInputFn:b,recordCanvas:k,inlineImages:D,sampling:O,slimDOMOptions:X,iframeManager:Z,stylesheetManager:Q,canvasManager:et,keepIframeSrcFn:U,processedNodeManager:ee},mirror:o2}),en=(e=!1)=>{if(!I)return;w({type:ob.Meta,data:{href:window.location.href,width:oo(),height:oa()}},e),Q.reset(),er.init(),oA.forEach(e=>e.lock());let t=function(e,t){let{mirror:r=new aO,blockClass:n="rr-block",blockSelector:i=null,unblockSelector:a=null,maskAllText:o=!1,maskTextClass:s="rr-mask",unmaskTextClass:l=null,maskTextSelector:u=null,unmaskTextSelector:c=null,inlineStylesheet:d=!0,inlineImages:f=!1,recordCanvas:p=!1,maskAllInputs:h=!1,maskAttributeFn:m,maskTextFn:g,maskInputFn:_,slimDOM:y=!1,dataURLOptions:v,preserveWhiteSpace:b,onSerialize:E,onIframeLoad:S,iframeLoadTimeout:w,onStylesheetLoad:R,stylesheetLoadTimeout:P,keepIframeSrcFn:T=()=>!1}=t||{};return a6(e,{doc:e,mirror:r,blockClass:n,blockSelector:i,unblockSelector:a,maskAllText:o,maskTextClass:s,unmaskTextClass:l,maskTextSelector:u,unmaskTextSelector:c,skipChild:!1,inlineStylesheet:d,maskInputOptions:!0===h?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===h?{}:h,maskAttributeFn:m,maskTextFn:g,maskInputFn:_,slimDOMOptions:!0===y||"all"===y?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===y,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===y?{}:y,dataURLOptions:v,inlineImages:f,recordCanvas:p,preserveWhiteSpace:b,onSerialize:E,onIframeLoad:S,iframeLoadTimeout:w,onStylesheetLoad:R,stylesheetLoadTimeout:P,keepIframeSrcFn:T,newlyAddedElement:!1})}(document,{mirror:o2,blockClass:a,blockSelector:o,unblockSelector:s,maskAllText:c,maskTextClass:d,unmaskTextClass:f,maskTextSelector:p,unmaskTextSelector:h,inlineStylesheet:m,maskAllInputs:z,maskAttributeFn:v,maskInputFn:b,maskTextFn:E,slimDOM:X,dataURLOptions:x,recordCanvas:k,inlineImages:D,onSerialize:e=>{od(e,o2)&&Z.addIframe(e),of(e,o2)&&Q.trackLinkElement(e),op(e)&&er.addShadowRoot(e.shadowRoot,document)},onIframeLoad:(e,t)=>{Z.attachIframe(e,t),e.contentWindow&&et.addWindow(e.contentWindow),er.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{Q.attachLinkElement(e,t)},keepIframeSrcFn:U});if(!t)return console.warn("Failed to snapshot the document");w({type:ob.FullSnapshot,data:{node:t,initialOffset:oi(window)}}),oA.forEach(e=>e.unlock()),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&Q.adoptStyleSheets(document.adoptedStyleSheets,o2.getId(document))};R=en;try{let e=[],t=e=>oM(o$)({onMutation:H,mutationCb:Y,mousemoveCb:(e,t)=>w({type:ob.IncrementalSnapshot,data:{source:t,positions:e}}),mouseInteractionCb:e=>w({type:ob.IncrementalSnapshot,data:{source:oE.MouseInteraction,...e}}),scrollCb:K,viewportResizeCb:e=>w({type:ob.IncrementalSnapshot,data:{source:oE.ViewportResize,...e}}),inputCb:e=>w({type:ob.IncrementalSnapshot,data:{source:oE.Input,...e}}),mediaInteractionCb:e=>w({type:ob.IncrementalSnapshot,data:{source:oE.MediaInteraction,...e}}),styleSheetRuleCb:e=>w({type:ob.IncrementalSnapshot,data:{source:oE.StyleSheetRule,...e}}),styleDeclarationCb:e=>w({type:ob.IncrementalSnapshot,data:{source:oE.StyleDeclaration,...e}}),canvasMutationCb:J,fontCb:e=>w({type:ob.IncrementalSnapshot,data:{source:oE.Font,...e}}),selectionCb:e=>{w({type:ob.IncrementalSnapshot,data:{source:oE.Selection,...e}})},customElementCb:e=>{w({type:ob.IncrementalSnapshot,data:{source:oE.CustomElement,...e}})},blockClass:a,ignoreClass:l,ignoreSelector:u,maskAllText:c,maskTextClass:d,unmaskTextClass:f,maskTextSelector:p,unmaskTextSelector:h,maskInputOptions:z,inlineStylesheet:m,sampling:O,recordDOM:I,recordCanvas:k,inlineImages:D,userTriggeredOnInput:N,collectFonts:j,doc:e,maskAttributeFn:v,maskInputFn:b,maskTextFn:E,keepIframeSrcFn:U,blockSelector:o,unblockSelector:s,slimDOMOptions:X,dataURLOptions:x,mirror:o2,iframeManager:Z,stylesheetManager:Q,shadowDomManager:er,processedNodeManager:ee,canvasManager:et,ignoreCSSAttributes:F,plugins:L?.filter(e=>e.observer)?.map(e=>({observer:e.observer,options:e.options,callback:t=>w({type:ob.Plugin,data:{plugin:e.name,payload:t}})}))||[]},{});Z.addLoadListener(r=>{try{e.push(t(r.contentDocument))}catch(e){console.warn(e)}});let r=()=>{en(),e.push(t(document))};return"interactive"===document.readyState||"complete"===document.readyState?r():(e.push(a7("DOMContentLoaded",()=>{w({type:ob.DomContentLoaded,data:{}}),"DOMContentLoaded"===A&&r()})),e.push(a7("load",()=>{w({type:ob.Load,data:{}}),"load"===A&&r()},window))),()=>{e.forEach(e=>e()),ee.destroy(),R=void 0,S=void 0}}catch(e){console.warn(e)}}function o5(e){return e>0x2540be3ff?e:1e3*e}function o4(e){return e>0x2540be3ff?e/1e3:e}function o8(e,t){"sentry.transaction"!==t.category&&(["ui.click","ui.input"].includes(t.category)?e.triggerUserActivity():e.checkAndHandleExpiredSession(),e.addUpdate(()=>(e.throttledAddEvent({type:ob.Custom,timestamp:1e3*(t.timestamp||0),data:{tag:"breadcrumb",payload:eX(t,10,1e3)}}),"console"===t.category)))}function o6(e){return e.closest("button,a")||e}function o7(e){let t=o9(e);return t&&t instanceof Element?o6(t):t}function o9(e){var t;return"object"==typeof(t=e)&&t&&"target"in t?e.target:e}o3.mirror=o2,o3.takeFullSnapshot=function(e){if(!R)throw Error("please take full snapshot after start recording");R(e)},!function(e){e[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped"}(ng||(ng={}));let se=new Set([oE.Mutation,oE.StyleSheetRule,oE.StyleDeclaration,oE.AdoptedStyleSheet,oE.CanvasMutation,oE.Selection,oE.MediaInteraction]);class st{constructor(e,t,r=o8){this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=t.timeout/1e3,this._threshold=t.threshold/1e3,this._scrollTimeout=t.scrollTimeout/1e3,this._replay=e,this._ignoreSelector=t.ignoreSelector,this._addBreadcrumbEvent=r}addListeners(){var e;let t=(e=()=>{this._lastMutation=sn()},P||(P=[],ei(x,"open",function(e){return function(...t){if(P)try{P.forEach(e=>e())}catch(e){}return e.apply(x,t)}})),P.push(e),()=>{let t=P?P.indexOf(e):-1;t>-1&&P.splice(t,1)});this._teardown=()=>{t(),this._clicks=[],this._lastMutation=0,this._lastScroll=0}}removeListeners(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}handleClick(e,t){var r,n,i;if(r=t,n=this._ignoreSelector,!sr.includes(r.tagName)||"INPUT"===r.tagName&&!["submit","button"].includes(r.getAttribute("type")||"")||"A"===r.tagName&&(r.hasAttribute("download")||r.hasAttribute("target")&&"_self"!==r.getAttribute("target"))||n&&r.matches(n)||!((i=e).data&&"number"==typeof i.data.nodeId&&i.timestamp))return;let a={timestamp:o4(e.timestamp),clickBreadcrumb:e,clickCount:0,node:t};this._clicks.some(e=>e.node===a.node&&1>Math.abs(e.timestamp-a.timestamp))||(this._clicks.push(a),1===this._clicks.length&&this._scheduleCheckClicks())}registerMutation(e=Date.now()){this._lastMutation=o4(e)}registerScroll(e=Date.now()){this._lastScroll=o4(e)}registerClick(e){let t=o6(e);this._handleMultiClick(t)}_handleMultiClick(e){this._getClicks(e).forEach(e=>{e.clickCount++})}_getClicks(e){return this._clicks.filter(t=>t.node===e)}_checkClicks(){let e=[],t=sn();for(let r of(this._clicks.forEach(r=>{!r.mutationAfter&&this._lastMutation&&(r.mutationAfter=r.timestamp<=this._lastMutation?this._lastMutation-r.timestamp:void 0),!r.scrollAfter&&this._lastScroll&&(r.scrollAfter=r.timestamp<=this._lastScroll?this._lastScroll-r.timestamp:void 0),r.timestamp+this._timeout<=t&&e.push(r)}),e)){let e=this._clicks.indexOf(r);e>-1&&(this._generateBreadcrumbs(r),this._clicks.splice(e,1))}this._clicks.length&&this._scheduleCheckClicks()}_generateBreadcrumbs(e){let t=this._replay,r=e.scrollAfter&&e.scrollAfter<=this._scrollTimeout,n=e.mutationAfter&&e.mutationAfter<=this._threshold,{clickCount:i,clickBreadcrumb:a}=e;if(!r&&!n){let r=1e3*Math.min(e.mutationAfter||this._timeout,this._timeout),n=r<1e3*this._timeout?"mutation":"timeout",o={type:"default",message:a.message,timestamp:a.timestamp,category:"ui.slowClickDetected",data:{...a.data,url:x.location.href,route:t.getCurrentRoute(),timeAfterClickMs:r,endReason:n,clickCount:i||1}};this._addBreadcrumbEvent(t,o);return}if(i>1){let e={type:"default",message:a.message,timestamp:a.timestamp,category:"ui.multiClick",data:{...a.data,url:x.location.href,route:t.getCurrentRoute(),clickCount:i,metric:!0}};this._addBreadcrumbEvent(t,e)}}_scheduleCheckClicks(){this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=nf(()=>this._checkClicks(),1e3)}}let sr=["A","BUTTON","INPUT"];function sn(){return Date.now()/1e3}function si(e){return{timestamp:Date.now()/1e3,type:"default",...e}}var sa=(e=>(e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment",e))(sa||{});let so=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]),ss=e=>t=>{var r,n;if(!e.isEnabled())return;let i=function(e){let{target:t,message:r}=function(e){let t,r="click"===e.name,n=null;try{n=r?o7(e.event):o9(e.event),t=Q(n,{maxStringLength:200})||"<unknown>"}catch(e){t="<unknown>"}return{target:n,message:t}}(e);return si({category:`ui.${e.name}`,...sl(t,r)})}(t);if(!i)return;let a="click"===t.name,o=a?t.event:void 0;a&&e.clickDetector&&o&&o.target&&!o.altKey&&!o.metaKey&&!o.ctrlKey&&!o.shiftKey&&(r=e.clickDetector,n=o7(t.event),r.handleClick(i,n)),o8(e,i)};function sl(e,t){let r=o3.mirror.getId(e),n=r&&o3.mirror.getNode(r),i=n&&o3.mirror.getMeta(n),a=i&&i.type===sa.Element?i:null;return{message:t,data:a?{nodeId:r,node:{id:r,tagName:a.tagName,textContent:Array.from(a.childNodes).map(e=>e.type===sa.Text&&e.textContent).filter(Boolean).map(e=>e.trim()).join(""),attributes:function(e){let t={};for(let r in!e["data-sentry-component"]&&e["data-sentry-element"]&&(e["data-sentry-component"]=e["data-sentry-element"]),e)if(so.has(r)){let n=r;("data-testid"===r||"data-test-id"===r)&&(n="testId"),t[n]=e[r]}return t}(a.attributes)}}:{}}}let su={resource:function(e){let{entryType:t,initiatorType:r,name:n,responseEnd:i,startTime:a,decodedBodySize:o,encodedBodySize:s,responseStatus:l,transferSize:u}=e;return["fetch","xmlhttprequest"].includes(r)?null:{type:`${t}.${r}`,start:sf(a),end:sf(i),name:n,data:{size:u,statusCode:l,decodedBodySize:o,encodedBodySize:s}}},paint:function(e){let{duration:t,entryType:r,name:n,startTime:i}=e,a=sf(i);return{type:r,name:n,start:a,end:a+t,data:void 0}},navigation:function(e){let{entryType:t,name:r,decodedBodySize:n,duration:i,domComplete:a,encodedBodySize:o,domContentLoadedEventStart:s,domContentLoadedEventEnd:l,domInteractive:u,loadEventStart:c,loadEventEnd:d,redirectCount:f,startTime:p,transferSize:h,type:m}=e;return 0===i?null:{type:`${t}.${m}`,start:sf(p),end:sf(a),name:r,data:{size:h,decodedBodySize:n,encodedBodySize:o,duration:i,domInteractive:u,domContentLoadedEventStart:s,domContentLoadedEventEnd:l,loadEventStart:c,loadEventEnd:d,domComplete:a,redirectCount:f}}}};function sc(e,t){return({metric:r})=>void t.replayPerformanceEntries.push(e(r))}function sd(e){let t=su[e.entryType];return t?t(e):null}function sf(e){return((ey()||x.performance.timeOrigin)+e)/1e3}function sp(e){let t=e.entries[e.entries.length-1];return s_(e,"largest-contentful-paint",t?.element?[t.element]:void 0)}function sh(e){let t=[],r=[];for(let n of e.entries)if(void 0!==n.sources){let e=[];for(let t of n.sources)if(t.node){r.push(t.node);let n=o3.mirror.getId(t.node);n&&e.push(n)}t.push({value:n.value,nodeIds:e.length?e:void 0})}return s_(e,"cumulative-layout-shift",r,t)}function sm(e){let t=e.entries[e.entries.length-1];return s_(e,"first-input-delay",t?.target?[t.target]:void 0)}function sg(e){let t=e.entries[e.entries.length-1];return s_(e,"interaction-to-next-paint",t?.target?[t.target]:void 0)}function s_(e,t,r,n){let i=e.value,a=e.rating,o=sf(i);return{type:"web-vital",name:t,start:o,end:o,data:{value:i,size:i,rating:a,nodeIds:r?r.map(e=>o3.mirror.getId(e)):void 0,attributions:n}}}let sy=["info","warn","error","log"],sv=function(){let e=!1,t=!1,r={exception:()=>void 0,infoTick:()=>void 0,setConfig:e=>{e.captureExceptions,e.traceInternals}};return sy.forEach(e=>{r[e]=()=>void 0}),r}();class sb extends Error{constructor(){super("Event buffer exceeded maximum size of 20000000.")}}class sE{constructor(){this.events=[],this._totalSize=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(e){let t=JSON.stringify(e).length;if(this._totalSize+=t,this._totalSize>2e7)throw new sb;this.events.push(e)}finish(){return new Promise(e=>{let t=this.events;this.clear(),e(JSON.stringify(t))})}clear(){this.events=[],this._totalSize=0,this.hasCheckout=!1}getEarliestTimestamp(){let e=this.events.map(e=>e.timestamp).sort()[0];return e?o5(e):null}}class sS{constructor(e){this._worker=e,this._id=0}ensureReady(){return this._ensureReadyPromise||(this._ensureReadyPromise=new Promise((e,t)=>{this._worker.addEventListener("message",({data:r})=>{r.success?e():t()},{once:!0}),this._worker.addEventListener("error",e=>{t(e)},{once:!0})})),this._ensureReadyPromise}destroy(){this._worker.terminate()}postMessage(e,t){let r=this._getAndIncrementId();return new Promise((n,i)=>{let a=({data:t})=>{if(t.method===e&&t.id===r){if(this._worker.removeEventListener("message",a),!t.success)return void i(Error("Error in compression worker"));n(t.response)}};this._worker.addEventListener("message",a),this._worker.postMessage({id:r,method:e,arg:t})})}_getAndIncrementId(){return this._id++}}class sw{constructor(e){this._worker=new sS(e),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return!!this._earliestTimestamp}get type(){return"worker"}ensureReady(){return this._worker.ensureReady()}destroy(){this._worker.destroy()}addEvent(e){let t=o5(e.timestamp);(!this._earliestTimestamp||t<this._earliestTimestamp)&&(this._earliestTimestamp=t);let r=JSON.stringify(e);return(this._totalSize+=r.length,this._totalSize>2e7)?Promise.reject(new sb):this._sendEventToWorker(r)}finish(){return this._finishRequest()}clear(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear").then(null,e=>{})}getEarliestTimestamp(){return this._earliestTimestamp}_sendEventToWorker(e){return this._worker.postMessage("addEvent",e)}async _finishRequest(){let e=await this._worker.postMessage("finish");return this._earliestTimestamp=null,this._totalSize=0,e}}class sR{constructor(e){this._fallback=new sE,this._compression=new sw(e),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}get waitForCheckout(){return this._used.waitForCheckout}get type(){return this._used.type}get hasEvents(){return this._used.hasEvents}get hasCheckout(){return this._used.hasCheckout}set hasCheckout(e){this._used.hasCheckout=e}set waitForCheckout(e){this._used.waitForCheckout=e}destroy(){this._fallback.destroy(),this._compression.destroy()}clear(){return this._used.clear()}getEarliestTimestamp(){return this._used.getEarliestTimestamp()}addEvent(e){return this._used.addEvent(e)}async finish(){return await this.ensureWorkerIsLoaded(),this._used.finish()}ensureWorkerIsLoaded(){return this._ensureWorkerIsLoadedPromise}async _ensureWorkerIsLoaded(){try{await this._compression.ensureReady()}catch(e){return}await this._switchToCompressionWorker()}async _switchToCompressionWorker(){let{events:e,hasCheckout:t,waitForCheckout:r}=this._fallback,n=[];for(let t of e)n.push(this._compression.addEvent(t));this._compression.hasCheckout=t,this._compression.waitForCheckout=r,this._used=this._compression;try{await Promise.all(n),this._fallback.clear()}catch(e){}}}function sP(){try{return"sessionStorage"in x&&!!x.sessionStorage}catch{return!1}}function sT(e){return void 0!==e&&Math.random()<e}function sO(e){if(sP())try{x.sessionStorage.setItem(a_,JSON.stringify(e))}catch{}}function sx(e){let t=Date.now(),r=e.id||ed(),n=e.started||t,i=e.lastActivity||t,a=e.segmentId||0;return{id:r,started:n,lastActivity:i,segmentId:a,sampled:e.sampled,previousSessionId:e.previousSessionId}}function sC({sessionSampleRate:e,allowBuffering:t,stickySession:r=!1},{previousSessionId:n}={}){let i=sx({sampled:sT(e)?"session":!!t&&"buffer",previousSessionId:n});return r&&sO(i),i}function sI(e,t,r=+new Date){return null===e||void 0===t||t<0||0!==t&&e+t<=r}function sk(e,{maxReplayDuration:t,sessionIdleExpire:r,targetTime:n=Date.now()}){return sI(e.started,t,n)||sI(e.lastActivity,r,n)}function sM(e,{sessionIdleExpire:t,maxReplayDuration:r}){return!!sk(e,{sessionIdleExpire:t,maxReplayDuration:r})&&("buffer"!==e.sampled||0!==e.segmentId)}function sA({sessionIdleExpire:e,maxReplayDuration:t,previousSessionId:r},n){let i=n.stickySession&&function(){if(!sP())return null;try{let e=x.sessionStorage.getItem(a_);if(!e)return null;let t=JSON.parse(e);return sx(t)}catch{return null}}();return i?sM(i,{sessionIdleExpire:e,maxReplayDuration:t})?sC(n,{previousSessionId:i.id}):i:sC(n,{previousSessionId:r})}function sN(e,t,r){return!!sD(e,t)&&(sj(e,t,r),!0)}async function sj(e,t,r){let{eventBuffer:n}=e;if(!n||n.waitForCheckout&&!r)return null;let i="buffer"===e.recordingMode;try{r&&i&&n.clear(),r&&(n.hasCheckout=!0,n.waitForCheckout=!1);let a=e.getOptions(),o=function(e,t){try{if("function"==typeof t&&e.type===ob.Custom)return t(e)}catch(e){return null}return e}(t,a.beforeAddRecordingEvent);if(!o)return;return await n.addEvent(o)}catch(a){let t=a&&a instanceof sb;if(t&&i)return n.clear(),n.waitForCheckout=!0,null;e.handleException(a),await e.stop({reason:t?"addEventSizeExceeded":"addEvent"});let r=eN();r&&r.recordDroppedEvent("internal_sdk_error","replay")}}function sD(e,t){if(!e.eventBuffer||e.isPaused()||!e.isEnabled())return!1;let r=o5(t.timestamp);return!(r+e.timeouts.sessionIdlePause<Date.now())&&!(r>e.getContext().initialTimestamp+e.getOptions().maxReplayDuration)}function sL(e){return"transaction"===e.type}function sU(e){return"feedback"===e.type}function sF(e){return!!e.category}function sB(){let e=ek().getPropagationContext().dsc;e&&delete e.replay_id;let t=tv();if(t){let e=tR(t);delete e.replay_id}}function sH(e,t){return t.map(({type:t,start:r,end:n,name:i,data:a})=>{let o=e.throttledAddEvent({type:ob.Custom,timestamp:r,data:{tag:"performanceSpan",payload:{op:t,description:i,startTimestamp:r,endTimestamp:n,data:a}}});return"string"==typeof o?Promise.resolve(null):o})}function s$(e,t){e.isEnabled()&&null!==t&&!function(e,t){var r,n,i,a,o=eN();let s=o?.getDsn(),l=o?.getOptions().tunnel;return r=t,!!(n=s)&&r.includes(n.host)||(i=t,!!(a=l)&&ad(i)===ad(a))}(0,t.name)&&e.addUpdate(()=>(sH(e,[t]),!0))}function sW(e){if(!e)return;let t=new TextEncoder;try{if("string"==typeof e)return t.encode(e).length;if(e instanceof URLSearchParams)return t.encode(e.toString()).length;if(e instanceof FormData){let r=ah(e);return t.encode(r).length}if(e instanceof Blob)return e.size;if(e instanceof ArrayBuffer)return e.byteLength}catch{}}function sq(e){if(!e)return;let t=parseInt(e,10);return isNaN(t)?void 0:t}function sz(e,t){if(!e)return{headers:{},size:void 0,_meta:{warnings:[t]}};let r={...e._meta},n=r.warnings||[];return r.warnings=[...n,t],e._meta=r,e}function sX(e,t){if(!t)return null;let{startTimestamp:r,endTimestamp:n,url:i,method:a,statusCode:o,request:s,response:l}=t;return{type:e,start:r/1e3,end:n/1e3,name:i,data:{method:a,statusCode:o,request:s,response:l}}}function sG(e){return{headers:{},size:e,_meta:{warnings:["URL_SKIPPED"]}}}function sV(e,t,r){if(!t&&0===Object.keys(e).length)return;if(!t)return{headers:e};if(!r)return{headers:e,size:t};let n={headers:e,size:t},{body:i,warnings:a}=function(e){if(!e||"string"!=typeof e)return{body:e};let t=e.length>15e4,r=function(e){let t=e[0],r=e[e.length-1];return"["===t&&"]"===r||"{"===t&&"}"===r}(e);if(t){let t=e.slice(0,15e4);return r?{body:t,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:`${t}…`,warnings:["TEXT_TRUNCATED"]}}if(r)try{return{body:JSON.parse(e)}}catch{}return{body:e}}(r);return n.body=i,a?.length&&(n._meta={warnings:a}),n}function sY(e,t){return Object.entries(e).reduce((r,[n,i])=>{let a=n.toLowerCase();return t.includes(a)&&e[n]&&(r[a]=i),r},{})}function sK(e,t=x.document.baseURI){if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith(x.location.origin))return e;let r=new URL(e,t);if(r.origin!==new URL(t).origin)return e;let n=r.href;return!e.endsWith("/")&&n.endsWith("/")?n.slice(0,-1):n}async function sJ(e,t,r){try{let n=await sQ(e,t,r),i=sX("resource.fetch",n);s$(r.replay,i)}catch(e){}}async function sQ(e,t,r){var n,i;let a=Date.now(),{startTimestamp:o=a,endTimestamp:s=a}=t,{url:l,method:u,status_code:c=0,request_body_size:d,response_body_size:f}=e.data,p=(n=r.networkDetailAllowUrls,en(sK(l),n)&&(i=r.networkDetailDenyUrls,!en(sK(l),i)));return{startTimestamp:o,endTimestamp:s,url:l,method:u,statusCode:c,request:p?function({networkCaptureBodies:e,networkRequestHeaders:t},r,n){var i,a;let o=r?(i=r,a=t,1===i.length&&"string"!=typeof i[0]?s2(i[0],a):2===i.length?s2(i[1],a):{}):{};if(!e)return sV(o,n,void 0);let[s,l]=am(ag(r),sv),u=sV(o,n,s);return l?sz(u,l):u}(r,t.input,d):sG(d),response:await sZ(p,r,t.response,f)}}async function sZ(e,{networkCaptureBodies:t,networkResponseHeaders:r},n,i){if(!e&&void 0!==i)return sG(i);let a=n?s1(n.headers,r):{};if(!n||!t&&void 0!==i)return sV(a,i,void 0);let[o,s]=await s0(n),l=function(e,{networkCaptureBodies:t,responseBodySize:r,captureDetails:n,headers:i}){try{let a=e?.length&&void 0===r?sW(e):r;if(!n)return sG(a);if(t)return sV(i,a,e);return sV(i,a,void 0)}catch(e){return sV(i,r,void 0)}}(o,{networkCaptureBodies:t,responseBodySize:i,captureDetails:e,headers:a});return s?sz(l,s):l}async function s0(e){let t=function(e){try{return e.clone()}catch(e){}}(e);if(!t)return[void 0,"BODY_PARSE_ERROR"];try{var r;return[await (r=t,new Promise((e,t)=>{let n=nf(()=>t(Error("Timeout while trying to read response body")),500);s3(r).then(t=>e(t),e=>t(e)).finally(()=>clearTimeout(n))}))]}catch(e){if(e instanceof Error&&e.message.indexOf("Timeout")>-1)return[void 0,"BODY_PARSE_TIMEOUT"];return[void 0,"BODY_PARSE_ERROR"]}}function s1(e,t){let r={};return t.forEach(t=>{e.get(t)&&(r[t]=e.get(t))}),r}function s2(e,t){if(!e)return{};let r=e.headers;return r?r instanceof Headers?s1(r,t):Array.isArray(r)?{}:sY(r,t):{}}async function s3(e){return await e.text()}async function s5(e,t,r){try{let n=function(e,t,r){var n,i;let a=Date.now(),{startTimestamp:o=a,endTimestamp:s=a,input:l,xhr:u}=t,{url:c,method:d,status_code:f=0,request_body_size:p,response_body_size:h}=e.data;if(!c)return null;if(!u||(n=r.networkDetailAllowUrls,!en(sK(c),n))||(i=r.networkDetailDenyUrls,en(sK(c),i))){let e=sG(p);return{startTimestamp:o,endTimestamp:s,url:c,method:d,statusCode:f,request:e,response:sG(h)}}let m=u[rL],g=m?sY(m.request_headers,r.networkRequestHeaders):{},_=sY(function(e){let t=e.getAllResponseHeaders();return t?t.split("\r\n").reduce((e,t)=>{let[r,n]=t.split(": ");return n&&(e[r.toLowerCase()]=n),e},{}):{}}(u),r.networkResponseHeaders),[y,v]=r.networkCaptureBodies?am(l,sv):[void 0],[b,E]=r.networkCaptureBodies?function(e){let t=[];try{return[e.responseText]}catch(e){t.push(e)}try{var r=e.response,n=e.responseType;try{if("string"==typeof r)return[r];if(r instanceof Document)return[r.body.outerHTML];if("json"===n&&r&&"object"==typeof r)return[JSON.stringify(r)];if(!r)return[void 0]}catch(e){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}catch(e){t.push(e)}return[void 0]}(u):[void 0],S=sV(g,p,y),w=sV(_,h,b);return{startTimestamp:o,endTimestamp:s,url:c,method:d,statusCode:f,request:v?sz(S,v):S,response:E?sz(w,E):w}}(e,t,r),i=sX("resource.xhr",n);s$(r.replay,i)}catch(e){}}async function s4(e){try{return Promise.all(sH(e,[function(e){let{jsHeapSizeLimit:t,totalJSHeapSize:r,usedJSHeapSize:n}=e,i=Date.now()/1e3;return{type:"memory",name:"memory",start:i,end:i,data:{memory:{jsHeapSizeLimit:t,totalJSHeapSize:r,usedJSHeapSize:n}}}}(x.performance.memory)]))}catch(e){return[]}}let s8=x.navigator;async function s6({client:e,scope:t,replayId:r,event:n}){let i={event_id:r,integrations:"object"!=typeof e._integrations||null===e._integrations||Array.isArray(e._integrations)?void 0:Object.keys(e._integrations)};e.emit("preprocessEvent",n,i);let a=await tO(e.getOptions(),n,i,t,e,eM());if(!a)return null;e.emit("postprocessEvent",a,i),a.platform=a.platform||"javascript";let o=e.getSdkMetadata(),{name:s,version:l}=o?.sdk||{};return a.sdk={...a.sdk,name:s||"sentry.javascript.unknown",version:l||"0.0.0"},a}async function s7({recordingData:e,replayId:t,segmentId:r,eventContext:n,timestamp:i,session:a}){var o;let s,l=function({recordingData:e,headers:t}){let r,n=`${JSON.stringify(t)}
`;if("string"==typeof e)r=`${n}${e}`;else{let t=new TextEncoder().encode(n);(r=new Uint8Array(t.length+e.length)).set(t),r.set(e,t.length)}return r}({recordingData:e,headers:{segment_id:r}}),{urls:u,errorIds:c,traceIds:d,initialTimestamp:f}=n,p=eN(),h=ek(),m=p?.getTransport(),g=p?.getDsn();if(!p||!m||!g||!a.sampled)return eD({});let _={type:"replay_event",replay_start_timestamp:f/1e3,timestamp:i/1e3,error_ids:c,trace_ids:d,urls:u,replay_id:t,segment_id:r,replay_type:a.sampled},y=await s6({scope:h,client:p,replayId:t,event:_});if(!y)return p.recordDroppedEvent("event_processor","replay"),eD({});delete y.sdkProcessingMetadata;let v=(o=p.getOptions().tunnel,t1(t8(y,t4(y),o,g),[[{type:"replay_event"},y],[{type:"replay_recording",length:"string"==typeof l?new TextEncoder().encode(l).length:l.length},l]]));try{s=await m.send(v)}catch(t){let e=Error(ay);try{e.cause=t}catch{}throw e}if("number"==typeof s.statusCode&&(s.statusCode<200||s.statusCode>=300))throw new s9(s.statusCode);let b=nu({},s);if(nl(b,"replay"))throw new le(b);return s}class s9 extends Error{constructor(e){super(`Transport returned status code ${e}`)}}class le extends Error{constructor(e){super("Rate limit hit"),this.rateLimits=e}}async function lt(e,t={count:0,interval:5e3}){let{recordingData:r,onError:n}=e;if(r.length)try{return await s7(e),!0}catch(r){if(r instanceof s9||r instanceof le)throw r;if(tI("Replays",{_retryCount:t.count}),n&&n(r),t.count>=3){let e=Error(`${ay} - max retries exceeded`);try{e.cause=r}catch{}throw e}return t.interval*=++t.count,new Promise((r,n)=>{nf(async()=>{try{await lt(e,t),r(!0)}catch(e){n(e)}},t.interval)})}}let lr="__THROTTLED";class ln{constructor({options:e,recordingOptions:t}){this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._requiresManualStart=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=t,this._options=e,this._debouncedFlush=function(e,t,r){let n,i,a,o=r?.maxWait?Math.max(r.maxWait,t):0;function s(){return l(),n=e()}function l(){void 0!==i&&clearTimeout(i),void 0!==a&&clearTimeout(a),i=a=void 0}function u(){return i&&clearTimeout(i),i=nf(s,t),o&&void 0===a&&(a=nf(s,o)),n}return u.cancel=l,u.flush=function(){return void 0!==i||void 0!==a?s():n},u}(()=>this._flush(),this._options.flushMinDelay,{maxWait:this._options.flushMaxDelay}),this._throttledAddEvent=function(e,t,r){let n=new Map,i=e=>{let t=e-5;n.forEach((e,r)=>{r<t&&n.delete(r)})},a=()=>[...n.values()].reduce((e,t)=>e+t,0),o=!1;return(...t)=>{let r=Math.floor(Date.now()/1e3);if(i(r),a()>=300){let e=o;return o=!0,e?"__SKIPPED":lr}o=!1;let s=n.get(r)||0;return n.set(r,s+1),e(...t)}}((e,t)=>(function(e,t,r){return sD(e,t)?sj(e,t,r):Promise.resolve(null)})(this,e,t),0,0);let{slowClickTimeout:r,slowClickIgnoreSelectors:n}=this.getOptions(),i=r?{threshold:Math.min(3e3,r),timeout:r,scrollTimeout:300,ignoreSelector:n?n.join(","):""}:void 0;i&&(this.clickDetector=new st(this,i)),this._handleVisibilityChange=()=>{"visible"===x.document.visibilityState?this._doChangeToForegroundTasks():this._doChangeToBackgroundTasks()},this._handleWindowBlur=()=>{let e=si({category:"ui.blur"});this._doChangeToBackgroundTasks(e)},this._handleWindowFocus=()=>{let e=si({category:"ui.focus"});this._doChangeToForegroundTasks(e)},this._handleKeyboardEvent=e=>{!function(e,t){if(!e.isEnabled())return;e.updateUserActivity();let r=function(e){var t;let{metaKey:r,shiftKey:n,ctrlKey:i,altKey:a,key:o,target:s}=e;if(!s||"INPUT"===(t=s).tagName||"TEXTAREA"===t.tagName||t.isContentEditable||!o)return null;let l=r||i||a,u=1===o.length;if(!l&&u)return null;let c=Q(s,{maxStringLength:200})||"<unknown>",d=sl(s,c);return si({category:"ui.keyDown",message:c,data:{...d.data,metaKey:r,shiftKey:n,ctrlKey:i,altKey:a,key:o}})}(t);r&&o8(e,r)}(this,e)}}getContext(){return this._context}isEnabled(){return this._isEnabled}isPaused(){return this._isPaused}isRecordingCanvas(){return!!this._canvas}getOptions(){return this._options}handleException(e){this._options.onError&&this._options.onError(e)}initializeSampling(e){let{errorSampleRate:t,sessionSampleRate:r}=this._options,n=t<=0&&r<=0;if(this._requiresManualStart=n,!n)this._initializeSessionForSampling(e),this.session&&!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",this._initializeRecording())}start(){if(this._isEnabled&&"session"===this.recordingMode||this._isEnabled&&"buffer"===this.recordingMode)return;this._updateUserActivity();let e=sA({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=e,this._initializeRecording()}startBuffering(){if(this._isEnabled)return;let e=sA({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=e,this.recordingMode="buffer",this._initializeRecording()}startRecording(){try{var e;let t,r=this._canvas;this._stopRecording=o3({...this._recordingOptions,..."buffer"===this.recordingMode?{checkoutEveryNms:6e4}:this._options._experiments.continuousCheckout&&{checkoutEveryNms:Math.max(36e4,this._options._experiments.continuousCheckout)},emit:(e=this,t=!1,(r,n)=>{if(!e.checkAndHandleExpiredSession())return;let i=n||!t;t=!0,e.clickDetector&&function(e,t){try{var r;if(r=t,3!==r.type)return;let{source:n}=t.data;if(se.has(n)&&e.registerMutation(t.timestamp),n===oE.Scroll&&e.registerScroll(t.timestamp),t.data.source===oE.MouseInteraction){let{type:r,id:n}=t.data,i=o3.mirror.getNode(n);i instanceof HTMLElement&&r===oS.Click&&e.registerClick(i)}}catch{}}(e.clickDetector,r),e.addUpdate(()=>{var t;if("buffer"===e.recordingMode&&i&&e.setInitialState(),!sN(e,r,i))return!0;if(!i)return!1;let n=e.session;if(t=e,i&&t.session&&0===t.session.segmentId&&sN(t,function(e){let t=e.getOptions();return{type:ob.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:e.isRecordingCanvas(),sessionSampleRate:t.sessionSampleRate,errorSampleRate:t.errorSampleRate,useCompressionOption:t.useCompression,blockAllMedia:t.blockAllMedia,maskAllText:t.maskAllText,maskAllInputs:t.maskAllInputs,useCompression:!!e.eventBuffer&&"worker"===e.eventBuffer.type,networkDetailHasUrls:t.networkDetailAllowUrls.length>0,networkCaptureBodies:t.networkCaptureBodies,networkRequestHasHeaders:t.networkRequestHeaders.length>0,networkResponseHasHeaders:t.networkResponseHeaders.length>0}}}}(t),!1),"buffer"===e.recordingMode&&n&&e.eventBuffer){let t=e.eventBuffer.getEarliestTimestamp();t&&(n.started=t,e.getOptions().stickySession&&sO(n))}return!!n?.previousSessionId||("session"===e.recordingMode&&e.flush(),!0)})}),.../iPhone|iPad|iPod/i.test(s8?.userAgent??"")||/Macintosh/i.test(s8?.userAgent??"")&&s8?.maxTouchPoints&&s8?.maxTouchPoints>1?{sampling:{mousemove:!1}}:{},onMutation:this._onMutationHandler.bind(this),...r?{recordCanvas:r.recordCanvas,getCanvasManager:r.getCanvasManager,sampling:r.sampling,dataURLOptions:r.dataURLOptions}:{}})}catch(e){this.handleException(e)}}stopRecording(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(e){return this.handleException(e),!1}}async stop({forceFlush:e=!1,reason:t}={}){if(this._isEnabled){this._isEnabled=!1;try{sB(),this._removeListeners(),this.stopRecording(),this._debouncedFlush.cancel(),e&&await this._flush({force:!0}),this.eventBuffer?.destroy(),this.eventBuffer=null,function(){if(sP())try{x.sessionStorage.removeItem(a_)}catch{}}(),this.session=void 0}catch(e){this.handleException(e)}}}pause(){this._isPaused||(this._isPaused=!0,this.stopRecording())}resume(){this._isPaused&&this._checkSession()&&(this._isPaused=!1,this.startRecording())}async sendBufferedReplayOrFlush({continueRecording:e=!0}={}){if("session"===this.recordingMode)return this.flushImmediate();let t=Date.now();await this.flushImmediate();let r=this.stopRecording();e&&r&&"session"!==this.recordingMode&&(this.recordingMode="session",this.session&&(this._updateUserActivity(t),this._updateSessionActivity(t),this._maybeSaveSession()),this.startRecording())}addUpdate(e){let t=e();"buffer"!==this.recordingMode&&!0!==t&&this._debouncedFlush()}triggerUserActivity(){if(this._updateUserActivity(),!this._stopRecording){if(!this._checkSession())return;this.resume();return}this.checkAndHandleExpiredSession(),this._updateSessionActivity()}updateUserActivity(){this._updateUserActivity(),this._updateSessionActivity()}conditionalFlush(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}flush(){return this._debouncedFlush()}flushImmediate(){return this._debouncedFlush(),this._debouncedFlush.flush()}cancelFlush(){this._debouncedFlush.cancel()}getSessionId(){return this.session?.id}checkAndHandleExpiredSession(){return this._lastActivity&&sI(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled?void this.pause():!!this._checkSession()}setInitialState(){let e=`${x.location.pathname}${x.location.hash}${x.location.search}`,t=`${x.location.origin}${e}`;this.performanceEntries=[],this.replayPerformanceEntries=[],this._clearContext(),this._context.initialUrl=t,this._context.initialTimestamp=Date.now(),this._context.urls.push(t)}throttledAddEvent(e,t){let r=this._throttledAddEvent(e,t);if(r===lr){let e=si({category:"replay.throttled"});this.addUpdate(()=>!sN(this,{type:5,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e,metric:!0}}))}return r}getCurrentRoute(){let e=this.lastActiveSpan||tv(),t=e&&ty(e),r=(t&&td(t).data||{})[eG];if(t&&r&&["route","custom"].includes(r))return td(t).description}_initializeRecording(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=function({useCompression:e,workerUrl:t}){if(e&&window.Worker){let e=function(e){try{let t=e||function(){if("undefined"==typeof __SENTRY_EXCLUDE_REPLAY_WORKER__||!__SENTRY_EXCLUDE_REPLAY_WORKER__){let e=new Blob(['var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),s=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),a=function(t,e){for(var i=new n(31),s=0;s<31;++s)i[s]=e+=1<<t[s-1];var a=new r(i[30]);for(s=1;s<30;++s)for(var o=i[s];o<i[s+1];++o)a[o]=o-i[s]<<5|s;return{b:i,r:a}},o=a(e,2),h=o.b,f=o.r;h[28]=258,f[258]=28;for(var l=a(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,s=0,a=new n(r);s<i;++s)t[s]&&++a[t[s]-1];var o,h=new n(r);for(s=1;s<r;++s)h[s]=h[s-1]+a[s-1]<<1;if(e){o=new n(1<<r);var f=15-r;for(s=0;s<i;++s)if(t[s])for(var l=s<<4|t[s],c=r-t[s],v=h[t[s]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>f]=l}else for(o=new n(i),s=0;s<i;++s)t[s]&&(o[s]=u[h[t[s]-1]++]>>15-t[s]);return o},p=new t(288);for(c=0;c<144;++c)p[c]=8;for(c=144;c<256;++c)p[c]=9;for(c=256;c<280;++c)p[c]=7;for(c=280;c<288;++c)p[c]=8;var g=new t(32);for(c=0;c<32;++c)g[c]=5;var w=d(p,9,0),y=d(g,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},_=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},x=function(r,e){for(var i=[],s=0;s<r.length;++s)r[s]&&i.push({s:s,f:r[s]});var a=i.length,o=i.slice();if(!a)return{t:F,l:0};if(1==a){var h=new t(i[0].s+1);return h[i[0].s]=1,{t:h,l:1}}i.sort((function(t,n){return t.f-n.f})),i.push({s:-1,f:25001});var f=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:f.f+l.f,l:f,r:l};c!=a-1;)f=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:f.f+l.f,l:f,r:l};var d=o[0].s;for(s=1;s<a;++s)o[s].s>d&&(d=o[s].s);var p=new n(d+1),g=A(i[c-1],p,0);if(g>e){s=0;var w=0,y=g-e,m=1<<y;for(o.sort((function(t,n){return p[n.s]-p[t.s]||t.f-n.f}));s<a;++s){var b=o[s].s;if(!(p[b]>e))break;w+=m-(1<<g-p[b]),p[b]=e}for(w>>=y;w>0;){var M=o[s].s;p[M]<e?w-=1<<e-p[M]++-1:++s}for(;s>=0&&w;--s){var E=o[s].s;p[E]==e&&(--p[E],++w)}g=e}return{t:new t(p),l:g}},A=function(t,n,r){return-1==t.s?Math.max(A(t.l,n,r+1),A(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,s=t[0],a=1,o=function(t){e[i++]=t},h=1;h<=r;++h)if(t[h]==s&&h!=r)++a;else{if(!s&&a>2){for(;a>138;a-=138)o(32754);a>2&&(o(a>10?a-11<<5|28690:a-3<<5|12305),a=0)}else if(a>3){for(o(s),--a;a>6;a-=6)o(8304);a>2&&(o(a-3<<5|8208),a=0)}for(;a--;)o(s);a=1,s=t[h]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var s=0;s<e;++s)t[i+s+4]=r[s];return 8*(i+4+e)},U=function(t,r,a,o,h,f,l,u,c,v,m){z(r,m++,a),++h[256];for(var b=x(h,15),M=b.t,E=b.l,A=x(f,15),U=A.t,C=A.l,F=D(M),I=F.c,S=F.n,L=D(U),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=x(q,7),H=G.t,J=G.l,K=19;K>4&&!H[s[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(h,p)+T(f,g)+l,X=T(h,M)+T(f,U)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(U,C,0),R=U;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[s[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=w,P=p,Q=y,R=g;for(B=0;B<u;++B){var rt=o[B];if(rt>255){_(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;_(r,m,Q[et]),m+=R[et],et>3&&(_(r,m,rt>>5&8191),m+=i[et])}else _(r,m,N[rt]),m+=P[rt]}return _(r,m,N[256]),m+P[256]},C=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}},L=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,s=0|r.length,a=0;a!=s;){for(var o=Math.min(a+2655,s);a<o;++a)i+=e+=r[a];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},O=function(s,a,o,h,u){if(!u&&(u={l:1},a.dictionary)){var c=a.dictionary.subarray(-32768),v=new t(c.length+s.length);v.set(c),v.set(s,c.length),s=v,u.w=c.length}return function(s,a,o,h,u,c){var v=c.z||s.length,d=new t(h+v+5*(1+Math.ceil(v/7e3))+u),p=d.subarray(h,d.length-u),g=c.l,w=7&(c.r||0);if(a){w&&(p[0]=c.r>>3);for(var y=C[a-1],M=y>>13,E=8191&y,z=(1<<o)-1,_=c.p||new n(32768),x=c.h||new n(z+1),A=Math.ceil(o/3),D=2*A,T=function(t){return(s[t]^s[t+1]<<A^s[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=x[H];if(_[J]=K,x[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!g)){w=U(s,p,0,F,I,S,O,q,G,j-G,w),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(s[j+Q]==s[j+Q-W]){for(var $=0;$<Z&&s[j+$]==s[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-_[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=_[J])&32767}if(R){F[q++]=268435456|f[Q]<<18|l[R];var it=31&f[Q],st=31&l[R];O+=e[it]+i[st],++I[257+it],++S[st],B=j+Q,++L}else F[q++]=s[j],++I[s[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=s[j],++I[s[j]];w=U(s,p,g,F,I,S,O,q,G,j-G,w),g||(c.r=7&w|p[w/8|0]<<3,w-=7,c.h=x,c.p=_,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+g;j+=65535){var at=j+65535;at>=v&&(p[w/8|0]=g,at=v),w=k(p,w+1,s.subarray(j,at))}c.i=v}return b(d,0,h+m(w)+u)}(s,null==a.level?6:a.level,null==a.mem?u.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(s.length)))):20:12+a.mem,o,h,u)},j=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},q=function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&j(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}},B=function(t){return 10+(t.filename?t.filename.length+1:0)},G=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(O(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var s=this.b.length-this.s.z;this.b.set(n.subarray(0,s),this.s.z),this.s.z=this.b.length,this.p(this.b,!1),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(s),32768),this.s.z=n.length-s+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n.prototype.flush=function(){this.ondata||E(5),this.s.l&&E(4),this.p(this.b,!1),this.s.w=this.s.i,this.s.i-=2},n}();var H=function(){function t(t,n){this.c=L(),this.v=1,G.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),G.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=O(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=L();i.p(n.dictionary),j(t,2,i.d())}}(r,this.o),this.v=0),n&&j(r,r.length-4,this.c.d()),this.ondata(r,n)},t.prototype.flush=function(){G.prototype.flush.call(this)},t}(),J="undefined"!=typeof TextEncoder&&new TextEncoder,K="undefined"!=typeof TextDecoder&&new TextDecoder;try{K.decode(F,{stream:!0})}catch(t){}var N=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(P(t),this.d=n||!1)},t}();function P(n,r){if(J)return J.encode(n);for(var e=n.length,i=new t(n.length+(n.length>>1)),s=0,a=function(t){i[s++]=t},o=0;o<e;++o){if(s+5>i.length){var h=new t(s+8+(e-o<<1));h.set(i),i=h}var f=n.charCodeAt(o);f<128||r?a(f):f<2048?(a(192|f>>6),a(128|63&f)):f>55295&&f<57344?(a(240|(f=65536+(1047552&f)|1023&n.charCodeAt(++o))>>18),a(128|f>>12&63),a(128|f>>6&63),a(128|63&f)):(a(224|f>>12),a(128|f>>6&63),a(128|63&f))}return b(i,0,s)}function Q(t){return function(t,n){n||(n={});var r=S(),e=t.length;r.p(t);var i=O(t,n,B(n),8),s=i.length;return q(i,n),j(i,s-8,r.d()),j(i,s-4,e),i}(P(t))}const R=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(const r of t)n+=r.length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new H,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new N(((t,n)=>{this.deflate.push(t,n)})),this.stream.push("[")}},V={clear:()=>{R.clear()},addEvent:t=>R.addEvent(t),finish:()=>R.finish(),compress:t=>Q(t)};addEventListener("message",(function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in V&&"function"==typeof V[n])try{const t=V[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}})),postMessage({id:void 0,method:"init",success:!0,response:void 0});']);return URL.createObjectURL(e)}return""}();if(!t)return;let r=new Worker(t);return new sR(r)}catch(e){}}(t);if(e)return e}return new sE}({useCompression:this._options.useCompression,workerUrl:this._options.workerUrl}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}_initializeSessionForSampling(e){let t=this._options.errorSampleRate>0,r=sA({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,previousSessionId:e},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:t});this.session=r}_checkSession(){if(!this.session)return!1;let e=this.session;return!sM(e,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})||(this._refreshSession(e),!1)}async _refreshSession(e){this._isEnabled&&(await this.stop({reason:"refresh session"}),this.initializeSampling(e.id))}_addListeners(){try{x.document.addEventListener("visibilitychange",this._handleVisibilityChange),x.addEventListener("blur",this._handleWindowBlur),x.addEventListener("focus",this._handleWindowFocus),x.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(!function(e,{autoFlushOnFeedback:t}){let r=eN();rN(ss(e)),rB(t=>{if(!e.isEnabled())return;let r=function(e){let{from:t,to:r}=e,n=Date.now()/1e3;return{type:"navigation.push",start:n,end:n,name:r,data:{previous:t}}}(t);null!==r&&(e.getContext().urls.push(r.name),e.triggerUserActivity(),e.addUpdate(()=>(sH(e,[r]),!1)))});let n=eN();n&&n.on("beforeAddBreadcrumb",t=>(function(e,t){var r;if(!e.isEnabled()||!sF(t))return;let n=(r=t,!sF(r)||["fetch","xhr","sentry.event","sentry.transaction"].includes(r.category)||r.category.startsWith("ui.")?null:"console"===r.category?function(e){let t=e.data?.arguments;if(!Array.isArray(t)||0===t.length)return si(e);let r=!1,n=t.map(e=>{if(!e)return e;if("string"==typeof e)return e.length>5e3?(r=!0,`${e.slice(0,5e3)}…`):e;if("object"==typeof e)try{let t=eX(e,7);if(JSON.stringify(t).length>5e3)return r=!0,`${JSON.stringify(t,null,2).slice(0,5e3)}…`;return t}catch{}return e});return si({...e,data:{...e.data,arguments:n,...r?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}(r):si(r));n&&o8(e,n)})(e,t));let i=eN();try{let{networkDetailAllowUrls:t,networkDetailDenyUrls:r,networkCaptureBodies:n,networkRequestHeaders:a,networkResponseHeaders:o}=e.getOptions(),s={replay:e,networkDetailAllowUrls:t,networkDetailDenyUrls:r,networkCaptureBodies:n,networkRequestHeaders:a,networkResponseHeaders:o};i&&i.on("beforeAddBreadcrumb",(e,t)=>(function(e,t,r){if(t.data)try{var n,i,a,o;if(n=t,"xhr"===n.category&&(i=r,i?.xhr)&&(!function(e,t){let{xhr:r,input:n}=t;if(!r)return;let i=sW(n),a=r.getResponseHeader("content-length")?sq(r.getResponseHeader("content-length")):function(e,t){try{let r="json"===t&&e&&"object"==typeof e?JSON.stringify(e):e;return sW(r)}catch{return}}(r.response,r.responseType);void 0!==i&&(e.data.request_body_size=i),void 0!==a&&(e.data.response_body_size=a)}(t,r),s5(t,r,e)),a=t,"fetch"===a.category&&(o=r,o?.response)){let{input:n,response:i}=r,a=sW(n?ag(n):void 0),o=i?sq(i.headers.get("content-length")):void 0;void 0!==a&&(t.data.request_body_size=a),void 0!==o&&(t.data.response_body_size=o),sJ(t,r,e)}}catch(e){}})(s,e,t))}catch{}tk(Object.assign((t,r)=>!e.isEnabled()||e.isPaused()?t:"replay_event"===t.type?(delete t.breadcrumbs,t):!t.type||sL(t)||sU(t)?e.checkAndHandleExpiredSession()?sU(t)?(e.flush(),t.contexts.feedback.replay_id=e.getSessionId(),e.triggerUserActivity(),e.addUpdate(()=>!t.timestamp||(e.throttledAddEvent({type:ob.Custom,timestamp:1e3*t.timestamp,data:{tag:"breadcrumb",payload:{timestamp:t.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:t.event_id}}}}),!1)),t):!t.type&&t.exception&&t.exception.values&&t.exception.values.length&&r.originalException?.__rrweb__&&!e.getOptions()._experiments.captureExceptions?null:(("buffer"===e.recordingMode&&t.message!==ay&&t.exception&&!t.type&&sT(e.getOptions().errorSampleRate)||"session"===e.recordingMode)&&(t.tags={...t.tags,replayId:e.getSessionId()}),t):(sB(),t):t,{id:"Replay"})),r&&(r.on("beforeSendEvent",t=>{e.isEnabled()&&!t.type&&function(e,t){let r=t.exception?.values?.[0]?.value;"string"==typeof r&&(r.match(/(reactjs\.org\/docs\/error-decoder\.html\?invariant=|react\.dev\/errors\/)(418|419|422|423|425)/)||r.match(/(does not match server-rendered HTML|Hydration failed because)/i))&&o8(e,si({category:"replay.hydrate-error",data:{url:Z()}}))}(e,t)}),r.on("afterSendEvent",(t,r)=>{if(!e.isEnabled()||t.type&&!sL(t))return;let n=r?.statusCode;if(n&&!(n<200)&&!(n>=300)){if(sL(t))return void function(e,t){let r=e.getContext();t.contexts?.trace?.trace_id&&r.traceIds.size<100&&r.traceIds.add(t.contexts.trace.trace_id)}(e,t);!function(e,t){let r=e.getContext();if(t.event_id&&r.errorIds.size<100&&r.errorIds.add(t.event_id),"buffer"!==e.recordingMode||!t.tags||!t.tags.replayId)return;let{beforeErrorSampling:n}=e.getOptions();("function"!=typeof n||n(t))&&nf(async()=>{try{await e.sendBufferedReplayOrFlush()}catch(t){e.handleException(t)}})}(e,t)}}),r.on("createDsc",t=>{let r=e.getSessionId();r&&e.isEnabled()&&"session"===e.recordingMode&&e.checkAndHandleExpiredSession()&&(t.replay_id=r)}),r.on("spanStart",t=>{e.lastActiveSpan=t}),r.on("spanEnd",t=>{e.lastActiveSpan=t}),r.on("beforeSendFeedback",async(r,n)=>{let i=e.getSessionId();n?.includeReplay&&e.isEnabled()&&i&&r.contexts?.feedback&&("api"===r.contexts.feedback.source&&t&&await e.flush(),r.contexts.feedback.replay_id=i)}),t&&r.on("openFeedbackWidget",async()=>{await e.flush()}))}(this,{autoFlushOnFeedback:this._options._experiments.autoFlushOnFeedback}),this._hasInitializedCoreListeners=!0)}catch(e){this.handleException(e)}this._performanceCleanupCallback=function(e){function t(t){e.performanceEntries.includes(t)||e.performanceEntries.push(t)}function r({entries:e}){e.forEach(t)}let n=[];return["navigation","paint","resource"].forEach(e=>{n.push(ib(e,r))}),n.push(i_(sc(sp,e)),ig(sc(sh,e)),iy(sc(sm,e)),iv(sc(sg,e))),()=>{n.forEach(e=>e())}}(this)}_removeListeners(){try{x.document.removeEventListener("visibilitychange",this._handleVisibilityChange),x.removeEventListener("blur",this._handleWindowBlur),x.removeEventListener("focus",this._handleWindowFocus),x.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceCleanupCallback&&this._performanceCleanupCallback()}catch(e){this.handleException(e)}}_doChangeToBackgroundTasks(e){this.session&&(sk(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(e&&this._createCustomBreadcrumb(e),this.conditionalFlush()))}_doChangeToForegroundTasks(e){if(this.session)this.checkAndHandleExpiredSession()&&e&&this._createCustomBreadcrumb(e)}_updateUserActivity(e=Date.now()){this._lastActivity=e}_updateSessionActivity(e=Date.now()){this.session&&(this.session.lastActivity=e,this._maybeSaveSession())}_createCustomBreadcrumb(e){this.addUpdate(()=>{this.throttledAddEvent({type:ob.Custom,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e}})})}_addPerformanceEntries(){let e=this.performanceEntries.map(sd).filter(Boolean).concat(this.replayPerformanceEntries);if(this.performanceEntries=[],this.replayPerformanceEntries=[],this._requiresManualStart){let t=this._context.initialTimestamp/1e3;e=e.filter(e=>e.start>=t)}return Promise.all(sH(this,e))}_clearContext(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}_updateInitialTimestampFromEventBuffer(){let{session:e,eventBuffer:t}=this;if(!e||!t||this._requiresManualStart||e.segmentId)return;let r=t.getEarliestTimestamp();r&&r<this._context.initialTimestamp&&(this._context.initialTimestamp=r)}_popEventContext(){let e={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),e}async _runFlush(){let e=this.getSessionId();if(this.session&&this.eventBuffer&&e&&(await this._addPerformanceEntries(),this.eventBuffer?.hasEvents)){if((await s4(this),this.eventBuffer)&&e===this.getSessionId())try{this._updateInitialTimestampFromEventBuffer();let t=Date.now();if(t-this._context.initialTimestamp>this._options.maxReplayDuration+3e4)throw Error("Session is too long, not sending replay");let r=this._popEventContext(),n=this.session.segmentId++;this._maybeSaveSession();let i=await this.eventBuffer.finish();await lt({replayId:e,recordingData:i,segmentId:n,eventContext:r,session:this.session,timestamp:t,onError:e=>this.handleException(e)})}catch(t){this.handleException(t),this.stop({reason:"sendReplay"});let e=eN();e&&e.recordDroppedEvent(t instanceof le?"ratelimit_backoff":"send_error","replay")}}}async _flush({force:e=!1}={}){if(!this._isEnabled&&!e||!this.checkAndHandleExpiredSession()||!this.session)return;let t=this.session.started,r=Date.now()-t;this._debouncedFlush.cancel();let n=r<this._options.minReplayDuration,i=r>this._options.maxReplayDuration+5e3;if(n||i){n&&this._debouncedFlush();return}let a=this.eventBuffer;a&&0===this.session.segmentId&&a.hasCheckout;let o=!!this._flushLock;this._flushLock||(this._flushLock=this._runFlush());try{await this._flushLock}catch(e){this.handleException(e)}finally{this._flushLock=void 0,o&&this._debouncedFlush()}}_maybeSaveSession(){this.session&&this._options.stickySession&&sO(this.session)}_onMutationHandler(e){let t=e.length,r=this._options.mutationLimit,n=this._options.mutationBreadcrumbLimit,i=r&&t>r;if(t>n||i){let e=si({category:"replay.mutations",data:{count:t,limit:i}});this._createCustomBreadcrumb(e)}return!i||(this.stop({reason:"mutationLimit",forceFlush:"session"===this.recordingMode}),!1)}}function li(e,t){return[...e,...t].join(",")}let la='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',lo=["content-length","content-type","accept"],ls=!1;class ll{constructor({flushMinDelay:e=5e3,flushMaxDelay:t=5500,minReplayDuration:r=4999,maxReplayDuration:n=36e5,stickySession:i=!0,useCompression:a=!0,workerUrl:o,_experiments:s={},maskAllText:l=!0,maskAllInputs:u=!0,blockAllMedia:c=!0,mutationBreadcrumbLimit:d=750,mutationLimit:f=1e4,slowClickTimeout:p=7e3,slowClickIgnoreSelectors:h=[],networkDetailAllowUrls:m=[],networkDetailDenyUrls:g=[],networkCaptureBodies:_=!0,networkRequestHeaders:y=[],networkResponseHeaders:v=[],mask:b=[],maskAttributes:E=["title","placeholder"],unmask:S=[],block:w=[],unblock:R=[],ignore:P=[],maskFn:T,beforeAddRecordingEvent:O,beforeErrorSampling:x,onError:C}={}){this.name="Replay";let I=function({mask:e,unmask:t,block:r,unblock:n,ignore:i}){return{maskTextSelector:li(e,[".sentry-mask","[data-sentry-mask]"]),unmaskTextSelector:li(t,[]),blockSelector:li(r,[".sentry-block","[data-sentry-block]","base","iframe[srcdoc]:not([src])"]),unblockSelector:li(n,[]),ignoreSelector:li(i,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'])}}({mask:b,unmask:S,block:w,unblock:R,ignore:P});if(this._recordingOptions={maskAllInputs:u,maskAllText:l,maskInputOptions:{password:!0},maskTextFn:T,maskInputFn:T,maskAttributeFn:(e,t,r)=>(function({el:e,key:t,maskAttributes:r,maskAllText:n,privacyOptions:i,value:a}){return!n||i.unmaskTextSelector&&e.matches(i.unmaskTextSelector)?a:r.includes(t)||"value"===t&&"INPUT"===e.tagName&&["submit","button"].includes(e.getAttribute("type")||"")?a.replace(/[\S]/g,"*"):a})({maskAttributes:E,maskAllText:l,privacyOptions:I,key:e,value:t,el:r}),...I,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:e=>{try{e.__rrweb__=!0}catch(e){}},recordCrossOriginIframes:!!s.recordCrossOriginIframes},this._initialOptions={flushMinDelay:e,flushMaxDelay:t,minReplayDuration:Math.min(r,15e3),maxReplayDuration:Math.min(n,36e5),stickySession:i,useCompression:a,workerUrl:o,blockAllMedia:c,maskAllInputs:u,maskAllText:l,mutationBreadcrumbLimit:d,mutationLimit:f,slowClickTimeout:p,slowClickIgnoreSelectors:h,networkDetailAllowUrls:m,networkDetailDenyUrls:g,networkCaptureBodies:_,networkRequestHeaders:lu(y),networkResponseHeaders:lu(v),beforeAddRecordingEvent:O,beforeErrorSampling:x,onError:C,_experiments:s},this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?`${this._recordingOptions.blockSelector},${la}`:la),this._isInitialized&&ap())throw Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}get _isInitialized(){return ls}set _isInitialized(e){ls=e}afterAllSetup(e){ap()&&!this._replay&&(this._setup(e),this._initialize(e))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}flush(e){return this._replay?this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(e):(this._replay.start(),Promise.resolve()):Promise.resolve()}getReplayId(){if(this._replay?.isEnabled())return this._replay.getSessionId()}getRecordingMode(){if(this._replay?.isEnabled())return this._replay.recordingMode}_initialize(e){this._replay&&(this._maybeLoadFromReplayCanvasIntegration(e),this._replay.initializeSampling())}_setup(e){let t=function(e,t){let r=t.getOptions(),n={sessionSampleRate:0,errorSampleRate:0,...e},i=e9(r.replaysSessionSampleRate),a=e9(r.replaysOnErrorSampleRate);return null==i&&null==a&&j(()=>{console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")}),null!=i&&(n.sessionSampleRate=i),null!=a&&(n.errorSampleRate=a),n}(this._initialOptions,e);this._replay=new ln({options:t,recordingOptions:this._recordingOptions})}_maybeLoadFromReplayCanvasIntegration(e){try{let t=e.getIntegrationByName("ReplayCanvas");if(!t)return;this._replay._canvas=t.getOptions()}catch{}}}function lu(e){return[...lo,...e.map(e=>e.toLowerCase())]}r(57177).env.NEXT_PUBLIC_SENTRY_DISABLED||function(e){ac&&j(()=>{console.warn("[@sentry/nextjs] You are calling `Sentry.init()` more than once on the client. This can happen if you have both a `sentry.client.config.ts` and a `instrumentation-client.ts` file with `Sentry.init()` calls. It is recommended to call `Sentry.init()` once in `instrumentation-client.ts`.")}),ac=!0;let t={environment:function(e){let t=e?ny.env.NEXT_PUBLIC_VERCEL_ENV:ny.env.VERCEL_ENV;return t?`vercel-${t}`:void 0}(!0)||"production",defaultIntegrations:function(e){let t=nh(e);("undefined"==typeof __SENTRY_TRACING__||__SENTRY_TRACING__)&&t.push(function(e={}){let t=i1({...e,instrumentNavigation:!1,instrumentPageLoad:!1,onRequestSpanStart(...t){let[r,{headers:n}]=t;return n?.get("next-router-prefetch")&&r?.setAttribute("http.request.prefetch",!0),e.onRequestSpanStart?.(...t)}}),{instrumentPageLoad:r=!0,instrumentNavigation:n=!0}=e;return{...t,afterAllSetup(e){n&&function(e){if(x.document.getElementById("__NEXT_DATA__"))an.events.on("routeChangeStart",t=>{let r,n,i=rA(t),a=function(e){let t=x.__BUILD_MANIFEST?.sortedPages;if(t)return t.find(t=>{let r=function(e){let t=e.split("/"),r="";t[t.length-1]?.match(/^\[\[\.\.\..+\]\]$/)&&(t.pop(),r="(?:/(.+?))?");let n=t.map(e=>e.replace(/^\[\.\.\..+\]$/,"(.+?)").replace(/^\[.*\]$/,"([^/]+?)")).join("/");return RegExp(`^${n}${r}(?:/)?$`)}(t);return e.match(r)})}(i);a?(r=a,n="route"):(r=i,n="url"),i3(e,{name:r,attributes:{[eK]:"navigation",[eJ]:"auto.navigation.nextjs.pages_router_instrumentation",[eG]:n}})});else{T=(t,r)=>{let n=new URL(t,x.location.href).pathname;"router-patch"===i6&&(i6="transition-start-hook");let i=i7.current;i?(i.updateName(n),i.setAttributes({"navigation.type":`router.${r}`}),i7.current=void 0):i3(e,{name:n,attributes:{[eK]:"navigation",[eJ]:"auto.navigation.nextjs.app_router_instrumentation",[eG]:"url","navigation.type":`router.${r}`}})},x.addEventListener("popstate",()=>{i7.current?.isRecording()?(i7.current.updateName(x.location.pathname),i7.current.setAttribute(eG,"url")):i7.current=i3(e,{name:x.location.pathname,attributes:{[eJ]:"auto.navigation.nextjs.app_router_instrumentation",[eG]:"url","navigation.type":"browser.popstate"}})});let t=!1,r=0,n=setInterval(()=>{r++;let i=x?.next?.router??x?.nd?.router;t||r>500?clearInterval(n):i&&(clearInterval(n),t=!0,at(e,i,i7),["nd","next"].forEach(t=>{let r=x[t];r&&(x[t]=new Proxy(r,{set:(t,r,n)=>("router"===r&&"object"==typeof n&&null!==n&&at(e,n,i7),t[r]=n,!0)}))}))},20)}}(e),t.afterAllSetup(e),r&&function(e){if(x.document.getElementById("__NEXT_DATA__")){let{route:t,params:r,sentryTrace:n,baggage:i}=function(){let e,t=x.document.getElementById("__NEXT_DATA__");if(t?.innerHTML)try{e=JSON.parse(t.innerHTML)}catch(e){}if(!e)return{};let r={},{page:n,query:i,props:a}=e;return r.route=n,r.params=i,a?.pageProps&&(r.sentryTrace=a.pageProps._sentryTraceData,r.baggage=a.pageProps._sentryBaggage),r}(),a=tn(i),o=t||x.location.pathname;a?.["sentry-transaction"]&&"/_error"===o&&(o=(o=a["sentry-transaction"]).replace(/^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\s+/i,""));let s=ey();i2(e,{name:o,startTime:s?s/1e3:void 0,attributes:{[eK]:"pageload",[eJ]:"auto.pageload.nextjs.pages_router_instrumentation",[eG]:t?"route":"url",...r&&e.getOptions().sendDefaultPii&&{...r}}},{sentryTrace:n,baggage:i})}else{let t=ey();i2(e,{name:x.location.pathname,startTime:t?t/1e3:void 0,attributes:{[eK]:"pageload",[eJ]:"auto.pageload.nextjs.app_router_instrumentation",[eG]:"url"}})}}(e)}}}());let r=x._sentryRewriteFramesAssetPrefixPath||"",n=au.env._sentryAssetPrefix||x._sentryAssetPrefix,i=au.env._sentryBasePath||x._sentryBasePath,a="true"===au.env._experimentalThirdPartyOriginStackFrames||"true"===x._experimentalThirdPartyOriginStackFrames;return t.push(al({assetPrefix:n,basePath:i,rewriteFramesAssetPrefixPath:r,experimentalThirdPartyOriginStackFrames:a})),t}(e),release:"3627ac2a0c932ad040b50dffdfd93db9500798b5",...e};!function(e){let t="/monitoring";if(t&&e.dsn){let r=tZ(e.dsn);if(!r)return;let n=r.host.match(/^o(\d+)\.ingest(?:\.([a-z]{2}))?\.sentry\.io$/);if(n){let i=n[1],a=n[2],o=`${t}?o=${i}&p=${r.projectId}`;a&&(o+=`&r=${a}`),e.tunnel=o}}}(t),L(t,"nextjs",["nextjs","react"]),function(e){let t={...e};L(t,"react"),tI("react",{version:n_.version}),function(e={}){var t,r;let n=function(e={}){return{...{defaultIntegrations:nh(),release:"string"==typeof __SENTRY_RELEASE__?__SENTRY_RELEASE__:x.SENTRY_RELEASE?.id,sendClientReports:!0},...function(e){let t={};for(let r of Object.getOwnPropertyNames(e))void 0!==e[r]&&(t[r]=e[r]);return t}(e)}}(e);if(!n.skipBrowserExtensionCheck&&function(){let e=void 0!==x.window&&x;if(!e)return!1;let t=e.chrome?"chrome":"browser",r=e[t],n=r?.runtime?.id,i=Z()||"",a=!!n&&x===x.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(e=>i.startsWith(`${e}//`)),o=void 0!==e.nw;return!!n&&!a&&!o}())return;let i={...n,stackParser:Array.isArray(t=n.stackParser||na)?eH(...t):t,integrations:function(e){let t,r=e.defaultIntegrations||[],n=e.integrations;if(r.forEach(e=>{e.isDefaultInstance=!0}),Array.isArray(n))t=[...r,...n];else if("function"==typeof n){let e=n(r);t=Array.isArray(e)?e:[e]}else t=r;let i={};return t.forEach(e=>{let{name:t}=e,r=i[t];r&&!r.isDefaultInstance&&e.isDefaultInstance||(i[t]=e)}),Object.values(i)}(n),transport:n.transport||np};!0===i.debug&&j(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")}),ek().update(i.initialScope);let a=new rg(i);r=a,ek().setClient(r),a.init()}(t)}(t);let r=e=>"transaction"===e.type&&"/404"===e.transaction?null:e;r.id="NextClient404Filter",tk(r);let n=e=>"transaction"===e.type&&e.transaction===i8?null:e;n.id="IncompleteTransactionFilter",tk(n);let i=(e,t)=>{var r;return F(r=t?.originalException)&&"string"==typeof r.digest&&r.digest.startsWith("NEXT_REDIRECT;")||e.exception?.values?.[0]?.value==="NEXT_REDIRECT"?null:e};i.id="NextRedirectErrorFilter",tk(i)}({dsn:"https://<EMAIL>/4509635207692288",integrations:[new ll(O)],sendDefaultPii:!0,tracesSampleRate:1,replaysSessionSampleRate:.1,replaysOnErrorSampleRate:1,debug:!1});let lc=function(e,t){T&&T(e,t)}},25620:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=r(76073);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26393:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return i}});let n=r(66673);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=(0,n.parsePath)(e);return""+r+t+i+a}},27389:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRouterContext:function(){return i},GlobalLayoutRouterContext:function(){return o},LayoutRouterContext:function(){return a},MissingSlotContext:function(){return l},TemplateContext:function(){return s}});let n=r(51532)._(r(44109)),i=n.default.createContext(null),a=n.default.createContext(null),o=n.default.createContext(null),s=n.default.createContext(null),l=n.default.createContext(new Set)},27651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Router:function(){return a.default},createRouter:function(){return m},default:function(){return p},makePublicRouterInstance:function(){return g},useRouter:function(){return h},withRouter:function(){return l.default}});let n=r(51532),i=n._(r(44109)),a=n._(r(35507)),o=r(18987),s=n._(r(95359)),l=n._(r(54680)),u={router:null,readyCallbacks:[],ready(e){if(this.router)return e();this.readyCallbacks.push(e)}},c=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],d=["push","replace","reload","back","prefetch","beforePopState"];function f(){if(!u.router)throw Object.defineProperty(Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return u.router}Object.defineProperty(u,"events",{get:()=>a.default.events}),c.forEach(e=>{Object.defineProperty(u,e,{get:()=>f()[e]})}),d.forEach(e=>{u[e]=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return f()[e](...r)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(e=>{u.ready(()=>{a.default.events.on(e,function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let i="on"+e.charAt(0).toUpperCase()+e.substring(1);if(u[i])try{u[i](...r)}catch(e){console.error("Error when running the Router event: "+i),console.error((0,s.default)(e)?e.message+"\n"+e.stack:e+"")}})})});let p=u;function h(){let e=i.default.useContext(o.RouterContext);if(!e)throw Object.defineProperty(Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted"),"__NEXT_ERROR_CODE",{value:"E509",enumerable:!1,configurable:!0});return e}function m(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return u.router=new a.default(...t),u.readyCallbacks.forEach(e=>e()),u.readyCallbacks=[],u.router}function g(e){let t={};for(let r of c){if("object"==typeof e[r]){t[r]=Object.assign(Array.isArray(e[r])?[]:{},e[r]);continue}t[r]=e[r]}return t.events=a.default.events,d.forEach(r=>{t[r]=function(){for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return e[r](...n)}}),t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30452:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"denormalizePagePath",{enumerable:!0,get:function(){return a}});let n=r(56192),i=r(19822);function a(e){let t=(0,i.normalizePathSep)(e);return t.startsWith("/index/")&&!(0,n.isDynamicRoute)(t)?t.slice(6):"/index"!==t?t:"/"}},31949:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}});let r=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32340:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BloomFilter",{enumerable:!0,get:function(){return r}});class r{static from(e,t){void 0===t&&(t=1e-4);let n=new r(e.length,t);for(let t of e)n.add(t);return n}export(){return{numItems:this.numItems,errorRate:this.errorRate,numBits:this.numBits,numHashes:this.numHashes,bitArray:this.bitArray}}import(e){this.numItems=e.numItems,this.errorRate=e.errorRate,this.numBits=e.numBits,this.numHashes=e.numHashes,this.bitArray=e.bitArray}add(e){this.getHashValues(e).forEach(e=>{this.bitArray[e]=1})}contains(e){return this.getHashValues(e).every(e=>this.bitArray[e])}getHashValues(e){let t=[];for(let r=1;r<=this.numHashes;r++){let n=function(e){let t=0;for(let r=0;r<e.length;r++)t=Math.imul(t^e.charCodeAt(r),0x5bd1e995),t^=t>>>13,t=Math.imul(t,0x5bd1e995);return t>>>0}(""+e+r)%this.numBits;t.push(n)}return t}constructor(e,t=1e-4){this.numItems=e,this.errorRate=t,this.numBits=Math.ceil(-(e*Math.log(t))/(Math.log(2)*Math.log(2))),this.numHashes=Math.ceil(this.numBits/e*Math.log(2)),this.bitArray=Array(this.numBits).fill(0)}}},34890:(e,t)=>{"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return n},setConfig:function(){return i}});let n=()=>r;function i(e){r=e}},35507:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createKey:function(){return z},default:function(){return V},matchesMiddleware:function(){return L}});let n=r(51532),i=r(98781),a=r(36415),o=r(50817),s=r(52937),l=i._(r(95359)),u=r(30452),c=r(48319),d=n._(r(40998)),f=r(6097),p=r(61460),h=r(39106),m=n._(r(13886)),g=r(52978),_=r(91399),y=r(319);r(31949);let v=r(66673),b=r(10618),E=r(52471),S=r(69476),w=r(4091),R=r(25620),P=r(85372),T=r(91557),O=r(4184),x=r(90457),C=r(17416),I=r(8742),k=r(90020),M=r(10773),A=r(53846),N=r(90495),j=r(181);function D(){return Object.assign(Object.defineProperty(Error("Route Cancelled"),"__NEXT_ERROR_CODE",{value:"E315",enumerable:!1,configurable:!0}),{cancelled:!0})}async function L(e){let t=await Promise.resolve(e.router.pageLoader.getMiddleware());if(!t)return!1;let{pathname:r}=(0,v.parsePath)(e.asPath),n=(0,R.hasBasePath)(r)?(0,S.removeBasePath)(r):r,i=(0,w.addBasePath)((0,b.addLocale)(n,e.locale));return t.some(e=>new RegExp(e.regexp).test(i))}function U(e){let t=(0,f.getLocationOrigin)();return e.startsWith(t)?e.substring(t.length):e}function F(e,t,r){let[n,i]=(0,P.resolveHref)(e,t,!0),a=(0,f.getLocationOrigin)(),o=n.startsWith(a),s=i&&i.startsWith(a);n=U(n),i=i?U(i):i;let l=o?n:(0,w.addBasePath)(n),u=r?U((0,P.resolveHref)(e,r)):i||n;return{url:l,as:s?u:(0,w.addBasePath)(u)}}function B(e,t){let r=(0,a.removeTrailingSlash)((0,u.denormalizePagePath)(e));return"/404"===r||"/_error"===r?e:(t.includes(r)||t.some(t=>{if((0,p.isDynamicRoute)(t)&&(0,_.getRouteRegex)(t).re.test(r))return e=t,!0}),(0,a.removeTrailingSlash)(e))}async function H(e){if(!await L(e)||!e.fetchData)return null;let t=await e.fetchData(),r=await function(e,t,r){let n={basePath:r.router.basePath,i18n:{locales:r.router.locales},trailingSlash:!1},i=t.headers.get("x-nextjs-rewrite"),s=i||t.headers.get("x-nextjs-matched-path"),l=t.headers.get(j.MATCHED_PATH_HEADER);if(!l||s||l.includes("__next_data_catchall")||l.includes("/_error")||l.includes("/404")||(s=l),s){if(s.startsWith("/")){let t=(0,h.parseRelativeUrl)(s),l=(0,O.getNextPathnameInfo)(t.pathname,{nextConfig:n,parseData:!0}),u=(0,a.removeTrailingSlash)(l.pathname);return Promise.all([r.router.pageLoader.getPageList(),(0,o.getClientBuildManifest)()]).then(n=>{let[a,{__rewrites:o}]=n,s=(0,b.addLocale)(l.pathname,l.locale);if((0,p.isDynamicRoute)(s)||!i&&a.includes((0,c.normalizeLocalePath)((0,S.removeBasePath)(s),r.router.locales).pathname)){let r=(0,O.getNextPathnameInfo)((0,h.parseRelativeUrl)(e).pathname,{nextConfig:void 0,parseData:!0});t.pathname=s=(0,w.addBasePath)(r.pathname)}{let e=(0,m.default)(s,a,o,t.query,e=>B(e,a),r.router.locales);e.matchedPage&&(t.pathname=e.parsedAs.pathname,s=t.pathname,Object.assign(t.query,e.parsedAs.query))}let d=a.includes(u)?u:B((0,c.normalizeLocalePath)((0,S.removeBasePath)(t.pathname),r.router.locales).pathname,a);if((0,p.isDynamicRoute)(d)){let e=(0,g.getRouteMatcher)((0,_.getRouteRegex)(d))(s);Object.assign(t.query,e||{})}return{type:"rewrite",parsedAs:t,resolvedHref:d}})}let t=(0,v.parsePath)(e);return Promise.resolve({type:"redirect-external",destination:""+(0,x.formatNextPathnameInfo)({...(0,O.getNextPathnameInfo)(t.pathname,{nextConfig:n,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""})+t.query+t.hash})}let u=t.headers.get("x-nextjs-redirect");if(u){if(u.startsWith("/")){let e=(0,v.parsePath)(u),t=(0,x.formatNextPathnameInfo)({...(0,O.getNextPathnameInfo)(e.pathname,{nextConfig:n,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-internal",newAs:""+t+e.query+e.hash,newUrl:""+t+e.query+e.hash})}return Promise.resolve({type:"redirect-external",destination:u})}return Promise.resolve({type:"next"})}(t.dataHref,t.response,e);return{dataHref:t.dataHref,json:t.json,response:t.response,text:t.text,cacheKey:t.cacheKey,effect:r}}let $=Symbol("SSG_DATA_NOT_FOUND");function W(e){try{return JSON.parse(e)}catch(e){return null}}function q(e){let{dataHref:t,inflightCache:r,isPrefetch:n,hasMiddleware:i,isServerRender:a,parseJSON:s,persistCache:l,isBackground:u,unstable_skipClientCache:c}=e,{href:d}=new URL(t,window.location.href),f=e=>{var u;return(function e(t,r,n){return fetch(t,{credentials:"same-origin",method:n.method||"GET",headers:Object.assign({},n.headers,{"x-nextjs-data":"1"})}).then(i=>!i.ok&&r>1&&i.status>=500?e(t,r-1,n):i)})(t,a?3:1,{headers:Object.assign({},n?{purpose:"prefetch"}:{},n&&i?{"x-middleware-prefetch":"1"}:{},{}),method:null!=(u=null==e?void 0:e.method)?u:"GET"}).then(r=>r.ok&&(null==e?void 0:e.method)==="HEAD"?{dataHref:t,response:r,text:"",json:{},cacheKey:d}:r.text().then(e=>{if(!r.ok){if(i&&[301,302,307,308].includes(r.status))return{dataHref:t,response:r,text:e,json:{},cacheKey:d};if(404===r.status){var n;if(null==(n=W(e))?void 0:n.notFound)return{dataHref:t,json:{notFound:$},response:r,text:e,cacheKey:d}}let s=Object.defineProperty(Error("Failed to load static props"),"__NEXT_ERROR_CODE",{value:"E124",enumerable:!1,configurable:!0});throw a||(0,o.markAssetError)(s),s}return{dataHref:t,json:s?W(e):null,response:r,text:e,cacheKey:d}})).then(e=>(l&&"no-cache"!==e.response.headers.get("x-middleware-cache")||delete r[d],e)).catch(e=>{throw c||delete r[d],("Failed to fetch"===e.message||"NetworkError when attempting to fetch resource."===e.message||"Load failed"===e.message)&&(0,o.markAssetError)(e),e})};return c&&l?f({}).then(e=>("no-cache"!==e.response.headers.get("x-middleware-cache")&&(r[d]=Promise.resolve(e)),e)):void 0!==r[d]?r[d]:r[d]=f(u?{method:"HEAD"}:{})}function z(){return Math.random().toString(36).slice(2,10)}function X(e){let{url:t,router:r}=e;if(t===(0,w.addBasePath)((0,b.addLocale)(r.asPath,r.locale)))throw Object.defineProperty(Error("Invariant: attempted to hard navigate to the same URL "+t+" "+location.href),"__NEXT_ERROR_CODE",{value:"E282",enumerable:!1,configurable:!0});window.location.href=t}let G=e=>{let{route:t,router:r}=e,n=!1,i=r.clc=()=>{n=!0};return()=>{if(n){let e=Object.defineProperty(Error('Abort fetching component for route: "'+t+'"'),"__NEXT_ERROR_CODE",{value:"E483",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}i===r.clc&&(r.clc=null)}};class V{reload(){window.location.reload()}back(){window.history.back()}forward(){window.history.forward()}push(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=F(this,e,t),this.change("pushState",e,t,r)}replace(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=F(this,e,t),this.change("replaceState",e,t,r)}async _bfl(e,t,n,i){{if(!this._bfl_s&&!this._bfl_d){let t,a,{BloomFilter:s}=r(32340);try{({__routerFilterStatic:t,__routerFilterDynamic:a}=await (0,o.getClientBuildManifest)())}catch(t){if(console.error(t),i)return!0;return X({url:(0,w.addBasePath)((0,b.addLocale)(e,n||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}(null==t?void 0:t.numHashes)&&(this._bfl_s=new s(t.numItems,t.errorRate),this._bfl_s.import(t)),(null==a?void 0:a.numHashes)&&(this._bfl_d=new s(a.numItems,a.errorRate),this._bfl_d.import(a))}let c=!1,d=!1;for(let{as:r,allowMatchCurrent:o}of[{as:e},{as:t}])if(r){let t=(0,a.removeTrailingSlash)(new URL(r,"http://n").pathname),f=(0,w.addBasePath)((0,b.addLocale)(t,n||this.locale));if(o||t!==(0,a.removeTrailingSlash)(new URL(this.asPath,"http://n").pathname)){var s,l,u;for(let e of(c=c||!!(null==(s=this._bfl_s)?void 0:s.contains(t))||!!(null==(l=this._bfl_s)?void 0:l.contains(f)),[t,f])){let t=e.split("/");for(let e=0;!d&&e<t.length+1;e++){let r=t.slice(0,e).join("/");if(r&&(null==(u=this._bfl_d)?void 0:u.contains(r))){d=!0;break}}}if(c||d){if(i)return!0;return X({url:(0,w.addBasePath)((0,b.addLocale)(e,n||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}}}}return!1}async change(e,t,r,n,i){var u,c,d,P,T,O,x,k,N;let j,U;if(!(0,I.isLocalURL)(t))return X({url:t,router:this}),!1;let H=1===n._h;H||n.shallow||await this._bfl(r,void 0,n.locale);let W=H||n._shouldResolveHref||(0,v.parsePath)(t).pathname===(0,v.parsePath)(r).pathname,q={...this.state},z=!0!==this.isReady;this.isReady=!0;let G=this.isSsr;if(H||(this.isSsr=!1),H&&this.clc)return!1;let Y=q.locale;f.ST&&performance.mark("routeChange");let{shallow:K=!1,scroll:J=!0}=n,Q={shallow:K};this._inFlightRoute&&this.clc&&(G||V.events.emit("routeChangeError",D(),this._inFlightRoute,Q),this.clc(),this.clc=null),r=(0,w.addBasePath)((0,b.addLocale)((0,R.hasBasePath)(r)?(0,S.removeBasePath)(r):r,n.locale,this.defaultLocale));let Z=(0,E.removeLocale)((0,R.hasBasePath)(r)?(0,S.removeBasePath)(r):r,q.locale);this._inFlightRoute=r;let ee=Y!==q.locale;if(!H&&this.onlyAHashChange(Z)&&!ee){q.asPath=Z,V.events.emit("hashChangeStart",r,Q),this.changeState(e,t,r,{...n,scroll:!1}),J&&this.scrollToHash(Z);try{await this.set(q,this.components[q.route],null)}catch(e){throw(0,l.default)(e)&&e.cancelled&&V.events.emit("routeChangeError",e,Z,Q),e}return V.events.emit("hashChangeComplete",r,Q),!0}let et=(0,h.parseRelativeUrl)(t),{pathname:er,query:en}=et;try{[j,{__rewrites:U}]=await Promise.all([this.pageLoader.getPageList(),(0,o.getClientBuildManifest)(),this.pageLoader.getMiddleware()])}catch(e){return X({url:r,router:this}),!1}this.urlIsNew(Z)||ee||(e="replaceState");let ei=r;er=er?(0,a.removeTrailingSlash)((0,S.removeBasePath)(er)):er;let ea=(0,a.removeTrailingSlash)(er),eo=r.startsWith("/")&&(0,h.parseRelativeUrl)(r).pathname;if(null==(u=this.components[er])?void 0:u.__appRouter)return X({url:r,router:this}),new Promise(()=>{});let es=!!(eo&&ea!==eo&&(!(0,p.isDynamicRoute)(ea)||!(0,g.getRouteMatcher)((0,_.getRouteRegex)(ea))(eo))),el=!n.shallow&&await L({asPath:r,locale:q.locale,router:this});if(H&&el&&(W=!1),W&&"/_error"!==er)if(n._shouldResolveHref=!0,r.startsWith("/")){let e=(0,m.default)((0,w.addBasePath)((0,b.addLocale)(Z,q.locale),!0),j,U,en,e=>B(e,j),this.locales);if(e.externalDest)return X({url:r,router:this}),!0;el||(ei=e.asPath),e.matchedPage&&e.resolvedHref&&(er=e.resolvedHref,et.pathname=(0,w.addBasePath)(er),el||(t=(0,y.formatWithValidation)(et)))}else et.pathname=B(er,j),et.pathname!==er&&(er=et.pathname,et.pathname=(0,w.addBasePath)(er),el||(t=(0,y.formatWithValidation)(et)));if(!(0,I.isLocalURL)(r))return X({url:r,router:this}),!1;ei=(0,E.removeLocale)((0,S.removeBasePath)(ei),q.locale),ea=(0,a.removeTrailingSlash)(er);let eu=!1;if((0,p.isDynamicRoute)(ea)){let e=(0,h.parseRelativeUrl)(ei),n=e.pathname,i=(0,_.getRouteRegex)(ea);eu=(0,g.getRouteMatcher)(i)(n);let a=ea===n,o=a?(0,A.interpolateAs)(ea,n,en):{};if(eu&&(!a||o.result))a?r=(0,y.formatWithValidation)(Object.assign({},e,{pathname:o.result,query:(0,M.omit)(en,o.params)})):Object.assign(en,eu);else{let e=Object.keys(i.groups).filter(e=>!en[e]&&!i.groups[e].optional);if(e.length>0&&!el)throw Object.defineProperty(Error((a?"The provided `href` ("+t+") value is missing query values ("+e.join(", ")+") to be interpolated properly. ":"The provided `as` value ("+n+") is incompatible with the `href` value ("+ea+"). ")+"Read more: https://nextjs.org/docs/messages/"+(a?"href-interpolation-failed":"incompatible-href-as")),"__NEXT_ERROR_CODE",{value:"E344",enumerable:!1,configurable:!0})}}H||V.events.emit("routeChangeStart",r,Q);let ec="/404"===this.pathname||"/_error"===this.pathname;try{let a=await this.getRouteInfo({route:ea,pathname:er,query:en,as:r,resolvedAs:ei,routeProps:Q,locale:q.locale,isPreview:q.isPreview,hasMiddleware:el,unstable_skipClientCache:n.unstable_skipClientCache,isQueryUpdating:H&&!this.isFallback,isMiddlewareRewrite:es});if(H||n.shallow||await this._bfl(r,"resolvedAs"in a?a.resolvedAs:void 0,q.locale),"route"in a&&el){ea=er=a.route||ea,Q.shallow||(en=Object.assign({},a.query||{},en));let e=(0,R.hasBasePath)(et.pathname)?(0,S.removeBasePath)(et.pathname):et.pathname;if(eu&&er!==e&&Object.keys(eu).forEach(e=>{eu&&en[e]===eu[e]&&delete en[e]}),(0,p.isDynamicRoute)(er)){let e=!Q.shallow&&a.resolvedAs?a.resolvedAs:(0,w.addBasePath)((0,b.addLocale)(new URL(r,location.href).pathname,q.locale),!0);(0,R.hasBasePath)(e)&&(e=(0,S.removeBasePath)(e));let t=(0,_.getRouteRegex)(er),n=(0,g.getRouteMatcher)(t)(new URL(e,location.href).pathname);n&&Object.assign(en,n)}}if("type"in a)if("redirect-internal"===a.type)return this.change(e,a.newUrl,a.newAs,n);else return X({url:a.destination,router:this}),new Promise(()=>{});let o=a.Component;if(o&&o.unstable_scriptLoader&&[].concat(o.unstable_scriptLoader()).forEach(e=>{(0,s.handleClientScriptLoad)(e.props)}),(a.__N_SSG||a.__N_SSP)&&a.props){if(a.props.pageProps&&a.props.pageProps.__N_REDIRECT){n.locale=!1;let t=a.props.pageProps.__N_REDIRECT;if(t.startsWith("/")&&!1!==a.props.pageProps.__N_REDIRECT_BASE_PATH){let r=(0,h.parseRelativeUrl)(t);r.pathname=B(r.pathname,j);let{url:i,as:a}=F(this,t,t);return this.change(e,i,a,n)}return X({url:t,router:this}),new Promise(()=>{})}if(q.isPreview=!!a.props.__N_PREVIEW,a.props.notFound===$){let e;try{await this.fetchComponent("/404"),e="/404"}catch(t){e="/_error"}if(a=await this.getRouteInfo({route:e,pathname:e,query:en,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:q.locale,isPreview:q.isPreview,isNotFound:!0}),"type"in a)throw Object.defineProperty(Error("Unexpected middleware effect on /404"),"__NEXT_ERROR_CODE",{value:"E158",enumerable:!1,configurable:!0})}}H&&"/_error"===this.pathname&&(null==(d=self.__NEXT_DATA__.props)||null==(c=d.pageProps)?void 0:c.statusCode)===500&&(null==(P=a.props)?void 0:P.pageProps)&&(a.props.pageProps.statusCode=500);let u=n.shallow&&q.route===(null!=(T=a.route)?T:ea),f=null!=(O=n.scroll)?O:!H&&!u,m=null!=i?i:f?{x:0,y:0}:null,y={...q,route:ea,pathname:er,query:en,asPath:Z,isFallback:!1};if(H&&ec){if(a=await this.getRouteInfo({route:this.pathname,pathname:this.pathname,query:en,as:r,resolvedAs:ei,routeProps:{shallow:!1},locale:q.locale,isPreview:q.isPreview,isQueryUpdating:H&&!this.isFallback}),"type"in a)throw Object.defineProperty(Error("Unexpected middleware effect on "+this.pathname),"__NEXT_ERROR_CODE",{value:"E225",enumerable:!1,configurable:!0});"/_error"===this.pathname&&(null==(k=self.__NEXT_DATA__.props)||null==(x=k.pageProps)?void 0:x.statusCode)===500&&(null==(N=a.props)?void 0:N.pageProps)&&(a.props.pageProps.statusCode=500);try{await this.set(y,a,m)}catch(e){throw(0,l.default)(e)&&e.cancelled&&V.events.emit("routeChangeError",e,Z,Q),e}return!0}if(V.events.emit("beforeHistoryChange",r,Q),this.changeState(e,t,r,n),!(H&&!m&&!z&&!ee&&(0,C.compareRouterStates)(y,this.state))){try{await this.set(y,a,m)}catch(e){if(e.cancelled)a.error=a.error||e;else throw e}if(a.error)throw H||V.events.emit("routeChangeError",a.error,Z,Q),a.error;H||V.events.emit("routeChangeComplete",r,Q),f&&/#.+$/.test(r)&&this.scrollToHash(r)}return!0}catch(e){if((0,l.default)(e)&&e.cancelled)return!1;throw e}}changeState(e,t,r,n){void 0===n&&(n={}),("pushState"!==e||(0,f.getURL)()!==r)&&(this._shallow=n.shallow,window.history[e]({url:t,as:r,options:n,__N:!0,key:this._key="pushState"!==e?this._key:z()},"",r))}async handleRouteInfoError(e,t,r,n,i,a){if(e.cancelled)throw e;if((0,o.isAssetError)(e)||a)throw V.events.emit("routeChangeError",e,n,i),X({url:n,router:this}),D();console.error(e);try{let n,{page:i,styleSheets:a}=await this.fetchComponent("/_error"),o={props:n,Component:i,styleSheets:a,err:e,error:e};if(!o.props)try{o.props=await this.getInitialProps(i,{err:e,pathname:t,query:r})}catch(e){console.error("Error in error page `getInitialProps`: ",e),o.props={}}return o}catch(e){return this.handleRouteInfoError((0,l.default)(e)?e:Object.defineProperty(Error(e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}),t,r,n,i,!0)}}async getRouteInfo(e){let{route:t,pathname:r,query:n,as:i,resolvedAs:o,routeProps:s,locale:u,hasMiddleware:d,isPreview:f,unstable_skipClientCache:p,isQueryUpdating:h,isMiddlewareRewrite:m,isNotFound:g}=e,_=t;try{var v,b,E,w;let e=this.components[_];if(s.shallow&&e&&this.route===_)return e;let t=G({route:_,router:this});d&&(e=void 0);let l=!e||"initial"in e?void 0:e,R={dataHref:this.pageLoader.getDataHref({href:(0,y.formatWithValidation)({pathname:r,query:n}),skipInterpolation:!0,asPath:g?"/404":o,locale:u}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:h?this.sbc:this.sdc,persistCache:!f,isPrefetch:!1,unstable_skipClientCache:p,isBackground:h},P=h&&!m?null:await H({fetchData:()=>q(R),asPath:g?"/404":o,locale:u,router:this}).catch(e=>{if(h)return null;throw e});if(P&&("/_error"===r||"/404"===r)&&(P.effect=void 0),h&&(P?P.json=self.__NEXT_DATA__.props:P={json:self.__NEXT_DATA__.props}),t(),(null==P||null==(v=P.effect)?void 0:v.type)==="redirect-internal"||(null==P||null==(b=P.effect)?void 0:b.type)==="redirect-external")return P.effect;if((null==P||null==(E=P.effect)?void 0:E.type)==="rewrite"){let t=(0,a.removeTrailingSlash)(P.effect.resolvedHref),i=await this.pageLoader.getPageList();if((!h||i.includes(t))&&(_=t,r=P.effect.resolvedHref,n={...n,...P.effect.parsedAs.query},o=(0,S.removeBasePath)((0,c.normalizeLocalePath)(P.effect.parsedAs.pathname,this.locales).pathname),e=this.components[_],s.shallow&&e&&this.route===_&&!d))return{...e,route:_}}if((0,T.isAPIRoute)(_))return X({url:i,router:this}),new Promise(()=>{});let O=l||await this.fetchComponent(_).then(e=>({Component:e.page,styleSheets:e.styleSheets,__N_SSG:e.mod.__N_SSG,__N_SSP:e.mod.__N_SSP})),x=null==P||null==(w=P.response)?void 0:w.headers.get("x-middleware-skip"),C=O.__N_SSG||O.__N_SSP;x&&(null==P?void 0:P.dataHref)&&delete this.sdc[P.dataHref];let{props:I,cacheKey:k}=await this._getData(async()=>{if(C){if((null==P?void 0:P.json)&&!x)return{cacheKey:P.cacheKey,props:P.json};let e=(null==P?void 0:P.dataHref)?P.dataHref:this.pageLoader.getDataHref({href:(0,y.formatWithValidation)({pathname:r,query:n}),asPath:o,locale:u}),t=await q({dataHref:e,isServerRender:this.isSsr,parseJSON:!0,inflightCache:x?{}:this.sdc,persistCache:!f,isPrefetch:!1,unstable_skipClientCache:p});return{cacheKey:t.cacheKey,props:t.json||{}}}return{headers:{},props:await this.getInitialProps(O.Component,{pathname:r,query:n,asPath:i,locale:u,locales:this.locales,defaultLocale:this.defaultLocale})}});return O.__N_SSP&&R.dataHref&&k&&delete this.sdc[k],this.isPreview||!O.__N_SSG||h||q(Object.assign({},R,{isBackground:!0,persistCache:!1,inflightCache:this.sbc})).catch(()=>{}),I.pageProps=Object.assign({},I.pageProps),O.props=I,O.route=_,O.query=n,O.resolvedAs=o,this.components[_]=O,O}catch(e){return this.handleRouteInfoError((0,l.getProperError)(e),r,n,i,s)}}set(e,t,r){return this.state=e,this.sub(t,this.components["/_app"].Component,r)}beforePopState(e){this._bps=e}onlyAHashChange(e){if(!this.asPath)return!1;let[t,r]=this.asPath.split("#",2),[n,i]=e.split("#",2);return!!i&&t===n&&r===i||t===n&&r!==i}scrollToHash(e){let[,t=""]=e.split("#",2);(0,N.handleSmoothScroll)(()=>{if(""===t||"top"===t)return void window.scrollTo(0,0);let e=decodeURIComponent(t),r=document.getElementById(e);if(r)return void r.scrollIntoView();let n=document.getElementsByName(e)[0];n&&n.scrollIntoView()},{onlyHashChange:this.onlyAHashChange(e)})}urlIsNew(e){return this.asPath!==e}async prefetch(e,t,r){if(void 0===t&&(t=e),void 0===r&&(r={}),(0,k.isBot)(window.navigator.userAgent))return;let n=(0,h.parseRelativeUrl)(e),i=n.pathname,{pathname:s,query:l}=n,u=s,c=await this.pageLoader.getPageList(),d=t,f=void 0!==r.locale?r.locale||void 0:this.locale,R=await L({asPath:t,locale:f,router:this});if(t.startsWith("/")){let r;({__rewrites:r}=await (0,o.getClientBuildManifest)());let i=(0,m.default)((0,w.addBasePath)((0,b.addLocale)(t,this.locale),!0),c,r,n.query,e=>B(e,c),this.locales);if(i.externalDest)return;R||(d=(0,E.removeLocale)((0,S.removeBasePath)(i.asPath),this.locale)),i.matchedPage&&i.resolvedHref&&(n.pathname=s=i.resolvedHref,R||(e=(0,y.formatWithValidation)(n)))}n.pathname=B(n.pathname,c),(0,p.isDynamicRoute)(n.pathname)&&(s=n.pathname,n.pathname=s,Object.assign(l,(0,g.getRouteMatcher)((0,_.getRouteRegex)(n.pathname))((0,v.parsePath)(t).pathname)||{}),R||(e=(0,y.formatWithValidation)(n)));let P=await H({fetchData:()=>q({dataHref:this.pageLoader.getDataHref({href:(0,y.formatWithValidation)({pathname:u,query:l}),skipInterpolation:!0,asPath:d,locale:f}),hasMiddleware:!0,isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0}),asPath:t,locale:f,router:this});if((null==P?void 0:P.effect.type)==="rewrite"&&(n.pathname=P.effect.resolvedHref,s=P.effect.resolvedHref,l={...l,...P.effect.parsedAs.query},d=P.effect.parsedAs.pathname,e=(0,y.formatWithValidation)(n)),(null==P?void 0:P.effect.type)==="redirect-external")return;let T=(0,a.removeTrailingSlash)(s);await this._bfl(t,d,r.locale,!0)&&(this.components[i]={__appRouter:!0}),await Promise.all([this.pageLoader._isSsg(T).then(t=>!!t&&q({dataHref:(null==P?void 0:P.json)?null==P?void 0:P.dataHref:this.pageLoader.getDataHref({href:e,asPath:d,locale:f}),isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0,unstable_skipClientCache:r.unstable_skipClientCache||r.priority&&!0}).then(()=>!1).catch(()=>!1)),this.pageLoader[r.priority?"loadPage":"prefetch"](T)])}async fetchComponent(e){let t=G({route:e,router:this});try{let r=await this.pageLoader.loadPage(e);return t(),r}catch(e){throw t(),e}}_getData(e){let t=!1,r=()=>{t=!0};return this.clc=r,e().then(e=>{if(r===this.clc&&(this.clc=null),t){let e=Object.defineProperty(Error("Loading initial props cancelled"),"__NEXT_ERROR_CODE",{value:"E405",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}return e})}getInitialProps(e,t){let{Component:r}=this.components["/_app"],n=this._wrapApp(r);return t.AppTree=n,(0,f.loadGetInitialProps)(r,{AppTree:n,Component:e,router:this,ctx:t})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(e,t,r,{initialProps:n,pageLoader:i,App:o,wrapApp:s,Component:l,err:u,subscription:c,isFallback:d,locale:m,locales:g,defaultLocale:_,domainLocales:v,isPreview:b}){this.sdc={},this.sbc={},this.isFirstPopStateEvent=!0,this._key=z(),this.onPopState=e=>{let t,{isFirstPopStateEvent:r}=this;this.isFirstPopStateEvent=!1;let n=e.state;if(!n){let{pathname:e,query:t}=this;this.changeState("replaceState",(0,y.formatWithValidation)({pathname:(0,w.addBasePath)(e),query:t}),(0,f.getURL)());return}if(n.__NA)return void window.location.reload();if(!n.__N||r&&this.locale===n.options.locale&&n.as===this.asPath)return;let{url:i,as:a,options:o,key:s}=n;this._key=s;let{pathname:l}=(0,h.parseRelativeUrl)(i);(!this.isSsr||a!==(0,w.addBasePath)(this.asPath)||l!==(0,w.addBasePath)(this.pathname))&&(!this._bps||this._bps(n))&&this.change("replaceState",i,a,Object.assign({},o,{shallow:o.shallow&&this._shallow,locale:o.locale||this.defaultLocale,_h:0}),t)};let E=(0,a.removeTrailingSlash)(e);this.components={},"/_error"!==e&&(this.components[E]={Component:l,initial:!0,props:n,err:u,__N_SSG:n&&n.__N_SSG,__N_SSP:n&&n.__N_SSP}),this.components["/_app"]={Component:o,styleSheets:[]},this.events=V.events,this.pageLoader=i;let S=(0,p.isDynamicRoute)(e)&&self.__NEXT_DATA__.autoExport;if(this.basePath="",this.sub=c,this.clc=null,this._wrapApp=s,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.isExperimentalCompile||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!S&&!self.location.search&&0),this.state={route:E,pathname:e,query:t,asPath:S?e:r,isPreview:!!b,locale:void 0,isFallback:d},this._initialMatchesMiddlewarePromise=Promise.resolve(!1),!r.startsWith("//")){let n={locale:m},i=(0,f.getURL)();this._initialMatchesMiddlewarePromise=L({router:this,locale:m,asPath:i}).then(a=>(n._shouldResolveHref=r!==e,this.changeState("replaceState",a?i:(0,y.formatWithValidation)({pathname:(0,w.addBasePath)(e),query:t}),i,n),a))}window.addEventListener("popstate",this.onPopState)}}V.events=(0,d.default)()},36415:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},36806:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=""),("/"===e?"/index":/^\/index(\/|$)/.test(e)?"/index"+e:e)+t}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},37110:(e,t,r)=>{e.exports=r(27651)},38500:(e,t)=>{"use strict";function r(){return""}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDeploymentIdQueryOrEmptyString",{enumerable:!0,get:function(){return r}})},39106:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return a}});let n=r(6097),i=r(5257);function a(e,t,r){void 0===r&&(r=!0);let a=new URL((0,n.getLocationOrigin)()),o=t?new URL(t,a):e.startsWith(".")?new URL(window.location.href):a,{pathname:s,searchParams:l,search:u,hash:c,href:d,origin:f}=new URL(e,o);if(f!==a.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:r?(0,i.searchParamsToUrlQuery)(l):void 0,search:u,hash:c,href:d.slice(f.length)}}},39772:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(46700),i=r(21196);function a(e){return(0,i.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},40403:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return f}});let n=r(51532),i=r(4091),a=r(53846),o=n._(r(36806)),s=r(10618),l=r(61460),u=r(39106),c=r(36415),d=r(50817);r(77717);class f{getPageList(){return(0,d.getClientBuildManifest)().then(e=>e.sortedPages)}getMiddleware(){return window.__MIDDLEWARE_MATCHERS=[{regexp:"^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\.json)?[\\/#\\?]?$",originalSource:"/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"},{regexp:"^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$",originalSource:"/(api|trpc)(.*)"}],window.__MIDDLEWARE_MATCHERS}getDataHref(e){let{asPath:t,href:r,locale:n}=e,{pathname:d,query:f,search:p}=(0,u.parseRelativeUrl)(r),{pathname:h}=(0,u.parseRelativeUrl)(t),m=(0,c.removeTrailingSlash)(d);if("/"!==m[0])throw Object.defineProperty(Error('Route name should start with a "/", got "'+m+'"'),"__NEXT_ERROR_CODE",{value:"E303",enumerable:!1,configurable:!0});var g=e.skipInterpolation?h:(0,l.isDynamicRoute)(m)?(0,a.interpolateAs)(d,h,f).result:m;let _=(0,o.default)((0,c.removeTrailingSlash)((0,s.addLocale)(g,n)),".json");return(0,i.addBasePath)("/_next/data/"+this.buildId+_+p,!0)}_isSsg(e){return this.promisedSsgManifest.then(t=>t.has(e))}loadPage(e){return this.routeLoader.loadRoute(e).then(e=>{if("component"in e)return{page:e.component,mod:e.exports,styleSheets:e.styles.map(e=>({href:e.href,text:e.content}))};throw e.error})}prefetch(e){return this.routeLoader.prefetch(e)}constructor(e,t){this.routeLoader=(0,d.createRouteLoader)(t),this.buildId=e,this.assetPrefix=t,this.promisedSsgManifest=new Promise(e=>{window.__SSG_MANIFEST?e(window.__SSG_MANIFEST):window.__SSG_MANIFEST_CB=()=>{e(window.__SSG_MANIFEST)}})}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},40602:(e,t)=>{"use strict";function r(e){return new URL(e,"http://n").searchParams}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"asPathToSearchParams",{enumerable:!0,get:function(){return r}})},40859:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(5257),i=r(39106);function a(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},40998:(e,t)=>{"use strict";function r(){let e=Object.create(null);return{on(t,r){(e[t]||(e[t]=[])).push(r)},off(t,r){e[t]&&e[t].splice(e[t].indexOf(r)>>>0,1)},emit(t){for(var r=arguments.length,n=Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];(e[t]||[]).slice().map(e=>{e(...n)})}}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},41706:(e,t,r)=>{"use strict";let n,i,a,o,s,l,u,c,d,f,p,h;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{emitter:function(){return H},hydrate:function(){return es},initialize:function(){return z},router:function(){return n},version:function(){return B}});let m=r(51532),g=r(88017);r(61866);let _=m._(r(44109)),y=m._(r(49282)),v=r(74364),b=m._(r(40998)),E=r(18987),S=r(90495),w=r(61460),R=r(5257),P=r(34890),T=r(6097),O=r(45719),x=m._(r(59346)),C=m._(r(40403)),I=r(46041),k=r(27651),M=r(95359),A=r(77958),N=r(69476),j=r(25620),D=r(27389),L=r(54906),U=r(70344),F=r(78212);r(51856),r(39772);let B="15.3.2",H=(0,b.default)(),$=e=>[].slice.call(e),W=!1;class q extends _.default.Component{componentDidCatch(e,t){this.props.fn(e,t)}componentDidMount(){this.scrollToHash(),n.isSsr&&(i.isFallback||i.nextExport&&((0,w.isDynamicRoute)(n.pathname)||location.search||1)||i.props&&i.props.__N_SSG&&(location.search||1))&&n.replace(n.pathname+"?"+String((0,R.assign)((0,R.urlQueryToSearchParams)(n.query),new URLSearchParams(location.search))),a,{_h:1,shallow:!i.isFallback&&!W}).catch(e=>{if(!e.cancelled)throw e})}componentDidUpdate(){this.scrollToHash()}scrollToHash(){let{hash:e}=location;if(!(e=e&&e.substring(1)))return;let t=document.getElementById(e);t&&setTimeout(()=>t.scrollIntoView(),0)}render(){return this.props.children}}async function z(e){void 0===e&&(e={}),i=JSON.parse(document.getElementById("__NEXT_DATA__").textContent),window.__NEXT_DATA__=i,h=i.defaultLocale;let t=i.assetPrefix||"";if(self.__next_set_public_path__(""+t+"/_next/"),(0,P.setConfig)({serverRuntimeConfig:{},publicRuntimeConfig:i.runtimeConfig||{}}),a=(0,T.getURL)(),(0,j.hasBasePath)(a)&&(a=(0,N.removeBasePath)(a)),i.scriptLoader){let{initScriptLoader:e}=r(52937);e(i.scriptLoader)}o=new C.default(i.buildId,t);let u=e=>{let[t,r]=e;return o.routeLoader.onEntrypoint(t,r)};return window.__NEXT_P&&window.__NEXT_P.map(e=>setTimeout(()=>u(e),0)),window.__NEXT_P=[],window.__NEXT_P.push=u,(l=(0,x.default)()).getIsSsr=()=>n.isSsr,s=document.getElementById("__next"),{assetPrefix:t}}function X(e,t){return(0,g.jsx)(e,{...t})}function G(e){var t;let{children:r}=e,i=_.default.useMemo(()=>(0,L.adaptForAppRouterInstance)(n),[]);return(0,g.jsx)(q,{fn:e=>Y({App:d,err:e}).catch(e=>console.error("Error rendering page: ",e)),children:(0,g.jsx)(D.AppRouterContext.Provider,{value:i,children:(0,g.jsx)(U.SearchParamsContext.Provider,{value:(0,L.adaptForSearchParams)(n),children:(0,g.jsx)(L.PathnameContextProviderAdapter,{router:n,isAutoExport:null!=(t=self.__NEXT_DATA__.autoExport)&&t,children:(0,g.jsx)(U.PathParamsContext.Provider,{value:(0,L.adaptForPathParams)(n),children:(0,g.jsx)(E.RouterContext.Provider,{value:(0,k.makePublicRouterInstance)(n),children:(0,g.jsx)(v.HeadManagerContext.Provider,{value:l,children:(0,g.jsx)(A.ImageConfigContext.Provider,{value:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1},children:r})})})})})})})})}let V=e=>t=>{let r={...t,Component:p,err:i.err,router:n};return(0,g.jsx)(G,{children:X(e,r)})};function Y(e){let{App:t,err:s}=e;return console.error(s),console.error("A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred"),o.loadPage("/_error").then(n=>{let{page:i,styleSheets:a}=n;return(null==u?void 0:u.Component)===i?r.e(2022).then(r.t.bind(r,2022,23)).then(n=>r.e(1067).then(r.t.bind(r,61067,23)).then(r=>(e.App=t=r.default,n))).then(e=>({ErrorComponent:e.default,styleSheets:[]})):{ErrorComponent:i,styleSheets:a}}).then(r=>{var o;let{ErrorComponent:l,styleSheets:u}=r,c=V(t),d={Component:l,AppTree:c,router:n,ctx:{err:s,pathname:i.page,query:i.query,asPath:a,AppTree:c}};return Promise.resolve((null==(o=e.props)?void 0:o.err)?e.props:(0,T.loadGetInitialProps)(t,d)).then(t=>ea({...e,err:s,Component:l,styleSheets:u,props:t}))})}function K(e){let{callback:t}=e;return _.default.useLayoutEffect(()=>t(),[t]),null}let J={navigationStart:"navigationStart",beforeRender:"beforeRender",afterRender:"afterRender",afterHydrate:"afterHydrate",routeChange:"routeChange"},Q={hydration:"Next.js-hydration",beforeHydration:"Next.js-before-hydration",routeChangeToRender:"Next.js-route-change-to-render",render:"Next.js-render"},Z=null,ee=!0;function et(){[J.beforeRender,J.afterHydrate,J.afterRender,J.routeChange].forEach(e=>performance.clearMarks(e))}function er(){T.ST&&(performance.mark(J.afterHydrate),performance.getEntriesByName(J.beforeRender,"mark").length&&(performance.measure(Q.beforeHydration,J.navigationStart,J.beforeRender),performance.measure(Q.hydration,J.beforeRender,J.afterHydrate)),f&&performance.getEntriesByName(Q.hydration).forEach(f),et())}function en(){if(!T.ST)return;performance.mark(J.afterRender);let e=performance.getEntriesByName(J.routeChange,"mark");e.length&&(performance.getEntriesByName(J.beforeRender,"mark").length&&(performance.measure(Q.routeChangeToRender,e[0].name,J.beforeRender),performance.measure(Q.render,J.beforeRender,J.afterRender),f&&(performance.getEntriesByName(Q.render).forEach(f),performance.getEntriesByName(Q.routeChangeToRender).forEach(f))),et(),[Q.routeChangeToRender,Q.render].forEach(e=>performance.clearMeasures(e)))}function ei(e){let{callbacks:t,children:r}=e;return _.default.useLayoutEffect(()=>t.forEach(e=>e()),[t]),r}function ea(e){let t,r,{App:i,Component:a,props:o,err:l}=e,d="initial"in e?void 0:e.styleSheets;a=a||u.Component;let f={...o=o||u.props,Component:a,err:l,router:n};u=f;let p=!1,h=new Promise((e,t)=>{c&&c(),r=()=>{c=null,e()},c=()=>{p=!0,c=null;let e=Object.defineProperty(Error("Cancel rendering route"),"__NEXT_ERROR_CODE",{value:"E503",enumerable:!1,configurable:!0});e.cancelled=!0,t(e)}});function m(){r()}!function(){if(!d)return;let e=new Set($(document.querySelectorAll("style[data-n-href]")).map(e=>e.getAttribute("data-n-href"))),t=document.querySelector("noscript[data-n-css]"),r=null==t?void 0:t.getAttribute("data-n-css");d.forEach(t=>{let{href:n,text:i}=t;if(!e.has(n)){let e=document.createElement("style");e.setAttribute("data-n-href",n),e.setAttribute("media","x"),r&&e.setAttribute("nonce",r),document.head.appendChild(e),e.appendChild(document.createTextNode(i))}})}();let v=(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(K,{callback:function(){if(d&&!p){let e=new Set(d.map(e=>e.href)),t=$(document.querySelectorAll("style[data-n-href]")),r=t.map(e=>e.getAttribute("data-n-href"));for(let n=0;n<r.length;++n)e.has(r[n])?t[n].removeAttribute("media"):t[n].setAttribute("media","x");let n=document.querySelector("noscript[data-n-css]");n&&d.forEach(e=>{let{href:t}=e,r=document.querySelector('style[data-n-href="'+t+'"]');r&&(n.parentNode.insertBefore(r,n.nextSibling),n=r)}),$(document.querySelectorAll("link[data-n-p]")).forEach(e=>{e.parentNode.removeChild(e)})}if(e.scroll){let{x:t,y:r}=e.scroll;(0,S.handleSmoothScroll)(()=>{window.scrollTo(t,r)})}}}),(0,g.jsxs)(G,{children:[X(i,f),(0,g.jsx)(O.Portal,{type:"next-route-announcer",children:(0,g.jsx)(I.RouteAnnouncer,{})})]})]});var b=s;T.ST&&performance.mark(J.beforeRender);let E=(t=ee?er:en,(0,g.jsx)(ei,{callbacks:[t,m],children:v}));return Z?(0,_.default.startTransition)(()=>{Z.render(E)}):(Z=y.default.hydrateRoot(b,E,{onRecoverableError:F.onRecoverableError}),ee=!1),h}async function eo(e){if(e.err&&(void 0===e.Component||!e.isHydratePass))return void await Y(e);try{await ea(e)}catch(r){let t=(0,M.getProperError)(r);if(t.cancelled)throw t;await Y({...e,err:t})}}async function es(e){let t=i.err;try{let e=await o.routeLoader.whenEntrypoint("/_app");if("error"in e)throw e.error;let{component:t,exports:r}=e;d=t,r&&r.reportWebVitals&&(f=e=>{let t,{id:n,name:i,startTime:a,value:o,duration:s,entryType:l,entries:u,attribution:c}=e,d=Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12);u&&u.length&&(t=u[0].startTime);let f={id:n||d,name:i,startTime:a||t,value:null==o?s:o,label:"mark"===l||"measure"===l?"custom":"web-vital"};c&&(f.attribution=c),r.reportWebVitals(f)});let n=await o.routeLoader.whenEntrypoint(i.page);if("error"in n)throw n.error;p=n.component}catch(e){t=(0,M.getProperError)(e)}window.__NEXT_PRELOADREADY&&await window.__NEXT_PRELOADREADY(i.dynamicIds),n=(0,k.createRouter)(i.page,i.query,a,{initialProps:i.props,pageLoader:o,App:d,Component:p,wrapApp:V,err:t,isFallback:!!i.isFallback,subscription:(e,t,r)=>eo(Object.assign({},e,{App:t,scroll:r})),locale:i.locale,locales:i.locales,defaultLocale:h,domainLocales:i.domainLocales,isPreview:i.isPreview}),W=await n._initialMatchesMiddlewarePromise;let r={App:d,initial:!0,Component:p,props:i.props,err:t,isHydratePass:!0};(null==e?void 0:e.beforeRender)&&await e.beforeRender(),eo(r)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42651:e=>{"use strict";e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},45719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Portal",{enumerable:!0,get:function(){return a}});let n=r(44109),i=r(25775),a=e=>{let{children:t,type:r}=e,[a,o]=(0,n.useState)(null);return(0,n.useEffect)(()=>{let e=document.createElement(r);return document.body.appendChild(e),o(e),()=>{document.body.removeChild(e)}},[r]),a?(0,i.createPortal)(t,a):null};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46041:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RouteAnnouncer:function(){return l},default:function(){return u}});let n=r(51532),i=r(88017),a=n._(r(44109)),o=r(27651),s={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",top:0,width:"1px",whiteSpace:"nowrap",wordWrap:"normal"},l=()=>{let{asPath:e}=(0,o.useRouter)(),[t,r]=a.default.useState(""),n=a.default.useRef(e);return a.default.useEffect(()=>{if(n.current!==e)if(n.current=e,document.title)r(document.title);else{var t;let n=document.querySelector("h1");r((null!=(t=null==n?void 0:n.innerText)?t:null==n?void 0:n.textContent)||e)}},[e]),(0,i.jsx)("p",{"aria-live":"assertive",id:"__next-route-announcer__",role:"alert",style:s,children:t})},u=l;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46700:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return i},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return o},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),i="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}function o(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47716:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reportGlobalError",{enumerable:!0,get:function(){return r}});let r="function"==typeof reportError?reportError:e=>{globalThis.console.error(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48319:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return n}});let r=new WeakMap;function n(e,t){let n;if(!t)return{pathname:e};let i=r.get(t);i||(i=t.map(e=>e.toLowerCase()),r.set(t,i));let a=e.split("/",2);if(!a[1])return{pathname:e};let o=a[1].toLowerCase(),s=i.indexOf(o);return s<0?{pathname:e}:(n=t[s],{pathname:e=e.slice(n.length+1)||"/",detectedLocale:n})}},50817:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createRouteLoader:function(){return g},getClientBuildManifest:function(){return h},isAssetError:function(){return c},markAssetError:function(){return u}}),r(51532),r(36806);let n=r(87897),i=r(16288),a=r(38500),o=r(78807);function s(e,t,r){let n,i=t.get(e);if(i)return"future"in i?i.future:Promise.resolve(i);let a=new Promise(e=>{n=e});return t.set(e,{resolve:n,future:a}),r?r().then(e=>(n(e),e)).catch(r=>{throw t.delete(e),r}):a}let l=Symbol("ASSET_LOAD_ERROR");function u(e){return Object.defineProperty(e,l,{})}function c(e){return e&&l in e}let d=function(e){try{return e=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||e.relList.supports("prefetch")}catch(e){return!1}}(),f=()=>(0,a.getDeploymentIdQueryOrEmptyString)();function p(e,t,r){return new Promise((n,a)=>{let o=!1;e.then(e=>{o=!0,n(e)}).catch(a),(0,i.requestIdleCallback)(()=>setTimeout(()=>{o||a(r)},t))})}function h(){return self.__BUILD_MANIFEST?Promise.resolve(self.__BUILD_MANIFEST):p(new Promise(e=>{let t=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{e(self.__BUILD_MANIFEST),t&&t()}}),3800,u(Object.defineProperty(Error("Failed to load client build manifest"),"__NEXT_ERROR_CODE",{value:"E273",enumerable:!1,configurable:!0})))}function m(e,t){return h().then(r=>{if(!(t in r))throw u(Object.defineProperty(Error("Failed to lookup route: "+t),"__NEXT_ERROR_CODE",{value:"E446",enumerable:!1,configurable:!0}));let i=r[t].map(t=>e+"/_next/"+(0,o.encodeURIPath)(t));return{scripts:i.filter(e=>e.endsWith(".js")).map(e=>(0,n.__unsafeCreateTrustedScriptURL)(e)+f()),css:i.filter(e=>e.endsWith(".css")).map(e=>e+f())}})}function g(e){let t=new Map,r=new Map,n=new Map,a=new Map;function o(e){{var t;let n=r.get(e.toString());return n?n:document.querySelector('script[src^="'+e+'"]')?Promise.resolve():(r.set(e.toString(),n=new Promise((r,n)=>{(t=document.createElement("script")).onload=r,t.onerror=()=>n(u(Object.defineProperty(Error("Failed to load script: "+e),"__NEXT_ERROR_CODE",{value:"E74",enumerable:!1,configurable:!0}))),t.crossOrigin=void 0,t.src=e,document.body.appendChild(t)})),n)}}function l(e){let t=n.get(e);return t||n.set(e,t=fetch(e,{credentials:"same-origin"}).then(t=>{if(!t.ok)throw Object.defineProperty(Error("Failed to load stylesheet: "+e),"__NEXT_ERROR_CODE",{value:"E189",enumerable:!1,configurable:!0});return t.text().then(t=>({href:e,content:t}))}).catch(e=>{throw u(e)})),t}return{whenEntrypoint:e=>s(e,t),onEntrypoint(e,r){(r?Promise.resolve().then(()=>r()).then(e=>({component:e&&e.default||e,exports:e}),e=>({error:e})):Promise.resolve(void 0)).then(r=>{let n=t.get(e);n&&"resolve"in n?r&&(t.set(e,r),n.resolve(r)):(r?t.set(e,r):t.delete(e),a.delete(e))})},loadRoute(r,n){return s(r,a,()=>{let i;return p(m(e,r).then(e=>{let{scripts:n,css:i}=e;return Promise.all([t.has(r)?[]:Promise.all(n.map(o)),Promise.all(i.map(l))])}).then(e=>this.whenEntrypoint(r).then(t=>({entrypoint:t,styles:e[1]}))),3800,u(Object.defineProperty(Error("Route did not complete loading: "+r),"__NEXT_ERROR_CODE",{value:"E12",enumerable:!1,configurable:!0}))).then(e=>{let{entrypoint:t,styles:r}=e,n=Object.assign({styles:r},t);return"error"in t?t:n}).catch(e=>{if(n)throw e;return{error:e}}).finally(()=>null==i?void 0:i())})},prefetch(t){let r;return(r=navigator.connection)&&(r.saveData||/2g/.test(r.effectiveType))?Promise.resolve():m(e,t).then(e=>Promise.all(d?e.scripts.map(e=>{var t,r,n;return t=e.toString(),r="script",new Promise((e,i)=>{let a='\n      link[rel="prefetch"][href^="'+t+'"],\n      link[rel="preload"][href^="'+t+'"],\n      script[src^="'+t+'"]';if(document.querySelector(a))return e();n=document.createElement("link"),r&&(n.as=r),n.rel="prefetch",n.crossOrigin=void 0,n.onload=e,n.onerror=()=>i(u(Object.defineProperty(Error("Failed to prefetch: "+t),"__NEXT_ERROR_CODE",{value:"E268",enumerable:!1,configurable:!0}))),n.href=t,document.head.appendChild(n)})}):[])).then(()=>{(0,i.requestIdleCallback)(()=>this.loadRoute(t,!0).catch(()=>{}))}).catch(()=>{})}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51226:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51532:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n})},51856:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(51532)._(r(40998));class i{end(e){if("ended"===this.state.state)throw Object.defineProperty(Error("Span has already ended"),"__NEXT_ERROR_CODE",{value:"E17",enumerable:!1,configurable:!0});this.state={state:"ended",endTime:null!=e?e:Date.now()},this.onSpanEnd(this)}constructor(e,t,r){var n,i;this.name=e,this.attributes=null!=(n=t.attributes)?n:{},this.startTime=null!=(i=t.startTime)?i:Date.now(),this.onSpanEnd=r,this.state={state:"inprogress"}}}class a{startSpan(e,t){return new i(e,t,this.handleSpanEnd)}onSpanEnd(e){return this._emitter.on("spanend",e),()=>{this._emitter.off("spanend",e)}}constructor(){this._emitter=(0,n.default)(),this.handleSpanEnd=e=>{this._emitter.emit("spanend",e)}}}let o=new a;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52014:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getReactStitchedError",{enumerable:!0,get:function(){return u}});let n=r(51532),i=n._(r(44109)),a=n._(r(95359)),o=r(92828),s="react-stack-bottom-frame",l=RegExp("(at "+s+" )|("+s+"\\@)");function u(e){let t=(0,a.default)(e),r=t&&e.stack||"",n=t?e.message:"",s=r.split("\n"),u=s.findIndex(e=>l.test(e)),c=u>=0?s.slice(0,u).join("\n"):r,d=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return Object.assign(d,e),(0,o.copyNextErrorCode)(e,d),d.stack=c,function(e){if(!i.default.captureOwnerStack)return;let t=e.stack||"",r=i.default.captureOwnerStack();r&&!1===t.endsWith(r)&&(e.stack=t+=r)}(d),d}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52471:(e,t,r)=>{"use strict";function n(e,t){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeLocale",{enumerable:!0,get:function(){return n}}),r(66673),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52937:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return y},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return g}});let n=r(51532),i=r(98781),a=r(88017),o=n._(r(25775)),s=i._(r(44109)),l=r(74364),u=r(3236),c=r(16288),d=new Map,f=new Set,p=e=>{if(o.default.preinit)return void e.forEach(e=>{o.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},h=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:i=null,dangerouslySetInnerHTML:a,children:o="",strategy:s="afterInteractive",onError:l,stylesheets:c}=e,h=r||t;if(h&&f.has(h))return;if(d.has(t)){f.add(h),d.get(t).then(n,l);return}let m=()=>{i&&i(),f.add(h)},g=document.createElement("script"),_=new Promise((e,t)=>{g.addEventListener("load",function(t){e(),n&&n.call(this,t),m()}),g.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});a?(g.innerHTML=a.__html||"",m()):o?(g.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):"",m()):t&&(g.src=t,d.set(t,_)),(0,u.setAttributesFromProps)(g,e),"worker"===s&&g.setAttribute("type","text/partytown"),g.setAttribute("data-nscript",s),c&&p(c),document.body.appendChild(g)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>h(e))}):h(e)}function g(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function _(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:i=null,strategy:u="afterInteractive",onError:d,stylesheets:p,...m}=e,{updateScripts:g,scripts:_,getIsSsr:y,appDir:v,nonce:b}=(0,s.useContext)(l.HeadManagerContext),E=(0,s.useRef)(!1);(0,s.useEffect)(()=>{let e=t||r;E.current||(i&&e&&f.has(e)&&i(),E.current=!0)},[i,t,r]);let S=(0,s.useRef)(!1);if((0,s.useEffect)(()=>{if(!S.current){if("afterInteractive"===u)h(e);else"lazyOnload"===u&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>h(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>h(e))}));S.current=!0}},[e,u]),("beforeInteractive"===u||"worker"===u)&&(g?(_[u]=(_[u]||[]).concat([{id:t,src:r,onLoad:n,onReady:i,onError:d,...m}]),g(_)):y&&y()?f.add(t||r):y&&!y()&&h(e)),v){if(p&&p.forEach(e=>{o.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)if(!r)return m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}});else return o.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin}),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...m,id:t}])+")"}});"afterInteractive"===u&&r&&o.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(_,"__nextScript",{value:!0});let y=_;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52978:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(6097);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},o={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?o[e]=r.split("/").map(e=>a(e)):o[e]=a(r))}return o}}},53846:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return a}});let n=r(52978),i=r(91399);function a(e,t,r){let a="",o=(0,i.getRouteRegex)(e),s=o.groups,l=(t!==e?(0,n.getRouteMatcher)(o)(t):"")||r;a=e;let u=Object.keys(s);return u.every(e=>{let t=l[e]||"",{repeat:r,optional:n}=s[e],i="["+(r?"...":"")+e+"]";return n&&(i=(t?"":"/")+"["+i+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in l)&&(a=a.replace(i,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(a=""),{params:u,result:a}}},54680:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),r(51532);let n=r(88017);r(44109);let i=r(27651);function a(e){function t(t){return(0,n.jsx)(e,{router:(0,i.useRouter)(),...t})}return t.getInitialProps=e.getInitialProps,t.origGetInitialProps=e.origGetInitialProps,t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54906:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PathnameContextProviderAdapter:function(){return p},adaptForAppRouterInstance:function(){return c},adaptForPathParams:function(){return f},adaptForSearchParams:function(){return d}});let n=r(98781),i=r(88017),a=n._(r(44109)),o=r(70344),s=r(56192),l=r(40602),u=r(91399);function c(e){return{back(){e.back()},forward(){e.forward()},refresh(){e.reload()},hmrRefresh(){},push(t,r){let{scroll:n}=void 0===r?{}:r;e.push(t,void 0,{scroll:n})},replace(t,r){let{scroll:n}=void 0===r?{}:r;e.replace(t,void 0,{scroll:n})},prefetch(t){e.prefetch(t)}}}function d(e){return e.isReady&&e.query?(0,l.asPathToSearchParams)(e.asPath):new URLSearchParams}function f(e){if(!e.isReady||!e.query)return null;let t={};for(let r of Object.keys((0,u.getRouteRegex)(e.pathname).groups))t[r]=e.query[r];return t}function p(e){let{children:t,router:r,...n}=e,l=(0,a.useRef)(n.isAutoExport),u=(0,a.useMemo)(()=>{let e,t=l.current;if(t&&(l.current=!1),(0,s.isDynamicRoute)(r.pathname)&&(r.isFallback||t&&!r.isReady))return null;try{e=new URL(r.asPath,"http://f")}catch(e){return"/"}return e.pathname},[r.asPath,r.isFallback,r.isReady,r.pathname]);return(0,i.jsx)(o.PathnameContext.Provider,{value:u,children:t})}},56192:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return n.getSortedRouteObjects},getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return i.isDynamicRoute}});let n=r(71194),i=r(61460)},56242:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},57177:(e,t,r)=>{"use strict";var n,i;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(i=r.g.process)?void 0:i.env)?r.g.process:r(3510)},58840:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return a}});let n=r(73920),i=r(76073);function a(e,t,r,a){if(!t||t===r)return e;let o=e.toLowerCase();return!a&&((0,i.pathHasPrefix)(o,"/api")||(0,i.pathHasPrefix)(o,"/"+t.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+t)}},59303:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(71957),r(66808);let n=r(41706);window.next={version:n.version,get router(){return n.router},emitter:n.emitter},(0,n.initialize)({}).then(()=>(0,n.hydrate)()).catch(console.error),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59346:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},isEqualNode:function(){return a}});let i=r(3236);function a(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let r=t.getAttribute("nonce");if(r&&!e.getAttribute("nonce")){let n=t.cloneNode(!0);return n.setAttribute("nonce",""),n.nonce=r,r===e.nonce&&e.isEqualNode(n)}}return e.isEqualNode(t)}function o(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"])if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;else e.props.href=e.props["data-href"],e.props["data-href"]=void 0;let r=t[e.type]||[];r.push(e),t[e.type]=r});let r=t.title?t.title[0]:null,i="";if(r){let{children:e}=r.props;i="string"==typeof e?e:Array.isArray(e)?e.join(""):""}i!==document.title&&(document.title=i),["meta","base","link","style","script"].forEach(e=>{n(e,t[e]||[])})}}}n=(e,t)=>{let r=document.querySelector("head");if(!r)return;let n=new Set(r.querySelectorAll(""+e+"[data-next-head]"));if("meta"===e){let e=r.querySelector("meta[charset]");null!==e&&n.add(e)}let o=[];for(let e=0;e<t.length;e++){let r=function(e){let{type:t,props:r}=e,n=document.createElement(t);(0,i.setAttributesFromProps)(n,r);let{children:a,dangerouslySetInnerHTML:o}=r;return o?n.innerHTML=o.__html||"":a&&(n.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):""),n}(t[e]);r.setAttribute("data-next-head","");let s=!0;for(let e of n)if(a(e,r)){n.delete(e),s=!1;break}s&&o.push(r)}for(let e of n){var s;null==(s=e.parentNode)||s.removeChild(e)}for(let e of o)"meta"===e.tagName.toLowerCase()&&null!==e.getAttribute("charset")&&r.prepend(e),r.appendChild(e)},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61460:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return o}});let n=r(82421),i=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,a=/\/\[[^/]+\](?=\/|$)/;function o(e,t){return(void 0===t&&(t=!0),(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),t)?a.test(e):i.test(e)}},61866:()=>{"trimStart"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),"trimEnd"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),"description"in Symbol.prototype||Object.defineProperty(Symbol.prototype,"description",{configurable:!0,get:function(){var e=/\((.*)\)/.exec(this.toString());return e?e[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(e,t){return t=this.concat.apply([],this),e>1&&t.some(Array.isArray)?t.flat(e-1):t},Array.prototype.flatMap=function(e,t){return this.map(e,t).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(e){if("function"!=typeof e)return this.then(e,e);var t=this.constructor||Promise;return this.then(function(r){return t.resolve(e()).then(function(){return r})},function(r){return t.resolve(e()).then(function(){throw r})})}),Object.fromEntries||(Object.fromEntries=function(e){return Array.from(e).reduce(function(e,t){return e[t[0]]=t[1],e},{})}),Array.prototype.at||(Array.prototype.at=function(e){var t=Math.trunc(e)||0;if(t<0&&(t+=this.length),!(t<0||t>=this.length))return this[t]}),Object.hasOwn||(Object.hasOwn=function(e,t){if(null==e)throw TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(e),t)}),"canParse"in URL||(URL.canParse=function(e,t){try{return new URL(e,t),!0}catch(e){return!1}})},62653:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}function n(e){return e.startsWith("@")&&"@children"!==e}function i(e,t){if(e.includes(a)){let e=JSON.stringify(t);return"{}"!==e?a+"?"+e:a}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return o},PAGE_SEGMENT_KEY:function(){return a},addSearchParamsIfPageSegment:function(){return i},isGroupSegment:function(){return r},isParallelRouteSegment:function(){return n}});let a="__PAGE__",o="__DEFAULT__"},66673:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},66808:(e,t,r)=>{"use strict";e.exports=r(21440)},69476:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(25620),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70344:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PathParamsContext:function(){return o},PathnameContext:function(){return a},SearchParamsContext:function(){return i}});let n=r(44109),i=(0,n.createContext)(null),a=(0,n.createContext)(null),o=(0,n.createContext)(null)},71194:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return i},getSortedRoutes:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").'),"__NEXT_ERROR_CODE",{value:"E458",enumerable:!1,configurable:!0});r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Object.defineProperty(Error("Catch-all must be the last part of the URL."),"__NEXT_ERROR_CODE",{value:"E392",enumerable:!1,configurable:!0});let i=e[0];if(i.startsWith("[")&&i.endsWith("]")){let r=i.slice(1,-1),o=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),o=!0),r.startsWith("…"))throw Object.defineProperty(Error("Detected a three-dot character ('…') at ('"+r+"'). Did you mean ('...')?"),"__NEXT_ERROR_CODE",{value:"E147",enumerable:!1,configurable:!0});if(r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Object.defineProperty(Error("Segment names may not start or end with extra brackets ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E421",enumerable:!1,configurable:!0});if(r.startsWith("."))throw Object.defineProperty(Error("Segment names may not start with erroneous periods ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E288",enumerable:!1,configurable:!0});function a(e,r){if(null!==e&&e!==r)throw Object.defineProperty(Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"')."),"__NEXT_ERROR_CODE",{value:"E337",enumerable:!1,configurable:!0});t.forEach(e=>{if(e===r)throw Object.defineProperty(Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E247",enumerable:!1,configurable:!0});if(e.replace(/\W/g,"")===i.replace(/\W/g,""))throw Object.defineProperty(Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E499",enumerable:!1,configurable:!0})}),t.push(r)}if(n)if(o){if(null!=this.restSlugName)throw Object.defineProperty(Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).'),"__NEXT_ERROR_CODE",{value:"E299",enumerable:!1,configurable:!0});a(this.optionalRestSlugName,r),this.optionalRestSlugName=r,i="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E300",enumerable:!1,configurable:!0});a(this.restSlugName,r),this.restSlugName=r,i="[...]"}else{if(o)throw Object.defineProperty(Error('Optional route parameters are not yet supported ("'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E435",enumerable:!1,configurable:!0});a(this.slugName,r),this.slugName=r,i="[]"}}this.children.has(i)||this.children.set(i,new r),this.children.get(i)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}function i(e,t){let r={},i=[];for(let n=0;n<e.length;n++){let a=t(e[n]);r[a]=n,i[n]=a}return n(i).map(t=>e[r[t]])}},71957:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(38500),self.__next_set_public_path__=e=>{r.p=e},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72362:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return o}});let n=r(6855),i=r(62653);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},73920:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(66673);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=(0,n.parsePath)(e);return""+t+r+i+a}},74364:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HeadManagerContext",{enumerable:!0,get:function(){return n}});let n=r(51532)._(r(44109)).default.createContext({})},74521:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),o=(r||{}).decode||e,s=0;s<a.length;s++){var l=a[s],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return i},t.serialize=function(e,t,n){var a=n||{},o=a.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},75755:(e,t)=>{"use strict";function r(e){return Object.prototype.toString.call(e)}function n(e){if("[object Object]"!==r(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getObjectClassLabel:function(){return r},isPlainObject:function(){return n}})},76073:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(66673);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},77128:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(74521);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},77717:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{APP_BUILD_MANIFEST:function(){return v},APP_CLIENT_INTERNALS:function(){return J},APP_PATHS_MANIFEST:function(){return g},APP_PATH_ROUTES_MANIFEST:function(){return _},BARREL_OPTIMIZATION_PREFIX:function(){return $},BLOCKED_PAGES:function(){return L},BUILD_ID_FILE:function(){return D},BUILD_MANIFEST:function(){return y},CLIENT_PUBLIC_FILES_PATH:function(){return U},CLIENT_REFERENCE_MANIFEST:function(){return W},CLIENT_STATIC_FILES_PATH:function(){return F},CLIENT_STATIC_FILES_RUNTIME_AMP:function(){return Z},CLIENT_STATIC_FILES_RUNTIME_MAIN:function(){return Y},CLIENT_STATIC_FILES_RUNTIME_MAIN_APP:function(){return K},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS:function(){return et},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL:function(){return er},CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH:function(){return Q},CLIENT_STATIC_FILES_RUNTIME_WEBPACK:function(){return ee},COMPILER_INDEXES:function(){return a},COMPILER_NAMES:function(){return i},CONFIG_FILES:function(){return j},DEFAULT_RUNTIME_WEBPACK:function(){return en},DEFAULT_SANS_SERIF_FONT:function(){return el},DEFAULT_SERIF_FONT:function(){return es},DEV_CLIENT_MIDDLEWARE_MANIFEST:function(){return M},DEV_CLIENT_PAGES_MANIFEST:function(){return C},DYNAMIC_CSS_MANIFEST:function(){return V},EDGE_RUNTIME_WEBPACK:function(){return ei},EDGE_UNSUPPORTED_NODE_APIS:function(){return ep},EXPORT_DETAIL:function(){return R},EXPORT_MARKER:function(){return w},FUNCTIONS_CONFIG_MANIFEST:function(){return b},IMAGES_MANIFEST:function(){return O},INTERCEPTION_ROUTE_REWRITE_MANIFEST:function(){return G},MIDDLEWARE_BUILD_MANIFEST:function(){return z},MIDDLEWARE_MANIFEST:function(){return I},MIDDLEWARE_REACT_LOADABLE_MANIFEST:function(){return X},MODERN_BROWSERSLIST_TARGET:function(){return n.default},NEXT_BUILTIN_DOCUMENT:function(){return H},NEXT_FONT_MANIFEST:function(){return S},PAGES_MANIFEST:function(){return h},PHASE_DEVELOPMENT_SERVER:function(){return d},PHASE_EXPORT:function(){return l},PHASE_INFO:function(){return p},PHASE_PRODUCTION_BUILD:function(){return u},PHASE_PRODUCTION_SERVER:function(){return c},PHASE_TEST:function(){return f},PRERENDER_MANIFEST:function(){return P},REACT_LOADABLE_MANIFEST:function(){return A},ROUTES_MANIFEST:function(){return T},RSC_MODULE_TYPES:function(){return ef},SERVER_DIRECTORY:function(){return N},SERVER_FILES_MANIFEST:function(){return x},SERVER_PROPS_ID:function(){return eo},SERVER_REFERENCE_MANIFEST:function(){return q},STATIC_PROPS_ID:function(){return ea},STATIC_STATUS_PAGES:function(){return eu},STRING_LITERAL_DROP_BUNDLE:function(){return B},SUBRESOURCE_INTEGRITY_MANIFEST:function(){return E},SYSTEM_ENTRYPOINTS:function(){return eh},TRACE_OUTPUT_VERSION:function(){return ec},TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST:function(){return k},TURBO_TRACE_DEFAULT_MEMORY_LIMIT:function(){return ed},UNDERSCORE_NOT_FOUND_ROUTE:function(){return o},UNDERSCORE_NOT_FOUND_ROUTE_ENTRY:function(){return s},WEBPACK_STATS:function(){return m}});let n=r(51532)._(r(42651)),i={client:"client",server:"server",edgeServer:"edge-server"},a={[i.client]:0,[i.server]:1,[i.edgeServer]:2},o="/_not-found",s=""+o+"/page",l="phase-export",u="phase-production-build",c="phase-production-server",d="phase-development-server",f="phase-test",p="phase-info",h="pages-manifest.json",m="webpack-stats.json",g="app-paths-manifest.json",_="app-path-routes-manifest.json",y="build-manifest.json",v="app-build-manifest.json",b="functions-config-manifest.json",E="subresource-integrity-manifest",S="next-font-manifest",w="export-marker.json",R="export-detail.json",P="prerender-manifest.json",T="routes-manifest.json",O="images-manifest.json",x="required-server-files.json",C="_devPagesManifest.json",I="middleware-manifest.json",k="_clientMiddlewareManifest.json",M="_devMiddlewareManifest.json",A="react-loadable-manifest.json",N="server",j=["next.config.js","next.config.mjs","next.config.ts"],D="BUILD_ID",L=["/_document","/_app","/_error"],U="public",F="static",B="__NEXT_DROP_CLIENT_FILE__",H="__NEXT_BUILTIN_DOCUMENT__",$="__barrel_optimize__",W="client-reference-manifest",q="server-reference-manifest",z="middleware-build-manifest",X="middleware-react-loadable-manifest",G="interception-route-rewrite-manifest",V="dynamic-css-manifest",Y="main",K=""+Y+"-app",J="app-pages-internals",Q="react-refresh",Z="amp",ee="webpack",et="polyfills",er=Symbol(et),en="webpack-runtime",ei="edge-runtime-webpack",ea="__N_SSG",eo="__N_SSP",es={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},el={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},eu=["/500"],ec=1,ed=6e3,ef={client:"client",server:"server"},ep=["clearImmediate","setImmediate","BroadcastChannel","ByteLengthQueuingStrategy","CompressionStream","CountQueuingStrategy","DecompressionStream","DomException","MessageChannel","MessageEvent","MessagePort","ReadableByteStreamController","ReadableStreamBYOBRequest","ReadableStreamDefaultController","TransformStreamDefaultController","WritableStreamDefaultController"],eh=new Set([Y,Q,Z,K]);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77958:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return a}});let n=r(51532)._(r(44109)),i=r(56242),a=n.default.createContext(i.imageConfigDefault)},78212:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"onRecoverableError",{enumerable:!0,get:function(){return l}});let n=r(51532),i=r(9688),a=r(47716),o=r(52014),s=n._(r(95359)),l=(e,t)=>{let r=(0,s.default)(e)&&"cause"in e?e.cause:e,n=(0,o.getReactStitchedError)(r);(0,i.isBailoutToCSRError)(r)||(0,a.reportGlobalError)(n)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78480:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var o=e.charCodeAt(a);if(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122||95===o){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var s=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--s){a++;break}}else if("("===e[a]&&(s++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,a=void 0===n?"./":n,o="[^"+i(t.delimiter||"/#?")+"]+?",s=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},f=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var h=d("CHAR"),m=d("NAME"),g=d("PATTERN");if(m||g){var _=h||"";-1===a.indexOf(_)&&(c+=_,_=""),c&&(s.push(c),c=""),s.push({name:m||l++,prefix:_,suffix:"",pattern:g||o,modifier:d("MODIFIER")||""});continue}var y=h||d("ESCAPED_CHAR");if(y){c+=y;continue}if(c&&(s.push(c),c=""),d("OPEN")){var _=p(),v=d("NAME")||"",b=d("PATTERN")||"",E=p();f("CLOSE"),s.push({name:v||(b?l++:""),pattern:v&&!b?o:b,prefix:_,suffix:E,modifier:d("MODIFIER")||""});continue}f("END")}return s}function r(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,i=void 0===n?function(e){return e}:n,o=t.validate,s=void 0===o||o,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var o=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,c="*"===a.modifier||"+"===a.modifier;if(Array.isArray(o)){if(!c)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===o.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var d=0;d<o.length;d++){var f=i(o[d],a);if(s&&!l[n].test(f))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+f+'"');r+=a.prefix+f+a.suffix}continue}if("string"==typeof o||"number"==typeof o){var f=i(String(o),a);if(s&&!l[n].test(f))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+f+'"');r+=a.prefix+f+a.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],o=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):s[r.name]=i(n[e],r)}}(l);return{path:a,index:o,params:s}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function o(e,t,r){void 0===r&&(r={});for(var n=r.strict,o=void 0!==n&&n,s=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+i(r.endsWith||"")+"]|$",f="["+i(r.delimiter||"/#?")+"]",p=void 0===s||s?"^":"",h=0;h<e.length;h++){var m=e[h];if("string"==typeof m)p+=i(c(m));else{var g=i(c(m.prefix)),_=i(c(m.suffix));if(m.pattern)if(t&&t.push(m),g||_)if("+"===m.modifier||"*"===m.modifier){var y="*"===m.modifier?"?":"";p+="(?:"+g+"((?:"+m.pattern+")(?:"+_+g+"(?:"+m.pattern+"))*)"+_+")"+y}else p+="(?:"+g+"("+m.pattern+")"+_+")"+m.modifier;else p+="("+m.pattern+")"+m.modifier;else p+="(?:"+g+_+")"+m.modifier}}if(void 0===l||l)o||(p+=f+"?"),p+=r.endsWith?"(?="+d+")":"$";else{var v=e[e.length-1],b="string"==typeof v?f.indexOf(v[v.length-1])>-1:void 0===v;o||(p+="(?:"+f+"(?="+d+"))?"),b||(p+="(?="+f+"|"+d+")")}return new RegExp(p,a(r))}function s(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,r,n).source}).join("|")+")",a(n)):o(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(s(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=o,t.pathToRegexp=s})(),e.exports=t})()},78807:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},79030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},81414:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return f}});let n=r(78480),i=r(96687),a=r(40859),o=r(82421),s=r(77128);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},a=r=>{let n,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,s.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||n.some(e=>a(e)))&&i}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),n=r.pathname;n&&(n=l(n));let o=r.href;o&&(o=l(o));let s=r.hostname;s&&(s=l(s));let u=r.hash;return u&&(u=l(u)),{...r,pathname:n,hostname:s,href:o,hash:u}}function f(e){let t,r,i=Object.assign({},e.query),a=d(e),{hostname:s,query:u}=a,f=a.pathname;a.hash&&(f=""+f+a.hash);let p=[],h=[];for(let e of((0,n.pathToRegexp)(f,h),h))p.push(e.name);if(s){let e=[];for(let t of((0,n.pathToRegexp)(s,e),e))p.push(t.name)}let m=(0,n.compile)(f,{validate:!1});for(let[r,i]of(s&&(t=(0,n.compile)(s,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[r]=i.map(t=>c(l(t),e.params)):"string"==typeof i&&(u[r]=c(l(i),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>p.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,o.isInterceptionRouteAppPath)(f))for(let t of f.split("/")){let r=o.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=m(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=n,a.hash=(i?"#":"")+(i||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...i,...a.query},{newUrl:r,destQuery:u,parsedDestination:a}}},82421:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return a}});let n=r(72362),i=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function o(e){let t,r,a;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=o.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},83447:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(78480);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=a(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},84563:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return i}});let n=r(76073);function i(e,t){if(!(0,n.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},85372:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(5257),i=r(319),a=r(10773),o=r(6097),s=r(14688),l=r(8742),u=r(56192),c=r(53846);function d(e,t,r){let d,f="string"==typeof t?t:(0,i.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,o.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,l.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,s.normalizePathTrailingSlash)(e.pathname);let t="";if((0,u.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:o,params:s}=(0,c.interpolateAs)(e.pathname,e.pathname,r);o&&(t=(0,i.formatWithValidation)({pathname:o,hash:e.hash,query:(0,a.omit)(r,s)}))}let o=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[o,t||o]:o}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87897:(e,t)=>{"use strict";let r;function n(e){var t;return(null==(t=function(){if(void 0===r){var e;r=(null==(e=window.trustedTypes)?void 0:e.createPolicy("nextjs",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e}))||null}return r}())?void 0:t.createScriptURL(e))||e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"__unsafeCreateTrustedScriptURL",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90020:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return l},isBot:function(){return s}});let n=r(79030),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=n.HTML_LIMITED_BOT_UA_RE.source;function o(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return i.test(e)||o(e)}function l(e){return i.test(e)?"dom":o(e)?"html":void 0}},90457:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return s}});let n=r(36415),i=r(73920),a=r(26393),o=r(58840);function s(e){let t=(0,o.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,n.removeTrailingSlash)(t)),e.buildId&&(t=(0,a.addPathSuffix)((0,i.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,i.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,a.addPathSuffix)(t,"/"):(0,n.removeTrailingSlash)(t)}},90495:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange)return void e();let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},91399:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return h},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=r(181),i=r(82421),a=r(96687),o=r(36415),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(s);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},l=1,c=[];for(let d of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),o=d.match(s);if(e&&o&&o[2]){let{key:t,optional:r,repeat:i}=u(o[2]);n[t]={pos:l++,repeat:i,optional:r},c.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(o&&o[2]){let{key:e,repeat:t,optional:i}=u(o[2]);n[e]={pos:l++,repeat:t,optional:i},r&&o[1]&&c.push("/"+(0,a.escapeStringRegexp)(o[1]));let s=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&o[1]&&(s=s.substring(1)),c.push(s)}else c.push("/"+(0,a.escapeStringRegexp)(d));t&&o&&o[3]&&c.push((0,a.escapeStringRegexp)(o[3]))}return{parameterizedRoute:c.join(""),groups:n}}function d(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:o}=c(e,r,n),s=a;return i||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:o}}function f(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:o,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:f}=u(i),p=c.replace(/\W/g,"");s&&(p=""+s+p);let h=!1;(0===p.length||p.length>30)&&(h=!0),isNaN(parseInt(p.slice(0,1)))||(h=!0),h&&(p=n());let m=p in o;s?o[p]=""+s+c:o[p]=c;let g=r?(0,a.escapeStringRegexp)(r):"";return t=m&&l?"\\k<"+p+">":f?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function p(e,t,r,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},h=[];for(let c of(0,o.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),o=c.match(s);if(e&&o&&o[2])h.push(f({getSafeRouteKey:d,interceptionMarker:o[1],segment:o[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(o&&o[2]){l&&o[1]&&h.push("/"+(0,a.escapeStringRegexp)(o[1]));let e=f({getSafeRouteKey:d,segment:o[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&o[1]&&(e=e.substring(1)),h.push(e)}else h.push("/"+(0,a.escapeStringRegexp)(c));r&&o&&o[3]&&h.push((0,a.escapeStringRegexp)(o[3]))}return{namedParameterizedRoute:h.join(""),routeKeys:p}}function h(e,t){var r,n,i;let a=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),o=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(o+="(?:/)?"),{...d(e,t),namedRegex:"^"+o+"$",routeKeys:a.routeKeys}}function m(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},91557:(e,t)=>{"use strict";function r(e){return"/api"===e||!!(null==e?void 0:e.startsWith("/api/"))}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAPIRoute",{enumerable:!0,get:function(){return r}})},92828:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{copyNextErrorCode:function(){return n},createDigestWithErrorCode:function(){return r},extractNextErrorCode:function(){return i}});let r=(e,t)=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e?`${t}@${e.__NEXT_ERROR_CODE}`:t,n=(e,t)=>{let r=i(e);r&&"object"==typeof t&&null!==t&&Object.defineProperty(t,"__NEXT_ERROR_CODE",{value:r,enumerable:!1,configurable:!0})},i=e=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e&&"string"==typeof e.__NEXT_ERROR_CODE?e.__NEXT_ERROR_CODE:"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest?e.digest.split("@").find(e=>e.startsWith("E")):void 0},95359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return i},getProperError:function(){return a}});let n=r(75755);function i(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}function a(e){return i(e)?e:Object.defineProperty(Error((0,n.isPlainObject)(e)?function(e){let t=new WeakSet;return JSON.stringify(e,(e,r)=>{if("object"==typeof r&&null!==r){if(t.has(r))return"[Circular]";t.add(r)}return r})}(e):e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},96687:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},98781:(e,t,r)=>{"use strict";function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function i(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=a?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(i,o,s):i[o]=e[o]}return i.default=e,r&&r.set(e,i),i}r.r(t),r.d(t,{_:()=>i})}},e=>{var t=t=>e(e.s=t);e.O(0,[6593],()=>t(59303)),_N_E=e.O()}]);