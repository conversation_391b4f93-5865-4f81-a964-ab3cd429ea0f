try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="95e74e4d-88cc-49fd-b5cd-620b9944a3ac",e._sentryDebugIdIdentifier="sentry-dbid-95e74e4d-88cc-49fd-b5cd-620b9944a3ac")}catch(e){}exports.id=125,exports.ids=[125],exports.modules={7760:()=>{},45962:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=45962,e.exports=t},49616:()=>{},53547:(e,t,s)=>{"use strict";s.d(t,{WX:()=>p,$y:()=>y,ZA:()=>f});var r=s(79615),n=s(14243),a=s(61287),i=s(66402),o=s(36218);let l=new(s(16698)).AsyncLocalStorage;var u=s(20402);let c=async()=>{var e,t;let s;try{let e=await (0,a.TG)(),t=(0,o._b)(e,n.AA.Headers.ClerkRequestData);s=(0,u.Kk)(t)}catch(e){if(e&&(0,a.Sz)(e))throw e}let r=null!=(t=null==(e=l.getStore())?void 0:e.get("requestData"))?t:s;return(null==r?void 0:r.secretKey)||(null==r?void 0:r.publishableKey)?(0,i.n)(r):(0,i.n)({})};async function d(){s(58838);let{userId:e}=await (0,r.j)();return e?(await c()).users.getUser(e):null}var h=s(35886);async function m(){try{let{userId:e}=await (0,r.j)();if(!e)return{success:!1,error:"Unauthorized - No user session",response:h.NextResponse.json({error:"Authentication required"},{status:401})};let t=await d();if(!t)return{success:!1,error:"Unable to fetch user profile",response:h.NextResponse.json({error:"User profile not found"},{status:401})};let s=t.emailAddresses?.[0]?.emailAddress,n=t.firstName,a=t.lastName;if(!s)return console.log("No email found for user:",t.id),{success:!1,error:"Invalid user - No email found",response:h.NextResponse.json({error:"User email not found"},{status:401})};return{success:!0,user:{clerkId:e,email:s,firstName:n||void 0,lastName:a||void 0}}}catch(e){return console.error("Authentication validation error:",e),{success:!1,error:"Authentication validation failed",response:h.NextResponse.json({error:"Authentication error"},{status:500})}}}function p(e,t=500,s){let r={error:e,timestamp:new Date().toISOString(),...s&&{details:s}};return console.error("API Error:",r),h.NextResponse.json(r,{status:t})}function y(e,t=200){return h.NextResponse.json(e,{status:t})}function f(e){return async(t,s)=>{let r=await m();if(!r.success)return r.response;try{if(s)return await e(r.user,t,s);return await e(r.user,t)}catch(e){return console.error("API handler error:",e),p("Internal server error")}}}},54360:(e,t,s)=>{"use strict";s.d(t,{o:()=>n});class r{constructor(e){this.user=e}async makeRequest(e,t={}){let s,r,{method:n="GET",body:a,params:i}=t;if(s=`http://localhost:8002/api${e}`,i){let e=new URLSearchParams;Object.entries(i).forEach(([t,s])=>{null!=s&&e.append(t,s.toString())}),e.toString()&&(s+=`?${e.toString()}`)}r={method:n,headers:{"Content-Type":"application/json","X-Clerk-User-Id":this.user.clerkId,"X-User-Email":this.user.email}},a&&"GET"!==n&&(r.body=JSON.stringify(a));try{let e=await fetch(s,r);if(!e.ok){let t=await e.text();throw Error(`API request failed: ${e.status} ${e.statusText} - ${t}`)}return await e.json()}catch(t){throw console.error(`API request failed for ${e}:`,t),t}}async getAppointments(e){return this.makeRequest("/appointments",{params:{depth:"2",...e}})}async getAppointment(e){return this.makeRequest(`/appointments/${e}`,{params:{depth:"2"}})}async createAppointment(e){return this.makeRequest("/appointments",{method:"POST",body:e})}async updateAppointment(e,t){return this.makeRequest(`/appointments/${e}`,{method:"PATCH",body:t})}async deleteAppointment(e){return this.makeRequest(`/appointments/${e}`,{method:"DELETE"})}async getPatients(e){let t={depth:"1",...e};return e?.search&&(t["where[or][0][fullName][contains]"]=e.search,t["where[or][1][phone][contains]"]=e.search,t["where[or][2][email][contains]"]=e.search,delete t.search),this.makeRequest("/patients",{params:t})}async getPatient(e){return this.makeRequest(`/patients/${e}`,{params:{depth:"1"}})}async createPatient(e){return this.makeRequest("/patients",{method:"POST",body:e})}async updatePatient(e,t){return this.makeRequest(`/patients/${e}`,{method:"PATCH",body:t})}async deletePatient(e){return this.makeRequest(`/patients/${e}`,{method:"DELETE"})}async getTreatments(e){return this.makeRequest("/treatments",{params:{depth:"1",...e}})}async getTreatment(e){return this.makeRequest(`/treatments/${e}`)}async createTreatment(e){return this.makeRequest("/treatments",{method:"POST",body:e})}async updateTreatment(e,t){return this.makeRequest(`/treatments/${e}`,{method:"PATCH",body:t})}async deleteTreatment(e){return this.makeRequest(`/treatments/${e}`,{method:"DELETE"})}async getUsers(e){return this.makeRequest("/users",{params:{depth:"1",...e}})}async updateUser(e,t){return this.makeRequest(`/users/${e}`,{method:"PATCH",body:t})}async syncCurrentUser(){return this.makeRequest("/users/sync",{method:"POST",body:{clerkId:this.user.clerkId,email:this.user.email,firstName:this.user.firstName,lastName:this.user.lastName}})}async syncUser(e){try{let t=await this.makeRequest("/users",{params:{where:JSON.stringify({clerkId:{equals:e.clerkId}}),limit:1}});if(!t.docs||!(t.docs.length>0))return await this.makeRequest("/users",{method:"POST",body:{email:e.email,clerkId:e.clerkId,firstName:e.firstName,lastName:e.lastName,role:"front-desk",lastLogin:new Date().toISOString()}});{let s=t.docs[0];return await this.makeRequest(`/users/${s.id}`,{method:"PATCH",body:{email:e.email,firstName:e.firstName,lastName:e.lastName,lastLogin:new Date().toISOString()}})}}catch(t){return console.error("Error syncing user with Payload:",t),{id:"temp-id",email:e.email,clerkId:e.clerkId,role:"front-desk",firstName:e.firstName,lastName:e.lastName}}}async getPatientInteractions(e){return this.makeRequest("/patient-interactions",{params:{depth:"2",...e}})}async getPatientInteraction(e){return this.makeRequest(`/patient-interactions/${e}`,{params:{depth:"2"}})}async createPatientInteraction(e){return this.makeRequest("/patient-interactions",{method:"POST",body:e})}async updatePatientInteraction(e,t){return this.makeRequest(`/patient-interactions/${e}`,{method:"PATCH",body:t})}async deletePatientInteraction(e){return this.makeRequest(`/patient-interactions/${e}`,{method:"DELETE"})}async getPatientTasks(e){return this.makeRequest("/patient-tasks",{params:{depth:"2",...e}})}async getPatientTask(e){return this.makeRequest(`/patient-tasks/${e}`,{params:{depth:"2"}})}async createPatientTask(e){return this.makeRequest("/patient-tasks",{method:"POST",body:e})}async updatePatientTask(e,t){return this.makeRequest(`/patient-tasks/${e}`,{method:"PATCH",body:t})}async deletePatientTask(e){return this.makeRequest(`/patient-tasks/${e}`,{method:"DELETE"})}async getPatientInteractionsByPatient(e,t){return this.makeRequest(`/patients/${e}/interactions`,{params:{depth:"2",...t}})}async getPatientTasksByPatient(e,t){return this.makeRequest(`/patients/${e}/tasks`,{params:{depth:"2",...t}})}async getPatientTimeline(e,t){return this.makeRequest(`/patients/${e}/timeline`,{params:{depth:"2",...t}})}}function n(e){return new r(e)}},66402:(e,t,s)=>{"use strict";s.d(t,{n:()=>K});var r,n,a,i,o,l,u,c,d,h,m,p,y,f,k,S,g,b,w,R=s(10238),q=s(58534);s(94087),s(81315);var T=s(24598),E=s(95517),v=s(6881),I=class{constructor(){(0,v.VK)(this,a),(0,v.VK)(this,r,"clerk_telemetry_throttler"),(0,v.VK)(this,n,864e5)}isEventThrottled(e){if(!(0,v.S7)(this,a,l))return!1;let t=Date.now(),s=(0,v.jq)(this,a,i).call(this,e),u=(0,v.S7)(this,a,o)?.[s];if(!u){let e={...(0,v.S7)(this,a,o),[s]:t};localStorage.setItem((0,v.S7)(this,r),JSON.stringify(e))}if(u&&t-u>(0,v.S7)(this,n)){let e=(0,v.S7)(this,a,o);delete e[s],localStorage.setItem((0,v.S7)(this,r),JSON.stringify(e))}return!!u}};r=new WeakMap,n=new WeakMap,a=new WeakSet,i=function(e){let{sk:t,pk:s,payload:r,...n}=e,a={...r,...n};return JSON.stringify(Object.keys({...r,...n}).sort().map(e=>a[e]))},o=function(){let e=localStorage.getItem((0,v.S7)(this,r));return e?JSON.parse(e):{}},l=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,v.S7)(this,r)),!1}};var N={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},P=class{constructor(e){(0,v.VK)(this,p),(0,v.VK)(this,u),(0,v.VK)(this,c),(0,v.VK)(this,d,{}),(0,v.VK)(this,h,[]),(0,v.VK)(this,m),(0,v.OV)(this,u,{maxBufferSize:e.maxBufferSize??N.maxBufferSize,samplingRate:e.samplingRate??N.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:N.endpoint}),e.clerkVersion||"undefined"!=typeof window?(0,v.S7)(this,d).clerkVersion=e.clerkVersion??"":(0,v.S7)(this,d).clerkVersion="",(0,v.S7)(this,d).sdk=e.sdk,(0,v.S7)(this,d).sdkVersion=e.sdkVersion,(0,v.S7)(this,d).publishableKey=e.publishableKey??"";let t=(0,E.q5)(e.publishableKey);t&&((0,v.S7)(this,d).instanceType=t.instanceType),e.secretKey&&((0,v.S7)(this,d).secretKey=e.secretKey.substring(0,16)),(0,v.OV)(this,c,new I)}get isEnabled(){return!("development"!==(0,v.S7)(this,d).instanceType||(0,v.S7)(this,u).disabled||"undefined"!=typeof process&&(0,T.zz)(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return(0,v.S7)(this,u).debug||"undefined"!=typeof process&&(0,T.zz)(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=(0,v.jq)(this,p,w).call(this,e.event,e.payload);(0,v.jq)(this,p,g).call(this,t.event,t),(0,v.jq)(this,p,y).call(this,t,e.eventSamplingRate)&&((0,v.S7)(this,h).push(t),(0,v.jq)(this,p,k).call(this))}};u=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,m=new WeakMap,p=new WeakSet,y=function(e,t){return this.isEnabled&&!this.isDebug&&(0,v.jq)(this,p,f).call(this,e,t)},f=function(e,t){let s=Math.random();return!!(s<=(0,v.S7)(this,u).samplingRate&&(void 0===t||s<=t))&&!(0,v.S7)(this,c).isEventThrottled(e)},k=function(){if("undefined"==typeof window)return void(0,v.jq)(this,p,S).call(this);if((0,v.S7)(this,h).length>=(0,v.S7)(this,u).maxBufferSize){(0,v.S7)(this,m)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)((0,v.S7)(this,m)),(0,v.jq)(this,p,S).call(this);return}(0,v.S7)(this,m)||("requestIdleCallback"in window?(0,v.OV)(this,m,requestIdleCallback(()=>{(0,v.jq)(this,p,S).call(this)})):(0,v.OV)(this,m,setTimeout(()=>{(0,v.jq)(this,p,S).call(this)},0)))},S=function(){fetch(new URL("/v1/event",(0,v.S7)(this,u).endpoint),{method:"POST",body:JSON.stringify({events:(0,v.S7)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,v.OV)(this,h,[])}).catch(()=>void 0)},g=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},b=function(){let e={name:(0,v.S7)(this,d).sdk,version:(0,v.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},w=function(e,t){let s=(0,v.jq)(this,p,b).call(this);return{event:e,cv:(0,v.S7)(this,d).clerkVersion??"",it:(0,v.S7)(this,d).instanceType??"",sdk:s.name,sdkv:s.version,...(0,v.S7)(this,d).publishableKey?{pk:(0,v.S7)(this,d).publishableKey}:{},...(0,v.S7)(this,d).secretKey?{sk:(0,v.S7)(this,d).secretKey}:{},payload:t}};(0,q.C)(R.nr);var O=s(17469);let A={secretKey:O.rB,publishableKey:O.At,apiUrl:O.H$,apiVersion:O.mG,userAgent:"@clerk/nextjs@6.12.12",proxyUrl:O.Rg,domain:O.V2,isSatellite:O.fS,sdkMetadata:O.tm,telemetry:{disabled:O.nN,debug:O.Mh}},K=e=>(function(e){let t={...e},s=(0,R.y3)(t),r=(0,R.Bs)({options:t,apiClient:s}),n=new P({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...s,...r,telemetry:n}})({...A,...e})},86047:(e,t,s)=>{"use strict";e.exports=s(44870)}};
//# sourceMappingURL=125.js.map