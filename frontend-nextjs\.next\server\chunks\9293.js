try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="1326ae38-9496-4a47-8648-4d966dce8a62",e._sentryDebugIdIdentifier="sentry-dbid-1326ae38-9496-4a47-8648-4d966dce8a62")}catch(e){}"use strict";exports.id=9293,exports.ids=[9293],exports.modules={57158:(e,r,t)=>{t.d(r,{y4:()=>a,p8:()=>i,dc:()=>n});var s=Object.getOwnPropertyNames;let d=((e,r)=>function(){return r||(0,e[s(e)[0]])((r={exports:{}}).exports,r),r.exports})({"src/runtime/node/safe-node-apis.js"(e,r){let{existsSync:s,writeFileSync:d,readFileSync:o,appendFileSync:i,mkdirSync:n,rmSync:a}=t(73024);r.exports={fs:{existsSync:s,writeFileSync:d,readFileSync:o,appendFileSync:i,mkdirSync:n,rmSync:a},path:t(76760),cwd:()=>process.cwd()}}})(),o=e=>{throw Error(`Clerk: ${e} is missing. This is an internal error. Please contact Clerk's support.`)},i=()=>(d.fs||o("fs"),d.fs),n=()=>(d.path||o("path"),d.path),a=()=>(d.cwd||o("cwd"),d.cwd)},81674:(e,r,t)=>{t.r(r),t.d(r,{hasSrcAppDir:()=>d,suggestMiddlewareLocation:()=>o});var s=t(57158);function d(){let{existsSync:e}=(0,s.p8)(),r=(0,s.dc)(),t=(0,s.y4)();return!!e(r.join(t(),"src","app"))}function o(){let e=["ts","js"],r=(e,r,t)=>`Clerk: clerkMiddleware() was not run, your middleware file might be misplaced. Move your middleware file to ./${r}middleware.${e}. Currently located at ./${t}middleware.${e}`,{existsSync:t}=(0,s.p8)(),d=(0,s.dc)(),o=(0,s.y4)(),i=d.join(o(),"src","app"),n=d.join(o(),"app"),a=(s,o,i)=>{for(let n of e)if(t(d.join(s,`middleware.${n}`)))return r(n,o,i)};return t(i)?a(i,"src/","src/app/")||a(o(),"src/",""):t(n)?a(n,"","app/"):void 0}}};
//# sourceMappingURL=9293.js.map