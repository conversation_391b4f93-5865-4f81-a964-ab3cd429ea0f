try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="33bffe19-5981-4e35-aaaa-1a1e906dc5f4",e._sentryDebugIdIdentifier="sentry-dbid-33bffe19-5981-4e35-aaaa-1a1e906dc5f4")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1382],{40548:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>i,TN:()=>c,XL:()=>o});var a=r(52880);r(99004);var n=r(85017),l=r(54651);let s=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(s({variant:r}),t),...n,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,l.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},49202:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(99004),n={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let l=(e,t,r,l)=>{let s=(0,a.forwardRef)((r,s)=>{let{color:i="currentColor",size:o=24,stroke:c=2,title:d,className:u,children:f,...v}=r;return(0,a.createElement)("svg",{ref:s,...n[e],width:o,height:o,className:["tabler-icon","tabler-icon-".concat(t),u].join(" "),..."filled"===e?{fill:i}:{strokeWidth:c,stroke:i},...v},[d&&(0,a.createElement)("title",{key:"svg-title"},d),...l.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(f)?f:[f]])});return s.displayName="".concat(r),s}},51969:(e,t,r)=>{Promise.resolve().then(r.bind(r,68955))},54651:(e,t,r)=>{"use strict";r.d(t,{Jv:()=>c,cn:()=>l,fw:()=>o,r6:()=>i,z3:()=>s});var a=r(97921),n=r(56309);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function s(e){var t,r;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:n=0,sizeType:l="normal"}=a;if(0===e)return"0 Byte";let s=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,s)).toFixed(n)," ").concat("accurate"===l?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][s])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][s])?r:"Bytes")}function i(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function o(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let r=Math.floor(t/60);if(r<60)return"".concat(r,"分钟前");let a=Math.floor(r/60);if(a<24)return"".concat(a,"小时前");let n=Math.floor(a/24);if(n<7)return"".concat(n,"天前");let l=Math.floor(n/7);if(l<4)return"".concat(l,"周前");let s=Math.floor(n/30);if(s<12)return"".concat(s,"个月前");let i=Math.floor(n/365);return"".concat(i,"年前")}function c(e){return("string"==typeof e?new Date(e):e)<new Date}},68955:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(52880),n=r(40548),l=r(92708);function s(e){let{error:t}=e;return(0,a.jsxs)(n.Fc,{variant:"destructive","data-sentry-element":"Alert","data-sentry-component":"OverviewError","data-sentry-source-file":"error.tsx",children:[(0,a.jsx)(l.A,{className:"h-4 w-4","data-sentry-element":"IconAlertCircle","data-sentry-source-file":"error.tsx"}),(0,a.jsx)(n.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,a.jsxs)(n.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load statistics: ",t.message]})]})}},85017:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var a=r(97921);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=a.$,s=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:i}=t,o=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],a=null==i?void 0:i[e];if(null===t)return null;let l=n(t)||n(a);return s[e][l]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return l(e,o,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},92708:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var a=(0,r(49202).A)("outline","alert-circle","IconAlertCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,9442,4579,9253,7358],()=>t(51969)),_N_E=e.O()}]);