exports.id=5043,exports.ids=[5043],exports.modules={25043:(e,t,r)=>{"use strict";r.r(t),r.d(t,{RichText:()=>ew});var n=r(46610),i=r(90420),o=r(79097),l=r(28709),a=r(49891),s=r(26620);let u=(0,s.createContext)(null),d={didCatch:!1,error:null};class c extends s.Component{constructor(e){super(e),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=d}static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary(){let{error:e}=this.state;if(null!==e){for(var t,r,n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];null==(t=(r=this.props).onReset)||t.call(r,{args:i,reason:"imperative-api"}),this.setState(d)}}componentDidCatch(e,t){var r,n;null==(r=(n=this.props).onError)||r.call(n,e,t)}componentDidUpdate(e,t){let{didCatch:r}=this.state,{resetKeys:n}=this.props;if(r&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some((e,r)=>!Object.is(e,t[r]))}(e.resetKeys,n)){var i,o;null==(i=(o=this.props).onReset)||i.call(o,{next:n,prev:e.resetKeys,reason:"keys"}),this.setState(d)}}render(){let{children:e,fallbackRender:t,FallbackComponent:r,fallback:n}=this.props,{didCatch:i,error:o}=this.state,l=e;if(i){let e={error:o,resetErrorBoundary:this.resetErrorBoundary};if("function"==typeof t)l=t(e);else if(r)l=(0,s.createElement)(r,e);else if(void 0!==n)l=n;else throw o}return(0,s.createElement)(u.Provider,{value:{didCatch:i,error:o,resetErrorBoundary:this.resetErrorBoundary}},l)}}r(99930);var f=r(1062),m=r(47186);let p="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,h=p?s.useLayoutEffect:s.useEffect,g={tag:"history-merge"};function y({initialConfig:e,children:t}){let r=(0,s.useMemo)(()=>{let{theme:t,namespace:r,nodes:n,onError:i,editorState:o,html:l}=e,a=(0,f.Mx)(null,t),s=(0,m.ieJ)({editable:e.editable,html:l,namespace:r,nodes:n,onError:e=>i(e,s),theme:t});return function(e,t){if(null!==t){if(void 0===t)e.update(()=>{let t=(0,m.NiT)();if(t.isEmpty()){let r=(0,m.lJ7)();t.append(r);let n=p?document.activeElement:null;(null!==(0,m.vJq)()||null!==n&&n===e.getRootElement())&&r.select()}},g);else if(null!==t)switch(typeof t){case"string":{let r=e.parseEditorState(t);e.setEditorState(r,g);break}case"object":e.setEditorState(t,g);break;case"function":e.update(()=>{(0,m.NiT)().isEmpty()&&t(e)},g)}}}(s,o),[s,a]},[]);return h(()=>{let t=e.editable,[n]=r;n.setEditable(void 0===t||t)},[]),(0,o.jsx)(f.Gu.Provider,{value:r,children:t})}var x=r(10553);function b(e,t){return(b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var v={error:null},E=function(e){function t(){for(var t,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return(t=e.call.apply(e,[this].concat(n))||this).state=v,t.resetErrorBoundary=function(){for(var e,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];null==t.props.onReset||(e=t.props).onReset.apply(e,n),t.reset()},t}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,b(t,e),t.getDerivedStateFromError=function(e){return{error:e}};var r=t.prototype;return r.reset=function(){this.setState(v)},r.componentDidCatch=function(e,t){var r,n;null==(r=(n=this.props).onError)||r.call(n,e,t)},r.componentDidUpdate=function(e,t){var r,n,i,o,l=this.state.error,a=this.props.resetKeys;null!==l&&null!==t.error&&(void 0===(i=e.resetKeys)&&(i=[]),void 0===(o=a)&&(o=[]),i.length!==o.length||i.some(function(e,t){return!Object.is(e,o[t])}))&&(null==(r=(n=this.props).onResetKeysChange)||r.call(n,e.resetKeys,a),this.reset())},r.render=function(){var e=this.state.error,t=this.props,r=t.fallbackRender,n=t.FallbackComponent,i=t.fallback;if(null!==e){var o={error:e,resetErrorBoundary:this.resetErrorBoundary};if(s.isValidElement(i))return i;if("function"==typeof r)return r(o);if(n)return s.createElement(n,o);throw Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop")}return this.props.children},t}(s.Component);function C({children:e,onError:t}){return(0,o.jsx)(E,{fallback:(0,o.jsx)("div",{style:{border:"1px solid #f00",color:"#f00",padding:"8px"},children:"An error was thrown."}),onError:t,children:e})}var S=r(26549);function j(e){e.undoStack=[],e.redoStack=[],e.current=null}function w({delay:e,externalHistoryState:t}){let[r]=(0,f.DF)();return function(e,t,r=1e3){let n=(0,s.useMemo)(()=>t||{current:null,redoStack:[],undoStack:[]},[t]);(0,s.useEffect)(()=>(function(e,t,r){let n,i,o=(n=Date.now(),i=0,(t,o,l,a,s,u)=>{let d=Date.now();if(u.has("historic"))return i=0,n=d,2;let c=function(e,t,r,n,i){if(null===e||0===r.size&&0===n.size&&!i)return 0;let o=t._selection,l=e._selection;if(i)return 1;if(!((0,m.I2P)(o)&&(0,m.I2P)(l)&&l.isCollapsed()&&o.isCollapsed()))return 0;let a=function(e,t,r){let n=e._nodeMap,i=[];for(let e of t){let t=n.get(e);void 0!==t&&i.push(t)}for(let[e,t]of r){if(!t)continue;let r=n.get(e);void 0===r||(0,m.hVZ)(r)||i.push(r)}return i}(t,r,n);if(0===a.length)return 0;if(a.length>1){let r=t._nodeMap,n=r.get(o.anchor.key),i=r.get(l.anchor.key);return n&&i&&!e._nodeMap.has(n.__key)&&(0,m.kFe)(n)&&1===n.__text.length&&1===o.anchor.offset?2:0}let s=a[0],u=e._nodeMap.get(s.__key);if(!(0,m.kFe)(u)||!(0,m.kFe)(s)||u.__mode!==s.__mode)return 0;let d=u.__text,c=s.__text;if(d===c)return 0;let f=o.anchor,p=l.anchor;if(f.key!==p.key||"text"!==f.type)return 0;let h=f.offset,g=p.offset,y=c.length-d.length;return 1===y&&g===h-1?2:-1===y&&g===h+1?3:4*(-1===y&&g===h)}(t,o,a,s,e.isComposing()),f=(()=>{let f=null===l||l.editor===e,p=u.has("history-push");if(!p&&f&&u.has("history-merge"))return 0;if(null===t)return 1;let h=o._selection;return a.size>0||s.size>0?!1===p&&0!==c&&c===i&&d<n+r&&f||1===a.size&&function(e,t,r){let n=t._nodeMap.get(e),i=r._nodeMap.get(e),o=t._selection,l=r._selection;return!((0,m.I2P)(o)&&(0,m.I2P)(l)&&"element"===o.anchor.type&&"element"===o.focus.type&&"text"===l.anchor.type&&"text"===l.focus.type||!(0,m.kFe)(n)||!(0,m.kFe)(i)||n.__parent!==i.__parent)&&JSON.stringify(t.read(()=>n.exportJSON()))===JSON.stringify(r.read(()=>i.exportJSON()))}(Array.from(a)[0],t,o)?0:1:2*(null===h)})();return n=d,i=c,f});return(0,S.Sd)(e.registerCommand(m.ZKf,()=>((function(e,t){let r=t.redoStack,n=t.undoStack;if(0!==n.length){let i=t.current,o=n.pop();null!==i&&(r.push(i),e.dispatchCommand(m.WWb,!0)),0===n.length&&e.dispatchCommand(m.KfJ,!1),t.current=o||null,o&&o.editor.setEditorState(o.editorState,{tag:"historic"})}})(e,t),!0),m.jZM),e.registerCommand(m.V30,()=>((function(e,t){let r=t.redoStack,n=t.undoStack;if(0!==r.length){let i=t.current;null!==i&&(n.push(i),e.dispatchCommand(m.KfJ,!0));let o=r.pop();0===r.length&&e.dispatchCommand(m.WWb,!1),t.current=o||null,o&&o.editor.setEditorState(o.editorState,{tag:"historic"})}})(e,t),!0),m.jZM),e.registerCommand(m.SKc,()=>(j(t),!1),m.jZM),e.registerCommand(m.t5V,()=>(j(t),e.dispatchCommand(m.WWb,!1),e.dispatchCommand(m.KfJ,!1),!0),m.jZM),e.registerUpdateListener(({editorState:r,prevEditorState:n,dirtyLeaves:i,dirtyElements:l,tags:a})=>{let s=t.current,u=t.redoStack,d=t.undoStack,c=null===s?null:s.editorState;if(null!==s&&r===c)return;let f=o(n,r,s,i,l,a);if(1===f)0!==u.length&&(t.redoStack=[],e.dispatchCommand(m.WWb,!1)),null!==s&&(d.push({...s}),e.dispatchCommand(m.KfJ,!0));else if(2===f)return;t.current={editor:e,editorState:r}}))})(e,n,r),[r,e,n])}(r,t,e),null}let k="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?s.useLayoutEffect:s.useEffect;function _({ignoreHistoryMergeTagChange:e=!0,ignoreSelectionChange:t=!1,onChange:r}){let[n]=(0,f.DF)();return k(()=>{if(r)return n.registerUpdateListener(({editorState:i,dirtyElements:o,dirtyLeaves:l,prevEditorState:a,tags:s})=>{t&&0===o.size&&0===l.size||e&&s.has("history-merge")||a.isEmpty()||r(i,n,s)})},[n,e,t,r]),null}var N=r(17390);function F(e){return()=>(function(e){if(!function(e,t=!0){if(e)return!1;let r=(0,m.NiT)().getTextContent();return t&&(r=r.trim()),""===r}(e,!1))return!1;let t=(0,m.NiT)().getChildren(),r=t.length;if(r>1)return!1;for(let e=0;e<r;e++){let r=t[e];if((0,m.Cyw)(r))return!1;if((0,m.ff4)(r)){if(!(0,m.bSg)(r)||0!==r.__indent)return!1;let t=r.getChildren(),n=t.length;for(let r=0;r<n;r++){let r=t[e];if(!(0,m.kFe)(r))return!1}}}return!0})(e)}var P=r(69373),D=r(35141);let L="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?s.useLayoutEffect:s.useEffect;function R(e){return e.getEditorState().read(F(e.isComposing()))}function M({contentEditable:e,placeholder:t=null,ErrorBoundary:r}){var n;let[i]=(0,f.DF)(),l=function(e,t){let[r,n]=(0,s.useState)(()=>e.getDecorators());return L(()=>e.registerDecoratorListener(e=>{(0,P.flushSync)(()=>{n(e)})}),[e]),(0,s.useEffect)(()=>{n(e.getDecorators())},[e]),(0,s.useMemo)(()=>{let n=[],i=Object.keys(r);for(let l=0;l<i.length;l++){let a=i[l],u=(0,o.jsx)(t,{onError:t=>e._onError(t),children:(0,o.jsx)(s.Suspense,{fallback:null,children:r[a]})}),d=e.getElementByKey(a);null!==d&&n.push((0,P.createPortal)(u,d,a))}return n},[t,r,e])}(i,r);return L(()=>(0,S.Sd)((0,D.ZI)(n),function(e){let t=window.location.origin,r=r=>{if(r.origin!==t)return;let n=e.getRootElement();if(document.activeElement!==n)return;let i=r.data;if("string"==typeof i){let t;try{t=JSON.parse(i)}catch(e){return}if(t&&"nuanria_messaging"===t.protocol&&"request"===t.type){let n=t.payload;if(n&&"makeChanges"===n.functionId){let t=n.args;if(t){let[n,i,o,l,a,s]=t;e.update(()=>{let e=(0,m.vJq)();if((0,m.I2P)(e)){let t=e.anchor,s=t.getNode(),u=0,d=0;if((0,m.kFe)(s)&&n>=0&&i>=0&&(u=n,d=n+i,e.setTextNodeRange(s,u,s,d)),u===d&&""===o||(e.insertRawText(o),s=t.getNode()),(0,m.kFe)(s)){u=l,d=l+a;let t=s.getTextContentSize();u=u>t?t:u,d=d>t?t:d,e.setTextNodeRange(s,u,s,d)}r.stopImmediatePropagation()}})}}}}};return window.addEventListener("message",r,!0),()=>{window.removeEventListener("message",r,!0)}}(n)),[n=i]),(0,o.jsxs)(o.Fragment,{children:[e,(0,o.jsx)(T,{content:t}),l]})}function T({content:e}){let[t]=(0,f.DF)(),r=function(e){let[t,r]=(0,s.useState)(()=>R(e));return L(()=>{function t(){r(R(e))}return t(),(0,S.Sd)(e.registerUpdateListener(()=>{t()}),e.registerEditableListener(()=>{t()}))},[e]),t}(t),n=(0,N.a)();return r?"function"==typeof e?e(n):e:null}var J=r(93779);let B="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?s.useLayoutEffect:s.useEffect,K=(0,s.forwardRef)(function({editor:e,ariaActiveDescendant:t,ariaAutoComplete:r,ariaControls:n,ariaDescribedBy:i,ariaErrorMessage:l,ariaExpanded:a,ariaInvalid:u,ariaLabel:d,ariaLabelledBy:c,ariaMultiline:f,ariaOwns:m,ariaRequired:p,autoCapitalize:h,className:g,id:y,role:x="textbox",spellCheck:b=!0,style:v,tabIndex:E,"data-testid":C,...S},j){let[w,k]=(0,s.useState)(e.isEditable()),_=(0,s.useCallback)(t=>{t&&t.ownerDocument&&t.ownerDocument.defaultView?e.setRootElement(t):e.setRootElement(null)},[e]),N=(0,s.useMemo)(()=>(function(...e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}})(j,_),[_,j]);return B(()=>(k(e.isEditable()),e.registerEditableListener(e=>{k(e)})),[e]),(0,o.jsx)("div",{"aria-activedescendant":w?t:void 0,"aria-autocomplete":w?r:"none","aria-controls":w?n:void 0,"aria-describedby":i,...null!=l?{"aria-errormessage":l}:{},"aria-expanded":w&&"combobox"===x?!!a:void 0,...null!=u?{"aria-invalid":u}:{},"aria-label":d,"aria-labelledby":c,"aria-multiline":f,"aria-owns":w?m:void 0,"aria-readonly":!w||void 0,"aria-required":p,autoCapitalize:h,className:g,contentEditable:w,"data-testid":C,id:y,ref:N,role:w?x:void 0,spellCheck:b,style:v,tabIndex:E,...S})});function I(e){return e.getEditorState().read(F(e.isComposing()))}let O=(0,s.forwardRef)(function(e,t){let{placeholder:r,...n}=e,[i]=(0,f.DF)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(K,{editor:i,...n,ref:t}),null!=r&&(0,o.jsx)(A,{editor:i,content:r})]})});function A({content:e,editor:t}){let r=function(e){let[t,r]=(0,s.useState)(()=>I(e));return B(()=>{function t(){r(I(e))}return t(),(0,S.Sd)(e.registerUpdateListener(()=>{t()}),e.registerEditableListener(()=>{t()}))},[e]),t}(t),[n,i]=(0,s.useState)(t.isEditable());if((0,s.useLayoutEffect)(()=>(i(t.isEditable()),t.registerEditableListener(e=>{i(e)})),[t]),!r)return null;let l=null;return"function"==typeof e?l=e(n):null!==e&&(l=e),null===l?null:(0,o.jsx)("div",{"aria-hidden":!0,children:l})}var q=({anchorElem:e,clientProps:t,plugin:r})=>"floatingAnchorElem"===r.position&&e?r.Component&&(0,o.jsx)(r.Component,{anchorElem:e,clientProps:t}):r.Component&&(0,o.jsx)(r.Component,{clientProps:t});function $(){let e=(0,x.c)(3),[t]=(0,f.DF)(),r,n;return e[0]!==t?(r=()=>(0,S.Sd)(t.registerCommand(m.d8p,H,m.AcJ),t.registerCommand(m.w$Z,G,m.AcJ),t.registerCommand(m.gCZ,G,m.AcJ),t.registerCommand(m.MvL,U,m.AcJ),t.registerCommand(m.UDz,W,m.AcJ),t.registerCommand(m.bbF,z,m.AcJ)),n=[t],e[0]=t,e[1]=r,e[2]=n):(r=e[1],n=e[2]),null}function z(e){let t=(0,m.vJq)();if((0,m.RTZ)(t)){e.preventDefault();let r=t.getNodes()[0]?.getNextSibling();if((0,m.Cyw)(r)){let e=(0,m.i0_)().getElementByKey(r.getKey());return e&&Q({element:e,node:r}),!0}if(!(0,m.ff4)(r))return!0;let n=r.getFirstDescendant()??r;return n&&((0,S.Bt)(n,X)?.selectEnd(),e.preventDefault()),!0}if(!(0,m.I2P)(t))return!1;let r=(t.isBackward()?t.anchor:t.focus).getNode(),n=(0,S.Bt)(r,Z),i=n?.getNextSibling();if(!n||i!==ee(n))return!1;if((0,m.Cyw)(i)){let t=(0,m.i0_)().getElementByKey(i.getKey());if(t)return Q({element:t,node:i}),e.preventDefault(),!0}return!1}function Z(e){return null!==ee(e)}function W(e){let t=(0,m.vJq)();if((0,m.RTZ)(t)){let r=t.getNodes()[0]?.getPreviousSibling();if((0,m.Cyw)(r)){let t=(0,m.i0_)().getElementByKey(r.getKey());return!!t&&(Q({element:t,node:r}),e.preventDefault(),!0)}if(!(0,m.ff4)(r))return!1;let n=r.getLastDescendant()??r;return!!n&&((0,S.Bt)(n,X)?.selectStart(),e.preventDefault(),!0)}if(!(0,m.I2P)(t))return!1;let r=(t.isBackward()?t.anchor:t.focus).getNode(),n=(0,S.Bt)(r,V),i=n?.getPreviousSibling();if(!n||i!==et(n))return!1;if((0,m.Cyw)(i)){let t=(0,m.i0_)().getElementByKey(i.getKey());if(t)return Q({element:t,node:i}),e.preventDefault(),!0}return!1}function V(e){return null!==et(e)}function U(){let e=function(){let e=(0,m.vJq)();if(!(0,m.RTZ)(e))return;let t=e.getNodes();if(1!==t.length)return;let r=t[0];return(0,m.Cyw)(r)?{decorator:r,element:(0,m.i0_)().getElementByKey(r.getKey())}:void 0}();return document.querySelector(".decorator-selected")?.classList.remove("decorator-selected"),!!e&&(e.element?.classList.add("decorator-selected"),!0)}function H(e){document.querySelector(".decorator-selected")?.classList.remove("decorator-selected");let t=function(e){if(!(e.target instanceof HTMLElement))return;let t=e.target.closest('[data-lexical-decorator="true"]');if(!(t instanceof HTMLElement))return;let r=(0,m.xL4)(t);return(0,m.Cyw)(r)?{element:t,node:r}:void 0}(e);if(!t)return!0;let{target:r}=e;return!(r instanceof HTMLElement)||r.isContentEditable||r.closest('button, textarea, input, .react-select, .code-editor, .no-select-decorator, [role="button"]')?(0,m.n1P)(null):Q(t),!0}function G(e){let t=(0,m.vJq)();return!!(0,m.RTZ)(t)&&(e.preventDefault(),t.getNodes().forEach(Y),!0)}function Y(e){e.remove()}function Q({element:e,node:t}){document.querySelector(".decorator-selected")?.classList.remove("decorator-selected");let r=(0,m.rUs)();r.add(t.getKey()),(0,m.n1P)(r),e.scrollIntoView({behavior:"smooth",block:"nearest"}),e.classList.add("decorator-selected")}function X(e){if((0,m.Cyw)(e)&&!e.isInline())return!0;if(!(0,m.ff4)(e)||(0,m.IqF)(e))return!1;let t=e.getFirstChild(),r=null===t||(0,m.wH$)(t)||(0,m.kFe)(t)||t.isInline();return!e.isInline()&&!1!==e.canBeEmpty()&&r}function ee(e){let t=e.getNextSibling();for(;null!==t;){if(X(t))return t;t=t.getNextSibling()}return null}function et(e){let t=e.getPreviousSibling();for(;null!==t;){if(X(t))return t;t=t.getPreviousSibling()}return null}function er(e){let{anchorElem:t}=e,r=void 0===t?document.body:t,[l]=(0,f.DF)();return function(e,t,r){t.parentElement;let{editorConfig:l}=(0,i.b)(),a=(l?.admin?.hideGutter,(0,s.useRef)(null)),[u,d]=(0,s.useState)(null),c=(0,s.useCallback)(t=>{let r=u;r?.node&&(e.update(()=>{let t=!0;if((r?.node.getType()!=="paragraph"||""!==r.node.getTextContent())&&(t=!1),!t){let t=(0,m.lJ7)();r?.node.insertAfter(t),setTimeout(()=>{d(r={elem:e.getElementByKey(t.getKey()),node:t})},0)}}),setTimeout(()=>{e.update(()=>{e.focus(),r?.node&&"select"in r.node&&"function"==typeof r.node.select&&r.node.select()})},1),setTimeout(()=>{e.dispatchCommand(n.i,{node:r?.node})},2),t.stopPropagation(),t.preventDefault())},[e,u]);return(0,P.createPortal)((0,o.jsx)(s.Fragment,{children:(0,o.jsx)("button",{"aria-label":"Add block",className:"icon add-block-menu",onClick:e=>{c(e)},ref:a,type:"button",children:(0,o.jsx)("div",{className:r?"icon":""})})}),t)}(l,r,l._editable)}function en(e){let{anchorElem:t}=e,r=void 0===t?document.body:t,[n]=(0,f.DF)();return function(e,t,r){t.parentElement;let n=(0,s.useRef)(null),l=(0,s.useRef)(null),a=(0,s.useRef)(null),u=(0,s.useRef)(!1),[d,c]=(0,s.useState)(null),[f,p]=(0,s.useState)(null),{editorConfig:h}=(0,i.b)();return h?.admin?.hideGutter,(0,P.createPortal)((0,o.jsxs)(s.Fragment,{children:[(0,o.jsx)("div",{className:"icon draggable-block-menu",draggable:!0,onDragEnd:function(){var e,t;u.current=!1,f?.elem&&(e=l.current,t=f?.elem,e&&(e.style.opacity="0"),t&&(t.style.opacity="",t.style.marginBottom="",t.style.marginTop=""))},onDragStart:function(t){let r=t.dataTransfer;if(!r||!d)return;!function(e,t){let{transform:r}=t.style;e.setDragImage(t,0,0),setTimeout(()=>{t.style.transform=r})}(r,d);let n="";e.update(()=>{let e=(0,m.xL4)(d);e&&(n=e.getKey())}),u.current=!0,r.setData("application/x-lexical-drag-block",n)},ref:n,children:(0,o.jsx)("div",{className:r?"icon":""})}),(0,o.jsx)("div",{className:"draggable-block-target-line",ref:l}),(0,o.jsx)("div",{className:"debug-highlight",ref:a})]}),t)}(n,r,n._editable)}var ei="insert-paragraph-at-end",eo=()=>{let e,t=(0,x.c)(4),[r]=(0,f.DF)(),{editorConfig:n}=(0,i.b)();if(n?.admin?.hideInsertParagraphAtEnd)return null;t[0]!==r?(e=()=>{r.update(el)},t[0]=r,t[1]=e):e=t[1];let l=e,a;return t[2]!==l?(a=(0,o.jsx)("div",{"aria-label":"Insert Paragraph",className:ei,onClick:l,role:"button",tabIndex:0,children:(0,o.jsx)("div",{className:`${ei}-inside`,children:(0,o.jsx)("span",{children:"+"})})}),t[2]=l,t[3]=a):a=t[3],a};function el(){let e=(0,m.lJ7)();(0,m.NiT)().append(e),e.select()}var ea=()=>{let e=(0,x.c)(4),{editorConfig:t}=(0,i.b)(),[r]=(0,f.DF)(),o,l;return e[0]!==r||e[1]!==t.features.markdownTransformers?(o=()=>(0,n.c)(r,t.features.markdownTransformers??[]),l=[r,t.features.markdownTransformers],e[0]=r,e[1]=t.features.markdownTransformers,e[2]=o,e[3]=l):(o=e[2],l=e[3]),s.useEffect(o,l),null};function es(){let[e]=(0,f.DF)();return null}function eu(){let[e]=(0,f.DF)();return null}var ed="slash-menu-popup";function ec({isSelected:e,item:t,onClick:r,onMouseEnter:n,ref:l}){let{fieldProps:{featureClientSchemaMap:a,schemaPath:s}}=(0,i.b)(),{i18n:u}=(0,J.d)(),d=`${ed}__item ${ed}__item-${t.key}`;e&&(d+=` ${ed}__item--selected`);let c=t.key;return t.label&&(c="function"==typeof t.label?t.label({featureClientSchemaMap:a,i18n:u,schemaPath:s}):t.label),c.length>25&&(c=c.substring(0,25)+"..."),(0,o.jsxs)("button",{"aria-selected":e,className:d,id:ed+"__item-"+t.key,onClick:r,onMouseEnter:n,ref:l,role:"option",tabIndex:-1,type:"button",children:[t?.Icon&&(0,o.jsx)(t.Icon,{}),(0,o.jsx)("span",{className:`${ed}__item-text`,children:c})]},t.key)}function ef({anchorElem:e=document.body}){let[t]=(0,f.DF)(),[r,l]=(0,s.useState)(null),{editorConfig:a}=(0,i.b)(),{i18n:u}=(0,J.d)(),{fieldProps:{featureClientSchemaMap:d,schemaPath:c}}=(0,i.b)(),m=function(e,t){let r=(0,x.c)(4),{maxLength:i,minLength:o}=t,l=void 0===i?75:i,a=void 0===o?1:o,s;return r[0]!==l||r[1]!==a||"/"!==r[2]?(s=t=>{let{query:r}=t,i="[^/"+n.h+"\\s]",o=RegExp("(^|\\s|\\()(["+e+"]((?:"+i+"){0,"+l+"}))$").exec(r);if(null!==o){let e=o[1],t=o[3];if(t.length>=a)return{leadOffset:o.index+e.length,matchingString:t,replaceableString:o[2]}}return null},r[0]=l,r[1]=a,r[2]=e,r[3]=s):s=r[3],s}("/",{minLength:0}),p=(0,s.useCallback)(()=>{let e=[];for(let n of a.features.slashMenu.dynamicGroups)if(r){let i=n({editor:t,queryString:r});e=e.concat(i)}return e},[t,r,a?.features]),h=(0,s.useMemo)(()=>{let e=[];for(let t of a?.features.slashMenu.groups??[])e.push(t);if(r)for(let t of(e=(e=e.map(e=>{let t=e.items.filter(e=>{let t=e.key;return e.label&&(t="function"==typeof e.label?e.label({featureClientSchemaMap:d,i18n:u,schemaPath:c}):e.label),!!RegExp(r,"gi").exec(t)||null!=e.keywords&&e.keywords.some(e=>RegExp(r,"gi").exec(e))});return t.length?{...e,items:t}:null})).filter(e=>null!=e),p())){let r=e.find(e=>e.key===t.key);r?e=e.filter(e=>e.key!==t.key):r={...t,items:[]},r?.items?.length&&(r.items=r.items.concat(r.items)),e.push(r)}return e},[r,a?.features.slashMenu.groups,p,d,u,c]);return(0,o.jsx)(n.j,{anchorElem:e,groups:h,menuRenderFn:(e,{selectedItemKey:t,selectItemAndCleanUp:r,setSelectedItemKey:n})=>e.current&&h.length?P.createPortal((0,o.jsx)("div",{className:ed,children:h.map(e=>{let i=e.key;return e.label&&d&&(i="function"==typeof e.label?e.label({featureClientSchemaMap:d,i18n:u,schemaPath:c}):e.label),(0,o.jsxs)("div",{className:`${ed}__group ${ed}__group-${e.key}`,children:[(0,o.jsx)("div",{className:`${ed}__group-title`,children:i}),e.items.map((e,i)=>(0,o.jsx)(ec,{index:i,isSelected:t===e.key,item:e,onClick:()=>{n(e.key),r(e)},onMouseEnter:()=>{n(e.key)},ref:t=>{e.ref={current:t}}},e.key))]},e.key)})}),e.current):null,onQueryChange:l,triggerFn:m})}function em(e){let t,r=(0,x.c)(6),{features:n}=e,[i]=(0,f.DF)(),o;return r[0]!==i||r[1]!==n.enabledFormats?(o=()=>{var e;let t,r,o=(e=n.enabledFormats,t=Object.keys(m.LZn),r=new Set(e),t.filter(e=>!r.has(e)));if(0!==o.length)return i.registerNodeTransform(m.Ey8,e=>{o.forEach(t=>{e.hasFormat(t)&&e.toggleFormat(t)})})},r[0]=i,r[1]=n.enabledFormats,r[2]=o):o=r[2],r[3]!==i||r[4]!==n?(t=[i,n],r[3]=i,r[4]=n,r[5]=t):t=r[5],null}function ep(e){let t=(0,x.c)(7),{className:r,editorConfig:n}=e,{t:i}=(0,J.d)(),l;if(t[0]!==r||t[1]!==n?.admin?.placeholder||t[2]!==i){let e;t[4]!==n?.admin?.placeholder||t[5]!==i?(e=n?.admin?.placeholder??i("lexical:general:placeholder"),t[4]=n?.admin?.placeholder,t[5]=i,t[6]=e):e=t[6],l=(0,o.jsx)(O,{"aria-placeholder":i("lexical:general:placeholder"),className:r??"ContentEditable__root",placeholder:(0,o.jsx)("p",{className:"editor-placeholder",children:e})}),t[0]=r,t[1]=n?.admin?.placeholder,t[2]=i,t[3]=l}else l=t[3];return l}var eh=e=>{let t,r=(0,x.c)(19),{editorConfig:n,editorContainerRef:l,isSmallWidthViewport:a,onChange:u}=e,d=(0,i.b)(),[c]=(0,f.DF)(),[p,h]=(0,s.useState)(null),g;r[0]===Symbol.for("react.memo_cache_sentinel")?(g=e=>{null!==e&&h(e)},r[0]=g):g=r[0];let y=g,b,v;if(r[1]!==c||r[2]!==d?(b=()=>{if(!d?.uuid)return void console.error("Lexical Editor must be used within an EditorConfigProvider");d?.parentEditor?.uuid&&d.parentEditor?.registerChild(d.uuid,d);let e=()=>{d.focusEditor(d)},t=()=>{d.blurEditor(d)},r=c.registerCommand(m.$7r,()=>(e(),!0),m.AcJ),n=c.registerCommand(m.I2H,()=>(t(),!0),m.AcJ);return()=>{r(),n(),d.parentEditor?.unregisterChild?.(d.uuid)}},v=[c,d],r[1]=c,r[2]=d,r[3]=b,r[4]=v):(b=r[3],v=r[4]),(0,s.useEffect)(b,v),r[5]!==c||r[6]!==n||r[7]!==l||r[8]!==p||r[9]!==a||r[10]!==u){let e,i;r[12]!==u?(e=(e,t,r)=>{(!r.has("focus")||r.size>1)&&u?.(e,t,r)},r[12]=u,r[13]=e):e=r[13],r[14]!==c||r[15]!==n.features.plugins||r[16]!==p||r[17]!==a?(i=p&&(0,o.jsxs)(s.Fragment,{children:[!a&&c.isEditable()&&(0,o.jsxs)(s.Fragment,{children:[(0,o.jsx)(en,{anchorElem:p}),(0,o.jsx)(er,{anchorElem:p})]}),n.features.plugins?.map(e=>{if("floatingAnchorElem"===e.position&&!(!0===e.desktopOnly&&a))return(0,o.jsx)(q,{anchorElem:p,clientProps:e.clientProps,plugin:e},e.key)}),c.isEditable()&&(0,o.jsx)(s.Fragment,{children:(0,o.jsx)(ef,{anchorElem:p})})]}),r[14]=c,r[15]=n.features.plugins,r[16]=p,r[17]=a,r[18]=i):i=r[18],t=(0,o.jsxs)(s.Fragment,{children:[n.features.plugins?.map(eg),(0,o.jsxs)("div",{className:"editor-container",ref:l,children:[n.features.plugins?.map(ey),(0,o.jsx)(M,{contentEditable:(0,o.jsx)("div",{className:"editor-scroller",children:(0,o.jsx)("div",{className:"editor",ref:y,children:(0,o.jsx)(ep,{editorConfig:n})})}),ErrorBoundary:C}),(0,o.jsx)(es,{}),(0,o.jsx)(eo,{}),(0,o.jsx)($,{}),(0,o.jsx)(em,{features:n.features}),(0,o.jsx)(eu,{}),(0,o.jsx)(_,{ignoreSelectionChange:!0,onChange:e}),i,c.isEditable()&&(0,o.jsxs)(s.Fragment,{children:[(0,o.jsx)(w,{}),n?.features?.markdownTransformers?.length>0&&(0,o.jsx)(ea,{})]}),n.features.plugins?.map(ex),n.features.plugins?.map(eb)]}),n.features.plugins?.map(ev)]}),r[5]=c,r[6]=n,r[7]=l,r[8]=p,r[9]=a,r[10]=u,r[11]=t}else t=r[11];return t};function eg(e){if("aboveContainer"===e.position)return(0,o.jsx)(q,{clientProps:e.clientProps,plugin:e},e.key)}function ey(e){if("top"===e.position)return(0,o.jsx)(q,{clientProps:e.clientProps,plugin:e},e.key)}function ex(e){if("normal"===e.position)return(0,o.jsx)(q,{clientProps:e.clientProps,plugin:e},e.key)}function eb(e){if("bottom"===e.position)return(0,o.jsx)(q,{clientProps:e.clientProps,plugin:e},e.key)}function ev(e){if("belowContainer"===e.position)return(0,o.jsx)(q,{clientProps:e.clientProps,plugin:e},e.key)}var eE=({children:e,providers:t})=>{if(!t?.length)return e;let r=t[0];return t.length>1?(0,o.jsx)(r,{children:(0,o.jsx)(eE,{providers:t.slice(1),children:e})}):(0,o.jsx)(r,{children:e})},eC=e=>{let{composerKey:t,editorConfig:r,fieldProps:a,isSmallWidthViewport:u,onChange:d,readOnly:c,value:f}=e,m=(0,i.b)(),p=(0,l.useEditDepth)(),h=s.useRef(null),g=(0,s.useMemo)(()=>{if(f&&"object"!=typeof f)throw Error("The value passed to the Lexical editor is not an object. This is not supported. Please remove the data from the field and start again. This is the value that was passed in: "+JSON.stringify(f));if(f&&Array.isArray(f)&&!("root"in f))throw Error("You have tried to pass in data from the old Slate editor to the new Lexical editor. The data structure is different, thus you will have to migrate your data. We offer a one-line migration script which migrates all your rich text fields: https://payloadcms.com/docs/lexical/migration#migration-via-migration-script-recommended");if(f&&"jsonContent"in f)throw Error("You have tried to pass in data from payload-plugin-lexical. The data structure is different, thus you will have to migrate your data. Migration guide: https://payloadcms.com/docs/lexical/migration#migrating-from-payload-plugin-lexical");return{editable:!0!==c,editorState:null!=f?JSON.stringify(f):void 0,namespace:r.lexical.namespace,nodes:(0,n.l)({editorConfig:r}),onError:e=>{throw e},theme:r.lexical.theme}},[r]);return g?(0,o.jsx)(y,{initialConfig:g,children:(0,o.jsx)(i.a,{editorConfig:r,editorContainerRef:h,fieldProps:a,parentContext:m?.editDepth===p?m:void 0,children:(0,o.jsx)(eE,{providers:r.features.providers,children:(0,o.jsx)(eh,{editorConfig:r,editorContainerRef:h,isSmallWidthViewport:u,onChange:d})})})},t+g.editable):(0,o.jsx)("p",{children:"Loading..."})},eS="rich-text-lexical";function ej({error:e}){return(0,o.jsxs)("div",{className:"errorBoundary",role:"alert",children:[(0,o.jsx)("p",{children:"Something went wrong:"}),(0,o.jsx)("pre",{style:{color:"red"},children:e.message})]})}var ew=e=>{let{editorConfig:t,field:r,field:{admin:{className:i,description:u,readOnly:d}={},label:f,localized:m,required:p},path:h,readOnly:g,validate:y}=e,x=(0,l.useEditDepth)(),b=(0,s.useCallback)((e,t)=>"function"!=typeof y||y(e,{...t,required:p}),[y,p]),{customComponents:{AfterInput:v,BeforeInput:E,Description:C,Error:S,Label:j}={},disabled:w,initialValue:k,path:_,setValue:N,showError:F,value:P}=(0,l.useField)({potentiallyStalePath:h,validate:b}),D=g||d||w,[L,R]=(0,s.useState)(!1),[M,T]=(0,s.useState)(),J=s.useRef(k),B=s.useRef(P);(0,s.useEffect)(()=>{let e=()=>{let e=window.matchMedia("(max-width: 768px)").matches;e!==L&&R(e)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[L]);let K=[eS,"field-type",i,F&&"error",D&&`${eS}--read-only`,t?.admin?.hideGutter!==!0&&!L?`${eS}--show-gutter`:null].filter(Boolean).join(" "),I=`${_}.${x}`,O=(0,n.b)(),A=(0,s.useCallback)(e=>{O(()=>{let t=e.toJSON();B.current=t,N(t)})},[N,O]),q=(0,s.useMemo)(()=>(0,a.FR)(r),[r]),$=(0,l.useEffectEvent)(e=>{B.current!==P&&JSON.stringify(B.current)!==JSON.stringify(P)&&(J.current=e,B.current=P,T(new Date))});return(0,s.useEffect)(()=>{Object.is(k,J.current)||$(k)},[k]),(0,o.jsxs)("div",{className:K,style:q,children:[(0,o.jsx)(l.RenderCustomComponent,{CustomComponent:S,Fallback:(0,o.jsx)(l.FieldError,{path:_,showError:F})}),j||(0,o.jsx)(l.FieldLabel,{label:f,localized:m,path:_,required:p}),(0,o.jsxs)("div",{className:`${eS}__wrap`,children:[(0,o.jsxs)(c,{fallbackRender:ej,onReset:()=>{},children:[E,(0,o.jsx)(eC,{composerKey:I,editorConfig:t,fieldProps:e,isSmallWidthViewport:L,onChange:A,readOnly:D,value:P},JSON.stringify({path:_,rerenderProviderKey:M})),v]}),C,(0,o.jsx)(l.RenderCustomComponent,{CustomComponent:C,Fallback:(0,o.jsx)(l.FieldDescription,{description:u,path:_})})]})]},I)}},99930:()=>{}};