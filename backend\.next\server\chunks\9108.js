"use strict";exports.id=9108,exports.ids=[9108],exports.modules={22584:(a,e,t)=>{function n(a){return(e,t={})=>{let n,i=t.width,r=i&&a.matchPatterns[i]||a.matchPatterns[a.defaultMatchWidth],o=e.match(r);if(!o)return null;let l=o[0],s=i&&a.parsePatterns[i]||a.parsePatterns[a.defaultParseWidth],d=Array.isArray(s)?function(a,e){for(let t=0;t<a.length;t++)if(e(a[t]))return t}(s,a=>a.test(l)):function(a,e){for(let t in a)if(Object.prototype.hasOwnProperty.call(a,t)&&e(a[t]))return t}(s,a=>a.test(l));return n=a.valueCallback?a.valueCallback(d):d,{value:n=t.valueCallback?t.valueCallback(n):n,rest:e.slice(l.length)}}}t.d(e,{A:()=>n})},43416:(a,e,t)=>{t.d(e,{K:()=>n});function n(a){return(e,t={})=>{let n=e.match(a.matchPattern);if(!n)return null;let i=n[0],r=e.match(a.parsePattern);if(!r)return null;let o=a.valueCallback?a.valueCallback(r[0]):r[0];return{value:o=t.valueCallback?t.valueCallback(o):o,rest:e.slice(i.length)}}}},79108:(a,e,t)=>{t.r(e),t.d(e,{default:()=>m,tr:()=>u});let n={lessThanXSeconds:{one:"bir saniyeden az",other:"{{count}} saniyeden az"},xSeconds:{one:"1 saniye",other:"{{count}} saniye"},halfAMinute:"yarım dakika",lessThanXMinutes:{one:"bir dakikadan az",other:"{{count}} dakikadan az"},xMinutes:{one:"1 dakika",other:"{{count}} dakika"},aboutXHours:{one:"yaklaşık 1 saat",other:"yaklaşık {{count}} saat"},xHours:{one:"1 saat",other:"{{count}} saat"},xDays:{one:"1 g\xfcn",other:"{{count}} g\xfcn"},aboutXWeeks:{one:"yaklaşık 1 hafta",other:"yaklaşık {{count}} hafta"},xWeeks:{one:"1 hafta",other:"{{count}} hafta"},aboutXMonths:{one:"yaklaşık 1 ay",other:"yaklaşık {{count}} ay"},xMonths:{one:"1 ay",other:"{{count}} ay"},aboutXYears:{one:"yaklaşık 1 yıl",other:"yaklaşık {{count}} yıl"},xYears:{one:"1 yıl",other:"{{count}} yıl"},overXYears:{one:"1 yıldan fazla",other:"{{count}} yıldan fazla"},almostXYears:{one:"neredeyse 1 yıl",other:"neredeyse {{count}} yıl"}};var i=t(89500);let r={date:(0,i.k)({formats:{full:"d MMMM y EEEE",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.yyyy"},defaultWidth:"full"}),time:(0,i.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,i.k)({formats:{full:"{{date}} 'saat' {{time}}",long:"{{date}} 'saat' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"'ge\xe7en hafta' eeee 'saat' p",yesterday:"'d\xfcn saat' p",today:"'bug\xfcn saat' p",tomorrow:"'yarın saat' p",nextWeek:"eeee 'saat' p",other:"P"};var l=t(84246);let s={ordinalNumber:(a,e)=>Number(a)+".",era:(0,l.o)({values:{narrow:["M\xd6","MS"],abbreviated:["M\xd6","MS"],wide:["Milattan \xd6nce","Milattan Sonra"]},defaultWidth:"wide"}),quarter:(0,l.o)({values:{narrow:["1","2","3","4"],abbreviated:["1\xc7","2\xc7","3\xc7","4\xc7"],wide:["İlk \xe7eyrek","İkinci \xc7eyrek","\xdc\xe7\xfcnc\xfc \xe7eyrek","Son \xe7eyrek"]},defaultWidth:"wide",argumentCallback:a=>Number(a)-1}),month:(0,l.o)({values:{narrow:["O","Ş","M","N","M","H","T","A","E","E","K","A"],abbreviated:["Oca","Şub","Mar","Nis","May","Haz","Tem","Ağu","Eyl","Eki","Kas","Ara"],wide:["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eyl\xfcl","Ekim","Kasım","Aralık"]},defaultWidth:"wide"}),day:(0,l.o)({values:{narrow:["P","P","S","\xc7","P","C","C"],short:["Pz","Pt","Sa","\xc7a","Pe","Cu","Ct"],abbreviated:["Paz","Pzt","Sal","\xc7ar","Per","Cum","Cts"],wide:["Pazar","Pazartesi","Salı","\xc7arşamba","Perşembe","Cuma","Cumartesi"]},defaultWidth:"wide"}),dayPeriod:(0,l.o)({values:{narrow:{am:"\xf6\xf6",pm:"\xf6s",midnight:"gy",noon:"\xf6",morning:"sa",afternoon:"\xf6s",evening:"ak",night:"ge"},abbreviated:{am:"\xd6\xd6",pm:"\xd6S",midnight:"gece yarısı",noon:"\xf6ğle",morning:"sabah",afternoon:"\xf6ğleden sonra",evening:"akşam",night:"gece"},wide:{am:"\xd6.\xd6.",pm:"\xd6.S.",midnight:"gece yarısı",noon:"\xf6ğle",morning:"sabah",afternoon:"\xf6ğleden sonra",evening:"akşam",night:"gece"}},defaultWidth:"wide",formattingValues:{narrow:{am:"\xf6\xf6",pm:"\xf6s",midnight:"gy",noon:"\xf6",morning:"sa",afternoon:"\xf6s",evening:"ak",night:"ge"},abbreviated:{am:"\xd6\xd6",pm:"\xd6S",midnight:"gece yarısı",noon:"\xf6ğlen",morning:"sabahleyin",afternoon:"\xf6ğleden sonra",evening:"akşamleyin",night:"geceleyin"},wide:{am:"\xf6.\xf6.",pm:"\xf6.s.",midnight:"gece yarısı",noon:"\xf6ğlen",morning:"sabahleyin",afternoon:"\xf6ğleden sonra",evening:"akşamleyin",night:"geceleyin"}},defaultFormattingWidth:"wide"})};var d=t(22584);let u={code:"tr",formatDistance:(a,e,t)=>{let i,r=n[a];if(i="string"==typeof r?r:1===e?r.one:r.other.replace("{{count}}",e.toString()),t?.addSuffix)if(t.comparison&&t.comparison>0)return i+" sonra";else return i+" \xf6nce";return i},formatLong:r,formatRelative:(a,e,t,n)=>o[a],localize:s,match:{ordinalNumber:(0,t(43416).K)({matchPattern:/^(\d+)(\.)?/i,parsePattern:/\d+/i,valueCallback:function(a){return parseInt(a,10)}}),era:(0,d.A)({matchPatterns:{narrow:/^(mö|ms)/i,abbreviated:/^(mö|ms)/i,wide:/^(milattan önce|milattan sonra)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(^mö|^milattan önce)/i,/(^ms|^milattan sonra)/i]},defaultParseWidth:"any"}),quarter:(0,d.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^[1234]ç/i,wide:/^((i|İ)lk|(i|İ)kinci|üçüncü|son) çeyrek/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i],abbreviated:[/1ç/i,/2ç/i,/3ç/i,/4ç/i],wide:[/^(i|İ)lk çeyrek/i,/(i|İ)kinci çeyrek/i,/üçüncü çeyrek/i,/son çeyrek/i]},defaultParseWidth:"any",valueCallback:a=>a+1}),month:(0,d.A)({matchPatterns:{narrow:/^[oşmnhtaek]/i,abbreviated:/^(oca|şub|mar|nis|may|haz|tem|ağu|eyl|eki|kas|ara)/i,wide:/^(ocak|şubat|mart|nisan|mayıs|haziran|temmuz|ağustos|eylül|ekim|kasım|aralık)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^o/i,/^ş/i,/^m/i,/^n/i,/^m/i,/^h/i,/^t/i,/^a/i,/^e/i,/^e/i,/^k/i,/^a/i],any:[/^o/i,/^ş/i,/^mar/i,/^n/i,/^may/i,/^h/i,/^t/i,/^ağ/i,/^ey/i,/^ek/i,/^k/i,/^ar/i]},defaultParseWidth:"any"}),day:(0,d.A)({matchPatterns:{narrow:/^[psçc]/i,short:/^(pz|pt|sa|ça|pe|cu|ct)/i,abbreviated:/^(paz|pzt|sal|çar|per|cum|cts)/i,wide:/^(pazar(?!tesi)|pazartesi|salı|çarşamba|perşembe|cuma(?!rtesi)|cumartesi)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^p/i,/^p/i,/^s/i,/^ç/i,/^p/i,/^c/i,/^c/i],any:[/^pz/i,/^pt/i,/^sa/i,/^ça/i,/^pe/i,/^cu/i,/^ct/i],wide:[/^pazar(?!tesi)/i,/^pazartesi/i,/^salı/i,/^çarşamba/i,/^perşembe/i,/^cuma(?!rtesi)/i,/^cumartesi/i]},defaultParseWidth:"any"}),dayPeriod:(0,d.A)({matchPatterns:{narrow:/^(öö|ös|gy|ö|sa|ös|ak|ge)/i,any:/^(ö\.?\s?[ös]\.?|öğleden sonra|gece yarısı|öğle|(sabah|öğ|akşam|gece)(leyin))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^ö\.?ö\.?/i,pm:/^ö\.?s\.?/i,midnight:/^(gy|gece yarısı)/i,noon:/^öğ/i,morning:/^sa/i,afternoon:/^öğleden sonra/i,evening:/^ak/i,night:/^ge/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:1}},m=u},84246:(a,e,t)=>{t.d(e,{o:()=>n});function n(a){return(e,t)=>{let n;if("formatting"===(t?.context?String(t.context):"standalone")&&a.formattingValues){let e=a.defaultFormattingWidth||a.defaultWidth,i=t?.width?String(t.width):e;n=a.formattingValues[i]||a.formattingValues[e]}else{let e=a.defaultWidth,i=t?.width?String(t.width):a.defaultWidth;n=a.values[i]||a.values[e]}return n[a.argumentCallback?a.argumentCallback(e):e]}}},89500:(a,e,t)=>{t.d(e,{k:()=>n});function n(a){return (e={})=>{let t=e.width?String(e.width):a.defaultWidth;return a.formats[t]||a.formats[a.defaultWidth]}}}};