(()=>{var t={};t.id=8139,t.ids=[8139],t.modules={643:t=>{"use strict";t.exports=require("node:perf_hooks")},1065:()=>{},1708:t=>{"use strict";t.exports=require("node:process")},2712:()=>{},4573:t=>{"use strict";t.exports=require("node:buffer")},4984:t=>{"use strict";t.exports=require("readline")},6345:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){return{script:"\n    const loadingWrapper = document.getElementById('loading-wrapper');\n    if (loadingWrapper) {\n      loadingWrapper.classList.add('fadeOut');\n    }\n    ",container:'\n<style type="text/css">\n.fadeOut {\n  -webkit-animation: fadeOut 0.5s ease-out forwards;\n  animation: fadeOut 0.5s ease-out forwards;\n}\n\n@-webkit-keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@-webkit-keyframes fadeOut {\n  from {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n  to {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n  to {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n}\n\n@-webkit-keyframes appearIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    transform: translateY(0px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@keyframes appearIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    transform: translateY(0px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@-webkit-keyframes scaleIn {\n  from {\n    -webkit-transform: scale(0);\n    -ms-transform: scale(0);\n    transform: scale(0);\n  }\n  to {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n  }\n}\n\n@keyframes scaleIn {\n  from {\n    -webkit-transform: scale(0);\n    -ms-transform: scale(0);\n    transform: scale(0);\n  }\n  to {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n  }\n}\n\n@-webkit-keyframes innerDrawIn {\n  0% {\n    stroke-dashoffset: 70;\n  }\n  50% {\n    stroke-dashoffset: 140;\n  }\n  100% {\n    stroke-dashoffset: 210;\n  }\n}\n\n@keyframes innerDrawIn {\n  0% {\n    stroke-dashoffset: 70;\n  }\n  50% {\n    stroke-dashoffset: 140;\n  }\n  100% {\n    stroke-dashoffset: 210;\n  }\n}\n\n@-webkit-keyframes outerDrawIn {\n  0% {\n    stroke-dashoffset: 76;\n  }\n  100% {\n    stroke-dashoffset: 152;\n  }\n}\n\n@keyframes outerDrawIn {\n  0% {\n    stroke-dashoffset: 76;\n  }\n  100% {\n    stroke-dashoffset: 152;\n  }\n}\n\n.hHWjkv {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n}\n\n.gCDOzd {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n}\n\n.hmCcxi {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n}\n\n.eHamQi {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n  animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n}\n\n.byhgGu {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n  animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n}\n\n.llAKP {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n  animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n}\n\n.bglIGM {\n  -webkit-transform-origin: 64px 28px;\n  -ms-transform-origin: 64px 28px;\n  transform-origin: 64px 28px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n}\n\n.ksxRII {\n  -webkit-transform-origin: 95.98500061035156px 46.510000228881836px;\n  -ms-transform-origin: 95.98500061035156px 46.510000228881836px;\n  transform-origin: 95.98500061035156px 46.510000228881836px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n}\n\n.cWrBmb {\n  -webkit-transform-origin: 95.97162628173828px 83.4900016784668px;\n  -ms-transform-origin: 95.97162628173828px 83.4900016784668px;\n  transform-origin: 95.97162628173828px 83.4900016784668px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n}\n\n.Wnusb {\n  -webkit-transform-origin: 64px 101.97999572753906px;\n  -ms-transform-origin: 64px 101.97999572753906px;\n  transform-origin: 64px 101.97999572753906px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n  animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n}\n\n.bfPqf {\n  -webkit-transform-origin: 32.03982162475586px 83.4900016784668px;\n  -ms-transform-origin: 32.03982162475586px 83.4900016784668px;\n  transform-origin: 32.03982162475586px 83.4900016784668px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n  animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n}\n\n.edRCTN {\n  -webkit-transform-origin: 32.033552169799805px 46.510000228881836px;\n  -ms-transform-origin: 32.033552169799805px 46.510000228881836px;\n  transform-origin: 32.033552169799805px 46.510000228881836px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n  animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n}\n\n.iEGVWn {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.3333333333333333s, appearIn 0.1s ease-out forwards 0.3333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.3333333333333333s, appearIn 0.1s ease-out forwards 0.3333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.bsocdx {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.5333333333333333s, appearIn 0.1s ease-out forwards 0.5333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.5333333333333333s, appearIn 0.1s ease-out forwards 0.5333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.jAZXmP {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.7333333333333334s, appearIn 0.1s ease-out forwards 0.7333333333333334s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.7333333333333334s, appearIn 0.1s ease-out forwards 0.7333333333333334s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.hSeArx {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.9333333333333333s, appearIn 0.1s ease-out forwards 0.9333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.9333333333333333s, appearIn 0.1s ease-out forwards 0.9333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.bVgqGk {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 1.1333333333333333s, appearIn 0.1s ease-out forwards 1.1333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 1.1333333333333333s, appearIn 0.1s ease-out forwards 1.1333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.hEFqBt {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 1.3333333333333333s, appearIn 0.1s ease-out forwards 1.3333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 1.3333333333333333s, appearIn 0.1s ease-out forwards 1.3333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.dzEKCM {\n  opacity: 0;\n  stroke-dasharray: 70;\n  -webkit-animation: innerDrawIn 1s ease-in-out forwards 1.3666666666666667s, appearIn 0.1s linear forwards 1.3666666666666667s;\n  animation: innerDrawIn 1s ease-in-out forwards 1.3666666666666667s, appearIn 0.1s linear forwards 1.3666666666666667s;\n  -webkit-animation-iteration-count: infinite, 1;\n  animation-iteration-count: infinite, 1;\n}\n\n.DYnPx {\n  opacity: 0;\n  stroke-dasharray: 70;\n  -webkit-animation: innerDrawIn 1s ease-in-out forwards 1.5333333333333332s, appearIn 0.1s linear forwards 1.5333333333333332s;\n  animation: innerDrawIn 1s ease-in-out forwards 1.5333333333333332s, appearIn 0.1s linear forwards 1.5333333333333332s;\n  -webkit-animation-iteration-count: infinite, 1;\n  animation-iteration-count: infinite, 1;\n}\n\n.hjPEAQ {\n  opacity: 0;\n  stroke-dasharray: 70;\n  -webkit-animation: innerDrawIn 1s ease-in-out forwards 1.7000000000000002s, appearIn 0.1s linear forwards 1.7000000000000002s;\n  animation: innerDrawIn 1s ease-in-out forwards 1.7000000000000002s, appearIn 0.1s linear forwards 1.7000000000000002s;\n  -webkit-animation-iteration-count: infinite, 1;\n  animation-iteration-count: infinite, 1;\n}\n\n#loading-wrapper {\n  position: absolute;\n  width: 100vw;\n  height: 100vh;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-align-items: center;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-flex-direction: column;\n  -ms-flex-direction: column;\n  flex-direction: column;\n}\n\n.logo {\n  width: 75px;\n  height: 75px;\n  margin-bottom: 20px;\n  opacity: 0;\n  -webkit-animation: fadeIn 0.5s ease-out forwards;\n  animation: fadeIn 0.5s ease-out forwards;\n}\n\n.text {\n  font-size: 32px;\n  font-weight: 200;\n  text-align: center;\n  color: rgba(255, 255, 255, 0.6);\n  opacity: 0;\n  -webkit-animation: fadeIn 0.5s ease-out forwards;\n  animation: fadeIn 0.5s ease-out forwards;\n}\n\n.dGfHfc {\n  font-weight: 400;\n}\n</style>\n<div id="loading-wrapper">\n<svg class="logo" viewBox="0 0 128 128" xmlns:xlink="http://www.w3.org/1999/xlink">\n  <title>GraphQL Playground Logo</title>\n  <defs>\n    <linearGradient id="linearGradient-1" x1="4.86%" x2="96.21%" y1="0%" y2="99.66%">\n      <stop stop-color="#E00082" stop-opacity=".8" offset="0%"></stop>\n      <stop stop-color="#E00082" offset="100%"></stop>\n    </linearGradient>\n  </defs>\n  <g>\n    <rect id="Gradient" width="127.96" height="127.96" y="1" fill="url(#linearGradient-1)" rx="4"></rect>\n    <path id="Border" fill="#E00082" fill-rule="nonzero" d="M4.7 2.84c-1.58 0-2.86 1.28-2.86 2.85v116.57c0 1.57 1.28 2.84 2.85 2.84h116.57c1.57 0 2.84-1.26 2.84-2.83V5.67c0-1.55-1.26-2.83-2.83-2.83H4.67zM4.7 0h116.58c3.14 0 5.68 2.55 5.68 5.7v116.58c0 3.14-2.54 5.68-5.68 5.68H4.68c-3.13 0-5.68-2.54-5.68-5.68V5.68C-1 2.56 1.55 0 4.7 0z"></path>\n    <path class="bglIGM" x="64" y="28" fill="#fff" d="M64 36c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8" style="transform: translate(100px, 100px);"></path>\n    <path class="ksxRII" x="95.98500061035156" y="46.510000228881836" fill="#fff" d="M89.04 50.52c-2.2-3.84-.9-8.73 2.94-10.96 3.83-2.2 8.72-.9 10.95 2.94 2.2 3.84.9 8.73-2.94 10.96-3.85 2.2-8.76.9-10.97-2.94"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="cWrBmb" x="95.97162628173828" y="83.4900016784668" fill="#fff" d="M102.9 87.5c-2.2 3.84-7.1 5.15-10.94 2.94-3.84-2.2-5.14-7.12-2.94-10.96 2.2-3.84 7.12-5.15 10.95-2.94 3.86 2.23 5.16 7.12 2.94 10.96"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="Wnusb" x="64" y="101.97999572753906" fill="#fff" d="M64 110c-4.43 0-8-3.6-8-8.02 0-4.44 3.57-8.02 8-8.02s8 3.58 8 8.02c0 4.4-3.57 8.02-8 8.02"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="bfPqf" x="32.03982162475586" y="83.4900016784668" fill="#fff" d="M25.1 87.5c-2.2-3.84-.9-8.73 2.93-10.96 3.83-2.2 8.72-.9 10.95 2.94 2.2 3.84.9 8.73-2.94 10.96-3.85 2.2-8.74.9-10.95-2.94"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="edRCTN" x="32.033552169799805" y="46.510000228881836" fill="#fff" d="M38.96 50.52c-2.2 3.84-7.12 5.15-10.95 2.94-3.82-2.2-5.12-7.12-2.92-10.96 2.2-3.84 7.12-5.15 10.95-2.94 3.83 2.23 5.14 7.12 2.94 10.96"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="iEGVWn" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M63.55 27.5l32.9 19-32.9-19z"></path>\n    <path class="bsocdx" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M96 46v38-38z"></path>\n    <path class="jAZXmP" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M96.45 84.5l-32.9 19 32.9-19z"></path>\n    <path class="hSeArx" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M64.45 103.5l-32.9-19 32.9 19z"></path>\n    <path class="bVgqGk" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M32 84V46v38z"></path>\n    <path class="hEFqBt" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M31.55 46.5l32.9-19-32.9 19z"></path>\n    <path class="dzEKCM" id="Triangle-Bottom" stroke="#fff" stroke-width="4" d="M30 84h70" stroke-linecap="round"></path>\n    <path class="DYnPx" id="Triangle-Left" stroke="#fff" stroke-width="4" d="M65 26L30 87" stroke-linecap="round"></path>\n    <path class="hjPEAQ" id="Triangle-Right" stroke="#fff" stroke-width="4" d="M98 87L63 26" stroke-linecap="round"></path>\n  </g>\n</svg>\n<div class="text">Loading\n  <span class="dGfHfc">GraphQL Playground</span>\n</div>\n</div>\n'}}},6369:t=>{t.exports={indexOf:function(t,e){var r,n;if(Array.prototype.indexOf)return t.indexOf(e);for(r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return -1},forEach:function(t,e,r){var n,a;if(Array.prototype.forEach)return t.forEach(e,r);for(n=0,a=t.length;n<a;n++)e.call(r,t[n],n,t)},trim:function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(t){var e=/\s|\n|\t/.exec(t);return e?e.index:-1}}},8086:t=>{"use strict";t.exports=require("module")},9288:t=>{"use strict";t.exports=require("sharp")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:t=>{"use strict";t.exports=require("punycode")},12412:t=>{"use strict";t.exports=require("assert")},13129:(t,e,r)=>{var n=r(74988),a=r(19610),o=r(38023);function i(t,e){return new o(e).process(t)}for(var s in(e=t.exports=i).filterXSS=i,e.FilterXSS=o,n)e[s]=n[s];for(var l in a)e[l]=a[l];"undefined"!=typeof window&&(window.filterXSS=t.exports),"undefined"!=typeof self&&"undefined"!=typeof DedicatedWorkerGlobalScope&&self instanceof DedicatedWorkerGlobalScope&&(self.filterXSS=t.exports)},14985:t=>{"use strict";t.exports=require("dns")},16698:t=>{"use strict";t.exports=require("node:async_hooks")},17219:(t,e,r)=>{var n=r(52942),a=r(67808);for(var o in(e=t.exports=function(t,e){return new a(e).process(t)}).FilterCSS=a,n)e[o]=n[o];"undefined"!=typeof window&&(window.filterCSS=t.exports)},19610:(t,e,r)=>{var n=r(6369),a=/[^a-zA-Z0-9\\_:.-]/gim;function o(t){return'"'===t[0]&&'"'===t[t.length-1]||"'"===t[0]&&"'"===t[t.length-1]?t.substr(1,t.length-2):t}e.parseTag=function(t,e,r){"use strict";var a="",o=0,i=!1,s=!1,l=0,c=t.length,p="",f="";t:for(l=0;l<c;l++){var u=t.charAt(l);if(!1===i){if("<"===u){i=l;continue}}else if(!1===s){if("<"===u){a+=r(t.slice(o,l)),i=l,o=l;continue}if(">"===u||l===c-1){a+=r(t.slice(o,i)),p=function(t){var e,r=n.spaceIndex(t);return e=-1===r?t.slice(1,-1):t.slice(1,r+1),"/"===(e=n.trim(e).toLowerCase()).slice(0,1)&&(e=e.slice(1)),"/"===e.slice(-1)&&(e=e.slice(0,-1)),e}(f=t.slice(i,l+1)),a+=e(i,a.length,p,f,"</"===f.slice(0,2)),o=l+1,i=!1;continue}if('"'===u||"'"===u)for(var d=1,m=t.charAt(l-d);""===m.trim()||"="===m;){if("="===m){s=u;continue t}m=t.charAt(l-++d)}}else if(u===s){s=!1;continue}}return o<c&&(a+=r(t.substr(o))),a},e.parseAttr=function(t,e){"use strict";var r=0,i=0,s=[],l=!1,c=t.length;function p(t,r){if(!((t=(t=n.trim(t)).replace(a,"").toLowerCase()).length<1)){var o=e(t,r||"");o&&s.push(o)}}for(var f=0;f<c;f++){var u,d,m=t.charAt(f);if(!1===l&&"="===m){l=t.slice(r,f),r=f+1,i='"'===t.charAt(r)||"'"===t.charAt(r)?r:function(t,e){for(;e<t.length;e++){var r=t[e];if(" "!==r){if("'"===r||'"'===r)return e;return -1}}}(t,f+1);continue}if(!1!==l&&f===i){if(-1===(d=t.indexOf(m,f+1)))break;p(l,n.trim(t.slice(i+1,d))),l=!1,r=(f=d)+1;continue}if(/\s|\n|\t/.test(m)){if(t=t.replace(/\s|\n|\t/g," "),!1===l){if(-1===(d=function(t,e){for(;e<t.length;e++){var r=t[e];if(" "!==r){if("="===r)return e;return -1}}}(t,f))){p(n.trim(t.slice(r,f))),l=!1,r=f+1;continue}f=d-1;continue}if(-1!==(d=function(t,e){for(;e>0;e--){var r=t[e];if(" "!==r){if("="===r)return e;return -1}}}(t,f-1)))continue;p(l,o(n.trim(t.slice(r,f)))),l=!1,r=f+1;continue}}return r<t.length&&(!1===l?p(t.slice(r)):p(l,o(n.trim(t.slice(r))))),n.trim(s.join(" "))}},19771:t=>{"use strict";t.exports=require("process")},20271:(t,e,r)=>{"use strict";r.a(t,async(t,n)=>{try{r.r(e),r.d(e,{GET:()=>s});var a=r(68117);r(1065);var o=r(60235),i=t([a]);a=(i.then?(await i)():i)[0];let s=(0,o.f)(a.A);n()}catch(t){n(t)}})},21820:t=>{"use strict";t.exports=require("os")},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30160:(t,e,r)=>{"use strict";r.d(e,{D4:()=>p,F7:()=>h,Pe:()=>m,gd:()=>d,h1:()=>c,kg:()=>g,lF:()=>f,oE:()=>u});var n=r(32293);let a=Object.prototype.hasOwnProperty,o=Array.isArray,i=function(){let t=[];for(let e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),s=function(t){for(;t.length>1;){let e=t.pop(),r=e.obj[e.prop];if(o(r)){let t=[];for(let e=0;e<r.length;++e)void 0!==r[e]&&t.push(r[e]);e.obj[e.prop]=t}}},l=function(t,e){let r=e&&e.plainObjects?Object.create(null):{};for(let e=0;e<t.length;++e)void 0!==t[e]&&(r[e]=t[e]);return r},c=function t(e,r,n){if(!r)return e;if("object"!=typeof r){if(o(e))e.push(r);else{if(!e||"object"!=typeof e)return[e,r];(n&&(n.plainObjects||n.allowPrototypes)||!a.call(Object.prototype,r))&&(e[r]=!0)}return e}if(!e||"object"!=typeof e)return[e].concat(r);let i=e;return(o(e)&&!o(r)&&(i=l(e,n)),o(e)&&o(r))?(r.forEach(function(r,o){if(a.call(e,o)){let a=e[o];a&&"object"==typeof a&&r&&"object"==typeof r?e[o]=t(a,r,n):e.push(r)}else e[o]=r}),e):Object.keys(r).reduce(function(e,o){let i=r[o];return a.call(e,o)?e[o]=t(e[o],i,n):e[o]=i,e},i)},p=function(t,e,r){let n=t.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(t){return n}},f=function(t,e,r,a,o){if(0===t.length)return t;let s=t;if("symbol"==typeof t?s=Symbol.prototype.toString.call(t):"string"!=typeof t&&(s=String(t)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"});let l="";for(let t=0;t<s.length;t+=1024){let e=s.length>=1024?s.slice(t,t+1024):s,r=[];for(let t=0;t<e.length;++t){let a=e.charCodeAt(t);if(45===a||46===a||95===a||126===a||a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||o===n.j1&&(40===a||41===a)){r[r.length]=e.charAt(t);continue}if(a<128){r[r.length]=i[a];continue}if(a<2048){r[r.length]=i[192|a>>6]+i[128|63&a];continue}if(a<55296||a>=57344){r[r.length]=i[224|a>>12]+i[128|a>>6&63]+i[128|63&a];continue}t+=1,a=65536+((1023&a)<<10|1023&e.charCodeAt(t)),r[r.length]=i[240|a>>18]+i[128|a>>12&63]+i[128|a>>6&63]+i[128|63&a]}l+=r.join("")}return l},u=function(t){let e=[{obj:{o:t},prop:"o"}],r=[];for(let t=0;t<e.length;++t){let n=e[t],a=n.obj[n.prop],o=Object.keys(a);for(let t=0;t<o.length;++t){let n=o[t],i=a[n];"object"==typeof i&&null!==i&&-1===r.indexOf(i)&&(e.push({obj:a,prop:n}),r.push(i))}}return s(e),t},d=function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},m=function(t){return!!t&&"object"==typeof t&&!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},g=function(t,e){return[].concat(t,e)},h=function(t,e){if(o(t)){let r=[];for(let n=0;n<t.length;n+=1)r.push(e(t[n]));return r}return e(t)}},32293:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>l,_J:()=>i,j1:()=>s});let n=String.prototype.replace,a=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"},i={RFC1738:function(t){return n.call(t,a,"+")},RFC3986:function(t){return String(t)}},s=o.RFC1738;o.RFC3986;let l=o.RFC3986},32467:t=>{"use strict";t.exports=require("node:http2")},32785:t=>{"use strict";t.exports=import("prettier")},33873:t=>{"use strict";t.exports=require("path")},34589:t=>{"use strict";t.exports=require("node:assert")},34631:t=>{"use strict";t.exports=require("tls")},34922:(t,e,r)=>{"use strict";r.d(e,{Y:()=>a});var n=r(89140);let a=({config:t,cookies:e,headers:r})=>{let a=Object.keys(t.i18n.supportedLanguages),o=e.get(`${t.cookiePrefix||"payload"}-lng`),i="string"==typeof o?o:o?.value;if(i&&a.includes(i))return i;let s=r.get("Accept-Language")?(0,n.R8)(r.get("Accept-Language")):void 0;return s&&a.includes(s)?s:t.i18n.fallbackLanguage}},35672:t=>{"use strict";t.exports=require("dns/promises")},37067:t=>{"use strict";t.exports=require("node:http")},37540:t=>{"use strict";t.exports=require("node:console")},37830:t=>{"use strict";t.exports=require("node:stream/web")},38023:(t,e,r)=>{var n=r(17219).FilterCSS,a=r(74988),o=r(19610),i=o.parseTag,s=o.parseAttr,l=r(6369);function c(t){(t=function(t){var e={};for(var r in t)e[r]=t[r];return e}(t||{})).stripIgnoreTag&&(t.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),t.onIgnoreTag=a.onIgnoreTagStripAll),t.whiteList||t.allowList?t.whiteList=function(t){var e={};for(var r in t)Array.isArray(t[r])?e[r.toLowerCase()]=t[r].map(function(t){return t.toLowerCase()}):e[r.toLowerCase()]=t[r];return e}(t.whiteList||t.allowList):t.whiteList=a.whiteList,this.attributeWrapSign=!0===t.singleQuotedAttributeValue?"'":a.attributeWrapSign,t.onTag=t.onTag||a.onTag,t.onTagAttr=t.onTagAttr||a.onTagAttr,t.onIgnoreTag=t.onIgnoreTag||a.onIgnoreTag,t.onIgnoreTagAttr=t.onIgnoreTagAttr||a.onIgnoreTagAttr,t.safeAttrValue=t.safeAttrValue||a.safeAttrValue,t.escapeHtml=t.escapeHtml||a.escapeHtml,this.options=t,!1===t.css?this.cssFilter=!1:(t.css=t.css||{},this.cssFilter=new n(t.css))}c.prototype.process=function(t){if(!(t=(t=t||"").toString()))return"";var e=this.options,r=e.whiteList,n=e.onTag,o=e.onIgnoreTag,c=e.onTagAttr,p=e.onIgnoreTagAttr,f=e.safeAttrValue,u=e.escapeHtml,d=this.attributeWrapSign,m=this.cssFilter;e.stripBlankChar&&(t=a.stripBlankChar(t)),e.allowCommentTag||(t=a.stripCommentTag(t));var g=!1;e.stripIgnoreTagBody&&(o=(g=a.StripTagBody(e.stripIgnoreTagBody,o)).onIgnoreTag);var h=i(t,function(t,e,a,i,g){var h,w,y={sourcePosition:t,position:e,isClosing:g,isWhite:Object.prototype.hasOwnProperty.call(r,a)},b=n(a,i,y);if(null!=b)return b;if(y.isWhite){if(y.isClosing)return"</"+a+">";var x=function(t){var e=l.spaceIndex(t);if(-1===e)return{html:"",closing:"/"===t[t.length-2]};var r="/"===(t=l.trim(t.slice(e+1,-1)))[t.length-1];return r&&(t=l.trim(t.slice(0,-1))),{html:t,closing:r}}(i),k=r[a],v=s(x.html,function(t,e){var r,n,o=-1!==l.indexOf(k,t),i=c(a,t,e,o);return null!=i?i:o?(e=f(a,t,e,m))?t+"="+d+e+d:t:null!=(i=p(a,t,e,o))?i:void 0});return i="<"+a,v&&(i+=" "+v),x.closing&&(i+=" /"),i+=">"}return null!=(b=o(a,i,y))?b:u(i)},u);return g&&(h=g.remove(h)),h},t.exports=c},38522:t=>{"use strict";t.exports=require("node:zlib")},39151:function(t,e,r){"use strict";var n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var a in e=arguments[r])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0});var a=r(13129),o=r(6345),i=function(t){return a.filterXSS(t,{whiteList:[],stripIgnoreTag:!0,stripIgnoreTagBody:["script"]})},s=o.default(),l="playground-config",c=function(t){var e=t.version,r=t.cdnUrl,n=void 0===r?"//cdn.jsdelivr.net/npm":r,a=t.faviconUrl,o=function(t,r){return i(n+"/"+t+(e?"@"+e:"")+"/"+r||"")};return'\n    <link \n      rel="stylesheet" \n      href="'+o("graphql-playground-react","build/static/css/index.css")+'"\n    />\n    '+("string"==typeof a?'<link rel="shortcut icon" href="'+i(a||"")+'" />':"")+"\n    "+(void 0===a?'<link rel="shortcut icon" href="'+o("graphql-playground-react","build/favicon.png")+'" />':"")+'\n    <script \n      src="'+o("graphql-playground-react","build/static/js/middleware.js")+'"\n    ><\/script>\n'};e.renderPlaygroundPage=function(t){var e=n(n({},t),{canSaveConfig:!1});return t.subscriptionsEndpoint&&(e.subscriptionEndpoint=i(t.subscriptionsEndpoint||"")),t.config&&(e.configString=JSON.stringify(t.config,null,2)),e.endpoint||e.configString?e.endpoint&&(e.endpoint=i(e.endpoint||"")):console.warn("WARNING: You didn't provide an endpoint and don't have a .graphqlconfig. Make sure you have at least one of them."),'\n  <!DOCTYPE html>\n  <html>\n  <head>\n    <meta charset=utf-8 />\n    <meta name="viewport" content="user-scalable=no, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, minimal-ui">\n    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700|Source+Code+Pro:400,700" rel="stylesheet">\n    <title>'+(e.title||"GraphQL Playground")+"</title>\n    "+("react"===e.env||"electron"===e.env?"":c(e))+'\n  </head>\n  <body>\n    <style type="text/css">\n      html {\n        font-family: "Open Sans", sans-serif;\n        overflow: hidden;\n      }\n  \n      body {\n        margin: 0;\n        background: #172a3a;\n      }\n\n      #'+l+" {\n        display: none;\n      }\n  \n      .playgroundIn {\n        -webkit-animation: playgroundIn 0.5s ease-out forwards;\n        animation: playgroundIn 0.5s ease-out forwards;\n      }\n  \n      @-webkit-keyframes playgroundIn {\n        from {\n          opacity: 0;\n          -webkit-transform: translateY(10px);\n          -ms-transform: translateY(10px);\n          transform: translateY(10px);\n        }\n        to {\n          opacity: 1;\n          -webkit-transform: translateY(0);\n          -ms-transform: translateY(0);\n          transform: translateY(0);\n        }\n      }\n  \n      @keyframes playgroundIn {\n        from {\n          opacity: 0;\n          -webkit-transform: translateY(10px);\n          -ms-transform: translateY(10px);\n          transform: translateY(10px);\n        }\n        to {\n          opacity: 1;\n          -webkit-transform: translateY(0);\n          -ms-transform: translateY(0);\n          transform: translateY(0);\n        }\n      }\n    </style>\n    "+s.container+"\n    "+a.filterXSS('<div id="'+l+'">'+JSON.stringify(e)+"</div>",{whiteList:{div:["id"]}})+'\n    <div id="root" />\n    <script type="text/javascript">\n      window.addEventListener(\'load\', function (event) {\n        '+s.script+"\n  \n        const root = document.getElementById('root');\n        root.classList.add('playgroundIn');\n        const configText = document.getElementById('"+l+'\').innerText;\n        \n        if(configText && configText.length) {\n          try {\n            GraphQLPlayground.init(root, JSON.parse(configText));\n          }\n          catch(err) {\n            console.error("could not find config")\n          }\n        }\n        else {\n          GraphQLPlayground.init(root);\n        }\n      })\n    <\/script>\n  </body>\n  </html>\n'}},40610:t=>{"use strict";t.exports=require("node:dns")},41204:t=>{"use strict";t.exports=require("string_decoder")},41692:t=>{"use strict";t.exports=require("node:tls")},41792:t=>{"use strict";t.exports=require("node:querystring")},42033:(t,e,r)=>{"use strict";e.p=r(39151).renderPlaygroundPage},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:t=>{"use strict";t.exports=require("node:os")},51455:t=>{"use strict";t.exports=require("node:fs/promises")},52942:(t,e)=>{function r(){var t={};return t["align-content"]=!1,t["align-items"]=!1,t["align-self"]=!1,t["alignment-adjust"]=!1,t["alignment-baseline"]=!1,t.all=!1,t["anchor-point"]=!1,t.animation=!1,t["animation-delay"]=!1,t["animation-direction"]=!1,t["animation-duration"]=!1,t["animation-fill-mode"]=!1,t["animation-iteration-count"]=!1,t["animation-name"]=!1,t["animation-play-state"]=!1,t["animation-timing-function"]=!1,t.azimuth=!1,t["backface-visibility"]=!1,t.background=!0,t["background-attachment"]=!0,t["background-clip"]=!0,t["background-color"]=!0,t["background-image"]=!0,t["background-origin"]=!0,t["background-position"]=!0,t["background-repeat"]=!0,t["background-size"]=!0,t["baseline-shift"]=!1,t.binding=!1,t.bleed=!1,t["bookmark-label"]=!1,t["bookmark-level"]=!1,t["bookmark-state"]=!1,t.border=!0,t["border-bottom"]=!0,t["border-bottom-color"]=!0,t["border-bottom-left-radius"]=!0,t["border-bottom-right-radius"]=!0,t["border-bottom-style"]=!0,t["border-bottom-width"]=!0,t["border-collapse"]=!0,t["border-color"]=!0,t["border-image"]=!0,t["border-image-outset"]=!0,t["border-image-repeat"]=!0,t["border-image-slice"]=!0,t["border-image-source"]=!0,t["border-image-width"]=!0,t["border-left"]=!0,t["border-left-color"]=!0,t["border-left-style"]=!0,t["border-left-width"]=!0,t["border-radius"]=!0,t["border-right"]=!0,t["border-right-color"]=!0,t["border-right-style"]=!0,t["border-right-width"]=!0,t["border-spacing"]=!0,t["border-style"]=!0,t["border-top"]=!0,t["border-top-color"]=!0,t["border-top-left-radius"]=!0,t["border-top-right-radius"]=!0,t["border-top-style"]=!0,t["border-top-width"]=!0,t["border-width"]=!0,t.bottom=!1,t["box-decoration-break"]=!0,t["box-shadow"]=!0,t["box-sizing"]=!0,t["box-snap"]=!0,t["box-suppress"]=!0,t["break-after"]=!0,t["break-before"]=!0,t["break-inside"]=!0,t["caption-side"]=!1,t.chains=!1,t.clear=!0,t.clip=!1,t["clip-path"]=!1,t["clip-rule"]=!1,t.color=!0,t["color-interpolation-filters"]=!0,t["column-count"]=!1,t["column-fill"]=!1,t["column-gap"]=!1,t["column-rule"]=!1,t["column-rule-color"]=!1,t["column-rule-style"]=!1,t["column-rule-width"]=!1,t["column-span"]=!1,t["column-width"]=!1,t.columns=!1,t.contain=!1,t.content=!1,t["counter-increment"]=!1,t["counter-reset"]=!1,t["counter-set"]=!1,t.crop=!1,t.cue=!1,t["cue-after"]=!1,t["cue-before"]=!1,t.cursor=!1,t.direction=!1,t.display=!0,t["display-inside"]=!0,t["display-list"]=!0,t["display-outside"]=!0,t["dominant-baseline"]=!1,t.elevation=!1,t["empty-cells"]=!1,t.filter=!1,t.flex=!1,t["flex-basis"]=!1,t["flex-direction"]=!1,t["flex-flow"]=!1,t["flex-grow"]=!1,t["flex-shrink"]=!1,t["flex-wrap"]=!1,t.float=!1,t["float-offset"]=!1,t["flood-color"]=!1,t["flood-opacity"]=!1,t["flow-from"]=!1,t["flow-into"]=!1,t.font=!0,t["font-family"]=!0,t["font-feature-settings"]=!0,t["font-kerning"]=!0,t["font-language-override"]=!0,t["font-size"]=!0,t["font-size-adjust"]=!0,t["font-stretch"]=!0,t["font-style"]=!0,t["font-synthesis"]=!0,t["font-variant"]=!0,t["font-variant-alternates"]=!0,t["font-variant-caps"]=!0,t["font-variant-east-asian"]=!0,t["font-variant-ligatures"]=!0,t["font-variant-numeric"]=!0,t["font-variant-position"]=!0,t["font-weight"]=!0,t.grid=!1,t["grid-area"]=!1,t["grid-auto-columns"]=!1,t["grid-auto-flow"]=!1,t["grid-auto-rows"]=!1,t["grid-column"]=!1,t["grid-column-end"]=!1,t["grid-column-start"]=!1,t["grid-row"]=!1,t["grid-row-end"]=!1,t["grid-row-start"]=!1,t["grid-template"]=!1,t["grid-template-areas"]=!1,t["grid-template-columns"]=!1,t["grid-template-rows"]=!1,t["hanging-punctuation"]=!1,t.height=!0,t.hyphens=!1,t.icon=!1,t["image-orientation"]=!1,t["image-resolution"]=!1,t["ime-mode"]=!1,t["initial-letters"]=!1,t["inline-box-align"]=!1,t["justify-content"]=!1,t["justify-items"]=!1,t["justify-self"]=!1,t.left=!1,t["letter-spacing"]=!0,t["lighting-color"]=!0,t["line-box-contain"]=!1,t["line-break"]=!1,t["line-grid"]=!1,t["line-height"]=!1,t["line-snap"]=!1,t["line-stacking"]=!1,t["line-stacking-ruby"]=!1,t["line-stacking-shift"]=!1,t["line-stacking-strategy"]=!1,t["list-style"]=!0,t["list-style-image"]=!0,t["list-style-position"]=!0,t["list-style-type"]=!0,t.margin=!0,t["margin-bottom"]=!0,t["margin-left"]=!0,t["margin-right"]=!0,t["margin-top"]=!0,t["marker-offset"]=!1,t["marker-side"]=!1,t.marks=!1,t.mask=!1,t["mask-box"]=!1,t["mask-box-outset"]=!1,t["mask-box-repeat"]=!1,t["mask-box-slice"]=!1,t["mask-box-source"]=!1,t["mask-box-width"]=!1,t["mask-clip"]=!1,t["mask-image"]=!1,t["mask-origin"]=!1,t["mask-position"]=!1,t["mask-repeat"]=!1,t["mask-size"]=!1,t["mask-source-type"]=!1,t["mask-type"]=!1,t["max-height"]=!0,t["max-lines"]=!1,t["max-width"]=!0,t["min-height"]=!0,t["min-width"]=!0,t["move-to"]=!1,t["nav-down"]=!1,t["nav-index"]=!1,t["nav-left"]=!1,t["nav-right"]=!1,t["nav-up"]=!1,t["object-fit"]=!1,t["object-position"]=!1,t.opacity=!1,t.order=!1,t.orphans=!1,t.outline=!1,t["outline-color"]=!1,t["outline-offset"]=!1,t["outline-style"]=!1,t["outline-width"]=!1,t.overflow=!1,t["overflow-wrap"]=!1,t["overflow-x"]=!1,t["overflow-y"]=!1,t.padding=!0,t["padding-bottom"]=!0,t["padding-left"]=!0,t["padding-right"]=!0,t["padding-top"]=!0,t.page=!1,t["page-break-after"]=!1,t["page-break-before"]=!1,t["page-break-inside"]=!1,t["page-policy"]=!1,t.pause=!1,t["pause-after"]=!1,t["pause-before"]=!1,t.perspective=!1,t["perspective-origin"]=!1,t.pitch=!1,t["pitch-range"]=!1,t["play-during"]=!1,t.position=!1,t["presentation-level"]=!1,t.quotes=!1,t["region-fragment"]=!1,t.resize=!1,t.rest=!1,t["rest-after"]=!1,t["rest-before"]=!1,t.richness=!1,t.right=!1,t.rotation=!1,t["rotation-point"]=!1,t["ruby-align"]=!1,t["ruby-merge"]=!1,t["ruby-position"]=!1,t["shape-image-threshold"]=!1,t["shape-outside"]=!1,t["shape-margin"]=!1,t.size=!1,t.speak=!1,t["speak-as"]=!1,t["speak-header"]=!1,t["speak-numeral"]=!1,t["speak-punctuation"]=!1,t["speech-rate"]=!1,t.stress=!1,t["string-set"]=!1,t["tab-size"]=!1,t["table-layout"]=!1,t["text-align"]=!0,t["text-align-last"]=!0,t["text-combine-upright"]=!0,t["text-decoration"]=!0,t["text-decoration-color"]=!0,t["text-decoration-line"]=!0,t["text-decoration-skip"]=!0,t["text-decoration-style"]=!0,t["text-emphasis"]=!0,t["text-emphasis-color"]=!0,t["text-emphasis-position"]=!0,t["text-emphasis-style"]=!0,t["text-height"]=!0,t["text-indent"]=!0,t["text-justify"]=!0,t["text-orientation"]=!0,t["text-overflow"]=!0,t["text-shadow"]=!0,t["text-space-collapse"]=!0,t["text-transform"]=!0,t["text-underline-position"]=!0,t["text-wrap"]=!0,t.top=!1,t.transform=!1,t["transform-origin"]=!1,t["transform-style"]=!1,t.transition=!1,t["transition-delay"]=!1,t["transition-duration"]=!1,t["transition-property"]=!1,t["transition-timing-function"]=!1,t["unicode-bidi"]=!1,t["vertical-align"]=!1,t.visibility=!1,t["voice-balance"]=!1,t["voice-duration"]=!1,t["voice-family"]=!1,t["voice-pitch"]=!1,t["voice-range"]=!1,t["voice-rate"]=!1,t["voice-stress"]=!1,t["voice-volume"]=!1,t.volume=!1,t["white-space"]=!1,t.widows=!1,t.width=!0,t["will-change"]=!1,t["word-break"]=!0,t["word-spacing"]=!0,t["word-wrap"]=!0,t["wrap-flow"]=!1,t["wrap-through"]=!1,t["writing-mode"]=!1,t["z-index"]=!1,t}var n=/javascript\s*\:/img;e.whiteList=r(),e.getDefaultWhiteList=r,e.onAttr=function(t,e,r){},e.onIgnoreAttr=function(t,e,r){},e.safeAttrValue=function(t,e){return n.test(e)?"":e}},53053:t=>{"use strict";t.exports=require("node:diagnostics_channel")},54546:(t,e,r)=>{"use strict";r.d(e,{o:()=>f});var n=r(22373),a=r(90990),o=r(26441),i=r(52345),s=r(10724),l=r(76657),c=r(34922),p=r(38263);let f=async({canSetHeaders:t,config:e,params:r,request:f})=>{let u=(0,p.J)(f.headers),d=await (0,s.nm0)({config:e}),{config:m}=d,g=m.localization,h=new URL(f.url),{pathname:w,searchParams:y}=h,b=!m.graphQL.disable&&w===`${m.routes.api}${m.routes.graphQL}`,x=(0,c.Y)({config:m,cookies:u,headers:f.headers}),k=await (0,n.L)({config:m.i18n,context:"api",language:x}),v=y.get("fallback-locale")||y.get("fallbackLocale"),I=y.get("locale"),A=v,{search:q}=h,j=q?a.q(q,{arrayLimit:1e3,depth:10,ignoreQueryPrefix:!0}):{};if(g){let t=(0,l.T)({fallbackLocale:A,locale:I,localization:g});A=t.fallbackLocale,I=t.locale}let S=Object.assign(f,{context:{},fallbackLocale:A,hash:h.hash,host:h.host,href:h.href,i18n:k,locale:I,origin:h.origin,pathname:h.pathname,payload:d,payloadAPI:b?"GraphQL":"REST",payloadDataLoader:void 0,payloadUploadSizes:{},port:h.port,protocol:h.protocol,query:j,routeParams:r||{},search:h.search,searchParams:h.searchParams,t:k.t,transactionID:void 0,user:null});S.payloadDataLoader=(0,i.Y)(S);let{responseHeaders:L,user:O}=await (0,o.F)({canSetHeaders:t,headers:S.headers,isGraphQL:b,payload:d});return S.user=O,L&&(S.responseHeaders=L),S}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},57075:t=>{"use strict";t.exports=require("node:stream")},57975:t=>{"use strict";t.exports=require("node:util")},59528:(t,e,r)=>{"use strict";r.a(t,async(t,n)=>{try{r.r(e),r.d(e,{patchFetch:()=>c,routeModule:()=>p,serverHooks:()=>d,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>u});var a=r(70293),o=r(32498),i=r(83889),s=r(20271),l=t([s]);s=(l.then?(await l)():l)[0];let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/(payload)/api/graphql-playground/route",pathname:"/api/graphql-playground",filename:"route",bundlePath:"app/(payload)/api/graphql-playground/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\(payload)\\api\\graphql-playground\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:u,serverHooks:d}=p;function c(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:u})}n()}catch(t){n(t)}})},60235:(t,e,r)=>{"use strict";r.d(e,{f:()=>o});var n=r(42033),a=r(54546);let o=t=>async e=>{let r=await (0,a.o)({config:t,request:e});return r.payload.config.graphQL.disable||r.payload.config.graphQL.disablePlaygroundInProduction?new Response("Route Not Found",{status:404}):new Response((0,n.p)({endpoint:`${r.payload.config.routes.api}${r.payload.config.routes.graphQL}`,settings:{"request.credentials":"include"}}),{headers:{"Content-Type":"text/html"},status:200})}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:t=>{"use strict";t.exports=import("pg")},67808:(t,e,r)=>{var n=r(52942),a=r(80624);function o(t){(t=function(t){var e={};for(var r in t)e[r]=t[r];return e}(t||{})).whiteList=t.whiteList||n.whiteList,t.onAttr=t.onAttr||n.onAttr,t.onIgnoreAttr=t.onIgnoreAttr||n.onIgnoreAttr,t.safeAttrValue=t.safeAttrValue||n.safeAttrValue,this.options=t}r(98563),o.prototype.process=function(t){if(!(t=(t=t||"").toString()))return"";var e=this.options,r=e.whiteList,n=e.onAttr,o=e.onIgnoreAttr,i=e.safeAttrValue;return a(t,function(t,e,a,s,l){var c=r[a],p=!1;if(!0===c?p=c:"function"==typeof c?p=c(s):c instanceof RegExp&&(p=c.test(s)),!0!==p&&(p=!1),s=i(a,s)){var f={position:e,sourcePosition:t,source:l,isWhite:p};if(p){var u,d=n(a,s,f);return null==d?a+":"+s:d}var m,d=o(a,s,f);if(null!=d)return d}})},t.exports=o},70293:(t,e,r)=>{"use strict";t.exports=r(44870)},73024:t=>{"use strict";t.exports=require("node:fs")},73136:t=>{"use strict";t.exports=require("node:url")},73429:t=>{"use strict";t.exports=require("node:util/types")},73496:t=>{"use strict";t.exports=require("http2")},73566:t=>{"use strict";t.exports=require("worker_threads")},74075:t=>{"use strict";t.exports=require("zlib")},74988:(t,e,r)=>{var n=r(17219).FilterCSS,a=r(17219).getDefaultWhiteList,o=r(6369);function i(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var s=new n;function l(t){return t.replace(c,"&lt;").replace(p,"&gt;")}var c=/</g,p=/>/g,f=/"/g,u=/&quot;/g,d=/&#([a-zA-Z0-9]*);?/gim,m=/&colon;?/gim,g=/&newline;?/gim,h=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,w=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,y=/u\s*r\s*l\s*\(.*/gi;function b(t){return t.replace(f,"&quot;")}function x(t){return t.replace(u,'"')}function k(t){return t.replace(d,function(t,e){return"x"===e[0]||"X"===e[0]?String.fromCharCode(parseInt(e.substr(1),16)):String.fromCharCode(parseInt(e,10))})}function v(t){return t.replace(m,":").replace(g," ")}function I(t){for(var e="",r=0,n=t.length;r<n;r++)e+=32>t.charCodeAt(r)?" ":t.charAt(r);return o.trim(e)}function A(t){return t=I(t=v(t=k(t=x(t))))}function q(t){return t=l(t=b(t))}e.whiteList=i(),e.getDefaultWhiteList=i,e.onTag=function(t,e,r){},e.onIgnoreTag=function(t,e,r){},e.onTagAttr=function(t,e,r){},e.onIgnoreTagAttr=function(t,e,r){},e.safeAttrValue=function(t,e,r,n){if(r=A(r),"href"===e||"src"===e){if("#"===(r=o.trim(r)))return"#";if("http://"!==r.substr(0,7)&&"https://"!==r.substr(0,8)&&"mailto:"!==r.substr(0,7)&&"tel:"!==r.substr(0,4)&&"data:image/"!==r.substr(0,11)&&"ftp://"!==r.substr(0,6)&&"./"!==r.substr(0,2)&&"../"!==r.substr(0,3)&&"#"!==r[0]&&"/"!==r[0])return""}else if("background"===e){if(h.lastIndex=0,h.test(r))return""}else if("style"===e){if(w.lastIndex=0,w.test(r)||(y.lastIndex=0,y.test(r)&&(h.lastIndex=0,h.test(r))))return"";!1!==n&&(r=(n=n||s).process(r))}return r=q(r)},e.escapeHtml=l,e.escapeQuote=b,e.unescapeQuote=x,e.escapeHtmlEntities=k,e.escapeDangerHtml5Entities=v,e.clearNonPrintableCharacter=I,e.friendlyAttrValue=A,e.escapeAttrValue=q,e.onIgnoreTagStripAll=function(){return""},e.StripTagBody=function(t,e){"function"!=typeof e&&(e=function(){});var r=!Array.isArray(t),n=[],a=!1;return{onIgnoreTag:function(i,s,l){if(r?0:-1===o.indexOf(t,i))return e(i,s,l);if(!l.isClosing)return a||(a=l.position),"[removed]";var c="[/removed]",p=l.position+c.length;return n.push([!1!==a?a:l.position,p]),a=!1,c},remove:function(t){var e="",r=0;return o.forEach(n,function(n){e+=t.slice(r,n[0]),r=n[1]}),e+=t.slice(r)}}},e.stripCommentTag=function(t){for(var e="",r=0;r<t.length;){var n=t.indexOf("\x3c!--",r);if(-1===n){e+=t.slice(r);break}e+=t.slice(r,n);var a=t.indexOf("--\x3e",n);if(-1===a)break;r=a+3}return e},e.stripBlankChar=function(t){var e=t.split("");return(e=e.filter(function(t){var e=t.charCodeAt(0);return 127!==e&&(!(e<=31)||10===e||13===e)})).join("")},e.attributeWrapSign='"',e.cssFilter=s,e.getDefaultCSSWhiteList=a},75919:t=>{"use strict";t.exports=require("node:worker_threads")},76760:t=>{"use strict";t.exports=require("node:path")},77030:t=>{"use strict";t.exports=require("node:net")},77598:t=>{"use strict";t.exports=require("node:crypto")},78474:t=>{"use strict";t.exports=require("node:events")},79428:t=>{"use strict";t.exports=require("buffer")},79551:t=>{"use strict";t.exports=require("url")},79646:t=>{"use strict";t.exports=require("child_process")},79748:t=>{"use strict";t.exports=require("fs/promises")},80099:t=>{"use strict";t.exports=require("node:sqlite")},80624:(t,e,r)=>{var n=r(98563);t.exports=function(t,e){";"!==(t=n.trimRight(t))[t.length-1]&&(t+=";");var r=t.length,a=!1,o=0,i=0,s="";function l(){if(!a){var r=n.trim(t.slice(o,i)),l=r.indexOf(":");if(-1!==l){var c=n.trim(r.slice(0,l)),p=n.trim(r.slice(l+1));if(c){var f=e(o,s.length,c,p,r);f&&(s+=f+"; ")}}}o=i+1}for(;i<r;i++){var c=t[i];if("/"===c&&"*"===t[i+1]){var p=t.indexOf("*/",i+2);if(-1===p)break;o=(i=p+1)+1,a=!1}else"("===c?a=!0:")"===c?a=!1:";"===c?a||l():"\n"===c&&l()}return n.trim(s)}},81630:t=>{"use strict";t.exports=require("http")},83997:t=>{"use strict";t.exports=require("tty")},89140:(t,e,r)=>{"use strict";r.d(e,{R8:()=>o,aG:()=>n});let n=["ar","fa","he"],a=["ar","az","bg","bn-BD","bn-IN","ca","cs","bn-BD","bn-IN","da","de","en","es","et","fa","fr","he","hr","hu","hy","it","ja","ko","lt","lv","my","nb","nl","pl","pt","ro","rs","rs-latin","ru","sk","sl","sv","th","tr","uk","vi","zh","zh-TW"];function o(t){let e;for(let{language:r}of t.split(",").map(t=>{let[e,r]=t.trim().split(";q=");return{language:e,quality:r?parseFloat(r):1}}).sort((t,e)=>e.quality-t.quality))!e&&a.includes(r)&&(e=r);return e}},90990:(t,e,r)=>{"use strict";r.d(e,{q:()=>u});var n=r(30160);let a=Object.prototype.hasOwnProperty,o=Array.isArray,i={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:n.D4,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},s=function(t,e){return t&&"string"==typeof t&&e.comma&&t.indexOf(",")>-1?t.split(","):t},l=function(t,e){let r,l={__proto__:null},c=e.ignoreQueryPrefix?t.replace(/^\?/,""):t,p=e.parameterLimit===1/0?void 0:e.parameterLimit,f=c.split(e.delimiter,p),u=-1,d=e.charset;if(e.charsetSentinel)for(r=0;r<f.length;++r)0===f[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===f[r]?d="utf-8":"utf8=%26%2310003%3B"===f[r]&&(d="iso-8859-1"),u=r,r=f.length);for(r=0;r<f.length;++r){let t,c;if(r===u)continue;let p=f[r],m=p.indexOf("]="),g=-1===m?p.indexOf("="):m+1;-1===g?(t=e.decoder(p,i.decoder,d,"key"),c=e.strictNullHandling?null:""):(t=e.decoder(p.slice(0,g),i.decoder,d,"key"),c=n.F7(s(p.slice(g+1),e),function(t){return e.decoder(t,i.decoder,d,"value")})),c&&e.interpretNumericEntities&&"iso-8859-1"===d&&(c=c.replace(/&#(\d+);/g,function(t,e){return String.fromCharCode(parseInt(e,10))})),p.indexOf("[]=")>-1&&(c=o(c)?[c]:c);let h=a.call(l,t);h&&"combine"===e.duplicates?l[t]=n.kg(l[t],c):h&&"last"!==e.duplicates||(l[t]=c)}return l},c=function(t,e,r,n){let a=n?e:s(e,r);for(let e=t.length-1;e>=0;--e){let n,o=t[e];if("[]"===o&&r.parseArrays)n=r.allowEmptyArrays&&""===a?[]:[].concat(a);else{n=r.plainObjects?Object.create(null):{};let t="["===o.charAt(0)&&"]"===o.charAt(o.length-1)?o.slice(1,-1):o,e=r.decodeDotInKeys?t.replace(/%2E/g,"."):t,i=parseInt(e,10);r.parseArrays||""!==e?!isNaN(i)&&o!==e&&String(i)===e&&i>=0&&r.parseArrays&&i<=r.arrayLimit?(n=[])[i]=a:"__proto__"!==e&&(n[e]=a):n={0:a}}a=n}return a},p=function(t,e,r,n){if(!t)return;let o=r.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,i=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(o),l=s?o.slice(0,s.index):o,p=[];if(l){if(!r.plainObjects&&a.call(Object.prototype,l)&&!r.allowPrototypes)return;p.push(l)}let f=0;for(;r.depth>0&&null!==(s=i.exec(o))&&f<r.depth;){if(f+=1,!r.plainObjects&&a.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;p.push(s[1])}return s&&p.push("["+o.slice(s.index)+"]"),c(p,e,r,n)},f=function(t){if(!t)return i;if(void 0!==t.allowEmptyArrays&&"boolean"!=typeof t.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==t.decodeDotInKeys&&"boolean"!=typeof t.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==t.decoder&&void 0!==t.decoder&&"function"!=typeof t.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let e=void 0===t.charset?i.charset:t.charset,r=void 0===t.duplicates?i.duplicates:t.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===t.allowDots?!0===t.decodeDotInKeys||i.allowDots:!!t.allowDots,allowEmptyArrays:"boolean"==typeof t.allowEmptyArrays?!!t.allowEmptyArrays:i.allowEmptyArrays,allowPrototypes:"boolean"==typeof t.allowPrototypes?t.allowPrototypes:i.allowPrototypes,allowSparse:"boolean"==typeof t.allowSparse?t.allowSparse:i.allowSparse,arrayLimit:"number"==typeof t.arrayLimit?t.arrayLimit:i.arrayLimit,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:i.charsetSentinel,comma:"boolean"==typeof t.comma?t.comma:i.comma,decodeDotInKeys:"boolean"==typeof t.decodeDotInKeys?t.decodeDotInKeys:i.decodeDotInKeys,decoder:"function"==typeof t.decoder?t.decoder:i.decoder,delimiter:"string"==typeof t.delimiter||n.gd(t.delimiter)?t.delimiter:i.delimiter,depth:"number"==typeof t.depth||!1===t.depth?+t.depth:i.depth,duplicates:r,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof t.interpretNumericEntities?t.interpretNumericEntities:i.interpretNumericEntities,parameterLimit:"number"==typeof t.parameterLimit?t.parameterLimit:i.parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:"boolean"==typeof t.plainObjects?t.plainObjects:i.plainObjects,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:i.strictNullHandling}};function u(t,e){let r=f(e);if(""===t||null==t)return r.plainObjects?Object.create(null):{};let a="string"==typeof t?l(t,r):t,o=r.plainObjects?Object.create(null):{},i=Object.keys(a);for(let e=0;e<i.length;++e){let s=i[e],l=p(s,a[s],r,"string"==typeof t);o=n.h1(o,l,r)}return!0===r.allowSparse?o:n.oE(o)}},91645:t=>{"use strict";t.exports=require("net")},94735:t=>{"use strict";t.exports=require("events")},96088:()=>{},98563:t=>{t.exports={indexOf:function(t,e){var r,n;if(Array.prototype.indexOf)return t.indexOf(e);for(r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return -1},forEach:function(t,e,r){var n,a;if(Array.prototype.forEach)return t.forEach(e,r);for(n=0,a=t.length;n<a;n++)e.call(r,t[n],n,t)},trim:function(t){return String.prototype.trim?t.trim():t.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(t){return String.prototype.trimRight?t.trimRight():t.replace(/(\s*$)/g,"")}}},98995:t=>{"use strict";t.exports=require("node:module")}};var e=require("../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[3889,2481,8754],()=>r(59528));module.exports=n})();