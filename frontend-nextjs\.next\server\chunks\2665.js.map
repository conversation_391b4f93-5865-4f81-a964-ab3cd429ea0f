{"version": 3, "file": "2665.js", "mappings": "ocAoCA,EAnCA,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAQF,cACA,0CACA,cACA,SACK,CACL,EACA,GACA,wBACA,QACA,CAAK,CACL,oBACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,qBACA,QACA,CAAK,CACL,0BACA,QACA,CACA,CAAC,EACD,MAAmB,EAAQ,KAAa,EACxC,EAAoB,EAAQ,IAAiC,EADnC,GAa1B,CACA,EAAc,EAAQ,GAbK,EAae,CAC1C,CAAM,SACN,CAFqB,CAEP,EAAQ,IAAuC,CAC7D,CAEA,WAHqB,CAGb,oEAAsE,CAC9E,uBACA,iBACA,mCACA,CACA,CACA,oBACA,8BACA,cACA,CACA,cACA,eACA,kCAEA,GACA,qBAEA,aACA,aACA,iCACS,GAET,OACA,EACA,UACA,wCACA,IACA,UACA,GACA,WACA,QACA,MACA,OACA,CAAS,CACT,CACA,CACA,SAKA,oBACA,qCACA,CACA,aACA,QACA,CACA,0BACA,iBACA,KAEA,OADA,gBACA,CACA,CACA,qBACA,2CACA,CACA,6BACA,iBACA,uBAEA,WAEA,uBACA,kBACA,CACA,YACA,MACA,aAEA,IAAgB,aAAc,sBAC9B,KACA,UACA,EAAU,CACV,KACA,SACA,KAEA,EACA,gBACA,4FACA,WAGA,oFACA,KACA,EAGU,mDACV,QAHA,iCACA,MAIA,UAMA,OALA,cACA,mBACA,mBACA,iBAEA,4EACA,8FACA,OACA,YACA,iFACA,uBAA+C,yCAAyC,QAAQ,kEAAoF,GACpL,QACA,qBACA,CAAyB,CAEzB,CACA,IACA,+CAAuG,IAEvG,IACA,cACA,sBAEA,WACA,uBAEA,kBACA,QAGA,IACyB,UAEzB,MADA,OACA,CACA,CAAyB,aAKzB,OAHA,QACA,IAEA,CACA,CAAkB,SAGlB,MAFA,OACA,IACA,CACA,CACA,CAAa,EACb,CACA,WACA,WACA,wBACA,KACA,EAAc,CACd,KACA,QACA,4EAGA,WACA,OACA,6CACA,4BAEA,yBACA,eACA,wBAUA,+CAVA,EACA,wCACA,2BACA,yBAEA,OADA,cACA,uBACA,EACA,yBAEA,CAGA,CAHc,CAlBd,CAsBA,CACA,gBACA,WACA,gFACA,gDACA,CACA,kBAEA,OADA,gCAEA,CACA,wBACA,6BACA,eACA,CACA,0BACA,6BACA,UACA,IACA,UAEA,CACA,CACA,YACA,YACA,YACA,CAAC,gCCtPD,qCAA6C,CAC7C,QACA,CAAC,EAAC,OACF,gCAA+C,CAC/C,cACA,eACA,QACA,CACA,CAAC,EAAC,IACF,EAA6B,EAAQ,KAA+C,EACpF,EAA2B,EAAQ,KAA6C,CAD5C,CAEpC,UADkC,CAClC,KACA,4DACA,oBACA,IACA,yCAAiD,GAAgB,+BACjE,GAGA,YCpBA,MAAM,aAAa,OAAO,cAAc,sCAAsC,SAAW,EAAE,oBAAoB,aAAe,SAAe,SAAe,YAAkB,iCAAiC,EAAiB,eAAe,qBAAuE,OAAlD,gBAAoB,uBAA8B,eAAsB,2BAA2B,qDAAqD,SAAS,0CAA0C,iBAAiB,kDAAkD,UAAU,2CAA2C,qBAAqB,4BAA4B,UAAU,oCAAoC,gDAAgD,eAAwB,eAAe,sCAAsC,SAAW,EAAE,iBAAiB,YAAc,SAAe,SAAe,QAA8B,CAAf,MAAe,EAAc,cAAc,cAAsB,sBAAsB,8BAAgC,KAAa,mBAAmB,WAAa,EAAivB,UAAjvB,MAAsB,6BAA6B,IAAI,UAAU,UAAU,kJAA4M,OAApD,uCAAoD,GAAa,oBAAwB,IAAG,aAAY,8BAAgC,+EAAkG,kCAAkC,kEAAqF,kDAAkD,EAAE,GAAG,oEAAoE,EAAE,GAAG,2CAAmE,eAAe,uBAA17B,OAA07B,IAA6B,wDAAwD,uBAA+B,mBAA2B,iBAAyB,iBAAyB,mBAA2B,kBAAkB,wBAAoB,sBAA2B,gBAAuB,YAAkB,eAAe,sCAAsC,SAAW,EAAE,oBAAoB,aAAe,SAAe,SAAe,WAAkB,SAAiB,eAAe,qBAAuE,OAAlD,iBAAoB,sBAA8B,eAAsB,0BAA0B,qDAAqD,mBAAmB,gDAAgD,gBAAgB,+CAA+C,UAAU,gDAAgD,eAAwB,eAAe,sCAAsC,SAAW,EAAE,wBAAwB,aAAe,SAAe,SAAe,SAAe,SAAe,SAAe,gBAAsB,oCAAoC,EAAqB,cAAc,mCAAmC,6BAA6B,yCAAyC,6BAA6B,mCAAmC,qBAAqB,wBAAoB,sBAAkC,eAAsB,uBAAuB,qDAAqD,qCAAqC,iDAAiD,sCAAsC,kDAAkD,SAAS,4CAA4C,UAAU,+CAA+C,uBAAuB,6BAA6B,mBAAgC,eAAe,sCAAsC,SAAW,EAAE,kBAAkB,aAAe,SAAe,SAAe,SAAe,SAAe,SAAgB,SAAe,cAAc,oDAAoD,uCAAuC,6CAA6C,6BAA6B,uBAAuB,mCAAmC,qCAAqC,uBAAuB,qCAAqC,qBAAqE,OAAhD,iBAAoB,sBAA4B,eAAsB,2BAA2B,6EAA+E,UAAM,yCAAyC,EAAS,oBAAoB,oDAAoD,eAAe,+CAA+C,UAAU,+CAA+C,qDAAqD,aAAoB,eAAe,sCAAsC,SAAW,EAAE,oEAAoE,aAA8B,OAAf,KAAe,+CAA4D,cAAuB,6BAAgC,eAA2G,mBAAnF,WAA4B,+CAA2I,aAAhD,cAAyB,wBAAiG,gBAAlD,YAA0B,wBAAwB,CAA8B,aAAa,sCAAsC,SAAW,EAAE,oBAAqB,SAAkB,eAAe,mCAAmC,YAAY,2BAA6B,KAAwB,CAAjB,MAAiB,gBAAuB,IAAI,gBAAgB,+DAAiE,cAAc,2BAA2D,OAApB,oBAAoB,EAAS,eAAe,2BAA4D,OAArB,qBAAqB,EAAS,oBAAoB,2BAAuC,eAAkB,qBAAqB,SAAS,QAAQ,cAAwB,gBAA0B,aAAa,sCAAsC,SAAW,EAAE,oCAAoC,4DAA4D,eAAe,sCAAsC,SAAW,EAAE,wDAAwD,aAAe,SAAe,SAAe,sBAA+G,iBAAlF,aAA2B,EAAE,sDAAuS,iCAApN,YAA2C,2BAAwB,6DAA6D,SAAS,GAAG,MAAK,CAAO,iDAAiD,KAAW,CAAgE,cAAc,sCAAsC,SAAW,EAAE,iBAAiB,EAAe,QAAf,OAAe,yBAAqC,eAAe,sCAAsC,SAAW,EAAE,4BAA4B,YAAe,SAAyB,SAAS,sBAAsB,iBAAiB,sBAAsB,UAAU,SAAS,SAAS,YAAY,UAAU,aAAa,uBAAwC,aAAa,sCAAsC,SAAW,EAAE,yCAA2F,mBAAlD,YAA6B,qBAAyD,SAAkB,eAAe,WAAa,uCAAuC,uCAAuC,mBAAmB,+BAAsE,OAA3B,2BAA2B,GAAU,kBAAkB,+BAAuE,OAA5B,4BAA4B,IAAW,qBAA+B,eAAe,sCAAsC,SAAW,EAAE,cAAc,EAAe,KAAf,OAAe,mBAA4B,cAAc,sCAAsC,SAAW,EAAE,6BAA6B,YAAe,SAA0B,eAAe,mDAAmD,YAAY,oCAA2C,YAAY,oCAA2C,WAAW,mCAA0C,WAAW,mCAA0C,cAAc,uCAAwF,kBAAyB,8BAAgC,KAA2B,CAApB,MAAO,aAAa,WAA9H,uBAA8H,CAAmB,aAAa,sCAAsC,SAAW,EAAE,2BAA2B,QAAU,oBAAoB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,sBAAsB,QAAE,EAAwB,cAAyL,YAAY,WAAW,IAAK,aAAvM,YAAyB,sBAAsB,YAAY,iBAAyD,GAAxC,uBAA0B,eAAc,qBAA0B,6BAAyD,SAAoC,sBAAsC,eAAe,sCAAsC,SAAW,EAAE,kCAAkC,YAAqgB,4BAAtf,cAAuC,SAA2G,OAA0B,iBAAa,2BAAgC,UAAiB,aAAoB,OAAvN,sBAA0B,sBAAsB,sBAA8B,uBAAqB,QAAoH,CAAO,+LAAiP,CAAoD,aAAa,sCAAsC,SAAW,EAAE,sBAAsB,SAAM,GAAa,mBAAsB,sBAAyB,oBAAuB,oBAAuB,sBAAyB,0BAA6B,oBAAuB,oCAAsC,EAAG,eAAe,sCAAsC,SAAW,EAAE,uDAAuD,aAAe,SAAe,SAAe,0BAAgC,qCAA2C,EAAE,GAAG,gBAA+jB,iBAAziB,qBAAuC,MAAM,6BAA4C,mBAAmB,aAAa,4EAAkF,EAAE,GAA+B,OAA5B,4BAA4B,GAAa,0BAA0B,4DAAkE,WAAW,MAAM,GAAG,4CAA4C,UAAU,GAA+B,OAA5B,4BAA4B,GAA+F,OAAlF,OAAO,uDAAuD,GAAG,GAAG,UAAU,IAAI,IAAmN,YAAvK,YAAsB,QAAQ,sCAAqD,4BAAsC,CAAP,MAAO,4BAAiN,mBAA7I,cAA+B,0DAA0D,GAAG,GAAG,UAAU,IAAI,UAAa,IAAM,YAAa,CAAoC,eAAe,sCAAsC,SAAW,EAAE,gDAAgD,aAAe,kCAAwC,cAAoC,mBAAqB,UAAgB,aAAmB,MAAO,aAAgB,OAAS,qDAAqD,sBAAuB,mBAAgC,cAAc,cAA6B,OAAT,SAAS,GAAsD,mBAAgC,YAAa,SAAY,YAAa,SAAa,iBAAmB,MAAO,YAAkB,OAAS,qDAAqD,uBAAyC,kBAAlB,CAAwC,MAAxC,KAAkB,GAAwC,mBAAgB,qCAAwC,EAA3V,IAA2V,GAAkB,CAApW,GAAoW,YAAkB,kBAAqB,EAApZ,IAAoZ,GAA3Y,IAA6Z,MAAmB,4BAAkD,4BAAkD,eAAe,sCAAsC,SAAW,EAAE,iBAAiB,EAAe,QAAf,OAAe,yBAAqC,aAAa,sCAAsC,SAAW,EAAE,mBAAmB,SAAM,GAAa,iBAAoB,uBAA0B,8BAAgC,EAAG,aAAa,sCAAsC,SAAW,EAAE,4aAA6a,SAAgB,eAAe,qBAAqB,+BAA+B,mBAAmB,6BAA6B,yBAAyB,qCAAqC,2BAA2B,sCAAsC,6BAA6B,wCAAwC,mCAAmC,gDAAgD,iCAAiC,mCAAmC,aAAsB,UAAkB,cAAwB,mBAA2C,WAAW,qBAAsC,mBAAiD,WAAW,2BAAkD,mBAA6C,cAAc,uBAA0C,SAA2B,gBAAgB,oBAAoB,wBAA4C,oBAAgE,+BAA0D,oBAA8D,6BAAsD,oBAAsE,sCAAsE,mBAA2B,4BAA4C,8BAAgD,oCAA0D,uCAAiE,qCAA6D,+CAA8H,kBAA/C,WAA2B,oBAAoB,CAAkC,eAAe,sCAAsC,SAAW,EAAE,iDAAiD,YAAe,SAAwB,gBAAgB,qBAAqB,sBAAsC,4BAA4C,qBAAqB,mEAAmE,kBAAqB,2BAA2B,6BAA+B,aAAa,EAAE,mBAAmB,kBAAqB,WAAU,CAAE,yCAA6C,mFAAsF,sCAAsC,SAAW,EAAE,WAAW,aAAa,sCAAsC,SAAW,EAAE,qBAAqB,4DAA6D,oBAAoB,mEAAmE,kBAAqB,2BAA2B,6BAA+B,aAAa,EAAE,mBAAmB,kBAAqB,WAAU,CAAE,yCAA6C,mFAAsF,sCAAsC,SAAW,EAAE,YAAY,eAAe,sCAAsC,SAAW,EAAE,qBAAqB,EAAe,YAAf,OAAe,6BAA6C,aAAa,sCAAsC,SAAW,EAAE,8BAA+B,SAA4B,aAAa,aAAa,SAAS,SAAS,UAAU,0BAA8C,aAAa,sCAAsC,SAAW,EAAE,qDAAqD,wBAAwB,SAAS,WAA6B,YAAY,SAAS,QAAY,GAAS,gBAAwB,wBAAwB,WAAW,SAAmB,WAAS,eAAe,sCAAsC,SAAW,EAAE,eAA8B,UAAf,KAAe,uBAAiC,eAAe,sCAAsC,SAAW,EAAE,0BAA0B,YAAe,SAAuB,sCAAsC,oBAAoB,cAAc,yBAAyB,kBAAkB,YAAY,iBAAiB,YAAY,cAAc,YAAY,aAAa,YAAY,cAAc,YAAY,QAAQ,cAAc,SAAa,uBAAuB,qBAAoC,eAAe,sCAAsC,SAAW,EAAE,oBAAoB,aAAe,SAAe,SAAe,SAAe,4BAAmC,SAAiB,4BAA4B,IAA+iB,EAA3f,CAAqhB,EAAzkB,sBAA0D,8BAA8B,uCAAmC,UAA8c,OAA1B,EAApb,IAA8c,sFAA9c,4BAAkD,0BAAiC,IAAK,mBAA+B,yBAAyB,MAAM,EAAM,EAAM,sBAAuB,MAAO,qBAA8B,IAAI,qBAA8B,IAAI,IAAI,EAAK,IAAI,IAAI,KAAI,2BAA0C,wBAA8B,qBAA2B,6BAAgC,cAAkD,CAA8H,SAAxJ,CAAwJ,KAAe,sCAAsC,SAAW,EAAE,4BAA4B,YAAe,SAAyB,iBAAiB,yBAAyB,uBAAwC,eAAe,sCAAsC,SAAW,EAAE,qBAAqB,IAAe,MAAf,QAAe,UAAyB,SAAkB,qBAAqB,iBAAiB,YAAY,eAAe,eAAe,iBAAiB,0CAA0C,yBAAyB,wBAA0B,oDAAoD,aAAa,kBAAmB,sBAAsB,mFAA8E,GAAO,IAAS,aAAiB,gBAA1B,CAA0B,EAAuB,gBAA0B,eAAe,sCAAsC,SAAW,EAAE,6BAA6B,aAAe,EAAe,GAAf,SAAe,yBAAiC,EAA0B,iBAAiB,MAAM,+EAA2F,cAAc,MAAM,oCAAgD,eAAe,iBAAiB,yBAAyB,MAAM,2DAAuE,wBAA0C,aAAa,sCAAsC,SAAW,EAAE,0BAAgC,YAAa,+BAAkC,uBAA0B,+CAAkD,4CAA8C,EAAG,eAAe,sCAAsC,SAAW,EAAE,0FAA0F,aAAe,SAAe,SAAe,2DAAiE,cAAoB,6BAA6J,gBAAsB,uBAAnJ,YAA+F,gBAA7E,WAAyB,+CAA+H,YAAiE,aAA/C,YAAuB,yBAAyH,iBAAzE,cAA6B,uCAA4K,iBAAhG,YAA2B,MAAM,6CAA+D,CAAgC,eAAe,sCAAsC,SAAW,EAAE,wBAAwB,YAA8D,CAA/C,MAA+C,EAAqB,eAAe,4BAA4B,kBAAoB,SAAS,oBAAsB,gCAA4B,2BAA2B,0BAA0B,EAAS,SAAS,oBAAiD,OAA3B,2BAA2B,EAAS,OAAO,kCAAkC,YAAY,mCAAoC,SAArX,IAAqX,aAAwB,EAAS,UAAla,IAAka,CAAc,UAAU,WAAtc,GAAsc,CAA1b,GAA+c,4BAA/c,IAAY,CAAmc,yBAAyD,eAAiB,YAA7gB,KAAkiB,WAAW,mBAAqB,wBAA8B,8CAAiD,UAAW,QAAO,EAAS,UAAW,yBAA1tB,KAAyvB,wFAAzvB,GAAW,CAA8uB,GAA6F,QAAQ,wDAAwD,SAAS,YAAyE,OAA9C,8CAA8C,GAAU,mBAAgC,aAAa,sCAAsC,SAAW,EAAE,qCAAqC,qBAAuB,UAAgB,GAAG,MAAM,EAAE,aAAmB,GAAG,MAAM,QAAQ,GAAG,KAAK,EAAE,gBAA0B,EAAE,GAAG,EAAE,KAAK,UAAgB,MAAM,QAAQ,QAAuD,cAAzC,YAAwB,kBAAkG,gBAAvD,YAA0B,6BAA6B,CAA8B,cAAc,sCAAsC,SAAW,EAAE,0BAA0B,YAA2E,oBAA5D,YAA6B,+BAA+B,CAAoC,eAAe,sCAAsC,SAAW,EAAE,iEAAiE,aAAe,oCAAoC,qDAAqD,wBAAwB,gFAAgF,aAAa,sCAAsC,SAAW,EAAE,kBAAkB,SAAM,GAAa,2BAA8B,uBAA0B,uBAA0B,2BAA8B,2BAA8B,4BAA8B,EAAG,eAAe,sCAAsC,SAAW,EAAE,+EAA+E,aAAe,SAAe,cAAoB,GAAG,KAAK,aAAmB,GAAG,IAAI,cAA2B,wCAAwE,cAA0B,uCAA1D,mBAAiG,kBAAuH,qBAAzF,YAA+B,kCAA+J,kBAA7D,YAA4B,iCAAiC,CAAkC,aAAa,sCAAsC,SAAW,EAAE,wBAAwB,SAAM,GAAa,qBAAwB,eAAkB,qBAAwB,wCAA0C,EAAG,aAAa,sCAAsC,SAAW,EAAE,oBAAoB,SAAM,GAAa,mBAAsB,yBAA4B,iCAAkC,CAAG,aAAa,sCAAsC,SAAW,EAAE,iBAAiB,oBAAoB,KAAS,cAAgC,WAAW,cAAkB,iBAAiB,YAAY,YAAY,KAAW,IAAI,mCAAqD,KAAQ,QAAQ,eAAiB,iBAAiB,mBAAiF,SAAS,MAAc,sBAAR,EAAQ,cAAsC,SAAW,EAAzD,EAA2D,MAA3D,EAA2D,YAA3D,EAA2D,QAA3D,EAA2D,sCAA3D,EAA2D,gBAA3D,EAA2D,+BAA3D,EAA2D,eAA3D,EAA2D,mBAA3D,EAA2D,iBAA3D,EAA2D,WAA3D,EAA2D,0BAA3D,EAA2D,iBAA3D,EAA2D,oBAA3D,EAA2D,YAA3D,EAA2D,qBAA3D,EAA2D,qBAA3D,EAA2D,UAA3D,EAA2D,gBAA3D,EAA2D,aAA3D,EAA2D,kBAA3D,EAA2D,wEAA6c,aAA+B,sBAAviB,EAAuiB,kCAA0D,6BAA+B,yCAAyC,EAAE,aAA+B,sBAA1sB,EAA0sB,oBAA4C,6BAA+B,2BAA2B,EAAE,sBAAlzB,EAAkzB,gBAAwC,6BAA+B,uBAAuB,EAAE,aAA+B,sBAAj7B,EAAi7B,qBAA6C,6BAA+B,4BAA4B,EAAE,aAA+B,sBAA1jC,EAA0jC,gBAAwC,6BAA+B,uBAAuB,EAAE,aAA+B,sBAAzrC,EAAyrC,mBAA2C,6BAA+B,0BAA0B,EAAE,aAA+B,sBAA9zC,EAA8zC,aAAqC,6BAA+B,oBAAoB,EAAE,aAA+B,sBAAv7C,EAAu7C,wBAAgD,6BAA+B,+BAA+B,EAAE,sBAAviD,EAAuiD,wBAAgD,6BAA+B,+BAA+B,EAAE,aAA+B,sBAAtrD,EAAsrD,eAAuC,6BAA+B,sBAAsB,EAAE,aAA+B,sBAAnzD,EAAmzD,uBAA+C,6BAA+B,8BAA8B,EAAE,aAA+B,sBAAh8D,EAAg8D,oBAA4C,6BAA+B,2BAA2B,EAAE,aAA+B,sBAAvkE,EAAukE,YAAoC,6BAA+B,mBAAmB,EAAE,aAA+B,sBAA9rE,EAA8rE,kBAA0C,6BAA+B,yBAAyB,EAAE,aAA+B,sBAAj0E,EAAi0E,cAAsC,6BAA+B,qBAAqB,EAAE,YAA8B,sBAA37E,EAA27E,oBAA4C,6BAA+B,2BAA2B,EAAE,aAA+B,sBAAlkF,EAAkkF,sBAA8C,6BAA+B,6BAA6B,EAAE,sBAA9qF,EAA8qF,kBAA0C,6BAA+B,yBAAyB,EAAE,sBAAlxF,EAAkxF,iBAAyC,6BAA+B,wBAAwB,EAAE,aAA+B,sBAAn5F,EAAm5F,kBAA0C,6BAA+B,yBAAyB,EAAE,sBAAv/F,EAAu/F,mBAA2C,6BAA+B,0BAA0B,EAAE,sBAA7lG,EAA6lG,wBAAgD,6BAA+B,+BAA+B,EAAE,YAAgC,sBAA7uG,EAA6uG,WAAmC,6BAA+B,kBAAkB,EAAE,aAAiC,sBAAp2G,EAAo2G,QAAgC,6BAA+B,eAAe,EAAE,aAAiC,sBAAr9G,EAAq9G,WAAmC,6BAA+B,kBAAkB,EAAE,aAAiC,sBAA5kH,EAA4kH,eAAuC,6BAA+B,sBAAsB,EAAE,aAAiC,sBAA3sH,EAA2sH,SAAiC,6BAA+B,gBAAgB,EAAE,WAAc,0FAAyF,GAAI,aAAiB,8BCM163B,SAASA,EACdC,CAAuB,EAEvB,OACc,OAAZA,GACA,iBAAOA,GACP,SAAUA,GACc,YAAxB,OAAOA,EAAQC,IAAI,uFAPPF,qCAAAA,kCCLhB,gDAAkF,6BCI3E,SAASG,EAAiBC,CAAY,EAC3C,OAAOA,EAAKC,OAAO,CAAC,MAAO,IAC7B,6FAFgBF,qCAAAA,mCCJhB,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAWF,SANA,KACA,0CACA,cACA,SACK,CACL,EACA,GAIA,mBACA,QACA,CAAK,CACL,0BACA,QACA,CACA,CAAC,EACD,MAAuB,EAAQ,KAA+B,EAC9D,UAD8B,CAC9B,GACA,6DACA,CAcA,qBACA,KACA,EAoBA,mDAnCA,YACA,kBACA,gCAEA,iCACA,YACA,mBAEA,QACA,CACA,QACA,CAAK,CACL,EAuBA,8BACA,aACA,cACA,eACA,CAAK,CACL,uKClEaG,0BAA0B,mBAA1BA,GAkBGC,mCAAmC,mBAAnCA,GAXAC,0BAA0B,mBAA1BA,aAViB,OAGpBF,EAA6B,CACxC,WACA,MACA,OACA,QACD,CAEM,SAASE,EAA2BJ,CAAY,EAErD,OAKUK,SAJRL,EACGM,KAAK,CAAC,KACNC,IAAI,CAAC,GACJL,EAA2BK,IAAI,CAAC,GAAOC,EAAQC,UAAU,CAACC,IAGlE,CAEO,SAASP,EAAoCH,CAAY,EAC9D,IAAIW,EACFC,EACAC,EAEF,IAAK,IAAML,KAAWR,EAAKM,KAAK,CAAC,KAAM,GACrCM,CACIA,CADKV,EAA2BK,IAAI,CAAC,GAAOC,EAAQC,UAAU,CAACC,IACvD,CACT,CAACC,EAAmBE,EAAiB,CAAGb,EAAKM,KAAK,CAACM,EAAQ,GAC5D,KACF,CAGF,GAAI,CAACD,GAAqB,CAACC,GAAU,CAACC,EACpC,MAAM,UADgD,WAGrD,CAFK,MACH,+BAA8Bb,EAAK,qFADhC,+DAEN,GAKF,OAFAW,EAAoBG,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBH,GAE7BC,GACN,IAAK,MAGDC,EADwB,CAL0B,IAKrB,CAA3BF,EACkB,IAAGE,EAEJF,EAAoB,IAAME,EAE/C,KACF,KAAK,OAEH,GAAIF,KAA2B,GAbsE,MAc7F,qBAEL,CAFK,MACH,+BAA8BX,EAAK,gEADhC,+DAEN,GAEFa,EAAmBF,EAChBL,KAAK,CAAC,KACNS,KAAK,CAAC,EAAG,CAAC,GACVC,MAAM,CAACH,GACPI,IAAI,CAAC,KACR,KACF,KAAK,QAEHJ,EAAmB,IAAMA,EACzB,KACF,KAAK,WAGH,IAAMK,EAAyBP,EAAkBL,KAAK,CAAC,KACvD,GAAIY,EAAuBC,MAAM,EAAI,EACnC,CADsC,KAChC,qBAEL,CAFK,MACH,+BAA8BnB,EAAK,mEADhC,+DAEN,GAGFa,EAAmBK,EAChBH,KAAK,CAAC,EAAG,CAAC,GACVC,MAAM,CAACH,GACPI,IAAI,CAAC,KACR,KACF,SACE,MAAM,qBAAyC,CAAzC,MAAU,gCAAV,+DAAwC,EAClD,CAEA,MAAO,mBAAEN,EAAmBE,kBAAiB,CAC/C,uKClEgBC,gBAAgB,mBAAhBA,GAmCAM,eAAe,mBAAfA,aAzDmB,WACJ,OAqBxB,SAASN,EAAiBO,CAAa,EAC5C,MAAOC,CAAAA,EAAAA,EAAAA,kBAAAA,EACLD,EAAMf,KAAK,CAAC,KAAKiB,MAAM,CAAC,CAACC,EAAUhB,EAASiB,EAAOC,IAEjD,CAAKlB,GAKDmB,CAAAA,EAAAA,EAAAA,CALU,aAKVA,EAAenB,IAKA,KAAK,CALK,CAKlB,CAAC,EAAE,EAMXA,CAAY,SAAZA,GAAkC,UAAZA,CAAY,EAAM,CACzCiB,IAAUC,EAASP,MAAM,CAAG,EAhBrBK,CAiBP,CAIQA,EAAS,IAAGhB,EACrB,IAEP,CAMO,SAASY,EAAgBQ,CAAW,EACzC,OAAOA,EAAI3B,OAAO,CAChB,cACA,KAGJ,8BC/DO,SAAS4B,EAAoBC,CAAU,EAC5C,OAAOC,OAAOC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,EACxC,CAEO,SAASK,EAAcL,CAAU,EACtC,GAAID,mBAAkD,GAA9BC,GACtB,OAAO,EAGT,IAAME,EAAYD,OAAOK,cAAc,CAACN,GAWxC,OAAqB,OAAdE,GAAsBA,EAAUK,cAAc,CAAC,gBACxD,wIArBgBR,mBAAmB,mBAAnBA,GAIAM,aAAa,mBAAbA,wKC6NAG,qBAAqB,mBAArBA,GAtBAC,eAAe,mBAAfA,IA3MhB,OAAMC,EAOJC,OAAOC,CAAe,CAAQ,CAC5B,IAAI,CAACC,OAAO,CAACD,EAAQpC,KAAK,CAAC,KAAKsC,MAAM,CAACC,SAAU,EAAE,EAAE,EACvD,CAEAC,QAAmB,CACjB,OAAO,IAAI,CAACC,OAAO,EACrB,CAEQA,QAAQC,CAAoB,CAAY,CAAhCA,KAAAA,IAAAA,GAAAA,GAAiB,KAC/B,IAAMC,EAAgB,IAAI,IAAI,CAACC,QAAQ,CAACC,IAAI,GAAG,CAACC,IAAI,EAC9B,MAAM,EAAxB,IAAI,CAACC,QAAQ,EACfJ,EAAcK,MAAM,CAACL,EAAcM,OAAO,CAAC,MAAO,GAEhD,MAA4B,KAAxB,CAACC,YAAY,EACnBP,EAAcK,MAAM,CAACL,EAAcM,OAAO,CAAC,SAAU,GAErB,MAAM,CAApC,IAAI,CAACE,oBAAoB,EAC3BR,EAAcK,MAAM,CAACL,EAAcM,OAAO,CAAC,WAAY,GAGzD,IAAMG,EAAST,EACZU,GAAG,CAAC,GAAO,IAAI,CAACT,QAAQ,CAACU,GAAG,CAACC,GAAId,OAAO,CAAE,GAAEC,EAASa,EAAE,MACvDtC,MAAM,CAAC,CAACuC,EAAMC,IAAS,IAAID,KAASC,EAAK,CAAE,EAAE,EAQhD,GANsB,MAAM,CAAxB,IAAI,CAACV,QAAQ,EACfK,EAAOM,IAAI,IACN,IAAI,CAACd,QAAQ,CAACU,GAAG,CAAC,MAAOb,OAAO,CAAIC,EAAO,IAAG,IAAI,CAACK,QAAQ,CAAC,OAI/D,CAAC,IAAI,CAACY,WAAW,CAAE,CACrB,IAAMC,EAAe,MAAXlB,EAAiB,IAAMA,EAAOjC,KAAK,CAAC,EAAG,CAAC,GAClD,GAAiC,MAAM,IAA/B,CAAC0C,oBAAoB,CAC3B,MAAM,qBAEL,CAFK,MACH,uFAAsFS,EAAE,UAASA,EAAE,QAAO,IAAI,CAACT,oBAAoB,CAAC,SADjI,+DAEN,GAGFC,EAAOS,OAAO,CAACD,EACjB,CAkBA,OAhB0B,MAAM,CAA5B,IAAI,CAACV,YAAY,EACnBE,EAAOM,IAAI,IACN,IAAI,CAACd,QAAQ,CACbU,GAAG,CAAC,SACJb,OAAO,CAAIC,EAAO,OAAM,IAAI,CAACQ,YAAY,CAAC,OAIf,MAAM,CAApC,IAAI,CAACC,oBAAoB,EAC3BC,EAAOM,IAAI,IACN,IAAI,CAACd,QAAQ,CACbU,GAAG,CAAC,WACJb,OAAO,CAAIC,EAAO,QAAO,IAAI,CAACS,oBAAoB,CAAC,QAInDC,CACT,CAEQf,QACNyB,CAAkB,CAClBC,CAAmB,CACnBC,CAAmB,CACb,CACN,GAAwB,IAApBF,EAASjD,MAAM,CAAQ,CACzB,IAAI,CAAC8C,WAAW,EAAG,EACnB,MACF,CAEA,GAAIK,EACF,MAAM,IADQ,GACR,cAAwD,CAAxD,MAAW,+CAAX,+DAAuD,GAI/D,IAAIC,EAAcH,CAAQ,CAAC,EAAE,CAG7B,GAAIG,EAAY9D,UAAU,CAAC,MAAQ8D,EAAYC,QAAQ,CAAC,KAAM,CAE5D,IAAIC,EAAcF,EAAYxD,KAAK,CAAC,EAAG,CAAC,GAEpC2D,GAAa,EAOjB,GANID,EAAYhE,UAAU,CAAC,MAAQgE,EAAYD,QAAQ,CAAC,MAAM,CAE5DC,EAAcA,EAAY1D,KAAK,CAAC,EAAG,CAAC,GACpC2D,GAAa,GAGXD,EAAYhE,UAAU,CAAC,KACzB,CAD+B,KACzB,qBAEL,CAFSkE,MACP,6CAA4CF,EAAY,6BADrD,+DAEN,GASF,GANIA,EAAYhE,UAAU,CAAC,QAAQ,CAEjCgE,EAAcA,EAAYG,SAAS,CAAC,GACpCN,GAAa,GAGXG,EAAYhE,UAAU,CAAC,MAAQgE,EAAYD,QAAQ,CAAC,KACtD,CAD4D,KACtD,qBAEL,CAFK,MACH,4DAA2DC,EAAY,OADpE,+DAEN,GAGF,GAAIA,EAAYhE,UAAU,CAAC,KACzB,CAD+B,KACzB,qBAEL,CAFSkE,MACP,wDAAuDF,EAAY,OADhE,+DAEN,GAGF,SAASI,EAAWC,CAA2B,CAAEC,CAAgB,EAC/D,GAAqB,MAAM,CAAvBD,GAMEA,IAAiBC,EAEnB,MAAM,EAFuB,KAEvB,cAEL,CAFK,MACH,mEAAkED,EAAa,UAASC,EAAS,OAD9F,+DAEN,GAIJV,EAAUW,OAAO,CAAEC,IACjB,GAAIA,IAASF,EACX,MAAM,EADe,KACf,cAEL,CAFK,MACH,uCAAsCA,EAAS,yCAD5C,+DAEN,GAGF,GAAIE,EAAKhF,OAAO,CAAC,MAAO,MAAQsE,EAAYtE,OAAO,CAAC,MAAO,IACzD,CAD8D,KACxD,qBAEL,CAFK,MACH,mCAAkCgF,EAAK,UAASF,EAAS,kEADtD,+DAEN,EAEJ,GAEAV,EAAUL,IAAI,CAACe,EACjB,CAEA,GAAIT,EACF,GAAII,EAAY,CACd,GAAyB,CAFb,KAER,IAAI,CAAClB,YAAY,CACnB,MAAM,qBAEL,CAFK,MACH,wFAAuF,IAAI,CAACA,YAAY,CAAC,WAAUY,CAAQ,CAAC,EAAE,CAAC,QAD5H,+DAEN,GAGFS,EAAW,IAAI,CAACpB,oBAAoB,CAAEgB,GAEtC,IAAI,CAAChB,oBAAoB,CAAGgB,EAE5BF,EAAc,SAChB,KAAO,CACL,GAAiC,MAAM,IAA/B,CAACd,oBAAoB,CAC3B,MAAM,qBAEL,CAFK,MACH,yFAAwF,IAAI,CAACA,oBAAoB,CAAC,YAAWW,CAAQ,CAAC,EAAE,CAAC,OADtI,+DAEN,GAGFS,EAAW,IAAI,CAACrB,YAAY,CAAEiB,GAE9B,IAAI,CAACjB,YAAY,CAAGiB,EAEpBF,EAAc,OAChB,KACK,CACL,GAAIG,EACF,MAAM,IADQ,GACR,cAEL,CAFSC,MACP,qDAAoDP,CAAQ,CAAC,EAAE,CAAC,OAD7D,+DAEN,GAEFS,EAAW,IAAI,CAACxB,QAAQ,CAAEoB,GAE1B,IAAI,CAACpB,QAAQ,CAAGoB,EAEhBF,EAAc,IAChB,CACF,CAGI,IAAK,CAACrB,QAAQ,CAACgC,GAAG,CAACX,IACrB,IAAI,CAACrB,KAD8B,GACtB,CAACiC,GAAG,CAACZ,EAAa,IAAI/B,GAGrC,IAAI,CAACU,QAAQ,CACVU,GAAG,CAACW,GACJ5B,OAAO,CAACyB,EAASrD,KAAK,CAAC,GAAIsD,EAAWC,EAC3C,oBAvMAL,WAAAA,EAAuB,OACvBf,QAAAA,CAAiC,IAAIkC,SACrC/B,QAAAA,CAA0B,UAC1BG,YAAAA,CAA8B,UAC9BC,oBAAAA,CAAsC,KAoMxC,CAEO,SAASlB,EACd8C,CAAsC,EAatC,IAAMC,EAAO,IAAI9C,EAKjB,OAFA6C,EAAgBL,OAAO,CAAC,GAAcM,EAAK7C,MAAM,CAAC8C,IAE3CD,EAAKxC,MAAM,EACpB,CAEO,SAASR,EACdkD,CAAY,CACZC,CAA0B,EAI1B,IAAMC,EAAkC,CAAC,EACnCC,EAAsB,EAAE,CAC9B,IAAK,IAAIC,EAAI,EAAGA,EAAIJ,EAAQrE,MAAM,CAAEyE,IAAK,CACvC,IAAMpE,EAAWiE,EAAOD,CAAO,CAACI,EAAE,EAClCF,CAAO,CAAClE,EAAS,CAAGoE,EACpBD,CAAS,CAACC,EAAE,CAAGpE,CACjB,CAOA,OAJee,EAAgBoD,GAIjBhC,GAAG,CAAEnC,GAAagE,CAAO,CAACE,CAAO,CAAClE,EAAS,CAAC,CAC5D,0BCrOAqE,EAAOC,OAAO,CARqB,CACjC,CAOeC,WANf,UACA,aACA,WACA,YACD,4BCTD,IAAS,CAHT,YACA,0BAA2C,UAC3C,8BCDA,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAWF,SANA,KACA,0CACA,cACA,SACK,CACL,EACA,GACA,wBACA,QACA,CAAK,CACL,gCACA,QACA,CACA,CAAC,EACD,OACA,cACA,cACA,cACA,mBACA,kBACA,EACA,uBACA,cACA,2BACA,+HCvBgBC,qCAAAA,aAXe,WACE,OAU1B,SAASA,EAAoBC,CAAY,EAC9C,IAAIC,EAAQnG,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBkG,GAC7B,OAAOC,EAAMzF,UAAU,CAAC,YAAc,CAAC0F,CAAAA,EAAAA,EAAAA,cAAAA,EAAeD,GAClDA,EAAMnF,KAAK,CAAC,GACF,WAAVmF,EACEA,EACA,GACR,uKCqZaE,IAAI,mBAAJA,GAitBGC,IAAI,mBAAJA,GAiCAC,IAAI,mBAAJA,GA7MHC,UAAU,mBAAVA,GAwNb,OAsBC,mBAtBoBC,8dAlqCW,YACM,WAWT,UAEQ,6DACjB,YAKb,UAEuB,WAEJ,UACQ,sIAwBlC,IAAMC,EAAwB,IAAIC,IAElC,SAASC,EACPC,CAA4B,CAC5BpF,CAAgB,CAChBqF,CAAkB,EAElB,IAAMC,EAAiCC,CAAAA,EAAAA,EAAAA,YAAAA,EAAaH,EAAe,SAC7DI,EACJC,EACI,EAD+B,CAE/BF,GAAAA,EAFJE,YAEIF,EAAaH,EAAepF,GAElC,MAAO,aACLsF,YACAE,EACAE,SAAU,IAAI,IAAIR,IAAI,IAAII,KAAgBE,EAAU,EACtD,CACF,CAEA,SAASG,EAAmBC,CAAkB,CAAEC,CAAkB,EAGhE,GAAM,aACJC,CAAW,eACXV,CAAa,kBACbW,CAAgB,yBAChBC,CAAuB,aACvBC,CAAW,CACZ,CAAGL,EAEJ,OAAOR,EAAcc,aAAa,CAC/B9E,MAAM,CACL,GAAc+E,EAASnD,QAAQ,CAAC,QAAU,CAACmD,EAASnD,QAAQ,CAAC,eAE9Db,GAAG,CAAC,GACH,QADIgE,EACHC,SADGD,CAGFE,MAAO,CAACL,EACRM,MAAOT,EAAMS,KAAK,CAClBL,YAAaJ,EAAMI,WAAW,EAAIA,EAClCM,UAAU,EACVC,IAAK,GAAGV,EAAY,OAAO,EAAEW,GAAAA,EAAAA,aAAAA,EAC3BN,GAAAA,EACEJ,EAAAA,CAAkB,EAPjBI,GAUb,CAMA,SAASO,EAAU,QACjBC,CAAM,CAGP,EACC,GAAI,CAACA,EAAQ,OAAO,KAGpB,IAAMC,EAAuCC,MAAMC,OAAO,CAACH,GACtDA,EACD,EAAE,CACN,GACE,EACOd,KAAK,EAEZgB,EADA,IACMC,OAAO,CAACH,EAAOd,KAAK,CAACnE,QAAQ,EACnC,CACA,IAAMqF,EAAaC,QACjBA,EAAAA,GANgE,MAMhEA,MAAAA,CAAAA,EAAAA,CAJgE,EAIhEA,IAAAA,EAAAA,EAAInB,KAAAA,GAA8B,OAAlCmB,EAAAA,EAAWC,uBAAAA,EAAuB,OAAlCD,EAAoCE,MAAM,EAE5CP,EAAOd,KAAK,CAACnE,QAAQ,CAAC8B,OAAO,CAAC,IACxBqD,MAAMC,OAAO,CAACK,GAChBA,EAAM3D,GADkB,IACX,CAAC,GAAQuD,EAAUC,IAAOJ,EAAUpE,IAAI,CAACwE,IAC7CD,EAAUI,IACnBP,EAAUpE,EADiB,EACb,CAAC2E,EAEnB,EACF,CAGA,MACE,UAACC,QAAAA,CACCC,aAAW,GACXJ,wBAAyB,CACvBC,OAAQN,EACLzE,GAAG,CAAEiF,GAAUA,EAAMvB,KAAK,CAACoB,uBAAuB,CAACC,MAAM,EACzDzH,IAAI,CAAC,IACLhB,OAAO,CAAC,iCAAkC,IAC1CA,OAAO,CAAC,2BAA4B,GACzC,GAGN,CAEA,SAAS6I,EACP1B,CAAkB,CAClBC,CAAkB,CAClB0B,CAAoB,EAEpB,GAAM,gBACJC,CAAc,aACd1B,CAAW,eACX2B,CAAa,kBACb1B,CAAgB,yBAChBC,CAAuB,aACvBC,CAAW,CACZ,CAAGL,EAEJ,OAAO4B,EAAerF,GAAG,CAAEuF,GACzB,CAAKA,EAAK1E,QAAQ,CAAC,QAAUuE,EAAM7B,QAAQ,CAACiC,QAAQ,CAACD,GAAc,IAAP,CAG1D,UAACtB,SAAAA,CACCwB,MAAO,CAACH,GAAiBzB,EACzBK,MAAO,CAACL,EAERQ,IAAK,GAAGV,EAAY,OAAO,EAAEW,CAAAA,EAAAA,EAAAA,aAAAA,EAAciB,GAAAA,EAAQ3B,EAAAA,CAAkB,CACrEO,MAAOT,EAAMS,KAAK,CAClBL,YAAaJ,EAAMI,WAAW,EAAIA,GAH7ByB,GAOb,CAEA,SAASG,EACPjC,CAAkB,CAClBC,CAAkB,CAClB0B,CAAoB,MAYOnC,EAV3B,GAAM,aACJU,CAAW,CACXV,eAAa,eACbqC,CAAa,kBACb1B,CAAgB,yBAChBC,CAAuB,aACvBC,CAAW,CACZ,CAAGL,EAOJ,MAAO,IALe2B,EAAM7B,QAAQ,CAACtE,MAAM,CAAC,GAAUsG,EAAK1E,QAAQ,CAAC,WAC9D8E,MAAAA,CAAAA,EAAqB1C,EAAc2C,gBAAAA,EAAgB,OAA9B3C,EAAgChE,MAAM,CAAC,GAChEsG,EAAK1E,QAAQ,CAAC,QAGgC,CAACb,GAAG,CAAC,GAEjD,UAACiE,SAAAA,CAECI,IAAK,GAAGV,EAAY,OAAO,EAAEW,CAAAA,EAAAA,EAAAA,aAAAA,EAAciB,GAAAA,EAAQ3B,EAAAA,CAAkB,CACrEO,MAAOT,EAAMS,KAAK,CAClBsB,MAAO,CAACH,GAAiBzB,EACzBK,MAAO,CAACL,EACRC,YAAaJ,EAAMI,WAAW,EAAIA,GAL7ByB,GASb,CA6GA,SAASM,EAAkBpC,CAAkB,CAAEC,CAAkB,EAC/D,GAAM,CAAEoC,cAAY,yBAAEjC,CAAuB,aAAEC,CAAW,CAAE,CAAGL,EAEzDsC,EA9GR,SAASC,CAA0C,CAAEtC,CAAkB,EACrE,GAAM,aAAEC,CAAW,cAAEmC,CAAY,aAAEhC,CAAW,mBAAEmC,CAAiB,CAAE,CAAGxC,EAGtE,GAAI,CAACwC,EAA0D,OAAO,KAEtE,GAAI,CAEF,GAJwB3C,CAIlB4C,OAJ0C,KAAK,MAI/B,CAAE,CAAGC,OAAuBA,CAChD,qCAQIC,EAAoB7G,CALTmF,MAAMC,OAAO,CAACjB,EAAMnE,QAAQ,EACzCmE,EAAMnE,QAAQ,CACd,CAACmE,EAAMnE,QAAQ,CAAC,EAGe3C,IAAI,CACpCoI,QAECA,EAAAA,QAlIC,CAAC,CAiIgBA,GAjIN,CAAC,CAACA,EAAMtB,KAAK,GAkIzBsB,MAAAA,CAAAA,EAAY,GAAZA,IAAAA,EAAAA,EAAOtB,KAAAA,GAAK,OAAZsB,EAAAA,EAAcF,uBAAAA,EAAuB,OAArCE,EAAuCD,MAAM,CAACvH,MAAAA,GAC9C,0BAA2BwH,EAAMtB,KAAK,GAG1C,MACE,YADF,CACE,oBACG,CAAC0C,GACA,UAACnC,QADDmC,CACCnC,CACCoC,SAFFD,eAEwB,GACtBtB,wBAAyB,CACvBC,OAAQ,CAAC;;oBAEH,EAAEpB,EAAY;;UAExB,CACE,IAGJ,UAACM,SAAAA,CACCqC,iBAAe,GACfxB,wBAAyB,CACvBC,OAAQmB,GACV,KAEAJ,EAAaS,MAAM,EAAI,IAAIvG,GAAG,CAAC,CAACuF,EAAmBzH,KACnD,GAAM,UACJ0I,CAAQ,KACRnC,CAAG,CACH9E,SAAUkH,CAAc,yBACxB3B,CAAuB,CACvB,GAAG4B,EACJ,CAAGnB,EAEAoB,EAGA,CAAC,EAEL,GAAItC,EAEFsC,EAAStC,CAFF,EAEK,CAAGA,OACV,GACLS,GACAA,EAAwBC,MAAM,CAG9B4B,CAFA,CAES7B,uBAAuB,CAAG,CACjCC,OAAQD,EAAwBC,MAAM,OAEnC,GAAI0B,EAETE,EAAS7B,YAFgB,WAEO,CAAG,CACjCC,OAC4B,UAA1B,OAAO0B,EACHA,EACA/B,MAAMC,OAAO,CAAC8B,GACZA,EAAenJ,IAAI,CAAC,IACpB,EACV,OAEA,MAAM,qBAEL,CAFK,MACJ,gJADI,8DAEN,GAGF,MACE,YADF,MACE,EAAC2G,SAAAA,CACE,GAAG0C,CAAQ,CACX,GAAGD,CAAW,CACfE,KAAK,iBACLC,IAAKxC,GAAOvG,EACZqG,MAAOT,EAAMS,KAAK,CAClB2C,eAAa,SACbhD,YAAaJ,EAAMI,WAAW,EAAIA,GAGxC,KAGN,CAAE,MAAOiD,EAAK,CAIZ,MAHIC,GAAAA,EAAAA,OAAAA,EAAQD,IAAqB,oBAAoB,CAAjCA,EAAIE,IAAI,EAC1BC,QAAQC,IAAI,CAAC,CAAC,SAAS,EAAEJ,EAAIK,OAAO,EAAE,EAEjC,IACT,CACF,EAKmD3D,EAASC,GAEpD2D,EAA4BvB,CAAAA,EAAawB,iBAAiB,EAAI,IACjErI,MAAM,CAAC,GAAYgF,EAAOI,GAAG,EAC7BrE,GAAG,CAAC,CAACuF,EAAmBzH,KACvB,GAAM,UAAE0I,CAAQ,CAAE,GAAGE,EAAa,CAAGnB,EACrC,MACE,YADF,MACE,EAACtB,SAAAA,CACE,GAAGyC,CAAW,CACfG,IAAKH,EAAYrC,GAAG,EAAIvG,EACxBoG,MAAOwC,EAAYxC,KAAK,EAAI,CAACL,EAC7BM,MAAOT,EAAMS,KAAK,CAClB2C,eAAa,oBACbhD,YAAaJ,EAAMI,WAAW,EAAIA,GAGxC,GAEF,MACE,YADF,CACE,oBACGiC,EACAsB,IAGP,CA8EO,MAAM5E,UAAa8E,EAAAA,OAAK,CAACC,SAAS,gBAChCC,WAAAA,CAAcC,EAAAA,WAAW,aAIpBtC,CAAoB,CAAwB,CACtD,GAAM,aACJzB,CAAW,kBACXC,CAAgB,gBAChByB,CAAc,oBACdsC,CAAkB,aAClB7D,CAAW,aACX8D,CAAW,CACZ,CAAG,IAAI,CAACnE,OAAO,CACVoE,EAAWzC,EAAM7B,QAAQ,CAACtE,MAAM,CAAC,GAAO6I,EAAEjH,QAAQ,CAAC,SACnDsC,EAA2B,IAAIJ,IAAIqC,EAAMjC,WAAW,EAItD4E,EAA8B,IAAIhF,IAAI,EAAE,EACxCiF,EAAuBtD,MAAMuD,IAAI,CACnC,IAAIlF,IAAIsC,EAAepG,MAAM,CAAC,GAAUsG,EAAK1E,QAAQ,CAAC,WAExD,GAAImH,EAAqBxK,MAAM,CAAE,CAC/B,IAAM0K,EAAW,IAAInF,IAAI8E,GAIzBE,EAAiB,IAAIhF,IAHrBiF,EAAuBA,EAAqB/I,MAAM,CAChD,GAAO,CAAEiJ,CAAAA,EAAS3G,GAAG,CAACuG,IAAM3E,EAAY5B,GAAG,CAACuG,EAAAA,CAAAA,CAAC,EAG/CD,EAASxH,IAAI,IAAI2H,EACnB,CAEA,IAAIG,EAAiC,EAAE,CAwCvC,OAvCAN,EAASxG,OAAO,CAAC,IACf,IAAM+G,EAAejF,EAAY5B,GAAG,CAACgE,GAC/B8C,EAAkBN,EAAexG,GAAG,CAACgE,GACrC+C,EAA6BX,EAAmBpG,GAAG,CAACgE,EAEtD,CAACqC,GACHO,EAAgB9H,IAAI,CAClB,UAACkI,OAAAA,CAECpE,MAAO,IAAI,CAACT,KAAK,CAACS,KAAK,CACvBqE,IAAI,UACJC,KAAM,GAAG9E,EAAY,OAAO,EAAEW,CAAAA,EAAAA,EAAAA,aAAAA,EAC5BiB,GAAAA,EACE3B,EAAAA,CAAkB,CACtB8E,GAAG,QACH5E,YAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,EAAIA,GAPlC,GAAGyB,EAAK,QAAQ,CAAC,GAY5B4C,EAAgB9H,IAAI,CAClB,UAACkI,CADiB,MACjBA,CAECpE,MAAO,IAAI,CAACT,KAAK,CAACS,KAAK,CACvBqE,IAAI,aACJC,KAAM,GAAG9E,EAAY,OAAO,EAAEW,CAAAA,EAAAA,EAAAA,aAAAA,EAC5BiB,GAAAA,EACE3B,EAAAA,CAAkB,CACtBE,YAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,EAAIA,EACvC6E,WAAUN,OAAkB3L,EAAY0L,EAAe,QAAK1L,EAC5DkM,WACER,GAAgBC,GAAmBC,OAC/B5L,EACA,IAXD6I,GAeX,GAEkC,IAA3B4C,EAAgB3K,MAAM,CAAS,KAAO2K,CAC/C,CAEAU,yBAA0B,CACxB,GAAM,gBAAExD,CAAc,aAAE1B,CAAW,kBAAEC,CAAgB,aAAEE,CAAW,CAAE,CAClE,IAAI,CAACL,OAAO,CAEd,OACE4B,EACGrF,GAAG,CAAC,GACH,EAAUa,EAAN,MAAc,CAAC,OAKjB,UAAC0H,OAAAA,CACCC,IAAI,UAEJC,KAAM,GAAG9E,EAAY,OAAO,EAAEW,CAAAA,EAAAA,EAAAA,aAAAA,EAC5BiB,GAAAA,EACE3B,EAAAA,CAAkB,CACtB8E,GAAG,SACHvE,MAAO,IAAI,CAACT,KAAK,CAACS,KAAK,CACvBL,YAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,EAAIA,GANlCyB,GANA,MAiBVtG,MAAM,CAACC,QAEd,CAEA4J,oBAAoB1D,CAAoB,CAAwB,CAC9D,GAAM,aAAEzB,CAAW,kBAAEC,CAAgB,cAAEkC,CAAY,aAAEhC,CAAW,CAAE,CAChE,IAAI,CAACL,OAAO,CACRsF,EAAe3D,EAAM7B,QAAQ,CAACtE,MAAM,CAAC,GAClCsG,EAAK1E,QAAQ,CAAC,QAGvB,MAAO,IACDiF,CAAAA,EAAawB,iBAAiB,EAAI,IAAItH,GAAG,CAAC,GAC5C,IAD6CuF,CAC7C,KAACgD,KAD4ChD,EAC5CgD,CAECpE,MAAO,IAAI,CAACT,KAAK,CAACS,KAAK,CACvBqE,IAAI,UACJC,KAAMlD,EAAKlB,GAAG,CACdqE,GAAG,SACH5E,YAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,EAAIA,GALlCyB,EAAKlB,GAAG,MAQd0E,EAAa/I,GAAG,CAAC,GAClB,UAACuI,KADkBhD,EAClBgD,CAECpE,MAAO,IAAI,CAACT,KAAK,CAACS,KAAK,CACvBqE,IAAI,UACJC,KAAM,GAAG9E,EAAY,OAAO,EAAEW,CAAAA,EAAAA,EAAAA,aAAAA,EAC5BiB,GAAAA,EACE3B,EAAAA,CAAkB,CACtB8E,GAAG,SACH5E,YAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,EAAIA,GAPlCyB,IAUV,CAGHyD,mCAAoC,CAClC,GAAM,CAAElD,cAAY,CAAE,CAAG,IAAI,CAACrC,OAAO,CAC/B,OAAEU,CAAK,aAAEL,CAAW,CAAE,CAAG,IAAI,CAACJ,KAAK,CAEzC,MAAQoC,CAAAA,EAAawB,iBAAiB,EAAI,IACvCrI,MAAM,CACJgF,GACC,CAACA,EAAOI,GAAG,GAAKJ,CAAAA,CAAOa,uBAAuB,EAAIb,EAAO1E,QAAAA,GAE5DS,GAAG,CAAC,CAACuF,EAAmBzH,KACvB,GAAM,UACJ0I,CAAQ,UACRjH,CAAQ,yBACRuF,CAAuB,KACvBT,CAAG,CACH,GAAGqC,EACJ,CAAGnB,EACA0D,EAEU,GAad,OAXInE,GAA2BA,EAAwBC,MAAM,CAC3DkE,CAD6D,CACtDnE,EAAwBC,MAAM,CAC5BxF,IACT0J,EACsB,IAFH,MAEjB,OAAO1J,EACHA,EACAmF,MAAMC,OAAO,CAACpF,GACZA,EAASjC,IAAI,CAAC,IACd,IAIR,oBAAC2G,SAAAA,CACE,GAAGyC,CAAW,CACf5B,wBAAyB,CAAEC,OAAQkE,CAAK,EACxCpC,IAAKH,EAAYwC,EAAE,EAAIpL,EACvBqG,MAAOA,EACP2C,eAAa,oBACbhD,YACEA,QACCR,GAIT,EACJ,CAEA6B,GAP4C,cAO3BC,CAAoB,CAAE,CACrC,OAAOD,EAAiB,IAAI,CAAC1B,OAAO,CAAE,IAAI,CAACC,KAAK,CAAE0B,EACpD,CAEAS,mBAAoB,CAClB,OAAOA,EAAkB,IAAI,CAACpC,OAAO,CAAE,IAAI,CAACC,KAAK,CACnD,CAEAgC,WAAWN,CAAoB,CAAE,CAC/B,OAAOM,EAAW,IAAI,CAACjC,OAAO,CAAE,IAAI,CAACC,KAAK,CAAE0B,EAC9C,CAEA5B,oBAAqB,CACnB,OAAOA,EAAmB,IAAI,CAACC,OAAO,CAAE,IAAI,CAACC,KAAK,CACpD,CAEAyF,QAAS,CACP,GAAM,QACJ3E,CAAM,SACN4E,CAAO,WACPlG,CAAS,WACTmG,CAAS,eACTC,CAAa,eACbC,CAAa,iBACbC,CAAe,UACfC,CAAQ,oBACRC,CAAkB,oBAClBC,CAAkB,yBAClB9F,CAAuB,CACvB+D,aAAW,aACXjE,CAAW,kBACXiG,CAAgB,CACjB,CAAG,IAAI,CAACnG,OAAO,CAEVoG,GAA0C,IAAvBH,EACnBI,GACmB,IAAvBH,GAAgC,CAAC9F,EAEnC,IAAI,CAACJ,OAAO,CAACsG,qBAAqB,CAACtH,IAAI,EAAG,EAE1C,GAAI,MAAEuH,CAAI,CAAE,CAAG,IAAI,CAACvG,OAAO,CACvBwG,EAAkC,EAAE,CACpCC,EAAwC,EAAE,CAC1CF,IACFA,EADQ,OACI,CAAC,IAEThF,GACe,SAAfA,EAAM4B,IAAI,EACa,YAAvB5B,EAAMtB,KAAK,CAAC,GAAM,EACI,SACtB,CADAsB,EAAMtB,KAAK,CAAC,EAAK,CAEb,IAAI,CAACD,OAAO,CAAC0G,cAAc,CAC7BF,CAD+B,CACnB5J,IAAI,CACdkH,EAAAA,OAAK,CAAC6C,YAAY,CAACpF,EAAO,CAAE,iBAAkB,EAAG,IAGnDiF,EAAY5J,IAAI,CAAC2E,GAGfA,IACE,GADK,CACD,CAACvB,OAAO,CAAC0G,cAAc,CAC7BD,CAD+B,CACb7J,IAAI,CACpBkH,EAAAA,OAAK,CAAC6C,YAAY,CAACpF,EAAO,CAAE,iBAAkB,EAAG,IAGnDkF,EAAkB7J,IAAI,CAAC2E,GAI/B,GACAgF,EAAOC,EAAY5M,MAAM,CAAC6M,IAE5B,IAAI3K,EAA8BgI,EAAAA,OAAK,CAAC8C,QAAQ,CAACC,OAAO,CACtD,IAAI,CAAC5G,KAAK,CAACnE,QAAQ,EACnBN,MAAM,CAACC,SA4BLqL,GAAgB,EAChBC,EAAkB,GAGtBR,EAAOzC,EAAAA,OAAK,CAAC8C,QAAQ,CAACrK,GAAG,CAACgK,GAAQ,EAAE,CAAE,IACpC,GAAI,CAAChF,EAAO,OAAOA,EACnB,GAAM,MAAE4B,CAAI,OAAElD,CAAK,CAAE,CAAGsB,EACxB,GAA2C9B,CAAvCI,CAAkD,CACpD,GADqC,CACjCmH,EAAkB,GAwBtB,GAtBa,SAAT7D,GAAkC,YAAY,CAA3BlD,EAAMgH,IAAI,CAC/BD,EAAU,kBACQ,SAAT7D,GAAiC,aAAa,CAA3BlD,EAAM8E,GAAG,CACrCgC,GAAkB,EACA,UAAU,CAAnB5D,IAMNlD,EAAMW,GAAG,EAAsC,CAAC,EAAnCX,EAAMW,GAAG,CAACzE,OAAO,CAAC,eAC/B8D,EAAMoB,uBAAuB,EAC3B,IAAO8B,IAAI,EAAmB,oBAAflD,EAAMkD,IAAI,CAAK,CAAgB,EACjD,CACA6D,EAAU,UACVrM,OAAOoB,IAAI,CAACkE,GAAOrC,OAAO,CAAC,IACzBoJ,GAAW,CAAC,CAAC,EAAEE,EAAK,EAAE,EAAEjH,CAAK,CAACiH,EAAK,CAAC,CAAC,CAAC,GAExCF,GAAW,MAIXA,EAIF,OAJW,QACHtD,IAAI,CACV,CAAC,2BAA2B,EAAEnC,EAAM4B,IAAI,CAAC,wBAAwB,EAAE6D,EAAQ,IAAI,EAAElB,EAAcjH,IAAI,CAAC,sDAAsD,CAAC,EAEtJ,IAEX,KAEe,EAFR,OAEDsE,GAAiC,WAAW,CAAzBlD,EAAM8E,GAAG,GAC9B+B,GAAgB,GAGpB,OAAOvF,CAET,GAEA,IAAMI,EAAuBpC,EAC3B,IAAI,CAACS,OAAO,CAACR,aAAa,CAC1B,IAAI,CAACQ,OAAO,CAAC8F,aAAa,CAACjH,IAAI,CACQY,CAAvCI,EAGIsH,EAlZV,CA+YyC,QA/YhCC,CACqD,CAC5DrB,CAAuB,CACvB7F,EAAsB,EAAE,EAExB,GAAI,CAACiG,EACH,MAAO,CACLkB,SAFmB,EAEP,KACZC,QAAS,IACX,EAGF,IAAMC,EAAgBpB,EAAiBqB,KAAK,CAAC,QAAQ,CAC/CC,EAAiBtB,EAAiBqB,KAAK,CAACzB,EAAgB,CAExD2B,EAAqBzG,MAAMuD,IAAI,CACnC,IAAIlF,IAAI,IAAKiI,GAAiB,EAAE,IAAOE,GAAkB,EAAE,CAAE,GAS/D,MAAO,CACLJ,WAAYM,KAJXJ,CADkBxN,MAAM,EACxBwN,CAAAA,GAAiBE,CAAAA,CAAAA,CAAa,CAAC,EAK9B,MADUE,CACT7C,OAAAA,CACC8C,iBACEzB,EAAiB0B,oBAAoB,CAAG,cAAgB,GAE1D9C,IAAI,aACJC,KAAK,IACL3E,YAAY,cAEZ,KACJiH,QAASI,EACLA,EAAmBnL,GAAG,CAAC,IACrB,IAAMuL,EAAM,8BAA8BC,IAAI,CAACC,EAAU,CAAC,EAAE,CAC5D,MACE,UAAClD,OAAAA,CAECC,IAAI,UACJC,KAAM,GAAG9E,EAAY,OAAO,EAAEW,GAAAA,EAAAA,aAAAA,EAAcmH,GAAAA,CAAW,CACvD/C,GAAG,OACH9B,KAAM,CAAC,KAAK,EAAE2E,EAAAA,CAAK,CACnBzH,YAAY,YACZuH,iBAAgBI,EAASjG,QAAQ,CAAC,MAAQ,cAAgB,IANrDiG,EASX,GACA,IACN,CACF,EA8VM7B,EACAJ,EACA7F,GAQI+H,EAAiBC,CALCC,CAAAA,EAAAA,EAAAA,iBAAAA,EACtBC,CAAAA,EAAAA,EAAAA,SAAAA,IAAYC,uBAAuB,GACnC,IAAI,CAACrI,OAAO,CAACsI,+BAA+B,GAGJ,IAAI/L,GAAG,CAC/C,CAAC,KAAE6G,CAAG,OAAE1I,CAAK,CAAE,CAAEL,IACf,IADeA,CACf,KAACkO,KADclO,EACdkO,CAAsCtB,KAAM7D,EAAKoF,QAAS9N,GAAhD,CAAC,gBAAgB,EAAEL,EAAAA,CAAO,GAIzC,MACE,WAACkM,OAAAA,CAAM,GAnbb,SAASkC,CAAiC,EACxC,GAAM,aAAEpI,CAAW,OAAEK,CAAK,CAAE,GAAGgI,EAAW,CAAGzI,EAO7C,OAFIyI,CAGN,EA0aiC,IAAI,CAACzI,KAAK,CAAC,WACnC,IAAI,CAACD,OAAO,CAAC6B,aAAa,EACzB,iCACE,UAACL,QAAAA,CACCmH,qBAAmB,IACnBC,kBACyCnJ,CAAvCI,CACI,EAD+B,UAE/B5G,EAENoI,wBAAyB,CACvBC,OAAQ,CAAC,kBAAkB,CAAC,IAGhC,UAACuH,WAAAA,CACCF,qBAAmB,IACnBC,kBACE/I,CAAAA,CACI,EAD+B,KAE/B5G,gBAGN,UAACuI,QAAAA,CACCH,wBAAyB,CACvBC,OAAQ,CAAC,mBAAmB,CAAC,SAMtCiF,EACA,IAAI,CAACvG,OAAO,CAAC0G,cAAc,CAAG,KAC7B,UAAC6B,OAAAA,CACCtB,KAAK,kBACLuB,QAAS1E,EAAAA,OAAK,CAAC8C,QAAQ,CAACkC,KAAK,CAACvC,GAAQ,EAAE,EAAE1L,QAAQ,KAIrDiB,EAEAqL,EAAiBE,UAAU,CAC3BF,EAAiBG,OAAO,CAExBzH,GACC,CADkC,EAClC,OADsCJ,CACtC,sBACE,UAAC8I,OAAAA,CACCtB,KAAK,WACLuB,QAAQ,uDAET,CAACzB,GACA,UAACjC,MADDiC,CACCjC,CACCC,IAAI,KAFNgC,OAGE/B,KACEa,EACAkD,EAAAA,OAAAA,YAAAA,CAAuC,KAK7C,UAACjE,OAAAA,CACCC,IAAI,UACJE,GAAG,SACHD,KAAK,qCAEP,UAAClE,EAAAA,CAAUC,OAAQA,IACnB,UAACS,QAAAA,CACCwH,kBAAgB,GAChB3H,wBAAyB,CACvBC,OAAQ,CAAC,slBAAslB,CAAC,IAGpmB,UAACuH,WAAAA,UACC,UAACrH,QAAAA,CACCwH,kBAAgB,GAChB3H,wBAAyB,CACvBC,OAAQ,CAAC,kFAAkF,CAAC,MAIlG,UAACd,SAAAA,CAAOwB,KAAK,IAACpB,IAAI,wCAGrB,CAAEf,CAAAA,EACD,GADoC,EACpC,CADwCJ,CAAQ,EAChD,aADgD,WAE7C,CAACqH,GAAiBlB,GACjB,UADiBA,OAChBd,CACCC,GAFea,CAEX,UACJZ,KAAMa,GA9fbF,GAAW,GAAGsD,EAAAA,EAASA,EAAOlH,CA8fDmH,OA9fS,CAAC,KAAO,IAAM,IAAI,MAAK,IAigBzD,IAAI,CAAC3D,iCAAiC,GACtC,CAACpB,GAAe,IAAI,CAACgF,WAAW,CAACxH,GACjC,CAACwC,GAAe,UAAC0E,EAAhB1E,SAAgB0E,CAASO,CAAzBjF,YAAqC,IAAI,CAAClE,KAAK,CAACS,KAAK,EAAI,KAE1D,CAAC0F,GACA,CAACC,GACD,IAAI,CAACjB,uBAAuB,GAC7B,CAACgB,GACA,CAACC,GACD,IAAI,CAAChB,mBAAmB,CAAC1D,GAE1B,CAACvB,GACA,CAACgG,GACD,IAAI,CAACrG,kBAAkB,GAExB,CAACK,GACA,CAACgG,GACD,IAAI,CAAChE,iBAAiB,GACvB,CAAChC,GACA,CAACgG,GACD,IAAI,CAAC1E,gBAAgB,CAACC,GACvB,CAACvB,GACA,CAACgG,GACD,IAAI,CAACnE,UAAU,CAACN,GAEjBwC,GAAe,IAAI,CAACgF,WAAW,CAACxH,GAChCwC,GAAe,UAAC0E,EAAhB1E,SAAgB0E,CAASO,CAAzBjF,YAAqC,IAAI,CAAClE,KAAK,CAACS,KAAK,EAAI,KACzD,IAAI,CAACV,OAAO,CAAC6B,aAAa,EAIzB,EAHA,CAGA,OAACgH,WAAAA,CAASpD,GAAG,6BAEdwC,EACAlH,GAAU,CANiD,OAS/D+C,EAAAA,OAAK,CAACuF,aAAa,CAACvF,EAAAA,OAAK,CAACwF,QAAQ,CAAE,CAAC,KAAOtD,GAAY,EAAE,IAGjE,CACF,CA2DO,MAAM7G,UAAmB2E,EAAAA,OAAK,CAACC,SAAS,gBACtCC,WAAAA,CAAcC,EAAAA,WAAW,CAIhCvC,iBAAiBC,CAAoB,CAAE,CACrC,OAAOD,EAAiB,IAAI,CAAC1B,OAAO,CAAE,IAAI,CAACC,KAAK,CAAE0B,EACpD,CAEAS,mBAAoB,CAClB,OAAOA,EAAkB,IAAI,CAACpC,OAAO,CAAE,IAAI,CAACC,KAAK,CACnD,CAEAgC,WAAWN,CAAoB,CAAE,CAC/B,OAAOM,EAAW,IAAI,CAACjC,OAAO,CAAE,IAAI,CAACC,KAAK,CAAE0B,EAC9C,CAEA5B,oBAAqB,CACnB,OAAOA,EAAmB,IAAI,CAACC,OAAO,CAAE,IAAI,CAACC,KAAK,CACpD,CAEA,OAAOsJ,sBAAsBvJ,CAA4B,CAAU,CACjE,GAAM,eAAE8F,CAAa,oBAAE0D,CAAkB,CAAE,CAAGxJ,EAC9C,GAAI,CACF,IAAMyJ,EAAOC,KAAKC,SAAS,CAAC7D,GAE5B,GAAIzG,EAAsBvB,GAAG,CAACgI,EAAcjH,IAAI,EAC9C,CADiD,KAC1C+K,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBH,GAG9B,IAAMI,EAGAC,MAFJjK,CAEW2E,IAAI,CAFoB,GAC/B,CAAgD,SACpB,CAC5BuF,EAAchB,EAAAA,OAAAA,CAAAA,CAAsC,CAAtCA,MAEhBS,GAAsBK,EAAQL,IAE9BnK,EAAsB2K,GAAG,CAAClE,EAAcjH,IAAI,EAG9C4E,EALoD,MAK5CC,IAAI,CACV,CAAC,wBAAwB,EAAEoC,EAAcjH,IAAI,CAAC,CAAC,EAC7CiH,EAAcjH,IAAI,GAAKmB,EAAQ+F,eAAe,CAC1C,GACA,CAAC,QAAQ,EAAE/F,EAAQ+F,eAAe,CAAC,EAAE,CAAC,CAC3C,IAAI,EAAEgE,EACLF,GACA,gCAAgC,EAAEE,EAClCP,GACA;AAAA,oEAAmH,CAAC,GAInHI,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBH,EAC9B,CAAE,MAAOnG,EAAK,CACZ,GAAIC,CAAAA,EAAAA,EAAAA,OAAAA,EAAQD,IAAsD,CAAC,GAAG,CAAlDA,EAAIK,OAAO,CAACxH,OAAO,CAAC,sBACtC,MAAM,qBAEL,CAFK,MACJ,CAAC,wDAAwD,EAAE2J,EAAcjH,IAAI,CAAC,sDAAsD,CAAC,EADjI,+DAEN,EAEF,OAAMyE,CACR,CACF,CAEAoC,QAAS,CACP,GAAM,aACJxF,CAAW,WACXT,CAAS,eACTD,CAAa,oBACbyG,CAAkB,uBAClBK,CAAqB,CACrBnG,kBAAgB,yBAChBC,CAAuB,aACvBC,CAAW,CACZ,CAAG,IAAI,CAACL,OAAO,CACVoG,GAA0C,IAAvBH,EAIzB,GAFAK,CAEIzG,CAFkBV,IAEiB,MAFP,EAAG,EAEQM,EAEvC,OAAO,EAF2C,GA8CtD,IAAMkC,EAAuBpC,EAC3B,IAAI,CAACS,OAAO,CAACR,aAAa,CAC1B,IAAI,CAACQ,OAAO,CAAC8F,aAAa,CAACjH,IAAI,CACQY,CAAvCI,EAGF,GAHqC,GAInC,iCACG,CAACuG,GAAoB5G,EAAcyK,QAAQ,CACxCzK,EAAcyK,QAAQ,CAAC1N,GAAG,CAAC,GACzB,IAD0BuF,CAC1B,KAACtB,KADyBsB,IACzBtB,CAECI,IAAK,GAAGV,EAAY,OAAO,EAAEW,GAAAA,EAAAA,aAAa,EACxCiB,GAAAA,EACE3B,EAAAA,CAAkB,CACtBO,MAAO,IAAI,CAACT,KAAK,CAACS,KAAK,CACvBL,YAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,EAAIA,GALlCyB,IAQT,KACHsE,EAAmB,KAClB,UAAC5F,GADiB,MACjBA,CACCiF,GAAG,gBACHtC,KAAK,mBACLzC,MAAO,IAAI,CAACT,KAAK,CAACS,KAAK,CACvBL,YAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,EAAIA,EACvCgB,wBAAyB,CACvBC,OAAQnC,EAAWoK,qBAAqB,CAAC,IAAI,CAACvJ,OAAO,CACvD,IAGHI,GACC,CAACgG,GACD,IAAI,CAACrG,kBAAkB,GACxBK,GACC,CAACgG,GACD,IAAI,CAAChE,iBAAiB,GACvBhC,GACC,CAACgG,GACD,IAAI,CAAC1E,gBAAgB,CAACC,GACvBvB,GAA2B,CAACgG,GAAoB,IAAI,CAACnE,UAAU,CAACN,KAGvE,CACF,CAEO,SAAS1C,EACdgB,CAGC,EAED,GAAM,WACJR,CAAS,uBACT6G,CAAqB,QACrB4D,CAAM,cACN7H,CAAY,eACZyD,CAAa,CACd,CAAGqE,CAAAA,EAAAA,EAAAA,cAAAA,IAKJ,OAHA7D,EAAsBrH,IAAI,EAAG,GAC7BmL,SApPOA,CACoC,CAC3CtE,CAAwB,CACxB7F,CAAU,MAUWnE,EAAAA,EAGAA,EAAAA,EAXrB,GAAI,CAACmE,EAAMnE,QAAQ,CAAE,OAErB,IAAMuO,EAAmC,EAAE,CAErCvO,EAAWmF,MAAMC,OAAO,CAACjB,EAAMnE,QAAQ,EACzCmE,EAAMnE,QAAQ,CACd,CAACmE,EAAMnE,QAAQ,CAAC,CAEdwO,EAC0CtL,OAAAA,EAD3BlD,EAAS3C,CACkB6F,CAD1CsL,EAA4B,CAChC,GAA+B/I,EAAM4B,IAAI,GAAKnE,EAAAA,CAAAA,EAAAA,OAD3BlD,EAAAA,EAElBmE,KAAAA,EAAK,OAFanE,EAEXA,QAAQ,CACZyO,EAC0C,MAD3BzO,GAAAA,EAAS3C,EAAxBoR,EAA4B,CAChC,GAA8C,SAAfhJ,EAAM4B,IAAI,CAAK,GACxC,OAFarH,EAAAA,EAElBmE,KAAAA,EAAK,OAFanE,EAEXA,QAAQ,CAGZ0O,EAAmB,IACnBvJ,MAAMC,OAAO,CAACoJ,GAAgBA,EAAe,CAACA,EAAa,IAC3DrJ,MAAMC,OAAO,CAACqJ,GAAgBA,EAAe,CAACA,EAAa,CAChE,CAEDzG,EAAAA,OAAK,CAAC8C,QAAQ,CAAChJ,OAAO,CAAC4M,EAAkB,QAInCjJ,EAHJ,GAAKA,CAAD,EAGJ,IAHY,IAGRA,EAAAA,EAAM4B,IAAAA,EAAI,OAAV5B,EAAYkJ,YAAY,EAAE,CAC5B,GAA6B,sBAAzBlJ,EAAMtB,KAAK,CAAC8C,QAAQ,CAA0B,CAChDV,EAAawB,iBAAiB,CAC5BxB,GAAawB,iBAAiB,EAAI,IAClCjK,MAAM,CAAC,CACP,CACE,GAAG2H,EAAMtB,KAAK,EAEjB,EACD,MACF,MAAO,GACL,CAAC,aAAc,mBAAoB,SAAS,CAAC8B,QAAQ,CACnDR,EAAMtB,KAAK,CAAC8C,QAAQ,EAEtB,YACAsH,EAAkBzN,IAAI,CAAC2E,EAAMtB,KAAK,OAE7B,GAAI,KAAgC,IAAzBsB,EAAMtB,KAAK,CAAC8C,QAAQ,CAAkB,YACtDsH,EAAkBzN,IAAI,CAAC,CAAE,GAAG2E,EAAMtB,KAAK,CAAE8C,SAAU,kBAAmB,EAExE,CAEJ,GAEA+C,EAAczD,YAAY,CAAGgI,CAC/B,EA6LkChI,EAAcyD,EAAe7F,GAG3D,UAACuF,OAAAA,CACE,GAAGvF,CAAK,CACTyK,KAAMzK,EAAMyK,IAAI,EAAIR,QAAUjR,EAC9B0R,IAAK9K,CAAAA,CAAmD,EAAhB,MAAqB5G,EAC7D2P,mBACE/I,IAII5G,CAJ+B,EAQ3C,CAEO,CATCwG,QASQP,IACd,CATMW,EASA,MATQ+K,GAAG,CAACC,KAAa,GAAL,KASlBvE,CAAqB,CAAE,CAAG6D,CAAAA,EAAAA,CARxB,CAQwBA,cAAAA,IAGlC,OAFA7D,EAAsBpH,IAAI,EAAG,EAEtB,UAAC4L,sCAAAA,CAAAA,EACV,CAMe,MAAM1L,UAAyB0E,EAAAA,OAAK,CAACC,SAAS,CAO3D,OAAOgH,gBAAgBC,CAAoB,CAAiC,CAC1E,OAAOA,EAAIC,sBAAsB,CAACD,EACpC,CAEAtF,QAAS,CACP,MACE,WAACzG,EAAAA,WACC,UAACD,EAAAA,CAAAA,GACD,WAACkM,OAAAA,WACC,UAAChM,EAAAA,CAAAA,GACD,UAACC,EAAAA,CAAAA,QAIT,CACF,CAgBEC,CAAgB,CAAC+L,EAAAA,qBAAqB,CAAC,CAXvC,EAW0CC,OAXjCA,EACP,MACE,WAACnM,EAAAA,WACC,UAACD,EAAAA,CAAAA,GACD,WAACkM,OAAAA,WACC,UAAChM,EAAAA,CAAAA,GACD,UAACC,EAAAA,CAAAA,QAIT,gCC3rCU,kBAAgF,6BCVrF,SAASjF,EAAmBtB,CAAY,EAC7C,OAAOA,EAAKS,UAAU,CAAC,KAAOT,EAAQ,IAAGA,CAC3C,+FAFgBsB,qCAAAA,6HCcA6E,qCAAAA,aAfT,OAGDsM,EAAa,gCAGbC,EAAoB,sBASnB,SAASvM,EAAe9E,CAAa,CAAEsR,CAAsB,QAKlE,CAL4CA,KAAAA,IAAAA,IAAAA,GAAkB,GAC1DvS,CAAAA,EAAAA,EAAAA,0BAAAA,EAA2BiB,KAC7BA,EAAQlB,CAD6B,EAC7BA,EAAAA,mCAAAA,EAAoCkB,GAAOR,gBAAgB,EAGjE8R,GACKD,EAAkBE,GADf,CACmB,CAACvR,GAGzBoR,EAAWG,IAAI,CAACvR,EACzB,uKC5B0BiB,qBAAqB,mBAArBA,EAAAA,qBAAqB,EAAtCC,eAAe,mBAAfA,EAAAA,eAAe,EACf4D,cAAc,mBAAdA,EAAAA,cAAc,YADgC,WACxB,4KCkClB0M,kBAAkB,mBAAlBA,GAiDAC,oBAAoB,mBAApBA,GApDAC,kBAAkB,mBAAlBA,GACAC,wBAAwB,mBAAxBA,GA8BAC,0BAA0B,mBAA1BA,GALAC,aAAa,mBAAbA,GADAC,aAAa,mBAAbA,GAvBAC,cAAc,mBAAdA,GAyBAC,wBAAwB,mBAAxBA,GAOAC,yBAAyB,mBAAzBA,GANAC,wBAAwB,mBAAxBA,GA4BAC,+BAA+B,mBAA/BA,GAPAC,gCAAgC,mBAAhCA,GACAC,oCAAoC,mBAApCA,GAUAC,qCAAqC,mBAArCA,IACAC,4CAA4C,mBAA5CA,IAPAC,yCAAyC,mBAAzCA,GAIAC,mCAAmC,mBAAnCA,IA5EAC,gBAAgB,mBAAhBA,GARAC,cAAc,mBAAdA,GA8CAC,YAAY,mBAAZA,GA4CAC,uBAAuB,mBAAvBA,IAUAC,uBAAuB,mBAAvBA,IANAC,kBAAkB,mBAAlBA,IAnDAC,8BAA8B,mBAA9BA,GAJAC,yBAAyB,mBAAzBA,GAiCAC,oBAAoB,mBAApBA,GAmBAC,oBAAoB,mBAApBA,IA6BAC,0BAA0B,mBAA1BA,IAtFAC,aAAa,mBAAbA,GADAC,aAAa,mBAAbA,GAHAC,yBAAyB,mBAAzBA,GAOAC,eAAe,mBAAfA,GAgCAC,mCAAmC,mBAAnCA,GALAC,yBAAyB,mBAAzBA,GAxBAC,mBAAmB,mBAAnBA,GA0BAC,kCAAkC,mBAAlCA,GAtEJlP,0BAA0B,mBAA1BA,EAAAA,OAA0B,EA4DtBwM,qBAAqB,mBAArBA,GAxBA2C,kBAAkB,mBAAlBA,GARAC,cAAc,mBAAdA,GAHAC,wBAAwB,mBAAxBA,GAHAC,YAAY,mBAAZA,GAKAC,UAAU,mBAAVA,GAJAC,sBAAsB,mBAAtBA,GACAC,uBAAuB,mBAAvBA,GAEAC,UAAU,mBAAVA,GAaAC,kBAAkB,mBAAlBA,GASAC,uBAAuB,mBAAvBA,GARAC,eAAe,mBAAfA,GA2EAC,gBAAgB,mBAAhBA,IAlEAC,gBAAgB,mBAAhBA,GAPAC,qBAAqB,mBAArBA,GAuDAC,eAAe,mBAAfA,IA/BAC,yBAAyB,mBAAzBA,GA8BAC,eAAe,mBAAfA,IAcAC,mBAAmB,mBAAnBA,IAnDAC,0BAA0B,mBAA1BA,GAxBAC,8BAA8B,mBAA9BA,GA4GAC,kBAAkB,mBAAlBA,IAhCAC,oBAAoB,mBAApBA,IAlEAC,oCAAoC,mBAApCA,GAoEAC,gCAAgC,mBAAhCA,IA7FAC,0BAA0B,mBAA1BA,GACAC,gCAAgC,mBAAhCA,GAQAC,aAAa,mBAAbA,wBA/B0B,QAM1B5C,EAAiB,CAC5B6C,OAAQ,SACRC,OAAQ,SACRC,WAAY,aACd,EAIahD,EAET,CACF,CAACC,EAAe6C,MAAM,CAAC,CAAE,EACzB,CAAC7C,EAAe8C,MAAM,CAAC,CAAE,EACzB,CAAC9C,EAAe+C,UAAU,CAAC,CAAE,CAC/B,EAEaL,EAA6B,cAC7BC,EAAoC,GAAED,EAA2B,QACjErB,EAAe,eACfE,EAAyB,yBACzBC,EAA0B,0BAC1BJ,EAA2B,2BAC3BK,EAAa,aACbH,EAAa,aACbH,EAAiB,sBACjByB,EAAgB,qBAChB7D,EAAqB,0BACrBC,EAA2B,gCAC3BI,EAAiB,sBACjBP,EAAqB,0BACrB+B,EAA4B,iCAC5ByB,EAAiC,iCACjCnB,EAAqB,qBACrBP,EAAgB,qBAChBD,EAAgB,qBAChBgB,EAAqB,0BACrBE,EAAkB,uBAClBf,EAAkB,uBAClBkB,EAAwB,6BACxBzB,EAA4B,yBAC5BU,EAAsB,2BACtBwB,EACX,iCACWnC,EAAiC,8BACjCsB,EAA0B,+BAC1BG,EAAmB,SACnB7B,EAAe,CAC1B,iBACA,kBACA,iBACD,CACYd,EAAgB,WAChBD,EAAgB,CAAC,aAAc,QAAS,UAAU,CAClDG,EAA2B,SAC3BE,EAA2B,SAC3B6C,EAA6B,4BAC7B7D,EAAwB,4BACxBU,EAA6B,sBAG7BK,EAA4B,4BAE5B2C,EAA4B,4BAE5BlB,EAA4B,4BAE5BE,EACX,qCAEWH,EACX,sCAEWP,EAAuB,uBAGvBd,EAAoC,OACpCC,EAAwC,GAAED,EAAiC,OAE3EX,EAAuB,sBAEvBe,EAA6C,gBAE7CL,EAAmC,MAEnCM,GAAuC,UAEvCH,GAAwC,YACxCC,GAA+CoD,OAC1DrD,IAEWO,GAA0B,kBAC1BM,GAAuB,uBACvB0B,GAAkB,UAClBF,GAAkB,UAClB5B,GAAqB,CAChC/F,KAAM,kBACN4I,cAAe,IACfC,WAAY,kBACZC,WAAY,IACd,EACahD,GAA0B,CACrC9F,KAAM,QACN4I,cAAe,IACfC,WAAY,kBACZC,WAAY,IACd,EACahB,GAAsB,CAAC,OAAO,CAC9BI,GAAuB,EAEvBE,GAAmC,IAEnCZ,GAAmB,CAC9BgB,OAAQ,SACRC,OAAQ,QACV,EAMarC,GAA6B,CACxC,iBACA,eACA,mBACA,4BACA,oBACA,uBACA,sBACA,eACA,iBACA,eACA,cACA,+BACA,4BACA,kCACA,mCACA,kCACD,CAEY6B,GAAqB,IAAI5P,IAAY,CAChD+M,EACAI,EACAL,EACAE,EACD,qPCtJM,SAASzL,EAAciB,CAAY,EACxC,OAAOA,EACJ5I,KAAK,CAAC,KACNqD,GAAG,CAAC,GAAOyT,mBAAmBC,IAC9BpW,IAAI,CAAC,IACV,0FALgBgH,qCAAAA,iCCET,SAAStG,EAAenB,CAAe,EAE5C,MAAsB,MAAfA,CAAO,CAAC,EAAE,EAAYA,EAAQgE,QAAQ,CAAC,IAChD,CAEO,SAAS8S,EAAuB9W,CAAe,EACpD,OAAOA,EAAQC,UAAU,CAAC,MAAoB,cAAZD,CACpC,CAEO,SAAS+W,EACd/W,CAAgB,CAChBgX,CAA2D,EAI3D,GAFsBhX,CAElBiX,CAF0BtO,QAAQ,CAACuO,GAEpB,CACjB,IAAMC,EAAmB7G,KAAKC,SAAS,CAACyG,GACxC,MAA4B,OAArBG,EACHD,EAAmB,IAAMC,EACzBD,CACN,CAEA,OAAOlX,CACT,wIAGaoX,mBAAmB,mBAAnBA,GADAF,gBAAgB,mBAAhBA,GAhBGH,4BAA4B,mBAA5BA,GATA5V,cAAc,mBAAdA,GAKA2V,sBAAsB,mBAAtBA,KAoBT,IAAMI,EAAmB,WACnBE,EAAsB,0CCrBnC,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAiBF,cACA,0CACA,cACA,SACK,CACL,EACA,GACA,yBACA,QACA,CAAK,CACL,qCACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,8BACA,QACA,CAAK,CACL,4BACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,8BACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,oCACA,QACA,CAAK,CACL,oBACA,QACA,CAAK,CACL,sBACA,QACA,CAAK,CACL,+BACA,QACA,CAAK,CACL,sBACA,QACA,CAAK,CACL,2BACA,QACA,CACA,CAAC,EACD,kBAaA,OAZA,2CACA,uBACA,yBACA,2CACA,6BACA,6EACA,iDACA,yCACA,uCACA,2DACA,mDACA,mCACA,CACA,CAAC,MAAqB,EACtB,cAGA,OAFA,yEACA,iDACA,CACA,CAAC,MAAyB,EAC1B,cAKA,OAJA,mDACA,mCACA,+DACA,2CACA,CACA,CAAC,MAAqB,EACtB,cAgCA,OA/BA,2CACA,yCACA,2DACA,iEACA,+DACA,6DACA,iEACA,6DACA,iEACA,qDACA,6CACA,iCACA,iCACA,yCACA,iDACA,2CACA,uDACA,yDACA,mDACA,yEACA,uDACA,6CACA,2CACA,uDACA,uCACA,+CAEA,gBACA,0BACA,4BACA,gCACA,CACA,CAAC,MAAyB,EAC1B,cAEA,OADA,wCACA,CACA,CAAC,MAAsB,EACvB,cAMA,OALA,iDACA,yCACA,yCACA,yCACA,6CACA,CACA,CAAC,MAAiB,EAClB,cAKA,OAJA,4CACA,4DACA,0CACA,0BACA,CACA,CAAC,MAAoB,EACrB,cAEA,OADA,qCACA,CACA,CAAC,MAAiB,EAClB,cAEA,OADA,+BACA,CACA,CAAC,MAAe,EAChB,cAEA,OADA,gDACA,CACA,CAAC,MAAgC,EACjC,cAGA,OAFA,sDACA,sDACA,CACA,CAAC,MAA0B,EAC3B,cAEA,OADA,+BACA,CACA,CAAC,MAAqB,EACtB,OACA,qBACA,2BACA,4BACA,wBACA,kBACA,0BACA,wBACA,kBACA,mCACA,mCACA,mCACA,qCACA,oCACA,uCACA,+BACA,wCACA,CACA,GACA,oCACA,qCACA,wCACA,4HC1LgBC,qCAAAA,aAbmB,WACJ,WACA,OAWxB,SAASA,EAAkB5R,CAAY,EAC5C,IAAM6R,EACJ,iBAAiBlF,IAAI,CAAC3M,IAAS,CAACE,GAAAA,EAAAA,cAAAA,EAAeF,GAC1C,SAAQA,EACA,MAATA,EACE,SACA3E,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmB2E,EAEc,EACvC,GAAM,OAAE8R,CAAK,CAAE,CAAG5H,EAAQ,KAAM,EAC1B6H,EAAeD,EAAME,MADF9H,GACW,CAAC2H,GACrC,GAAIE,IAAiBF,EACnB,MAAM,IADyB,EACrBI,cAAc,CACrB,yCAAwCJ,EAAW,IAAGE,EAG7D,CAEA,OAAOF,CACT,8BCtBA,gBACA,KACA,sBAA+B,EAAK,iBACpC,CAZA,qCAA6C,CAC7C,QACA,CAAC,EAAC,OACF,qCAAoD,CACpD,cACA,eACA,QACA,CACA,CAAC,EAAC,mKC2ZWK,WAAW,mBAAXA,GAoBAC,uBAAuB,mBAAvBA,GAPAC,iBAAiB,mBAAjBA,GAZAH,cAAc,mBAAdA,GACAI,iBAAiB,mBAAjBA,GATAC,EAAE,mBAAFA,GACAC,EAAE,mBAAFA,GAlXAC,UAAU,mBAAVA,GAsQGC,QAAQ,mBAARA,GA+BAC,cAAc,mBAAdA,GAXAC,iBAAiB,mBAAjBA,GAKAC,MAAM,mBAANA,GAPHC,aAAa,mBAAbA,GAmBGC,SAAS,mBAATA,GAkBMC,mBAAmB,mBAAnBA,GAdNC,wBAAwB,mBAAxBA,GA+GAC,cAAc,mBAAdA,KA9ZT,IAAMT,EAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,OAAO,CAsQ9D,SAASC,EACdS,CAAK,EAEL,IACIC,EADAC,GAAO,EAGX,OAAQ,sCAAIC,EAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAKV,OAJKD,IACHA,EADS,CACF,EACPD,EAASD,KAAMG,IAEVF,CACT,CACF,CAIA,IAAMG,EAAqB,6BACdT,EAAgB,GAAiBS,EAAmB3G,IAAI,CAAChR,GAE/D,SAASgX,IACd,GAAM,UAAEY,CAAQ,UAAEC,CAAQ,MAAEC,CAAI,CAAE,CAAGC,OAAOC,QAAQ,CACpD,OAAUJ,EAAS,KAAIC,GAAWC,EAAO,IAAMA,EAAbA,EAAoB,CACxD,CADyD,SAGzCb,IACd,GAAM,CAAEzM,MAAI,CAAE,CAAGuN,OAAOC,QAAQ,CAC1BC,EAASjB,IACf,OAAOxM,EAAKxH,SAAS,CAACiV,EAAO1Y,MAAM,CACrC,CAEO,SAASwX,EAAkBxN,CAA2B,EAC3D,MAA4B,UAArB,OAAOA,EACVA,EACAA,EAAU2O,WAAW,EAAI3O,EAAUkD,IAAI,EAAI,SACjD,CAEO,SAAS0K,EAAUgB,CAAmB,EAC3C,OAAOA,EAAIC,QAAQ,EAAID,EAAIE,WAAW,CAGjC,SAAShB,EAAyBrX,CAAW,EAClD,IAAMsY,EAAWtY,EAAItB,KAAK,CAAC,KAG3B,OAFmB4Z,CAAQ,CAAC,EAAE,CAMzBja,MAFD,CAEQ,CAAC,MAAO,KACfA,OAAO,CAAC,SAAU,MACpBia,CAAAA,CAAS,EAAE,CAAI,IAAGA,EAASnZ,KAAK,CAAC,GAAGE,IAAI,CAAC,KAAS,GAEvD,CAFwD,eAIlC+X,EAIpBmB,CAAgC,CAAE/H,CAAM,EAUxC,IAAM2H,EAAM3H,EAAI2H,GAAG,EAAK3H,EAAIA,GAAG,EAAIA,EAAIA,GAAG,CAAC2H,GAAG,CAE9C,GAAI,CAACI,EAAIhI,eAAe,EAAE,MACxB,EAAQC,GAAG,EAAIA,EAAIjH,SAAS,CAEnB,CAFqB,UAGf,MAAM6N,EAAoB5G,EAAIjH,SAAS,CAAEiH,EAAIA,GAAG,CAC7D,EAEK,CAAC,EAGV,IAAM/K,EAAQ,MAAM8S,EAAIhI,eAAe,CAACC,GAExC,GAAI2H,GAAOhB,EAAUgB,GACnB,GADyB,IAClB1S,EAGT,GAAI,CAACA,EAIH,KAJU,CAIJ,qBAAkB,CAAlB,MAHW,IAAGsR,EAClBwB,GACA,+DAA8D9S,EAAM,cAChE,+DAAiB,GAazB,OAAOA,CACT,CAEO,IAAMkR,EAA4B,aAAvB,OAAO6B,YACZ5B,EACXD,GACC,CAAC,OAAQ,UAAW,mBAAmB,CAAW8B,KAAK,CACtD,GAA2C,YAA/B,OAAOD,WAAW,CAACE,EAAO,CAGnC,OAAMnC,UAAoBxT,MAAO,CACjC,MAAMuT,UAAuBvT,MAAO,CACpC,MAAM2T,UAA0B3T,MAGrC4V,YAAYtU,CAAY,CAAE,CACxB,KAAK,GACL,IAAI,CAAC2E,IAAI,CAAG,SACZ,IAAI,CAACyD,IAAI,CAAG,oBACZ,IAAI,CAACtD,OAAO,CAAI,gCAA+B9E,CACjD,CACF,CAEO,MAAMoS,UAA0B1T,MACrC4V,YAAYtU,CAAY,CAAE8E,CAAe,CAAE,CACzC,KAAK,GACL,IAAI,CAACA,OAAO,CAAI,wCAAuC9E,EAAK,IAAG8E,CACjE,CACF,CAEO,MAAMqN,UAAgCzT,MAE3C4V,aAAc,CACZ,KAAK,GACL,IAAI,CAAC3P,IAAI,CAAG,SACZ,IAAI,CAACG,OAAO,CAAI,mCAClB,CACF,CAWO,SAASmO,EAAesB,CAAY,EACzC,OAAO1J,KAAKC,SAAS,CAAC,CAAEhG,QAASyP,EAAMzP,OAAO,CAAE0P,MAAOD,EAAMC,KAAK,EACpE,8BC9bA,4BAA0C,CAC1C,cACA,eACA,QACA,CACA,CAAC,EACD,OACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,CAMA,UACA,QAMA,MALA,mBACA,sBACM,QACN,uBAEA,CACA,EACA,gBACA,uBACA,uEAAmF,SAAc,IAAI,EAAO,wBAC5G,aACA,cACA,eACA,CAAS,EAGT,GADA,mBAA8B,KAC9B,cACA,aAEA,UACA,wBAIA,GAHA,GACA,OAEA,IAEA,SADA,cACA,KAEA,uDAIA,SAFA,EADA,6CACA,UAEA,IADA,oCCpEA,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAMF,cACA,0CACA,cACA,SACK,CACL,EACA,GACA,wBACA,QACA,CAAK,CACL,oBACA,QACA,CAAK,CACL,yBACA,QACA,CACA,CAAC,EACD,MAAmB,EAAQ,KAAyB,EACpD,UAD0B,CAC1B,GACA,kCACA,CACA,cAQA,OAPA,iCACA,4CAEA,gCACA,wCAEA,qBAEA,CACA,sBAGA,IAFA,EAMA,IAJA,IAEA,IAKA,aACA,iBACA,OAIA,eAGA,SACA,cAKA,iBAEA,CACA,sBAGA,IACA,OAKA,aAEA,aACA,IACA,kBAEA,CACA", "sources": ["webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/lib/trace/tracer.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/get-page-files.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/compiled/@opentelemetry/api/index.js", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/is-thenable.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/route-modules/pages/vendored/contexts/html-context.js", "webpack://next-shadcn-dashboard-starter/../../../../src/shared/lib/page-path/normalize-path-sep.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/lib/is-error.js", "webpack://next-shadcn-dashboard-starter/../../../../../src/shared/lib/router/utils/interception-routes.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/shared/lib/router/utils/app-paths.ts", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/is-plain-object.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/shared/lib/router/utils/sorted-routes.ts", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/modern-browserslist-target.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/cjs/_interop_require_default.cjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/htmlescape.js", "webpack://next-shadcn-dashboard-starter/../../../../src/shared/lib/page-path/denormalize-page-path.ts", "webpack://next-shadcn-dashboard-starter/../../src/pages/_document.tsx", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/route-modules/pages/module.compiled.js", "webpack://next-shadcn-dashboard-starter/../../../../src/shared/lib/page-path/ensure-leading-slash.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/shared/lib/router/utils/is-dynamic.ts", "webpack://next-shadcn-dashboard-starter/../../../../../src/shared/lib/router/utils/index.ts", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/constants.ts", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/encode-uri-path.ts", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/segment.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/lib/trace/constants.js", "webpack://next-shadcn-dashboard-starter/../../../../src/shared/lib/page-path/normalize-page-path.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/lib/trace/utils.js", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/utils.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/lib/pretty-bytes.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/utils.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    BubbledError: null,\n    SpanKind: null,\n    SpanStatusCode: null,\n    getTracer: null,\n    isBubbledError: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    BubbledError: function() {\n        return BubbledError;\n    },\n    SpanKind: function() {\n        return SpanKind;\n    },\n    SpanStatusCode: function() {\n        return SpanStatusCode;\n    },\n    getTracer: function() {\n        return getTracer;\n    },\n    isBubbledError: function() {\n        return isBubbledError;\n    }\n});\nconst _constants = require(\"./constants\");\nconst _isthenable = require(\"../../../shared/lib/is-thenable\");\nlet api;\n// we want to allow users to use their own version of @opentelemetry/api if they\n// want to, so we try to require it first, and if it fails we fall back to the\n// version that is bundled with Next.js\n// this is because @opentelemetry/api has to be synced with the version of\n// @opentelemetry/tracing that is used, and we don't want to force users to use\n// the version that is bundled with Next.js.\n// the API is ~stable, so this should be fine\nif (process.env.NEXT_RUNTIME === 'edge') {\n    api = require('@opentelemetry/api');\n} else {\n    try {\n        api = require('@opentelemetry/api');\n    } catch (err) {\n        api = require('next/dist/compiled/@opentelemetry/api');\n    }\n}\nconst { context, propagation, trace, SpanStatusCode, SpanKind, ROOT_CONTEXT } = api;\nclass BubbledError extends Error {\n    constructor(bubble, result){\n        super(), this.bubble = bubble, this.result = result;\n    }\n}\nfunction isBubbledError(error) {\n    if (typeof error !== 'object' || error === null) return false;\n    return error instanceof BubbledError;\n}\nconst closeSpanWithError = (span, error)=>{\n    if (isBubbledError(error) && error.bubble) {\n        span.setAttribute('next.bubble', true);\n    } else {\n        if (error) {\n            span.recordException(error);\n        }\n        span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message: error == null ? void 0 : error.message\n        });\n    }\n    span.end();\n};\n/** we use this map to propagate attributes from nested spans to the top span */ const rootSpanAttributesStore = new Map();\nconst rootSpanIdKey = api.createContextKey('next.rootSpanId');\nlet lastSpanId = 0;\nconst getSpanId = ()=>lastSpanId++;\nconst clientTraceDataSetter = {\n    set (carrier, key, value) {\n        carrier.push({\n            key,\n            value\n        });\n    }\n};\nclass NextTracerImpl {\n    /**\n   * Returns an instance to the trace with configured name.\n   * Since wrap / trace can be defined in any place prior to actual trace subscriber initialization,\n   * This should be lazily evaluated.\n   */ getTracerInstance() {\n        return trace.getTracer('next.js', '0.0.1');\n    }\n    getContext() {\n        return context;\n    }\n    getTracePropagationData() {\n        const activeContext = context.active();\n        const entries = [];\n        propagation.inject(activeContext, entries, clientTraceDataSetter);\n        return entries;\n    }\n    getActiveScopeSpan() {\n        return trace.getSpan(context == null ? void 0 : context.active());\n    }\n    withPropagatedContext(carrier, fn, getter) {\n        const activeContext = context.active();\n        if (trace.getSpanContext(activeContext)) {\n            // Active span is already set, too late to propagate.\n            return fn();\n        }\n        const remoteContext = propagation.extract(activeContext, carrier, getter);\n        return context.with(remoteContext, fn);\n    }\n    trace(...args) {\n        var _trace_getSpanContext;\n        const [type, fnOrOptions, fnOrEmpty] = args;\n        // coerce options form overload\n        const { fn, options } = typeof fnOrOptions === 'function' ? {\n            fn: fnOrOptions,\n            options: {}\n        } : {\n            fn: fnOrEmpty,\n            options: {\n                ...fnOrOptions\n            }\n        };\n        const spanName = options.spanName ?? type;\n        if (!_constants.NextVanillaSpanAllowlist.includes(type) && process.env.NEXT_OTEL_VERBOSE !== '1' || options.hideSpan) {\n            return fn();\n        }\n        // Trying to get active scoped span to assign parent. If option specifies parent span manually, will try to use it.\n        let spanContext = this.getSpanContext((options == null ? void 0 : options.parentSpan) ?? this.getActiveScopeSpan());\n        let isRootSpan = false;\n        if (!spanContext) {\n            spanContext = (context == null ? void 0 : context.active()) ?? ROOT_CONTEXT;\n            isRootSpan = true;\n        } else if ((_trace_getSpanContext = trace.getSpanContext(spanContext)) == null ? void 0 : _trace_getSpanContext.isRemote) {\n            isRootSpan = true;\n        }\n        const spanId = getSpanId();\n        options.attributes = {\n            'next.span_name': spanName,\n            'next.span_type': type,\n            ...options.attributes\n        };\n        return context.with(spanContext.setValue(rootSpanIdKey, spanId), ()=>this.getTracerInstance().startActiveSpan(spanName, options, (span)=>{\n                const startTime = 'performance' in globalThis && 'measure' in performance ? globalThis.performance.now() : undefined;\n                const onCleanup = ()=>{\n                    rootSpanAttributesStore.delete(spanId);\n                    if (startTime && process.env.NEXT_OTEL_PERFORMANCE_PREFIX && _constants.LogSpanAllowList.includes(type || '')) {\n                        performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(type.split('.').pop() || '').replace(/[A-Z]/g, (match)=>'-' + match.toLowerCase())}`, {\n                            start: startTime,\n                            end: performance.now()\n                        });\n                    }\n                };\n                if (isRootSpan) {\n                    rootSpanAttributesStore.set(spanId, new Map(Object.entries(options.attributes ?? {})));\n                }\n                try {\n                    if (fn.length > 1) {\n                        return fn(span, (err)=>closeSpanWithError(span, err));\n                    }\n                    const result = fn(span);\n                    if ((0, _isthenable.isThenable)(result)) {\n                        // If there's error make sure it throws\n                        return result.then((res)=>{\n                            span.end();\n                            // Need to pass down the promise result,\n                            // it could be react stream response with error { error, stream }\n                            return res;\n                        }).catch((err)=>{\n                            closeSpanWithError(span, err);\n                            throw err;\n                        }).finally(onCleanup);\n                    } else {\n                        span.end();\n                        onCleanup();\n                    }\n                    return result;\n                } catch (err) {\n                    closeSpanWithError(span, err);\n                    onCleanup();\n                    throw err;\n                }\n            }));\n    }\n    wrap(...args) {\n        const tracer = this;\n        const [name, options, fn] = args.length === 3 ? args : [\n            args[0],\n            {},\n            args[1]\n        ];\n        if (!_constants.NextVanillaSpanAllowlist.includes(name) && process.env.NEXT_OTEL_VERBOSE !== '1') {\n            return fn;\n        }\n        return function() {\n            let optionsObj = options;\n            if (typeof optionsObj === 'function' && typeof fn === 'function') {\n                optionsObj = optionsObj.apply(this, arguments);\n            }\n            const lastArgId = arguments.length - 1;\n            const cb = arguments[lastArgId];\n            if (typeof cb === 'function') {\n                const scopeBoundCb = tracer.getContext().bind(context.active(), cb);\n                return tracer.trace(name, optionsObj, (_span, done)=>{\n                    arguments[lastArgId] = function(err) {\n                        done == null ? void 0 : done(err);\n                        return scopeBoundCb.apply(this, arguments);\n                    };\n                    return fn.apply(this, arguments);\n                });\n            } else {\n                return tracer.trace(name, optionsObj, ()=>fn.apply(this, arguments));\n            }\n        };\n    }\n    startSpan(...args) {\n        const [type, options] = args;\n        const spanContext = this.getSpanContext((options == null ? void 0 : options.parentSpan) ?? this.getActiveScopeSpan());\n        return this.getTracerInstance().startSpan(type, options, spanContext);\n    }\n    getSpanContext(parentSpan) {\n        const spanContext = parentSpan ? trace.setSpan(context.active(), parentSpan) : undefined;\n        return spanContext;\n    }\n    getRootSpanAttributes() {\n        const spanId = context.active().getValue(rootSpanIdKey);\n        return rootSpanAttributesStore.get(spanId);\n    }\n    setRootSpanAttribute(key, value) {\n        const spanId = context.active().getValue(rootSpanIdKey);\n        const attributes = rootSpanAttributesStore.get(spanId);\n        if (attributes) {\n            attributes.set(key, value);\n        }\n    }\n}\nconst getTracer = (()=>{\n    const tracer = new NextTracerImpl();\n    return ()=>tracer;\n})();\n\n//# sourceMappingURL=tracer.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"getPageFiles\", {\n    enumerable: true,\n    get: function() {\n        return getPageFiles;\n    }\n});\nconst _denormalizepagepath = require(\"../shared/lib/page-path/denormalize-page-path\");\nconst _normalizepagepath = require(\"../shared/lib/page-path/normalize-page-path\");\nfunction getPageFiles(buildManifest, page) {\n    const normalizedPage = (0, _denormalizepagepath.denormalizePagePath)((0, _normalizepagepath.normalizePagePath)(page));\n    let files = buildManifest.pages[normalizedPage];\n    if (!files) {\n        console.warn(`Could not find files for ${normalizedPage} in .next/build-manifest.json`);\n        return [];\n    }\n    return files;\n}\n\n//# sourceMappingURL=get-page-files.js.map", "(()=>{\"use strict\";var e={491:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ContextAPI=void 0;const n=r(223);const a=r(172);const o=r(930);const i=\"context\";const c=new n.NoopContextManager;class ContextAPI{constructor(){}static getInstance(){if(!this._instance){this._instance=new ContextAPI}return this._instance}setGlobalContextManager(e){return(0,a.registerGlobal)(i,e,o.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,a.getGlobal)(i)||c}disable(){this._getContextManager().disable();(0,a.unregisterGlobal)(i,o.DiagAPI.instance())}}t.ContextAPI=ContextAPI},930:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.DiagAPI=void 0;const n=r(56);const a=r(912);const o=r(957);const i=r(172);const c=\"diag\";class DiagAPI{constructor(){function _logProxy(e){return function(...t){const r=(0,i.getGlobal)(\"diag\");if(!r)return;return r[e](...t)}}const e=this;const setLogger=(t,r={logLevel:o.DiagLogLevel.INFO})=>{var n,c,s;if(t===e){const t=new Error(\"Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation\");e.error((n=t.stack)!==null&&n!==void 0?n:t.message);return false}if(typeof r===\"number\"){r={logLevel:r}}const u=(0,i.getGlobal)(\"diag\");const l=(0,a.createLogLevelDiagLogger)((c=r.logLevel)!==null&&c!==void 0?c:o.DiagLogLevel.INFO,t);if(u&&!r.suppressOverrideMessage){const e=(s=(new Error).stack)!==null&&s!==void 0?s:\"<failed to generate stacktrace>\";u.warn(`Current logger will be overwritten from ${e}`);l.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,i.registerGlobal)(\"diag\",l,e,true)};e.setLogger=setLogger;e.disable=()=>{(0,i.unregisterGlobal)(c,e)};e.createComponentLogger=e=>new n.DiagComponentLogger(e);e.verbose=_logProxy(\"verbose\");e.debug=_logProxy(\"debug\");e.info=_logProxy(\"info\");e.warn=_logProxy(\"warn\");e.error=_logProxy(\"error\")}static instance(){if(!this._instance){this._instance=new DiagAPI}return this._instance}}t.DiagAPI=DiagAPI},653:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.MetricsAPI=void 0;const n=r(660);const a=r(172);const o=r(930);const i=\"metrics\";class MetricsAPI{constructor(){}static getInstance(){if(!this._instance){this._instance=new MetricsAPI}return this._instance}setGlobalMeterProvider(e){return(0,a.registerGlobal)(i,e,o.DiagAPI.instance())}getMeterProvider(){return(0,a.getGlobal)(i)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,a.unregisterGlobal)(i,o.DiagAPI.instance())}}t.MetricsAPI=MetricsAPI},181:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.PropagationAPI=void 0;const n=r(172);const a=r(874);const o=r(194);const i=r(277);const c=r(369);const s=r(930);const u=\"propagation\";const l=new a.NoopTextMapPropagator;class PropagationAPI{constructor(){this.createBaggage=c.createBaggage;this.getBaggage=i.getBaggage;this.getActiveBaggage=i.getActiveBaggage;this.setBaggage=i.setBaggage;this.deleteBaggage=i.deleteBaggage}static getInstance(){if(!this._instance){this._instance=new PropagationAPI}return this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(u,e,s.DiagAPI.instance())}inject(e,t,r=o.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=o.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(u,s.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(u)||l}}t.PropagationAPI=PropagationAPI},997:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.TraceAPI=void 0;const n=r(172);const a=r(846);const o=r(139);const i=r(607);const c=r(930);const s=\"trace\";class TraceAPI{constructor(){this._proxyTracerProvider=new a.ProxyTracerProvider;this.wrapSpanContext=o.wrapSpanContext;this.isSpanContextValid=o.isSpanContextValid;this.deleteSpan=i.deleteSpan;this.getSpan=i.getSpan;this.getActiveSpan=i.getActiveSpan;this.getSpanContext=i.getSpanContext;this.setSpan=i.setSpan;this.setSpanContext=i.setSpanContext}static getInstance(){if(!this._instance){this._instance=new TraceAPI}return this._instance}setGlobalTracerProvider(e){const t=(0,n.registerGlobal)(s,this._proxyTracerProvider,c.DiagAPI.instance());if(t){this._proxyTracerProvider.setDelegate(e)}return t}getTracerProvider(){return(0,n.getGlobal)(s)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(s,c.DiagAPI.instance());this._proxyTracerProvider=new a.ProxyTracerProvider}}t.TraceAPI=TraceAPI},277:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;const n=r(491);const a=r(780);const o=(0,a.createContextKey)(\"OpenTelemetry Baggage Key\");function getBaggage(e){return e.getValue(o)||undefined}t.getBaggage=getBaggage;function getActiveBaggage(){return getBaggage(n.ContextAPI.getInstance().active())}t.getActiveBaggage=getActiveBaggage;function setBaggage(e,t){return e.setValue(o,t)}t.setBaggage=setBaggage;function deleteBaggage(e){return e.deleteValue(o)}t.deleteBaggage=deleteBaggage},993:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.BaggageImpl=void 0;class BaggageImpl{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){const t=this._entries.get(e);if(!t){return undefined}return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map((([e,t])=>[e,t]))}setEntry(e,t){const r=new BaggageImpl(this._entries);r._entries.set(e,t);return r}removeEntry(e){const t=new BaggageImpl(this._entries);t._entries.delete(e);return t}removeEntries(...e){const t=new BaggageImpl(this._entries);for(const r of e){t._entries.delete(r)}return t}clear(){return new BaggageImpl}}t.BaggageImpl=BaggageImpl},830:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.baggageEntryMetadataSymbol=void 0;t.baggageEntryMetadataSymbol=Symbol(\"BaggageEntryMetadata\")},369:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.baggageEntryMetadataFromString=t.createBaggage=void 0;const n=r(930);const a=r(993);const o=r(830);const i=n.DiagAPI.instance();function createBaggage(e={}){return new a.BaggageImpl(new Map(Object.entries(e)))}t.createBaggage=createBaggage;function baggageEntryMetadataFromString(e){if(typeof e!==\"string\"){i.error(`Cannot create baggage metadata from unknown type: ${typeof e}`);e=\"\"}return{__TYPE__:o.baggageEntryMetadataSymbol,toString(){return e}}}t.baggageEntryMetadataFromString=baggageEntryMetadataFromString},67:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.context=void 0;const n=r(491);t.context=n.ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NoopContextManager=void 0;const n=r(780);class NoopContextManager{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=NoopContextManager},780:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ROOT_CONTEXT=t.createContextKey=void 0;function createContextKey(e){return Symbol.for(e)}t.createContextKey=createContextKey;class BaseContext{constructor(e){const t=this;t._currentContext=e?new Map(e):new Map;t.getValue=e=>t._currentContext.get(e);t.setValue=(e,r)=>{const n=new BaseContext(t._currentContext);n._currentContext.set(e,r);return n};t.deleteValue=e=>{const r=new BaseContext(t._currentContext);r._currentContext.delete(e);return r}}}t.ROOT_CONTEXT=new BaseContext},506:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.diag=void 0;const n=r(930);t.diag=n.DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.DiagComponentLogger=void 0;const n=r(172);class DiagComponentLogger{constructor(e){this._namespace=e.namespace||\"DiagComponentLogger\"}debug(...e){return logProxy(\"debug\",this._namespace,e)}error(...e){return logProxy(\"error\",this._namespace,e)}info(...e){return logProxy(\"info\",this._namespace,e)}warn(...e){return logProxy(\"warn\",this._namespace,e)}verbose(...e){return logProxy(\"verbose\",this._namespace,e)}}t.DiagComponentLogger=DiagComponentLogger;function logProxy(e,t,r){const a=(0,n.getGlobal)(\"diag\");if(!a){return}r.unshift(t);return a[e](...r)}},972:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.DiagConsoleLogger=void 0;const r=[{n:\"error\",c:\"error\"},{n:\"warn\",c:\"warn\"},{n:\"info\",c:\"info\"},{n:\"debug\",c:\"debug\"},{n:\"verbose\",c:\"trace\"}];class DiagConsoleLogger{constructor(){function _consoleFunc(e){return function(...t){if(console){let r=console[e];if(typeof r!==\"function\"){r=console.log}if(typeof r===\"function\"){return r.apply(console,t)}}}}for(let e=0;e<r.length;e++){this[r[e].n]=_consoleFunc(r[e].c)}}}t.DiagConsoleLogger=DiagConsoleLogger},912:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.createLogLevelDiagLogger=void 0;const n=r(957);function createLogLevelDiagLogger(e,t){if(e<n.DiagLogLevel.NONE){e=n.DiagLogLevel.NONE}else if(e>n.DiagLogLevel.ALL){e=n.DiagLogLevel.ALL}t=t||{};function _filterFunc(r,n){const a=t[r];if(typeof a===\"function\"&&e>=n){return a.bind(t)}return function(){}}return{error:_filterFunc(\"error\",n.DiagLogLevel.ERROR),warn:_filterFunc(\"warn\",n.DiagLogLevel.WARN),info:_filterFunc(\"info\",n.DiagLogLevel.INFO),debug:_filterFunc(\"debug\",n.DiagLogLevel.DEBUG),verbose:_filterFunc(\"verbose\",n.DiagLogLevel.VERBOSE)}}t.createLogLevelDiagLogger=createLogLevelDiagLogger},957:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.DiagLogLevel=void 0;var r;(function(e){e[e[\"NONE\"]=0]=\"NONE\";e[e[\"ERROR\"]=30]=\"ERROR\";e[e[\"WARN\"]=50]=\"WARN\";e[e[\"INFO\"]=60]=\"INFO\";e[e[\"DEBUG\"]=70]=\"DEBUG\";e[e[\"VERBOSE\"]=80]=\"VERBOSE\";e[e[\"ALL\"]=9999]=\"ALL\"})(r=t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;const n=r(200);const a=r(521);const o=r(130);const i=a.VERSION.split(\".\")[0];const c=Symbol.for(`opentelemetry.js.api.${i}`);const s=n._globalThis;function registerGlobal(e,t,r,n=false){var o;const i=s[c]=(o=s[c])!==null&&o!==void 0?o:{version:a.VERSION};if(!n&&i[e]){const t=new Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);r.error(t.stack||t.message);return false}if(i.version!==a.VERSION){const t=new Error(`@opentelemetry/api: Registration of version v${i.version} for ${e} does not match previously registered API v${a.VERSION}`);r.error(t.stack||t.message);return false}i[e]=t;r.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`);return true}t.registerGlobal=registerGlobal;function getGlobal(e){var t,r;const n=(t=s[c])===null||t===void 0?void 0:t.version;if(!n||!(0,o.isCompatible)(n)){return}return(r=s[c])===null||r===void 0?void 0:r[e]}t.getGlobal=getGlobal;function unregisterGlobal(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);const r=s[c];if(r){delete r[e]}}t.unregisterGlobal=unregisterGlobal},130:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.isCompatible=t._makeCompatibilityCheck=void 0;const n=r(521);const a=/^(\\d+)\\.(\\d+)\\.(\\d+)(-(.+))?$/;function _makeCompatibilityCheck(e){const t=new Set([e]);const r=new Set;const n=e.match(a);if(!n){return()=>false}const o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(o.prerelease!=null){return function isExactmatch(t){return t===e}}function _reject(e){r.add(e);return false}function _accept(e){t.add(e);return true}return function isCompatible(e){if(t.has(e)){return true}if(r.has(e)){return false}const n=e.match(a);if(!n){return _reject(e)}const i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(i.prerelease!=null){return _reject(e)}if(o.major!==i.major){return _reject(e)}if(o.major===0){if(o.minor===i.minor&&o.patch<=i.patch){return _accept(e)}return _reject(e)}if(o.minor<=i.minor){return _accept(e)}return _reject(e)}}t._makeCompatibilityCheck=_makeCompatibilityCheck;t.isCompatible=_makeCompatibilityCheck(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.metrics=void 0;const n=r(653);t.metrics=n.MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ValueType=void 0;var r;(function(e){e[e[\"INT\"]=0]=\"INT\";e[e[\"DOUBLE\"]=1]=\"DOUBLE\"})(r=t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class NoopMeter{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=NoopMeter;class NoopMetric{}t.NoopMetric=NoopMetric;class NoopCounterMetric extends NoopMetric{add(e,t){}}t.NoopCounterMetric=NoopCounterMetric;class NoopUpDownCounterMetric extends NoopMetric{add(e,t){}}t.NoopUpDownCounterMetric=NoopUpDownCounterMetric;class NoopHistogramMetric extends NoopMetric{record(e,t){}}t.NoopHistogramMetric=NoopHistogramMetric;class NoopObservableMetric{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=NoopObservableMetric;class NoopObservableCounterMetric extends NoopObservableMetric{}t.NoopObservableCounterMetric=NoopObservableCounterMetric;class NoopObservableGaugeMetric extends NoopObservableMetric{}t.NoopObservableGaugeMetric=NoopObservableGaugeMetric;class NoopObservableUpDownCounterMetric extends NoopObservableMetric{}t.NoopObservableUpDownCounterMetric=NoopObservableUpDownCounterMetric;t.NOOP_METER=new NoopMeter;t.NOOP_COUNTER_METRIC=new NoopCounterMetric;t.NOOP_HISTOGRAM_METRIC=new NoopHistogramMetric;t.NOOP_UP_DOWN_COUNTER_METRIC=new NoopUpDownCounterMetric;t.NOOP_OBSERVABLE_COUNTER_METRIC=new NoopObservableCounterMetric;t.NOOP_OBSERVABLE_GAUGE_METRIC=new NoopObservableGaugeMetric;t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new NoopObservableUpDownCounterMetric;function createNoopMeter(){return t.NOOP_METER}t.createNoopMeter=createNoopMeter},660:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;const n=r(102);class NoopMeterProvider{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=NoopMeterProvider;t.NOOP_METER_PROVIDER=new NoopMeterProvider},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;Object.defineProperty(e,n,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var a=this&&this.__exportStar||function(e,t){for(var r in e)if(r!==\"default\"&&!Object.prototype.hasOwnProperty.call(t,r))n(t,e,r)};Object.defineProperty(t,\"__esModule\",{value:true});a(r(46),t)},651:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t._globalThis=void 0;t._globalThis=typeof globalThis===\"object\"?globalThis:global},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){if(n===undefined)n=r;Object.defineProperty(e,n,{enumerable:true,get:function(){return t[r]}})}:function(e,t,r,n){if(n===undefined)n=r;e[n]=t[r]});var a=this&&this.__exportStar||function(e,t){for(var r in e)if(r!==\"default\"&&!Object.prototype.hasOwnProperty.call(t,r))n(t,e,r)};Object.defineProperty(t,\"__esModule\",{value:true});a(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.propagation=void 0;const n=r(181);t.propagation=n.PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NoopTextMapPropagator=void 0;class NoopTextMapPropagator{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=NoopTextMapPropagator},194:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.defaultTextMapSetter=t.defaultTextMapGetter=void 0;t.defaultTextMapGetter={get(e,t){if(e==null){return undefined}return e[t]},keys(e){if(e==null){return[]}return Object.keys(e)}};t.defaultTextMapSetter={set(e,t,r){if(e==null){return}e[t]=r}}},845:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.trace=void 0;const n=r(997);t.trace=n.TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NonRecordingSpan=void 0;const n=r(476);class NonRecordingSpan{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return false}recordException(e,t){}}t.NonRecordingSpan=NonRecordingSpan},614:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NoopTracer=void 0;const n=r(491);const a=r(607);const o=r(403);const i=r(139);const c=n.ContextAPI.getInstance();class NoopTracer{startSpan(e,t,r=c.active()){const n=Boolean(t===null||t===void 0?void 0:t.root);if(n){return new o.NonRecordingSpan}const s=r&&(0,a.getSpanContext)(r);if(isSpanContext(s)&&(0,i.isSpanContextValid)(s)){return new o.NonRecordingSpan(s)}else{return new o.NonRecordingSpan}}startActiveSpan(e,t,r,n){let o;let i;let s;if(arguments.length<2){return}else if(arguments.length===2){s=t}else if(arguments.length===3){o=t;s=r}else{o=t;i=r;s=n}const u=i!==null&&i!==void 0?i:c.active();const l=this.startSpan(e,o,u);const g=(0,a.setSpan)(u,l);return c.with(g,s,undefined,l)}}t.NoopTracer=NoopTracer;function isSpanContext(e){return typeof e===\"object\"&&typeof e[\"spanId\"]===\"string\"&&typeof e[\"traceId\"]===\"string\"&&typeof e[\"traceFlags\"]===\"number\"}},124:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.NoopTracerProvider=void 0;const n=r(614);class NoopTracerProvider{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=NoopTracerProvider},125:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ProxyTracer=void 0;const n=r(614);const a=new n.NoopTracer;class ProxyTracer{constructor(e,t,r,n){this._provider=e;this.name=t;this.version=r;this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){const a=this._getTracer();return Reflect.apply(a.startActiveSpan,a,arguments)}_getTracer(){if(this._delegate){return this._delegate}const e=this._provider.getDelegateTracer(this.name,this.version,this.options);if(!e){return a}this._delegate=e;return this._delegate}}t.ProxyTracer=ProxyTracer},846:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.ProxyTracerProvider=void 0;const n=r(125);const a=r(124);const o=new a.NoopTracerProvider;class ProxyTracerProvider{getTracer(e,t,r){var a;return(a=this.getDelegateTracer(e,t,r))!==null&&a!==void 0?a:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return(e=this._delegate)!==null&&e!==void 0?e:o}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return(n=this._delegate)===null||n===void 0?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=ProxyTracerProvider},996:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.SamplingDecision=void 0;var r;(function(e){e[e[\"NOT_RECORD\"]=0]=\"NOT_RECORD\";e[e[\"RECORD\"]=1]=\"RECORD\";e[e[\"RECORD_AND_SAMPLED\"]=2]=\"RECORD_AND_SAMPLED\"})(r=t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;const n=r(780);const a=r(403);const o=r(491);const i=(0,n.createContextKey)(\"OpenTelemetry Context Key SPAN\");function getSpan(e){return e.getValue(i)||undefined}t.getSpan=getSpan;function getActiveSpan(){return getSpan(o.ContextAPI.getInstance().active())}t.getActiveSpan=getActiveSpan;function setSpan(e,t){return e.setValue(i,t)}t.setSpan=setSpan;function deleteSpan(e){return e.deleteValue(i)}t.deleteSpan=deleteSpan;function setSpanContext(e,t){return setSpan(e,new a.NonRecordingSpan(t))}t.setSpanContext=setSpanContext;function getSpanContext(e){var t;return(t=getSpan(e))===null||t===void 0?void 0:t.spanContext()}t.getSpanContext=getSpanContext},325:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.TraceStateImpl=void 0;const n=r(564);const a=32;const o=512;const i=\",\";const c=\"=\";class TraceStateImpl{constructor(e){this._internalState=new Map;if(e)this._parse(e)}set(e,t){const r=this._clone();if(r._internalState.has(e)){r._internalState.delete(e)}r._internalState.set(e,t);return r}unset(e){const t=this._clone();t._internalState.delete(e);return t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce(((e,t)=>{e.push(t+c+this.get(t));return e}),[]).join(i)}_parse(e){if(e.length>o)return;this._internalState=e.split(i).reverse().reduce(((e,t)=>{const r=t.trim();const a=r.indexOf(c);if(a!==-1){const o=r.slice(0,a);const i=r.slice(a+1,t.length);if((0,n.validateKey)(o)&&(0,n.validateValue)(i)){e.set(o,i)}else{}}return e}),new Map);if(this._internalState.size>a){this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,a))}}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){const e=new TraceStateImpl;e._internalState=new Map(this._internalState);return e}}t.TraceStateImpl=TraceStateImpl},564:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.validateValue=t.validateKey=void 0;const r=\"[_0-9a-z-*/]\";const n=`[a-z]${r}{0,255}`;const a=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`;const o=new RegExp(`^(?:${n}|${a})$`);const i=/^[ -~]{0,255}[!-~]$/;const c=/,|=/;function validateKey(e){return o.test(e)}t.validateKey=validateKey;function validateValue(e){return i.test(e)&&!c.test(e)}t.validateValue=validateValue},98:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.createTraceState=void 0;const n=r(325);function createTraceState(e){return new n.TraceStateImpl(e)}t.createTraceState=createTraceState},476:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;const n=r(475);t.INVALID_SPANID=\"0000000000000000\";t.INVALID_TRACEID=\"00000000000000000000000000000000\";t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.SpanKind=void 0;var r;(function(e){e[e[\"INTERNAL\"]=0]=\"INTERNAL\";e[e[\"SERVER\"]=1]=\"SERVER\";e[e[\"CLIENT\"]=2]=\"CLIENT\";e[e[\"PRODUCER\"]=3]=\"PRODUCER\";e[e[\"CONSUMER\"]=4]=\"CONSUMER\"})(r=t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;const n=r(476);const a=r(403);const o=/^([0-9a-f]{32})$/i;const i=/^[0-9a-f]{16}$/i;function isValidTraceId(e){return o.test(e)&&e!==n.INVALID_TRACEID}t.isValidTraceId=isValidTraceId;function isValidSpanId(e){return i.test(e)&&e!==n.INVALID_SPANID}t.isValidSpanId=isValidSpanId;function isSpanContextValid(e){return isValidTraceId(e.traceId)&&isValidSpanId(e.spanId)}t.isSpanContextValid=isSpanContextValid;function wrapSpanContext(e){return new a.NonRecordingSpan(e)}t.wrapSpanContext=wrapSpanContext},847:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.SpanStatusCode=void 0;var r;(function(e){e[e[\"UNSET\"]=0]=\"UNSET\";e[e[\"OK\"]=1]=\"OK\";e[e[\"ERROR\"]=2]=\"ERROR\"})(r=t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.TraceFlags=void 0;var r;(function(e){e[e[\"NONE\"]=0]=\"NONE\";e[e[\"SAMPLED\"]=1]=\"SAMPLED\"})(r=t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});t.VERSION=void 0;t.VERSION=\"1.6.0\"}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var a=t[r]={exports:{}};var o=true;try{e[r].call(a.exports,a,a.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return a.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r={};(()=>{var e=r;Object.defineProperty(e,\"__esModule\",{value:true});e.trace=e.propagation=e.metrics=e.diag=e.context=e.INVALID_SPAN_CONTEXT=e.INVALID_TRACEID=e.INVALID_SPANID=e.isValidSpanId=e.isValidTraceId=e.isSpanContextValid=e.createTraceState=e.TraceFlags=e.SpanStatusCode=e.SpanKind=e.SamplingDecision=e.ProxyTracerProvider=e.ProxyTracer=e.defaultTextMapSetter=e.defaultTextMapGetter=e.ValueType=e.createNoopMeter=e.DiagLogLevel=e.DiagConsoleLogger=e.ROOT_CONTEXT=e.createContextKey=e.baggageEntryMetadataFromString=void 0;var t=__nccwpck_require__(369);Object.defineProperty(e,\"baggageEntryMetadataFromString\",{enumerable:true,get:function(){return t.baggageEntryMetadataFromString}});var n=__nccwpck_require__(780);Object.defineProperty(e,\"createContextKey\",{enumerable:true,get:function(){return n.createContextKey}});Object.defineProperty(e,\"ROOT_CONTEXT\",{enumerable:true,get:function(){return n.ROOT_CONTEXT}});var a=__nccwpck_require__(972);Object.defineProperty(e,\"DiagConsoleLogger\",{enumerable:true,get:function(){return a.DiagConsoleLogger}});var o=__nccwpck_require__(957);Object.defineProperty(e,\"DiagLogLevel\",{enumerable:true,get:function(){return o.DiagLogLevel}});var i=__nccwpck_require__(102);Object.defineProperty(e,\"createNoopMeter\",{enumerable:true,get:function(){return i.createNoopMeter}});var c=__nccwpck_require__(901);Object.defineProperty(e,\"ValueType\",{enumerable:true,get:function(){return c.ValueType}});var s=__nccwpck_require__(194);Object.defineProperty(e,\"defaultTextMapGetter\",{enumerable:true,get:function(){return s.defaultTextMapGetter}});Object.defineProperty(e,\"defaultTextMapSetter\",{enumerable:true,get:function(){return s.defaultTextMapSetter}});var u=__nccwpck_require__(125);Object.defineProperty(e,\"ProxyTracer\",{enumerable:true,get:function(){return u.ProxyTracer}});var l=__nccwpck_require__(846);Object.defineProperty(e,\"ProxyTracerProvider\",{enumerable:true,get:function(){return l.ProxyTracerProvider}});var g=__nccwpck_require__(996);Object.defineProperty(e,\"SamplingDecision\",{enumerable:true,get:function(){return g.SamplingDecision}});var p=__nccwpck_require__(357);Object.defineProperty(e,\"SpanKind\",{enumerable:true,get:function(){return p.SpanKind}});var d=__nccwpck_require__(847);Object.defineProperty(e,\"SpanStatusCode\",{enumerable:true,get:function(){return d.SpanStatusCode}});var _=__nccwpck_require__(475);Object.defineProperty(e,\"TraceFlags\",{enumerable:true,get:function(){return _.TraceFlags}});var f=__nccwpck_require__(98);Object.defineProperty(e,\"createTraceState\",{enumerable:true,get:function(){return f.createTraceState}});var b=__nccwpck_require__(139);Object.defineProperty(e,\"isSpanContextValid\",{enumerable:true,get:function(){return b.isSpanContextValid}});Object.defineProperty(e,\"isValidTraceId\",{enumerable:true,get:function(){return b.isValidTraceId}});Object.defineProperty(e,\"isValidSpanId\",{enumerable:true,get:function(){return b.isValidSpanId}});var v=__nccwpck_require__(476);Object.defineProperty(e,\"INVALID_SPANID\",{enumerable:true,get:function(){return v.INVALID_SPANID}});Object.defineProperty(e,\"INVALID_TRACEID\",{enumerable:true,get:function(){return v.INVALID_TRACEID}});Object.defineProperty(e,\"INVALID_SPAN_CONTEXT\",{enumerable:true,get:function(){return v.INVALID_SPAN_CONTEXT}});const O=__nccwpck_require__(67);Object.defineProperty(e,\"context\",{enumerable:true,get:function(){return O.context}});const P=__nccwpck_require__(506);Object.defineProperty(e,\"diag\",{enumerable:true,get:function(){return P.diag}});const N=__nccwpck_require__(886);Object.defineProperty(e,\"metrics\",{enumerable:true,get:function(){return N.metrics}});const S=__nccwpck_require__(939);Object.defineProperty(e,\"propagation\",{enumerable:true,get:function(){return S.propagation}});const C=__nccwpck_require__(845);Object.defineProperty(e,\"trace\",{enumerable:true,get:function(){return C.trace}});e[\"default\"]={context:O.context,diag:P.diag,metrics:N.metrics,propagation:S.propagation,trace:C.trace}})();module.exports=r})();", "/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n", "\"use strict\";\nmodule.exports = require('../../module.compiled').vendored['contexts'].HtmlContext;\n\n//# sourceMappingURL=html-context.js.map", "/**\n * For a given page path, this function ensures that there is no backslash\n * escaping slashes in the path. Example:\n *  - `foo\\/bar\\/baz` -> `foo/bar/baz`\n */\nexport function normalizePathSep(path: string): string {\n  return path.replace(/\\\\/g, '/')\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    default: null,\n    getProperError: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * Checks whether the given value is a NextError.\n * This can be used to print a more detailed error message with properties like `code` & `digest`.\n */ default: function() {\n        return isError;\n    },\n    getProperError: function() {\n        return getProperError;\n    }\n});\nconst _isplainobject = require(\"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === 'object' && err !== null && 'name' in err && 'message' in err;\n}\nfunction safeStringify(obj) {\n    const seen = new WeakSet();\n    return JSON.stringify(obj, (_key, value)=>{\n        // If value is an object and already seen, replace with \"[Circular]\"\n        if (typeof value === 'object' && value !== null) {\n            if (seen.has(value)) {\n                return '[Circular]';\n            }\n            seen.add(value);\n        }\n        return value;\n    });\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (process.env.NODE_ENV === 'development') {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === 'undefined') {\n            return Object.defineProperty(new Error('An undefined error was thrown, ' + 'see here for more info: https://nextjs.org/docs/messages/threw-undefined'), \"__NEXT_ERROR_CODE\", {\n                value: \"E98\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (err === null) {\n            return Object.defineProperty(new Error('A null error was thrown, ' + 'see here for more info: https://nextjs.org/docs/messages/threw-undefined'), \"__NEXT_ERROR_CODE\", {\n                value: \"E336\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    return Object.defineProperty(new Error((0, _isplainobject.isPlainObject)(err) ? safeStringify(err) : err + ''), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n}\n\n//# sourceMappingURL=is-error.js.map", "import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n", "export function getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nexport function isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\n}\n", "class UrlNode {\n  placeholder: boolean = true\n  children: Map<string, UrlNode> = new Map()\n  slugName: string | null = null\n  restSlugName: string | null = null\n  optionalRestSlugName: string | null = null\n\n  insert(urlPath: string): void {\n    this._insert(urlPath.split('/').filter(Boolean), [], false)\n  }\n\n  smoosh(): string[] {\n    return this._smoosh()\n  }\n\n  private _smoosh(prefix: string = '/'): string[] {\n    const childrenPaths = [...this.children.keys()].sort()\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[]'), 1)\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[...]'), 1)\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[[...]]'), 1)\n    }\n\n    const routes = childrenPaths\n      .map((c) => this.children.get(c)!._smoosh(`${prefix}${c}/`))\n      .reduce((prev, curr) => [...prev, ...curr], [])\n\n    if (this.slugName !== null) {\n      routes.push(\n        ...this.children.get('[]')!._smoosh(`${prefix}[${this.slugName}]/`)\n      )\n    }\n\n    if (!this.placeholder) {\n      const r = prefix === '/' ? '/' : prefix.slice(0, -1)\n      if (this.optionalRestSlugName != null) {\n        throw new Error(\n          `You cannot define a route with the same specificity as a optional catch-all route (\"${r}\" and \"${r}[[...${this.optionalRestSlugName}]]\").`\n        )\n      }\n\n      routes.unshift(r)\n    }\n\n    if (this.restSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[...]')!\n          ._smoosh(`${prefix}[...${this.restSlugName}]/`)\n      )\n    }\n\n    if (this.optionalRestSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[[...]]')!\n          ._smoosh(`${prefix}[[...${this.optionalRestSlugName}]]/`)\n      )\n    }\n\n    return routes\n  }\n\n  private _insert(\n    urlPaths: string[],\n    slugNames: string[],\n    isCatchAll: boolean\n  ): void {\n    if (urlPaths.length === 0) {\n      this.placeholder = false\n      return\n    }\n\n    if (isCatchAll) {\n      throw new Error(`Catch-all must be the last part of the URL.`)\n    }\n\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0]\n\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith('[') && nextSegment.endsWith(']')) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1)\n\n      let isOptional = false\n      if (segmentName.startsWith('[') && segmentName.endsWith(']')) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1)\n        isOptional = true\n      }\n\n      if (segmentName.startsWith('…')) {\n        throw new Error(\n          `Detected a three-dot character ('…') at ('${segmentName}'). Did you mean ('...')?`\n        )\n      }\n\n      if (segmentName.startsWith('...')) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3)\n        isCatchAll = true\n      }\n\n      if (segmentName.startsWith('[') || segmentName.endsWith(']')) {\n        throw new Error(\n          `Segment names may not start or end with extra brackets ('${segmentName}').`\n        )\n      }\n\n      if (segmentName.startsWith('.')) {\n        throw new Error(\n          `Segment names may not start with erroneous periods ('${segmentName}').`\n        )\n      }\n\n      function handleSlug(previousSlug: string | null, nextSlug: string) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\n              `You cannot use different slug names for the same dynamic path ('${previousSlug}' !== '${nextSlug}').`\n            )\n          }\n        }\n\n        slugNames.forEach((slug) => {\n          if (slug === nextSlug) {\n            throw new Error(\n              `You cannot have the same slug name \"${nextSlug}\" repeat within a single dynamic path`\n            )\n          }\n\n          if (slug.replace(/\\W/g, '') === nextSegment.replace(/\\W/g, '')) {\n            throw new Error(\n              `You cannot have the slug names \"${slug}\" and \"${nextSlug}\" differ only by non-word symbols within a single dynamic path`\n            )\n          }\n        })\n\n        slugNames.push(nextSlug)\n      }\n\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error(\n              `You cannot use both an required and optional catch-all route at the same level (\"[...${this.restSlugName}]\" and \"${urlPaths[0]}\" ).`\n            )\n          }\n\n          handleSlug(this.optionalRestSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = '[[...]]'\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error(\n              `You cannot use both an optional and required catch-all route at the same level (\"[[...${this.optionalRestSlugName}]]\" and \"${urlPaths[0]}\").`\n            )\n          }\n\n          handleSlug(this.restSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = '[...]'\n        }\n      } else {\n        if (isOptional) {\n          throw new Error(\n            `Optional route parameters are not yet supported (\"${urlPaths[0]}\").`\n          )\n        }\n        handleSlug(this.slugName, segmentName)\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = '[]'\n      }\n    }\n\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode())\n    }\n\n    this.children\n      .get(nextSegment)!\n      ._insert(urlPaths.slice(1), slugNames, isCatchAll)\n  }\n}\n\nexport function getSortedRoutes(\n  normalizedPages: ReadonlyArray<string>\n): string[] {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode()\n\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach((pagePath) => root.insert(pagePath))\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh()\n}\n\nexport function getSortedRouteObjects<T>(\n  objects: T[],\n  getter: (obj: T) => string\n): T[] {\n  // We're assuming here that all the pathnames are unique, that way we can\n  // sort the list and use the index as the key.\n  const indexes: Record<string, number> = {}\n  const pathnames: string[] = []\n  for (let i = 0; i < objects.length; i++) {\n    const pathname = getter(objects[i])\n    indexes[pathname] = i\n    pathnames[i] = pathname\n  }\n\n  // Sort the pathnames.\n  const sorted = getSortedRoutes(pathnames)\n\n  // Map the sorted pathnames back to the original objects using the new sorted\n  // index.\n  return sorted.map((pathname) => objects[indexes[pathname]])\n}\n", "// Note: This file is JS because it's used by the taskfile-swc.js file, which is JS.\n// Keep file changes in sync with the corresponding `.d.ts` files.\n/**\n * These are the browser versions that support all of the following:\n * static import: https://caniuse.com/es6-module\n * dynamic import: https://caniuse.com/es6-module-dynamic-import\n * import.meta: https://caniuse.com/mdn-javascript_operators_import_meta\n */\nconst MODERN_BROWSERSLIST_TARGET = [\n  'chrome 64',\n  'edge 79',\n  'firefox 67',\n  'opera 51',\n  'safari 12',\n]\n\nmodule.exports = MODERN_BROWSERSLIST_TARGET\n", "\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n", "// This utility is based on https://github.com/zertosh/htmlescape\n// License: https://github.com/zertosh/htmlescape/blob/0527ca7156a524d256101bb310a9f970f63078ad/LICENSE\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    ESCAPE_REGEX: null,\n    htmlEscapeJsonString: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ESCAPE_REGEX: function() {\n        return ESCAPE_REGEX;\n    },\n    htmlEscapeJsonString: function() {\n        return htmlEscapeJsonString;\n    }\n});\nconst ESCAPE_LOOKUP = {\n    '&': '\\\\u0026',\n    '>': '\\\\u003e',\n    '<': '\\\\u003c',\n    '\\u2028': '\\\\u2028',\n    '\\u2029': '\\\\u2029'\n};\nconst ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction htmlEscapeJsonString(str) {\n    return str.replace(ESCAPE_REGEX, (match)=>ESCAPE_LOOKUP[match]);\n}\n\n//# sourceMappingURL=htmlescape.js.map", "import { isDynamicRoute } from '../router/utils'\nimport { normalizePathSep } from './normalize-path-sep'\n\n/**\n * Performs the opposite transformation of `normalizePagePath`. Note that\n * this function is not idempotent either in cases where there are multiple\n * leading `/index` for the page. Examples:\n *  - `/index` -> `/`\n *  - `/index/foo` -> `/foo`\n *  - `/index/index` -> `/index`\n */\nexport function denormalizePagePath(page: string) {\n  let _page = normalizePathSep(page)\n  return _page.startsWith('/index/') && !isDynamicRoute(_page)\n    ? _page.slice(6)\n    : _page !== '/index'\n      ? _page\n      : '/'\n}\n", "/// <reference types=\"webpack/module.d.ts\" />\n\nimport React, { type JSX } from 'react'\nimport { NEXT_BUILTIN_DOCUMENT } from '../shared/lib/constants'\nimport type {\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps,\n  DocumentType,\n  NEXT_DATA,\n} from '../shared/lib/utils'\nimport type { ScriptProps } from '../client/script'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\n\nimport { getPageFiles } from '../server/get-page-files'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { htmlEscapeJsonString } from '../server/htmlescape'\nimport isError from '../lib/is-error'\n\nimport {\n  HtmlContext,\n  useHtmlContext,\n} from '../shared/lib/html-context.shared-runtime'\nimport type { HtmlProps } from '../shared/lib/html-context.shared-runtime'\nimport { encodeURIPath } from '../shared/lib/encode-uri-path'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { getTracer } from '../server/lib/trace/tracer'\nimport { getTracedMetadata } from '../server/lib/trace/utils'\n\nexport type { DocumentContext, DocumentInitialProps, DocumentProps }\n\nexport type OriginProps = {\n  nonce?: string\n  crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined\n  children?: React.ReactNode\n}\n\ntype DocumentFiles = {\n  sharedFiles: readonly string[]\n  pageFiles: readonly string[]\n  allFiles: readonly string[]\n}\n\ntype HeadHTMLProps = React.DetailedHTMLProps<\n  React.HTMLAttributes<HTMLHeadElement>,\n  HTMLHeadElement\n>\n\ntype HeadProps = OriginProps & HeadHTMLProps\n\n/** Set of pages that have triggered a large data warning on production mode. */\nconst largePageDataWarnings = new Set<string>()\n\nfunction getDocumentFiles(\n  buildManifest: BuildManifest,\n  pathname: string,\n  inAmpMode: boolean\n): DocumentFiles {\n  const sharedFiles: readonly string[] = getPageFiles(buildManifest, '/_app')\n  const pageFiles: readonly string[] =\n    process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n      ? []\n      : getPageFiles(buildManifest, pathname)\n\n  return {\n    sharedFiles,\n    pageFiles,\n    allFiles: [...new Set([...sharedFiles, ...pageFiles])],\n  }\n}\n\nfunction getPolyfillScripts(context: HtmlProps, props: OriginProps) {\n  // polyfills.js has to be rendered as nomodule without async\n  // It also has to be the first script to load\n  const {\n    assetPrefix,\n    buildManifest,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin,\n  } = context\n\n  return buildManifest.polyfillFiles\n    .filter(\n      (polyfill) => polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n    )\n    .map((polyfill) => (\n      <script\n        key={polyfill}\n        defer={!disableOptimizedLoading}\n        nonce={props.nonce}\n        crossOrigin={props.crossOrigin || crossOrigin}\n        noModule={true}\n        src={`${assetPrefix}/_next/${encodeURIPath(\n          polyfill\n        )}${assetQueryString}`}\n      />\n    ))\n}\n\nfunction hasComponentProps(child: any): child is React.ReactElement<any> {\n  return !!child && !!child.props\n}\n\nfunction AmpStyles({\n  styles,\n}: {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode>\n}) {\n  if (!styles) return null\n\n  // try to parse styles from fragment for backwards compat\n  const curStyles: React.ReactElement<any>[] = Array.isArray(styles)\n    ? (styles as React.ReactElement[])\n    : []\n  if (\n    // @ts-ignore Property 'props' does not exist on type ReactElement\n    styles.props &&\n    // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)\n  ) {\n    const hasStyles = (el: React.ReactElement<any>) =>\n      el?.props?.dangerouslySetInnerHTML?.__html\n    // @ts-ignore Property 'props' does not exist on type ReactElement\n    styles.props.children.forEach((child: React.ReactElement) => {\n      if (Array.isArray(child)) {\n        child.forEach((el) => hasStyles(el) && curStyles.push(el))\n      } else if (hasStyles(child)) {\n        curStyles.push(child)\n      }\n    })\n  }\n\n  /* Add custom styles before AMP styles to prevent accidental overrides */\n  return (\n    <style\n      amp-custom=\"\"\n      dangerouslySetInnerHTML={{\n        __html: curStyles\n          .map((style) => style.props.dangerouslySetInnerHTML.__html)\n          .join('')\n          .replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, '')\n          .replace(/\\/\\*@ sourceURL=.*?\\*\\//g, ''),\n      }}\n    />\n  )\n}\n\nfunction getDynamicChunks(\n  context: HtmlProps,\n  props: OriginProps,\n  files: DocumentFiles\n) {\n  const {\n    dynamicImports,\n    assetPrefix,\n    isDevelopment,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin,\n  } = context\n\n  return dynamicImports.map((file) => {\n    if (!file.endsWith('.js') || files.allFiles.includes(file)) return null\n\n    return (\n      <script\n        async={!isDevelopment && disableOptimizedLoading}\n        defer={!disableOptimizedLoading}\n        key={file}\n        src={`${assetPrefix}/_next/${encodeURIPath(file)}${assetQueryString}`}\n        nonce={props.nonce}\n        crossOrigin={props.crossOrigin || crossOrigin}\n      />\n    )\n  })\n}\n\nfunction getScripts(\n  context: HtmlProps,\n  props: OriginProps,\n  files: DocumentFiles\n) {\n  const {\n    assetPrefix,\n    buildManifest,\n    isDevelopment,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin,\n  } = context\n\n  const normalScripts = files.allFiles.filter((file) => file.endsWith('.js'))\n  const lowPriorityScripts = buildManifest.lowPriorityFiles?.filter((file) =>\n    file.endsWith('.js')\n  )\n\n  return [...normalScripts, ...lowPriorityScripts].map((file) => {\n    return (\n      <script\n        key={file}\n        src={`${assetPrefix}/_next/${encodeURIPath(file)}${assetQueryString}`}\n        nonce={props.nonce}\n        async={!isDevelopment && disableOptimizedLoading}\n        defer={!disableOptimizedLoading}\n        crossOrigin={props.crossOrigin || crossOrigin}\n      />\n    )\n  })\n}\n\nfunction getPreNextWorkerScripts(context: HtmlProps, props: OriginProps) {\n  const { assetPrefix, scriptLoader, crossOrigin, nextScriptWorkers } = context\n\n  // disable `nextScriptWorkers` in edge runtime\n  if (!nextScriptWorkers || process.env.NEXT_RUNTIME === 'edge') return null\n\n  try {\n    // @ts-expect-error: Prevent webpack from processing this require\n    let { partytownSnippet } = __non_webpack_require__(\n      '@builder.io/partytown/integration'!\n    )\n\n    const children = Array.isArray(props.children)\n      ? props.children\n      : [props.children]\n\n    // Check to see if the user has defined their own Partytown configuration\n    const userDefinedConfig = children.find(\n      (child) =>\n        hasComponentProps(child) &&\n        child?.props?.dangerouslySetInnerHTML?.__html.length &&\n        'data-partytown-config' in child.props\n    )\n\n    return (\n      <>\n        {!userDefinedConfig && (\n          <script\n            data-partytown-config=\"\"\n            dangerouslySetInnerHTML={{\n              __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `,\n            }}\n          />\n        )}\n        <script\n          data-partytown=\"\"\n          dangerouslySetInnerHTML={{\n            __html: partytownSnippet(),\n          }}\n        />\n        {(scriptLoader.worker || []).map((file: ScriptProps, index: number) => {\n          const {\n            strategy,\n            src,\n            children: scriptChildren,\n            dangerouslySetInnerHTML,\n            ...scriptProps\n          } = file\n\n          let srcProps: {\n            src?: string\n            dangerouslySetInnerHTML?: ScriptProps['dangerouslySetInnerHTML']\n          } = {}\n\n          if (src) {\n            // Use external src if provided\n            srcProps.src = src\n          } else if (\n            dangerouslySetInnerHTML &&\n            dangerouslySetInnerHTML.__html\n          ) {\n            // Embed inline script if provided with dangerouslySetInnerHTML\n            srcProps.dangerouslySetInnerHTML = {\n              __html: dangerouslySetInnerHTML.__html,\n            }\n          } else if (scriptChildren) {\n            // Embed inline script if provided with children\n            srcProps.dangerouslySetInnerHTML = {\n              __html:\n                typeof scriptChildren === 'string'\n                  ? scriptChildren\n                  : Array.isArray(scriptChildren)\n                    ? scriptChildren.join('')\n                    : '',\n            }\n          } else {\n            throw new Error(\n              'Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script'\n            )\n          }\n\n          return (\n            <script\n              {...srcProps}\n              {...scriptProps}\n              type=\"text/partytown\"\n              key={src || index}\n              nonce={props.nonce}\n              data-nscript=\"worker\"\n              crossOrigin={props.crossOrigin || crossOrigin}\n            />\n          )\n        })}\n      </>\n    )\n  } catch (err) {\n    if (isError(err) && err.code !== 'MODULE_NOT_FOUND') {\n      console.warn(`Warning: ${err.message}`)\n    }\n    return null\n  }\n}\n\nfunction getPreNextScripts(context: HtmlProps, props: OriginProps) {\n  const { scriptLoader, disableOptimizedLoading, crossOrigin } = context\n\n  const webWorkerScripts = getPreNextWorkerScripts(context, props)\n\n  const beforeInteractiveScripts = (scriptLoader.beforeInteractive || [])\n    .filter((script) => script.src)\n    .map((file: ScriptProps, index: number) => {\n      const { strategy, ...scriptProps } = file\n      return (\n        <script\n          {...scriptProps}\n          key={scriptProps.src || index}\n          defer={scriptProps.defer ?? !disableOptimizedLoading}\n          nonce={props.nonce}\n          data-nscript=\"beforeInteractive\"\n          crossOrigin={props.crossOrigin || crossOrigin}\n        />\n      )\n    })\n\n  return (\n    <>\n      {webWorkerScripts}\n      {beforeInteractiveScripts}\n    </>\n  )\n}\n\nfunction getHeadHTMLProps(props: HeadProps) {\n  const { crossOrigin, nonce, ...restProps } = props\n\n  // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n  const headProps: HeadHTMLProps & {\n    [P in Exclude<keyof HeadProps, keyof HeadHTMLProps>]?: never\n  } = restProps\n\n  return headProps\n}\n\nfunction getAmpPath(ampPath: string, asPath: string): string {\n  return ampPath || `${asPath}${asPath.includes('?') ? '&' : '?'}amp=1`\n}\n\nfunction getNextFontLinkTags(\n  nextFontManifest: DeepReadonly<NextFontManifest> | undefined,\n  dangerousAsPath: string,\n  assetPrefix: string = ''\n) {\n  if (!nextFontManifest) {\n    return {\n      preconnect: null,\n      preload: null,\n    }\n  }\n\n  const appFontsEntry = nextFontManifest.pages['/_app']\n  const pageFontsEntry = nextFontManifest.pages[dangerousAsPath]\n\n  const preloadedFontFiles = Array.from(\n    new Set([...(appFontsEntry ?? []), ...(pageFontsEntry ?? [])])\n  )\n\n  // If no font files should preload but there's an entry for the path, add a preconnect tag.\n  const preconnectToSelf = !!(\n    preloadedFontFiles.length === 0 &&\n    (appFontsEntry || pageFontsEntry)\n  )\n\n  return {\n    preconnect: preconnectToSelf ? (\n      <link\n        data-next-font={\n          nextFontManifest.pagesUsingSizeAdjust ? 'size-adjust' : ''\n        }\n        rel=\"preconnect\"\n        href=\"/\"\n        crossOrigin=\"anonymous\"\n      />\n    ) : null,\n    preload: preloadedFontFiles\n      ? preloadedFontFiles.map((fontFile) => {\n          const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)![1]\n          return (\n            <link\n              key={fontFile}\n              rel=\"preload\"\n              href={`${assetPrefix}/_next/${encodeURIPath(fontFile)}`}\n              as=\"font\"\n              type={`font/${ext}`}\n              crossOrigin=\"anonymous\"\n              data-next-font={fontFile.includes('-s') ? 'size-adjust' : ''}\n            />\n          )\n        })\n      : null,\n  }\n}\n\n// Use `React.Component` to avoid errors from the RSC checks because\n// it can't be imported directly in Server Components:\n//\n//   import { Component } from 'react'\n//\n// More info: https://github.com/vercel/next.js/pull/40686\nexport class Head extends React.Component<HeadProps> {\n  static contextType = HtmlContext\n\n  context!: HtmlProps\n\n  getCssLinks(files: DocumentFiles): JSX.Element[] | null {\n    const {\n      assetPrefix,\n      assetQueryString,\n      dynamicImports,\n      dynamicCssManifest,\n      crossOrigin,\n      optimizeCss,\n    } = this.context\n    const cssFiles = files.allFiles.filter((f) => f.endsWith('.css'))\n    const sharedFiles: Set<string> = new Set(files.sharedFiles)\n\n    // Unmanaged files are CSS files that will be handled directly by the\n    // webpack runtime (`mini-css-extract-plugin`).\n    let unmanagedFiles: Set<string> = new Set([])\n    let localDynamicCssFiles = Array.from(\n      new Set(dynamicImports.filter((file) => file.endsWith('.css')))\n    )\n    if (localDynamicCssFiles.length) {\n      const existing = new Set(cssFiles)\n      localDynamicCssFiles = localDynamicCssFiles.filter(\n        (f) => !(existing.has(f) || sharedFiles.has(f))\n      )\n      unmanagedFiles = new Set(localDynamicCssFiles)\n      cssFiles.push(...localDynamicCssFiles)\n    }\n\n    let cssLinkElements: JSX.Element[] = []\n    cssFiles.forEach((file) => {\n      const isSharedFile = sharedFiles.has(file)\n      const isUnmanagedFile = unmanagedFiles.has(file)\n      const isFileInDynamicCssManifest = dynamicCssManifest.has(file)\n\n      if (!optimizeCss) {\n        cssLinkElements.push(\n          <link\n            key={`${file}-preload`}\n            nonce={this.props.nonce}\n            rel=\"preload\"\n            href={`${assetPrefix}/_next/${encodeURIPath(\n              file\n            )}${assetQueryString}`}\n            as=\"style\"\n            crossOrigin={this.props.crossOrigin || crossOrigin}\n          />\n        )\n      }\n\n      cssLinkElements.push(\n        <link\n          key={file}\n          nonce={this.props.nonce}\n          rel=\"stylesheet\"\n          href={`${assetPrefix}/_next/${encodeURIPath(\n            file\n          )}${assetQueryString}`}\n          crossOrigin={this.props.crossOrigin || crossOrigin}\n          data-n-g={isUnmanagedFile ? undefined : isSharedFile ? '' : undefined}\n          data-n-p={\n            isSharedFile || isUnmanagedFile || isFileInDynamicCssManifest\n              ? undefined\n              : ''\n          }\n        />\n      )\n    })\n\n    return cssLinkElements.length === 0 ? null : cssLinkElements\n  }\n\n  getPreloadDynamicChunks() {\n    const { dynamicImports, assetPrefix, assetQueryString, crossOrigin } =\n      this.context\n\n    return (\n      dynamicImports\n        .map((file) => {\n          if (!file.endsWith('.js')) {\n            return null\n          }\n\n          return (\n            <link\n              rel=\"preload\"\n              key={file}\n              href={`${assetPrefix}/_next/${encodeURIPath(\n                file\n              )}${assetQueryString}`}\n              as=\"script\"\n              nonce={this.props.nonce}\n              crossOrigin={this.props.crossOrigin || crossOrigin}\n            />\n          )\n        })\n        // Filter out nulled scripts\n        .filter(Boolean)\n    )\n  }\n\n  getPreloadMainLinks(files: DocumentFiles): JSX.Element[] | null {\n    const { assetPrefix, assetQueryString, scriptLoader, crossOrigin } =\n      this.context\n    const preloadFiles = files.allFiles.filter((file: string) => {\n      return file.endsWith('.js')\n    })\n\n    return [\n      ...(scriptLoader.beforeInteractive || []).map((file) => (\n        <link\n          key={file.src}\n          nonce={this.props.nonce}\n          rel=\"preload\"\n          href={file.src}\n          as=\"script\"\n          crossOrigin={this.props.crossOrigin || crossOrigin}\n        />\n      )),\n      ...preloadFiles.map((file: string) => (\n        <link\n          key={file}\n          nonce={this.props.nonce}\n          rel=\"preload\"\n          href={`${assetPrefix}/_next/${encodeURIPath(\n            file\n          )}${assetQueryString}`}\n          as=\"script\"\n          crossOrigin={this.props.crossOrigin || crossOrigin}\n        />\n      )),\n    ]\n  }\n\n  getBeforeInteractiveInlineScripts() {\n    const { scriptLoader } = this.context\n    const { nonce, crossOrigin } = this.props\n\n    return (scriptLoader.beforeInteractive || [])\n      .filter(\n        (script) =>\n          !script.src && (script.dangerouslySetInnerHTML || script.children)\n      )\n      .map((file: ScriptProps, index: number) => {\n        const {\n          strategy,\n          children,\n          dangerouslySetInnerHTML,\n          src,\n          ...scriptProps\n        } = file\n        let html: NonNullable<\n          ScriptProps['dangerouslySetInnerHTML']\n        >['__html'] = ''\n\n        if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n          html = dangerouslySetInnerHTML.__html\n        } else if (children) {\n          html =\n            typeof children === 'string'\n              ? children\n              : Array.isArray(children)\n                ? children.join('')\n                : ''\n        }\n\n        return (\n          <script\n            {...scriptProps}\n            dangerouslySetInnerHTML={{ __html: html }}\n            key={scriptProps.id || index}\n            nonce={nonce}\n            data-nscript=\"beforeInteractive\"\n            crossOrigin={\n              crossOrigin ||\n              (process.env.__NEXT_CROSS_ORIGIN as typeof crossOrigin)\n            }\n          />\n        )\n      })\n  }\n\n  getDynamicChunks(files: DocumentFiles) {\n    return getDynamicChunks(this.context, this.props, files)\n  }\n\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props)\n  }\n\n  getScripts(files: DocumentFiles) {\n    return getScripts(this.context, this.props, files)\n  }\n\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props)\n  }\n\n  render() {\n    const {\n      styles,\n      ampPath,\n      inAmpMode,\n      hybridAmp,\n      canonicalBase,\n      __NEXT_DATA__,\n      dangerousAsPath,\n      headTags,\n      unstable_runtimeJS,\n      unstable_JsPreload,\n      disableOptimizedLoading,\n      optimizeCss,\n      assetPrefix,\n      nextFontManifest,\n    } = this.context\n\n    const disableRuntimeJS = unstable_runtimeJS === false\n    const disableJsPreload =\n      unstable_JsPreload === false || !disableOptimizedLoading\n\n    this.context.docComponentsRendered.Head = true\n\n    let { head } = this.context\n    let cssPreloads: Array<JSX.Element> = []\n    let otherHeadElements: Array<JSX.Element> = []\n    if (head) {\n      head.forEach((child) => {\n        if (\n          child &&\n          child.type === 'link' &&\n          child.props['rel'] === 'preload' &&\n          child.props['as'] === 'style'\n        ) {\n          if (this.context.strictNextHead) {\n            cssPreloads.push(\n              React.cloneElement(child, { 'data-next-head': '' })\n            )\n          } else {\n            cssPreloads.push(child)\n          }\n        } else {\n          if (child) {\n            if (this.context.strictNextHead) {\n              otherHeadElements.push(\n                React.cloneElement(child, { 'data-next-head': '' })\n              )\n            } else {\n              otherHeadElements.push(child)\n            }\n          }\n        }\n      })\n      head = cssPreloads.concat(otherHeadElements)\n    }\n    let children: React.ReactNode[] = React.Children.toArray(\n      this.props.children\n    ).filter(Boolean)\n    // show a warning if Head contains <title> (only in development)\n    if (process.env.NODE_ENV !== 'production') {\n      children = React.Children.map(children, (child: any) => {\n        const isReactHelmet = child?.props?.['data-react-helmet']\n        if (!isReactHelmet) {\n          if (child?.type === 'title') {\n            console.warn(\n              \"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\"\n            )\n          } else if (\n            child?.type === 'meta' &&\n            child?.props?.name === 'viewport'\n          ) {\n            console.warn(\n              \"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\"\n            )\n          }\n        }\n        return child\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n      })!\n      if (this.props.crossOrigin)\n        console.warn(\n          'Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated'\n        )\n    }\n\n    let hasAmphtmlRel = false\n    let hasCanonicalRel = false\n\n    // show warning and remove conflicting amp head tags\n    head = React.Children.map(head || [], (child) => {\n      if (!child) return child\n      const { type, props } = child\n      if (process.env.NEXT_RUNTIME !== 'edge' && inAmpMode) {\n        let badProp: string = ''\n\n        if (type === 'meta' && props.name === 'viewport') {\n          badProp = 'name=\"viewport\"'\n        } else if (type === 'link' && props.rel === 'canonical') {\n          hasCanonicalRel = true\n        } else if (type === 'script') {\n          // only block if\n          // 1. it has a src and isn't pointing to ampproject's CDN\n          // 2. it is using dangerouslySetInnerHTML without a type or\n          // a type of text/javascript\n          if (\n            (props.src && props.src.indexOf('ampproject') < -1) ||\n            (props.dangerouslySetInnerHTML &&\n              (!props.type || props.type === 'text/javascript'))\n          ) {\n            badProp = '<script'\n            Object.keys(props).forEach((prop) => {\n              badProp += ` ${prop}=\"${props[prop]}\"`\n            })\n            badProp += '/>'\n          }\n        }\n\n        if (badProp) {\n          console.warn(\n            `Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`\n          )\n          return null\n        }\n      } else {\n        // non-amp mode\n        if (type === 'link' && props.rel === 'amphtml') {\n          hasAmphtmlRel = true\n        }\n      }\n      return child\n      // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n    })!\n\n    const files: DocumentFiles = getDocumentFiles(\n      this.context.buildManifest,\n      this.context.__NEXT_DATA__.page,\n      process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n    )\n\n    const nextFontLinkTags = getNextFontLinkTags(\n      nextFontManifest,\n      dangerousAsPath,\n      assetPrefix\n    )\n\n    const tracingMetadata = getTracedMetadata(\n      getTracer().getTracePropagationData(),\n      this.context.experimentalClientTraceMetadata\n    )\n\n    const traceMetaTags = (tracingMetadata || []).map(\n      ({ key, value }, index) => (\n        <meta key={`next-trace-data-${index}`} name={key} content={value} />\n      )\n    )\n\n    return (\n      <head {...getHeadHTMLProps(this.props)}>\n        {this.context.isDevelopment && (\n          <>\n            <style\n              data-next-hide-fouc\n              data-ampdevmode={\n                process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n                  ? 'true'\n                  : undefined\n              }\n              dangerouslySetInnerHTML={{\n                __html: `body{display:none}`,\n              }}\n            />\n            <noscript\n              data-next-hide-fouc\n              data-ampdevmode={\n                process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n                  ? 'true'\n                  : undefined\n              }\n            >\n              <style\n                dangerouslySetInnerHTML={{\n                  __html: `body{display:block}`,\n                }}\n              />\n            </noscript>\n          </>\n        )}\n        {head}\n        {this.context.strictNextHead ? null : (\n          <meta\n            name=\"next-head-count\"\n            content={React.Children.count(head || []).toString()}\n          />\n        )}\n\n        {children}\n\n        {nextFontLinkTags.preconnect}\n        {nextFontLinkTags.preload}\n\n        {process.env.NEXT_RUNTIME !== 'edge' && inAmpMode && (\n          <>\n            <meta\n              name=\"viewport\"\n              content=\"width=device-width,minimum-scale=1,initial-scale=1\"\n            />\n            {!hasCanonicalRel && (\n              <link\n                rel=\"canonical\"\n                href={\n                  canonicalBase +\n                  require('../server/utils').cleanAmpPath(dangerousAsPath)\n                }\n              />\n            )}\n            {/* https://www.ampproject.org/docs/fundamentals/optimize_amp#optimize-the-amp-runtime-loading */}\n            <link\n              rel=\"preload\"\n              as=\"script\"\n              href=\"https://cdn.ampproject.org/v0.js\"\n            />\n            <AmpStyles styles={styles} />\n            <style\n              amp-boilerplate=\"\"\n              dangerouslySetInnerHTML={{\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`,\n              }}\n            />\n            <noscript>\n              <style\n                amp-boilerplate=\"\"\n                dangerouslySetInnerHTML={{\n                  __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`,\n                }}\n              />\n            </noscript>\n            <script async src=\"https://cdn.ampproject.org/v0.js\" />\n          </>\n        )}\n        {!(process.env.NEXT_RUNTIME !== 'edge' && inAmpMode) && (\n          <>\n            {!hasAmphtmlRel && hybridAmp && (\n              <link\n                rel=\"amphtml\"\n                href={canonicalBase + getAmpPath(ampPath, dangerousAsPath)}\n              />\n            )}\n            {this.getBeforeInteractiveInlineScripts()}\n            {!optimizeCss && this.getCssLinks(files)}\n            {!optimizeCss && <noscript data-n-css={this.props.nonce ?? ''} />}\n\n            {!disableRuntimeJS &&\n              !disableJsPreload &&\n              this.getPreloadDynamicChunks()}\n            {!disableRuntimeJS &&\n              !disableJsPreload &&\n              this.getPreloadMainLinks(files)}\n\n            {!disableOptimizedLoading &&\n              !disableRuntimeJS &&\n              this.getPolyfillScripts()}\n\n            {!disableOptimizedLoading &&\n              !disableRuntimeJS &&\n              this.getPreNextScripts()}\n            {!disableOptimizedLoading &&\n              !disableRuntimeJS &&\n              this.getDynamicChunks(files)}\n            {!disableOptimizedLoading &&\n              !disableRuntimeJS &&\n              this.getScripts(files)}\n\n            {optimizeCss && this.getCssLinks(files)}\n            {optimizeCss && <noscript data-n-css={this.props.nonce ?? ''} />}\n            {this.context.isDevelopment && (\n              // this element is used to mount development styles so the\n              // ordering matches production\n              // (by default, style-loader injects at the bottom of <head />)\n              <noscript id=\"__next_css__DO_NOT_USE__\" />\n            )}\n            {traceMetaTags}\n            {styles || null}\n          </>\n        )}\n        {React.createElement(React.Fragment, {}, ...(headTags || []))}\n      </head>\n    )\n  }\n}\n\nfunction handleDocumentScriptLoaderItems(\n  scriptLoader: { beforeInteractive?: any[] },\n  __NEXT_DATA__: NEXT_DATA,\n  props: any\n): void {\n  if (!props.children) return\n\n  const scriptLoaderItems: ScriptProps[] = []\n\n  const children = Array.isArray(props.children)\n    ? props.children\n    : [props.children]\n\n  const headChildren = children.find(\n    (child: React.ReactElement) => child.type === Head\n  )?.props?.children\n  const bodyChildren = children.find(\n    (child: React.ReactElement) => child.type === 'body'\n  )?.props?.children\n\n  // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n  const combinedChildren = [\n    ...(Array.isArray(headChildren) ? headChildren : [headChildren]),\n    ...(Array.isArray(bodyChildren) ? bodyChildren : [bodyChildren]),\n  ]\n\n  React.Children.forEach(combinedChildren, (child: any) => {\n    if (!child) return\n\n    // When using the `next/script` component, register it in script loader.\n    if (child.type?.__nextScript) {\n      if (child.props.strategy === 'beforeInteractive') {\n        scriptLoader.beforeInteractive = (\n          scriptLoader.beforeInteractive || []\n        ).concat([\n          {\n            ...child.props,\n          },\n        ])\n        return\n      } else if (\n        ['lazyOnload', 'afterInteractive', 'worker'].includes(\n          child.props.strategy\n        )\n      ) {\n        scriptLoaderItems.push(child.props)\n        return\n      } else if (typeof child.props.strategy === 'undefined') {\n        scriptLoaderItems.push({ ...child.props, strategy: 'afterInteractive' })\n        return\n      }\n    }\n  })\n\n  __NEXT_DATA__.scriptLoader = scriptLoaderItems\n}\n\nexport class NextScript extends React.Component<OriginProps> {\n  static contextType = HtmlContext\n\n  context!: HtmlProps\n\n  getDynamicChunks(files: DocumentFiles) {\n    return getDynamicChunks(this.context, this.props, files)\n  }\n\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props)\n  }\n\n  getScripts(files: DocumentFiles) {\n    return getScripts(this.context, this.props, files)\n  }\n\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props)\n  }\n\n  static getInlineScriptSource(context: Readonly<HtmlProps>): string {\n    const { __NEXT_DATA__, largePageDataBytes } = context\n    try {\n      const data = JSON.stringify(__NEXT_DATA__)\n\n      if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n        return htmlEscapeJsonString(data)\n      }\n\n      const bytes =\n        process.env.NEXT_RUNTIME === 'edge'\n          ? new TextEncoder().encode(data).buffer.byteLength\n          : Buffer.from(data).byteLength\n      const prettyBytes = require('../lib/pretty-bytes').default\n\n      if (largePageDataBytes && bytes > largePageDataBytes) {\n        if (process.env.NODE_ENV === 'production') {\n          largePageDataWarnings.add(__NEXT_DATA__.page)\n        }\n\n        console.warn(\n          `Warning: data for page \"${__NEXT_DATA__.page}\"${\n            __NEXT_DATA__.page === context.dangerousAsPath\n              ? ''\n              : ` (path \"${context.dangerousAsPath}\")`\n          } is ${prettyBytes(\n            bytes\n          )} which exceeds the threshold of ${prettyBytes(\n            largePageDataBytes\n          )}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`\n        )\n      }\n\n      return htmlEscapeJsonString(data)\n    } catch (err) {\n      if (isError(err) && err.message.indexOf('circular structure') !== -1) {\n        throw new Error(\n          `Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`\n        )\n      }\n      throw err\n    }\n  }\n\n  render() {\n    const {\n      assetPrefix,\n      inAmpMode,\n      buildManifest,\n      unstable_runtimeJS,\n      docComponentsRendered,\n      assetQueryString,\n      disableOptimizedLoading,\n      crossOrigin,\n    } = this.context\n    const disableRuntimeJS = unstable_runtimeJS === false\n\n    docComponentsRendered.NextScript = true\n\n    if (process.env.NEXT_RUNTIME !== 'edge' && inAmpMode) {\n      if (process.env.NODE_ENV === 'production') {\n        return null\n      }\n      const ampDevFiles = [\n        ...buildManifest.devFiles,\n        ...buildManifest.polyfillFiles,\n        ...buildManifest.ampDevFiles,\n      ]\n\n      return (\n        <>\n          {disableRuntimeJS ? null : (\n            <script\n              id=\"__NEXT_DATA__\"\n              type=\"application/json\"\n              nonce={this.props.nonce}\n              crossOrigin={this.props.crossOrigin || crossOrigin}\n              dangerouslySetInnerHTML={{\n                __html: NextScript.getInlineScriptSource(this.context),\n              }}\n              data-ampdevmode\n            />\n          )}\n          {ampDevFiles.map((file) => (\n            <script\n              key={file}\n              src={`${assetPrefix}/_next/${encodeURIPath(\n                file\n              )}${assetQueryString}`}\n              nonce={this.props.nonce}\n              crossOrigin={this.props.crossOrigin || crossOrigin}\n              data-ampdevmode\n            />\n          ))}\n        </>\n      )\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (this.props.crossOrigin)\n        console.warn(\n          'Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated'\n        )\n    }\n\n    const files: DocumentFiles = getDocumentFiles(\n      this.context.buildManifest,\n      this.context.__NEXT_DATA__.page,\n      process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n    )\n\n    return (\n      <>\n        {!disableRuntimeJS && buildManifest.devFiles\n          ? buildManifest.devFiles.map((file: string) => (\n              <script\n                key={file}\n                src={`${assetPrefix}/_next/${encodeURIPath(\n                  file\n                )}${assetQueryString}`}\n                nonce={this.props.nonce}\n                crossOrigin={this.props.crossOrigin || crossOrigin}\n              />\n            ))\n          : null}\n        {disableRuntimeJS ? null : (\n          <script\n            id=\"__NEXT_DATA__\"\n            type=\"application/json\"\n            nonce={this.props.nonce}\n            crossOrigin={this.props.crossOrigin || crossOrigin}\n            dangerouslySetInnerHTML={{\n              __html: NextScript.getInlineScriptSource(this.context),\n            }}\n          />\n        )}\n        {disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getPolyfillScripts()}\n        {disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getPreNextScripts()}\n        {disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getDynamicChunks(files)}\n        {disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files)}\n      </>\n    )\n  }\n}\n\nexport function Html(\n  props: React.DetailedHTMLProps<\n    React.HtmlHTMLAttributes<HTMLHtmlElement>,\n    HTMLHtmlElement\n  >\n) {\n  const {\n    inAmpMode,\n    docComponentsRendered,\n    locale,\n    scriptLoader,\n    __NEXT_DATA__,\n  } = useHtmlContext()\n\n  docComponentsRendered.Html = true\n  handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props)\n\n  return (\n    <html\n      {...props}\n      lang={props.lang || locale || undefined}\n      amp={process.env.NEXT_RUNTIME !== 'edge' && inAmpMode ? '' : undefined}\n      data-ampdevmode={\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        inAmpMode &&\n        process.env.NODE_ENV !== 'production'\n          ? ''\n          : undefined\n      }\n    />\n  )\n}\n\nexport function Main() {\n  const { docComponentsRendered } = useHtmlContext()\n  docComponentsRendered.Main = true\n  // @ts-ignore\n  return <next-js-internal-body-render-target />\n}\n\n/**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */\nexport default class Document<P = {}> extends React.Component<\n  DocumentProps & P\n> {\n  /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */\n  static getInitialProps(ctx: DocumentContext): Promise<DocumentInitialProps> {\n    return ctx.defaultGetInitialProps(ctx)\n  }\n\n  render() {\n    return (\n      <Html>\n        <Head />\n        <body>\n          <Main />\n          <NextScript />\n        </body>\n      </Html>\n    )\n  }\n}\n\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument: DocumentType =\n  function InternalFunctionDocument() {\n    return (\n      <Html>\n        <Head />\n        <body>\n          <Main />\n          <NextScript />\n        </body>\n      </Html>\n    )\n  }\n;(Document as any)[NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument\n", "\"use strict\";\nif (process.env.NEXT_RUNTIME === 'edge') {\n    module.exports = require('next/dist/server/route-modules/pages/module.js');\n} else {\n    if (process.env.NODE_ENV === 'development') {\n        if (process.env.TURBOPACK) {\n            module.exports = require('next/dist/compiled/next-server/pages-turbo.runtime.dev.js');\n        } else {\n            module.exports = require('next/dist/compiled/next-server/pages.runtime.dev.js');\n        }\n    } else {\n        if (process.env.TURBOPACK) {\n            module.exports = require('next/dist/compiled/next-server/pages-turbo.runtime.prod.js');\n        } else {\n            module.exports = require('next/dist/compiled/next-server/pages.runtime.prod.js');\n        }\n    }\n}\n\n//# sourceMappingURL=module.compiled.js.map", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n", "import {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\n\n// Identify /.*[param].*/ in route string\nconst TEST_ROUTE = /\\/[^/]*\\[[^/]+\\][^/]*(?=\\/|$)/\n\n// Identify /[param]/ in route string\nconst TEST_STRICT_ROUTE = /\\/\\[[^/]+\\](?=\\/|$)/\n\n/**\n * Check if a route is dynamic.\n *\n * @param route - The route to check.\n * @param strict - Whether to use strict mode which prohibits segments with prefixes/suffixes (default: true).\n * @returns Whether the route is dynamic.\n */\nexport function isDynamicRoute(route: string, strict: boolean = true): boolean {\n  if (isInterceptionRouteAppPath(route)) {\n    route = extractInterceptionRouteInformation(route).interceptedRoute\n  }\n\n  if (strict) {\n    return TEST_STRICT_ROUTE.test(route)\n  }\n\n  return TEST_ROUTE.test(route)\n}\n", "export { getSortedRoutes, getSortedRouteObjects } from './sorted-routes'\nexport { isDynamicRoute } from './is-dynamic'\n", "import MODERN_BROWSERSLIST_TARGET from './modern-browserslist-target'\n\nexport { MODERN_BROWSERSLIST_TARGET }\n\nexport type ValueOf<T> = Required<T>[keyof T]\n\nexport const COMPILER_NAMES = {\n  client: 'client',\n  server: 'server',\n  edgeServer: 'edge-server',\n} as const\n\nexport type CompilerNameValues = ValueOf<typeof COMPILER_NAMES>\n\nexport const COMPILER_INDEXES: {\n  [compilerKey in CompilerNameValues]: number\n} = {\n  [COMPILER_NAMES.client]: 0,\n  [COMPILER_NAMES.server]: 1,\n  [COMPILER_NAMES.edgeServer]: 2,\n} as const\n\nexport const UNDERSCORE_NOT_FOUND_ROUTE = '/_not-found'\nexport const UNDERSCORE_NOT_FOUND_ROUTE_ENTRY = `${UNDERSCORE_NOT_FOUND_ROUTE}/page`\nexport const PHASE_EXPORT = 'phase-export'\nexport const PHASE_PRODUCTION_BUILD = 'phase-production-build'\nexport const PHASE_PRODUCTION_SERVER = 'phase-production-server'\nexport const PHASE_DEVELOPMENT_SERVER = 'phase-development-server'\nexport const PHASE_TEST = 'phase-test'\nexport const PHASE_INFO = 'phase-info'\nexport const PAGES_MANIFEST = 'pages-manifest.json'\nexport const WEBPACK_STATS = 'webpack-stats.json'\nexport const APP_PATHS_MANIFEST = 'app-paths-manifest.json'\nexport const APP_PATH_ROUTES_MANIFEST = 'app-path-routes-manifest.json'\nexport const BUILD_MANIFEST = 'build-manifest.json'\nexport const APP_BUILD_MANIFEST = 'app-build-manifest.json'\nexport const FUNCTIONS_CONFIG_MANIFEST = 'functions-config-manifest.json'\nexport const SUBRESOURCE_INTEGRITY_MANIFEST = 'subresource-integrity-manifest'\nexport const NEXT_FONT_MANIFEST = 'next-font-manifest'\nexport const EXPORT_MARKER = 'export-marker.json'\nexport const EXPORT_DETAIL = 'export-detail.json'\nexport const PRERENDER_MANIFEST = 'prerender-manifest.json'\nexport const ROUTES_MANIFEST = 'routes-manifest.json'\nexport const IMAGES_MANIFEST = 'images-manifest.json'\nexport const SERVER_FILES_MANIFEST = 'required-server-files.json'\nexport const DEV_CLIENT_PAGES_MANIFEST = '_devPagesManifest.json'\nexport const MIDDLEWARE_MANIFEST = 'middleware-manifest.json'\nexport const TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST =\n  '_clientMiddlewareManifest.json'\nexport const DEV_CLIENT_MIDDLEWARE_MANIFEST = '_devMiddlewareManifest.json'\nexport const REACT_LOADABLE_MANIFEST = 'react-loadable-manifest.json'\nexport const SERVER_DIRECTORY = 'server'\nexport const CONFIG_FILES = [\n  'next.config.js',\n  'next.config.mjs',\n  'next.config.ts',\n]\nexport const BUILD_ID_FILE = 'BUILD_ID'\nexport const BLOCKED_PAGES = ['/_document', '/_app', '/_error']\nexport const CLIENT_PUBLIC_FILES_PATH = 'public'\nexport const CLIENT_STATIC_FILES_PATH = 'static'\nexport const STRING_LITERAL_DROP_BUNDLE = '__NEXT_DROP_CLIENT_FILE__'\nexport const NEXT_BUILTIN_DOCUMENT = '__NEXT_BUILTIN_DOCUMENT__'\nexport const BARREL_OPTIMIZATION_PREFIX = '__barrel_optimize__'\n\n// server/[entry]/page_client-reference-manifest.js\nexport const CLIENT_REFERENCE_MANIFEST = 'client-reference-manifest'\n// server/server-reference-manifest\nexport const SERVER_REFERENCE_MANIFEST = 'server-reference-manifest'\n// server/middleware-build-manifest.js\nexport const MIDDLEWARE_BUILD_MANIFEST = 'middleware-build-manifest'\n// server/middleware-react-loadable-manifest.js\nexport const MIDDLEWARE_REACT_LOADABLE_MANIFEST =\n  'middleware-react-loadable-manifest'\n// server/interception-route-rewrite-manifest.js\nexport const INTERCEPTION_ROUTE_REWRITE_MANIFEST =\n  'interception-route-rewrite-manifest'\n// server/dynamic-css-manifest.js\nexport const DYNAMIC_CSS_MANIFEST = 'dynamic-css-manifest'\n\n// static/runtime/main.js\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN = `main`\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN_APP = `${CLIENT_STATIC_FILES_RUNTIME_MAIN}-app`\n// next internal client components chunk for layouts\nexport const APP_CLIENT_INTERNALS = 'app-pages-internals'\n// static/runtime/react-refresh.js\nexport const CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH = `react-refresh`\n// static/runtime/amp.js\nexport const CLIENT_STATIC_FILES_RUNTIME_AMP = `amp`\n// static/runtime/webpack.js\nexport const CLIENT_STATIC_FILES_RUNTIME_WEBPACK = `webpack`\n// static/runtime/polyfills.js\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS = 'polyfills'\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL = Symbol(\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS\n)\nexport const DEFAULT_RUNTIME_WEBPACK = 'webpack-runtime'\nexport const EDGE_RUNTIME_WEBPACK = 'edge-runtime-webpack'\nexport const STATIC_PROPS_ID = '__N_SSG'\nexport const SERVER_PROPS_ID = '__N_SSP'\nexport const DEFAULT_SERIF_FONT = {\n  name: 'Times New Roman',\n  xAvgCharWidth: 821,\n  azAvgWidth: 854.3953488372093,\n  unitsPerEm: 2048,\n}\nexport const DEFAULT_SANS_SERIF_FONT = {\n  name: 'Arial',\n  xAvgCharWidth: 904,\n  azAvgWidth: 934.5116279069767,\n  unitsPerEm: 2048,\n}\nexport const STATIC_STATUS_PAGES = ['/500']\nexport const TRACE_OUTPUT_VERSION = 1\n// in `MB`\nexport const TURBO_TRACE_DEFAULT_MEMORY_LIMIT = 6000\n\nexport const RSC_MODULE_TYPES = {\n  client: 'client',\n  server: 'server',\n} as const\n\n// comparing\n// https://nextjs.org/docs/api-reference/edge-runtime\n// with\n// https://nodejs.org/docs/latest/api/globals.html\nexport const EDGE_UNSUPPORTED_NODE_APIS = [\n  'clearImmediate',\n  'setImmediate',\n  'BroadcastChannel',\n  'ByteLengthQueuingStrategy',\n  'CompressionStream',\n  'CountQueuingStrategy',\n  'DecompressionStream',\n  'DomException',\n  'MessageChannel',\n  'MessageEvent',\n  'MessagePort',\n  'ReadableByteStreamController',\n  'ReadableStreamBYOBRequest',\n  'ReadableStreamDefaultController',\n  'TransformStreamDefaultController',\n  'WritableStreamDefaultController',\n]\n\nexport const SYSTEM_ENTRYPOINTS = new Set<string>([\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n])\n", "export function encodeURIPath(file: string) {\n  return file\n    .split('/')\n    .map((p) => encodeURIComponent(p))\n    .join('/')\n}\n", "import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n", "/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/ // eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */ \"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    AppRenderSpan: null,\n    AppRouteRouteHandlersSpan: null,\n    BaseServerSpan: null,\n    LoadComponentsSpan: null,\n    LogSpanAllowList: null,\n    MiddlewareSpan: null,\n    NextNodeServerSpan: null,\n    NextServerSpan: null,\n    NextVanillaSpanAllowlist: null,\n    NodeSpan: null,\n    RenderSpan: null,\n    ResolveMetadataSpan: null,\n    RouterSpan: null,\n    StartServerSpan: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    AppRenderSpan: function() {\n        return AppRenderSpan;\n    },\n    AppRouteRouteHandlersSpan: function() {\n        return AppRouteRouteHandlersSpan;\n    },\n    BaseServerSpan: function() {\n        return BaseServerSpan;\n    },\n    LoadComponentsSpan: function() {\n        return LoadComponentsSpan;\n    },\n    LogSpanAllowList: function() {\n        return LogSpanAllowList;\n    },\n    MiddlewareSpan: function() {\n        return MiddlewareSpan;\n    },\n    NextNodeServerSpan: function() {\n        return NextNodeServerSpan;\n    },\n    NextServerSpan: function() {\n        return NextServerSpan;\n    },\n    NextVanillaSpanAllowlist: function() {\n        return NextVanillaSpanAllowlist;\n    },\n    NodeSpan: function() {\n        return NodeSpan;\n    },\n    RenderSpan: function() {\n        return RenderSpan;\n    },\n    ResolveMetadataSpan: function() {\n        return ResolveMetadataSpan;\n    },\n    RouterSpan: function() {\n        return RouterSpan;\n    },\n    StartServerSpan: function() {\n        return StartServerSpan;\n    }\n});\nvar BaseServerSpan = /*#__PURE__*/ function(BaseServerSpan) {\n    BaseServerSpan[\"handleRequest\"] = \"BaseServer.handleRequest\";\n    BaseServerSpan[\"run\"] = \"BaseServer.run\";\n    BaseServerSpan[\"pipe\"] = \"BaseServer.pipe\";\n    BaseServerSpan[\"getStaticHTML\"] = \"BaseServer.getStaticHTML\";\n    BaseServerSpan[\"render\"] = \"BaseServer.render\";\n    BaseServerSpan[\"renderToResponseWithComponents\"] = \"BaseServer.renderToResponseWithComponents\";\n    BaseServerSpan[\"renderToResponse\"] = \"BaseServer.renderToResponse\";\n    BaseServerSpan[\"renderToHTML\"] = \"BaseServer.renderToHTML\";\n    BaseServerSpan[\"renderError\"] = \"BaseServer.renderError\";\n    BaseServerSpan[\"renderErrorToResponse\"] = \"BaseServer.renderErrorToResponse\";\n    BaseServerSpan[\"renderErrorToHTML\"] = \"BaseServer.renderErrorToHTML\";\n    BaseServerSpan[\"render404\"] = \"BaseServer.render404\";\n    return BaseServerSpan;\n}(BaseServerSpan || {});\nvar LoadComponentsSpan = /*#__PURE__*/ function(LoadComponentsSpan) {\n    LoadComponentsSpan[\"loadDefaultErrorComponents\"] = \"LoadComponents.loadDefaultErrorComponents\";\n    LoadComponentsSpan[\"loadComponents\"] = \"LoadComponents.loadComponents\";\n    return LoadComponentsSpan;\n}(LoadComponentsSpan || {});\nvar NextServerSpan = /*#__PURE__*/ function(NextServerSpan) {\n    NextServerSpan[\"getRequestHandler\"] = \"NextServer.getRequestHandler\";\n    NextServerSpan[\"getServer\"] = \"NextServer.getServer\";\n    NextServerSpan[\"getServerRequestHandler\"] = \"NextServer.getServerRequestHandler\";\n    NextServerSpan[\"createServer\"] = \"createServer.createServer\";\n    return NextServerSpan;\n}(NextServerSpan || {});\nvar NextNodeServerSpan = /*#__PURE__*/ function(NextNodeServerSpan) {\n    NextNodeServerSpan[\"compression\"] = \"NextNodeServer.compression\";\n    NextNodeServerSpan[\"getBuildId\"] = \"NextNodeServer.getBuildId\";\n    NextNodeServerSpan[\"createComponentTree\"] = \"NextNodeServer.createComponentTree\";\n    NextNodeServerSpan[\"clientComponentLoading\"] = \"NextNodeServer.clientComponentLoading\";\n    NextNodeServerSpan[\"getLayoutOrPageModule\"] = \"NextNodeServer.getLayoutOrPageModule\";\n    NextNodeServerSpan[\"generateStaticRoutes\"] = \"NextNodeServer.generateStaticRoutes\";\n    NextNodeServerSpan[\"generateFsStaticRoutes\"] = \"NextNodeServer.generateFsStaticRoutes\";\n    NextNodeServerSpan[\"generatePublicRoutes\"] = \"NextNodeServer.generatePublicRoutes\";\n    NextNodeServerSpan[\"generateImageRoutes\"] = \"NextNodeServer.generateImageRoutes.route\";\n    NextNodeServerSpan[\"sendRenderResult\"] = \"NextNodeServer.sendRenderResult\";\n    NextNodeServerSpan[\"proxyRequest\"] = \"NextNodeServer.proxyRequest\";\n    NextNodeServerSpan[\"runApi\"] = \"NextNodeServer.runApi\";\n    NextNodeServerSpan[\"render\"] = \"NextNodeServer.render\";\n    NextNodeServerSpan[\"renderHTML\"] = \"NextNodeServer.renderHTML\";\n    NextNodeServerSpan[\"imageOptimizer\"] = \"NextNodeServer.imageOptimizer\";\n    NextNodeServerSpan[\"getPagePath\"] = \"NextNodeServer.getPagePath\";\n    NextNodeServerSpan[\"getRoutesManifest\"] = \"NextNodeServer.getRoutesManifest\";\n    NextNodeServerSpan[\"findPageComponents\"] = \"NextNodeServer.findPageComponents\";\n    NextNodeServerSpan[\"getFontManifest\"] = \"NextNodeServer.getFontManifest\";\n    NextNodeServerSpan[\"getServerComponentManifest\"] = \"NextNodeServer.getServerComponentManifest\";\n    NextNodeServerSpan[\"getRequestHandler\"] = \"NextNodeServer.getRequestHandler\";\n    NextNodeServerSpan[\"renderToHTML\"] = \"NextNodeServer.renderToHTML\";\n    NextNodeServerSpan[\"renderError\"] = \"NextNodeServer.renderError\";\n    NextNodeServerSpan[\"renderErrorToHTML\"] = \"NextNodeServer.renderErrorToHTML\";\n    NextNodeServerSpan[\"render404\"] = \"NextNodeServer.render404\";\n    NextNodeServerSpan[\"startResponse\"] = \"NextNodeServer.startResponse\";\n    // nested inner span, does not require parent scope name\n    NextNodeServerSpan[\"route\"] = \"route\";\n    NextNodeServerSpan[\"onProxyReq\"] = \"onProxyReq\";\n    NextNodeServerSpan[\"apiResolver\"] = \"apiResolver\";\n    NextNodeServerSpan[\"internalFetch\"] = \"internalFetch\";\n    return NextNodeServerSpan;\n}(NextNodeServerSpan || {});\nvar StartServerSpan = /*#__PURE__*/ function(StartServerSpan) {\n    StartServerSpan[\"startServer\"] = \"startServer.startServer\";\n    return StartServerSpan;\n}(StartServerSpan || {});\nvar RenderSpan = /*#__PURE__*/ function(RenderSpan) {\n    RenderSpan[\"getServerSideProps\"] = \"Render.getServerSideProps\";\n    RenderSpan[\"getStaticProps\"] = \"Render.getStaticProps\";\n    RenderSpan[\"renderToString\"] = \"Render.renderToString\";\n    RenderSpan[\"renderDocument\"] = \"Render.renderDocument\";\n    RenderSpan[\"createBodyResult\"] = \"Render.createBodyResult\";\n    return RenderSpan;\n}(RenderSpan || {});\nvar AppRenderSpan = /*#__PURE__*/ function(AppRenderSpan) {\n    AppRenderSpan[\"renderToString\"] = \"AppRender.renderToString\";\n    AppRenderSpan[\"renderToReadableStream\"] = \"AppRender.renderToReadableStream\";\n    AppRenderSpan[\"getBodyResult\"] = \"AppRender.getBodyResult\";\n    AppRenderSpan[\"fetch\"] = \"AppRender.fetch\";\n    return AppRenderSpan;\n}(AppRenderSpan || {});\nvar RouterSpan = /*#__PURE__*/ function(RouterSpan) {\n    RouterSpan[\"executeRoute\"] = \"Router.executeRoute\";\n    return RouterSpan;\n}(RouterSpan || {});\nvar NodeSpan = /*#__PURE__*/ function(NodeSpan) {\n    NodeSpan[\"runHandler\"] = \"Node.runHandler\";\n    return NodeSpan;\n}(NodeSpan || {});\nvar AppRouteRouteHandlersSpan = /*#__PURE__*/ function(AppRouteRouteHandlersSpan) {\n    AppRouteRouteHandlersSpan[\"runHandler\"] = \"AppRouteRouteHandlers.runHandler\";\n    return AppRouteRouteHandlersSpan;\n}(AppRouteRouteHandlersSpan || {});\nvar ResolveMetadataSpan = /*#__PURE__*/ function(ResolveMetadataSpan) {\n    ResolveMetadataSpan[\"generateMetadata\"] = \"ResolveMetadata.generateMetadata\";\n    ResolveMetadataSpan[\"generateViewport\"] = \"ResolveMetadata.generateViewport\";\n    return ResolveMetadataSpan;\n}(ResolveMetadataSpan || {});\nvar MiddlewareSpan = /*#__PURE__*/ function(MiddlewareSpan) {\n    MiddlewareSpan[\"execute\"] = \"Middleware.execute\";\n    return MiddlewareSpan;\n}(MiddlewareSpan || {});\nconst NextVanillaSpanAllowlist = [\n    \"Middleware.execute\",\n    \"BaseServer.handleRequest\",\n    \"Render.getServerSideProps\",\n    \"Render.getStaticProps\",\n    \"AppRender.fetch\",\n    \"AppRender.getBodyResult\",\n    \"Render.renderDocument\",\n    \"Node.runHandler\",\n    \"AppRouteRouteHandlers.runHandler\",\n    \"ResolveMetadata.generateMetadata\",\n    \"ResolveMetadata.generateViewport\",\n    \"NextNodeServer.createComponentTree\",\n    \"NextNodeServer.findPageComponents\",\n    \"NextNodeServer.getLayoutOrPageModule\",\n    \"NextNodeServer.startResponse\",\n    \"NextNodeServer.clientComponentLoading\"\n];\nconst LogSpanAllowList = [\n    \"NextNodeServer.findPageComponents\",\n    \"NextNodeServer.createComponentTree\",\n    \"NextNodeServer.clientComponentLoading\"\n];\n\n//# sourceMappingURL=constants.js.map", "import { ensureLeadingSlash } from './ensure-leading-slash'\nimport { isDynamicRoute } from '../router/utils'\nimport { NormalizeError } from '../utils'\n\n/**\n * Takes a page and transforms it into its file counterpart ensuring that the\n * output is normalized. Note this function is not idempotent because a page\n * `/index` can be referencing `/index/index.js` and `/index/index` could be\n * referencing `/index/index/index.js`. Examples:\n *  - `/` -> `/index`\n *  - `/index/foo` -> `/index/index/foo`\n *  - `/index` -> `/index/index`\n */\nexport function normalizePagePath(page: string): string {\n  const normalized =\n    /^\\/index(\\/|$)/.test(page) && !isDynamicRoute(page)\n      ? `/index${page}`\n      : page === '/'\n        ? '/index'\n        : ensureLeadingSlash(page)\n\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    const { posix } = require('path')\n    const resolvedPage = posix.normalize(normalized)\n    if (resolvedPage !== normalized) {\n      throw new NormalizeError(\n        `Requested and resolved page mismatch: ${normalized} ${resolvedPage}`\n      )\n    }\n  }\n\n  return normalized\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"getTracedMetadata\", {\n    enumerable: true,\n    get: function() {\n        return getTracedMetadata;\n    }\n});\nfunction getTracedMetadata(traceData, clientTraceMetadata) {\n    if (!clientTraceMetadata) return undefined;\n    return traceData.filter(({ key })=>clientTraceMetadata.includes(key));\n}\n\n//# sourceMappingURL=utils.js.map", "import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n", "/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ \"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"default\", {\n    enumerable: true,\n    get: function() {\n        return prettyBytes;\n    }\n});\nconst UNITS = [\n    'B',\n    'kB',\n    'MB',\n    'GB',\n    'TB',\n    'PB',\n    'EB',\n    'ZB',\n    'YB'\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === 'string') {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw Object.defineProperty(new TypeError(`Expected a finite number, got ${typeof number}: ${number}`), \"__NEXT_ERROR_CODE\", {\n            value: \"E572\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return ' 0 B';\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? '-' : options.signed ? '+' : '';\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + ' B';\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + ' ' + unit;\n}\n\n//# sourceMappingURL=pretty-bytes.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    cleanAmpPath: null,\n    debounce: null,\n    isBlockedPage: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    cleanAmpPath: function() {\n        return cleanAmpPath;\n    },\n    debounce: function() {\n        return debounce;\n    },\n    isBlockedPage: function() {\n        return isBlockedPage;\n    }\n});\nconst _constants = require(\"../shared/lib/constants\");\nfunction isBlockedPage(page) {\n    return _constants.BLOCKED_PAGES.includes(page);\n}\nfunction cleanAmpPath(pathname) {\n    if (pathname.match(/\\?amp=(y|yes|true|1)/)) {\n        pathname = pathname.replace(/\\?amp=(y|yes|true|1)&?/, '?');\n    }\n    if (pathname.match(/&amp=(y|yes|true|1)/)) {\n        pathname = pathname.replace(/&amp=(y|yes|true|1)/, '');\n    }\n    pathname = pathname.replace(/\\?$/, '');\n    return pathname;\n}\nfunction debounce(fn, ms, maxWait = Infinity) {\n    let timeoutId;\n    // The time the debouncing function was first called during this debounce queue.\n    let startTime = 0;\n    // The time the debouncing function was last called.\n    let lastCall = 0;\n    // The arguments and this context of the last call to the debouncing function.\n    let args, context;\n    // A helper used to that either invokes the debounced function, or\n    // reschedules the timer if a more recent call was made.\n    function run() {\n        const now = Date.now();\n        const diff = lastCall + ms - now;\n        // If the diff is non-positive, then we've waited at least `ms`\n        // milliseconds since the last call. Or if we've waited for longer than the\n        // max wait time, we must call the debounced function.\n        if (diff <= 0 || startTime + maxWait >= now) {\n            // It's important to clear the timeout id before invoking the debounced\n            // function, in case the function calls the debouncing function again.\n            timeoutId = undefined;\n            fn.apply(context, args);\n        } else {\n            // Else, a new call was made after the original timer was scheduled. We\n            // didn't clear the timeout (doing so is very slow), so now we need to\n            // reschedule the timer for the time difference.\n            timeoutId = setTimeout(run, diff);\n        }\n    }\n    return function(...passedArgs) {\n        // The arguments and this context of the most recent call are saved so the\n        // debounced function can be invoked with them later.\n        args = passedArgs;\n        context = this;\n        // Instead of constantly clearing and scheduling a timer, we record the\n        // time of the last call. If a second call comes in before the timer fires,\n        // then we'll reschedule in the run function. Doing this is considerably\n        // faster.\n        lastCall = Date.now();\n        // Only schedule a new timer if we're not currently waiting.\n        if (timeoutId === undefined) {\n            startTime = lastCall;\n            timeoutId = setTimeout(run, ms);\n        }\n    };\n}\n\n//# sourceMappingURL=utils.js.map"], "names": ["isThenable", "promise", "then", "normalizePathSep", "path", "replace", "INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "undefined", "split", "find", "segment", "startsWith", "m", "interceptingRoute", "marker", "interceptedRoute", "normalizeAppPath", "slice", "concat", "join", "splitInterceptingRoute", "length", "normalizeRscURL", "route", "ensureLeadingSlash", "reduce", "pathname", "index", "segments", "isGroupSegment", "url", "getObjectClassLabel", "value", "Object", "prototype", "toString", "call", "isPlainObject", "getPrototypeOf", "hasOwnProperty", "getSortedRouteObjects", "getSortedRoutes", "UrlNode", "insert", "url<PERSON><PERSON>", "_insert", "filter", "Boolean", "smoosh", "_smoosh", "prefix", "childrenPaths", "children", "keys", "sort", "slug<PERSON><PERSON>", "splice", "indexOf", "restSlugName", "optionalRestSlugName", "routes", "map", "get", "c", "prev", "curr", "push", "placeholder", "r", "unshift", "url<PERSON><PERSON>s", "slug<PERSON><PERSON><PERSON>", "isCatchAll", "nextSegment", "endsWith", "segmentName", "isOptional", "Error", "substring", "handleSlug", "previousSlug", "nextSlug", "for<PERSON>ach", "slug", "has", "set", "Map", "normalizedPages", "root", "pagePath", "objects", "getter", "indexes", "pathnames", "i", "module", "exports", "MODERN_BROWSERSLIST_TARGET", "denormalizePagePath", "page", "_page", "isDynamicRoute", "Head", "Html", "Main", "NextScript", "Document", "largePageDataWarnings", "Set", "getDocumentFiles", "buildManifest", "inAmpMode", "sharedFiles", "getPageFiles", "pageFiles", "process", "allFiles", "getPolyfillScripts", "context", "props", "assetPrefix", "assetQueryString", "disableOptimizedLoading", "crossOrigin", "polyfillFiles", "polyfill", "script", "defer", "nonce", "noModule", "src", "encodeURIPath", "AmpStyles", "styles", "curStyles", "Array", "isArray", "hasStyles", "el", "dangerouslySetInnerHTML", "__html", "child", "style", "amp-custom", "getDynamicChunks", "files", "dynamicImports", "isDevelopment", "file", "includes", "async", "getScripts", "lowPriorityScripts", "lowPriorityFiles", "getPreNextScripts", "<PERSON><PERSON><PERSON><PERSON>", "webWorkerScripts", "getPreNextWorkerScripts", "nextScriptWorkers", "partytownSnippet", "__non_webpack_require__", "userDefinedConfig", "data-partytown-config", "data-partytown", "worker", "strategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scriptProps", "srcProps", "type", "key", "data-nscript", "err", "isError", "code", "console", "warn", "message", "beforeInteractiveScripts", "beforeInteractive", "React", "Component", "contextType", "HtmlContext", "dynamicCssManifest", "optimizeCss", "cssFiles", "f", "unmanagedFiles", "localDynamicCssFiles", "from", "existing", "cssLinkElements", "isSharedFile", "isUnmanagedFile", "isFileInDynamicCssManifest", "link", "rel", "href", "as", "data-n-g", "data-n-p", "getPreloadDynamicChunks", "getPreloadMainLinks", "preloadFiles", "getBeforeInteractiveInlineScripts", "html", "id", "render", "ampPath", "hybridAmp", "canonicalBase", "__NEXT_DATA__", "dangerousAsPath", "headTags", "unstable_runtimeJS", "unstable_JsPreload", "nextFontManifest", "disableRuntimeJS", "disableJsPreload", "doc<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "head", "cssPreloads", "otherHeadElements", "strictNextHead", "cloneElement", "Children", "toArray", "hasAmphtmlRel", "hasCanonicalRel", "badProp", "name", "prop", "nextFontLinkTags", "getNextFontLinkTags", "preconnect", "preload", "appFontsEntry", "pages", "pageFontsEntry", "preloadedFontFiles", "preconnectToSelf", "data-next-font", "pagesUsingSizeAdjust", "ext", "exec", "fontFile", "traceMetaTags", "tracingMetadata", "getTracedMetadata", "getTracer", "getTracePropagationData", "experimentalClientTraceMetadata", "meta", "content", "getHeadHTMLProps", "restProps", "data-next-hide-fouc", "data-ampdevmode", "noscript", "count", "require", "amp-boilerplate", "<PERSON><PERSON><PERSON>", "getAmp<PERSON><PERSON>", "getCssLinks", "data-n-css", "createElement", "Fragment", "getInlineScriptSource", "largePageDataBytes", "data", "JSON", "stringify", "htmlEscapeJsonString", "bytes", "<PERSON><PERSON><PERSON>", "prettyBytes", "add", "devFiles", "locale", "useHtmlContext", "handleDocumentScriptLoaderItems", "scriptLoaderItems", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "combinedChildren", "__nextScript", "lang", "amp", "env", "NODE_ENV", "next-js-internal-body-render-target", "getInitialProps", "ctx", "defaultGetInitialProps", "body", "NEXT_BUILTIN_DOCUMENT", "InternalFunctionDocument", "TEST_ROUTE", "TEST_STRICT_ROUTE", "strict", "test", "APP_BUILD_MANIFEST", "APP_CLIENT_INTERNALS", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "BARREL_OPTIMIZATION_PREFIX", "BLOCKED_PAGES", "BUILD_ID_FILE", "BUILD_MANIFEST", "CLIENT_PUBLIC_FILES_PATH", "CLIENT_REFERENCE_MANIFEST", "CLIENT_STATIC_FILES_PATH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "COMPILER_INDEXES", "COMPILER_NAMES", "CONFIG_FILES", "DEFAULT_RUNTIME_WEBPACK", "DEFAULT_SANS_SERIF_FONT", "DEFAULT_SERIF_FONT", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "DEV_CLIENT_PAGES_MANIFEST", "DYNAMIC_CSS_MANIFEST", "EDGE_RUNTIME_WEBPACK", "EDGE_UNSUPPORTED_NODE_APIS", "EXPORT_DETAIL", "EXPORT_MARKER", "FUNCTIONS_CONFIG_MANIFEST", "IMAGES_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "NEXT_FONT_MANIFEST", "PAGES_MANIFEST", "PHASE_DEVELOPMENT_SERVER", "PHASE_EXPORT", "PHASE_INFO", "PHASE_PRODUCTION_BUILD", "PHASE_PRODUCTION_SERVER", "PHASE_TEST", "PRERENDER_MANIFEST", "REACT_LOADABLE_MANIFEST", "ROUTES_MANIFEST", "RSC_MODULE_TYPES", "SERVER_DIRECTORY", "SERVER_FILES_MANIFEST", "SERVER_PROPS_ID", "SERVER_REFERENCE_MANIFEST", "STATIC_PROPS_ID", "STATIC_STATUS_PAGES", "STRING_LITERAL_DROP_BUNDLE", "SUBRESOURCE_INTEGRITY_MANIFEST", "SYSTEM_ENTRYPOINTS", "TRACE_OUTPUT_VERSION", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "TURBO_TRACE_DEFAULT_MEMORY_LIMIT", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "WEBPACK_STATS", "client", "server", "edgeServer", "Symbol", "xAvgCharWidth", "azAvgWidth", "unitsPerEm", "encodeURIComponent", "p", "isParallelRouteSegment", "addSearchParamsIfPageSegment", "searchParams", "isPageSegment", "PAGE_SEGMENT_KEY", "stringified<PERSON><PERSON>y", "DEFAULT_SEGMENT_KEY", "normalizePagePath", "normalized", "posix", "resolvedPage", "normalize", "NormalizeError", "DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "result", "used", "args", "ABSOLUTE_URL_REGEX", "protocol", "hostname", "port", "window", "location", "origin", "displayName", "res", "finished", "headersSent", "urlParts", "App", "performance", "every", "method", "constructor", "error", "stack"], "sourceRoot": ""}