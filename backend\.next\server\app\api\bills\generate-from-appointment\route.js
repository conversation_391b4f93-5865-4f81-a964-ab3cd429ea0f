(()=>{var e={};e.id=5467,e.ids=[5467],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34869:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70293:(e,t,r)=>{"use strict";e.exports=r(44870)},78669:()=>{throw Error("Module parse failed: Identifier 'discountAmount' has already been declared (41:14)\nFile was processed with these loaders:\n * ./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-flight-loader/index.js\n * ./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_9158fa786a82beaa1f012e336be7ea10/node_modules/next/dist/build/webpack/loaders/next-swc-loader.js\nYou may need an additional loader to handle the result of these loaders.\n|         // Calculate bill amounts\n|         const subtotal = appointment.price || 0;\n>         const discountAmount = 0; // Can be customized\n|         const taxAmount = 0; // Can be customized\n|         const totalAmount = subtotal + taxAmount - discountAmount;")},81506:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>l,routeModule:()=>i,serverHooks:()=>p,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>u});var n=r(70293),o=r(32498),a=r(83889),s=r(78669);let i=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/bills/generate-from-appointment/route",pathname:"/api/bills/generate-from-appointment",filename:"route",bundlePath:"app/api/bills/generate-from-appointment/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\bills\\generate-from-appointment\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:u,serverHooks:p}=i;function l(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:u})}},93077:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[3889],()=>r(81506));module.exports=n})();