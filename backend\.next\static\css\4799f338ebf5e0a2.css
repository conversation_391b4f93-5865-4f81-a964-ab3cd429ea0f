@layer payload-default{}@layer payload-default{}@layer payload-default{.field-diff__locale-label{background:var(--theme-elevation-100);border-radius:var(--style-radius-s);padding:calc(var(--base)*.2)}[dir=ltr] .field-diff__locale-label{margin-right:calc(var(--base)*.25)}[dir=rtl] .field-diff__locale-label{margin-left:calc(var(--base)*.25)}.field-diff-container{position:relative}.field-diff-content{display:grid;grid-template-columns:calc(50% - 10px) calc(50% - 10px);grid-gap:20px;gap:20px;background:var(--theme-elevation-50);padding:10px}.field-diff-label{margin-bottom:calc(var(--base)*.35);font-weight:600;display:flex;flex-direction:row;height:100%;align-items:center;line-height:normal}.folder-edit-field{display:none}.edit-many-bulk-uploads__main .folder-edit-field,.edit-many__main .folder-edit-field{display:initial}:root{--diff-delete-pill-bg:var(--theme-error-200);--diff-delete-pill-color:var(--theme-error-600);--diff-delete-pill-border:var(--theme-error-400);--diff-delete-parent-bg:var(--theme-error-100);--diff-delete-parent-color:var(--theme-error-800);--diff-delete-link-color:var(--theme-error-600);--diff-create-pill-bg:var(--theme-success-200);--diff-create-pill-color:var(--theme-success-600);--diff-create-pill-border:var(--theme-success-400);--diff-create-parent-bg:var(--theme-success-100);--diff-create-parent-color:var(--theme-success-800);--diff-create-link-color:var(--theme-success-600)}html[data-theme=dark]{--diff-delete-pill-bg:var(--theme-error-200);--diff-delete-pill-color:var(--theme-error-650);--diff-delete-pill-border:var(--theme-error-400);--diff-delete-parent-bg:var(--theme-error-100);--diff-delete-parent-color:var(--theme-error-900);--diff-delete-link-color:var(--theme-error-750);--diff-create-pill-bg:var(--theme-success-200);--diff-create-pill-color:var(--theme-success-650);--diff-create-pill-border:var(--theme-success-400);--diff-create-parent-bg:var(--theme-success-100);--diff-create-parent-color:var(--theme-success-900);--diff-create-link-color:var(--theme-success-750)}.html-diff{font-size:14px;letter-spacing:.02em}.html-diff-no-value{color:var(--theme-elevation-400)}.html-diff pre{margin-top:0;margin-bottom:0}}@layer payload-default{}@layer payload-default{}@layer payload-default{}@layer payload-default{}@layer payload-default{}@layer payload-default{}@layer payload-default{}@layer payload-default{.html-diff blockquote:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h1:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h2:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h3:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h4:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h5:not([data-enable-match=false]):has([data-match-type=create]),.html-diff h6:not([data-enable-match=false]):has([data-match-type=create]),.html-diff p:not([data-enable-match=false]):has([data-match-type=create]),.html-diff pre:not([data-enable-match=false]):has([data-match-type=create]){position:relative;z-index:1}.html-diff blockquote:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h1:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h2:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h3:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h4:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h5:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff h6:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff p:not([data-enable-match=false]):has([data-match-type=create]):before,.html-diff pre:not([data-enable-match=false]):has([data-match-type=create]):before{content:"";position:absolute;top:-10px;bottom:-10px;left:-10px;right:-10px;display:block;background-color:var(--diff-create-parent-bg);color:var(--diff-create-parent-color);z-index:-1}.html-diff blockquote:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h1:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h2:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h3:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h4:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h5:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff h6:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff p:not([data-enable-match=false]):has([data-match-type=delete]),.html-diff pre:not([data-enable-match=false]):has([data-match-type=delete]){position:relative;z-index:1}.html-diff blockquote:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h1:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h2:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h3:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h4:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h5:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff h6:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff p:not([data-enable-match=false]):has([data-match-type=delete]):before,.html-diff pre:not([data-enable-match=false]):has([data-match-type=delete]):before{content:"";position:absolute;top:-10px;bottom:-10px;left:-10px;right:-10px;display:block;background-color:var(--diff-delete-parent-bg);color:var(--diff-delete-parent-color);z-index:-1}.html-diff li:not([data-enable-match=false]):has([data-match-type=create]){position:relative;z-index:1}.html-diff li:not([data-enable-match=false]):has([data-match-type=create]):before{content:"";position:absolute;top:0;bottom:0;left:-10px;right:-10px;display:block;background-color:var(--diff-create-parent-bg);color:var(--diff-create-parent-color);z-index:-1}.html-diff li:not([data-enable-match=false]):has([data-match-type=delete]){position:relative;z-index:1}.html-diff li:not([data-enable-match=false]):has([data-match-type=delete]):before{content:"";position:absolute;top:0;bottom:0;left:-10px;right:-10px;display:block;background-color:var(--diff-delete-parent-bg);color:var(--diff-delete-parent-color);z-index:-1}.html-diff li::marker{color:var(--theme-text)}.html-diff [data-match-type=delete]:not([data-enable-match=false]):not(:is([data-enable-match=false] *)){color:var(--diff-delete-pill-color);-webkit-text-decoration-color:var(--diff-delete-pill-color);text-decoration-color:var(--diff-delete-pill-color);-webkit-text-decoration-line:line-through;text-decoration-line:line-through;background-color:var(--diff-delete-pill-bg);border-radius:4px;text-decoration-thickness:1px}.html-diff a[data-match-type=delete] :not([data-enable-match=false]) :not(:is([data-enable-match=false] *)){color:var(--diff-delete-link-color)}.html-diff a[data-match-type=create]:not(img) :not([data-enable-match=false]) :not(:is([data-enable-match=false] *)){color:var(--diff-create-link-color)}.html-diff [data-match-type=create]:not(img):not([data-enable-match=false]):not(:is([data-enable-match=false] *)){background-color:var(--diff-create-pill-bg);color:var(--diff-create-pill-color);border-radius:4px}.html-diff .html-diff-create-inline-wrapper,.html-diff .html-diff-delete-inline-wrapper{display:inline-flex}.html-diff .html-diff-create-block-wrapper,.html-diff .html-diff-delete-block-wrapper{display:flex}.html-diff .html-diff-create-block-wrapper,.html-diff .html-diff-create-inline-wrapper,.html-diff .html-diff-delete-block-wrapper,.html-diff .html-diff-delete-inline-wrapper{position:relative;align-items:center;flex-direction:row}.html-diff .html-diff-create-block-wrapper:after,.html-diff .html-diff-create-inline-wrapper:after,.html-diff .html-diff-delete-block-wrapper:after,.html-diff .html-diff-delete-inline-wrapper:after{position:absolute;top:0;left:0;display:block;width:100%;height:100%;content:""}.icon--check{height:1.5384615385rem;width:1.5384615385rem}.icon--check .stroke{fill:none;stroke:currentColor;stroke-width:2px}}@layer payload-default{}@layer payload-default{}@layer payload-default{}