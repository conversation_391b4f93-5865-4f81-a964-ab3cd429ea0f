{"version": 3, "file": "8774.js", "mappings": "6eAEE,MAAe,SAIjB,EAHuB,kCAKvB,IAJqB,yBAAmB,mCAIxC,EACA,CAAK,4dCUD,sBAAsB,slBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAIH,CALS,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACpC,MAD2B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,GAAG,CACnB,aAAa,CAAE,WAAW,mBAC1B,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CA7BEA,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,OCvEtB,UDgF8B,CChF9B,GACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,UACA,sJEDe,SAASC,EAAU,kBAChCC,CAAgB,UAChBC,CAAQ,CAIT,EAEC,GAAM,eACJC,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACZ,MAAO,+BACH,UAACC,EAAAA,CAAmBA,CAAAA,CAACC,aAAcL,EAAkBM,sBAAoB,sBAAsBC,0BAAwB,yBACrH,UAACC,EAAAA,EAAaA,CAAAA,CAACC,WAAY,CAC3BC,UAA6B,WAASC,EAAAA,IAAIA,MAAGC,CAC/C,EAAGN,sBAAoB,gBAAgBC,0BAAwB,yBAC1DN,OAIX,mBC3BA,4CAAwQ,CAExQ,qCAAiQ,CAEjQ,uCAAgK,CAEhK,uCAAkL,CAElL,uCAAyJ,2FCJzJ,IAAMY,EAAU,CAAC,CACf,GAAGC,EACU,IACb,GAAM,CACJC,QAAQ,QAAQ,CACjB,CAAGZ,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACZ,MAAO,UAACa,EAAAA,OAAMA,CAAAA,CAACD,MAAOA,EAAgCE,UAAU,gBAAgBC,MAAO,CACrF,cAAe,iBACf,gBAAiB,4BACjB,kBAAmB,eACrB,EAA2B,GAAGJ,CAAK,CAAER,sBAAoB,SAASa,wBAAsB,UAAUZ,0BAAwB,cAC5H,4ECZO,SAASa,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,mBCLA,4CAA4O,CAE5O,4CAA+O,CAE/O,4CAA+O,CAE/O,4CAAqQ,CAErQ,4CAA8O,CAE9O,4CAAyP,CAEzP,4CAA4P,CAE5P,4CAA6P,kBCd7P,uCAAuH,gFCGxG,SAASG,EAAc,UACpCvB,CAAQ,CACR,GAAGa,EACgB,EACnB,MAAO,UAACW,EAAAA,CAAkBA,CAAAA,CAAE,GAAGX,CAAK,CAAER,sBAAoB,qBAAqBa,wBAAsB,gBAAgBZ,0BAAwB,8BAAsBN,GACrK,mBCRA,sCAAoH,6GCKrG,SAASyB,EAAY,OAClCC,CAAK,CAKN,EAIC,MAAO,UAACC,OAAAA,CAAKT,wBAAsB,cAAcZ,0BAAwB,4BACrE,UAACsB,OAAAA,UAKC,UAACC,IAASA,CAACC,WAAY,EAAGzB,sBAAoB,UAApCwB,EAAgDvB,0BAAwB,wBAG1F,8HCpBA,IAAMyB,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,8bAA+b,CACxdC,SAAU,CACRC,QAAS,CACPC,QAAS,mEACTC,YAAa,8JACbC,QAAS,wIACTC,UAAW,yEACXC,MAAO,uEACPC,KAAM,iDACR,EACAC,KAAM,CACJN,QAAS,gCACTO,GAAI,gDACJC,GAAI,uCACJC,KAAM,QACR,CACF,EACAC,gBAAiB,CACfX,QAAS,UACTO,KAAM,SACR,CACF,GACA,SAASK,EAAO,WACd9B,CAAS,SACTkB,CAAO,CACPO,MAAI,SACJM,GAAU,CAAK,CACf,GAAGlC,EAGJ,EACC,IAAMmC,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,SAC9B,MAAO,UAACD,EAAAA,CAAKE,YAAU,SAASlC,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACY,EAAe,SAC3DG,OACAO,YACAzB,CACF,IAAM,GAAGH,CAAK,CAAER,sBAAoB,OAAOa,wBAAsB,SAASZ,0BAAwB,cACpG,aCzCA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,6qBCPO,MAA2C,2BAAqB,8CAA+C,YAAU,QAAa,kBAAgB,2HCG9I,SAAS6C,IACtB,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACxB,MAAO,WAACC,MAAAA,CAAItC,UAAU,4GAA4GE,wBAAsB,WAAWZ,0BAAwB,0BACvL,UAACiD,OAAAA,CAAKvC,UAAU,gIAAuH,QAGvI,UAACwC,KAAAA,CAAGxC,UAAU,gDAAuC,wBAGrD,UAACyC,IAAAA,UAAE,yEAIH,WAACH,MAAAA,CAAItC,UAAU,2CACb,UAAC8B,EAAAA,CAAMA,CAAAA,CAACY,QAAS,IAAMN,EAAOO,IAAI,GAAIzB,QAAQ,UAAUO,KAAK,KAAKpC,sBAAoB,SAASC,0BAAwB,yBAAgB,YAGvI,UAACwC,EAAAA,CAAMA,CAAAA,CAACY,QAAS,IAAMN,EAAOQ,IAAI,CAAC,cAAe1B,QAAQ,QAAQO,KAAK,KAAKpC,sBAAoB,SAASC,0BAAwB,yBAAgB,sBAKzJ,mBC1BA,4CAA4O,CAE5O,4CAA+O,CAE/O,2CAA+O,CAE/O,2CAAqQ,CAErQ,4CAA8O,CAE9O,4CAAyP,CAEzP,4CAA4P,CAE5P,4CAA6P,ynCCd7P,uCAAuH,kBCAvH,uCAAoH,mClBmBhH,sBAAsB,oSmBsBnB,IAAMuD,EAAgB1C,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAC7B2C,IAAAA,QAAiB,CACjBC,IAAAA,QAAiB,CACjBC,IAAAA,QAAuB,CACvBC,IAAAA,QAAqB,CACrBC,IAAAA,QAAoB,CACpBC,IAAAA,QAAkB,EAClB,0FCrCF,IAAMC,EAAoB,CACxBC,KAAO,SADiB,EAExB3D,IAAM,UACR,EACa4D,EAAqB,CAChCC,KAAO,UACPC,WAAa,gCACf,EACaC,EAAqB,CAChCC,KADgC,KAChCA,CAAYN,EAAkBC,KAAAA,EAEjB,eAAeM,EAAW,QAAXA,EAC5B3E,CAAQ,CAGT,EACC,IAAM4E,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpB9E,EAAmB6E,EAAYE,GAAG,CAAC,KAAhBF,GAAAA,MAAiCG,CAAAA,EAAAA,KAAAA,CACpDC,EAAWjF,GAAkBkF,GAA7BD,KAAsC,YAC5C,MAAOE,CAAAA,EAAAA,EAAAA,IAAAA,CAACvD,CAAAA,MAAAA,CAAAA,CAAKwD,IAAK,SAAQC,wBAAwB,IAAClE,uBAAsB,cAAaZ,yBAAwB,wBAC1G+E,CAAAA,EAAAA,EAAAA,GAAAA,CAACC,CAAAA,MAAAA,CAAAA,CACC,SAAAD,CAAAA,EAAAA,EAAAA,GAAAA,CAACE,CAAAA,QAAAA,CAAAA,CAAOC,uBAAyB,EACjCC,MAAAA,CAAQ;;;8FAG8E,EAAErB,EAAkB1D,IAAI,CAAC,UAAvB0D;;;YAGpF,OAGNc,CAAAA,EAAAA,EAAAA,IAAAA,CAACtD,CAAAA,MAAAA,CAAAA,CAAKZ,SAAWG,CAAAA,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG,qEAAuEpB,CAAAA,EAAmB,CAAC,MAAM,EAAEA,EAAAA,CAAkB,CAAG,CAAjDA,EAAqDiF,EAAW,OAApCjF,QAAqD,EAAI8D,CAAAA,aAAAA,CAC9KwB,EAAAA,EAAAA,GAAAA,CAACK,CAAAA,IAAAA,CAAcC,UAAdD,CAA2B,IAAOrF,qBAAoB,iBAAgBC,yBAAwB,gBAC/F+E,CAAAA,EAAAA,EAAAA,GAAAA,CAACO,CAAAA,EAAAA,WAAAA,CAAAA,CAAYvF,qBAAoB,eAAcC,yBAAwB,cACrE,SAAA+E,CAAAA,EAAAA,EAAAA,GAAAA,CAAC9D,CAAAA,EAAAA,OAAAA,CAAAA,CAAcsE,SAAU,SAAQC,YAAa,UAASC,YAAY,IAACC,yBAAyB,IAACC,iBAAiB,IAAC5F,qBAAoB,iBAAgBC,yBAAwB,cAC1K,SAAA4E,CAAAA,EAAAA,EAAAA,IAAAA,CAACpF,CAAAA,EAAAA,OAAAA,CAAAA,CAAUC,gBAAkBA,CAAAA,EAA4BM,cAA5BN,OAAgD,aAAYO,yBAAwB,wBAC/G+E,CAAAA,EAAAA,EAAAA,GAAAA,CAACzE,CAAAA,EAAAA,OAAAA,CAAAA,CAAQP,qBAAoB,WAAUC,yBAAwB,gBAC9DN,cAMf,CpB9CA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OASN,EATe,IASc,KAAK,CAPZH,EAO8B,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CACrC,IAD0C,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,EAAI,OACtE,EAD+E,GAC5C,OAAO,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAE9E,CAAO,MAAQ,CAAC,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,GAAG,CACnB,aAAa,CAAE,QAAQ,mBACvB,EACA,aAAa,EADI,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,0FM7EvB,SAASsB,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,CAEO,SAAS8E,EACdC,CAAa,CACbC,EAGI,CAAC,CAAC,EAEN,GAAM,UAAEC,EAAW,CAAC,UAAEC,EAAW,QAAQ,CAAE,CAAGF,EAI9C,GAAc,IAAVD,EAAa,MAAO,SACxB,IAAMI,EAAIC,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACP,GAASK,KAAKE,GAAG,CAAC,OAChD,MAAO,GAAG,CAACP,EAAQK,KAAKG,GAAG,CAAC,KAAMJ,EAAAA,CAAC,CAAGK,OAAO,CAACP,GAAU,CAAC,EAC1C,aAAbC,EACKO,CALgB,QAAS,MAAO,MAAO,MAAO,MAAM,CAKtCN,EAAE,EAAI,SANb,CAAC,QAAS,KAAM,KAAM,KAAM,KAO9B,CAACA,EAAE,EAAI,SAErB,CAGO,SAASO,EAAeC,CAAU,EACvC,OAAO,IAAIC,KAAKC,cAAc,CAAC,QAAS,CACtCC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,UACRC,QAAQ,CACV,GAAGC,MAAM,CAACT,EACZ,CAkBO,SAASU,EAAmBV,CAAU,EAE3C,IAAMW,EAAgBlB,KAAKC,KAAK,CAAC,CADrB,IAAIkB,OACsBC,OAAO,GAAKb,EAAKa,OAAO,GAAC,CAAK,KAEpE,GAAIF,EAAgB,GAClB,CADsB,KACf,KAGT,IAAMG,EAAgBrB,KAAKC,KAAK,CAACiB,EAAgB,IACjD,GAAIG,EAAgB,GAClB,CADsB,KACf,GAAGA,EAAc,GAAG,CAAC,CAG9B,IAAMC,EAActB,KAAKC,KAAK,CAACoB,EAAgB,IAC/C,GAAIC,EAAc,GAChB,CADoB,KACb,GAAGA,EAAY,GAAG,CAAC,CAG5B,IAAMC,EAAavB,KAAKC,KAAK,CAACqB,EAAc,IAC5C,GAAIC,EAAa,EACf,CADkB,KACX,GAAGA,EAAW,EAAE,CAAC,CAG1B,IAAMC,EAAcxB,KAAKC,KAAK,CAACsB,EAAa,GAC5C,GAAIC,EAAc,EAChB,CADmB,KACZ,GAAGA,EAAY,EAAE,CAAC,CAG3B,IAAMC,EAAezB,KAAKC,KAAK,CAACsB,EAAa,IAC7C,GAAIE,EAAe,GACjB,CADqB,KACd,GAAGA,EAAa,GAAG,CAAC,CAG7B,IAAMC,EAAc1B,KAAKC,KAAK,CAACsB,EAAa,KAC5C,MAAO,GAAGG,EAAY,EAAE,CAAC,CAIpB,SAASC,EAAUC,CAAsB,EAE9C,MAAOC,CADwB,UAAnB,OAAOD,EAAuB,IAAIT,KAAKS,GAAWA,CAAAA,EACjD,IAAIT,IACnB,mBehGA,4CAAwQ,CAExQ,uCAAiQ,CAEjQ,uCAAgK,CAEhK,uCAAkL,CAElL,sCAAyJ,+FEKzJ,IAAMW,EAAeC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,MAA+B5H,GAC1D,SAASR,EAAoB,UAClCH,CAAQ,cACRI,CAAY,CAIb,EACC,GAAM,CAACoI,EAAaC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAAMtI,GAjBzC,WA4BpB,EAX6EuI,IAWtE,UAACL,EAAaM,QAAQ,EAAC7D,MAAO,aACnCyD,iBACAC,CACF,EAAGpI,sBAAoB,wBAAwBa,wBAAsB,sBAAsBZ,0BAAwB,4BAC9GN,GAEP,CACO,SAAS6I,IACd,IAAMC,EAAUC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAACT,GAC3B,QAAgB3H,IAAZmI,EACF,KADyB,CACnB,MAAU,6DAElB,OAAOA,CACT", "sources": ["webpack://next-shadcn-dashboard-starter/./src/app/favicon.ico", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync?c226", "webpack://next-shadcn-dashboard-starter/./src/app/theme.css", "webpack://next-shadcn-dashboard-starter/./src/components/layout/providers.tsx", "webpack://next-shadcn-dashboard-starter/?b615", "webpack://next-shadcn-dashboard-starter/./src/components/ui/sonner.tsx", "webpack://next-shadcn-dashboard-starter/./src/lib/utils.ts", "webpack://next-shadcn-dashboard-starter/?0856", "webpack://next-shadcn-dashboard-starter/?cc6a", "webpack://next-shadcn-dashboard-starter/./src/components/layout/ThemeToggle/theme-provider.tsx", "webpack://next-shadcn-dashboard-starter/?8195", "webpack://next-shadcn-dashboard-starter/./src/app/global-error.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/button.tsx", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://next-shadcn-dashboard-starter/?7caa", "webpack://next-shadcn-dashboard-starter/./src/app/not-found.tsx", "webpack://next-shadcn-dashboard-starter/?f22f", "webpack://next-shadcn-dashboard-starter/?37da", "webpack://next-shadcn-dashboard-starter/?4836", "webpack://next-shadcn-dashboard-starter/./src/lib/font.ts", "webpack://next-shadcn-dashboard-starter/src/app/layout.tsx", "webpack://next-shadcn-dashboard-starter/?5dd4", "webpack://next-shadcn-dashboard-starter/./src/app/globals.css", "webpack://next-shadcn-dashboard-starter/./src/components/active-theme.tsx"], "sourcesContent": ["  import { fillMetadataSegment } from 'next/dist/lib/metadata/get-metadata-route'\n\n  export default async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = fillMetadataSegment(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  }", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/',\n        componentType: 'Not-found',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/',\n      componentType: 'Not-found',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/',\n      componentType: 'Not-found',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/',\n      componentType: 'Not-found',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 9010;\nmodule.exports = webpackEmptyContext;", null, "'use client';\n\nimport { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';\nimport { dark } from '@clerk/themes';\nimport { useTheme } from 'next-themes';\nimport React from 'react';\nimport { ActiveThemeProvider } from '../active-theme';\nexport default function Providers({\n  activeThemeValue,\n  children\n}: {\n  activeThemeValue: string;\n  children: React.ReactNode;\n}) {\n  // we need the resolvedTheme value to set the baseTheme for clerk based on the dark or light theme\n  const {\n    resolvedTheme\n  } = useTheme();\n  return <>\r\n      <ActiveThemeProvider initialTheme={activeThemeValue} data-sentry-element=\"ActiveThemeProvider\" data-sentry-source-file=\"providers.tsx\">\r\n        <ClerkProvider appearance={{\n        baseTheme: resolvedTheme === 'dark' ? dark : undefined\n      }} data-sentry-element=\"ClerkProvider\" data-sentry-source-file=\"providers.tsx\">\r\n          {children}\r\n        </ClerkProvider>\r\n      </ActiveThemeProvider>\r\n    </>;\n}", "import(/* webpackMode: \"eager\", webpackExports: [\"__esModule\",\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\nextjs-toploader@3.8.16_nex_723fe9abaafa0667111aa9facca4b586\\\\node_modules\\\\nextjs-toploader\\\\dist\\\\index.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"NuqsAdapter\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\nuqs@2.4.1_next@15.3.2_@bab_c9b7efe6019a68e9ed2b5d9ba26d6a74\\\\node_modules\\\\nuqs\\\\dist\\\\adapters\\\\next\\\\app.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\providers.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\ThemeToggle\\\\theme-provider.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Toaster\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\sonner.tsx\");\n", "'use client';\n\nimport { useTheme } from 'next-themes';\nimport { Toaster as Sonner, ToasterProps } from 'sonner';\nconst Toaster = ({\n  ...props\n}: ToasterProps) => {\n  const {\n    theme = 'system'\n  } = useTheme();\n  return <Sonner theme={theme as ToasterProps['theme']} className='toaster group' style={{\n    '--normal-bg': 'var(--popover)',\n    '--normal-text': 'var(--popover-foreground)',\n    '--normal-border': 'var(--border)'\n  } as React.CSSProperties} {...props} data-sentry-element=\"Sonner\" data-sentry-component=\"Toaster\" data-sentry-source-file=\"sonner.tsx\" />;\n};\nexport { Toaster };", "import { type ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function formatBytes(\r\n  bytes: number,\r\n  opts: {\r\n    decimals?: number;\r\n    sizeType?: 'accurate' | 'normal';\r\n  } = {}\r\n) {\r\n  const { decimals = 0, sizeType = 'normal' } = opts;\r\n\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const accurateSizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB'];\r\n  if (bytes === 0) return '0 Byte';\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${\r\n    sizeType === 'accurate'\r\n      ? (accurateSizes[i] ?? 'Bytest')\r\n      : (sizes[i] ?? 'Bytes')\r\n  }`;\r\n}\r\n\r\n// Date and time formatting utilities\r\nexport function formatDateTime(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    hour12: false,\r\n  }).format(date);\r\n}\r\n\r\nexport function formatDate(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n  }).format(date);\r\n}\r\n\r\nexport function formatTime(date: Date): string {\r\n  return new Intl.DateTimeFormat('zh-CN', {\r\n    hour: '2-digit',\r\n    minute: '2-digit',\r\n    hour12: false,\r\n  }).format(date);\r\n}\r\n\r\nexport function formatRelativeTime(date: Date): string {\r\n  const now = new Date();\r\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n  if (diffInSeconds < 60) {\r\n    return '刚刚';\r\n  }\r\n\r\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\r\n  if (diffInMinutes < 60) {\r\n    return `${diffInMinutes}分钟前`;\r\n  }\r\n\r\n  const diffInHours = Math.floor(diffInMinutes / 60);\r\n  if (diffInHours < 24) {\r\n    return `${diffInHours}小时前`;\r\n  }\r\n\r\n  const diffInDays = Math.floor(diffInHours / 24);\r\n  if (diffInDays < 7) {\r\n    return `${diffInDays}天前`;\r\n  }\r\n\r\n  const diffInWeeks = Math.floor(diffInDays / 7);\r\n  if (diffInWeeks < 4) {\r\n    return `${diffInWeeks}周前`;\r\n  }\r\n\r\n  const diffInMonths = Math.floor(diffInDays / 30);\r\n  if (diffInMonths < 12) {\r\n    return `${diffInMonths}个月前`;\r\n  }\r\n\r\n  const diffInYears = Math.floor(diffInDays / 365);\r\n  return `${diffInYears}年前`;\r\n}\r\n\r\n// Task and interaction utilities\r\nexport function isOverdue(dueDate: string | Date): boolean {\r\n  const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;\r\n  return due < new Date();\r\n}\r\n\r\nexport function getDaysUntilDue(dueDate: string | Date): number {\r\n  const due = typeof dueDate === 'string' ? new Date(dueDate) : dueDate;\r\n  const now = new Date();\r\n  const diffInMs = due.getTime() - now.getTime();\r\n  return Math.ceil(diffInMs / (1000 * 60 * 60 * 24));\r\n}\r\n\r\nexport function getUrgencyLevel(dueDate: string | Date, priority: string): 'low' | 'medium' | 'high' | 'urgent' {\r\n  const daysUntil = getDaysUntilDue(dueDate);\r\n\r\n  if (daysUntil < 0) return 'urgent'; // Overdue\r\n  if (daysUntil === 0) return 'urgent'; // Due today\r\n  if (daysUntil === 1) return 'high'; // Due tomorrow\r\n\r\n  if (priority === 'urgent') return 'urgent';\r\n  if (priority === 'high') return daysUntil <= 3 ? 'high' : 'medium';\r\n  if (priority === 'medium') return daysUntil <= 7 ? 'medium' : 'low';\r\n\r\n  return 'low';\r\n}\r\n\r\n// Text processing utilities\r\nexport function truncateText(text: string, maxLength: number): string {\r\n  if (text.length <= maxLength) return text;\r\n  return text.substring(0, maxLength - 3) + '...';\r\n}\r\n\r\nexport function highlightSearchTerm(text: string, searchTerm: string): string {\r\n  if (!searchTerm) return text;\r\n\r\n  const regex = new RegExp(`(${searchTerm})`, 'gi');\r\n  return text.replace(regex, '<mark>$1</mark>');\r\n}\r\n\r\n// Validation utilities\r\nexport function isValidEmail(email: string): boolean {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n}\r\n\r\nexport function isValidPhone(phone: string): boolean {\r\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n  return phoneRegex.test(phone.replace(/\\s/g, ''));\r\n}\r\n\r\n// Array utilities\r\nexport function groupBy<T>(array: T[], keyFn: (item: T) => string): Record<string, T[]> {\r\n  return array.reduce((groups, item) => {\r\n    const key = keyFn(item);\r\n    if (!groups[key]) {\r\n      groups[key] = [];\r\n    }\r\n    groups[key].push(item);\r\n    return groups;\r\n  }, {} as Record<string, T[]>);\r\n}\r\n\r\nexport function sortBy<T>(array: T[], keyFn: (item: T) => any, direction: 'asc' | 'desc' = 'asc'): T[] {\r\n  return [...array].sort((a, b) => {\r\n    const aVal = keyFn(a);\r\n    const bVal = keyFn(b);\r\n\r\n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\r\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\r\n    return 0;\r\n  });\r\n}\r\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\client-page.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\client-segment.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\http-access-fallback\\\\error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\layout-router.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\metadata\\\\async-metadata.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\metadata\\\\metadata-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\render-from-template-context.js\");\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\n", "'use client';\n\nimport { ThemeProvider as NextThemesProvider, ThemeProviderProps } from 'next-themes';\nexport default function ThemeProvider({\n  children,\n  ...props\n}: ThemeProviderProps) {\n  return <NextThemesProvider {...props} data-sentry-element=\"NextThemesProvider\" data-sentry-component=\"ThemeProvider\" data-sentry-source-file=\"theme-provider.tsx\">{children}</NextThemesProvider>;\n}", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\n", "'use client';\n\nimport * as Sentry from '@sentry/nextjs';\nimport NextError from 'next/error';\nimport { useEffect } from 'react';\nexport default function GlobalError({\n  error\n}: {\n  error: Error & {\n    digest?: string;\n  };\n}) {\n  useEffect(() => {\n    Sentry.captureException(error);\n  }, [error]);\n  return <html data-sentry-component=\"GlobalError\" data-sentry-source-file=\"global-error.tsx\">\r\n      <body>\r\n        {/* `NextError` is the default Next.js error page component. Its type\r\n         definition requires a `statusCode` prop. However, since the App Router\r\n         does not expose status codes for errors, we simply pass 0 to render a\r\n         generic error message. */}\r\n        <NextError statusCode={0} data-sentry-element=\"NextError\" data-sentry-source-file=\"global-error.tsx\" />\r\n      </body>\r\n    </html>;\n}", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst buttonVariants = cva(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n  variants: {\n    variant: {\n      default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n      destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n      secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n      ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n      link: 'text-primary underline-offset-4 hover:underline'\n    },\n    size: {\n      default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n      sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n      lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n      icon: 'size-9'\n    }\n  },\n  defaultVariants: {\n    variant: 'default',\n    size: 'default'\n  }\n});\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> & VariantProps<typeof buttonVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'button';\n  return <Comp data-slot='button' className={cn(buttonVariants({\n    variant,\n    size,\n    className\n  }))} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Button\" data-sentry-source-file=\"button.tsx\" />;\n}\nexport { Button, buttonVariants };", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 45962;\nmodule.exports = webpackEmptyContext;", "import { createServerReference, callServer, findSourceMapURL } from 'private-next-rsc-action-client-wrapper'\nexport const invalidateCacheAction = /*#__PURE__*/createServerReference(\"7f22efd92a3b59d43d3d12fe480e87910640e1db9e\", callServer, undefined, findSourceMapURL, \"invalidateCacheAction\")", "'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nexport default function NotFound() {\n  const router = useRouter();\n  return <div className='absolute top-1/2 left-1/2 mb-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center text-center' data-sentry-component=\"NotFound\" data-sentry-source-file=\"not-found.tsx\">\r\n      <span className='from-foreground bg-linear-to-b to-transparent bg-clip-text text-[10rem] leading-none font-extrabold text-transparent'>\r\n        404\r\n      </span>\r\n      <h2 className='font-heading my-2 text-2xl font-bold'>\r\n        Something&apos;s missing\r\n      </h2>\r\n      <p>\r\n        Sorry, the page you are looking for doesn&apos;t exist or has been\r\n        moved.\r\n      </p>\r\n      <div className='mt-8 flex justify-center gap-2'>\r\n        <Button onClick={() => router.back()} variant='default' size='lg' data-sentry-element=\"Button\" data-sentry-source-file=\"not-found.tsx\">\r\n          Go back\r\n        </Button>\r\n        <Button onClick={() => router.push('/dashboard')} variant='ghost' size='lg' data-sentry-element=\"Button\" data-sentry-source-file=\"not-found.tsx\">\r\n          Back to Home\r\n        </Button>\r\n      </div>\r\n    </div>;\n}", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\client-page.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\client-segment.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\http-access-fallback\\\\error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\layout-router.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\metadata\\\\async-metadata.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\metadata\\\\metadata-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\render-from-template-context.js\");\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\n", "import {\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>_Mono,\r\n  Instrument_Sans,\r\n  <PERSON>,\r\n  Mu<PERSON>,\r\n  Noto_Sans_Mono\r\n} from 'next/font/google';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst fontSans = Geist({\r\n  subsets: ['latin'],\r\n  variable: '--font-sans'\r\n});\r\n\r\nconst fontMono = Geist_Mono({\r\n  subsets: ['latin'],\r\n  variable: '--font-mono'\r\n});\r\n\r\nconst fontInstrument = Instrument_Sans({\r\n  subsets: ['latin'],\r\n  variable: '--font-instrument'\r\n});\r\n\r\nconst fontNotoMono = Noto_Sans_Mono({\r\n  subsets: ['latin'],\r\n  variable: '--font-noto-mono'\r\n});\r\n\r\nconst fontMullish = Mulish({\r\n  subsets: ['latin'],\r\n  variable: '--font-mullish'\r\n});\r\n\r\nconst fontInter = Inter({\r\n  subsets: ['latin'],\r\n  variable: '--font-inter'\r\n});\r\n\r\nexport const fontVariables = cn(\r\n  fontSans.variable,\r\n  fontMono.variable,\r\n  fontInstrument.variable,\r\n  fontNotoMono.variable,\r\n  fontMullish.variable,\r\n  fontInter.variable\r\n);\r\n", "import Providers from '@/components/layout/providers';\nimport { Toaster } from '@/components/ui/sonner';\nimport { fontVariables } from '@/lib/font';\nimport ThemeProvider from '@/components/layout/ThemeToggle/theme-provider';\nimport { cn } from '@/lib/utils';\nimport type { Metadata, Viewport } from 'next';\nimport { cookies } from 'next/headers';\nimport NextTopLoader from 'nextjs-toploader';\nimport { NuqsAdapter } from 'nuqs/adapters/next/app';\nimport './globals.css';\nimport './theme.css';\nconst META_THEME_COLORS = {\n  light: '#ffffff',\n  dark: '#09090b'\n};\nexport const metadata: Metadata = {\n  title: '诊所管理系统',\n  description: '基于 Next.js 和 Shadcn 的医疗诊所管理系统'\n};\nexport const viewport: Viewport = {\n  themeColor: META_THEME_COLORS.light\n};\nexport default async function RootLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const cookieStore = await cookies();\n  const activeThemeValue = cookieStore.get('active_theme')?.value;\n  const isScaled = activeThemeValue?.endsWith('-scaled');\n  return <html lang='zh-CN' suppressHydrationWarning data-sentry-component=\"RootLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <head>\r\n        <script dangerouslySetInnerHTML={{\n        __html: `\n              try {\n                if (localStorage.theme === 'dark' || ((!('theme' in localStorage) || localStorage.theme === 'system') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {\n                  document.querySelector('meta[name=\"theme-color\"]').setAttribute('content', '${META_THEME_COLORS.dark}')\n                }\n              } catch (_) {}\n            `\n      }} />\r\n      </head>\r\n      <body className={cn('bg-background overflow-hidden overscroll-none font-sans antialiased', activeThemeValue ? `theme-${activeThemeValue}` : '', isScaled ? 'theme-scaled' : '', fontVariables)}>\r\n        <NextTopLoader showSpinner={false} data-sentry-element=\"NextTopLoader\" data-sentry-source-file=\"layout.tsx\" />\r\n        <NuqsAdapter data-sentry-element=\"NuqsAdapter\" data-sentry-source-file=\"layout.tsx\">\r\n          <ThemeProvider attribute='class' defaultTheme='system' enableSystem disableTransitionOnChange enableColorScheme data-sentry-element=\"ThemeProvider\" data-sentry-source-file=\"layout.tsx\">\r\n            <Providers activeThemeValue={activeThemeValue as string} data-sentry-element=\"Providers\" data-sentry-source-file=\"layout.tsx\">\r\n              <Toaster data-sentry-element=\"Toaster\" data-sentry-source-file=\"layout.tsx\" />\r\n              {children}\r\n            </Providers>\r\n          </ThemeProvider>\r\n        </NuqsAdapter>\r\n      </body>\r\n    </html>;\n}", "import(/* webpackMode: \"eager\", webpackExports: [\"__esModule\",\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\nextjs-toploader@3.8.16_nex_723fe9abaafa0667111aa9facca4b586\\\\node_modules\\\\nextjs-toploader\\\\dist\\\\index.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"NuqsAdapter\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\nuqs@2.4.1_next@15.3.2_@bab_c9b7efe6019a68e9ed2b5d9ba26d6a74\\\\node_modules\\\\nuqs\\\\dist\\\\adapters\\\\next\\\\app.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\providers.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\ThemeToggle\\\\theme-provider.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Toaster\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\sonner.tsx\");\n", null, "'use client';\n\nimport { ReactNode, createContext, useContext, useEffect, useState } from 'react';\nconst COOKIE_NAME = 'active_theme';\nconst DEFAULT_THEME = 'default';\nfunction setThemeCookie(theme: string) {\n  if (typeof window === 'undefined') return;\n  document.cookie = `${COOKIE_NAME}=${theme}; path=/; max-age=31536000; SameSite=Lax; ${window.location.protocol === 'https:' ? 'Secure;' : ''}`;\n}\ntype ThemeContextType = {\n  activeTheme: string;\n  setActiveTheme: (theme: string) => void;\n};\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\nexport function ActiveThemeProvider({\n  children,\n  initialTheme\n}: {\n  children: ReactNode;\n  initialTheme?: string;\n}) {\n  const [activeTheme, setActiveTheme] = useState<string>(() => initialTheme || DEFAULT_THEME);\n  useEffect(() => {\n    setThemeCookie(activeTheme);\n    Array.from(document.body.classList).filter(className => className.startsWith('theme-')).forEach(className => {\n      document.body.classList.remove(className);\n    });\n    document.body.classList.add(`theme-${activeTheme}`);\n    if (activeTheme.endsWith('-scaled')) {\n      document.body.classList.add('theme-scaled');\n    }\n  }, [activeTheme]);\n  return <ThemeContext.Provider value={{\n    activeTheme,\n    setActiveTheme\n  }} data-sentry-element=\"ThemeContext.Provider\" data-sentry-component=\"ActiveThemeProvider\" data-sentry-source-file=\"active-theme.tsx\">\r\n      {children}\r\n    </ThemeContext.Provider>;\n}\nexport function useThemeConfig() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useThemeConfig must be used within an ActiveThemeProvider');\n  }\n  return context;\n}"], "names": ["serverComponentModule.default", "Providers", "activeThemeValue", "children", "resolvedTheme", "useTheme", "ActiveThemeProvider", "initialTheme", "data-sentry-element", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON>", "appearance", "baseTheme", "dark", "undefined", "Toaster", "props", "theme", "<PERSON><PERSON>", "className", "style", "data-sentry-component", "cn", "inputs", "twMerge", "clsx", "ThemeProvider", "NextThemesProvider", "GlobalError", "error", "html", "body", "NextError", "statusCode", "buttonVariants", "cva", "variants", "variant", "default", "destructive", "outline", "secondary", "ghost", "link", "size", "sm", "lg", "icon", "defaultVariants", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot", "data-slot", "NotFound", "router", "useRouter", "div", "span", "h2", "p", "onClick", "back", "push", "fontVariables", "fontSans", "fontMono", "fontInstrument", "fontNotoMono", "fontMullish", "fontInter", "META_THEME_COLORS", "light", "metadata", "title", "description", "viewport", "themeColor", "RootLayout", "cookieStore", "cookies", "get", "value", "isScaled", "endsWith", "_jsxs", "lang", "suppressHydrationWarning", "_jsx", "head", "script", "dangerouslySetInnerHTML", "__html", "NextTopLoader", "showSpinner", "NuqsAdapter", "attribute", "defaultTheme", "enableSystem", "disableTransitionOnChange", "enableColorScheme", "formatBytes", "bytes", "opts", "decimals", "sizeType", "i", "Math", "floor", "log", "pow", "toFixed", "accurateSizes", "formatDateTime", "date", "Intl", "DateTimeFormat", "year", "month", "day", "hour", "minute", "hour12", "format", "formatRelativeTime", "diffInSeconds", "Date", "getTime", "diffInMinutes", "diffInHours", "diffInDays", "diffInWeeks", "diffInMonths", "diffInYears", "isOverdue", "dueDate", "due", "ThemeContext", "createContext", "activeTheme", "setActiveTheme", "useState", "DEFAULT_THEME", "Provider", "useThemeConfig", "context", "useContext"], "sourceRoot": ""}