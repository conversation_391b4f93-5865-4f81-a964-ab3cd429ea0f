try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="7034da1f-e701-422e-907d-ff84014e0fae",e._sentryDebugIdIdentifier="sentry-dbid-7034da1f-e701-422e-907d-ff84014e0fae")}catch(e){}"use strict";exports.id=4006,exports.ids=[4006],exports.modules={94006:(e,t,s)=>{s.d(t,{clerkDevelopmentCache:()=>l,createKeylessModeMessage:()=>n});var a=s(48855);let n=e=>`
\x1b[35m
[Clerk]:\x1b[0m You are running in keyless mode.
You can \x1b[35mclaim your keys\x1b[0m by visiting ${e.claimUrl}
`,l=function(){if((0,a.b_)())return global.__clerk_internal_keyless_logger||(global.__clerk_internal_keyless_logger={__cache:new Map,log:function({cacheKey:e,msg:t}){var s;this.__cache.has(e)&&Date.now()<((null==(s=this.__cache.get(e))?void 0:s.expiresAt)||0)||(console.log(t),this.__cache.set(e,{expiresAt:Date.now()+6e5}))},run:async function(e,{cacheKey:t,onSuccessStale:s=6e5,onErrorStale:a=6e5}){var n,l;if(this.__cache.has(t)&&Date.now()<((null==(n=this.__cache.get(t))?void 0:n.expiresAt)||0))return null==(l=this.__cache.get(t))?void 0:l.data;try{let a=await e();return this.__cache.set(t,{expiresAt:Date.now()+s,data:a}),a}catch(e){throw this.__cache.set(t,{expiresAt:Date.now()+a}),e}}}),globalThis.__clerk_internal_keyless_logger}()}};
//# sourceMappingURL=4006.js.map