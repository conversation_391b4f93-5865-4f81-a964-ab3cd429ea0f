"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2289],{20718:(e,t,l)=>{let n;l.r(t),l.d(t,{$createAutoLinkNode:()=>tC,$createBlockNode:()=>es,$createInlineBlockNode:()=>r.d,$createLinkNode:()=>tp,$createRelationshipNode:()=>lA,$createUploadNode:()=>nL,$isAutoLinkNode:()=>tk,$isBlockNode:()=>ed,$isInlineBlockNode:()=>r.e,$isLinkNode:()=>tf,$isRelationshipNode:()=>lR,$isUploadNode:()=>nN,AlignFeatureClient:()=>U,AutoLinkNode:()=>tx,BlockCollapsible:()=>n2,BlockEditButton:()=>n5,BlockNode:()=>ea,BlockRemoveButton:()=>n6,BlockquoteFeatureClient:()=>X,BlocksFeatureClient:()=>ep,BoldFeatureClient:()=>eN,CAN_USE_DOM:()=>d.a,ChecklistFeatureClient:()=>tQ,DETAIL_TYPE_TO_DETAIL:()=>lb,DOUBLE_LINE_BREAK:()=>lc,DebugJsxConverterFeatureClient:()=>l_,ELEMENT_FORMAT_TO_TYPE:()=>lx,ELEMENT_TYPE_TO_FORMAT:()=>lv,ENABLE_SLASH_MENU_COMMAND:()=>d.i,EditorConfigProvider:()=>u.a,FieldsDrawer:()=>i.a,FixedToolbarFeatureClient:()=>l4,HeadingFeatureClient:()=>e$,HorizontalRuleFeatureClient:()=>te,INSERT_BLOCK_COMMAND:()=>eu,INSERT_INLINE_BLOCK_COMMAND:()=>ec,IS_ALL_FORMATTING:()=>ld,IndentFeatureClient:()=>ti,InlineBlockContainer:()=>n1,InlineBlockEditButton:()=>n$,InlineBlockLabel:()=>n0,InlineBlockNode:()=>r.c,InlineBlockRemoveButton:()=>nQ,InlineCodeFeatureClient:()=>eI,InlineToolbarFeatureClient:()=>nl,ItalicFeatureClient:()=>eF,LTR_REGEX:()=>lm,LexicalPluginToLexicalFeatureClient:()=>le,LinkFeatureClient:()=>tK,LinkNode:()=>tc,NON_BREAKING_SPACE:()=>lu,NodeFormat:()=>ls,OrderedListFeatureClient:()=>t5,ParagraphFeatureClient:()=>lo,Point:()=>d.f,RTL_REGEX:()=>lf,Rect:()=>d.k,RelationshipFeatureClient:()=>lV,RelationshipNode:()=>lF,RichTextField:()=>nJ,SlateToLexicalFeatureClient:()=>ln,StrikethroughFeatureClient:()=>eR,SubscriptFeatureClient:()=>eH,SuperscriptFeatureClient:()=>eD,TEXT_MODE_TO_TYPE:()=>lC,TEXT_TYPE_TO_FORMAT:()=>lg,TEXT_TYPE_TO_MODE:()=>lk,TOGGLE_LINK_COMMAND:()=>tm,TableFeatureClient:()=>nx,TestRecorderFeatureClient:()=>ek,TextStateFeatureClient:()=>eZ,ToolbarButton:()=>lU,ToolbarDropdown:()=>l1,TreeViewFeatureClient:()=>ey,UnderlineFeatureClient:()=>ez,UnorderedListFeatureClient:()=>t8,UploadFeatureClient:()=>nH,UploadNode:()=>nT,addSwipeDownListener:()=>nX,addSwipeLeftListener:()=>nG,addSwipeRightListener:()=>nq,addSwipeUpListener:()=>nY,createBlockNode:()=>eG,createClientFeature:()=>V,defaultColors:()=>lS,defaultEditorLexicalConfig:()=>nB,getDOMRangeRect:()=>l8,getEnabledNodes:()=>d.l,getRestPopulateFn:()=>n3,getSelectedNode:()=>ts,isHTMLElement:()=>nV,isPoint:()=>d.g,joinClasses:()=>nK,sanitizeClientEditorConfig:()=>nO,sanitizeClientFeatures:()=>nD,setFloatingElemPosition:()=>l7,setFloatingElemPositionForLinkEditor:()=>tA,slashMenuBasicGroupWithItems:()=>z,toolbarAddDropdownGroupWithItems:()=>e1,toolbarFeatureButtonsGroupWithItems:()=>td,toolbarFormatGroupWithItems:()=>ew,toolbarTextDropdownGroupWithItems:()=>G,useBlockComponentContext:()=>en,useEditorConfigContext:()=>u.b,useInlineBlockComponentContext:()=>r.a,useLexicalDocumentDrawer:()=>s.a,useLexicalDrawer:()=>a.a,useLexicalListDrawer:()=>lP});var r=l(78412),o=l(94972),i=l(24825),a=l(22911),s=l(27044),d=l(89462),u=l(66856),c=l(5538),h=l(34381),p=l(92569),f=l(98525),m=l(90918),g=l(4106),b=l(88106),v=l(24239),x=l(27597),C=l(26048),k=l(6473),y=l(87991),w=l(44841),_=l(70153),E=l(44782),S=l(26751),T=l(68583),L=l(96551),N=l(38568),j=l(31176),I=l(93700),M=l(57344),F=l(86540),A=l(15094),R=l(57186),P=l(27208),H=l(20898),B=l(70116),D=l(47243),O=l(14440);function z(e){return{items:e,key:"basic",label:e=>{let{i18n:t}=e;return t.t("lexical:general:slashMenuBasicGroupLabel")}}}var J=()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M2.5 5H17.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M2.5 10H17.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M2.5 15H12.5",stroke:"currentColor",strokeWidth:"1.5"})]}),V=e=>t=>{let l={clientFeatureProps:t};if("function"==typeof e)l.feature=l=>{let{config:n,featureClientImportMap:r,featureClientSchemaMap:o,featureProviderMap:i,field:a,resolvedFeatures:s,schemaPath:d,unSanitizedEditorConfig:u}=l,c=e({config:n,featureClientImportMap:r,featureClientSchemaMap:o,featureProviderMap:i,field:a,props:t,resolvedFeatures:s,schemaPath:d,unSanitizedEditorConfig:u});return null===c.sanitizedClientFeatureProps&&(c.sanitizedClientFeatureProps=t),c};else{let n={...e};n.sanitizedClientFeatureProps=t,l.feature=n}return l},K=[(n=[{ChildComponent:J,isActive:e=>{let{selection:t}=e;if(!(0,c.I2P)(t))return!1;for(let e of t.getNodes()){if((0,c.ff4)(e)&&"left"===e.getFormatType())continue;let t=e.getParent();if(!((0,c.ff4)(t)&&"left"===t.getFormatType()))return!1}return!0},key:"alignLeft",label:e=>{let{i18n:t}=e;return t.t("lexical:align:alignLeftLabel")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.fUS,"left")},order:1},{ChildComponent:()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M2.5 5H17.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M2.5 10H17.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M5 15H15",stroke:"currentColor",strokeWidth:"1.5"})]}),isActive:e=>{let{selection:t}=e;if(!(0,c.I2P)(t))return!1;for(let e of t.getNodes()){if((0,c.ff4)(e)&&"center"===e.getFormatType())continue;let t=e.getParent();if(!((0,c.ff4)(t)&&"center"===t.getFormatType()))return!1}return!0},key:"alignCenter",label:e=>{let{i18n:t}=e;return t.t("lexical:align:alignCenterLabel")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.fUS,"center")},order:2},{ChildComponent:()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M2.5 5H17.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M2.5 10H17.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M7.5 15H17.5",stroke:"currentColor",strokeWidth:"1.5"})]}),isActive:e=>{let{selection:t}=e;if(!(0,c.I2P)(t))return!1;for(let e of t.getNodes()){if((0,c.ff4)(e)&&"right"===e.getFormatType())continue;let t=e.getParent();if(!((0,c.ff4)(t)&&"right"===t.getFormatType()))return!1}return!0},key:"alignRight",label:e=>{let{i18n:t}=e;return t.t("lexical:align:alignRightLabel")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.fUS,"right")},order:3},{ChildComponent:()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M2.5 5H17.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M2.5 10H17.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M2.5 15H17.5",stroke:"currentColor",strokeWidth:"1.5"})]}),isActive:e=>{let{selection:t}=e;if(!(0,c.I2P)(t))return!1;for(let e of t.getNodes()){if((0,c.ff4)(e)&&"justify"===e.getFormatType())continue;let t=e.getParent();if(!((0,c.ff4)(t)&&"justify"===t.getFormatType()))return!1}return!0},key:"alignJustify",label:e=>{let{i18n:t}=e;return t.t("lexical:align:alignJustifyLabel")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.fUS,"justify")},order:4}],{type:"dropdown",ChildComponent:J,items:n,key:"align",order:30})],U=V({toolbarFixed:{groups:K},toolbarInline:{groups:K}}),W=()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M13.5353 10.5725C13.5353 9.47709 11.0456 9.99991 11.0456 7.85883C11.0456 6.46464 12.1162 5.61816 13.361 5.61816C14.805 5.61816 16 6.86298 16 8.92937C16 11.2945 14.4564 13.7841 11.1203 14.3816L10.8216 13.1368C12.888 12.4895 13.5353 11.4937 13.5353 10.5725ZM6.71369 10.5725C6.71369 9.47709 4.22407 9.99991 4.22407 7.85883C4.22407 6.46464 5.29461 5.61816 6.53942 5.61816C7.9834 5.61816 9.17842 6.86298 9.17842 8.92937C9.17842 11.2945 7.63485 13.7841 4.29876 14.3816L4 13.1368C6.06639 12.4895 6.71369 11.4937 6.71369 10.5725Z",fill:"currentColor"})}),Z=()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"currentColor",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M11.708 14.5H7.79785V13.9414H8.01367C9.00391 13.9414 9.15625 13.9033 9.15625 13.6113V6.70508H8.07715C6.82031 6.70508 6.73145 7.08594 6.28711 8.67285H5.80469L5.91895 6.12109H13.5869L13.7012 8.67285H13.2188C12.7744 7.08594 12.6855 6.70508 11.4287 6.70508H10.3496V13.6113C10.3496 13.9033 10.502 13.9414 11.4922 13.9414H11.708V14.5Z",fill:"currentColor"})}),G=e=>({type:"dropdown",ChildComponent:Z,items:e,key:"text",order:25}),q={type:"element",dependencies:[f.dJ],export:(e,t)=>{if(!(0,f.jd)(e))return null;let l=t(e).split("\n"),n=[];for(let e of l)n.push("> "+e);return n.join("\n")},regExp:/^>\s/,replace:(e,t,l,n)=>{if(n){let l=e.getPreviousSibling();if((0,f.jd)(l)){l.splice(l.getChildrenSize(),0,[...t]),l.select(0,0),e.remove();return}}let r=(0,f.xi)();r.append(...t),e.replace(r),r.select(0,0)}},Y=[G([{ChildComponent:W,isActive:e=>{let{selection:t}=e;if(!(0,c.I2P)(t))return!1;for(let e of t.getNodes())if(!(0,f.jd)(e)&&!(0,f.jd)(e.getParent()))return!1;return!0},key:"blockquote",label:e=>{let{i18n:t}=e;return t.t("lexical:blockquote:label")},onSelect:e=>{let{editor:t}=e;t.update(()=>{let e=(0,c.vJq)();(0,m.zI)(e,()=>(0,f.xi)())})},order:20}])],X=V({markdownTransformers:[q],nodes:[f.dJ],slashMenu:{groups:[z([{Icon:W,key:"blockquote",keywords:["quote","blockquote"],label:e=>{let{i18n:t}=e;return t.t("lexical:blockquote:label")},onSelect:e=>{let{editor:t}=e;t.update(()=>{let e=(0,c.vJq)();(0,m.zI)(e,()=>(0,f.xi)())})}}])]},toolbarFixed:{groups:Y},toolbarInline:{groups:Y}}),$=()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("rect",{height:"4",rx:"0.5",stroke:"currentColor",width:"4",x:"8",y:"5"}),(0,h.jsx)("rect",{height:"4",rx:"0.5",stroke:"currentColor",width:"4",x:"5",y:"11"}),(0,h.jsx)("rect",{height:"4",rx:"0.5",stroke:"currentColor",width:"4",x:"11",y:"11"})]}),Q=()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{clipRule:"evenodd",d:"M5.33333 6.5C5.11232 6.5 4.90036 6.5878 4.74408 6.74408C4.5878 6.90036 4.5 7.11232 4.5 7.33333V12.1667C4.5 12.3877 4.5878 12.5996 4.74408 12.7559C4.90036 12.9122 5.11232 13 5.33333 13H14.6667C14.8877 13 15.0996 12.9122 15.2559 12.7559C15.4122 12.5996 15.5 12.3877 15.5 12.1667V11.6667C15.5 11.3905 15.7239 11.1667 16 11.1667C16.2761 11.1667 16.5 11.3905 16.5 11.6667V12.1667C16.5 12.6529 16.3068 13.1192 15.963 13.463C15.6192 13.8068 15.1529 14 14.6667 14H5.33333C4.8471 14 4.38079 13.8068 4.03697 13.463C3.69315 13.1192 3.5 12.6529 3.5 12.1667V7.33333C3.5 6.8471 3.69315 6.38079 4.03697 6.03697C4.38079 5.69315 4.8471 5.5 5.33333 5.5H10.3333C10.6095 5.5 10.8333 5.72386 10.8333 6C10.8333 6.27614 10.6095 6.5 10.3333 6.5H5.33333ZM13 6.5C12.7239 6.5 12.5 6.27614 12.5 6C12.5 5.72386 12.7239 5.5 13 5.5H16C16.2761 5.5 16.5 5.72386 16.5 6V9C16.5 9.27614 16.2761 9.5 16 9.5C15.7239 9.5 15.5 9.27614 15.5 9V7.20711L13.3536 9.35355C13.1583 9.54882 12.8417 9.54882 12.6464 9.35355C12.4512 9.15829 12.4512 8.84171 12.6464 8.64645L14.7929 6.5H13ZM6.16699 8.33325C6.16699 8.05711 6.39085 7.83325 6.66699 7.83325H11.0003C11.2765 7.83325 11.5003 8.05711 11.5003 8.33325C11.5003 8.60939 11.2765 8.83325 11.0003 8.83325H6.66699C6.39085 8.83325 6.16699 8.60939 6.16699 8.33325ZM6.16699 10.9999C6.16699 10.7238 6.39085 10.4999 6.66699 10.4999H13.3337C13.6098 10.4999 13.8337 10.7238 13.8337 10.9999C13.8337 11.2761 13.6098 11.4999 13.3337 11.4999H6.66699C6.39085 11.4999 6.16699 11.2761 6.16699 10.9999Z",fill:"currentColor",fillRule:"evenodd"})});function ee(e,t){return e?()=>(0,h.jsx)("img",{alt:null!=t?t:"Block Image",className:"lexical-block-custom-image",src:e,style:{maxHeight:20,maxWidth:20}}):$}var et=class extends v.d{static clone(e){return new this({cacheBuster:e.__cacheBuster,fields:e.__fields,format:e.__format,key:e.__key})}static getType(){return"block"}static importDOM(){return{}}static importJSON(e){var t;1===e.version&&(e={...e,fields:{...e.fields.data},version:2});let l=new et({fields:{...t=e.fields,id:(null==t?void 0:t.id)||new b.default().toHexString()}});return l.setFormat(e.format),l}static isInline(){return!1}decorate(e,t){return null}exportDOM(){let e=document.createElement("div"),t=document.createTextNode(this.getTextContent());return e.append(t),{element:e}}exportJSON(){return{...super.exportJSON(),type:"block",fields:this.getFields(),version:2}}getCacheBuster(){return this.getLatest().__cacheBuster}getFields(){return this.getLatest().__fields}getTextContent(){return"Block Field"}setFields(e,t){let l=this.getWritable();l.__fields=e,t||l.__cacheBuster++}constructor({cacheBuster:e,fields:t,format:l,key:n}){super(l,n),this.__fields=t,this.__cacheBuster=e||0}},el=(0,p.createContext)({initialState:!1}),en=()=>p.use(el),er=e=>{let t,l=(0,T.c)(13),{BlockDrawer:n,Collapsible:r,CustomBlock:o,EditButton:i,errorCount:a,formSchema:s,initialState:d,nodeKey:u,RemoveButton:c}=e,p=(0,x.useFormSubmitted)()&&a>0,f,m;return l[0]!==r||l[1]!==a||l[2]!==p?(m=e=>(0,h.jsx)(r,{editButton:e.editButton,errorCount:a,fieldHasErrors:p,Label:e.Label,removeButton:e.removeButton,children:e.children}),l[0]=r,l[1]=a,l[2]=p,l[3]=m):m=l[3],f=m,l[4]!==n||l[5]!==f||l[6]!==o||l[7]!==i||l[8]!==c||l[9]!==s||l[10]!==d||l[11]!==u?(t=o?(0,h.jsxs)(el,{value:{BlockCollapsible:f,EditButton:i,initialState:d,nodeKey:u,RemoveButton:c},children:[o,(0,h.jsx)(n,{})]}):(0,h.jsx)(f,{children:(0,h.jsx)(x.RenderFields,{fields:s,forceRender:!0,parentIndexPath:"",parentPath:"",parentSchemaPath:"",permissions:!0})}),l[4]=n,l[5]=f,l[6]=o,l[7]=i,l[8]=c,l[9]=s,l[10]=d,l[11]=u,l[12]=t):t=l[12],t},eo="lexical-block",ei=e=>{var t,l,n,r,o,i,s,d,f,m,b,v,T,L,N,j;let{cacheBuster:I,formData:M,nodeKey:F}=e,A=(0,x.useFormSubmitted)(),{id:R,collectionSlug:P,globalSlug:H}=(0,x.useDocumentInfo)(),{fieldProps:{featureClientSchemaMap:B,field:D,initialLexicalFormState:O,permissions:z,readOnly:J,schemaPath:V},uuid:K}=(0,u.b)(),{fields:U}=(0,x.useDocumentForm)(),W=(0,p.useRef)(new AbortController),Z=(0,x.useEditDepth)(),[G,q]=p.useState(0),{config:Y}=(0,C.b)(),X=(0,x.formatDrawerSlug)({slug:"lexical-blocks-create-".concat(K,"-").concat(M.id),depth:Z}),{toggleDrawer:$}=(0,a.a)(X),{getDocPreferences:Q,setDocFieldPreferences:ee}=(0,x.useDocumentInfo)(),[et]=(0,E.DF)(),{getFormState:el}=(0,x.useServerFunctions)(),en="".concat(V,".lexical_internal_feature.blocks.lexical_blocks.").concat(M.blockType,".fields"),[ei,ea]=p.useState(()=>{var e,t;return null!=O&&null!=(e=O[M.id])&&!!e.formState&&{...null==O||null==(t=O[M.id])?void 0:t.formState,blockName:{initialValue:M.blockName,passesCondition:!0,valid:!0,value:M.blockName}}}),es=(0,p.useRef)(!1),eu=(0,p.useRef)(I);(0,p.useEffect)(()=>{es.current?(eu.current!==I&&ea(!1),eu.current=I):es.current=!0},[I]);let[ec,eh]=p.useState(null==ei||null==(l=ei._components)||null==(t=l.customComponents)?void 0:t.BlockLabel),[ep,ef]=p.useState(null==ei||null==(r=ei._components)||null==(n=r.customComponents)?void 0:n.Block);(0,p.useEffect)(()=>{let e=new AbortController;return M&&!ei&&(async()=>{let{state:t}=await el({id:R,collectionSlug:P,data:M,docPermissions:{fields:!0},docPreferences:await Q(),documentFormState:(0,w.KS)(U),globalSlug:H,initialBlockData:M,operation:"update",renderAllFields:!0,schemaPath:en,signal:e.signal});if(t){var l,n,r,o;t.blockName={initialValue:M.blockName,passesCondition:!0,valid:!0,value:M.blockName};let e=(0,_.r)((0,w.KS)(t),!0);et.update(()=>{let t=(0,c.nsf)(F);t&&ed(t)&&(e.blockType=M.blockType,t.setFields(e,!0))}),ea(t),eh(null==(n=t._components)||null==(l=n.customComponents)?void 0:l.BlockLabel),ef(null==(o=t._components)||null==(r=o.customComponents)?void 0:r.Block)}})(),()=>{(0,y.eS)(e)}},[el,en,R,M,et,F,ei,P,H,Q,U]);let[em,eg]=p.useState(null!=(j=null==O||null==(o=O[M.id])?void 0:o.collapsed)&&j),eb="".concat(V,".lexical_internal_feature.blocks.lexical_blocks.").concat(M.blockType),ev=null==(s=B.blocks)||null==(i=s[eb])?void 0:i[0],ex=ev.blockReferences?"string"==typeof(null==ev||null==(d=ev.blockReferences)?void 0:d[0])?Y.blocksMap[null==ev||null==(f=ev.blockReferences)?void 0:f[0]]:null==ev||null==(m=ev.blockReferences)?void 0:m[0]:null==ev||null==(b=ev.blocks)?void 0:b[0],{i18n:eC,t:ek}=(0,k.d)(),ey=(0,p.useCallback)(async e=>{let{formState:t,submit:l}=e;(0,y.eS)(W.current);let n=new AbortController;W.current=n;let{state:r}=await el({id:R,collectionSlug:P,docPermissions:{fields:!0},docPreferences:await Q(),documentFormState:(0,w.KS)(U),formState:t,globalSlug:H,initialBlockFormState:t,operation:"update",renderAllFields:!!l,schemaPath:en,signal:n.signal});if(!r)return t;t.blockName&&(r.blockName=t.blockName);let o=(0,_.r)(function(e){let{fields:t}=e;for(let e in t){let l=t[e];Array.isArray(null==l?void 0:l.rows)&&"value"in l&&(l.disableFormData=!0)}return t}({fields:(0,w.KS)(r)}),!0);if(setTimeout(()=>{et.update(()=>{let e=(0,c.nsf)(F);e&&ed(e)&&(o.blockType=M.blockType,e.setFields(o,!0))})},0),l){var i,a,s,d;eh(null==(a=r._components)||null==(i=a.customComponents)?void 0:i.BlockLabel),ef(null==(d=r._components)||null==(s=d.customComponents)?void 0:s.Block);let e=0;for(let t of Object.values(r))(null==t?void 0:t.valid)===!1&&e++;q(e)}return r},[el,R,P,Q,H,en,M.blockType,U,et,F]);(0,p.useEffect)(()=>()=>{(0,y.eS)(W.current)},[]);let ew=(0,p.useCallback)(()=>{et.update(()=>{var e;null==(e=(0,c.nsf)(F))||e.remove()})},[et,F]),e_=(null==ex||null==(v=ex.labels)?void 0:v.singular)?(0,g.s)(ex.labels.singular,eC):null==ex?void 0:ex.slug,eE=(0,p.useCallback)(e=>{Q().then(t=>{var l,n;let r=null==t||null==(n=t.fields)||null==(l=n[D.name])?void 0:l.collapsed,o=r&&(null==r?void 0:r.length)?r:[];e?o.includes(M.id)||o.push(M.id):o.includes(M.id)&&o.splice(o.indexOf(M.id),1),ee(D.name,{collapsed:o,hello:"hi"})})},[Q,D.name,ee,M.id]),eS=(0,p.useMemo)(()=>()=>(0,h.jsx)(x.Button,{buttonStyle:"icon-label",className:"".concat(eo,"__editButton"),disabled:J,el:"button",icon:"edit",onClick:e=>(e.preventDefault(),e.stopPropagation(),$(),!1),onMouseDown:e=>{e.preventDefault()},round:!0,size:"small",tooltip:ek("lexical:blocks:inlineBlocks:edit",{label:e_})}),[e_,J,ek,$]),eT=(0,p.useMemo)(()=>()=>{var e;return(0,h.jsx)(x.Button,{buttonStyle:"icon-label",className:"".concat(eo,"__removeButton"),disabled:(null==D||null==(e=D.admin)?void 0:e.readOnly)||!1,icon:"x",onClick:e=>{e.preventDefault(),ew()},round:!0,tooltip:"Remove Block"})},[null==D||null==(T=D.admin)?void 0:T.readOnly,ew]),eL=(0,p.useMemo)(()=>e=>{var t,l;let{children:n,disableBlockName:r,editButton:o,errorCount:i,fieldHasErrors:a,Label:s,removeButton:d}=e;return(0,h.jsx)("div",{className:eo+" "+eo+"-"+M.blockType,children:(0,h.jsx)(x.Collapsible,{className:["".concat(eo,"__row"),a?"".concat(eo,"__row--has-errors"):"".concat(eo,"__row--no-errors")].join(" "),collapsibleStyle:a?"error":"default",header:(0,h.jsxs)("div",{className:"".concat(eo,"__block-header"),children:[(null!=s?s:ec)?null!=s?s:ec:(0,h.jsxs)("div",{children:[(0,h.jsx)(x.Pill,{className:"".concat(eo,"__block-pill ").concat(eo,"__block-pill-").concat(null==M?void 0:M.blockType),pillStyle:"white",size:"small",children:null!=e_?e_:null==M?void 0:M.blockType}),!r&&!(null==ex||null==(t=ex.admin)?void 0:t.disableBlockName)&&(0,h.jsx)(x.SectionTitle,{path:"blockName",readOnly:(null==D||null==(l=D.admin)?void 0:l.readOnly)||!1}),a&&(0,h.jsx)(x.ErrorPill,{count:null!=i?i:0,i18n:eC,withMessage:!0})]}),(0,h.jsxs)("div",{children:[ep&&!1!==o||!ep&&o?(0,h.jsx)(eS,{}):null,!1!==d&&et.isEditable()?(0,h.jsx)(eT,{}):null]})]}),isCollapsed:em,onToggle:e=>{eE(e),eg(e)},children:n},0)})},[ep,ec,eS,eT,e_,null==ex||null==(L=ex.admin)?void 0:L.disableBlockName,et,M.blockType,eC,em,eE,null==D||null==(N=D.admin)?void 0:N.readOnly]),eN=(0,p.useMemo)(()=>()=>{var e;return(0,h.jsx)(x.EditDepthProvider,{children:(0,h.jsx)(x.Drawer,{className:"",slug:X,title:ek("lexical:blocks:inlineBlocks:".concat((null==M?void 0:M.id)?"edit":"create"),{label:null!=e_?e_:ek("lexical:blocks:inlineBlocks:label")}),children:ei?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(x.RenderFields,{fields:null!=(e=null==ex?void 0:ex.fields)?e:[],forceRender:!0,parentIndexPath:"",parentPath:"",parentSchemaPath:en,permissions:!0,readOnly:!1}),(0,h.jsx)(x.FormSubmit,{programmaticSubmit:!0,children:ek("fields:saveChanges")})]}):null})})},[ei,X,e_,ek,null==ex?void 0:ex.fields,en,z]),ej=(0,p.useMemo)(()=>{var e,t;return ei?(0,h.jsx)(x.Form,{beforeSubmit:[async e=>{let{formState:t}=e;return await ey({formState:t,submit:!0})}],el:"div",fields:null!=(e=null==ex?void 0:ex.fields)?e:[],initialState:ei,onChange:[ey],onSubmit:(e,t)=>{t.blockType=M.blockType,et.update(()=>{let e=(0,c.nsf)(F);e&&ed(e)&&e.setFields(t,!0)}),$()},submitted:A,uuid:(0,S.A)(),children:(0,h.jsx)(er,{baseClass:eo,BlockDrawer:eN,Collapsible:eL,CustomBlock:ep,EditButton:eS,errorCount:G,formSchema:null!=(t=null==ex?void 0:ex.fields)?t:[],initialState:ei,nodeKey:F,RemoveButton:eT})}):null},[eL,eN,ep,null==ex?void 0:ex.fields,eT,eS,et,G,$,null==ex?void 0:ex.fields,ei,F,ey,A]);return ex?ej:(0,h.jsx)(eL,{disableBlockName:!0,fieldHasErrors:!0,children:(0,h.jsxs)("div",{className:"lexical-block-not-found",children:["Error: Block '",M.blockType,"' not found in the config but exists in the lexical data"]})})},ea=class extends et{static clone(e){return super.clone(e)}static getType(){return super.getType()}static importJSON(e){1===e.version&&(e={...e,fields:{...e.fields.data},version:2});let t=es(e.fields);return t.setFormat(e.format),t}decorate(e,t){return(0,h.jsx)(ei,{cacheBuster:this.getCacheBuster(),formData:this.getFields(),nodeKey:this.getKey()})}exportJSON(){return super.exportJSON()}};function es(e){return new ea({fields:{...e,id:(null==e?void 0:e.id)||new b.default().toHexString()}})}function ed(e){return e instanceof ea}var eu=(0,c.guI)("INSERT_BLOCK_COMMAND"),ec=(0,c.guI)("INSERT_INLINE_BLOCK_COMMAND"),eh=()=>{let e,t=(0,T.c)(12),[l]=(0,E.DF)(),[n,o]=(0,p.useState)(null),{setCreatedInlineBlock:i,uuid:s}=(0,u.b)(),d=(0,x.useEditDepth)(),h="lexical-inlineBlocks-create-"+s,f;t[0]!==d||t[1]!==h?(f=(0,x.formatDrawerSlug)({slug:h,depth:d}),t[0]=d,t[1]=h,t[2]=f):f=t[2];let m=f,{toggleDrawer:g}=(0,a.a)(m,!0),b;return t[3]!==l||t[4]!==i||t[5]!==n?(b=()=>{if(!l.hasNodes([ea]))throw Error("BlocksPlugin: BlocksNode not registered on editor");return(0,L.Sd)(l.registerCommand(eu,e=>(l.update(()=>{let t=(0,c.vJq)()||(0,c.S1w)();if((0,c.I2P)(t)){let l=es(e),{focus:n}=t,r=n.getNode();(0,L.Pe)(l),(0,c.bSg)(r)&&!r.__first&&r.remove()}}),!0),c.jZM),l.registerCommand(ec,e=>{if(n){let t=(0,c.nsf)(n);return!!t&&!!(0,r.e)(t)&&(t.setFields(e),o(null),!0)}let t=(0,r.d)(e);return null==i||i(t),(0,c.H2A)([t]),(0,c.IqF)(t.getParentOrThrow())&&(0,L.cj)(t,c.lJ7).selectEnd(),!0},c.jZM))},t[3]=l,t[4]=i,t[5]=n,t[6]=b):b=t[6],t[7]!==l||t[8]!==i||t[9]!==n||t[10]!==g?(e=[l,i,n,g],t[7]=l,t[8]=i,t[9]=n,t[10]=g,t[11]=e):e=t[11],(0,p.useEffect)(b,e),null},ep=V(e=>{let{config:t,featureClientSchemaMap:l,props:n,schemaPath:o}=e,i="".concat(o,".lexical_internal_feature.blocks.lexical_blocks"),a="".concat(o,".lexical_internal_feature.blocks.lexical_inline_blocks"),s=l.blocks;if(!s)return{};let d=Object.entries(s).filter(e=>{let[t]=e;return t.startsWith(i+".")&&!t.replace(i+".","").includes(".")}).map(e=>{let[,t]=e;return t[0]}),u=Object.entries(s).filter(e=>{let[t]=e;return t.startsWith(a+".")&&!t.replace(a+".","").includes(".")}).map(e=>{let[,t]=e;return t[0]}),c=d.map(e=>e.blockReferences?"string"==typeof e.blockReferences[0]?t.blocksMap[e.blockReferences[0]]:e.blockReferences[0]:e.blocks[0]).filter(e=>void 0!==e),h=u.map(e=>e.blockReferences?"string"==typeof e.blockReferences[0]?t.blocksMap[e.blockReferences[0]]:e.blockReferences[0]:e.blocks[0]).filter(e=>void 0!==e);return{nodes:[ea,r.c],plugins:[{Component:eh,position:"normal"}],sanitizedClientFeatureProps:n,slashMenu:{groups:[(null==c?void 0:c.length)?{items:c.map(e=>({Icon:ee(e.imageURL,e.imageAltText),key:"block-"+e.slug,keywords:["block","blocks",e.slug],label:t=>{var l;let{i18n:n}=t;return(null==e||null==(l=e.labels)?void 0:l.singular)?(0,g.s)(e.labels.singular,n):null==e?void 0:e.slug},onSelect:t=>{let{editor:l}=t;l.dispatchCommand(eu,{blockName:"",blockType:e.slug})}})),key:"blocks",label:e=>{let{i18n:t}=e;return t.t("lexical:blocks:label")}}:null,(null==h?void 0:h.length)?{items:h.map(e=>({Icon:Q,key:"inlineBlocks-"+e.slug,keywords:["inlineBlock","inline block",e.slug],label:t=>{var l;let{i18n:n}=t;return(null==e||null==(l=e.labels)?void 0:l.singular)?(0,g.s)(e.labels.singular,n):null==e?void 0:e.slug},onSelect:t=>{let{editor:l}=t;l.dispatchCommand(ec,{blockName:"",blockType:e.slug})}})),key:"inlineBlocks",label:e=>{let{i18n:t}=e;return t.t("lexical:blocks:inlineBlocks:label")}}:null].filter(Boolean)},toolbarFixed:{groups:[c.length?{type:"dropdown",ChildComponent:$,items:c.map((e,t)=>({ChildComponent:ee(e.imageURL,e.imageAltText),isActive:void 0,key:"block-"+e.slug,label:t=>{var l;let{i18n:n}=t;return(null==e||null==(l=e.labels)?void 0:l.singular)?(0,g.s)(e.labels.singular,n):null==e?void 0:e.slug},onSelect:t=>{let{editor:l}=t;l.dispatchCommand(eu,{blockName:"",blockType:e.slug})},order:t})),key:"blocks",order:20}:null,(null==h?void 0:h.length)?{type:"dropdown",ChildComponent:Q,items:h.map((e,t)=>({ChildComponent:e.imageURL?ee(e.imageURL,e.imageAltText):Q,isActive:void 0,key:"inlineBlock-"+e.slug,label:t=>{var l;let{i18n:n}=t;return(null==e||null==(l=e.labels)?void 0:l.singular)?(0,g.s)(e.labels.singular,n):null==e?void 0:e.slug},onSelect:t=>{let{editor:l}=t;l.dispatchCommand(ec,{blockName:"",blockType:e.slug})},order:t})),key:"inlineBlocks",order:25}:null].filter(Boolean)}}}),ef=d.a&&"documentMode"in document?document.documentMode:null,em=d.a&&/Mac|iPod|iPhone|iPad/.test(navigator.platform);d.a&&/^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent),d.a&&"InputEvent"in window&&!ef&&new window.InputEvent("input"),d.a&&/Version\/[\d.].*Safari/.test(navigator.userAgent),d.a&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&window.MSStream,d.a&&/Android/.test(navigator.userAgent),d.a&&/Win/.test(navigator.platform),d.a&&/^(?=.*Chrome).*/i.test(navigator.userAgent),d.a&&/AppleWebKit\/[\d.]+/.test(navigator.userAgent);var eg=e=>{var t,l;let n=document.createElement("textarea");n.value=e||"",n.style.position="absolute",n.style.opacity="0",null==(t=document.body)||t.appendChild(n),n.focus(),n.select();try{let e=document.execCommand("copy");console.log(e)}catch(e){console.error(e)}null==(l=document.body)||l.removeChild(n)},eb=(e,t)=>{var l,n;let r=document.createElement("a");r.setAttribute("href","data:text/plain;charset=utf-8,"+encodeURIComponent(t||"")),r.setAttribute("download",e),r.style.display="none",null==(l=document.body)||l.appendChild(r),r.click(),null==(n=document.body)||n.removeChild(r)},ev=e=>{let t=((e,t)=>{switch(e){case"click":return"      await page.mouse.click(".concat(t.x,", ").concat(t.y,");");case"keydown":return"      await page.keyboard.keydown('".concat(t,"');");case"keyup":return"      await page.keyboard.keyup('".concat(t,"');");case"press":return"      await page.keyboard.press('".concat(t,"');");case"selectAll":return"      await selectAll(page);";case"snapshot":return"      await assertHTMLSnapshot(page);\n      await assertSelection(page, {\n        anchorPath: [".concat(t.anchorPath.toString(),"],\n        anchorOffset: ").concat(t.anchorOffset,",\n        focusPath: [").concat(t.focusPath.toString(),"],\n        focusOffset: ").concat(t.focusOffset,",\n      });\n");case"type":return"      await page.keyboard.type('".concat(t,"');");default:return""}})(e.name,e.value);switch(e.count){case 1:return t;case 2:return[t,t].join("\n");default:return"      await repeat(".concat(e.count,", async () => {\n  ").concat(t,"\n      );")}};function ex(e,t){var l,n;let r=e,o=[];for(;r!==t;)null!=r&&o.unshift(Array.from(null!=(n=null==r||null==(l=r.parentNode)?void 0:l.childNodes)?n:[]).indexOf(r)),r=null==r?void 0:r.parentNode;return o}var eC=new Set(["ArrowDown","ArrowLeft","ArrowRight","ArrowUp","Backspace","Delete","Enter","Escape"]),ek=V({plugins:[{Component:()=>{let e=(0,T.c)(3),[t]=(0,E.DF)(),[l,n]=function(e){let[t,l]=(0,p.useState)([]),[n,r]=(0,p.useState)(!1),[,o]=(0,p.useState)(""),[i,a]=(0,p.useState)(""),s=(0,p.useRef)(null),d=(0,p.useRef)(!1),u=(0,p.useRef)(null),f=(0,p.useCallback)(()=>e,[e]),m=(0,p.useCallback)(()=>{let l=e.getRootElement(),n=(0,c.peL)(e._window);return null!=l&&null!=n&&null!=n.anchorNode&&null!=n.focusNode&&l.contains(n.anchorNode)&&l.contains(n.focusNode)?"\nimport {\n  initializeE2E,\n  assertHTMLSnapshot,\n  assertSelection,\n  repeat,\n} from '../utils';\nimport {selectAll} from '../keyboardShortcuts';\nimport { RangeSelection } from 'lexical';\nimport { NodeSelection } from 'lexical';\n\ndescribe('Test case', () => {\n  initializeE2E((e2e) => {\n    it('Should pass this test', async () => {\n      const {page} = e2e;\n\n      await page.focus('div[contenteditable=\"true\"]');\n".concat(t.map(ev).join("\n"),"\n    });\n});\n    "):null},[e,t]),g=(0,p.useCallback)((e,n)=>{l(l=>{let r=t.length-1,o=t[r];if(o&&o.name===e){if("type"===e)return[...t.slice(0,r),{...o,value:o.value+n}];if(o.value===n)return[...t.slice(0,r),{...o,count:o.count+1}]}return[...l,{name:e,count:1,value:n}]})},[t,l]);(0,p.useLayoutEffect)(()=>{let t=e=>{if(!n)return;let t=e.key;"a"===e.key.toLowerCase()&&(em?e.metaKey:e.ctrlKey)?g("selectAll",""):eC.has(t)?g("press",e.key):[...t].length>1?g("keydown",e.key):g("type",e.key)},l=e=>{if(!n)return;let t=e.key;!eC.has(t)&&[...t].length>1&&g("keyup",e.key)};return e.registerRootListener((e,n)=>{null!==n&&(n.removeEventListener("keydown",t),n.removeEventListener("keyup",l)),null!==e&&(e.addEventListener("keydown",t),e.addEventListener("keyup",l))})},[e,n,g]),(0,p.useLayoutEffect)(()=>{u.current&&u.current.scrollTo(0,u.current.scrollHeight)},[m]),(0,p.useEffect)(()=>{if(t){let e=m();null!==e&&a(e),u.current&&u.current.scrollTo(0,u.current.scrollHeight)}},[m,t]),(0,p.useEffect)(()=>e.registerUpdateListener(t=>{let{dirtyElements:l,dirtyLeaves:r,editorState:o}=t;if(!n)return;let i=o._selection,u=s.current,h=d.current;if(u!==i){if(0===r.size&&0===l.size&&!h){let t=(0,c.peL)(e._window);if(t&&(null==t.anchorNode||null==t.focusNode))return}s.current=i}d.current=!1;let p=m();null!==p&&a(p)}),[e,m,n,g]),(0,p.useEffect)(()=>n?e.registerUpdateListener(()=>{let t=e.getRootElement();null!==t&&o(null==t?void 0:t.innerHTML)}):void 0,[e,n]);let b=(0,p.useCallback)(e=>{n||(e.update(()=>{let e=(0,c.NiT)();e.clear();let t=(0,c.sTu)();e.append((0,c.lJ7)().append(t)),t.select()}),l([])),r(e=>!e)},[n]),v=(0,p.useCallback)(()=>{let t;if(!n)return;let l=(0,c.peL)(e._window);if(null===l||null==l.anchorNode||null==l.focusNode)return;let{anchorNode:r,anchorOffset:o,focusNode:i,focusOffset:a}=function(e){let{anchorNode:t,focusNode:l}=e,{anchorOffset:n,focusOffset:r}=e;return 0!==n&&n--,0!==r&&r--,{anchorNode:t,anchorOffset:n,focusNode:l,focusOffset:r}}(l),s=f().getRootElement(),d;null!==r&&(d=ex(r,s)),null!==i&&(t=ex(i,s)),g("snapshot",{anchorNode:r,anchorOffset:o,anchorPath:d,focusNode:i,focusOffset:a,focusPath:t})},[g,n,f]),x=(0,p.useCallback)(()=>{eg(m())},[m]),C=(0,p.useCallback)(()=>{eb("test.js",m())},[m]);return[(0,h.jsx)("button",{className:"editor-dev-button ".concat(n?"active":""),id:"test-recorder-button",onClick:e=>{b(f()),e.preventDefault()},title:n?"Disable test recorder":"Enable test recorder",type:"button",children:n?"Disable test recorder":"Enable test recorder"}),n?(0,h.jsxs)("div",{className:"test-recorder-output",children:[(0,h.jsxs)("div",{className:"test-recorder-toolbar",children:[(0,h.jsx)("button",{className:"test-recorder-button",id:"test-recorder-button-snapshot",onClick:e=>{v(),e.preventDefault()},title:"Insert snapshot",type:"button",children:"Insert Snapshot"}),(0,h.jsx)("button",{className:"test-recorder-button",id:"test-recorder-button-copy",onClick:e=>{x(),e.preventDefault()},title:"Copy to clipboard",type:"button",children:"Copy"}),(0,h.jsx)("button",{className:"test-recorder-button",id:"test-recorder-button-download",onClick:e=>{C(),e.preventDefault()},title:"Download as a file",type:"button",children:"Download"})]}),(0,h.jsx)("pre",{id:"test-recorder",ref:u,children:i})]}):null]}(t),r;return e[0]!==l||e[1]!==n?(r=(0,h.jsxs)(p.Fragment,{children:[(0,h.jsx)("p",{children:"HI"}),l,n,(0,h.jsx)("p",{children:"DONE"})]}),e[0]=l,e[1]=n,e[2]=r):r=e[2],r},position:"bottom"}]}),ey=V({plugins:[{Component:()=>{let e=(0,T.c)(2),[t]=(0,E.DF)(),l;return e[0]!==t?(l=(0,h.jsx)(N.G,{editor:t,timeTravelButtonClassName:"debug-timetravel-button",timeTravelPanelButtonClassName:"debug-timetravel-panel-button",timeTravelPanelClassName:"debug-timetravel-panel",timeTravelPanelSliderClassName:"debug-timetravel-panel-slider",treeTypeButtonClassName:"debug-treetype-button",viewClassName:"tree-view-output"}),e[0]=t,e[1]=l):l=e[1],l},position:"bottom"}]}),ew=e=>({type:"buttons",items:e,key:"format",order:40}),e_={type:"text-format",format:["bold","italic"],tag:"***"},eE={type:"text-format",format:["bold","italic"],intraword:!1,tag:"___"},eS={type:"text-format",format:["bold"],tag:"**"},eT={type:"text-format",format:["bold"],intraword:!1,tag:"__"},eL=[ew([{ChildComponent:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"currentColor",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M10.6772 15H6.27017V5.718H10.4172C12.6792 5.718 13.8492 6.602 13.8492 8.292C13.8492 9.098 13.1992 9.982 12.4712 10.216C13.3812 10.476 14.1742 11.256 14.1742 12.322C14.1742 14.09 12.9002 15 10.6772 15ZM8.46717 9.501H10.3262C11.3012 9.501 11.7042 9.046 11.7042 8.409C11.7042 7.72 11.2362 7.317 10.3392 7.317H8.46717V9.501ZM8.46717 11.061V13.401H10.4822C11.4702 13.401 11.9642 12.959 11.9642 12.218C11.9642 11.49 11.4702 11.061 10.4822 11.061H8.46717Z",fill:"currentColor"})}),isActive:e=>{let{selection:t}=e;return!!((0,c.I2P)(t)||(0,j.Ln)(t))&&t.hasFormat("bold")},key:"bold",onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.mB,"bold")},order:1}])],eN=V(e=>{let{featureProviderMap:t}=e,l=[eS,eT];return t.get("italic")&&l.push(eE,e_),{enableFormats:["bold"],markdownTransformers:l,toolbarFixed:{groups:eL},toolbarInline:{groups:eL}}}),ej=[ew([{ChildComponent:()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M7.76465 6L3.76465 10L7.76465 14",stroke:"currentColor"}),(0,h.jsx)("path",{d:"M12.2354 6L16.2354 10L12.2354 14",stroke:"currentColor"})]}),isActive:e=>{let{selection:t}=e;return!!((0,c.I2P)(t)||(0,j.Ln)(t))&&t.hasFormat("code")},key:"inlineCode",onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.mB,"code")},order:7}])],eI=V({enableFormats:["code"],markdownTransformers:[{type:"text-format",format:["code"],tag:"`"}],toolbarFixed:{groups:ej},toolbarInline:{groups:ej}}),eM=[ew([{ChildComponent:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"currentColor",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M11.311 14.2969L11.0327 15H6.18408L6.4624 14.2969C7.54639 14.2969 7.70752 14.209 7.83936 13.8721L10.8423 6.45996C10.8716 6.38672 10.8862 6.32812 10.8862 6.26953C10.8862 6.09375 10.6519 6.03516 9.80225 6.03516L10.0952 5.33203H14.9438L14.6509 6.03516C13.5669 6.03516 13.4204 6.12305 13.2886 6.45996L10.2856 13.8721C10.2563 13.9453 10.2271 14.0039 10.2271 14.0625C10.2271 14.2383 10.4614 14.2969 11.311 14.2969Z",fill:"currentColor"})}),isActive:e=>{let{selection:t}=e;return!!((0,c.I2P)(t)||(0,j.Ln)(t))&&t.hasFormat("italic")},key:"italic",onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.mB,"italic")},order:2}])],eF=V({enableFormats:["italic"],markdownTransformers:[{type:"text-format",format:["italic"],tag:"*"},{type:"text-format",format:["italic"],intraword:!1,tag:"_"}],toolbarFixed:{groups:eM},toolbarInline:{groups:eM}}),eA=[ew([{ChildComponent:()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"currentColor",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M5.50756 12.76H7.42756C7.56256 14.215 8.82256 14.71 10.1576 14.71C11.4326 14.71 12.4226 14.14 12.4226 13.06C12.4226 12.28 11.9576 11.845 10.6676 11.605L8.70256 11.245C7.12756 10.96 5.85256 10.21 5.85256 8.335C5.85256 6.43 7.53256 5.11 9.87256 5.11C12.4226 5.11 13.9526 6.22 14.1626 8.23H12.2876C12.1526 7.18 11.2226 6.595 9.88756 6.595C8.59756 6.595 7.78756 7.27 7.78756 8.215C7.78756 9.1 8.34256 9.385 9.49756 9.61L11.5676 10.015C13.3226 10.345 14.3726 11.215 14.3726 12.94C14.3726 14.89 12.5876 16.18 10.2176 16.18C7.66756 16.18 5.70256 15.115 5.50756 12.76Z",fill:"currentColor"}),(0,h.jsx)("path",{d:"M4.99756 11.44H15.0026V12.19H4.99756V11.44Z",fill:"currentColor"})]}),isActive:e=>{let{selection:t}=e;return!!((0,c.I2P)(t)||(0,j.Ln)(t))&&t.hasFormat("strikethrough")},key:"strikethrough",onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.mB,"strikethrough")},order:4}])],eR=V({enableFormats:["strikethrough"],markdownTransformers:[{type:"text-format",format:["strikethrough"],tag:"~~"}],toolbarFixed:{groups:eA},toolbarInline:{groups:eA}}),eP=[ew([{ChildComponent:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"currentColor",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M10.167 15L7.45002 11.36L4.73302 15H2.91302L6.55302 10.177L3.23802 5.718H5.20102L7.54102 8.89L9.89402 5.718H11.714L8.43802 10.06L12.13 15H10.167ZM16.7768 13.258C16.7768 14.155 16.1398 14.532 15.2038 15C14.5538 15.325 14.2808 15.546 14.2418 15.78H16.7898V16.82H12.7208V16.339C12.7208 15.286 13.5918 14.675 14.3588 14.233C15.0868 13.83 15.4378 13.635 15.4378 13.232C15.4378 12.894 15.2038 12.686 14.8268 12.686C14.3848 12.686 14.1248 13.024 14.1118 13.427H12.7468C12.8248 12.426 13.5528 11.633 14.8398 11.633C15.9448 11.633 16.7768 12.257 16.7768 13.258Z",fill:"currentColor"})}),isActive:e=>{let{selection:t}=e;return!!((0,c.I2P)(t)||(0,j.Ln)(t))&&t.hasFormat("subscript")},key:"subscript",onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.mB,"subscript")},order:5}])],eH=V({enableFormats:["subscript"],toolbarFixed:{groups:eP},toolbarInline:{groups:eP}}),eB=[ew([{ChildComponent:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"currentColor",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M10.167 15L7.45002 11.36L4.73302 15H2.91302L6.55302 10.177L3.23802 5.718H5.20102L7.54102 8.89L9.89402 5.718H11.714L8.43802 10.06L12.13 15H10.167ZM16.7768 7.252C16.7768 8.149 16.1398 8.526 15.2038 8.994C14.5538 9.319 14.2808 9.54 14.2418 9.774H16.7898V10.814H12.7208V10.333C12.7208 9.28 13.5918 8.669 14.3588 8.227C15.0868 7.824 15.4378 7.629 15.4378 7.226C15.4378 6.888 15.2038 6.68 14.8268 6.68C14.3848 6.68 14.1248 7.018 14.1118 7.421H12.7468C12.8248 6.42 13.5528 5.627 14.8398 5.627C15.9448 5.627 16.7768 6.251 16.7768 7.252Z",fill:"currentColor"})}),isActive:e=>{let{selection:t}=e;return!!((0,c.I2P)(t)||(0,j.Ln)(t))&&t.hasFormat("superscript")},key:"superscript",onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.mB,"superscript")},order:6}])],eD=V({enableFormats:["superscript"],toolbarFixed:{groups:eB},toolbarInline:{groups:eB}}),eO=[ew([{ChildComponent:()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"currentColor",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M13.9656 11.256C13.9656 13.791 12.5096 15.156 10.0006 15.156C7.50461 15.156 6.03561 13.791 6.03561 11.23V5.718H7.76461V11.243C7.76461 12.868 8.50561 13.778 10.0006 13.778C11.4956 13.778 12.2496 12.868 12.2496 11.243V5.718H13.9656V11.256Z",fill:"currentColor"}),(0,h.jsx)("path",{d:"M5.09961 16.3H14.9016V16.95H5.09961V16.3Z",fill:"currentColor"})]}),isActive:e=>{let{selection:t}=e;return!!((0,c.I2P)(t)||(0,j.Ln)(t))&&t.hasFormat("underline")},key:"underline",onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.mB,"underline")},order:3}])],ez=V({enableFormats:["underline"],toolbarFixed:{groups:eO},toolbarInline:{groups:eO}}),eJ=e=>{let{css:t}=e,l=t?Object.fromEntries(Object.entries(t).map(e=>{let[t,l]=e;return[t.replace(/-([a-z])/g,(e,t)=>t.toUpperCase()),l]})):{};return(0,h.jsx)("span",{style:{...l,alignItems:"center",borderRadius:"4px",display:"flex",fontSize:"16px",height:"20px",justifyContent:"center",width:"20px"},children:"A"})},eV=new Map;function eK(e,t,l){e.update(()=>{(0,m.Vs)(e=>{let n=eV.get(t);if(!n)throw Error("State config for ".concat(t," not found"));(0,c.Chh)(e,n.stateConfig,l)})})}function eU(){let[e]=(0,E.DF)();return(0,p.useEffect)(()=>e.registerMutationListener(c.Ey8,t=>{e.getEditorState().read(()=>{for(let[l,n]of t){if("destroyed"===n)continue;let t=(0,c.nsf)(l),r=e.getElementByKey(l);if(!t||!r)continue;let o=Object.create(null);eV.forEach((e,l)=>{var n;let i=(0,c.I8X)(t,e.stateConfig);if(!i)return void delete r.dataset[l];r.dataset[l]=i;let a=null==(n=e.stateValues[i])?void 0:n.css;a&&Object.assign(o,a)}),r.style.cssText="",Object.assign(r.style,o)}})}),[e]),null}var eW=e=>{let t=[];for(let l in e.state){let n=e.state[l];for(let e in n){let r=n[e];t.push({ChildComponent:()=>(0,h.jsx)(eJ,{css:r.css}),key:e,label:r.label,onSelect:t=>{let{editor:n}=t;eK(n,l,e)}})}}return[{type:"dropdown",ChildComponent:()=>(0,h.jsx)(eJ,{css:{color:"var(--theme-elevation-600)"}}),items:[{ChildComponent:()=>(0,h.jsx)(eJ,{}),key:"clear-style",label:"Default style",onSelect:t=>{let{editor:l}=t;for(let t in e.state)eK(l,t,void 0)},order:1},...t],key:"textState",order:30}]},eZ=V(e=>{let{props:t}=e;return function(e){for(let t in e){let l=e[t],n=(0,c.urt)(t,{parse:e=>"string"==typeof e&&Object.keys(l).includes(e)?e:void 0});eV.set(t,{stateConfig:n,stateValues:l})}}(t.state),{plugins:[{Component:eU,position:"normal"}],toolbarFixed:{groups:eW(t)},toolbarInline:{groups:eW(t)}}}),eG=e=>(t,l,n)=>{let r=e(n);r&&(r.append(...l),t.replace(r),r.select(0,0))},eq=e=>{let t=new RegExp("^(".concat(e.map(e=>Number(e.slice(1))).map(e=>"#{".concat(e,"}")).join("|"),")\\s"));return{type:"element",dependencies:[f.jL],export:(e,t)=>{if(!(0,f.Pi)(e))return null;let l=Number(e.getTag().slice(1));return"#".repeat(l)+" "+t(e)},regExp:t,replace:eG(e=>{var t;let l="h"+(null==(t=e[1])?void 0:t.length);return(0,f.fi)(l)})}},eY=e=>{let t=(0,c.vJq)();(0,m.zI)(t,()=>(0,f.fi)(e))},eX={h1:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M4.639 13.5V7.074H6.196V9.648H9.076V7.074H10.642V13.5H9.076V10.836H6.196V13.5H4.639ZM11.5656 9.045V8.019C12.6636 8.019 13.1316 7.731 13.2846 7.065H14.4006V13.5H12.8436V9.045H11.5656Z",fill:"currentColor"})}),h2:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M4.139 13.5V7.074H5.696V9.648H8.576V7.074H10.142V13.5H8.576V10.836H5.696V13.5H4.139ZM15.9796 8.973C15.9796 10.116 15.1696 10.656 14.0356 11.232C13.2256 11.646 12.8206 11.943 12.7846 12.294H15.9886V13.5H11.0566V12.951C11.0566 11.601 12.1636 10.845 13.1176 10.287C14.0356 9.756 14.5126 9.486 14.5126 8.946C14.5126 8.46 14.2156 8.145 13.6306 8.145C13.0186 8.145 12.6586 8.613 12.6226 9.198H11.1196C11.2186 7.947 12.1006 6.966 13.6396 6.966C15.0346 6.966 15.9796 7.785 15.9796 8.973Z",fill:"currentColor"})}),h3:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M4.139 13.5V7.074H5.696V9.648H8.576V7.074H10.142V13.5H8.576V10.836H5.696V13.5H4.139ZM16.1146 11.745C16.1146 12.744 15.2236 13.608 13.6126 13.608C12.0736 13.608 11.0926 12.762 10.9846 11.547H12.4696C12.5146 12.114 13.0006 12.456 13.6126 12.456C14.2876 12.456 14.6746 12.132 14.6746 11.619C14.6746 11.061 14.2426 10.836 13.6216 10.836H12.9826V9.738H13.6036C14.1526 9.738 14.5486 9.486 14.5486 8.937C14.5486 8.46 14.2156 8.127 13.6486 8.127C13.0366 8.127 12.6586 8.514 12.6226 9.045H11.1916C11.2726 7.929 12.1276 6.966 13.6666 6.966C15.1876 6.966 15.9706 7.848 15.9706 8.865C15.9706 9.603 15.5026 10.143 14.8186 10.269C15.6196 10.404 16.1146 10.971 16.1146 11.745Z",fill:"currentColor"})}),h4:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M3.639 13.5V7.074H5.196V9.648H8.076V7.074H9.642V13.5H8.076V10.836H5.196V13.5H3.639ZM15.1736 7.074V10.854H16.3706V12.033H15.1736V13.5H13.6796V12.033H10.5116V10.845L13.4996 7.074H15.1736ZM13.6796 8.46L11.8256 10.854H13.6796V8.46Z",fill:"currentColor"})}),h5:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M3.639 13.5V7.074H5.196V9.648H8.076V7.074H9.642V13.5H8.076V10.836H5.196V13.5H3.639ZM13.1576 10.269C12.6896 10.269 12.3746 10.494 12.2216 10.737H10.8176L11.1956 7.074H15.2546V8.28H12.3206L12.1856 9.549C12.4016 9.351 12.8516 9.126 13.4636 9.126C14.7866 9.126 15.6596 10.053 15.6596 11.358C15.6596 12.609 14.7326 13.608 13.1756 13.608C11.5826 13.608 10.6556 12.753 10.5566 11.511H12.1136C12.1586 12.06 12.5456 12.465 13.1576 12.465C13.8236 12.465 14.1746 11.97 14.1746 11.376C14.1746 10.764 13.8416 10.269 13.1576 10.269Z",fill:"currentColor"})}),h6:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M3.639 13.5V7.074H5.196V9.648H8.076V7.074H9.642V13.5H8.076V10.836H5.196V13.5H3.639ZM13.3646 8.127C12.5456 8.127 12.0416 8.937 12.0416 9.999C12.3296 9.54 12.8246 9.207 13.5536 9.207C14.8586 9.207 15.8036 10.134 15.8036 11.376C15.8036 12.645 14.8226 13.608 13.3196 13.608C11.7266 13.608 10.6196 12.393 10.6196 10.395C10.6196 8.316 11.7716 6.966 13.4186 6.966C14.7056 6.966 15.5786 7.749 15.7316 8.829H14.3186C14.2016 8.415 13.9226 8.127 13.3646 8.127ZM13.3106 12.51C13.9586 12.51 14.3816 12.042 14.3816 11.385C14.3816 10.737 13.9586 10.278 13.3106 10.278C12.6536 10.278 12.2126 10.737 12.2126 11.385C12.2126 12.042 12.6536 12.51 13.3106 12.51Z",fill:"currentColor"})})},e$=V(e=>{let{props:t}=e,{enabledHeadingSizes:l=["h1","h2","h3","h4","h5","h6"]}=t,n=[G(l.map((e,t)=>({ChildComponent:eX[e],isActive:t=>{let{selection:l}=t;if(!(0,c.I2P)(l))return!1;for(let t of l.getNodes()){if((0,f.Pi)(t)&&t.getTag()===e)continue;let l=t.getParent();if(!((0,f.Pi)(l)&&l.getTag()===e))return!1}return!0},key:e,label:t=>{let{i18n:l}=t;return l.t("lexical:heading:label",{headingLevel:e.charAt(1)})},onSelect:t=>{let{editor:l}=t;l.update(()=>{eY(e)})},order:t+2})))];return{markdownTransformers:[eq(l)],nodes:[f.jL],sanitizedClientFeatureProps:t,slashMenu:{groups:(null==l?void 0:l.length)?[z(l.map(e=>({Icon:eX[e],key:"heading-".concat(e.charAt(1)),keywords:["heading",e],label:t=>{let{i18n:l}=t;return l.t("lexical:heading:label",{headingLevel:e.charAt(1)})},onSelect:t=>{let{editor:l}=t;l.update(()=>{eY(e)})}})))]:[]},toolbarFixed:{groups:(null==l?void 0:l.length)?n:[]},toolbarInline:{groups:(null==l?void 0:l.length)?n:[]}}}),eQ=()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("rect",{fill:"currentColor",height:"1",width:"12",x:"4",y:"9.5"})}),e0=()=>(0,h.jsxs)("svg",{fill:"none",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M5 10h10",stroke:"currentColor"}),(0,h.jsx)("path",{d:"M10 15V5",stroke:"currentColor"})]}),e1=e=>({type:"dropdown",ChildComponent:e0,items:e,key:"add",order:10}),e2=(0,c.guI)("INSERT_HORIZONTAL_RULE_COMMAND"),e5=class extends c.Kp7{static clone(e){return new this(e.__key)}static getType(){return"horizontalrule"}static importDOM(){return{hr:()=>({conversion:e6,priority:0})}}static importJSON(e){return e3()}createDOM(e){let t=document.createElement("hr");return(0,L.ZB)(t,e.theme.hr),t}decorate(){return null}exportDOM(){return{element:document.createElement("hr")}}exportJSON(){return{type:"horizontalrule",version:1}}getTextContent(){return"\n"}isInline(){return!1}updateDOM(){return!1}};function e6(){return{node:e3()}}function e3(){return(0,c.pTq)(new e5)}var e4=class extends e5{static clone(e){return super.clone(e)}static getType(){return super.getType()}static importJSON(e){return e8()}decorate(){return null}exportJSON(){return super.exportJSON()}};function e8(){return(0,c.pTq)(new e4)}function e7(e){return e instanceof e4}function e9(e){let t=(0,c.vJq)();if(!(0,c.I2P)(t))return!1;if(null!==t.focus.getNode()){let e=e8();(0,L.Pe)(e)}return!0}var te=V({markdownTransformers:[{type:"element",dependencies:[e4],export:(e,t)=>e7(e)?"---":null,regExp:/^---\s*$/,replace:e=>{let t=e8();t&&e.replace(t)}}],nodes:[e4],plugins:[{Component:()=>{let e=(0,T.c)(3),[t]=(0,E.DF)(),l,n;return e[0]!==t?(l=()=>t.registerCommand(e2,e9,c.jZM),n=[t],e[0]=t,e[1]=l,e[2]=n):(l=e[1],n=e[2]),(0,p.useEffect)(l,n),null},position:"normal"}],slashMenu:{groups:[z([{Icon:eQ,key:"horizontalRule",keywords:["hr","horizontal rule","line","separator"],label:e=>{let{i18n:t}=e;return t.t("lexical:horizontalRule:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(e2,void 0)}}])]},toolbarFixed:{groups:[e1([{ChildComponent:eQ,isActive:e=>{let{selection:t}=e;return!!(0,c.RTZ)(t)&&!!t.getNodes().length&&e7(t.getNodes()[0])},key:"horizontalRule",label:e=>{let{i18n:t}=e;return t.t("lexical:horizontalRule:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(e2,void 0)}}])]}}),tt=()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M2.5 5H10.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M2.5 10H10.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M2.5 15H17.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M12.25 7.25L17.25 3.75V10.75L12.25 7.25Z",fill:"currentColor"})]}),tl=()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M17.5 5H9.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M17.5 10H9.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M17.5 15H2.5",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M7.75 7.25L2.75 3.75V10.75L7.75 7.25Z",fill:"currentColor"})]}),tn=e=>{let{clientProps:t}=e,[l]=(0,E.DF)(),{disabledNodes:n,disableTabNode:r}=t;return(0,p.useEffect)(()=>{if(!(!l||!(null==n?void 0:n.length)))return(0,L.Sd)(l.registerCommand(c.PiT,()=>(function(e){let t=(0,c.vJq)();if(!(0,c.I2P)(t))return!1;let l=new Set,n=t.getNodes();for(let t=0;t<n.length;t++){let r=n[t],o=r.getKey();if(l.has(o))continue;let i=(0,L.Bt)(r,e=>(0,c.ff4)(e)&&!e.isInline());if(null===i)continue;let a=i.getKey();i.canIndent()&&!l.has(a)&&(l.add(a),e(i))}return l.size>0})(e=>{if(!n.includes(e.getType())){let t=e.getIndent();e.setIndent(t+1)}}),c.AcJ),l.registerUpdateListener(e=>{let{dirtyElements:t,editorState:r}=e;l.update(()=>{for(let[e]of t){let t=r._nodeMap.get(e);(0,c.ff4)(t)&&n.includes(t.getType())&&t.getIndent()>0&&t.setIndent(0)}})}))},[l,n]),(0,p.useEffect)(()=>{if(!(!l||!r))return(0,L.Sd)(l.registerCommand(c.OXk,e=>(e.preventDefault(),l.dispatchCommand(e.shiftKey?c.sik:c.PiT,void 0)),c.AcJ),l.registerNodeTransform(c.MZu,e=>{e.remove()}))},[l,r]),(0,h.jsx)(I.m,{})},tr=e=>({type:"buttons",items:e,key:"indent",order:35}),to=e=>{let{disabledNodes:t}=e;return[tr([{ChildComponent:tt,isActive:()=>!1,isEnabled:e=>{var t;let{selection:l}=e,n=null!=(t=null==l?void 0:l.getNodes())?t:[],r=e=>ta(e)&&e.getIndent()>0;return n.some(e=>r(e)||!!(0,L.Bt)(e,r))},key:"indentDecrease",label:e=>{let{i18n:t}=e;return t.t("lexical:indent:decreaseLabel")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.sik,void 0)},order:1},{ChildComponent:tl,isActive:()=>!1,isEnabled:e=>{var l;let{selection:n}=e,r=null!=(l=null==n?void 0:n.getNodes())?l:[],o=e=>ta(e)&&!(null!=t?t:[]).includes(e.getType());return r.some(e=>o(e)||!!(0,L.Bt)(e,o))},key:"indentIncrease",label:e=>{let{i18n:t}=e;return t.t("lexical:indent:increaseLabel")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(c.PiT,void 0)},order:2}])]},ti=V(e=>{var t;let{props:l}=e,n=null!=(t=l.disabledNodes)?t:[];return{plugins:[{Component:tn,position:"normal"}],sanitizedClientFeatureProps:l,toolbarFixed:{groups:to({disabledNodes:n})},toolbarInline:{groups:to({disabledNodes:n})}}}),ta=e=>(0,c.ff4)(e)&&e.canIndent();function ts(e){let{anchor:t}=e,{focus:l}=e,n=e.anchor.getNode(),r=e.focus.getNode();return n===r?n:e.isBackward()?(0,m.Nx)(l)?n:r:(0,m.Nx)(t)?n:r}var td=e=>({type:"buttons",items:e,key:"features",order:50}),tu=new Set(["http:","https:","mailto:","sms:","tel:"]),tc=class e extends c.fGB{static clone(t){return new e({id:t.__id,fields:t.__fields,key:t.__key})}static getType(){return"link"}static importDOM(){return{a:e=>({conversion:th,priority:1})}}static importJSON(e){var t,l,n,r,o;let i=tp({}).updateFromJSON(e);return 1===e.version&&"object"==typeof(null==(l=e.fields)||null==(t=l.doc)?void 0:t.value)&&(null==(o=e.fields)||null==(r=o.doc)||null==(n=r.value)?void 0:n.id)&&(e.fields.doc.value=e.fields.doc.value.id,e.version=2),2!==e.version||e.id||(e.id=new b.default().toHexString(),e.version=3),i}canBeEmpty(){return!1}canInsertTextAfter(){return!1}canInsertTextBefore(){return!1}createDOM(e){var t,l,n,r,o,i;let a=document.createElement("a");return(null==(t=this.__fields)?void 0:t.linkType)==="custom"&&(a.href=this.sanitizeUrl(null!=(o=this.__fields.url)?o:"")),null!=(i=null==(l=this.__fields)?void 0:l.newTab)&&i&&(a.target="_blank"),(null==(n=this.__fields)?void 0:n.newTab)===!0&&(null==(r=this.__fields)?void 0:r.linkType)==="custom"&&(a.rel=tb(a.rel,"add","noopener")),(0,L.ZB)(a,e.theme.link),a}exportJSON(){let e=this.getFields();(null==e?void 0:e.linkType)==="internal"?delete e.url:(null==e?void 0:e.linkType)==="custom"&&delete e.doc;let t={...super.exportJSON(),type:"link",fields:e,version:3},l=this.getID();return l&&(t.id=l),t}extractWithChild(e,t,l){if(!(0,c.I2P)(t))return!1;let n=t.anchor.getNode(),r=t.focus.getNode();return this.isParentOf(n)&&this.isParentOf(r)&&t.getTextContent().length>0}getFields(){return this.getLatest().__fields}getID(){return this.getLatest().__id}insertNewAfter(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],l=this.getParentOrThrow().insertNewAfter(e,t);if((0,c.ff4)(l)){let e=tp({fields:this.__fields});return l.append(e),e}return null}isInline(){return!0}sanitizeUrl(e){try{let t=new URL(e);if(!tu.has(t.protocol))return"about:blank"}catch(e){return"https://"}return e}setFields(e){let t=this.getWritable();return t.__fields=e,t}setID(e){let t=this.getWritable();return t.__id=e,t}updateDOM(e,t,l){var n,r,o,i,a,s,d,u;let c=null==(n=this.__fields)?void 0:n.url,h=null==(r=this.__fields)?void 0:r.newTab;return null!=c&&c!==(null==(o=e.__fields)?void 0:o.url)&&(null==(i=this.__fields)?void 0:i.linkType)==="custom"&&(t.href=c),(null==(a=this.__fields)?void 0:a.linkType)==="internal"&&(null==(s=e.__fields)?void 0:s.linkType)==="custom"&&t.removeAttribute("href"),null==t.rel&&(t.rel=""),h!==(null==(d=e.__fields)?void 0:d.newTab)&&(null!=h&&h?(t.target="_blank",(null==(u=this.__fields)?void 0:u.linkType)==="custom"&&(t.rel=tb(t.rel,"add","noopener"))):(t.removeAttribute("target"),t.rel=tb(t.rel,"remove","noopener"))),!1}updateFromJSON(e){return super.updateFromJSON(e).setFields(e.fields).setID(e.id)}constructor({id:e,fields:t={linkType:"custom",newTab:!1},key:l}){super(l),this.__fields=t,this.__id=e}};function th(e){let t=null;if((0,c.nYP)(e)){var l;let n=e.textContent;null!==n&&""!==n&&(t=tp({id:new b.default().toHexString(),fields:{doc:null,linkType:"custom",newTab:"_blank"===e.getAttribute("target"),url:null!=(l=e.getAttribute("href"))?l:""}}))}return{node:t}}function tp(e){let{id:t,fields:l}=e;return(0,c.pTq)(new tc({id:null!=t?t:new b.default().toHexString(),fields:l}))}function tf(e){return e instanceof tc}var tm=(0,c.guI)("TOGGLE_LINK_COMMAND");function tg(e){var t;let l=(0,c.vJq)();if(!(0,c.I2P)(l)&&(null===e||!(null==(t=e.selectedNodes)?void 0:t.length)))return;let n=(0,c.I2P)(l)?l.extract():null===e?[]:e.selectedNodes;if(null===e){null==n||n.forEach(e=>{let t=e.getParent();tf(t)&&(t.getChildren().forEach(e=>{t.insertBefore(e)}),t.remove())});return}if((null==n?void 0:n.length)===1){let t=n[0],l=tf(t)?t:function(e){var t=e,l=e=>tf(e);let n=t;for(;null!==n&&!(null===(n=n.getParent())||l(n)););return n}(t);if(null!==l){l.setFields(e.fields),null!=e.text&&e.text!==l.getTextContent()&&(l.append((0,c.sTu)(e.text)),l.getChildren().forEach(e=>{e!==l.getLastChild()&&e.remove()}));return}}let r=null,o=null;null==n||n.forEach(t=>{let l=t.getParent();if(!(l===o||null===l||(0,c.ff4)(t)&&!t.isInline())){if(tf(l)){o=l,l.setFields(e.fields),null!=e.text&&e.text!==l.getTextContent()&&(l.append((0,c.sTu)(e.text)),l.getChildren().forEach(e=>{e!==l.getLastChild()&&e.remove()}));return}if(l.is(r)||(r=l,o=tp({fields:e.fields}),tf(l)?null===t.getPreviousSibling()?l.insertBefore(o):l.insertAfter(o):t.insertBefore(o)),tf(t)){if(t.is(o))return;if(null!==o){let e=t.getChildren();o.append(...e)}t.remove();return}null!==o&&o.append(t)}})}function tb(e,t,l){let n,r="".concat(e);if("add"===t){if(r.includes(l)){let e=RegExp(l,"g");r=r.replace(e,"").trim()}n=0===(r=r.trim()).length?"".concat(l):"".concat(r," ").concat(l)}else{let e=RegExp(l,"g");n=r.replace(e,"").trim()}return n}var tv={type:"text-match",dependencies:[tc],export:(e,t)=>{if(!tf(e))return null;let{url:l}=e.getFields();return"[".concat(t(e),"](").concat(l,")")},importRegExp:/\[([^[]+)\]\(([^()\s]+)(?:\s"((?:[^"]*\\")*[^"]*)"\s*)?\)/,regExp:/\[([^[]+)\]\(([^()\s]+)(?:\s"((?:[^"]*\\")*[^"]*)"\s*)?\)$/,replace:(e,t)=>{let[,l,n]=t,r=tp({fields:{doc:null,linkType:"custom",newTab:!1,url:n}}),o=(0,c.sTu)(l);return o.setFormat(e.getFormat()),r.append(o),e.replace(r),o},trigger:")"},tx=class e extends tc{static clone(t){return new e({id:"",fields:t.__fields,key:t.__key})}static getType(){return"autolink"}static importDOM(){return null}static importJSON(e){var t,l,n,r,o;let i=tC({}).updateFromJSON(e);return 1===e.version&&"object"==typeof(null==(l=e.fields)||null==(t=l.doc)?void 0:t.value)&&(null==(o=e.fields)||null==(r=o.doc)||null==(n=r.value)?void 0:n.id)&&(e.fields.doc.value=e.fields.doc.value.id,e.version=2),i}exportJSON(){let e=super.exportJSON();return{type:"autolink",children:e.children,direction:e.direction,fields:e.fields,format:e.format,indent:e.indent,version:2}}insertNewAfter(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],l=this.getParentOrThrow().insertNewAfter(e,t);if((0,c.ff4)(l)){let e=tC({fields:this.__fields});return l.append(e),e}return null}updateFromJSON(e){return super.updateFromJSON(e).setFields(e.fields)}};function tC(e){let{fields:t}=e;return(0,c.pTq)(new tx({id:"",fields:t}))}function tk(e){return e instanceof tx}function ty(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return l=>{let n=e.exec(l);return null===n?null:{index:n.index,length:n[0].length,text:n[0],url:t(n[0])}}}function tw(e,t){for(let l of t){let t=l(e);if(null!=t)return t}return null}var t_=/[.,;\s]/;function tE(e){return void 0!==e&&t_.test(e)}function tS(e){return tE(e[e.length-1])}function tT(e){let t=e.getPreviousSibling();return(0,c.ff4)(t)&&(t=t.getLastDescendant()),null===t||(0,c.wH$)(t)||(0,c.kFe)(t)&&tS(t.getTextContent())}function tL(e){let t=e.getNextSibling();return(0,c.ff4)(t)&&(t=t.getFirstDescendant()),null===t||(0,c.wH$)(t)||(0,c.kFe)(t)&&tE(t.getTextContent()[0])}function tN(e,t,l){var n,r,o,i,a,s,d;let u=e.getChildren(),h=u.length;for(let t=0;t<h;t++){let n=u[t];if(!(0,c.kFe)(n)||!n.isSimpleText()){tj(e),l(null,null!=(o=null==(r=e.getFields())?void 0:r.url)?o:null);return}}let p=e.getTextContent(),f=tw(p,t);if(null===f||f.text!==p){tj(e),l(null,null!=(a=null==(i=e.getFields())?void 0:i.url)?a:null);return}if(!tT(e)||!tL(e)){tj(e),l(null,null!=(d=null==(s=e.getFields())?void 0:s.url)?d:null);return}let m=null==(n=e.getFields())?void 0:n.url;if(m!==(null==f?void 0:f.url)){let t=e.getFields();t.url=null==f?void 0:f.url,e.setFields(t),l(f.url,null!=m?m:null)}}function tj(e){let t=e.getChildren(),l=t.length;for(let n=l-1;n>=0;n--)e.insertAfter(t[n]);return e.remove(),t.map(e=>e.getLatest())}var tI=[ty(RegExp("((https?:\\/\\/(www\\.)?)|(www\\.))[-\\w@:%.+~#=]{1,256}\\.[a-zA-Z\\d()]{1,6}\\b([-\\w()@:%+.~#?&/=]*)(?<![-.+():%])"),e=>e.startsWith("http")?e:"https://".concat(e)),ty(/(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\])|(([a-z\-\d]+\.)+[a-z]{2,}))/i,e=>"mailto:".concat(e))],tM=()=>{let[e]=(0,E.DF)();return function(e,t,l){let n=(0,T.c)(5),r,o;n[0]!==e||n[1]!==t||void 0!==n[2]?(r=()=>{if(!e.hasNodes([tx]))throw Error("LexicalAutoLinkPlugin: AutoLinkNode not registered on editor");let l=(e,t)=>{};return(0,L.Sd)(e.registerNodeTransform(c.Ey8,e=>{let n=e.getParentOrThrow(),r=e.getPreviousSibling();tk(n)?tN(n,t,l):tf(n)||(e.isSimpleText()&&(tE(e.getTextContent()[0])||!tk(r))&&function(e,t,l){let n=[...e],r=n.map(e=>e.getTextContent()).join(""),o=r,i,a=0;for(;null!=(i=tw(o,t))&&null!==i;){var s,d,u;let e=i.index,t=e+i.length;if(s=a+e,d=a+t,u=n,(s>0?!!tE(r[s-1]):!!tT(u[0]))&&(d<r.length?tE(r[d]):tL(u[u.length-1]))){let[r,,o,s]=function(e,t,l){let n=[],r=[],o=[],i=0,a=0,s=[...e];for(;s.length>0;){let e=s[0],d=e.getTextContent().length,u=a;a+d<=t?(n.push(e),i+=d):u>=l?o.push(e):r.push(e),a+=d,s.shift()}return[i,n,r,o]}(n,a+e,a+t),d=function(e,t,l,n){let r=tC({fields:{linkType:"custom",url:n.url,...n.fields}});if(1===e.length){let o=e[0],i;if(0===t?[i]=o.splitText(l):[,i]=o.splitText(t,l),i){let e=(0,c.sTu)(n.text);e.setFormat(i.getFormat()),e.setDetail(i.getDetail()),e.setStyle(i.getStyle()),r.append(e),i.replace(r)}return o}if(e.length>1){let n=e[0],o=n.getTextContent().length,i;0===t?i=n:[,i]=n.splitText(t);let a=[],s;if(e.forEach(e=>{let t=e.getTextContent().length,n=o,r=o+t;if(n<l)if(r<=l)a.push(e);else{let[t,r]=e.splitText(l-n);t&&a.push(t),s=r}o+=t}),i){let e=(0,c.vJq)(),t=e?e.getNodes().find(c.kFe):void 0,l=(0,c.sTu)(i.getTextContent());return l.setFormat(i.getFormat()),l.setDetail(i.getDetail()),l.setStyle(i.getStyle()),r.append(l,...a),t&&t===i&&((0,c.I2P)(e)?l.select(e.anchor.offset,e.focus.offset):(0,c.RTZ)(e)&&l.select(0,l.getTextContent().length)),i.replace(r),s}}}(o,a+e-r,a+t-r,i);n=d?[d,...s]:s,l(i.url,null),a=0}else a+=t;o=o.substring(t)}}(function(e){let t=[e],l=e.getNextSibling();for(;null!==l&&(0,c.kFe)(l)&&l.isSimpleText()&&(t.push(l),!/\s/.test(l.getTextContent()));)l=l.getNextSibling();return t}(e),t,l),!function(e,t,l){var n,r,o,i,a,s,d,u;let c=e.getPreviousSibling(),h=e.getNextSibling(),p=e.getTextContent();if(tk(c)){let n=null!=(r=c.getFields())&&!!r.url&&null!=(s=null==(i=c.getFields())||null==(o=i.url)?void 0:o.startsWith("mailto:"))&&s;(!tE(p[0])||(n?/^\.[a-z]{2,}/i.test(p):/^\.[a-z0-9]+/i.test(p)))&&(c.append(e),tN(c,t,l),l(null,null!=(d=null==(a=c.getFields())?void 0:a.url)?d:null))}tk(h)&&!tS(p)&&(tj(h),tN(h,t,l),l(null,null!=(u=null==(n=h.getFields())?void 0:n.url)?u:null))}(e,t,l))}))},o=[e,t,l],n[0]=e,n[1]=t,n[2]=l,n[3]=r,n[4]=o):(r=n[3],o=n[4]),(0,p.useEffect)(r,o)}(e,tI),null},tF=()=>(0,h.jsx)(M.g,{});function tA(e,t,l){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:10,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:5,o=l.parentElement;if(null===e||null==o){t.style.opacity="0",t.style.transform="translate(-10000px, -10000px)";return}let i=t.getBoundingClientRect(),a=l.getBoundingClientRect(),s=o.getBoundingClientRect(),d=e.top-n,u=e.left-r;d<s.top&&(d+=i.height+e.height+2*n),u+i.width>s.right&&(u=s.right-i.width-r),d-=a.top,u-=a.left,t.style.opacity="1",t.style.transform="translate(".concat(u,"px, ").concat(d,"px)")}var tR=(0,c.guI)("TOGGLE_LINK_WITH_MODAL_COMMAND");function tP(e){e.preventDefault()}function tH(e){let{anchorElem:t}=e,[l]=(0,E.DF)(),[n,r]=(0,p.useState)(),o=(0,p.useRef)(null),[s,d]=(0,p.useState)(null),[f,m]=(0,p.useState)(null),{fieldProps:{schemaPath:b},uuid:v}=(0,u.b)(),{config:w,getEntityConfig:_}=(0,C.b)(),{i18n:S,t:T}=(0,k.d)(),[N,j]=(0,p.useState)(),I=(0,x.useEditDepth)(),[M,F]=(0,p.useState)(!1),[A,R]=(0,p.useState)([]),P=(0,x.useLocale)(),[H,B]=(0,p.useState)(!1),D=(0,x.formatDrawerSlug)({slug:"lexical-rich-text-link-"+v,depth:I}),{toggleDrawer:O}=(0,a.a)(D),z=(0,p.useCallback)(()=>{F(!1),o&&o.current&&(o.current.style.opacity="0",o.current.style.transform="translate(-10000px, -10000px)"),B(!1),d(null),m(null),R([]),j(void 0)},[F,d,m,R]),J=(0,p.useCallback)(()=>{var e,n,i,a,s,u,h,p,f;let b=(0,c.vJq)(),v;if(!(0,c.I2P)(b)||!b)return void z();let x=ts(b);v=null==(e=l.getElementByKey(x.getKey()))?void 0:e.getBoundingClientRect();let C=(0,L.Bt)(x,tf),k=b.getNodes().filter(e=>!(0,c.wH$)(e)).find(e=>{let t=(0,L.Bt)(e,tf);return C&&!C.is(t)||t&&!t.is(C)});if(null==C||k)return void z();r(C);let E=C.getFields(),N={...E,id:C.getID(),text:C.getTextContent()};if((null==E?void 0:E.linkType)==="custom")d(null!=(n=null==E?void 0:E.url)?n:null),m(null);else{d("".concat("/"===w.routes.admin?"":w.routes.admin,"/collections/").concat(null==E||null==(i=E.doc)?void 0:i.relationTo,"/").concat(null==E||null==(a=E.doc)?void 0:a.value));let e=(null==E||null==(s=E.doc)?void 0:s.relationTo)?_({collectionSlug:null==E||null==(u=E.doc)?void 0:u.relationTo}):void 0;if(e){let t="object"==typeof(null==(h=E.doc)?void 0:h.value)?E.doc.value.id:null==(p=E.doc)?void 0:p.value,l=null==(f=E.doc)?void 0:f.relationTo;if(!t||!l)throw Error("Focus link parent is missing doc.value or doc.relationTo");m(T("fields:linkedTo",{label:"".concat((0,g.s)(e.labels.singular,S)," - ").concat(T("lexical:link:loadingWithEllipsis",S))}).replace(/<[^>]*>?/g,"")),y.zG.get("".concat(w.serverURL).concat(w.routes.api,"/").concat(l,"/").concat(t),{headers:{"Accept-Language":S.language},params:{depth:0,locale:null==P?void 0:P.code}}).then(async t=>{var l;if(!t.ok)throw Error("HTTP error! Status: ".concat(t.status));let n=(await t.json())[(null==e||null==(l=e.admin)?void 0:l.useAsTitle)||"id"];m(T("fields:linkedTo",{label:"".concat((0,g.s)(e.labels.singular,S)," - ").concat(n)}).replace(/<[^>]*>?/g,""))}).catch(()=>{m(T("fields:linkedTo",{label:"".concat((0,g.s)(e.labels.singular,S)," - ").concat(T("general:untitled",S)," - ID: ").concat(t)}).replace(/<[^>]*>?/g,""))})}else m((null==E?void 0:E.label)?String(null==E?void 0:E.label):null),d((null==E?void 0:E.url)?String(null==E?void 0:E.url):null)}j(N),F(!0),R(b?null==b?void 0:b.getNodes():[]),tk(C)?B(!0):B(!1);let I=o.current,M=(0,c.peL)(l._window),{activeElement:A}=document;if(null===I)return;let H=l.getRootElement();return null!==M&&null!==H&&H.contains(M.anchorNode)?(v||(v=M.getRangeAt(0).getBoundingClientRect()),null!=v&&(v.y+=40,tA(v,I,t))):(null==A||"link-input"!==A.className)&&(null!==H&&tA(null,I,t),d(null),m(null)),!0},[l,z,w.routes.admin,w.routes.api,w.serverURL,_,T,S,null==P?void 0:P.code,t]);return(0,p.useEffect)(()=>(0,L.Sd)(l.registerCommand(tR,e=>(l.dispatchCommand(tm,e),J(),O(),!0),c.AcJ)),[l,J,O,D]),(0,p.useEffect)(()=>{let e=t.parentElement,n=()=>{l.getEditorState().read(()=>{J()})};return window.addEventListener("resize",n),null==e||e.addEventListener("scroll",n),()=>{window.removeEventListener("resize",n),null==e||e.removeEventListener("scroll",n)}},[t.parentElement,l,J]),(0,p.useEffect)(()=>(0,L.Sd)(l.registerUpdateListener(e=>{let{editorState:t}=e;t.read(()=>{J()})}),l.registerCommand(c.MvL,()=>(J(),!0),c.AcJ),l.registerCommand(c.Q$N,()=>!!M&&(z(),!0),c.WgU)),[l,J,M,z]),(0,p.useEffect)(()=>{l.getEditorState().read(()=>{J()})},[l,J]),(0,h.jsxs)(p.Fragment,{children:[(0,h.jsx)("div",{className:"link-editor",ref:o,children:(0,h.jsxs)("div",{className:"link-input",children:[s&&s.length>0?(0,h.jsxs)("a",{href:s,rel:"noopener noreferrer",target:"_blank",children:[(null==n?void 0:n.__fields.newTab)?(0,h.jsx)(x.ExternalLinkIcon,{}):null,null!=f&&f.length>0?f:s]}):null!=f&&f.length>0?(0,h.jsxs)(h.Fragment,{children:[(null==n?void 0:n.__fields.newTab)?(0,h.jsx)(x.ExternalLinkIcon,{}):null,(0,h.jsx)("span",{className:"link-input__label-pure",children:f})]}):null,l.isEditable()&&(0,h.jsxs)(p.Fragment,{children:[(0,h.jsx)("button",{"aria-label":"Edit link",className:"link-edit",onClick:e=>{e.preventDefault(),O()},onMouseDown:tP,tabIndex:0,type:"button",children:(0,h.jsx)(x.EditIcon,{})}),!H&&(0,h.jsx)("button",{"aria-label":"Remove link",className:"link-trash",onClick:()=>{l.dispatchCommand(tm,null)},onMouseDown:tP,tabIndex:0,type:"button",children:(0,h.jsx)(x.CloseMenuIcon,{})})]})]})}),(0,h.jsx)(i.a,{className:"lexical-link-edit-drawer",data:N,drawerSlug:D,drawerTitle:T("fields:editLink"),featureKey:"link",handleDrawerSubmit:(e,t)=>{let n={...t};delete n.text,l.update(()=>{var e,t;let l=(0,c.vJq)(),r=null;if((0,c.I2P)(l)?r=ts(l).getParent():A.length&&(r=null!=(t=null==(e=A[0])?void 0:e.getParent())?t:null),r&&tk(r)){let e=tp({fields:n});r.replace(e,!0)}}),l.dispatchCommand(tm,{fields:n,selectedNodes:A,text:t.text})},schemaPath:b,schemaPathSuffix:"fields"})]})}var tB=e=>{let{anchorElem:t=document.body}=e;return(0,F.createPortal)((0,h.jsx)(tH,{anchorElem:t}),t)},tD=/^(?:[a-zA-Z][a-zA-Z\d+.-]*:(?:\/\/)?(?:[-;:&=+$,\w]+@)?[A-Za-z\d]+(?:\.[A-Za-z\d]+)+|www\.[A-Za-z\d]+(?:\.[A-Za-z\d]+)+|(?:tel|mailto):[\w+.-]+)(?:\/[+~%/\w-]*)?(?:\?[-;&=%\w]*)?(?:#\w+)?$/,tO=/^(?:\/[\w\-./]*(?:#\w[\w-]*)?|#[\w\-]+)$/,tz=e=>{let t=(0,T.c)(5),{clientProps:l}=e,[n]=(0,E.DF)(),r,o;return t[0]!==l.defaultLinkType||t[1]!==l.defaultLinkURL||t[2]!==n?(r=()=>{if(!n.hasNodes([tc]))throw Error("LinkPlugin: LinkNode not registered on editor");return(0,L.Sd)(n.registerCommand(tm,e=>{var t,n;return null===e?tg(null):((null==(t=e.fields)?void 0:t.linkType)||(e.fields.linkType=l.defaultLinkType),(null==(n=e.fields)?void 0:n.url)||(e.fields.url=l.defaultLinkURL),tg(e)),!0},c.AcJ),n.registerCommand(c.waI,e=>{let t=(0,c.vJq)();if(!(0,c.I2P)(t)||t.isCollapsed()||!(e instanceof ClipboardEvent)||null==e.clipboardData)return!1;let l=e.clipboardData.getData("text");return!!function(e){if(!e)return!1;if("https://"===e||tD.test(e)||tO.test(e))return!0;try{return new URL(e),!0}catch(e){}return!1}(l)&&!t.getNodes().some(tJ)&&(n.dispatchCommand(tm,{fields:{doc:null,linkType:"custom",newTab:!1,url:l},text:null}),e.preventDefault(),!0)},c.AcJ))},o=[l.defaultLinkType,l.defaultLinkURL,n],t[0]=l.defaultLinkType,t[1]=l.defaultLinkURL,t[2]=n,t[3]=r,t[4]=o):(r=t[3],o=t[4]),(0,p.useEffect)(r,o),null};function tJ(e){return(0,c.ff4)(e)}var tV=[td([{ChildComponent:()=>(0,h.jsx)("svg",{"aria-hidden":"true",className:"icon",fill:"none",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{d:"M8.5 11.5L11.5 8.5M8.5 7L9.625 5.875C10.868 4.633 12.882 4.633 14.125 5.875C15.368 7.118 15.368 9.133 14.125 10.375L13 11.5M7 8.5L5.746 9.754C4.56 10.94 4.519 12.85 5.652 14.087C6.814 15.354 8.78 15.449 10.058 14.298L11.5 13",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"})}),isActive:e=>{let{selection:t}=e;if((0,c.I2P)(t)){let e=ts(t);return null!=(0,L.Bt)(e,tf)}return!1},isEnabled:e=>{var t,l;let{selection:n}=e;return!!((0,c.I2P)(n)&&(null==(l=(0,c.vJq)())||null==(t=l.getTextContent())?void 0:t.length))},key:"link",label:e=>{let{i18n:t}=e;return t.t("lexical:link:label")},onSelect:e=>{let{editor:t,isActive:l}=e;if(l)t.dispatchCommand(tm,null);else{let e,l=[];if(t.getEditorState().read(()=>{var t,n,r;e=null==(t=(0,c.vJq)())?void 0:t.getTextContent(),l=null!=(r=null==(n=(0,c.vJq)())?void 0:n.getNodes())?r:[]}),!(null==e?void 0:e.length))return;t.dispatchCommand(tR,{fields:{doc:null},selectedNodes:l,text:e})}},order:1}])],tK=V(e=>{let{props:t}=e;return{markdownTransformers:[tv],nodes:[tc,(null==t?void 0:t.disableAutoLinks)===!0?null:tx].filter(Boolean),plugins:[{Component:tz,position:"normal"},(null==t?void 0:t.disableAutoLinks)===!0||(null==t?void 0:t.disableAutoLinks)==="creationOnly"?null:{Component:tM,position:"normal"},{Component:tF,position:"normal"},{Component:tB,position:"floatingAnchorElem"}].filter(Boolean),sanitizedClientFeatureProps:t,toolbarFixed:{groups:tV},toolbarInline:{groups:tV}}}),tU=()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("rect",{height:"13",rx:"1.5",stroke:"currentColor",width:"13",x:"3.5",y:"3.5"}),(0,h.jsx)("path",{d:"M7 10L9 12.5L13 7.5",stroke:"currentColor",strokeWidth:"1.5"})]}),tW=()=>(0,h.jsx)(R.Q,{});function tZ(e){return{items:e,key:"lists",label:e=>{let{i18n:t}=e;return t.t("lexical:general:slashMenuListGroupLabel")}}}var tG=e=>(t,l,n)=>{let r=t.getPreviousSibling(),o=t.getNextSibling(),i=(0,A.i)("check"===e?"x"===n[3]:void 0);if((0,A.HY)(o)&&o.getListType()===e){let e=o.getFirstChild();null!==e?e.insertBefore(i):o.append(i),t.remove()}else if((0,A.HY)(r)&&r.getListType()===e)r.append(i),t.remove();else{let l=(0,A.DE)(e,"number"===e?Number(n[2]):void 0);l.append(i),t.replace(l)}i.append(...l),i.select(0,0);let a=Math.floor(n[1].length/4);a&&i.setIndent(a)},tq=(e,t,l)=>{let n=[],r=e.getChildren(),o=0;for(let i of r)if((0,A.Mz)(i)){if(1===i.getChildrenSize()){let e=i.getFirstChild();if((0,A.HY)(e)){n.push(tq(e,t,l+1));continue}}let r=" ".repeat(4*l),a=e.getListType(),s="number"===a?"".concat(e.getStart()+o,". "):"check"===a?"- [".concat(i.getChecked()?"x":" ","] "):"- ";n.push(r+s+t(i)),o++}return n.join("\n")},tY={type:"element",dependencies:[A.v5,A.YW],export:(e,t)=>(0,A.HY)(e)?tq(e,t,0):null,regExp:/^(\s*)(?:-\s)?\s?(\[(\s|x)?\])\s/i,replace:tG("check")},tX=()=>(0,h.jsx)(P.A,{}),t$=[G([{ChildComponent:tU,isActive:e=>{let{selection:t}=e;if(!(0,c.I2P)(t))return!1;for(let e of t.getNodes()){if((0,A.HY)(e)&&"check"===e.getListType())continue;let t=e.getParent();if((0,A.HY)(t)&&"check"===t.getListType())continue;let l=null==t?void 0:t.getParent();if(!((0,A.HY)(l)&&"check"===l.getListType()))return!1}return!0},key:"checklist",label:e=>{let{i18n:t}=e;return t.t("lexical:checklist:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(A.mJ,void 0)},order:12}])],tQ=V(e=>{let{featureProviderMap:t}=e,l=[{Component:tX,position:"normal"}];return t.has("unorderedList")||t.has("orderedList")||l.push({Component:tW,position:"normal"}),{markdownTransformers:[tY],nodes:t.has("unorderedList")||t.has("orderedList")?[]:[A.v5,A.YW],plugins:l,slashMenu:{groups:[tZ([{Icon:tU,key:"checklist",keywords:["check list","check","checklist","cl"],label:e=>{let{i18n:t}=e;return t.t("lexical:checklist:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(A.mJ,void 0)}}])]},toolbarFixed:{groups:t$},toolbarInline:{groups:t$}}}),t0=()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M5.89284 12.479C5.89284 13.368 5.26284 13.788 4.38084 14.236C3.75084 14.558 3.43584 14.789 3.40784 15.062H5.89984V16H2.06384V15.573C2.06384 14.523 2.92484 13.935 3.66684 13.501C4.38084 13.088 4.75184 12.878 4.75184 12.458C4.75184 12.08 4.52084 11.835 4.06584 11.835C3.58984 11.835 3.30984 12.199 3.28184 12.654H2.11284C2.18984 11.681 2.87584 10.918 4.07284 10.918C5.15784 10.918 5.89284 11.555 5.89284 12.479Z",fill:"currentColor"}),(0,h.jsx)("path",{d:"M2.68608 4.535V3.737C3.54008 3.737 3.90408 3.513 4.02308 2.995H4.89108V8H3.68008L3.68008 4.535H2.68608Z",fill:"currentColor"}),(0,h.jsx)("path",{d:"M8 15L17 15",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M8 10L17 10",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M8 5L17 5",stroke:"currentColor",strokeWidth:"1.5"})]}),t1={type:"element",dependencies:[A.v5,A.YW],export:(e,t)=>(0,A.HY)(e)?tq(e,t,0):null,regExp:/^(\s*)(\d+)\.\s/,replace:tG("number")},t2=[G([{ChildComponent:t0,isActive:e=>{let{selection:t}=e;if(!(0,c.I2P)(t))return!1;for(let e of t.getNodes()){if((0,A.HY)(e)&&"number"===e.getListType())continue;let t=e.getParent();if((0,A.HY)(t)&&"number"===t.getListType())continue;let l=null==t?void 0:t.getParent();if(!((0,A.HY)(l)&&"number"===l.getListType()))return!1}return!0},key:"orderedList",label:e=>{let{i18n:t}=e;return t.t("lexical:orderedList:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(A.x,void 0)},order:10}])],t5=V(e=>{let{featureProviderMap:t}=e;return{markdownTransformers:[t1],nodes:t.has("orderedList")?[]:[A.v5,A.YW],plugins:t.has("orderedList")?[]:[{Component:tW,position:"normal"}],slashMenu:{groups:[tZ([{Icon:t0,key:"orderedList",keywords:["ordered list","ol"],label:e=>{let{i18n:t}=e;return t.t("lexical:orderedList:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(A.x,void 0)}}])]},toolbarFixed:{groups:t2},toolbarInline:{groups:t2}}}),t6=()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("circle",{cx:"4",cy:"5",fill:"currentColor",r:"1.15",stroke:"currentColor",strokeWidth:"0.3"}),(0,h.jsx)("circle",{cx:"4",cy:"10",fill:"currentColor",r:"1.15",stroke:"currentColor",strokeWidth:"0.3"}),(0,h.jsx)("circle",{cx:"4",cy:"15",fill:"currentColor",r:"1.15",stroke:"currentColor",strokeWidth:"0.3"}),(0,h.jsx)("path",{d:"M17 5H7",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M17 10H7",stroke:"currentColor",strokeWidth:"1.5"}),(0,h.jsx)("path",{d:"M17 15H7",stroke:"currentColor",strokeWidth:"1.5"})]}),t3={type:"element",dependencies:[A.v5,A.YW],export:(e,t)=>(0,A.HY)(e)?tq(e,t,0):null,regExp:/^(\s*)[-*+]\s/,replace:tG("bullet")},t4=[G([{ChildComponent:t6,isActive:e=>{let{selection:t}=e;if(!(0,c.I2P)(t))return!1;for(let e of t.getNodes()){if((0,A.HY)(e)&&"bullet"===e.getListType())continue;let t=e.getParent();if((0,A.HY)(t)&&"bullet"===t.getListType())continue;let l=null==t?void 0:t.getParent();if(!((0,A.HY)(l)&&"bullet"===l.getListType()))return!1}return!0},key:"unorderedList",label:e=>{let{i18n:t}=e;return t.t("lexical:unorderedList:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(A.q7,void 0)},order:11}])],t8=V({markdownTransformers:[t3],nodes:[A.v5,A.YW],plugins:[{Component:tW,position:"normal"}],slashMenu:{groups:[tZ([{Icon:t6,key:"unorderedList",keywords:["unordered list","ul"],label:e=>{let{i18n:t}=e;return t.t("lexical:unorderedList:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(A.q7,void 0)}}])]},toolbarFixed:{groups:t4},toolbarInline:{groups:t4}}),t7=p.lazy(()=>l.e(11).then(l.bind(l,90011)).then(e=>({default:e.UnknownConvertedNodeComponent}))),t9=class e extends c.Kp7{static clone(t){return new e({data:t.__data,key:t.__key})}static getType(){return"unknownConverted"}static importJSON(e){return function(e){let{data:t}=e;return new t9({data:t})}({data:e.data})}canInsertTextAfter(){return!0}canInsertTextBefore(){return!0}createDOM(e){let t=document.createElement("span");return(0,L.ZB)(t,"unknownConverted"),t}decorate(){return(0,h.jsx)(t7,{data:this.__data})}exportJSON(){return{type:this.getType(),data:this.__data,version:1}}isInline(){return!0}updateDOM(e,t){return!1}constructor({data:e,key:t}){super(t),this.__data=e}},le=V(()=>({nodes:[t9]})),lt=p.lazy(()=>l.e(7212).then(l.bind(l,67212)).then(e=>({default:e.UnknownConvertedNodeComponent}))),ll=class e extends c.Kp7{static clone(t){return new e({data:t.__data,key:t.__key})}static getType(){return"unknownConverted"}static importJSON(e){return function(e){let{data:t}=e;return new ll({data:t})}({data:e.data})}canInsertTextAfter(){return!0}canInsertTextBefore(){return!0}createDOM(e){let t=document.createElement("span");return(0,L.ZB)(t,"unknownConverted"),t}decorate(){return(0,h.jsx)(lt,{data:this.__data})}exportJSON(){return{type:this.getType(),data:this.__data,version:1}}isInline(){return!0}updateDOM(e,t){return!1}constructor({data:e,key:t}){super(t),this.__data=e}},ln=V(()=>({nodes:[ll]})),lr=[G([{ChildComponent:Z,isActive:e=>{let{selection:t}=e;if(!(0,c.I2P)(t))return!1;for(let e of t.getNodes())if(!(0,c.bSg)(e)&&!(0,c.bSg)(e.getParent()))return!1;return!0},key:"paragraph",label:e=>{let{i18n:t}=e;return t.t("lexical:paragraph:label2")},onSelect:e=>{let{editor:t}=e;t.update(()=>{let e=(0,c.vJq)();(0,m.zI)(e,()=>(0,c.lJ7)())})},order:1}])],lo=V({slashMenu:{groups:[z([{Icon:Z,key:"paragraph",keywords:["normal","paragraph","p","text"],label:e=>{let{i18n:t}=e;return t.t("lexical:paragraph:label")},onSelect:e=>{let{editor:t}=e;t.update(()=>{let e=(0,c.vJq)();(0,m.zI)(e,()=>(0,c.lJ7)())})}}])]},toolbarFixed:{groups:lr},toolbarInline:{groups:lr}}),li={horizontalrule:(0,h.jsx)("hr",{})},la={linebreak:(0,h.jsx)("br",{})},ls={DOM_ELEMENT_TYPE:1,DOM_TEXT_TYPE:3,NO_DIRTY_NODES:0,HAS_DIRTY_NODES:1,FULL_RECONCILE:2,IS_NORMAL:0,IS_TOKEN:1,IS_SEGMENTED:2,IS_INERT:3,IS_BOLD:1,IS_ITALIC:2,IS_STRIKETHROUGH:4,IS_UNDERLINE:8,IS_CODE:16,IS_SUBSCRIPT:32,IS_SUPERSCRIPT:64,IS_HIGHLIGHT:128,IS_DIRECTIONLESS:1,IS_UNMERGEABLE:2,IS_ALIGN_LEFT:1,IS_ALIGN_CENTER:2,IS_ALIGN_RIGHT:3,IS_ALIGN_JUSTIFY:4,IS_ALIGN_START:5,IS_ALIGN_END:6},ld=ls.IS_BOLD|ls.IS_ITALIC|ls.IS_STRIKETHROUGH|ls.IS_UNDERLINE|ls.IS_CODE|ls.IS_SUBSCRIPT|ls.IS_SUPERSCRIPT|ls.IS_HIGHLIGHT,lu="\xa0",lc="\n\n",lh="֑-߿יִ-﷽ﹰ-ﻼ",lp="A-Za-z\xc0-\xd6\xd8-\xf6\xf8-ʸ̀-֐ࠀ-῿‎Ⰰ-﬜︀-﹯﻽-￿",lf=RegExp("^[^"+lp+"]*["+lh+"]"),lm=RegExp("^[^"+lh+"]*["+lp+"]"),lg={bold:ls.IS_BOLD,code:ls.IS_CODE,highlight:ls.IS_HIGHLIGHT,italic:ls.IS_ITALIC,strikethrough:ls.IS_STRIKETHROUGH,subscript:ls.IS_SUBSCRIPT,superscript:ls.IS_SUPERSCRIPT,underline:ls.IS_UNDERLINE},lb={directionless:ls.IS_DIRECTIONLESS,unmergeable:ls.IS_UNMERGEABLE},lv={center:ls.IS_ALIGN_CENTER,end:ls.IS_ALIGN_END,justify:ls.IS_ALIGN_JUSTIFY,left:ls.IS_ALIGN_LEFT,right:ls.IS_ALIGN_RIGHT,start:ls.IS_ALIGN_START},lx={[ls.IS_ALIGN_CENTER]:"center",[ls.IS_ALIGN_END]:"end",[ls.IS_ALIGN_JUSTIFY]:"justify",[ls.IS_ALIGN_LEFT]:"left",[ls.IS_ALIGN_RIGHT]:"right",[ls.IS_ALIGN_START]:"start"},lC={normal:ls.IS_NORMAL,segmented:ls.IS_SEGMENTED,token:ls.IS_TOKEN},lk={[ls.IS_NORMAL]:"normal",[ls.IS_SEGMENTED]:"segmented",[ls.IS_TOKEN]:"token"},ly={paragraph:e=>{let{node:t,nodesToJSX:l}=e,n=l({nodes:t.children});return(null==n?void 0:n.length)?(0,h.jsx)("p",{children:n}):(0,h.jsx)("p",{children:(0,h.jsx)("br",{})})},text:e=>{let{node:t}=e,l=t.text;return t.format&ls.IS_BOLD&&(l=(0,h.jsx)("strong",{children:l})),t.format&ls.IS_ITALIC&&(l=(0,h.jsx)("em",{children:l})),t.format&ls.IS_STRIKETHROUGH&&(l=(0,h.jsx)("span",{style:{textDecoration:"line-through"},children:l})),t.format&ls.IS_UNDERLINE&&(l=(0,h.jsx)("span",{style:{textDecoration:"underline"},children:l})),t.format&ls.IS_CODE&&(l=(0,h.jsx)("code",{children:l})),t.format&ls.IS_SUBSCRIPT&&(l=(0,h.jsx)("sub",{children:l})),t.format&ls.IS_SUPERSCRIPT&&(l=(0,h.jsx)("sup",{children:l})),l},...la,quote:e=>{let{node:t,nodesToJSX:l}=e,n=l({nodes:t.children});return(0,h.jsx)("blockquote",{children:n})},table:e=>{let{node:t,nodesToJSX:l}=e,n=l({nodes:t.children});return(0,h.jsx)("div",{className:"lexical-table-container",children:(0,h.jsx)("table",{className:"lexical-table",style:{borderCollapse:"collapse"},children:(0,h.jsx)("tbody",{children:n})})})},tablecell:e=>{let{node:t,nodesToJSX:l}=e,n=l({nodes:t.children}),r=t.headerState>0?"th":"td",o="lexical-table-cell-header-".concat(t.headerState),i={backgroundColor:t.backgroundColor||void 0,border:"1px solid #ccc",padding:"8px"},a=t.colSpan&&t.colSpan>1?t.colSpan:void 0,s=t.rowSpan&&t.rowSpan>1?t.rowSpan:void 0;return(0,h.jsx)(r,{className:"lexical-table-cell ".concat(o),colSpan:a,rowSpan:s,style:i,children:n})},tablerow:e=>{let{node:t,nodesToJSX:l}=e,n=l({nodes:t.children});return(0,h.jsx)("tr",{className:"lexical-table-row",children:n})},heading:e=>{let{node:t,nodesToJSX:l}=e,n=l({nodes:t.children}),r=t.tag;return(0,h.jsx)(r,{children:n})},...li,list:e=>{let{node:t,nodesToJSX:l}=e,n=l({nodes:t.children}),r=t.tag;return(0,h.jsx)(r,{className:"list-".concat(null==t?void 0:t.listType),children:n})},listitem:e=>{let{node:t,nodesToJSX:l,parent:n}=e,r=t.children.some(e=>"list"===e.type),o=l({nodes:t.children});if(!("listType"in n)||(null==n?void 0:n.listType)!=="check")return(0,h.jsx)("li",{className:"".concat(r?"nestedListItem":""),style:r?{listStyleType:"none"}:void 0,value:null==t?void 0:t.value,children:o});{let e=(0,S.A)();return(0,h.jsx)("li",{"aria-checked":t.checked?"true":"false",className:"list-item-checkbox".concat(t.checked?" list-item-checkbox-checked":" list-item-checkbox-unchecked").concat(r?" nestedListItem":""),role:"checkbox",style:{listStyleType:"none"},tabIndex:-1,value:null==t?void 0:t.value,children:r?o:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("input",{checked:t.checked,id:e,readOnly:!0,type:"checkbox"}),(0,h.jsx)("label",{htmlFor:e,children:o}),(0,h.jsx)("br",{})]})})}},...(e=>{let{internalDocToHref:t}=e;return{autolink:e=>{let{node:t,nodesToJSX:l}=e,n=l({nodes:t.children}),r=t.fields.newTab?"noopener noreferrer":void 0,o=t.fields.newTab?"_blank":void 0;return(0,h.jsx)("a",{href:t.fields.url,rel:r,target:o,children:n})},link:e=>{var l;let{node:n,nodesToJSX:r}=e,o=r({nodes:n.children}),i=n.fields.newTab?"noopener noreferrer":void 0,a=n.fields.newTab?"_blank":void 0,s=null!=(l=n.fields.url)?l:"";return"internal"===n.fields.linkType&&(t?s=t({linkNode:n}):(console.error("Lexical => JSX converter: Link converter: found internal link, but internalDocToHref is not provided"),s="#")),(0,h.jsx)("a",{href:s,rel:i,target:a,children:o})}}})({}),upload:e=>{let{node:t}=e;if("object"!=typeof t.value)return null;let l=t.value,n=l.url;if(!l.mimeType.startsWith("image"))return(0,h.jsx)("a",{href:n,rel:"noopener noreferrer",children:l.filename});if(!l.sizes||!Object.keys(l.sizes).length)return(0,h.jsx)("img",{alt:l.filename,height:l.height,src:n,width:l.width});let r=[];for(let e in l.sizes){let t=l.sizes[e];if(!t||!t.width||!t.height||!t.mimeType||!t.filesize||!t.filename||!t.url)continue;let n=null==t?void 0:t.url;r.push((0,h.jsx)("source",{media:"(max-width: ".concat(t.width,"px)"),srcSet:n,type:t.mimeType},e))}return r.push((0,h.jsx)("img",{alt:null==l?void 0:l.filename,height:null==l?void 0:l.height,src:n,width:null==l?void 0:l.width},"image")),(0,h.jsx)("picture",{children:r})},tab:"	"},lw=e=>{let{className:t,converters:l,data:n,disableContainer:r,disableIndent:o,disableTextAlign:i}=e;if(!n)return null;let a={};a=l?"function"==typeof l?l({defaultConverters:ly}):l:ly;let s=n&&!Array.isArray(n)&&"object"==typeof n&&"root"in n&&function(e){var t;let{converters:l,data:n,disableIndent:r,disableTextAlign:o}=e;return!function(e){var t,l,n,r,o,i,a,s,d,u;let c=!!(null==e||null==(l=e.root)||null==(t=l.children)?void 0:t.length),h=!1;if((null==e||null==(r=e.root)||null==(n=r.children)?void 0:n.length)===1&&(null==e||null==(i=e.root)||null==(o=i.children[0])?void 0:o.type)==="paragraph"){let t=null==e||null==(a=e.root)?void 0:a.children[0];if((null==t?void 0:t.children)&&(null==t||null==(s=t.children)?void 0:s.length)!==0){if((null==t||null==(d=t.children)?void 0:d.length)===1){let e=null==t?void 0:t.children[0];(null==e?void 0:e.type)==="text"&&((null==e||null==(u=e.text)?void 0:u.length)||(h=!0))}}else h=!0}return!(!c||h)}(n)?(0,h.jsx)(h.Fragment,{}):function e(t){let{converters:l,disableIndent:n,disableTextAlign:r,nodes:o,parent:i}=t,a=l.unknown;return o.map((t,o)=>{var s,d,u,c,f,m,g,b;let v;"block"===t.type?(v=null==l||null==(d=l.blocks)?void 0:d[null==t||null==(s=t.fields)?void 0:s.blockType])||a||console.error("Lexical => JSX converter: Blocks converter: found ".concat(null==t||null==(u=t.fields)?void 0:u.blockType," block, but no converter is provided")):"inlineBlock"===t.type?(v=null==l||null==(f=l.inlineBlocks)?void 0:f[null==t||null==(c=t.fields)?void 0:c.blockType])||a||console.error("Lexical => JSX converter: Inline Blocks converter: found ".concat(null==t||null==(m=t.fields)?void 0:m.blockType," inline block, but no converter is provided")):v=l[t.type];try{let s;!v&&a&&(v=a),s=v?"function"==typeof v?v({childIndex:o,converters:l,node:t,nodesToJSX:o=>{var a,s,d,u;return e({converters:null!=(a=o.converters)?a:l,disableIndent:null!=(s=o.disableIndent)?s:n,disableTextAlign:null!=(d=o.disableTextAlign)?d:r,nodes:o.nodes,parent:null!=(u=o.parent)?u:{...t,parent:i}})},parent:i}):v:(0,h.jsx)("span",{children:"unknown node"},o);let d={};if(!r&&(!Array.isArray(r)||!(null==r?void 0:r.includes(t.type)))&&"format"in t&&t.format)switch(t.format){case"center":d.textAlign="center";break;case"end":case"right":d.textAlign="right";break;case"justify":d.textAlign="justify";break;case"left":break;case"start":d.textAlign="left"}if(!n&&(!Array.isArray(n)||!(null==n?void 0:n.includes(t.type)))&&"indent"in t&&t.indent&&"listitem"!==t.type&&(d.paddingInlineStart="".concat(2*Number(t.indent),"em")),p.isValidElement(s)){if(d.textAlign||d.paddingInlineStart){let e={...d,...null!=(b=null==s||null==(g=s.props)?void 0:g.style)?b:{}};return p.cloneElement(s,{key:o,style:e})}return p.cloneElement(s,{key:o})}return s}catch(e){return console.error("Error converting lexical node to JSX:",e,"node:",t),null}}).filter(Boolean)}({converters:l,disableIndent:r,disableTextAlign:o,nodes:null==n||null==(t=n.root)?void 0:t.children,parent:null==n?void 0:n.root})}({converters:a,data:n,disableIndent:o,disableTextAlign:i});return r?(0,h.jsx)(h.Fragment,{children:s}):(0,h.jsx)("div",{className:null!=t?t:"payload-richtext",children:s})},l_=V({plugins:[{Component:function(){let e,t=(0,T.c)(7),[l]=(0,E.DF)(),n;t[0]!==l?(n=l.getEditorState().toJSON(),t[0]=l,t[1]=n):n=t[1];let[r,o]=(0,p.useState)(n),i,a;return t[2]!==l?(i=()=>l.registerUpdateListener(e=>{let{editorState:t}=e;o(t.toJSON())}),a=[l],t[2]=l,t[3]=i,t[4]=a):(i=t[3],a=t[4]),(0,p.useEffect)(i,a),t[5]!==r?(e=(0,h.jsx)(lw,{converters:ly,data:r}),t[5]=r,t[6]=e):e=t[6],e},position:"bottom"}]}),lE={blue:{50:"oklch(0.97 0.014 254.604)",100:"oklch(0.932 0.032 255.585)",200:"oklch(0.882 0.059 254.128)",300:"oklch(0.809 0.105 251.813)",400:"oklch(0.707 0.165 254.624)",500:"oklch(0.623 0.214 259.815)",600:"oklch(0.546 0.245 262.881)",700:"oklch(0.488 0.243 264.376)",800:"oklch(0.424 0.199 265.638)",900:"oklch(0.379 0.146 265.522)",950:"oklch(0.282 0.091 267.935)"},green:{50:"oklch(0.982 0.018 155.826)",100:"oklch(0.962 0.044 156.743)",200:"oklch(0.925 0.084 155.995)",300:"oklch(0.871 0.15 154.449)",400:"oklch(0.792 0.209 151.711)",500:"oklch(0.723 0.219 149.579)",600:"oklch(0.627 0.194 149.214)",700:"oklch(0.527 0.154 150.069)",800:"oklch(0.448 0.119 151.328)",900:"oklch(0.393 0.095 152.535)",950:"oklch(0.266 0.065 152.934)"},orange:{50:"oklch(0.98 0.016 73.684)",100:"oklch(0.954 0.038 75.164)",200:"oklch(0.901 0.076 70.697)",300:"oklch(0.837 0.128 66.29)",400:"oklch(0.75 0.183 55.934)",500:"oklch(0.705 0.213 47.604)",600:"oklch(0.646 0.222 41.116)",700:"oklch(0.553 0.195 38.402)",800:"oklch(0.47 0.157 37.304)",900:"oklch(0.408 0.123 38.172)",950:"oklch(0.266 0.079 36.259)"},pink:{50:"oklch(0.971 0.014 343.198)",100:"oklch(0.948 0.028 342.258)",200:"oklch(0.899 0.061 343.231)",300:"oklch(0.823 0.12 346.018)",400:"oklch(0.718 0.202 349.761)",500:"oklch(0.656 0.241 354.308)",600:"oklch(0.592 0.249 0.584)",700:"oklch(0.525 0.223 3.958)",800:"oklch(0.459 0.187 3.815)",900:"oklch(0.408 0.153 2.432)",950:"oklch(0.284 0.109 3.907)"},purple:{50:"oklch(0.977 0.014 308.299)",100:"oklch(0.946 0.033 307.174)",200:"oklch(0.902 0.063 306.703)",300:"oklch(0.827 0.119 306.383)",400:"oklch(0.714 0.203 305.504)",500:"oklch(0.627 0.265 303.9)",600:"oklch(0.558 0.288 302.321)",700:"oklch(0.496 0.265 301.924)",800:"oklch(0.438 0.218 303.724)",900:"oklch(0.381 0.176 304.987)",950:"oklch(0.291 0.149 302.717)"},red:{50:"oklch(0.971 0.013 17.38)",100:"oklch(0.936 0.032 17.717)",200:"oklch(0.885 0.062 18.334)",300:"oklch(0.808 0.114 19.571)",400:"oklch(0.704 0.191 22.216)",500:"oklch(0.637 0.237 25.331)",600:"oklch(0.577 0.245 27.325)",700:"oklch(0.505 0.213 27.518)",800:"oklch(0.444 0.177 26.899)",900:"oklch(0.396 0.141 25.723)",950:"oklch(0.258 0.092 26.042)"},yellow:{50:"oklch(0.987 0.026 102.212)",100:"oklch(0.973 0.071 103.193)",200:"oklch(0.945 0.129 101.54)",300:"oklch(0.905 0.182 98.111)",400:"oklch(0.852 0.199 91.936)",500:"oklch(0.795 0.184 86.047)",600:"oklch(0.681 0.162 75.834)",700:"oklch(0.554 0.135 66.442)",800:"oklch(0.476 0.114 61.907)",900:"oklch(0.421 0.095 57.708)",950:"oklch(0.286 0.066 53.813)"}},lS={text:{"text-red":{css:{color:"light-dark(".concat(lE.red[600],", ").concat(lE.red[400],")")},label:"Red"},"text-orange":{css:{color:"light-dark(".concat(lE.orange[600],", ").concat(lE.orange[400],")")},label:"Orange"},"text-yellow":{css:{color:"light-dark(".concat(lE.yellow[700],", ").concat(lE.yellow[300],")")},label:"Yellow"},"text-green":{css:{color:"light-dark(".concat(lE.green[700],", ").concat(lE.green[400],")")},label:"Green"},"text-blue":{css:{color:"light-dark(".concat(lE.blue[600],", ").concat(lE.blue[400],")")},label:"Blue"},"text-purple":{css:{color:"light-dark(".concat(lE.purple[600],", ").concat(lE.purple[400],")")},label:"Purple"},"text-pink":{css:{color:"light-dark(".concat(lE.pink[600],", ").concat(lE.pink[400],")")},label:"Pink"}},background:{"bg-red":{css:{"background-color":"light-dark(".concat(lE.red[400],", ").concat(lE.red[600],")")},label:"Red"},"bg-orange":{css:{"background-color":"light-dark(".concat(lE.orange[400],", ").concat(lE.orange[600],")")},label:"Orange"},"bg-yellow":{css:{"background-color":"light-dark(".concat(lE.yellow[300],", ").concat(lE.yellow[700],")")},label:"Yellow"},"bg-green":{css:{"background-color":"light-dark(".concat(lE.green[400],", ").concat(lE.green[700],")")},label:"Green"},"bg-blue":{css:{"background-color":"light-dark(".concat(lE.blue[400],", ").concat(lE.blue[600],")")},label:"Blue"},"bg-purple":{css:{"background-color":"light-dark(".concat(lE.purple[400],", ").concat(lE.purple[600],")")},label:"Purple"},"bg-pink":{css:{"background-color":"light-dark(".concat(lE.pink[400],", ").concat(lE.pink[600],")")},label:"Pink"}}},lT=()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("g",{clipPath:"url(#clip0_4397_10817)",children:(0,h.jsx)("path",{d:"M7.75 12.25L15.25 4.75M15.25 4.75H11.5M15.25 4.75V8.5M13 11.5V13.75C13 14.5784 12.3284 15.25 11.5 15.25H6.25C5.42157 15.25 4.75 14.5784 4.75 13.75V8.5C4.75 7.67157 5.42157 7 6.25 7H8.5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"})}),(0,h.jsx)("defs",{children:(0,h.jsx)("clipPath",{id:"clip0_4397_10817",children:(0,h.jsx)("rect",{fill:"currentColor",height:"12",transform:"translate(4 4)",width:"12"})})})]});function lL(e){let t=e.getAttribute("data-lexical-relationship-id"),l=e.getAttribute("data-lexical-relationship-relationTo");return null!=t&&null!=l?{node:lj({relationTo:l,value:t})}:null}var lN=class extends v.d{static clone(e){return new this({data:e.__data,format:e.__format,key:e.__key})}static getType(){return"relationship"}static importDOM(){return{div:e=>e.hasAttribute("data-lexical-relationship-relationTo")&&e.hasAttribute("data-lexical-relationship-id")?{conversion:lL,priority:2}:null}}static importJSON(e){var t;1===e.version&&(null==e||null==(t=e.value)?void 0:t.id)&&(e.value=e.value.id);let l=lj({relationTo:e.relationTo,value:e.value});return l.setFormat(e.format),l}static isInline(){return!1}decorate(e,t){return null}exportDOM(){var e,t,l,n,r;let o=document.createElement("div");o.setAttribute("data-lexical-relationship-id",String("object"==typeof(null==(e=this.__data)?void 0:e.value)?null==(l=this.__data)||null==(t=l.value)?void 0:t.id:null==(n=this.__data)?void 0:n.value)),o.setAttribute("data-lexical-relationship-relationTo",null==(r=this.__data)?void 0:r.relationTo);let i=document.createTextNode(this.getTextContent());return o.append(i),{element:o}}exportJSON(){return{...super.exportJSON(),...this.getData(),type:"relationship",version:2}}getData(){return this.getLatest().__data}getTextContent(){var e,t,l,n,r;return"".concat(null==(e=this.__data)?void 0:e.relationTo," relation to ").concat("object"==typeof(null==(t=this.__data)?void 0:t.value)?null==(n=this.__data)||null==(l=n.value)?void 0:l.id:null==(r=this.__data)?void 0:r.value)}setData(e){this.getWritable().__data=e}constructor({data:e,format:t,key:l}){super(t,l),this.__data=e}};function lj(e){return new lN({data:e})}var lI=p.lazy(()=>l.e(3432).then(l.bind(l,43432)).then(e=>({default:e.RelationshipComponent})));function lM(e){let t=e.getAttribute("data-lexical-relationship-id"),l=e.getAttribute("data-lexical-relationship-relationTo");return null!=t&&null!=l?{node:lA({relationTo:l,value:t})}:null}var lF=class extends lN{static clone(e){return super.clone(e)}static getType(){return super.getType()}static importDOM(){return{div:e=>e.hasAttribute("data-lexical-relationship-relationTo")&&e.hasAttribute("data-lexical-relationship-id")?{conversion:lM,priority:2}:null}}static importJSON(e){var t;1===e.version&&(null==e||null==(t=e.value)?void 0:t.id)&&(e.value=e.value.id);let l=lA({relationTo:e.relationTo,value:e.value});return l.setFormat(e.format),l}decorate(e,t){var l;return(0,h.jsx)(lI,{className:null!=(l=t.theme.relationship)?l:"LexicalEditorTheme__relationship",data:this.__data,format:this.__format,nodeKey:this.getKey()})}exportJSON(){return super.exportJSON()}};function lA(e){return new lF({data:e})}function lR(e){return e instanceof lF}var lP=e=>{let t,l,n,r=(0,T.c)(23),[o]=(0,E.DF)(),[i,a]=(0,p.useState)(null),[s,d]=(0,p.useState)(!1),[u,f,m]=(0,x.useListDrawer)(e),{closeDrawer:g,drawerSlug:b,isDrawerOpen:v,openDrawer:C}=m,{modalState:k}=(0,x.useModal)(),y;r[0]===Symbol.for("react.memo_cache_sentinel")?(y=()=>{var e;a(null!=(e=(0,c.vJq)())?e:(0,c.S1w)())},r[0]=y):y=r[0];let w=y,_;r[1]!==o||r[2]!==i?(_=()=>{i&&o.update(()=>{if((0,c.I2P)(i)){let{anchor:e,focus:t}=i;(0,c.nsf)(e.key)&&(0,c.nsf)(t.key)&&(0,c.n1P)(i.clone())}else(0,c.NiT)().selectEnd()},{discrete:!0,skipTransforms:!0})},r[1]=o,r[2]=i,r[3]=_):_=r[3];let S=_,L;r[4]!==g?(L=()=>{g()},r[4]=g,r[5]=L):L=r[5];let N=L,j,I;return r[6]!==b||r[7]!==k||r[8]!==S||r[9]!==s?(j=()=>{if(!s)return;let e=k[b];!e||(null==e?void 0:e.isOpen)||(d(!1),setTimeout(()=>{S()},1))},I=[k,b,S,s],r[6]=b,r[7]=k,r[8]=S,r[9]=s,r[10]=j,r[11]=I):(j=r[10],I=r[11]),(0,p.useEffect)(j,I),r[12]!==f?(t=e=>(0,h.jsx)(f,{...e,onClick:()=>{w()}}),r[12]=f,r[13]=t):t=r[13],r[14]!==C?(l=()=>{w(),C(),d(!0)},r[14]=C,r[15]=l):l=r[15],r[16]!==u||r[17]!==N||r[18]!==v||r[19]!==b||r[20]!==t||r[21]!==l?(n={closeListDrawer:N,isListDrawerOpen:v,ListDrawer:u,listDrawerSlug:b,ListDrawerToggler:t,openListDrawer:l},r[16]=u,r[17]=N,r[18]=v,r[19]=b,r[20]=t,r[21]=l,r[22]=n):n=r[22],n},lH=e=>{let{editor:t,relationTo:l,replaceNodeKey:n,value:r}=e;n?t.update(()=>{let e=(0,c.nsf)(n);e&&e.replace(lA({relationTo:l,value:r}))}):t.dispatchCommand(lO,{relationTo:l,value:r})},lB=e=>{let t,l=(0,T.c)(16),{enabledCollectionSlugs:n}=e,[r]=(0,E.DF)(),i;l[0]!==(null==n?void 0:n[0])?(i=()=>null==n?void 0:n[0],l[0]=null==n?void 0:n[0],l[1]=i):i=l[1];let[a]=(0,p.useState)(i),[s,d]=(0,p.useState)(null),u=n||void 0,f;l[2]!==a||l[3]!==u?(f={collectionSlugs:u,selectedCollection:a},l[2]=a,l[3]=u,l[4]=f):f=l[4];let{closeListDrawer:m,ListDrawer:g,openListDrawer:b}=lP(f),v,x;l[5]!==r||l[6]!==b?(v=()=>r.registerCommand(o.a,e=>(d((null==e?void 0:e.replace)?null==e?void 0:e.replace.nodeKey:null),b(),!0),c.jZM),x=[r,b],l[5]=r,l[6]=b,l[7]=v,l[8]=x):(v=l[7],x=l[8]),(0,p.useEffect)(v,x),l[9]!==m||l[10]!==r||l[11]!==s?(t=e=>{let{collectionSlug:t,doc:l}=e;lH({editor:r,relationTo:t,replaceNodeKey:s,value:l.id}),m()},l[9]=m,l[10]=r,l[11]=s,l[12]=t):t=l[12];let C=t,k;return l[13]!==g||l[14]!==C?(k=(0,h.jsx)(g,{onSelect:C}),l[13]=g,l[14]=C,l[15]=k):k=l[15],k},lD=e=>{var t,l;return(null!=(l=null==e||null==(t=e.enabledCollectionSlugs)?void 0:t.length)?l:-1)>0?(0,h.jsx)(lB,{...e}):(0,h.jsx)(i.b,{...e,children:(0,h.jsx)(lB,{...e})})},lO=(0,c.guI)("INSERT_RELATIONSHIP_COMMAND");function lz(e){let{slug:t}=e;return t}function lJ(e){let t=(0,c.vJq)()||(0,c.S1w)();if((0,c.I2P)(t)){let l=lA(e),{focus:n}=t,r=n.getNode();(0,L.Pe)(l),(0,c.bSg)(r)&&!r.__first&&r.remove()}return!0}var lV=V({nodes:[lF],plugins:[{Component:e=>{let t,l,n,r=(0,T.c)(10),{clientProps:o}=e,[i]=(0,E.DF)(),{config:a}=(0,C.b)(),{collections:s}=a,d=null;if(null==o?void 0:o.enabledCollections)d=null==o?void 0:o.enabledCollections;else if(null==o?void 0:o.disabledCollections){let e;if(r[0]!==(null==o?void 0:o.disabledCollections)||r[1]!==s){let t;r[3]!==(null==o?void 0:o.disabledCollections)?(t=e=>{var t;let{slug:l}=e;return!(null==o||null==(t=o.disabledCollections)?void 0:t.includes(l))},r[3]=null==o?void 0:o.disabledCollections,r[4]=t):t=r[4],e=s.filter(t).map(lz),r[0]=null==o?void 0:o.disabledCollections,r[1]=s,r[2]=e}else e=r[2];d=e}return r[5]!==i?(t=()=>{if(!i.hasNodes([lF]))throw Error("RelationshipPlugin: RelationshipNode not registered on editor");return i.registerCommand(lO,lJ,c.jZM)},l=[i],r[5]=i,r[6]=t,r[7]=l):(t=r[6],l=r[7]),(0,p.useEffect)(t,l),r[8]!==d?(n=(0,h.jsx)(lD,{enabledCollectionSlugs:d}),r[8]=d,r[9]=n):n=r[9],n},position:"normal"}],slashMenu:{groups:[z([{Icon:lT,key:"relationship",keywords:["relationship","relation","rel"],label:e=>{let{i18n:t}=e;return t.t("lexical:relationship:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(o.a,{replace:!1})}}])]},toolbarFixed:{groups:[e1([{ChildComponent:lT,isActive:e=>{let{selection:t}=e;return!!(0,c.RTZ)(t)&&!!t.getNodes().length&&lR(t.getNodes()[0])},key:"relationship",label:e=>{let{i18n:t}=e;return t.t("lexical:relationship:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(o.a,{replace:!1})}}])]}}),lK="toolbar-popup__button",lU=e=>{let t,l,n=(0,T.c)(22),{children:r,editor:o,item:i}=e,a;n[0]===Symbol.for("react.memo_cache_sentinel")?(a={active:!1,enabled:!0},n[0]=a):a=n[0];let[s,f]=(0,p.useState)(a),m=(0,p.useDeferredValue)(s),g=(0,u.b)(),b,v=m.enabled?"":"disabled",x=m.active?"active":"",C=i.key?"".concat(lK,"-").concat(i.key):"",k;n[1]!==v||n[2]!==x||n[3]!==C?(k=[lK,v,x,C].filter(Boolean),n[1]=v,n[2]=x,n[3]=C,n[4]=k):k=n[4],b=k.join(" "),n[5]!==o||n[6]!==g||n[7]!==i?(t=()=>{o.getEditorState().read(()=>{let e=(0,c.vJq)();if(!e)return;let t=!!i.isActive&&i.isActive({editor:o,editorConfigContext:g,selection:e}),l=!i.isEnabled||i.isEnabled({editor:o,editorConfigContext:g,selection:e});f(e=>e.active===t&&e.enabled===l?e:{active:t,enabled:l})})},n[5]=o,n[6]=g,n[7]=i,n[8]=t):t=n[8];let y=t,w=(0,d.b)(),_,E;n[9]!==o||n[10]!==w||n[11]!==y?(_=()=>{let e=()=>w(y),t=(0,L.Sd)(o.registerUpdateListener(e));return document.addEventListener("mouseup",e),()=>{t(),document.removeEventListener("mouseup",e)}},E=[o,w,y],n[9]=o,n[10]=w,n[11]=y,n[12]=_,n[13]=E):(_=n[12],E=n[13]),(0,p.useEffect)(_,E),n[14]!==s||n[15]!==o||n[16]!==i?(l=()=>{s.enabled&&o.focus(()=>{var e;o.update(lW),null==(e=i.onSelect)||e.call(i,{editor:o,isActive:s.active})})},n[14]=s,n[15]=o,n[16]=i,n[17]=l):l=n[17];let S=l,N;return n[18]!==r||n[19]!==b||n[20]!==S?(N=(0,h.jsx)("button",{className:b,onClick:S,onMouseDown:lZ,type:"button",children:r}),n[18]=r,n[19]=b,n[20]=S,n[21]=N):N=n[21],N};function lW(){(0,c.JAT)("toolbar")}function lZ(e){e.preventDefault()}var lG="toolbar-popup__dropdown-item",lq=p.createContext(null);function lY(e){let{active:t,children:l,editor:n,enabled:r,Icon:o,item:i,tooltip:a}=e,s=(0,p.useMemo)(()=>[lG,!1===r?"disabled":"",t?"active":"",(null==i?void 0:i.key)?"".concat(lG,"-").concat(i.key):""].filter(Boolean).join(" "),[r,t,i.key]),d=(0,p.useRef)(null),u=p.use(lq);if(null===u)throw Error("DropDownItem must be used within a DropDown");let{registerItem:f}=u;return(0,p.useEffect)(()=>{(null==d?void 0:d.current)!=null&&f(d)},[d,f]),(0,h.jsx)(x.Button,{"aria-label":a,buttonStyle:"none",className:s,disabled:!1===r,icon:o,iconPosition:"left",iconStyle:"none",onClick:()=>{!1!==r&&n.focus(()=>{var e;n.update(()=>{(0,c.JAT)("toolbar")}),null==(e=i.onSelect)||e.call(i,{editor:n,isActive:t})})},onMouseDown:e=>{e.preventDefault()},ref:d,tooltip:a,type:"button",children:l})}function lX(e){let{children:t,dropDownRef:l,itemsContainerClassNames:n,onClose:r}=e,[o,i]=(0,p.useState)(),[a,s]=(0,p.useState)(),d=(0,p.useCallback)(e=>{i(t=>null!=t?[...t,e]:[e])},[i]),u=(0,p.useMemo)(()=>({registerItem:d}),[d]);return(0,p.useEffect)(()=>{null!=o&&null==a&&s(o[0]),null!=a&&(null==a?void 0:a.current)!=null&&a.current.focus()},[o,a]),(0,h.jsx)(lq,{value:u,children:(0,h.jsx)("div",{className:(null!=n?n:["toolbar-popup__dropdown-items"]).join(" "),onKeyDown:e=>{if(null==o)return;let{key:t}=e;["ArrowDown","ArrowUp","Escape","Tab"].includes(t)&&e.preventDefault(),"Escape"===t||"Tab"===t?r():"ArrowUp"===t?s(e=>{if(null==e)return o[0];let t=o.indexOf(e)-1;return o[-1===t?o.length-1:t]}):"ArrowDown"===t&&s(e=>null==e?o[0]:o[o.indexOf(e)+1])},ref:l,children:t})})}function l$(e){let{buttonAriaLabel:t,buttonClassName:l,children:n,disabled:r=!1,Icon:o,itemsContainerClassNames:i,label:a,stopCloseOnClickSelf:s}=e,d=(0,p.useRef)(null),u=(0,p.useRef)(null),[f,m]=(0,p.useState)(!1);(0,p.useEffect)(()=>{let e=u.current,t=d.current;if(f&&null!==e&&null!==t){let{left:l,top:n}=e.getBoundingClientRect(),r=window.scrollY||document.documentElement.scrollTop;t.style.top="".concat(n+r+e.offsetHeight+5,"px"),t.style.left="".concat(Math.min(l-5,window.innerWidth-t.offsetWidth-20),"px")}},[d,u,f]),(0,p.useEffect)(()=>{let e=u.current;if(null!==e&&f){let t=t=>{let l=t.target;(0,c.vAA)(l)&&(s&&d.current&&d.current.contains(l)||e.contains(l)||m(!1))};return document.addEventListener("click",t),()=>{document.removeEventListener("click",t)}}},[d,u,f,s]);let g=(0,F.createPortal)((0,h.jsx)(lX,{dropDownRef:d,itemsContainerClassNames:i,onClose:()=>{m(!1),(null==u?void 0:u.current)!=null&&u.current.focus()},children:n}),document.body);return(0,h.jsxs)(p.Fragment,{children:[(0,h.jsxs)("button",{"aria-label":t,className:l+(f?" active":""),disabled:r,onClick:e=>{e.preventDefault(),m(!f)},onMouseDown:e=>{e.preventDefault()},ref:u,type:"button",children:[o&&(0,h.jsx)(o,{}),a&&(0,h.jsx)("span",{className:"toolbar-popup__dropdown-label",children:a}),(0,h.jsx)("i",{className:"toolbar-popup__dropdown-caret"})]}),f&&(0,h.jsx)(p.Fragment,{children:g})]})}var lQ="toolbar-popup__dropdown",l0=p.memo(e=>{let t,l=(0,T.c)(18),{active:n,anchorElem:r,editor:o,enabled:i,item:a}=e,{i18n:s}=(0,k.d)(),{fieldProps:d}=(0,u.b)(),{featureClientSchemaMap:c,schemaPath:p}=d;if(a.Component){let e;return l[0]!==n||l[1]!==r||l[2]!==o||l[3]!==i||l[4]!==a?(e=(null==a?void 0:a.Component)&&(0,h.jsx)(a.Component,{active:n,anchorElem:r,editor:o,enabled:i,item:a},a.key),l[0]=n,l[1]=r,l[2]=o,l[3]=i,l[4]=a,l[5]=e):e=l[5],e}let f=a.key,m;if(a.label){let e;l[6]!==c||l[7]!==s||l[8]!==a||l[9]!==p?(e="function"==typeof a.label?a.label({featureClientSchemaMap:c,i18n:s,schemaPath:p}):a.label,l[6]=c,l[7]=s,l[8]=a,l[9]=p,l[10]=e):e=l[10],f=e}return m=f.length>25?f.substring(0,25)+"...":f,l[11]!==n||l[12]!==m||l[13]!==o||l[14]!==i||l[15]!==a||l[16]!==f?(t=(0,h.jsx)(lY,{active:n,editor:o,enabled:i,Icon:(null==a?void 0:a.ChildComponent)?(0,h.jsx)(a.ChildComponent,{}):void 0,item:a,tooltip:f,children:(0,h.jsx)("span",{className:"text",children:m})},a.key),l[11]=n,l[12]=m,l[13]=o,l[14]=i,l[15]=a,l[16]=f,l[17]=t):t=l[17],t}),l1=e=>{let{anchorElem:t,classNames:l,editor:n,group:r,Icon:o,itemsContainerClassNames:i,label:a,maxActiveItems:s,onActiveChange:f}=e,[m,g]=p.useState({activeItemKeys:[],enabledGroup:!0,enabledItemKeys:[]}),b=(0,p.useDeferredValue)(m),v=(0,u.b)(),{items:x,key:C}=r,k=(0,d.b)(),y=(0,p.useCallback)(()=>{n.getEditorState().read(()=>{let e=(0,c.vJq)();if(!e)return;let t=[],l=[],o=[];for(let r of x)r.isActive&&(!s||t.length<s)&&r.isActive({editor:n,editorConfigContext:v,selection:e})&&(t.push(r.key),l.push(r)),r.isEnabled?r.isEnabled({editor:n,editorConfigContext:v,selection:e})&&o.push(r.key):o.push(r.key);g({activeItemKeys:t,enabledGroup:!r.isEnabled||r.isEnabled({editor:n,editorConfigContext:v,selection:e}),enabledItemKeys:o}),f&&f({activeItems:l})})},[n,v,r,x,s,f]);(0,p.useEffect)(()=>(0,L.Sd)(n.registerUpdateListener(async()=>{await k(y)})),[n,k,y]);let w=(0,p.useMemo)(()=>(null==x?void 0:x.length)?x.map(e=>(0,h.jsx)(l0,{active:b.activeItemKeys.includes(e.key),anchorElem:t,editor:n,enabled:b.enabledItemKeys.includes(e.key),item:e},e.key)):null,[x,b,t,n]);return(0,h.jsx)(l$,{buttonAriaLabel:"".concat(C," dropdown"),buttonClassName:[lQ,"".concat(lQ,"-").concat(C),...l||[]].filter(Boolean).join(" "),disabled:!b.enabledGroup,Icon:o,itemsContainerClassNames:["".concat(lQ,"-items"),...i||[]],label:a,children:w},C)};function l2(e){let{anchorElem:t,editor:l,item:n}=e;return n.Component?(null==n?void 0:n.Component)&&(0,h.jsx)(n.Component,{anchorElem:t,editor:l,item:n},n.key):n.ChildComponent?(0,h.jsx)(lU,{editor:l,item:n,children:(0,h.jsx)(n.ChildComponent,{})},n.key):null}function l5(e){var t,l,n,r,o;let i,a,s=(0,T.c)(23),{anchorElem:d,editor:c,editorConfig:f,group:m,index:g}=e,{i18n:b}=(0,k.d)(),{fieldProps:v}=(0,u.b)(),{featureClientSchemaMap:x,schemaPath:C}=v,[y,w]=p.useState(void 0),[_,E]=p.useState(void 0),S;s[0]!==m.ChildComponent||s[1]!==m.items||s[2]!==m.type?(S=()=>{(null==m?void 0:m.type)==="dropdown"&&m.items.length&&m.ChildComponent?E(()=>m.ChildComponent):E(void 0)},s[0]=m.ChildComponent,s[1]=m.items,s[2]=m.type,s[3]=S):S=s[3],s[4]!==m?(i=[m],s[4]=m,s[5]=i):i=s[5],p.useEffect(S,i),s[6]!==x||s[7]!==m.ChildComponent||s[8]!==m.items||s[9]!==m.type||s[10]!==b||s[11]!==C?(a=e=>{let{activeItems:t}=e;if(!t.length)return void((null==m?void 0:m.type)==="dropdown"&&m.items.length&&m.ChildComponent?E(()=>m.ChildComponent):E(void 0),w(void 0));let l=t[0],n=l.key;l.label&&(n="function"==typeof l.label?l.label({featureClientSchemaMap:x,i18n:b,schemaPath:C}):l.label),n.length>25&&(n=n.substring(0,25)+"..."),1===t.length?(w(n),E(()=>l.ChildComponent)):(w(b.t("lexical:general:toolbarItemsActive",{count:t.length})),(null==m?void 0:m.type)==="dropdown"&&m.items.length&&m.ChildComponent?E(()=>m.ChildComponent):E(void 0))},s[6]=x,s[7]=m.ChildComponent,s[8]=m.items,s[9]=m.type,s[10]=b,s[11]=C,s[12]=a):a=s[12];let L=a,N="fixed-toolbar__group fixed-toolbar__group-".concat(m.key),j;return s[13]!==_||s[14]!==d||s[15]!==y||s[16]!==c||s[17]!==(null==(t=f.features.toolbarFixed)?void 0:t.groups.length)||s[18]!==m||s[19]!==g||s[20]!==L||s[21]!==N?(j=(0,h.jsxs)("div",{className:N,children:["dropdown"===m.type&&m.items.length?_?(0,h.jsx)(l1,{anchorElem:d,editor:c,group:m,Icon:_,itemsContainerClassNames:["fixed-toolbar__dropdown-items"],label:y,maxActiveItems:null!=(r=m.maxActiveItems)?r:1,onActiveChange:L}):(0,h.jsx)(l1,{anchorElem:d,editor:c,group:m,itemsContainerClassNames:["fixed-toolbar__dropdown-items"],label:y,maxActiveItems:null!=(o=m.maxActiveItems)?o:1,onActiveChange:L}):null,"buttons"===m.type&&m.items.length?m.items.map(e=>(0,h.jsx)(l2,{anchorElem:d,editor:c,item:e},e.key)):null,g<(null==(l=f.features.toolbarFixed)?void 0:l.groups.length)-1&&(0,h.jsx)("div",{className:"divider"})]},m.key),s[13]=_,s[14]=d,s[15]=y,s[16]=c,s[17]=null==(n=f.features.toolbarFixed)?void 0:n.groups.length,s[18]=m,s[19]=g,s[20]=L,s[21]=N,s[22]=j):j=s[22],j}function l6(e){var t,l;let{anchorElem:n,clientProps:r,editor:o,editorConfig:i,parentWithFixedToolbar:a}=e,s=p.useRef(null),{y:d}=(0,x.useScrollInfo)(),u=(0,p.useMemo)(()=>{if(!a||(null==r?void 0:r.disableIfParentHasFixedToolbar))return null;let e=a.editorContainerRef.current.previousElementSibling;for(;e;){if(e.classList.contains("fixed-toolbar"))return e;e=e.previousElementSibling}return null},[null==r?void 0:r.disableIfParentHasFixedToolbar,a]);return(0,x.useThrottledEffect)(()=>{if(!u)return;let e=s.current;if(!e)return;let t=e.getBoundingClientRect(),l=u.getBoundingClientRect();if(t.bottom<l.top||t.top>l.bottom){if(!e.classList.contains("fixed-toolbar--overlapping"))return;e.classList.remove("fixed-toolbar--overlapping"),e.classList.add("fixed-toolbar"),u.classList.remove("fixed-toolbar--hide"),u.classList.add("fixed-toolbar")}else e.classList.remove("fixed-toolbar"),e.classList.add("fixed-toolbar","fixed-toolbar--overlapping"),u.classList.remove("fixed-toolbar"),u.classList.add("fixed-toolbar","fixed-toolbar--hide")},50,[s,u,d]),(0,h.jsx)("div",{className:"fixed-toolbar",onFocus:e=>{e.stopPropagation()},ref:s,children:o.isEditable()&&(0,h.jsx)(p.Fragment,{children:(null==i?void 0:i.features)&&(null==(l=i.features)||null==(t=l.toolbarFixed)?void 0:t.groups.map((e,t)=>(0,h.jsx)(l5,{anchorElem:n,editor:o,editorConfig:i,group:e,index:t},e.key)))})})}var l3=e=>{var t,l;if(null==(t=e.parentEditor)?void 0:t.editorConfig){if(null==(l=e.parentEditor)?void 0:l.editorConfig.resolvedFeatureMap.has("toolbarFixed"))return e.parentEditor;if(e.parentEditor)return l3(e.parentEditor)}return!1},l4=V({plugins:[{Component:e=>{var t,l,n,r,o;let i=(0,T.c)(6),{clientProps:a}=e,[s]=(0,E.DF)(),d=(0,u.b)(),{editorConfig:c}=d,p=a.applyToFocusedEditor&&(null==(t=d.focusedEditor)?void 0:t.editor)||s,f=a.applyToFocusedEditor&&(null==(l=d.focusedEditor)?void 0:l.editorConfig)||c,m,g;if(i[0]!==a.disableIfParentHasFixedToolbar||i[1]!==p||i[2]!==f||i[3]!==d){g=Symbol.for("react.early_return_sentinel");e:{let e=l3(d);if((null==a?void 0:a.disableIfParentHasFixedToolbar)&&e||!(null==f||null==(o=f.features)||null==(r=o.toolbarFixed)||null==(n=r.groups)?void 0:n.length)){g=null;break e}m=(0,h.jsx)(l6,{anchorElem:document.body,editor:p,editorConfig:f,parentWithFixedToolbar:e})}i[0]=a.disableIfParentHasFixedToolbar,i[1]=p,i[2]=f,i[3]=d,i[4]=m,i[5]=g}else m=i[4],g=i[5];return g!==Symbol.for("react.early_return_sentinel")?g:m},position:"aboveContainer"}]});function l8(e,t){let l=e.getRangeAt(0),n;if(e.anchorNode===t){let e=t;for(;null!=e.firstElementChild;)e=e.firstElementChild;n=e.getBoundingClientRect()}else n=l.getBoundingClientRect();return n}function l7(e){let{alwaysDisplayOnTop:t=!1,anchorElem:l,anchorFlippedOffset:n=0,floatingElem:r,horizontalOffset:o=32,horizontalPosition:i="left",specialHandlingForCaret:a=!1,targetRect:s,verticalGap:d=10}=e,u=l.parentElement;if(null===s||null==u){r.style.opacity="0",r.style.transform="translate(-10000px, -10000px)";return}let c=r.getBoundingClientRect(),h=l.getBoundingClientRect(),p=u.getBoundingClientRect(),f=s.top-c.height-d,m=s.left-o;"center"===i&&(m=s.left+s.width/2-c.width/2);let g=0;return t||!(f<p.top)||a||(f+=g=c.height+s.height+2*d),"center"===i?m+c.width>p.right?m=p.right-c.width-o:m<p.left&&(m=p.left+o):m+c.width>p.right&&(m=p.right-c.width-o),m-=h.left,r.style.opacity="1",a&&0!==n?(f-=h.bottom-n+c.height-3,r.style.transform="translate(".concat(m,"px, ").concat(f,"px) rotate(180deg)")):(f-=h.top,r.style.transform="translate(".concat(m,"px, ").concat(f,"px)")),g}function l9(e){let{anchorElem:t,editor:l,item:n}=e;return n.Component?(null==n?void 0:n.Component)&&(0,h.jsx)(n.Component,{anchorElem:t,editor:l,item:n},n.key):n.ChildComponent?(0,h.jsx)(lU,{editor:l,item:n,children:(0,h.jsx)(n.ChildComponent,{})},n.key):null}function ne(e){var t,l,n,r,o;let i,a,s=(0,T.c)(19),{anchorElem:d,editor:c,group:f,index:m}=e,{editorConfig:g}=(0,u.b)(),[b,v]=p.useState(),x;s[0]!==f.ChildComponent||s[1]!==f.items||s[2]!==f.type?(x=()=>{(null==f?void 0:f.type)==="dropdown"&&f.items.length&&f.ChildComponent?v(()=>f.ChildComponent):v(void 0)},s[0]=f.ChildComponent,s[1]=f.items,s[2]=f.type,s[3]=x):x=s[3],s[4]!==f?(i=[f],s[4]=f,s[5]=i):i=s[5],p.useEffect(x,i),s[6]!==f.ChildComponent||s[7]!==f.items||s[8]!==f.type?(a=e=>{let{activeItems:t}=e;if(!t.length)return void((null==f?void 0:f.type)==="dropdown"&&f.items.length&&f.ChildComponent?v(()=>f.ChildComponent):v(void 0));let l=t[0];v(()=>null==l?void 0:l.ChildComponent)},s[6]=f.ChildComponent,s[7]=f.items,s[8]=f.type,s[9]=a):a=s[9];let C=a,k="inline-toolbar-popup__group inline-toolbar-popup__group-".concat(f.key),y;return s[10]!==b||s[11]!==d||s[12]!==c||s[13]!==(null==(t=g.features.toolbarInline)?void 0:t.groups.length)||s[14]!==f||s[15]!==m||s[16]!==C||s[17]!==k?(y=(0,h.jsxs)("div",{className:k,children:["dropdown"===f.type&&f.items.length?b?(0,h.jsx)(l1,{anchorElem:d,editor:c,group:f,Icon:b,maxActiveItems:null!=(r=f.maxActiveItems)?r:1,onActiveChange:C}):(0,h.jsx)(l1,{anchorElem:d,editor:c,group:f,maxActiveItems:null!=(o=f.maxActiveItems)?o:1,onActiveChange:C}):null,"buttons"===f.type&&f.items.length?f.items.map(e=>(0,h.jsx)(l9,{anchorElem:d,editor:c,item:e},e.key)):null,m<(null==(l=g.features.toolbarInline)?void 0:l.groups.length)-1&&(0,h.jsx)("div",{className:"divider"})]},f.key),s[10]=b,s[11]=d,s[12]=c,s[13]=null==(n=g.features.toolbarInline)?void 0:n.groups.length,s[14]=f,s[15]=m,s[16]=C,s[17]=k,s[18]=y):y=s[18],y}function nt(e){var t,l;let{anchorElem:n,editor:r}=e,o=(0,p.useRef)(null),i=(0,p.useRef)(null),{editorConfig:a}=(0,u.b)(),s=(0,p.useCallback)(()=>{if(null==o?void 0:o.current){let e="0"===o.current.style.opacity,t="none"===o.current.style.pointerEvents;e||(o.current.style.opacity="0"),t||(o.current.style.pointerEvents="none")}},[o]),d=(0,p.useCallback)(e=>{if((null==o?void 0:o.current)&&(1===e.buttons||3===e.buttons)){let t="0"===o.current.style.opacity,l="none"===o.current.style.pointerEvents;if(!t||!l){let t=e.clientX,l=e.clientY,n=document.elementFromPoint(t,l);o.current.contains(n)||s()}}},[s]),f=(0,p.useCallback)(()=>{(null==o?void 0:o.current)&&("1"!==o.current.style.opacity&&(o.current.style.opacity="1"),"auto"!==o.current.style.pointerEvents&&(o.current.style.pointerEvents="auto"))},[]);(0,p.useEffect)(()=>(document.addEventListener("mousemove",d),document.addEventListener("mouseup",f),()=>{document.removeEventListener("mousemove",d),document.removeEventListener("mouseup",f)}),[o,d,f]);let m=(0,p.useCallback)(()=>{var e;let t=(0,c.vJq)(),l=(0,c.peL)(r._window);if(null===o.current)return;let a=n.querySelector(":scope > .link-editor"),d=null!==a&&"style"in a&&(null==a||null==(e=a.style)?void 0:e.opacity)==="1",u=r.getRootElement();if(null!==t&&null!==l&&!l.isCollapsed&&null!==u&&u.contains(l.anchorNode)){let e=l8(l,u),t=l7({alwaysDisplayOnTop:d,anchorElem:n,floatingElem:o.current,horizontalPosition:"center",targetRect:e});i.current&&l7({anchorElem:o.current,anchorFlippedOffset:t,floatingElem:i.current,horizontalOffset:5,horizontalPosition:"center",specialHandlingForCaret:!0,targetRect:e,verticalGap:8})}else s()},[r,s,n]);return(0,p.useEffect)(()=>{let e=n.parentElement,t=()=>{r.getEditorState().read(()=>{m()})};return window.addEventListener("resize",t),e&&e.addEventListener("scroll",t),()=>{window.removeEventListener("resize",t),e&&e.removeEventListener("scroll",t)}},[r,m,n]),(0,p.useEffect)(()=>(r.getEditorState().read(()=>{m()}),(0,L.Sd)(r.registerUpdateListener(e=>{let{editorState:t}=e;t.read(()=>{m()})}),r.registerCommand(c.MvL,()=>(m(),!1),c.AcJ))),[r,m]),(0,h.jsxs)("div",{className:"inline-toolbar-popup",ref:o,children:[(0,h.jsx)("div",{className:"caret",ref:i}),(null==a?void 0:a.features)&&(null==(l=a.features)||null==(t=l.toolbarInline)?void 0:t.groups.map((e,t)=>(0,h.jsx)(ne,{anchorElem:n,editor:r,group:e,index:t},e.key)))]})}var nl=V({plugins:[{Component:e=>{let{anchorElem:t}=e,[l]=(0,E.DF)();return function(e,t){let l,n,r,o=(0,T.c)(12),[i,a]=(0,p.useState)(!1),s;o[0]!==e?(s=()=>{e.getEditorState().read(()=>{if(e.isComposing())return;let t=(0,c.vJq)(),l=(0,c.peL)(e._window),n=e.getRootElement();if(null!==l&&(!(0,c.I2P)(t)||null===n||!n.contains(l.anchorNode)))return void a(!1);if(!(0,c.I2P)(t))return;if(""!==t.getTextContent()){let e=t.getNodes(),l=!1;for(let t of e)if((0,c.kFe)(t)){a(!0),l=!0;break}l||a(!1)}else a(!1);let r=t.getTextContent().replace(/\n/g,"");if(!t.isCollapsed()&&""===r)return void a(!1)})},o[0]=e,o[1]=s):s=o[1];let d=s,u,f;return(o[2]!==d?(u=()=>(document.addEventListener("selectionchange",d),document.addEventListener("mouseup",d),()=>{document.removeEventListener("selectionchange",d),document.removeEventListener("mouseup",d)}),f=[d],o[2]=d,o[3]=u,o[4]=f):(u=o[3],f=o[4]),(0,p.useEffect)(u,f),o[5]!==e||o[6]!==d?(l=()=>(0,L.Sd)(e.registerUpdateListener(()=>{d()}),e.registerRootListener(()=>{null===e.getRootElement()&&a(!1)})),n=[e,d],o[5]=e,o[6]=d,o[7]=l,o[8]=n):(l=o[7],n=o[8]),(0,p.useEffect)(l,n),i&&e.isEditable())?(o[9]!==t||o[10]!==e?(r=(0,F.createPortal)((0,h.jsx)(nt,{anchorElem:t,editor:e}),t),o[9]=t,o[10]=e,o[11]=r):r=o[11],r):null}(l,t)},position:"floatingAnchorElem"}]}),nn=()=>(0,h.jsx)("svg",{className:"icon",fill:"none",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,h.jsx)("path",{clipRule:"evenodd",d:"M5.33333 4.5C4.8731 4.5 4.5 4.8731 4.5 5.33333V7.5H9.5V4.5H5.33333ZM5.33333 3.5C4.32081 3.5 3.5 4.32081 3.5 5.33333V14.6667C3.5 15.6792 4.32081 16.5 5.33333 16.5H14.6667C15.6792 16.5 16.5 15.6792 16.5 14.6667V5.33333C16.5 4.32081 15.6792 3.5 14.6667 3.5H5.33333ZM10.5 4.5V7.5H15.5V5.33333C15.5 4.8731 15.1269 4.5 14.6667 4.5H10.5ZM15.5 8.5H10.5V11.5H15.5V8.5ZM15.5 12.5H10.5V15.5H14.6667C15.1269 15.5 15.5 15.1269 15.5 14.6667V12.5ZM9.5 15.5V12.5H4.5V14.6667C4.5 15.1269 4.8731 15.5 5.33333 15.5H9.5ZM4.5 11.5H9.5V8.5H4.5V11.5Z",fill:"currentColor",fillRule:"evenodd"})}),nr=/^\|(.+)\|\s?$/,no=/^(\| ?:?-*:? ?)+\|\s?$/,ni=(e,t)=>{e=e.replace(/\\n/g,"\n");let l=(0,j.np)(j.o5.NO_STATUS);return(0,d.d)(e,t,l),l},na=(e,t)=>{let l=e.match(nr);return l&&l[1]?l[1].split("|").map(e=>ni(e,t)):null},ns=()=>(0,h.jsxs)("svg",{fill:"none",height:"18",viewBox:"0 0 20 20",width:"18",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M5 11C5.55228 11 6 10.5523 6 10C6 9.44772 5.55228 9 5 9C4.44772 9 4 9.44772 4 10C4 10.5523 4.44772 11 5 11Z",fill:"currentColor"}),(0,h.jsx)("path",{d:"M10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11Z",fill:"currentColor"}),(0,h.jsx)("path",{d:"M15 11C15.5523 11 16 10.5523 16 10C16 9.44772 15.5523 9 15 9C14.4477 9 14 9.44772 14 10C14 10.5523 14.4477 11 15 11Z",fill:"currentColor"})]});function nd(e){let t=e.getShape();return{columns:t.toX-t.fromX+1,rows:t.toY-t.fromY+1}}function nu(e){let{cellMerge:t,contextRef:l,onClose:n,setIsMenuOpen:r,tableCellNode:o}=e,[i]=(0,E.DF)(),a=(0,p.useRef)(null),[s,d]=(0,p.useState)(o),[u,f]=(0,p.useState)({columns:1,rows:1}),[m,g]=(0,p.useState)(!1),[b,v]=(0,p.useState)(!1),{y:C}=(0,x.useScrollInfo)();(0,p.useEffect)(()=>i.registerMutationListener(j.B9,e=>{"updated"===e.get(s.getKey())&&i.getEditorState().read(()=>{d(s.getLatest())})},{skipInitialization:!0}),[i,s]),(0,p.useEffect)(()=>{i.getEditorState().read(()=>{let e=(0,c.vJq)();if((0,j.Ln)(e)){let t=nd(e);f(nd(e)),g(t.columns>1||t.rows>1)}v(function(){let e=(0,c.vJq)();if((0,c.I2P)(e)&&!e.isCollapsed()||(0,j.Ln)(e)&&!e.anchor.is(e.focus)||!(0,c.I2P)(e)&&!(0,j.Ln)(e))return!1;let[t]=(0,j.be)(e.anchor);return t.__colSpan>1||t.__rowSpan>1}())})},[i]),(0,p.useEffect)(()=>{let e=l.current,t=a.current,n=i.getRootElement();if(null!=e&&null!=t&&null!=n){let l=n.getBoundingClientRect(),r=e.getBoundingClientRect();t.style.opacity="1";let o=t.getBoundingClientRect(),i=r.right+5;if(i+o.width>window.innerWidth||i+o.width>l.right){let e=r.left-o.width-5;i=(e<0?5:e)+window.pageXOffset}t.style.left="".concat(i+window.pageXOffset,"px");let a=r.top;if(a+o.height>window.innerHeight){let e=r.bottom-o.height;a=e<0?5:e}t.style.top="".concat(a,"px")}},[l,a,i,C]),(0,p.useEffect)(()=>{function e(e){null!=a.current&&null!=l.current&&(0,c.vAA)(e.target)&&!a.current.contains(e.target)&&!l.current.contains(e.target)&&r(!1)}return window.addEventListener("click",e),()=>window.removeEventListener("click",e)},[r,l]);let k=(0,p.useCallback)(()=>{i.update(()=>{if(s.isAttached()){let e=(0,j.sJ)(s),t=(0,j.zC)(e,i.getElementByKey(e.getKey()));if(null===t)throw Error("Expected to find tableElement in DOM");let l=(0,j.xp)(t);null!==l&&l.$clearHighlight(),e.markDirty(),d(s.getLatest())}(0,c.n1P)(null)})},[i,s]),y=()=>{i.update(()=>{let e=(0,c.vJq)();if((0,j.Ln)(e)){var t,l;let r,o=e.getNodes().filter(j.Xx);if(0===o.length)return;let i=(0,j.sJ)(o[0]),[a]=(0,j.tV)(i,null,null),s=1/0,d=-1/0,u=1/0,h=-1/0,p=new Set;for(let e of a)for(let t of e){if(!t||!t.cell)continue;let e=t.cell.getKey();if(!p.has(e)&&o.some(e=>e.is(t.cell))){p.add(e);let l=t.startRow,n=t.startColumn,r=t.cell.__rowSpan||1,o=t.cell.__colSpan||1;s=Math.min(s,l),d=Math.max(d,l+r-1),u=Math.min(u,n),h=Math.max(h,n+o-1)}}if(s===1/0||u===1/0)return;let f=d-s+1,m=h-u+1,g=null==a||null==(t=a[s])?void 0:t[u];if(!(null==g?void 0:g.cell))return;let b=g.cell;b.setColSpan(m),b.setRowSpan(f);let v=new Set([b.getKey()]);for(let e=s;e<=d;e++)for(let t=u;t<=h;t++){let n=null==a||null==(l=a[e])?void 0:l[t];if(!(null==n?void 0:n.cell))continue;let r=n.cell,o=r.getKey();v.has(o)||(v.add(o),function(e){if(1!==e.getChildrenSize())return!1;let t=e.getFirstChildOrThrow();return!(!(0,c.bSg)(t)||!t.isEmpty())}(r)||b.append(...r.getChildren()),r.remove())}0===b.getChildrenSize()&&b.append((0,c.lJ7)()),r=b.getLastDescendant(),(0,c.kFe)(r)?r.select():(0,c.ff4)(r)?r.selectEnd():null!==r&&r.selectNext(),n()}})},w=()=>{i.update(()=>{(0,j.GA)()})},_=(0,p.useCallback)(e=>{i.update(()=>{for(let t=0;t<u.rows;t++)(0,j.kC)(e);n()})},[i,n,u.rows]),S=(0,p.useCallback)(e=>{i.update(()=>{for(let t=0;t<u.columns;t++)(0,j.O8)(e);n()})},[i,n,u.columns]),T=(0,p.useCallback)(()=>{i.update(()=>{(0,j.kN)(),n()})},[i,n]),L=(0,p.useCallback)(()=>{i.update(()=>{(0,j.sJ)(s).remove(),k(),n()})},[i,s,k,n]),N=(0,p.useCallback)(()=>{i.update(()=>{(0,j.ex)(),n()})},[i,n]),I=(0,p.useCallback)(()=>{i.update(()=>{let e=(0,j.sJ)(s),t=(0,j.pA)(s),[l]=(0,j.tV)(e,null,null),r=new Set,o=s.getHeaderStyles()^j.o5.ROW;if(l[t])for(let e=0;e<l[t].length;e++){let n=l[t][e];(null==n?void 0:n.cell)&&(r.has(n.cell)||(r.add(n.cell),n.cell.setHeaderStyles(o,j.o5.ROW)))}k(),n()})},[i,s,k,n]),M=(0,p.useCallback)(()=>{i.update(()=>{let e=(0,j.sJ)(s),t=(0,j.Ze)(s),[l]=(0,j.tV)(e,null,null),r=new Set,o=s.getHeaderStyles()^j.o5.COLUMN;if(l)for(let e=0;e<l.length;e++){var i;let n=null==l||null==(i=l[e])?void 0:i[t];(null==n?void 0:n.cell)&&(r.has(n.cell)||(r.add(n.cell),n.cell.setHeaderStyles(o,j.o5.COLUMN)))}k(),n()})},[i,s,k,n]),A=(0,p.useCallback)(()=>{i.update(()=>{if(s.isAttached()){let e=(0,j.sJ)(s);e&&e.setRowStriping(!e.getRowStriping())}k(),n()})},[i,s,k,n]),R=(0,p.useCallback)(()=>{i.update(()=>{if(s.isAttached()){let e=(0,j.sJ)(s);e&&e.setFrozenColumns(+(0===e.getFrozenColumns()))}k(),n()})},[i,s,k,n]),P=null;return t&&(m?P=(0,h.jsx)("button",{className:"item","data-test-id":"table-merge-cells",onClick:()=>y(),type:"button",children:(0,h.jsx)("span",{className:"text",children:"Merge cells"})}):b&&(P=(0,h.jsx)("button",{className:"item","data-test-id":"table-unmerge-cells",onClick:()=>w(),type:"button",children:(0,h.jsx)("span",{className:"text",children:"Unmerge cells"})}))),(0,F.createPortal)((0,h.jsxs)("div",{className:"table-action-menu-dropdown",onClick:e=>{e.stopPropagation()},ref:a,children:[P?(0,h.jsxs)(p.Fragment,{children:[P,(0,h.jsx)("hr",{})]}):null,(0,h.jsx)("button",{className:"item","data-test-id":"table-row-striping",onClick:()=>A(),type:"button",children:(0,h.jsx)("span",{className:"text",children:"Toggle Row Striping"})}),(0,h.jsx)("button",{className:"item","data-test-id":"table-freeze-first-column",onClick:()=>R(),type:"button",children:(0,h.jsx)("span",{className:"text",children:"Toggle First Column Freeze"})}),(0,h.jsx)("button",{className:"item","data-test-id":"table-insert-row-above",onClick:()=>_(!1),type:"button",children:(0,h.jsxs)("span",{className:"text",children:["Insert ",1===u.rows?"row":"".concat(u.rows," rows")," above"]})}),(0,h.jsx)("button",{className:"item","data-test-id":"table-insert-row-below",onClick:()=>_(!0),type:"button",children:(0,h.jsxs)("span",{className:"text",children:["Insert ",1===u.rows?"row":"".concat(u.rows," rows")," below"]})}),(0,h.jsx)("hr",{}),(0,h.jsx)("button",{className:"item","data-test-id":"table-insert-column-before",onClick:()=>S(!1),type:"button",children:(0,h.jsxs)("span",{className:"text",children:["Insert ",1===u.columns?"column":"".concat(u.columns," columns")," ","left"]})}),(0,h.jsx)("button",{className:"item","data-test-id":"table-insert-column-after",onClick:()=>S(!0),type:"button",children:(0,h.jsxs)("span",{className:"text",children:["Insert ",1===u.columns?"column":"".concat(u.columns," columns")," ","right"]})}),(0,h.jsx)("hr",{}),(0,h.jsx)("button",{className:"item","data-test-id":"table-delete-columns",onClick:()=>N(),type:"button",children:(0,h.jsx)("span",{className:"text",children:"Delete column"})}),(0,h.jsx)("button",{className:"item","data-test-id":"table-delete-rows",onClick:()=>T(),type:"button",children:(0,h.jsx)("span",{className:"text",children:"Delete row"})}),(0,h.jsx)("button",{className:"item","data-test-id":"table-delete",onClick:()=>L(),type:"button",children:(0,h.jsx)("span",{className:"text",children:"Delete table"})}),(0,h.jsx)("hr",{}),(0,h.jsx)("button",{className:"item","data-test-id":"table-row-header",onClick:()=>I(),type:"button",children:(0,h.jsxs)("span",{className:"text",children:[(s.__headerState&j.o5.ROW)===j.o5.ROW?"Remove":"Add"," ","row header"]})}),(0,h.jsx)("button",{className:"item","data-test-id":"table-column-header",onClick:()=>M(),type:"button",children:(0,h.jsxs)("span",{className:"text",children:[(s.__headerState&j.o5.COLUMN)===j.o5.COLUMN?"Remove":"Add"," ","column header"]})})]}),document.body)}function nc(e){let{anchorElem:t,cellMerge:l}=e,[n]=(0,E.DF)(),r=(0,p.useRef)(null),o=(0,p.useRef)(null),[i,a]=(0,p.useState)(!1),[s,d]=(0,p.useState)(null),u=(0,p.useCallback)(()=>{let e=r.current,l=(0,c.vJq)(),o=(0,c.peL)(n._window),i=document.activeElement;function a(){e&&(e.classList.remove("table-cell-action-button-container--active"),e.classList.add("table-cell-action-button-container--inactive")),d(null)}if(null==l||null==e)return a();let s=n.getRootElement(),u=null,h=null;if((0,c.I2P)(l)&&null!==s&&null!==o&&s.contains(o.anchorNode)){let e=(0,j.zp)(l.anchor.getNode());if(null==e||null==(h=n.getElementByKey(e.getKey()))||!e.isAttached())return a();let t=(0,j.sJ)(e),r=(0,j.zC)(t,n.getElementByKey(t.getKey()));if(null===r)throw Error("TableActionMenu: Expected to find tableElement in DOM");u=(0,j.xp)(r),d(e)}else if((0,j.Ln)(l)){let e=(0,j.zp)(l.anchor.getNode());if(!(0,j.Xx)(e))throw Error("TableSelection anchorNode must be a TableCellNode");let t=(0,j.sJ)(e),r=(0,j.zC)(t,n.getElementByKey(t.getKey()));if(null===r)throw Error("TableActionMenu: Expected to find tableElement in DOM");u=(0,j.xp)(r),h=n.getElementByKey(e.getKey())}else if(!i)return a();if(null===u||null===h)return a();let p=!u||!u.isSelecting;if(e.classList.toggle("table-cell-action-button-container--active",p),e.classList.toggle("table-cell-action-button-container--inactive",!p),p){let l=h.getBoundingClientRect(),n=t.getBoundingClientRect(),r=l.top-n.top,o=l.right-n.left;e.style.transform="translate(".concat(o,"px, ").concat(r,"px)")}},[n,t]);(0,p.useEffect)(()=>{let e,t=()=>{e=void 0,n.getEditorState().read(u)},l=()=>(void 0===e&&(e=setTimeout(t,0)),!1);return(0,L.Sd)(n.registerUpdateListener(l),n.registerCommand(c.MvL,l,c.da8),n.registerRootListener((e,t)=>{t&&t.removeEventListener("pointerup",l),e&&(e.addEventListener("pointerup",l),l())}),()=>clearTimeout(e))});let f=(0,p.useRef)(s);return(0,p.useEffect)(()=>{f.current!==s&&a(!1),f.current=s},[f,s]),(0,h.jsx)("div",{className:"table-cell-action-button-container",ref:r,children:null!=s&&(0,h.jsxs)(p.Fragment,{children:[(0,h.jsx)("button",{className:"table-cell-action-button",onClick:e=>{e.stopPropagation(),a(!i)},ref:o,type:"button",children:(0,h.jsx)(ns,{})}),i&&(0,h.jsx)(nu,{cellMerge:l,contextRef:o,onClose:()=>a(!1),setIsMenuOpen:a,tableCellNode:s})]})})}function nh(e){let{editor:t}=e,l=(0,p.useRef)(null),n=(0,p.useRef)(null),r=(0,p.useRef)(null),[o,i]=(0,p.useState)(!1),a=(0,u.b)(),s=(0,p.useRef)(null),[d,f]=(0,p.useState)(null),[m,g]=(0,p.useState)(null),[b,v]=(0,p.useState)(!1),[x,C]=(0,p.useState)(null),k=(0,p.useCallback)(()=>{g(null),l.current=null,C(null),s.current=null,r.current=null},[]),y=e=>(1&e.buttons)==1;(0,p.useEffect)(()=>{let e=new Set;return(0,L.Sd)(t.registerMutationListener(j.HA,t=>{for(let[l,n]of t)"destroyed"===n?e.delete(l):e.add(l);i(e.size>0)}),t.registerNodeTransform(j.HA,e=>{if(e.getColWidths())return e;let t=e.getColumnCount();return e.setColWidths(Array(t).fill(92)),e}))},[t]),(0,p.useEffect)(()=>{if(!o)return;let e=e=>{let o=e.target;if((0,c.sby)(o)){if(x)return void f({x:e.clientX,y:e.clientY});if(v(y(e)),!(n.current&&n.current.contains(o))&&l.current!==o){l.current=o;let e=(0,j.cn)(o);e&&m!==e?t.getEditorState().read(()=>{let n=(0,c.xL4)(e.elem);if(!n)throw Error("TableCellResizer: Table cell node not found.");let i=(0,j.sJ)(n),a=(0,j.zC)(i,t.getElementByKey(i.getKey()));if(!a)throw Error("TableCellResizer: Table element not found.");l.current=o,r.current=a.getBoundingClientRect(),g(e)},{editor:t}):null==e&&k()}}},i=e=>{v(!0)},a=e=>{v(!1)},s=t.registerRootListener((t,l)=>{null==l||l.removeEventListener("mousemove",e),null==l||l.removeEventListener("mousedown",i),null==l||l.removeEventListener("mouseup",a),null==t||t.addEventListener("mousemove",e),null==t||t.addEventListener("mousedown",i),null==t||t.addEventListener("mouseup",a)});return()=>{s()}},[m,x,t,o,k]);let w=e=>"bottom"===e,_=(0,p.useCallback)(e=>{if(!m)throw Error("TableCellResizer: Expected active cell.");t.update(()=>{let l=(0,c.xL4)(m.elem);if(!(0,j.Xx)(l))throw Error("TableCellResizer: Table cell node not found.");let n=(0,j.sJ)(l),r=(0,j.pA)(l),o=n.getChildren(),i=l.getColSpan()===n.getColumnCount()?r:r+l.getRowSpan()-1;if(i>=o.length||i<0)throw Error("Expected table cell to be inside of table row.");let a=o[i];if(!(0,j.RN)(a))throw Error("Expected table row");let s=a.getHeight();void 0===s&&(s=Math.min(...a.getChildren().map(e=>{var l;return null!=(l=E(e,t))?l:1/0})));let d=Math.max(s+e,33);a.setHeight(d)},{tag:"skip-scroll-into-view"})},[m,t]),E=(e,t)=>{var l;return null==(l=t.getElementByKey(e.getKey()))?void 0:l.clientHeight},S=(e,t)=>{let l;return t.forEach(t=>{t.forEach((t,n)=>{t.cell===e&&(l=n)})}),l},T=(0,p.useCallback)(e=>{if(!m)throw Error("TableCellResizer: Expected active cell.");t.update(()=>{let t=(0,c.xL4)(m.elem);if(!(0,j.Xx)(t))throw Error("TableCellResizer: Table cell node not found.");let l=(0,j.sJ)(t),[n]=(0,j.tV)(l,null,null),r=S(t,n);if(void 0===r)throw Error("TableCellResizer: Table column not found.");let o=l.getColWidths();if(!o)return;let i=o[r];if(void 0===i)return;let a=[...o],s=Math.max(i+e,92);a[r]=s,l.setColWidths(a)},{tag:"skip-scroll-into-view"})},[m,t]),N=(0,p.useCallback)(e=>{let t=l=>{if(l.preventDefault(),l.stopPropagation(),!m)throw Error("TableCellResizer: Expected active cell.");if(s.current){let{x:n,y:r}=s.current;if(null===m)return;let o=(0,L.OV)(l.target);w(e)?_((l.clientY-r)/o):T((l.clientX-n)/o),k(),document.removeEventListener("mouseup",t)}};return t},[m,k,T,_]),I=(0,p.useCallback)(e=>t=>{if(t.preventDefault(),t.stopPropagation(),!m)throw Error("TableCellResizer: Expected active cell.");s.current={x:t.clientX,y:t.clientY},f(s.current),C(e),document.addEventListener("mouseup",N(e))},[m,N]),[M,F]=(0,p.useState)({bottom:null,left:null,right:null,top:null});return(0,p.useEffect)(()=>{if(m){let{height:e,left:t,top:l,width:n}=m.elem.getBoundingClientRect(),o=(0,L.OV)(m.elem),i={bottom:{backgroundColor:"none",cursor:"row-resize",height:"".concat(10,"px"),left:"".concat(window.scrollX+t,"px"),top:"".concat(window.scrollY+l+e-5,"px"),width:"".concat(n,"px")},right:{backgroundColor:"none",cursor:"col-resize",height:"".concat(e,"px"),left:"".concat(window.scrollX+t+n-5,"px"),top:"".concat(window.scrollY+l,"px"),width:"".concat(10,"px")}},a=r.current;x&&d&&a&&(w(x)?(i[x].left="".concat(window.scrollX+a.left,"px"),i[x].top="".concat(window.scrollY+d.y/o,"px"),i[x].height="3px",i[x].width="".concat(a.width,"px")):(i[x].top="".concat(window.scrollY+a.top,"px"),i[x].left="".concat(window.scrollX+d.x/o,"px"),i[x].width="3px",i[x].height="".concat(a.height,"px")),i[x].backgroundColor="#adf"),F(i)}else F({bottom:null,left:null,right:null,top:null})},[m,x,d]),(0,h.jsx)("div",{ref:n,children:null!=m&&!b&&(0,h.jsxs)(p.Fragment,{children:[(0,h.jsx)("div",{className:"".concat(a.editorConfig.lexical.theme.tableCellResizer," TableCellResizer__ui"),onMouseDown:I("right"),style:M.right||void 0}),(0,h.jsx)("div",{className:"".concat(a.editorConfig.lexical.theme.tableCellResizer," TableCellResizer__ui"),onMouseDown:I("bottom"),style:M.bottom||void 0})]})})}var np=Math.max,nf=Math.min,nm=function(e,t,l){let n,r,o=0,i,a=!1,s=!1,d,u,c,h=!0;if("function"!=typeof e)throw TypeError("Expected a function");function p(t){let l=n,r=i;return n=i=void 0,o=t,u=e.apply(r,l)}function f(e){let l=e-r,n=e-o;return void 0===r||l>=t||l<0||s&&n>=d}function m(){let e,l,n,i=Date.now();if(f(i))return g(i);c=setTimeout(m,(e=i-r,l=i-o,n=t-e,s?nf(n,d-l):n))}function g(e){return c=void 0,h&&n?p(e):(n=i=void 0,u)}function b(){let e=Date.now(),l=f(e);if(n=arguments,i=this,r=e,l){if(void 0===c){var d;return o=d=r,c=setTimeout(m,t),a?p(d):u}if(s)return clearTimeout(c),c=setTimeout(m,t),p(r)}return void 0===c&&(c=setTimeout(m,t)),u}return t=t||0,"object"==typeof l&&(a=!!l.leading,d=(s="maxWait"in l)?np(l.maxWait||0,t):d,h="trailing"in l?!!l.trailing:h),b.cancel=function(){void 0!==c&&clearTimeout(c),o=0,n=r=i=c=void 0},b.flush=function(){return void 0===c?u:g(Date.now())},b};function ng(e){var t;let l,n,r,o,i,{anchorElem:a}=e,[s]=(0,E.DF)(),d=(0,u.b)(),[f,m]=(0,p.useState)(!1),[g,b]=(0,p.useState)(!1),[v,x]=(0,p.useState)(!1),[C,k]=(0,p.useState)({}),y=(0,p.useRef)(new Set),w=(0,p.useRef)(null),_=(t=e=>{var t;let{isOutside:l,tableDOMNode:n}=function(e,t){let l=e.target;if(!(0,c.sby)(l))return{isOutside:!0,tableDOMNode:null};{let e=l.closest("td.".concat(t.theme.tableCell,", th.").concat(t.theme.tableCell));return{isOutside:!(e||l.closest("button.".concat(t.theme.tableAddRows))||l.closest("button.".concat(t.theme.tableAddColumns))||l.closest("div.".concat(t.theme.tableCellResizer))),tableDOMNode:e}}}(e,null==(t=d.editorConfig)?void 0:t.lexical);if(l){m(!1),b(!1);return}if(!n)return;w.current=n;let r=null,o=null,i=null;if(s.getEditorState().read(()=>{let e=(0,c.xL4)(n);if((0,j.Xx)(e)){let l=(0,L.Bt)(e,e=>(0,j.TX)(e));if(!(0,j.TX)(l))return;if(i=(0,j.zC)(l,s.getElementByKey(l.getKey()))){var t;let n=l.getChildrenSize(),i=null==(t=l.getChildAtIndex(0))?void 0:t.getChildrenSize(),a=(0,j.pA)(e),s=(0,j.Ze)(e);a===n-1?r=e:s===i-1&&(o=e)}}},{editor:s}),!i)return;let u=i.parentElement;if(!u)return;let{bottom:h,height:p,left:f,right:g,width:v,y:x}=i.getBoundingClientRect(),C=!1;u&&u.classList.contains("LexicalEditorTheme__tableScrollableWrapper")&&(C=u.scrollWidth>u.clientWidth);let{left:y,y:_}=a.getBoundingClientRect();r?(b(!1),m(!0),k({height:20,left:C&&u?u.offsetLeft:f-y,top:h-_+5,width:C&&u?u.offsetWidth:v})):o&&(b(!0),m(!1),k({height:p,left:g-y+5,top:x-_,width:20}))},n=(0,T.c)(6),r=(0,p.useRef)(null),n[0]!==t||250!==n[1]||50!==n[2]?(o=()=>(r.current=nm(t,50,{maxWait:250}),()=>{var e;null==(e=r.current)||e.cancel()}),i=[t,50,250],n[0]=t,n[1]=250,n[2]=50,n[3]=o,n[4]=i):(o=n[3],i=n[4]),(0,p.useEffect)(o,i),n[5]===Symbol.for("react.memo_cache_sentinel")?(l=function(){for(var e=arguments.length,t=Array(e),l=0;l<e;l++)t[l]=arguments[l];r.current&&r.current(...t)},n[5]=l):l=n[5],l),S=(0,p.useMemo)(()=>new ResizeObserver(()=>{m(!1),b(!1)}),[]);(0,p.useEffect)(()=>{if(v)return document.addEventListener("mousemove",_),()=>{m(!1),b(!1),document.removeEventListener("mousemove",_)}},[v,_]),(0,p.useEffect)(()=>(0,L.Sd)(s.registerMutationListener(j.HA,e=>{s.getEditorState().read(()=>{let t=!1;for(let[l,n]of e)switch(n){case"created":y.current.add(l),t=!0;break;case"destroyed":y.current.delete(l),t=!0}if(t){for(let e of(S.disconnect(),y.current)){let{tableElement:t}=(0,j.cl)(e);S.observe(t)}x(y.current.size>0)}},{editor:s})},{skipInitialization:!1})),[s,S]);let N=e=>{s.update(()=>{var t;w.current&&(null==(t=(0,c.xL4)(w.current))||t.selectEnd(),e?((0,j.kC)(),m(!1)):((0,j.O8)(),b(!1)))})};return(null==s?void 0:s.isEditable())?(0,h.jsxs)(h.Fragment,{children:[f&&(0,h.jsx)("button",{"aria-label":"Add Row",className:d.editorConfig.lexical.theme.tableAddRows,onClick:()=>N(!0),style:{...C},type:"button"}),g&&(0,h.jsx)("button",{"aria-label":"Add Column",className:d.editorConfig.lexical.theme.tableAddColumns,onClick:()=>N(!1),style:{...C},type:"button"})]}):null}var nb=(0,c.guI)("OPEN_EMBED_DRAWER_COMMAND"),nv=(0,p.createContext)({cellEditorConfig:null,cellEditorPlugins:null,set:()=>{}}),nx=V({markdownTransformers:[e=>{let{allTransformers:t}=e;return{type:"element",dependencies:[j.HA,j.py,j.B9],export:e=>{if(!(0,j.TX)(e))return null;let l=[];for(let n of e.getChildren()){let e=[];if(!(0,j.RN)(n))continue;let r=!1;for(let l of n.getChildren())(0,j.Xx)(l)&&(e.push((0,d.e)(t,l).replace(/\n/g,"\\n").trim()),l.__headerState===j.o5.ROW&&(r=!0));l.push("| ".concat(e.join(" | ")," |")),r&&l.push("| ".concat(e.map(e=>"---").join(" | ")," |"))}return l.join("\n")},regExp:nr,replace:(e,l,n)=>{let r=n[0];if(!r)return;if(no.test(r)){let t=e.getPreviousSibling();if(!t||!(0,j.TX)(t))return;let l=t.getChildren(),n=l[l.length-1];if(!n||!(0,j.RN)(n))return;n.getChildren().forEach(e=>{(0,j.Xx)(e)&&e.setHeaderStyles(j.o5.ROW,j.o5.ROW)}),e.remove();return}let o=na(r,t);if(null==o)return;let i=[o],a=e.getPreviousSibling(),s=o.length;for(;a&&!(!(0,c.bSg)(a)||1!==a.getChildrenSize());){let e=a.getFirstChild();if(!(0,c.kFe)(e))break;let l=na(e.getTextContent(),t);if(null==l)break;s=Math.max(s,l.length),i.unshift(l);let n=a.getPreviousSibling();a.remove(),a=n}let d=(0,j.pK)();for(let e of i){let l=(0,j.hx)();d.append(l);for(let n=0;n<s;n++)l.append(n<e.length?e[n]:ni("",t))}let u=e.getPreviousSibling();(0,j.TX)(u)&&function(e){let t=e.getFirstChild();return(0,j.RN)(t)?t.getChildrenSize():0}(u)===s?(u.append(...d.getChildren()),e.remove()):e.replace(d),d.selectEnd()}}}],nodes:[j.HA,j.B9,j.py],plugins:[{Component:()=>{let e,t,l,n=(0,T.c)(16),[r]=(0,E.DF)(),o=(0,p.use)(nv),s=(0,x.useEditDepth)(),{fieldProps:d,uuid:f}=(0,u.b)(),{schemaPath:m}=d,g="lexical-table-create-"+f,b;n[0]!==s||n[1]!==g?(b=(0,x.formatDrawerSlug)({slug:g,depth:s}),n[0]=s,n[1]=g,n[2]=b):b=n[2];let v=b,{toggleDrawer:C}=(0,a.a)(v,!0),k;return n[3]!==r||n[4]!==C?(k=()=>{if(!r.hasNodes([j.HA,j.py,j.B9]))throw Error("TablePlugin: TableNode, TableRowNode, or TableCellNode is not registered on editor");return(0,L.Sd)(r.registerCommand(nb,()=>{let e=null;return r.getEditorState().read(()=>{let t=(0,c.vJq)();(0,c.I2P)(t)&&(e=t)}),e&&C(),!0},c.jZM))},n[3]=r,n[4]=C,n[5]=k):k=n[5],n[6]!==o||n[7]!==r||n[8]!==C?(e=[o,r,C],n[6]=o,n[7]=r,n[8]=C,n[9]=e):e=n[9],(0,p.useEffect)(k,e),n[10]!==r?(t=(e,t)=>{t.columns&&t.rows&&r.dispatchCommand(j.Ng,{columns:String(t.columns),rows:String(t.rows)})},n[10]=r,n[11]=t):t=n[11],n[12]!==v||n[13]!==m||n[14]!==t?(l=(0,h.jsxs)(p.Fragment,{children:[(0,h.jsx)(i.a,{drawerSlug:v,drawerTitle:"Create Table",featureKey:"experimental_table",handleDrawerSubmit:t,schemaPath:m,schemaPathSuffix:"fields"}),(0,h.jsx)(B.A,{hasCellBackgroundColor:!1,hasCellMerge:!0,hasHorizontalScroll:!0})]}),n[12]=v,n[13]=m,n[14]=t,n[15]=l):l=n[15],l},position:"normal"},{Component:()=>{let e=(0,T.c)(3),[t]=(0,E.DF)(),l=(0,H.a)(),n;return e[0]!==t||e[1]!==l?(n=l?(0,F.createPortal)((0,h.jsx)(nh,{editor:t}),document.body):null,e[0]=t,e[1]=l,e[2]=n):n=e[2],n},position:"normal"},{Component:e=>{let t=(0,T.c)(3),{anchorElem:l}=e,n=(0,H.a)(),r;return t[0]!==l||t[1]!==n?(r=(0,F.createPortal)(n?(0,h.jsx)(nc,{anchorElem:null!=l?l:document.body,cellMerge:!0}):null,null!=l?l:document.body),t[0]=l,t[1]=n,t[2]=r):r=t[2],r},position:"floatingAnchorElem"},{Component:function(e){let t,l=(0,T.c)(2),{anchorElem:n}=e,r=void 0===n?document.body:n,[o]=(0,E.DF)();return(null==o?void 0:o.isEditable())?(l[0]!==r?(t=(0,F.createPortal)((0,h.jsx)(ng,{anchorElem:r}),r),l[0]=r,l[1]=t):t=l[1],t):null},position:"floatingAnchorElem"}],providers:[function(e){let{children:t}=e,[l,n]=(0,p.useState)({cellEditorConfig:null,cellEditorPlugins:null});return(0,h.jsx)(nv,{value:(0,p.useMemo)(()=>({cellEditorConfig:l.cellEditorConfig,cellEditorPlugins:l.cellEditorPlugins,set:(e,t)=>{n({cellEditorConfig:e,cellEditorPlugins:t})}}),[l.cellEditorConfig,l.cellEditorPlugins]),children:t})}],slashMenu:{groups:[z([{Icon:nn,key:"table",keywords:["table"],label:"Table",onSelect:e=>{let{editor:t}=e;t.dispatchCommand(nb,{})}}])]},toolbarFixed:{groups:[e1([{ChildComponent:nn,key:"table",label:"Table",onSelect:e=>{let{editor:t}=e;t.dispatchCommand(nb,{})}}])]}}),nC=()=>(0,h.jsxs)("svg",{"aria-hidden":"true",className:"icon",fill:"none",focusable:"false",height:"20",viewBox:"0 0 20 20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:[(0,h.jsx)("path",{d:"M14.6667 4H5.33333C4.59695 4 4 4.59695 4 5.33333V14.6667C4 15.403 4.59695 16 5.33333 16H14.6667C15.403 16 16 15.403 16 14.6667V5.33333C16 4.59695 15.403 4 14.6667 4Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),(0,h.jsx)("path",{d:"M7.99984 9.33366C8.73622 9.33366 9.33317 8.73671 9.33317 8.00033C9.33317 7.26395 8.73622 6.66699 7.99984 6.66699C7.26346 6.66699 6.6665 7.26395 6.6665 8.00033C6.6665 8.73671 7.26346 9.33366 7.99984 9.33366Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),(0,h.jsx)("path",{d:"M16 11.9995L13.9427 9.94214C13.6926 9.69218 13.3536 9.55176 13 9.55176C12.6464 9.55176 12.3074 9.69218 12.0573 9.94214L6 15.9995",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"})]});function nk(e){return null!=e.parentElement&&"LI"===e.parentElement.tagName&&null===e.previousSibling&&"checkbox"===e.getAttribute("aria-roledescription")}function ny(e){if(e.hasAttribute("data-lexical-upload-relation-to")&&e.hasAttribute("data-lexical-upload-id")){let t=e.getAttribute("data-lexical-upload-id"),l=e.getAttribute("data-lexical-upload-relation-to");if(null!=t&&null!=l)return{node:n_({data:{fields:{},relationTo:l,value:t}})}}return e.src.startsWith("file:///")||nk(e),null}var nw=class extends v.d{static clone(e){return new this({data:e.__data,format:e.__format,key:e.__key})}static getType(){return"upload"}static importDOM(){return{img:e=>({conversion:ny,priority:0})}}static importJSON(e){var t;1===e.version&&(null==e||null==(t=e.value)?void 0:t.id)&&(e.value=e.value.id),2!==e.version||(null==e?void 0:e.id)||(e.id=new b.default().toHexString(),e.version=3);let l=n_({data:{id:e.id,fields:e.fields,relationTo:e.relationTo,value:e.value}});return l.setFormat(e.format),l}static isInline(){return!1}decorate(){return(0,h.jsx)(RawUploadComponent,{data:this.__data,format:this.__format,nodeKey:this.getKey()})}exportDOM(){var e,t;let l=document.createElement("img");return l.setAttribute("data-lexical-upload-id",String(null==(e=this.__data)?void 0:e.value)),l.setAttribute("data-lexical-upload-relation-to",null==(t=this.__data)?void 0:t.relationTo),{element:l}}exportJSON(){return{...super.exportJSON(),...this.getData(),type:"upload",version:3}}getData(){return this.getLatest().__data}setData(e){this.getWritable().__data=e}updateDOM(){return!1}constructor({data:e,format:t,key:l}){super(t,l),this.__data=e}};function n_(e){let{data:t}=e;return(null==t?void 0:t.id)||(t.id=new b.default().toHexString()),(0,c.pTq)(new nw({data:t}))}var nE=p.lazy(()=>l.e(1714).then(l.bind(l,21714)).then(e=>({default:e.UploadComponent})));function nS(e){if(e.hasAttribute("data-lexical-upload-relation-to")&&e.hasAttribute("data-lexical-upload-id")){let t=e.getAttribute("data-lexical-upload-id"),l=e.getAttribute("data-lexical-upload-relation-to");if(null!=t&&null!=l)return{node:nL({data:{fields:{},relationTo:l,value:t}})}}return e.src.startsWith("file:///")||nk(e),null}var nT=class extends nw{static clone(e){return super.clone(e)}static getType(){return super.getType()}static importDOM(){return{img:e=>({conversion:nS,priority:0})}}static importJSON(e){var t;1===e.version&&(null==e||null==(t=e.value)?void 0:t.id)&&(e.value=e.value.id),2!==e.version||(null==e?void 0:e.id)||(e.id=new b.default().toHexString(),e.version=3);let l=nL({data:{id:e.id,fields:e.fields,relationTo:e.relationTo,value:e.value}});return l.setFormat(e.format),l}decorate(){return(0,h.jsx)(nE,{data:this.__data,nodeKey:this.getKey()})}exportJSON(){return super.exportJSON()}};function nL(e){let{data:t}=e;return(null==t?void 0:t.id)||(t.id=new b.default().toHexString()),(0,c.pTq)(new nT({data:t}))}function nN(e){return e instanceof nT}var nj=e=>{let{editor:t,relationTo:l,replaceNodeKey:n,value:r}=e;n?t.update(()=>{let e=(0,c.nsf)(n);e&&e.replace(nL({data:{fields:null,relationTo:l,value:r}}))}):t.dispatchCommand(nR,{fields:null,relationTo:l,value:r})},nI=e=>{let t,l=(0,T.c)(13),{enabledCollectionSlugs:n}=e,[r]=(0,E.DF)(),[o,a]=(0,p.useState)(null),s;l[0]!==n?(s={collectionSlugs:n,uploads:!0},l[0]=n,l[1]=s):s=l[1];let{closeListDrawer:d,ListDrawer:u,openListDrawer:f}=lP(s),m,g;l[2]!==r||l[3]!==f?(m=()=>r.registerCommand(i.c,e=>(a((null==e?void 0:e.replace)?null==e?void 0:e.replace.nodeKey:null),f(),!0),c.jZM),g=[r,f],l[2]=r,l[3]=f,l[4]=m,l[5]=g):(m=l[4],g=l[5]),(0,p.useEffect)(m,g),l[6]!==d||l[7]!==r||l[8]!==o?(t=e=>{let{collectionSlug:t,doc:l}=e;d(),nj({editor:r,relationTo:t,replaceNodeKey:o,value:l.id})},l[6]=d,l[7]=r,l[8]=o,l[9]=t):t=l[9];let b=t,v;return l[10]!==u||l[11]!==b?(v=(0,h.jsx)(u,{onSelect:b}),l[10]=u,l[11]=b,l[12]=v):v=l[12],v},nM=()=>{let e=(0,T.c)(3),[t]=(0,E.DF)(),l,n;return e[0]!==t?(l=()=>t.registerCommand(i.c,nA,c.jZM),n=[t],e[0]=t,e[1]=l,e[2]=n):(l=e[1],n=e[2]),(0,p.useEffect)(l,n),null},nF=e=>(0,h.jsx)(i.b,{...e,FallbackComponent:nM,uploads:!0,children:(0,h.jsx)(nI,{...e})});function nA(){return x.toast.error("No upload collections enabled"),!0}var nR=(0,c.guI)("INSERT_UPLOAD_COMMAND");function nP(e){let{slug:t}=e;return t}var nH=V({nodes:[nT],plugins:[{Component:e=>{let t,l=(0,T.c)(5),[n]=(0,E.DF)(),{config:r}=(0,C.b)(),{collections:o}=r,i,a;return l[0]!==n?(i=()=>{if(!n.hasNodes([nT]))throw Error("UploadPlugin: UploadNode not registered on editor");return(0,L.Sd)(n.registerCommand(nR,e=>(n.update(()=>{let t=(0,c.vJq)()||(0,c.S1w)();if((0,c.I2P)(t)){let l=nL({data:{id:e.id,fields:e.fields,relationTo:e.relationTo,value:e.value}}),{focus:n}=t,r=n.getNode();(0,L.Pe)(l),(0,c.bSg)(r)&&!r.__first&&r.remove()}}),!0),c.jZM))},a=[n],l[0]=n,l[1]=i,l[2]=a):(i=l[1],a=l[2]),(0,p.useEffect)(i,a),l[3]!==o?(t=(0,h.jsx)(nF,{enabledCollectionSlugs:o.map(nP)}),l[3]=o,l[4]=t):t=l[4],t},position:"normal"}],slashMenu:{groups:[z([{Icon:nC,key:"upload",keywords:["upload","image","file","img","picture","photo","media"],label:e=>{let{i18n:t}=e;return t.t("lexical:upload:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(i.c,{replace:!1})}}])]},toolbarFixed:{groups:[e1([{ChildComponent:nC,isActive:e=>{let{selection:t}=e;return!!(0,c.RTZ)(t)&&!!t.getNodes().length&&nN(t.getNodes()[0])},key:"upload",label:e=>{let{i18n:t}=e;return t.t("lexical:upload:label")},onSelect:e=>{let{editor:t}=e;t.dispatchCommand(i.c,{replace:!1})}}])]}}),nB={namespace:"lexical",theme:{block:"LexicalEditorTheme__block",blockCursor:"LexicalEditorTheme__blockCursor",characterLimit:"LexicalEditorTheme__characterLimit",code:"LexicalEditorTheme__code",codeHighlight:{atrule:"LexicalEditorTheme__tokenAttr",attr:"LexicalEditorTheme__tokenAttr",boolean:"LexicalEditorTheme__tokenProperty",builtin:"LexicalEditorTheme__tokenSelector",cdata:"LexicalEditorTheme__tokenComment",char:"LexicalEditorTheme__tokenSelector",class:"LexicalEditorTheme__tokenFunction","class-name":"LexicalEditorTheme__tokenFunction",comment:"LexicalEditorTheme__tokenComment",constant:"LexicalEditorTheme__tokenProperty",deleted:"LexicalEditorTheme__tokenProperty",doctype:"LexicalEditorTheme__tokenComment",entity:"LexicalEditorTheme__tokenOperator",function:"LexicalEditorTheme__tokenFunction",important:"LexicalEditorTheme__tokenVariable",inserted:"LexicalEditorTheme__tokenSelector",keyword:"LexicalEditorTheme__tokenAttr",namespace:"LexicalEditorTheme__tokenVariable",number:"LexicalEditorTheme__tokenProperty",operator:"LexicalEditorTheme__tokenOperator",prolog:"LexicalEditorTheme__tokenComment",property:"LexicalEditorTheme__tokenProperty",punctuation:"LexicalEditorTheme__tokenPunctuation",regex:"LexicalEditorTheme__tokenVariable",selector:"LexicalEditorTheme__tokenSelector",string:"LexicalEditorTheme__tokenSelector",symbol:"LexicalEditorTheme__tokenProperty",tag:"LexicalEditorTheme__tokenProperty",url:"LexicalEditorTheme__tokenOperator",variable:"LexicalEditorTheme__tokenVariable"},embedBlock:{base:"LexicalEditorTheme__embedBlock",focus:"LexicalEditorTheme__embedBlockFocus"},hashtag:"LexicalEditorTheme__hashtag",heading:{h1:"LexicalEditorTheme__h1",h2:"LexicalEditorTheme__h2",h3:"LexicalEditorTheme__h3",h4:"LexicalEditorTheme__h4",h5:"LexicalEditorTheme__h5",h6:"LexicalEditorTheme__h6"},hr:"LexicalEditorTheme__hr",hrSelected:"LexicalEditorTheme__hrSelected",indent:"LexicalEditorTheme__indent",inlineImage:"LexicalEditor__inline-image",link:"LexicalEditorTheme__link",list:{checklist:"LexicalEditorTheme__checklist",listitem:"LexicalEditorTheme__listItem",listitemChecked:"LexicalEditorTheme__listItemChecked",listitemUnchecked:"LexicalEditorTheme__listItemUnchecked",nested:{listitem:"LexicalEditorTheme__nestedListItem"},olDepth:["LexicalEditorTheme__ol1","LexicalEditorTheme__ol2","LexicalEditorTheme__ol3","LexicalEditorTheme__ol4","LexicalEditorTheme__ol5"],ul:"LexicalEditorTheme__ul"},ltr:"LexicalEditorTheme__ltr",mark:"LexicalEditorTheme__mark",markOverlap:"LexicalEditorTheme__markOverlap",paragraph:"LexicalEditorTheme__paragraph",quote:"LexicalEditorTheme__quote",relationship:"LexicalEditorTheme__relationship",rtl:"LexicalEditorTheme__rtl",tab:"LexicalEditorTheme__tabNode",table:"LexicalEditorTheme__table",tableAddColumns:"LexicalEditorTheme__tableAddColumns",tableAddRows:"LexicalEditorTheme__tableAddRows",tableAlignment:{center:"LexicalEditorTheme__tableAlignmentCenter",right:"LexicalEditorTheme__tableAlignmentRight"},tableCell:"LexicalEditorTheme__tableCell",tableCellActionButton:"LexicalEditorTheme__tableCellActionButton",tableCellActionButtonContainer:"LexicalEditorTheme__tableCellActionButtonContainer",tableCellHeader:"LexicalEditorTheme__tableCellHeader",tableCellResizer:"LexicalEditorTheme__tableCellResizer",tableCellSelected:"LexicalEditorTheme__tableCellSelected",tableFrozenColumn:"LexicalEditorTheme__tableFrozenColumn",tableRowStriping:"LexicalEditorTheme__tableRowStriping",tableScrollableWrapper:"LexicalEditorTheme__tableScrollableWrapper",tableSelected:"LexicalEditorTheme__tableSelected",tableSelection:"LexicalEditorTheme__tableSelection",text:{bold:"LexicalEditorTheme__textBold",code:"LexicalEditorTheme__textCode",italic:"LexicalEditorTheme__textItalic",strikethrough:"LexicalEditorTheme__textStrikethrough",subscript:"LexicalEditorTheme__textSubscript",superscript:"LexicalEditorTheme__textSuperscript",underline:"LexicalEditorTheme__textUnderline",underlineStrikethrough:"LexicalEditorTheme__textUnderlineStrikethrough"},upload:"editor-upload"}},nD=e=>{let t={enabledFeatures:[],enabledFormats:[],markdownTransformers:[],nodes:[],plugins:[],providers:[],slashMenu:{dynamicGroups:[],groups:[]},toolbarFixed:{groups:[]},toolbarInline:{groups:[]}},l={};if(e.forEach(e=>{var t;"toolbarFixed"===e.key&&(null==(t=e.sanitizedClientFeatureProps)?void 0:t.customGroups)&&(l={...l,...e.sanitizedClientFeatureProps.customGroups})}),!(null==e?void 0:e.size))return t;for(let n of(e.forEach(e=>{var l,n,r,o,i,a,s,d,u,c,h,p,f,m,g,b;if((null==(l=e.providers)?void 0:l.length)&&(t.providers=t.providers.concat(e.providers)),(null==(n=e.enableFormats)?void 0:n.length)&&t.enabledFormats.push(...e.enableFormats),null==(r=e.nodes)?void 0:r.length)for(let l of e.nodes)t.nodes.push(l);if((null==(o=e.plugins)?void 0:o.length)&&e.plugins.forEach((l,n)=>{var r;null==(r=t.plugins)||r.push({clientProps:e.sanitizedClientFeatureProps,Component:l.Component,key:e.key+n,position:l.position})}),null==(a=e.toolbarInline)||null==(i=a.groups)?void 0:i.length)for(let l of e.toolbarInline.groups){let e=t.toolbarInline.groups.find(e=>e.key===l.key);e?t.toolbarInline.groups=t.toolbarInline.groups.filter(e=>e.key!==l.key):e={...l,items:[]},(null==l||null==(h=l.items)?void 0:h.length)&&(e.items=e.items.concat(l.items)),null==(p=t.toolbarInline)||p.groups.push(e)}if(null==(d=e.toolbarFixed)||null==(s=d.groups)?void 0:s.length)for(let l of e.toolbarFixed.groups){let e=t.toolbarFixed.groups.find(e=>e.key===l.key);e?t.toolbarFixed.groups=t.toolbarFixed.groups.filter(e=>e.key!==l.key):e={...l,items:[]},(null==l||null==(f=l.items)?void 0:f.length)&&(e.items=e.items.concat(l.items)),null==(m=t.toolbarFixed)||m.groups.push(e)}if(null==(u=e.slashMenu)?void 0:u.groups)for(let l of((null==(g=e.slashMenu.dynamicGroups)?void 0:g.length)&&(t.slashMenu.dynamicGroups=t.slashMenu.dynamicGroups.concat(e.slashMenu.dynamicGroups)),e.slashMenu.groups)){let e=t.slashMenu.groups.find(e=>e.key===l.key);e?t.slashMenu.groups=t.slashMenu.groups.filter(e=>e.key!==l.key):e={...l,items:[]},(null==l||null==(b=l.items)?void 0:b.length)&&(e.items=e.items.concat(l.items)),t.slashMenu.groups.push(e)}if(null==(c=e.markdownTransformers)?void 0:c.length)for(let l of e.markdownTransformers)"function"==typeof l?t.markdownTransformers.push(l({allNodes:t.nodes,allTransformers:t.markdownTransformers})):t.markdownTransformers.push(l);t.enabledFeatures.push(e.key)}),Object.keys(l).length>0&&(t.toolbarFixed.groups=t.toolbarFixed.groups.map(e=>{let t=l[e.key];return t?D(e,t):e})),t.toolbarInline.groups.sort((e,t)=>e.order&&t.order?e.order-t.order:e.order?-1:+!!t.order),t.toolbarFixed.groups.sort((e,t)=>e.order&&t.order?e.order-t.order:e.order?-1:+!!t.order),t.toolbarInline.groups))n.items.sort((e,t)=>e.order&&t.order?e.order-t.order:e.order?-1:+!!t.order);for(let e of t.toolbarFixed.groups)e.items.sort((e,t)=>e.order&&t.order?e.order-t.order:e.order?-1:+!!t.order);return t};function nO(e,t,l){return{admin:l,features:nD(e),lexical:t,resolvedFeatureMap:e}}var nz=(0,p.lazy)(()=>Promise.all([l.e(4753),l.e(8423)]).then(l.bind(l,58423)).then(e=>({default:e.RichText}))),nJ=e=>{let t,l=(0,T.c)(16),{admin:n,clientFeatures:r,featureClientImportMap:o,featureClientSchemaMap:i,field:a,lexicalEditorConfig:s,schemaPath:d}=e,u;l[0]!==n?(u=void 0===n?{}:n,l[0]=n,l[1]=u):u=l[1];let c=u,f=void 0===s?nB:s,{config:m}=(0,C.b)(),[g,b]=(0,p.useState)(null),v,x;return l[2]!==c||l[3]!==r||l[4]!==m||l[5]!==o||l[6]!==i||l[7]!==a||l[8]!==g||l[9]!==f||l[10]!==d?(v=()=>{if(g)return;let e=[];for(let[t,l]of Object.entries(r))l.clientFeatureProvider&&e.push(l.clientFeatureProvider(l.clientFeatureProps));let t=f||nB;b(nO(function(e){var t,l,n;let{config:r,featureClientImportMap:o,featureClientSchemaMap:i,field:a,schemaPath:s,unSanitizedEditorConfig:d}=e;for(let e of d.features)if(!(null==e||null==(t=e.clientFeatureProps)?void 0:t.featureKey)||(null==e||null==(l=e.clientFeatureProps)?void 0:l.order)===void 0||(null==e||null==(n=e.clientFeatureProps)?void 0:n.order)===null)throw Error("A Feature you have installed does not return the client props as clientFeatureProps. Please make sure to always return those props, even if they are null, as other important props like order and featureKey are later on injected.");d.features=d.features.sort((e,t)=>e.clientFeatureProps.order-t.clientFeatureProps.order);let u=new Map;for(let e of d.features)u.set(e.clientFeatureProps.featureKey,e);let c=new Map,h=0;for(let e of d.features){let t="function"==typeof e.feature?e.feature({config:r,featureClientImportMap:o,featureClientSchemaMap:i,featureProviderMap:u,field:a,resolvedFeatures:c,schemaPath:s,unSanitizedEditorConfig:d}):e.feature;t.key=e.clientFeatureProps.featureKey,t.order=h,c.set(e.clientFeatureProps.featureKey,t),h++}return c}({config:m,featureClientImportMap:o,featureClientSchemaMap:i,field:a,schemaPath:null!=d?d:a.name,unSanitizedEditorConfig:{features:e,lexical:t}}),t,c))},x=[f,c,g,r,o,i,a,m,d],l[2]=c,l[3]=r,l[4]=m,l[5]=o,l[6]=i,l[7]=a,l[8]=g,l[9]=f,l[10]=d,l[11]=v,l[12]=x):(v=l[11],x=l[12]),(0,p.useEffect)(v,x),l[13]!==g||l[14]!==e?(t=(0,h.jsx)(p.Suspense,{fallback:(0,h.jsx)(C.d,{height:"35vh"}),children:g&&(0,h.jsx)(nz,{...e,editorConfig:g})}),l[13]=g,l[14]=e,l[15]=t):t=l[15],t};function nV(e){return e instanceof HTMLElement}function nK(){for(var e=arguments.length,t=Array(e),l=0;l<e;l++)t[l]=arguments[l];return t.filter(Boolean).join(" ")}var nU=new WeakMap;function nW(e){let t=e.changedTouches[0];return void 0===t?null:[t.clientX,t.clientY]}function nZ(e,t){let l=nU.get(e);if(void 0===l){let t=new Set,n=e=>{void 0!==l&&(l.start=nW(e))},r=e=>{if(void 0===l)return;let{start:n}=l;if(null===n)return;let r=nW(e);for(let l of t)null!==r&&l([r[0]-n[0],r[1]-n[1]],e)};e.addEventListener("touchstart",n),e.addEventListener("touchend",r),l={handleTouchend:r,handleTouchstart:n,listeners:t,start:null},nU.set(e,l)}return l.listeners.add(t),()=>{!function(e,t){let l=nU.get(e);if(void 0===l)return;let{listeners:n}=l;n.delete(t),0===n.size&&(nU.delete(e),e.removeEventListener("touchstart",l.handleTouchstart),e.removeEventListener("touchend",l.handleTouchend))}(e,t)}}function nG(e,t){return nZ(e,(e,l)=>{let[n,r]=e;n<0&&-n>Math.abs(r)&&t(n,l)})}function nq(e,t){return nZ(e,(e,l)=>{let[n,r]=e;n>0&&n>Math.abs(r)&&t(n,l)})}function nY(e,t){return nZ(e,(e,l)=>{let[n,r]=e;r<0&&-r>Math.abs(n)&&t(n,l)})}function nX(e,t){return nZ(e,(e,l)=>{let[n,r]=e;r>0&&r>Math.abs(n)&&t(n,l)})}var n$=()=>{let e=(0,T.c)(2),{EditButton:t}=(0,r.a)(),l;return e[0]!==t?(l=t?(0,h.jsx)(t,{}):null,e[0]=t,e[1]=l):l=e[1],l},nQ=()=>{let e=(0,T.c)(2),{RemoveButton:t}=(0,r.a)(),l;return e[0]!==t?(l=t?(0,h.jsx)(t,{}):null,e[0]=t,e[1]=l):l=e[1],l},n0=()=>{let e=(0,T.c)(2),{Label:t}=(0,r.a)(),l;return e[0]!==t?(l=t?(0,h.jsx)(t,{}):null,e[0]=t,e[1]=l):l=e[1],l},n1=e=>{let t=(0,T.c)(3),{children:l}=e,{InlineBlockContainer:n}=(0,r.a)(),o;return t[0]!==n||t[1]!==l?(o=n?(0,h.jsx)(n,{children:l}):null,t[0]=n,t[1]=l,t[2]=o):o=t[2],o},n2=e=>{let t=(0,T.c)(6),{children:l,editButton:n,Label:r,removeButton:o}=e,{BlockCollapsible:i}=en(),a;return t[0]!==i||t[1]!==r||t[2]!==l||t[3]!==n||t[4]!==o?(a=i?(0,h.jsx)(i,{editButton:n,Label:r,removeButton:o,children:l}):null,t[0]=i,t[1]=r,t[2]=l,t[3]=n,t[4]=o,t[5]=a):a=t[5],a},n5=()=>{let e=(0,T.c)(2),{EditButton:t}=en(),l;return e[0]!==t?(l=t?(0,h.jsx)(t,{}):null,e[0]=t,e[1]=l):l=e[1],l},n6=()=>{let e=(0,T.c)(2),{RemoveButton:t}=en(),l;return e[0]!==t?(l=t?(0,h.jsx)(t,{}):null,e[0]=t,e[1]=l):l=e[1],l},n3=e=>{let{apiURL:t,depth:l,draft:n,locale:r}=e;return async e=>{let{id:o,collectionSlug:i,select:a}=e,s=(0,O.A)({depth:null!=l?l:0,draft:null!=n&&n,locale:r,select:a},{addQueryPrefix:!0});return await fetch("".concat(t,"/").concat(i,"/").concat(o).concat(s),{credentials:"include",headers:{Accept:"application/json","Content-Type":"application/json"},method:"GET"}).then(e=>e.json())}}}}]);