{"version": 3, "file": "6455.js", "mappings": "2hBAIA,UACA,MACA,aAAU,GAAW,EAErB,YADmB,+BAAyB,EAC5C,6CACA,MAA6B,kBAAoB,CAAC,GAAyB,aAS3E,CARE,eAAS,MACX,GAGI,mBAAqB,MACzB,GACA,CAAK,CACL,CAAG,MACI,kBAAoB,KAGlB,gBAAkB,IAC3B,oCACA,+CACA,wEACA,mEACA,yCACA,CAAG,EARH,CASA,mDC3BO,MAA+C,2BAAqB,8CAA+C,YAAU,QAAa,kBAAgB", "sources": ["webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js", "webpack://next-shadcn-dashboard-starter/?f441"], "sourcesContent": ["import \"../../chunk-BUSYA2B4.js\";\nimport { useSelectedLayoutSegments } from \"next/navigation\";\nimport React, { useEffect } from \"react\";\nimport { createOrReadKeylessAction } from \"../keyless-actions\";\nconst KeylessCreatorOrReader = (props) => {\n  var _a;\n  const { children } = props;\n  const segments = useSelectedLayoutSegments();\n  const isNotFoundRoute = ((_a = segments[0]) == null ? void 0 : _a.startsWith(\"/_not-found\")) || false;\n  const [state, fetchKeys] = React.useActionState(createOrReadKeylessAction, null);\n  useEffect(() => {\n    if (isNotFoundRoute) {\n      return;\n    }\n    React.startTransition(() => {\n      fetchKeys();\n    });\n  }, [isNotFoundRoute]);\n  if (!React.isValidElement(children)) {\n    return children;\n  }\n  return React.cloneElement(children, {\n    key: state == null ? void 0 : state.publishableKey,\n    publishableKey: state == null ? void 0 : state.publishableKey,\n    __internal_keyless_claimKeylessApplicationUrl: state == null ? void 0 : state.claimUrl,\n    __internal_keyless_copyInstanceKeysUrl: state == null ? void 0 : state.apiKeysUrl,\n    __internal_bypassMissingPublishableKey: true\n  });\n};\nexport {\n  KeylessCreatorOrReader\n};\n//# sourceMappingURL=keyless-creator-reader.js.map", "import { createServerReference, callServer, findSourceMapURL } from 'private-next-rsc-action-client-wrapper'\nexport const createOrReadKeylessAction = /*#__PURE__*/createServerReference(\"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261\", callServer, undefined, findSourceMapURL, \"createOrReadKeylessAction\")"], "names": [], "sourceRoot": ""}