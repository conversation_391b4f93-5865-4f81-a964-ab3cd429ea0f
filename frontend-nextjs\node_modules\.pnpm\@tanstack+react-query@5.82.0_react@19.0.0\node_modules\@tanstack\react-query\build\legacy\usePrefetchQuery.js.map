{"version": 3, "sources": ["../../src/usePrefetchQuery.tsx"], "sourcesContent": ["import { useQueryClient } from './QueryClientProvider'\nimport type { DefaultError, QueryClient, QueryKey } from '@tanstack/query-core'\nimport type { UsePrefetchQueryOptions } from './types'\n\nexport function usePrefetchQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UsePrefetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchQuery(options)\n  }\n}\n"], "mappings": ";AAAA,SAAS,sBAAsB;AAIxB,SAAS,iBAMd,SACA,aACA;AACA,QAAM,SAAS,eAAe,WAAW;AAEzC,MAAI,CAAC,OAAO,cAAc,QAAQ,QAAQ,GAAG;AAC3C,WAAO,cAAc,OAAO;AAAA,EAC9B;AACF;", "names": []}