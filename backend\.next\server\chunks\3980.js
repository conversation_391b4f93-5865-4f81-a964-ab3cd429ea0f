"use strict";exports.id=3980,exports.ids=[3980],exports.modules={13980:(a,e,i)=>{i.r(e),i.d(e,{default:()=>s,ro:()=>l});let t={lessThanXSeconds:{one:"mai puțin de o secundă",other:"mai puțin de {{count}} secunde"},xSeconds:{one:"1 secundă",other:"{{count}} secunde"},halfAMinute:"jumătate de minut",lessThanXMinutes:{one:"mai puțin de un minut",other:"mai puțin de {{count}} minute"},xMinutes:{one:"1 minut",other:"{{count}} minute"},aboutXHours:{one:"circa 1 oră",other:"circa {{count}} ore"},xHours:{one:"1 oră",other:"{{count}} ore"},xDays:{one:"1 zi",other:"{{count}} zile"},aboutXWeeks:{one:"circa o săptăm\xe2nă",other:"circa {{count}} săptăm\xe2ni"},xWeeks:{one:"1 săptăm\xe2nă",other:"{{count}} săptăm\xe2ni"},aboutXMonths:{one:"circa 1 lună",other:"circa {{count}} luni"},xMonths:{one:"1 lună",other:"{{count}} luni"},aboutXYears:{one:"circa 1 an",other:"circa {{count}} ani"},xYears:{one:"1 an",other:"{{count}} ani"},overXYears:{one:"peste 1 an",other:"peste {{count}} ani"},almostXYears:{one:"aproape 1 an",other:"aproape {{count}} ani"}};var n=i(89500);let r={date:(0,n.k)({formats:{full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd.MM.yyyy"},defaultWidth:"full"}),time:(0,n.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,n.k)({formats:{full:"{{date}} 'la' {{time}}",long:"{{date}} 'la' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"eeee 'trecută la' p",yesterday:"'ieri la' p",today:"'astăzi la' p",tomorrow:"'m\xe2ine la' p",nextWeek:"eeee 'viitoare la' p",other:"P"};var u=i(84246);let m={ordinalNumber:(a,e)=>String(a),era:(0,u.o)({values:{narrow:["\xce","D"],abbreviated:["\xce.d.C.","D.C."],wide:["\xcenainte de Cristos","După Cristos"]},defaultWidth:"wide"}),quarter:(0,u.o)({values:{narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["primul trimestru","al doilea trimestru","al treilea trimestru","al patrulea trimestru"]},defaultWidth:"wide",argumentCallback:a=>a-1}),month:(0,u.o)({values:{narrow:["I","F","M","A","M","I","I","A","S","O","N","D"],abbreviated:["ian","feb","mar","apr","mai","iun","iul","aug","sep","oct","noi","dec"],wide:["ianuarie","februarie","martie","aprilie","mai","iunie","iulie","august","septembrie","octombrie","noiembrie","decembrie"]},defaultWidth:"wide"}),day:(0,u.o)({values:{narrow:["d","l","m","m","j","v","s"],short:["du","lu","ma","mi","jo","vi","s\xe2"],abbreviated:["dum","lun","mar","mie","joi","vin","s\xe2m"],wide:["duminică","luni","marți","miercuri","joi","vineri","s\xe2mbătă"]},defaultWidth:"wide"}),dayPeriod:(0,u.o)({values:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"ami",morning:"dim",afternoon:"da",evening:"s",night:"n"},abbreviated:{am:"AM",pm:"PM",midnight:"miezul nopții",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"},wide:{am:"a.m.",pm:"p.m.",midnight:"miezul nopții",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"},abbreviated:{am:"AM",pm:"PM",midnight:"miezul nopții",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"},wide:{am:"a.m.",pm:"p.m.",midnight:"miezul nopții",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"}},defaultFormattingWidth:"wide"})};var d=i(22584);let l={code:"ro",formatDistance:(a,e,i)=>{let n,r=t[a];if(n="string"==typeof r?r:1===e?r.one:r.other.replace("{{count}}",String(e)),i?.addSuffix)if(i.comparison&&i.comparison>0)return"\xeen "+n;else return n+" \xeen urmă";return n},formatLong:r,formatRelative:(a,e,i,t)=>o[a],localize:m,match:{ordinalNumber:(0,i(43416).K)({matchPattern:/^(\d+)?/i,parsePattern:/\d+/i,valueCallback:a=>parseInt(a,10)}),era:(0,d.A)({matchPatterns:{narrow:/^(Î|D)/i,abbreviated:/^(Î\.?\s?d\.?\s?C\.?|Î\.?\s?e\.?\s?n\.?|D\.?\s?C\.?|e\.?\s?n\.?)/i,wide:/^(Înainte de Cristos|Înaintea erei noastre|După Cristos|Era noastră)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^ÎC/i,/^DC/i],wide:[/^(Înainte de Cristos|Înaintea erei noastre)/i,/^(După Cristos|Era noastră)/i]},defaultParseWidth:"any"}),quarter:(0,d.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^trimestrul [1234]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:a=>a+1}),month:(0,d.A)({matchPatterns:{narrow:/^[ifmaasond]/i,abbreviated:/^(ian|feb|mar|apr|mai|iun|iul|aug|sep|oct|noi|dec)/i,wide:/^(ianuarie|februarie|martie|aprilie|mai|iunie|iulie|august|septembrie|octombrie|noiembrie|decembrie)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^i/i,/^f/i,/^m/i,/^a/i,/^m/i,/^i/i,/^i/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ia/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^iun/i,/^iul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,d.A)({matchPatterns:{narrow:/^[dlmjvs]/i,short:/^(d|l|ma|mi|j|v|s)/i,abbreviated:/^(dum|lun|mar|mie|jo|vi|sâ)/i,wide:/^(duminica|luni|marţi|miercuri|joi|vineri|sâmbătă)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^mi/i,/^j/i,/^v/i,/^s/i]},defaultParseWidth:"any"}),dayPeriod:(0,d.A)({matchPatterns:{narrow:/^(a|p|mn|a|(dimineaţa|după-amiaza|seara|noaptea))/i,any:/^([ap]\.?\s?m\.?|miezul nopții|amiaza|(dimineaţa|după-amiaza|seara|noaptea))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mn/i,noon:/amiaza/i,morning:/dimineaţa/i,afternoon:/după-amiaza/i,evening:/seara/i,night:/noaptea/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:1}},s=l},22584:(a,e,i)=>{function t(a){return(e,i={})=>{let t,n=i.width,r=n&&a.matchPatterns[n]||a.matchPatterns[a.defaultMatchWidth],o=e.match(r);if(!o)return null;let u=o[0],m=n&&a.parsePatterns[n]||a.parsePatterns[a.defaultParseWidth],d=Array.isArray(m)?function(a,e){for(let i=0;i<a.length;i++)if(e(a[i]))return i}(m,a=>a.test(u)):function(a,e){for(let i in a)if(Object.prototype.hasOwnProperty.call(a,i)&&e(a[i]))return i}(m,a=>a.test(u));return t=a.valueCallback?a.valueCallback(d):d,{value:t=i.valueCallback?i.valueCallback(t):t,rest:e.slice(u.length)}}}i.d(e,{A:()=>t})},43416:(a,e,i)=>{i.d(e,{K:()=>t});function t(a){return(e,i={})=>{let t=e.match(a.matchPattern);if(!t)return null;let n=t[0],r=e.match(a.parsePattern);if(!r)return null;let o=a.valueCallback?a.valueCallback(r[0]):r[0];return{value:o=i.valueCallback?i.valueCallback(o):o,rest:e.slice(n.length)}}}},84246:(a,e,i)=>{i.d(e,{o:()=>t});function t(a){return(e,i)=>{let t;if("formatting"===(i?.context?String(i.context):"standalone")&&a.formattingValues){let e=a.defaultFormattingWidth||a.defaultWidth,n=i?.width?String(i.width):e;t=a.formattingValues[n]||a.formattingValues[e]}else{let e=a.defaultWidth,n=i?.width?String(i.width):a.defaultWidth;t=a.values[n]||a.values[e]}return t[a.argumentCallback?a.argumentCallback(e):e]}}},89500:(a,e,i)=>{i.d(e,{k:()=>t});function t(a){return (e={})=>{let i=e.width?String(e.width):a.defaultWidth;return a.formats[i]||a.formats[a.defaultWidth]}}}};