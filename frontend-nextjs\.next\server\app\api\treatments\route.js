try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="ba14c715-ce76-47c0-83f8-3d32d4181d1c",e._sentryDebugIdIdentifier="sentry-dbid-ba14c715-ce76-47c0-83f8-3d32d4181d1c")}catch(e){}"use strict";(()=>{var e={};e.id=7057,e.ids=[7057],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6926:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>k,serverHooks:()=>A,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>b});var o={};t.r(o),t.d(o,{DELETE:()=>v,GET:()=>h,HEAD:()=>w,OPTIONS:()=>T,PATCH:()=>f,POST:()=>g,PUT:()=>m});var s=t(86047),n=t(85544),i=t(36135),a=t(63033),p=t(53547),d=t(54360),u=t(19761);let c=(0,p.ZA)(async(e,r)=>{try{let t=(0,d.o)(e),{searchParams:o}=new URL(r.url),s=parseInt(o.get("limit")||"10"),n=parseInt(o.get("page")||"1"),i=await t.getTreatments({limit:s,page:n});return(0,p.$y)(i)}catch(e){return console.error("Error fetching treatments:",e),(0,p.WX)("Failed to fetch treatments")}}),x=(0,p.ZA)(async(e,r)=>{try{let t=(0,d.o)(e),o=await r.json(),s=await t.createTreatment(o);return(0,p.$y)(s,201)}catch(e){return console.error("Error creating treatment:",e),(0,p.WX)("Failed to create treatment")}}),l={...a},q="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;function y(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,o)=>{let s;try{let e=q?.getStore();s=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/treatments",headers:s}).apply(t,o)}})}let h=y(c,"GET"),g=y(x,"POST"),m=y(void 0,"PUT"),f=y(void 0,"PATCH"),v=y(void 0,"DELETE"),w=y(void 0,"HEAD"),T=y(void 0,"OPTIONS"),k=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/treatments/route",pathname:"/api/treatments",filename:"route",bundlePath:"app/api/treatments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\treatments\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:E,workUnitAsyncStorage:b,serverHooks:A}=k;function P(){return(0,i.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:b})}},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[55,3738,1950,5886,9615,125],()=>t(6926));module.exports=o})();
//# sourceMappingURL=route.js.map