try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="8dc84ff9-60b3-4b19-a402-71390e16acd0",e._sentryDebugIdIdentifier="sentry-dbid-8dc84ff9-60b3-4b19-a402-71390e16acd0")}catch(e){}"use strict";exports.id=7331,exports.ids=[7331],exports.modules={1378:(e,t,a)=>{a.d(t,{V:()=>c});var l=a(24443);a(60222);var n=a(49913),r=a(72595),s=a(33284),i=a(39255);let o=()=>(0,l.jsx)(i.YJP,{className:"size-4","data-sentry-element":"ChevronLeftIcon","data-sentry-component":"LeftIcon","data-sentry-source-file":"calendar.tsx"}),d=()=>(0,l.jsx)(i.vKP,{className:"size-4","data-sentry-element":"ChevronRightIcon","data-sentry-component":"RightIcon","data-sentry-source-file":"calendar.tsx"});function c({className:e,classNames:t,showOutsideDays:a=!0,...i}){return(0,l.jsx)(n.hv,{showOutsideDays:a,className:(0,r.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,r.cn)((0,s.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,r.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===i.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,r.cn)((0,s.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:o,IconRight:d},...i,"data-sentry-element":"DayPicker","data-sentry-component":"Calendar","data-sentry-source-file":"calendar.tsx"})}},17880:(e,t,a)=>{a.d(t,{c:()=>n});var l=a(60222);function n(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}},27124:(e,t,a)=>{a.d(t,{r:()=>l});let l={textOperators:[{label:"Contains",value:"iLike"},{label:"Does not contain",value:"notILike"},{label:"Is",value:"eq"},{label:"Is not",value:"ne"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],numericOperators:[{label:"Is",value:"eq"},{label:"Is not",value:"ne"},{label:"Is less than",value:"lt"},{label:"Is less than or equal to",value:"lte"},{label:"Is greater than",value:"gt"},{label:"Is greater than or equal to",value:"gte"},{label:"Is between",value:"isBetween"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],dateOperators:[{label:"Is",value:"eq"},{label:"Is not",value:"ne"},{label:"Is before",value:"lt"},{label:"Is after",value:"gt"},{label:"Is on or before",value:"lte"},{label:"Is on or after",value:"gte"},{label:"Is between",value:"isBetween"},{label:"Is relative to today",value:"isRelativeToToday"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],selectOperators:[{label:"Is",value:"eq"},{label:"Is not",value:"ne"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],multiSelectOperators:[{label:"Has any of",value:"inArray"},{label:"Has none of",value:"notInArray"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],booleanOperators:[{label:"Is",value:"eq"},{label:"Is not",value:"ne"}],sortOrders:[{label:"Asc",value:"asc"},{label:"Desc",value:"desc"}],filterVariants:["text","number","range","date","dateRange","boolean","select","multiSelect"],operators:["iLike","notILike","eq","ne","inArray","notInArray","isEmpty","isNotEmpty","lt","lte","gt","gte","isBetween","isRelativeToToday"],joinOperators:["and","or"]}},61074:(e,t,a)=>{a.d(t,{Table:()=>r,TableBody:()=>i,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>s,TableRow:()=>o});var l=a(24443);a(60222);var n=a(72595);function r({className:e,...t}){return(0,l.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,l.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})})}function s({className:e,...t}){return(0,l.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",e),...t,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function i({className:e,...t}){return(0,l.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function o({className:e,...t}){return(0,l.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function d({className:e,...t}){return(0,l.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c({className:e,...t}){return(0,l.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},68882:(e,t,a)=>{a.d(t,{w:()=>$});var l=a(24443),n=a(60222),r=a(24417),s=a(40574),i=a(33284),o=a(1378),d=a(5149),c=a(75895);function u(e,t={}){if(!e)return"";try{return new Intl.DateTimeFormat("en-US",{month:t.month??"long",day:t.day??"numeric",year:t.year??"numeric",...t}).format(new Date(e))}catch(e){return""}}function m(e){return e&&"object"==typeof e&&!Array.isArray(e)}function f(e){if(!e)return;let t=new Date("string"==typeof e?Number(e):e);return Number.isNaN(t.getTime())?void 0:t}function x(e){return null==e?[]:Array.isArray(e)?e.map(e=>{if("number"==typeof e||"string"==typeof e)return e}):"string"==typeof e||"number"==typeof e?[e]:[]}function p({column:e,title:t,multiple:a}){let p=e.getFilterValue(),y=n.useMemo(()=>{if(!p)return a?{from:void 0,to:void 0}:[];if(a){let e=x(p);return{from:f(e[0]),to:f(e[1])}}let e=f(x(p)[0]);return e?[e]:[]},[p,a]),b=n.useCallback(t=>{if(!t)return void e.setFilterValue(void 0);if(!a||"getTime"in t)!a&&"getTime"in t&&e.setFilterValue(t.getTime());else{let a=t.from?.getTime(),l=t.to?.getTime();e.setFilterValue(a||l?[a,l]:void 0)}},[e,a]),g=n.useCallback(t=>{t.stopPropagation(),e.setFilterValue(void 0)},[e]),h=n.useMemo(()=>a?!!m(y)&&(y.from||y.to):!!Array.isArray(y)&&y.length>0,[a,y]),v=n.useCallback(e=>e.from||e.to?e.from&&e.to?`${u(e.from)} - ${u(e.to)}`:u(e.from??e.to):"",[]),j=n.useMemo(()=>{if(a){if(!m(y))return null;let e=y.from||y.to,a=e?v(y):"Select date range";return(0,l.jsxs)("span",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{children:t}),e&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c.Separator,{orientation:"vertical",className:"mx-0.5 data-[orientation=vertical]:h-4"}),(0,l.jsx)("span",{children:a})]})]})}if(m(y))return null;let e=y.length>0,n=e?u(y[0]):"Select date";return(0,l.jsxs)("span",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{children:t}),e&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c.Separator,{orientation:"vertical",className:"mx-0.5 data-[orientation=vertical]:h-4"}),(0,l.jsx)("span",{children:n})]})]})},[y,a,v,t]);return(0,l.jsxs)(d.AM,{"data-sentry-element":"Popover","data-sentry-component":"DataTableDateFilter","data-sentry-source-file":"data-table-date-filter.tsx",children:[(0,l.jsx)(d.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"data-table-date-filter.tsx",children:(0,l.jsxs)(i.$,{variant:"outline",size:"sm",className:"border-dashed","data-sentry-element":"Button","data-sentry-source-file":"data-table-date-filter.tsx",children:[h?(0,l.jsx)("div",{role:"button","aria-label":`Clear ${t} filter`,tabIndex:0,onClick:g,className:"focus-visible:ring-ring rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-1 focus-visible:outline-none",children:(0,l.jsx)(r.A,{})}):(0,l.jsx)(s.A,{}),j]})}),(0,l.jsx)(d.hl,{className:"w-auto p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"data-table-date-filter.tsx",children:a?(0,l.jsx)(o.V,{initialFocus:!0,mode:"range",selected:m(y)?y:{from:void 0,to:void 0},onSelect:b}):(0,l.jsx)(o.V,{initialFocus:!0,mode:"single",selected:m(y)?void 0:y[0],onSelect:b})})]})}var y=a(79745),b=a(10531),g=a(94902),h=a(44784),v=a(72595);function j({className:e,...t}){return(0,l.jsx)(g.uB,{"data-slot":"command",className:(0,v.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...t,"data-sentry-element":"CommandPrimitive","data-sentry-component":"Command","data-sentry-source-file":"command.tsx"})}function w({className:e,...t}){return(0,l.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3","data-sentry-component":"CommandInput","data-sentry-source-file":"command.tsx",children:[(0,l.jsx)(h.A,{className:"size-4 shrink-0 opacity-50","data-sentry-element":"SearchIcon","data-sentry-source-file":"command.tsx"}),(0,l.jsx)(g.uB.Input,{"data-slot":"command-input",className:(0,v.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...t,"data-sentry-element":"CommandPrimitive.Input","data-sentry-source-file":"command.tsx"})]})}function N({className:e,...t}){return(0,l.jsx)(g.uB.List,{"data-slot":"command-list",className:(0,v.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",e),...t,"data-sentry-element":"CommandPrimitive.List","data-sentry-component":"CommandList","data-sentry-source-file":"command.tsx"})}function C({...e}){return(0,l.jsx)(g.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...e,"data-sentry-element":"CommandPrimitive.Empty","data-sentry-component":"CommandEmpty","data-sentry-source-file":"command.tsx"})}function S({className:e,...t}){return(0,l.jsx)(g.uB.Group,{"data-slot":"command-group",className:(0,v.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...t,"data-sentry-element":"CommandPrimitive.Group","data-sentry-component":"CommandGroup","data-sentry-source-file":"command.tsx"})}function I({className:e,...t}){return(0,l.jsx)(g.uB.Separator,{"data-slot":"command-separator",className:(0,v.cn)("bg-border -mx-1 h-px",e),...t,"data-sentry-element":"CommandPrimitive.Separator","data-sentry-component":"CommandSeparator","data-sentry-source-file":"command.tsx"})}function z({className:e,...t}){return(0,l.jsx)(g.uB.Item,{"data-slot":"command-item",className:(0,v.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t,"data-sentry-element":"CommandPrimitive.Item","data-sentry-component":"CommandItem","data-sentry-source-file":"command.tsx"})}a(61780);var T=a(39255);function F({column:e,title:t,options:a,multiple:s}){let[o,u]=n.useState(!1),m=e?.getFilterValue(),f=n.useMemo(()=>new Set(Array.isArray(m)?m:[]),[m]),x=n.useCallback((t,a)=>{if(e)if(s){let l=new Set(f);a?l.delete(t.value):l.add(t.value);let n=Array.from(l);e.setFilterValue(n.length?n:void 0)}else e.setFilterValue(a?void 0:[t.value]),u(!1)},[e,s,f]),p=n.useCallback(t=>{t?.stopPropagation(),e?.setFilterValue(void 0)},[e]);return(0,l.jsxs)(d.AM,{open:o,onOpenChange:u,"data-sentry-element":"Popover","data-sentry-component":"DataTableFacetedFilter","data-sentry-source-file":"data-table-faceted-filter.tsx",children:[(0,l.jsx)(d.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"data-table-faceted-filter.tsx",children:(0,l.jsxs)(i.$,{variant:"outline",size:"sm",className:"border-dashed","data-sentry-element":"Button","data-sentry-source-file":"data-table-faceted-filter.tsx",children:[f?.size>0?(0,l.jsx)("div",{role:"button","aria-label":`Clear ${t} filter`,tabIndex:0,onClick:p,className:"focus-visible:ring-ring rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-1 focus-visible:outline-none",children:(0,l.jsx)(r.A,{})}):(0,l.jsx)(y.A,{}),t,f?.size>0&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c.Separator,{orientation:"vertical",className:"mx-0.5 data-[orientation=vertical]:h-4"}),(0,l.jsx)(b.E,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:f.size}),(0,l.jsx)("div",{className:"hidden items-center gap-1 lg:flex",children:f.size>2?(0,l.jsxs)(b.E,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[f.size," selected"]}):a.filter(e=>f.has(e.value)).map(e=>(0,l.jsx)(b.E,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:e.label},e.value))})]})]})}),(0,l.jsx)(d.hl,{className:"w-[12.5rem] p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"data-table-faceted-filter.tsx",children:(0,l.jsxs)(j,{"data-sentry-element":"Command","data-sentry-source-file":"data-table-faceted-filter.tsx",children:[(0,l.jsx)(w,{placeholder:t,"data-sentry-element":"CommandInput","data-sentry-source-file":"data-table-faceted-filter.tsx"}),(0,l.jsxs)(N,{className:"max-h-full","data-sentry-element":"CommandList","data-sentry-source-file":"data-table-faceted-filter.tsx",children:[(0,l.jsx)(C,{"data-sentry-element":"CommandEmpty","data-sentry-source-file":"data-table-faceted-filter.tsx",children:"No results found."}),(0,l.jsx)(S,{className:"max-h-[18.75rem] overflow-x-hidden overflow-y-auto","data-sentry-element":"CommandGroup","data-sentry-source-file":"data-table-faceted-filter.tsx",children:a.map(e=>{let t=f.has(e.value);return(0,l.jsxs)(z,{onSelect:()=>x(e,t),children:[(0,l.jsx)("div",{className:(0,v.cn)("border-primary flex size-4 items-center justify-center rounded-sm border",t?"bg-primary":"opacity-50 [&_svg]:invisible"),children:(0,l.jsx)(T.Srz,{})}),e.icon&&(0,l.jsx)(e.icon,{}),(0,l.jsx)("span",{className:"truncate",children:e.label}),e.count&&(0,l.jsx)("span",{className:"ml-auto font-mono text-xs",children:e.count})]},e.value)})}),f.size>0&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(I,{}),(0,l.jsx)(S,{children:(0,l.jsx)(z,{onSelect:()=>p(),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}var k=a(19342),A=a(18984),P=a(35837);function M({className:e,defaultValue:t,value:a,min:r=0,max:s=100,...i}){let o=n.useMemo(()=>Array.isArray(a)?a:Array.isArray(t)?t:[r,s],[a,t,r,s]);return(0,l.jsxs)(P.bL,{"data-slot":"slider",defaultValue:t,value:a,min:r,max:s,className:(0,v.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",e),...i,"data-sentry-element":"SliderPrimitive.Root","data-sentry-component":"Slider","data-sentry-source-file":"slider.tsx",children:[(0,l.jsx)(P.CC,{"data-slot":"slider-track",className:(0,v.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),"data-sentry-element":"SliderPrimitive.Track","data-sentry-source-file":"slider.tsx",children:(0,l.jsx)(P.Q6,{"data-slot":"slider-range",className:(0,v.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full"),"data-sentry-element":"SliderPrimitive.Range","data-sentry-source-file":"slider.tsx"})}),Array.from({length:o.length},(e,t)=>(0,l.jsx)(P.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},t))]})}function V(e){return Array.isArray(e)&&2===e.length&&"number"==typeof e[0]&&"number"==typeof e[1]}function R({column:e,title:t}){let a=n.useId(),s=V(e.getFilterValue())?e.getFilterValue():void 0,o=e.columnDef.meta?.range,u=e.columnDef.meta?.unit,{min:m,max:f,step:x}=n.useMemo(()=>{let t=0,a=100;if(o&&V(o))[t,a]=o;else{let l=e.getFacetedMinMaxValues();if(l&&Array.isArray(l)&&2===l.length){let[e,n]=l;"number"==typeof e&&"number"==typeof n&&(t=e,a=n)}}let l=a-t;return{min:t,max:a,step:l<=20?1:l<=100?Math.ceil(l/20):Math.ceil(l/50)}},[e,o]),p=n.useMemo(()=>s??[m,f],[s,m,f]),b=n.useCallback(e=>e.toLocaleString(void 0,{maximumFractionDigits:0}),[]),g=n.useCallback(t=>{let a=Number(t.target.value);!Number.isNaN(a)&&a>=m&&a<=p[1]&&e.setFilterValue([a,p[1]])},[e,m,p]),h=n.useCallback(t=>{let a=Number(t.target.value);!Number.isNaN(a)&&a<=f&&a>=p[0]&&e.setFilterValue([p[0],a])},[e,f,p]),j=n.useCallback(t=>{Array.isArray(t)&&2===t.length&&e.setFilterValue(t)},[e]),w=n.useCallback(t=>{t.target instanceof HTMLDivElement&&t.stopPropagation(),e.setFilterValue(void 0)},[e]);return(0,l.jsxs)(d.AM,{"data-sentry-element":"Popover","data-sentry-component":"DataTableSliderFilter","data-sentry-source-file":"data-table-slider-filter.tsx",children:[(0,l.jsx)(d.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"data-table-slider-filter.tsx",children:(0,l.jsxs)(i.$,{variant:"outline",size:"sm",className:"border-dashed","data-sentry-element":"Button","data-sentry-source-file":"data-table-slider-filter.tsx",children:[s?(0,l.jsx)("div",{role:"button","aria-label":`Clear ${t} filter`,tabIndex:0,className:"focus-visible:ring-ring rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-1 focus-visible:outline-none",onClick:w,children:(0,l.jsx)(r.A,{})}):(0,l.jsx)(y.A,{}),(0,l.jsx)("span",{children:t}),s?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c.Separator,{orientation:"vertical",className:"mx-0.5 data-[orientation=vertical]:h-4"}),b(s[0])," -"," ",b(s[1]),u?` ${u}`:""]}):null]})}),(0,l.jsxs)(d.hl,{align:"start",className:"flex w-auto flex-col gap-4","data-sentry-element":"PopoverContent","data-sentry-source-file":"data-table-slider-filter.tsx",children:[(0,l.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,l.jsx)("p",{className:"leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:t}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)(A.J,{htmlFor:`${a}-from`,className:"sr-only","data-sentry-element":"Label","data-sentry-source-file":"data-table-slider-filter.tsx",children:"From"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(k.p,{id:`${a}-from`,type:"number","aria-valuemin":m,"aria-valuemax":f,inputMode:"numeric",pattern:"[0-9]*",placeholder:m.toString(),min:m,max:f,value:p[0]?.toString(),onChange:g,className:(0,v.cn)("h-8 w-24",u&&"pr-8"),"data-sentry-element":"Input","data-sentry-source-file":"data-table-slider-filter.tsx"}),u&&(0,l.jsx)("span",{className:"bg-accent text-muted-foreground absolute top-0 right-0 bottom-0 flex items-center rounded-r-md px-2 text-sm",children:u})]}),(0,l.jsx)(A.J,{htmlFor:`${a}-to`,className:"sr-only","data-sentry-element":"Label","data-sentry-source-file":"data-table-slider-filter.tsx",children:"to"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(k.p,{id:`${a}-to`,type:"number","aria-valuemin":m,"aria-valuemax":f,inputMode:"numeric",pattern:"[0-9]*",placeholder:f.toString(),min:m,max:f,value:p[1]?.toString(),onChange:h,className:(0,v.cn)("h-8 w-24",u&&"pr-8"),"data-sentry-element":"Input","data-sentry-source-file":"data-table-slider-filter.tsx"}),u&&(0,l.jsx)("span",{className:"bg-accent text-muted-foreground absolute top-0 right-0 bottom-0 flex items-center rounded-r-md px-2 text-sm",children:u})]})]}),(0,l.jsxs)(A.J,{htmlFor:`${a}-slider`,className:"sr-only","data-sentry-element":"Label","data-sentry-source-file":"data-table-slider-filter.tsx",children:[t," slider"]}),(0,l.jsx)(M,{id:`${a}-slider`,min:m,max:f,step:x,value:p,onValueChange:j,"data-sentry-element":"Slider","data-sentry-source-file":"data-table-slider-filter.tsx"})]}),(0,l.jsx)(i.$,{"aria-label":`Clear ${t} filter`,variant:"outline",size:"sm",onClick:w,"data-sentry-element":"Button","data-sentry-source-file":"data-table-slider-filter.tsx",children:"Clear"})]})]})}var _=a(10510);function D({table:e}){let t=n.useMemo(()=>e.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()),[e]);return(0,l.jsxs)(d.AM,{"data-sentry-element":"Popover","data-sentry-component":"DataTableViewOptions","data-sentry-source-file":"data-table-view-options.tsx",children:[(0,l.jsx)(d.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"data-table-view-options.tsx",children:(0,l.jsxs)(i.$,{"aria-label":"Toggle columns",role:"combobox",variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex","data-sentry-element":"Button","data-sentry-source-file":"data-table-view-options.tsx",children:[(0,l.jsx)(_.A,{"data-sentry-element":"Settings2","data-sentry-source-file":"data-table-view-options.tsx"}),"View",(0,l.jsx)(T.TBE,{className:"ml-auto opacity-50","data-sentry-element":"CaretSortIcon","data-sentry-source-file":"data-table-view-options.tsx"})]})}),(0,l.jsx)(d.hl,{align:"end",className:"w-44 p-0","data-sentry-element":"PopoverContent","data-sentry-source-file":"data-table-view-options.tsx",children:(0,l.jsxs)(j,{"data-sentry-element":"Command","data-sentry-source-file":"data-table-view-options.tsx",children:[(0,l.jsx)(w,{placeholder:"Search columns...","data-sentry-element":"CommandInput","data-sentry-source-file":"data-table-view-options.tsx"}),(0,l.jsxs)(N,{"data-sentry-element":"CommandList","data-sentry-source-file":"data-table-view-options.tsx",children:[(0,l.jsx)(C,{"data-sentry-element":"CommandEmpty","data-sentry-source-file":"data-table-view-options.tsx",children:"No columns found."}),(0,l.jsx)(S,{"data-sentry-element":"CommandGroup","data-sentry-source-file":"data-table-view-options.tsx",children:t.map(e=>(0,l.jsxs)(z,{onSelect:()=>e.toggleVisibility(!e.getIsVisible()),children:[(0,l.jsx)("span",{className:"truncate",children:e.columnDef.meta?.label??e.id}),(0,l.jsx)(T.Srz,{className:(0,v.cn)("ml-auto size-4 shrink-0",e.getIsVisible()?"opacity-100":"opacity-0")})]},e.id))})]})]})})]})}function $({table:e,children:t,className:a,...r}){let s=e.getState().columnFilters.length>0,o=n.useMemo(()=>e.getAllColumns().filter(e=>e.getCanFilter()),[e]),d=n.useCallback(()=>{e.resetColumnFilters()},[e]);return(0,l.jsxs)("div",{role:"toolbar","aria-orientation":"horizontal",className:(0,v.cn)("flex w-full items-start justify-between gap-2 p-1",a),...r,"data-sentry-component":"DataTableToolbar","data-sentry-source-file":"data-table-toolbar.tsx",children:[(0,l.jsxs)("div",{className:"flex flex-1 flex-wrap items-center gap-2",children:[o.map(e=>(0,l.jsx)(B,{column:e},e.id)),s&&(0,l.jsxs)(i.$,{"aria-label":"Reset filters",variant:"outline",size:"sm",className:"border-dashed",onClick:d,children:[(0,l.jsx)(T.MKb,{}),"Reset"]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[t,(0,l.jsx)(D,{table:e,"data-sentry-element":"DataTableViewOptions","data-sentry-source-file":"data-table-toolbar.tsx"})]})]})}function B({column:e}){{let t=e.columnDef.meta;return n.useCallback(()=>{if(!t?.variant)return null;switch(t.variant){case"text":return(0,l.jsx)(k.p,{placeholder:t.placeholder??t.label,value:e.getFilterValue()??"",onChange:t=>e.setFilterValue(t.target.value),className:"h-8 w-40 lg:w-56"});case"number":return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(k.p,{type:"number",inputMode:"numeric",placeholder:t.placeholder??t.label,value:e.getFilterValue()??"",onChange:t=>e.setFilterValue(t.target.value),className:(0,v.cn)("h-8 w-[120px]",t.unit&&"pr-8")}),t.unit&&(0,l.jsx)("span",{className:"bg-accent text-muted-foreground absolute top-0 right-0 bottom-0 flex items-center rounded-r-md px-2 text-sm",children:t.unit})]});case"range":return(0,l.jsx)(R,{column:e,title:t.label??e.id});case"date":case"dateRange":return(0,l.jsx)(p,{column:e,title:t.label??e.id,multiple:"dateRange"===t.variant});case"select":case"multiSelect":return(0,l.jsx)(F,{column:e,title:t.label??e.id,options:t.options??[],multiple:"multiSelect"===t.variant});default:return null}},[e,t])()}}},74916:(e,t,a)=>{a.d(t,{b:()=>p});var l=a(24443),n=a(65052),r=a(49512),s=a(21999),i=a(33284),o=a(23032),d=a(72595),c=a(39255);function u({table:e,pageSizeOptions:t=[10,20,30,40,50],className:a,...n}){return(0,l.jsxs)("div",{className:(0,d.cn)("flex w-full flex-col-reverse items-center justify-between gap-4 overflow-auto p-1 sm:flex-row sm:gap-8",a),...n,"data-sentry-component":"DataTablePagination","data-sentry-source-file":"data-table-pagination.tsx",children:[(0,l.jsx)("div",{className:"text-muted-foreground flex-1 text-sm whitespace-nowrap",children:e.getFilteredSelectedRowModel().rows.length>0?(0,l.jsxs)(l.Fragment,{children:[e.getFilteredSelectedRowModel().rows.length," of"," ",e.getFilteredRowModel().rows.length," row(s) selected."]}):(0,l.jsxs)(l.Fragment,{children:[e.getFilteredRowModel().rows.length," row(s) total."]})}),(0,l.jsxs)("div",{className:"flex flex-col-reverse items-center gap-4 sm:flex-row sm:gap-6 lg:gap-8",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("p",{className:"text-sm font-medium whitespace-nowrap",children:"Rows per page"}),(0,l.jsxs)(o.l6,{value:`${e.getState().pagination.pageSize}`,onValueChange:t=>{e.setPageSize(Number(t))},"data-sentry-element":"Select","data-sentry-source-file":"data-table-pagination.tsx",children:[(0,l.jsx)(o.bq,{className:"h-8 w-[4.5rem] [&[data-size]]:h-8","data-sentry-element":"SelectTrigger","data-sentry-source-file":"data-table-pagination.tsx",children:(0,l.jsx)(o.yv,{placeholder:e.getState().pagination.pageSize,"data-sentry-element":"SelectValue","data-sentry-source-file":"data-table-pagination.tsx"})}),(0,l.jsx)(o.gC,{side:"top","data-sentry-element":"SelectContent","data-sentry-source-file":"data-table-pagination.tsx",children:t.map(e=>(0,l.jsx)(o.eb,{value:`${e}`,children:e},e))})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-center text-sm font-medium",children:["Page ",e.getState().pagination.pageIndex+1," of"," ",e.getPageCount()]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(i.$,{"aria-label":"Go to first page",variant:"outline",size:"icon",className:"hidden size-8 lg:flex",onClick:()=>e.setPageIndex(0),disabled:!e.getCanPreviousPage(),"data-sentry-element":"Button","data-sentry-source-file":"data-table-pagination.tsx",children:(0,l.jsx)(r.A,{"data-sentry-element":"ChevronsLeft","data-sentry-source-file":"data-table-pagination.tsx"})}),(0,l.jsx)(i.$,{"aria-label":"Go to previous page",variant:"outline",size:"icon",className:"size-8",onClick:()=>e.previousPage(),disabled:!e.getCanPreviousPage(),"data-sentry-element":"Button","data-sentry-source-file":"data-table-pagination.tsx",children:(0,l.jsx)(c.YJP,{"data-sentry-element":"ChevronLeftIcon","data-sentry-source-file":"data-table-pagination.tsx"})}),(0,l.jsx)(i.$,{"aria-label":"Go to next page",variant:"outline",size:"icon",className:"size-8",onClick:()=>e.nextPage(),disabled:!e.getCanNextPage(),"data-sentry-element":"Button","data-sentry-source-file":"data-table-pagination.tsx",children:(0,l.jsx)(c.vKP,{"data-sentry-element":"ChevronRightIcon","data-sentry-source-file":"data-table-pagination.tsx"})}),(0,l.jsx)(i.$,{"aria-label":"Go to last page",variant:"outline",size:"icon",className:"hidden size-8 lg:flex",onClick:()=>e.setPageIndex(e.getPageCount()-1),disabled:!e.getCanNextPage(),"data-sentry-element":"Button","data-sentry-source-file":"data-table-pagination.tsx",children:(0,l.jsx)(s.A,{"data-sentry-element":"ChevronsRight","data-sentry-source-file":"data-table-pagination.tsx"})})]})]})]})}var m=a(61074);function f({column:e,withBorder:t=!1}){let a=e.getIsPinned(),l="left"===a&&e.getIsLastColumn("left"),n="right"===a&&e.getIsFirstColumn("right");return{boxShadow:t?l?"-4px 0 4px -4px hsl(var(--border)) inset":n?"4px 0 4px -4px hsl(var(--border)) inset":void 0:void 0,left:"left"===a?`${e.getStart("left")}px`:void 0,right:"right"===a?`${e.getAfter("right")}px`:void 0,opacity:a?.97:1,position:a?"sticky":"relative",background:"hsl(var(--background))",width:e.getSize(),zIndex:+!!a}}a(27124);var x=a(67529);function p({table:e,actionBar:t,children:a}){return(0,l.jsxs)("div",{className:"flex flex-1 flex-col space-y-4","data-sentry-component":"DataTable","data-sentry-source-file":"data-table.tsx",children:[a,(0,l.jsx)("div",{className:"relative flex flex-1",children:(0,l.jsx)("div",{className:"absolute inset-0 flex overflow-hidden rounded-lg border",children:(0,l.jsxs)(x.ScrollArea,{className:"h-full w-full","data-sentry-element":"ScrollArea","data-sentry-source-file":"data-table.tsx",children:[(0,l.jsxs)(m.Table,{"data-sentry-element":"Table","data-sentry-source-file":"data-table.tsx",children:[(0,l.jsx)(m.TableHeader,{className:"bg-muted sticky top-0 z-10","data-sentry-element":"TableHeader","data-sentry-source-file":"data-table.tsx",children:e.getHeaderGroups().map(e=>(0,l.jsx)(m.TableRow,{children:e.headers.map(e=>(0,l.jsx)(m.TableHead,{colSpan:e.colSpan,style:{...f({column:e.column})},children:e.isPlaceholder?null:(0,n.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,l.jsx)(m.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"data-table.tsx",children:e.getRowModel().rows?.length?e.getRowModel().rows.map(e=>(0,l.jsx)(m.TableRow,{"data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,l.jsx)(m.TableCell,{style:{...f({column:e.column})},children:(0,n.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,l.jsx)(m.TableRow,{children:(0,l.jsx)(m.TableCell,{colSpan:e.getAllColumns().length,className:"h-24 text-center",children:"No results."})})})]}),(0,l.jsx)(x.$,{orientation:"horizontal","data-sentry-element":"ScrollBar","data-sentry-source-file":"data-table.tsx"})]})})}),(0,l.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,l.jsx)(u,{table:e,"data-sentry-element":"DataTablePagination","data-sentry-source-file":"data-table.tsx"}),t&&e.getFilteredSelectedRowModel().rows.length>0&&t]})]})}},92585:(e,t,a)=>{a.d(t,{u:()=>f});var l=a(65052),n=a(60608),r=a(98663),s=a(60222),i=a(17880),o=a(17050),d=a(13875),c=a(27124);let u=d.z.object({id:d.z.string(),desc:d.z.boolean()}),m=e=>{let t=e?e instanceof Set?e:new Set(e):null;return(0,o.Cp)({parse:e=>{try{let a=JSON.parse(e),l=d.z.array(u).safeParse(a);if(!l.success||t&&l.data.some(e=>!t.has(e.id)))return null;return l.data}catch{return null}},serialize:e=>JSON.stringify(e),eq:(e,t)=>e.length===t.length&&e.every((e,a)=>e.id===t[a]?.id&&e.desc===t[a]?.desc)})};function f(e){let{columns:t,pageCount:a=-1,initialState:o,history:d="replace",debounceMs:c=300,throttleMs:u=50,clearOnDefault:f=!1,enableAdvancedFilter:x=!1,scroll:p=!1,shallow:y=!0,startTransition:b,...g}=e,h=s.useMemo(()=>({history:d,scroll:p,shallow:y,throttleMs:u,debounceMs:c,clearOnDefault:f,startTransition:b}),[d,p,y,u,c,f,b]),[v,j]=s.useState(o?.rowSelection??{}),[w,N]=s.useState(o?.columnVisibility??{}),[C,S]=(0,r.ZA)("page",r.GJ.withOptions(h).withDefault(1)),[I,z]=(0,r.ZA)("perPage",r.GJ.withOptions(h).withDefault(o?.pagination?.pageSize??10)),T=s.useMemo(()=>({pageIndex:C-1,pageSize:I}),[C,I]),F=s.useCallback(e=>{if("function"==typeof e){let t=e(T);S(t.pageIndex+1),z(t.pageSize)}else S(e.pageIndex+1),z(e.pageSize)},[T,S,z]),k=s.useMemo(()=>new Set(t.map(e=>e.id).filter(Boolean)),[t]),[A,P]=(0,r.ZA)("sort",m(k).withOptions(h).withDefault(o?.sorting??[])),M=s.useCallback(e=>{"function"==typeof e?P(e(A)):P(e)},[A,P]),V=s.useMemo(()=>x?[]:t.filter(e=>e.enableColumnFilter),[t,x]),R=s.useMemo(()=>x?{}:V.reduce((e,t)=>(t.meta?.options?e[t.id??""]=(0,r.IN)(r.tU,",").withOptions(h):e[t.id??""]=r.tU.withOptions(h),e),{}),[V,h,x]),[_,D]=(0,r.ab)(R),$=function(e,t){let a=(0,i.c)(e),l=s.useRef(0);return s.useEffect(()=>()=>window.clearTimeout(l.current),[]),s.useCallback((...e)=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>a(...e),t)},[a,t])}(e=>{S(1),D(e)},c),B=s.useMemo(()=>x?[]:Object.entries(_).reduce((e,[t,a])=>{if(null!==a){let l=Array.isArray(a)?a:"string"==typeof a&&/[^a-zA-Z0-9]/.test(a)?a.split(/[^a-zA-Z0-9]+/).filter(Boolean):[a];e.push({id:t,value:l})}return e},[]),[_,x]),[E,L]=s.useState(B),O=s.useCallback(e=>{x||L(t=>{let a="function"==typeof e?e(t):e,l=a.reduce((e,t)=>(V.find(e=>e.id===t.id)&&(e[t.id]=t.value),e),{});for(let e of t)a.some(t=>t.id===e.id)||(l[e.id]=null);return $(l),a})},[$,V,x]);return{table:(0,l.N4)({...g,columns:t,initialState:o,pageCount:a,state:{pagination:T,sorting:A,columnVisibility:w,rowSelection:v,columnFilters:E},defaultColumn:{...g.defaultColumn,enableColumnFilter:!1},enableRowSelection:!0,onRowSelectionChange:j,onPaginationChange:F,onSortingChange:M,onColumnFiltersChange:O,onColumnVisibilityChange:N,getCoreRowModel:(0,n.HT)(),getFilteredRowModel:(0,n.hM)(),getPaginationRowModel:(0,n.kW)(),getSortedRowModel:(0,n.h5)(),getFacetedRowModel:(0,n.kQ)(),getFacetedUniqueValues:(0,n.oS)(),getFacetedMinMaxValues:(0,n.tX)(),manualPagination:!0,manualSorting:!0,manualFiltering:!0}),shallow:y,debounceMs:c,throttleMs:u}}d.z.object({id:d.z.string(),value:d.z.union([d.z.string(),d.z.array(d.z.string())]),variant:d.z.enum(c.r.filterVariants),operator:d.z.enum(c.r.operators),filterId:d.z.string()})}};
//# sourceMappingURL=7331.js.map