try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d48e8b0b-e309-4ff3-9b83-10a6d898b1d7",e._sentryDebugIdIdentifier="sentry-dbid-d48e8b0b-e309-4ff3-9b83-10a6d898b1d7")}catch(e){}exports.id=8774,exports.ids=[8774],exports.modules={3259:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(57434);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},5461:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});let n=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sonner.tsx","Toaster")},8036:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>f,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var o=r(63033),s=r(91611),a=r(19761),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx","default");let d={...o},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;n="function"==typeof i?new Proxy(i,{apply:(e,t,r)=>{let n,o,s;try{let e=l?.getStore();n=e?.headers.get("sentry-trace")??void 0,o=e?.headers.get("baggage")??void 0,s=e?.headers}catch(e){}return a.wrapServerComponentWithSentry(e,{componentRoute:"/",componentType:"Not-found",sentryTraceHeader:n,baggageHeader:o,headers:s}).apply(t,r)}}):i;let c=void 0,u=void 0,m=void 0,f=n},9010:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=9010,e.exports=t},10183:()=>{},17848:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(24443),o=r(21923),s=r(29830),a=r(55294);r(60222);var i=r(99971);function d({activeThemeValue:e,children:t}){let{resolvedTheme:r}=(0,a.D)();return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(i.l,{initialTheme:e,"data-sentry-element":"ActiveThemeProvider","data-sentry-source-file":"providers.tsx",children:(0,n.jsx)(o.lJ,{appearance:{baseTheme:"dark"===r?s.dark:void 0},"data-sentry-element":"ClerkProvider","data-sentry-source-file":"providers.tsx",children:t})})})}},18144:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,43141,23)),Promise.resolve().then(r.bind(r,714)),Promise.resolve().then(r.bind(r,17848)),Promise.resolve().then(r.bind(r,29705)),Promise.resolve().then(r.bind(r,19263))},19263:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>a});var n=r(24443),o=r(55294),s=r(85001);let a=({...e})=>{let{theme:t="system"}=(0,o.D)();return(0,n.jsx)(s.Toaster,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e,"data-sentry-element":"Sonner","data-sentry-component":"Toaster","data-sentry-source-file":"sonner.tsx"})}},19557:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(47509),o=r(86265);function s(...e){return(0,o.QP)((0,n.$)(e))}},24848:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,72716,23)),Promise.resolve().then(r.t.bind(r,76506,23)),Promise.resolve().then(r.t.bind(r,25850,23)),Promise.resolve().then(r.t.bind(r,95125,23)),Promise.resolve().then(r.t.bind(r,87633,23)),Promise.resolve().then(r.t.bind(r,65449,23)),Promise.resolve().then(r.t.bind(r,25889,23)),Promise.resolve().then(r.t.bind(r,82683,23))},26806:(e,t,r)=>{Promise.resolve().then(r.bind(r,62458))},29705:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var n=r(24443),o=r(55294);function s({children:e,...t}){return(0,n.jsx)(o.N,{...t,"data-sentry-element":"NextThemesProvider","data-sentry-component":"ThemeProvider","data-sentry-source-file":"theme-provider.tsx",children:e})}},32922:(e,t,r)=>{Promise.resolve().then(r.bind(r,8036))},33224:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(24443);r(89011);var o=r(11941),s=r.n(o);function a({error:e}){return(0,n.jsx)("html",{"data-sentry-component":"GlobalError","data-sentry-source-file":"global-error.tsx",children:(0,n.jsx)("body",{children:(0,n.jsx)(s(),{statusCode:0,"data-sentry-element":"NextError","data-sentry-source-file":"global-error.tsx"})})})}r(60222)},33284:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>i});var n=r(24443);r(60222);var o=r(16586),s=r(29693),a=r(72595);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:s=!1,...d}){let l=s?o.DX:"button";return(0,n.jsx)(l,{"data-slot":"button",className:(0,a.cn)(i({variant:t,size:r,className:e})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},45962:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=45962,e.exports=t},51303:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\ThemeToggle\\\\theme-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\ThemeToggle\\theme-provider.tsx","default")},55028:(e,t,r)=>{"use strict";r.d(t,{y:()=>o});var n=r(56495);let o=(0,n.createServerReference)("7f22efd92a3b59d43d3d12fe480e87910640e1db9e",n.callServer,void 0,n.findSourceMapURL,"invalidateCacheAction")},56526:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(24443),o=r(34769),s=r(33284);function a(){let e=(0,o.useRouter)();return(0,n.jsxs)("div",{className:"absolute top-1/2 left-1/2 mb-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center text-center","data-sentry-component":"NotFound","data-sentry-source-file":"not-found.tsx",children:[(0,n.jsx)("span",{className:"from-foreground bg-linear-to-b to-transparent bg-clip-text text-[10rem] leading-none font-extrabold text-transparent",children:"404"}),(0,n.jsx)("h2",{className:"font-heading my-2 text-2xl font-bold",children:"Something's missing"}),(0,n.jsx)("p",{children:"Sorry, the page you are looking for doesn't exist or has been moved."}),(0,n.jsxs)("div",{className:"mt-8 flex justify-center gap-2",children:[(0,n.jsx)(s.$,{onClick:()=>e.back(),variant:"default",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"not-found.tsx",children:"Go back"}),(0,n.jsx)(s.$,{onClick:()=>e.push("/dashboard"),variant:"ghost",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"not-found.tsx",children:"Back to Home"})]})]})}},59696:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85038,23)),Promise.resolve().then(r.t.bind(r,93456,23)),Promise.resolve().then(r.t.bind(r,5348,23)),Promise.resolve().then(r.t.bind(r,8175,23)),Promise.resolve().then(r.t.bind(r,92023,23)),Promise.resolve().then(r.t.bind(r,30543,23)),Promise.resolve().then(r.t.bind(r,27871,23)),Promise.resolve().then(r.t.bind(r,48113,23))},59718:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\providers.tsx","default")},62458:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx","default")},66718:(e,t,r)=>{Promise.resolve().then(r.bind(r,33224))},67202:(e,t,r)=>{Promise.resolve().then(r.bind(r,56526))},69549:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>E,generateImageMetadata:()=>_,generateMetadata:()=>$,generateViewport:()=>I,metadata:()=>M,viewport:()=>N});var o=r(63033),s=r(78869),a=r(59718),i=r(5461),d=r(80090),l=r.n(d),c=r(55652),u=r.n(c),m=r(78367),f=r.n(m),h=r(50731),v=r.n(h),p=r(10748),y=r.n(p),b=r(81096),g=r.n(b),x=r(19557);let j=(0,x.cn)(l().variable,u().variable,f().variable,v().variable,y().variable,g().variable);var C=r(51303),k=r(87927),w=r(19855),P=r.n(w),T=r(51403);r(96844),r(10183);var S=r(19761);let D={light:"#ffffff",dark:"#09090b"},M={title:"诊所管理系统",description:"基于 Next.js 和 Shadcn 的医疗诊所管理系统"},N={themeColor:D.light};async function A({children:e}){let t=await (0,k.UL)(),r=t.get("active_theme")?.value,n=r?.endsWith("-scaled");return(0,s.jsxs)("html",{lang:"zh-CN",suppressHydrationWarning:!0,"data-sentry-component":"RootLayout","data-sentry-source-file":"layout.tsx",children:[(0,s.jsx)("head",{children:(0,s.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              try {
                if (localStorage.theme === 'dark' || ((!('theme' in localStorage) || localStorage.theme === 'system') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                  document.querySelector('meta[name="theme-color"]').setAttribute('content', '${D.dark}')
                }
              } catch (_) {}
            `}})}),(0,s.jsxs)("body",{className:(0,x.cn)("bg-background overflow-hidden overscroll-none font-sans antialiased",r?`theme-${r}`:"",n?"theme-scaled":"",j),children:[(0,s.jsx)(P(),{showSpinner:!1,"data-sentry-element":"NextTopLoader","data-sentry-source-file":"layout.tsx"}),(0,s.jsx)(T.NuqsAdapter,{"data-sentry-element":"NuqsAdapter","data-sentry-source-file":"layout.tsx",children:(0,s.jsx)(C.default,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,enableColorScheme:!0,"data-sentry-element":"ThemeProvider","data-sentry-source-file":"layout.tsx",children:(0,s.jsxs)(a.default,{activeThemeValue:r,"data-sentry-element":"Providers","data-sentry-source-file":"layout.tsx",children:[(0,s.jsx)(i.Toaster,{"data-sentry-element":"Toaster","data-sentry-source-file":"layout.tsx"}),e]})})})]})]})}let U={...o},B="workUnitAsyncStorage"in U?U.workUnitAsyncStorage:"requestAsyncStorage"in U?U.requestAsyncStorage:void 0;n=new Proxy(A,{apply:(e,t,r)=>{let n,o,s;try{let e=B?.getStore();n=e?.headers.get("sentry-trace")??void 0,o=e?.headers.get("baggage")??void 0,s=e?.headers}catch(e){}return S.wrapServerComponentWithSentry(e,{componentRoute:"/",componentType:"Layout",sentryTraceHeader:n,baggageHeader:o,headers:s}).apply(t,r)}});let $=void 0,_=void 0,I=void 0,E=n},72595:(e,t,r)=>{"use strict";r.d(t,{Jv:()=>l,cn:()=>s,fw:()=>d,r6:()=>i,z3:()=>a});var n=r(70991),o=r(18111);function s(...e){return(0,o.QP)((0,n.$)(e))}function a(e,t={}){let{decimals:r=0,sizeType:n="normal"}=t;if(0===e)return"0 Byte";let o=Math.floor(Math.log(e)/Math.log(1024));return`${(e/Math.pow(1024,o)).toFixed(r)} ${"accurate"===n?["Bytes","KiB","MiB","GiB","TiB"][o]??"Bytest":["Bytes","KB","MB","GB","TB"][o]??"Bytes"}`}function i(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function d(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let r=Math.floor(t/60);if(r<60)return`${r}分钟前`;let n=Math.floor(r/60);if(n<24)return`${n}小时前`;let o=Math.floor(n/24);if(o<7)return`${o}天前`;let s=Math.floor(o/7);if(s<4)return`${s}周前`;let a=Math.floor(o/30);if(a<12)return`${a}个月前`;let i=Math.floor(o/365);return`${i}年前`}function l(e){return("string"==typeof e?new Date(e):e)<new Date}},88768:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,19855,23)),Promise.resolve().then(r.bind(r,51403)),Promise.resolve().then(r.bind(r,59718)),Promise.resolve().then(r.bind(r,51303)),Promise.resolve().then(r.bind(r,5461))},96844:()=>{},99971:(e,t,r)=>{"use strict";r.d(t,{l:()=>a,p:()=>i});var n=r(24443),o=r(60222);let s=(0,o.createContext)(void 0);function a({children:e,initialTheme:t}){let[r,a]=(0,o.useState)(()=>t||"default");return(0,n.jsx)(s.Provider,{value:{activeTheme:r,setActiveTheme:a},"data-sentry-element":"ThemeContext.Provider","data-sentry-component":"ActiveThemeProvider","data-sentry-source-file":"active-theme.tsx",children:e})}function i(){let e=(0,o.useContext)(s);if(void 0===e)throw Error("useThemeConfig must be used within an ActiveThemeProvider");return e}}};
//# sourceMappingURL=8774.js.map