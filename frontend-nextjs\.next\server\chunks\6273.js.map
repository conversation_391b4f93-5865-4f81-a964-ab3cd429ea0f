{"version": 3, "file": "6273.js", "mappings": "skBAMA,SAASA,EAAO,CACd,GAAGC,EAC+C,EAClD,MAAO,UAACC,EAAAA,EAAoB,EAACC,YAAU,SAAU,GAAGF,CAAK,CAAEG,sBAAoB,uBAAuBC,wBAAsB,SAASC,0BAAwB,cAC/J,CACA,SAASC,EAAc,CACrB,GAAGN,EACkD,EACrD,MAAO,UAACC,EAAAA,EAAuB,EAACC,YAAU,iBAAkB,GAAGF,CAAK,CAAEG,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACpL,CACA,SAASE,EAAa,CACpB,GAAGP,EACiD,EACpD,MAAO,UAACC,EAAAA,EAAsB,EAACC,YAAU,gBAAiB,GAAGF,CAAK,CAAEG,sBAAoB,yBAAyBC,wBAAsB,eAAeC,0BAAwB,cAChL,CAMA,SAASG,EAAc,WACrBC,CAAS,CACT,GAAGT,EACkD,EACrD,MAAO,UAACC,EAAAA,EAAuB,EAACC,YAAU,iBAAiBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0JD,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACxW,CACA,SAASM,EAAc,WACrBF,CAAS,UACTG,CAAQ,CACR,GAAGZ,EACkD,EACrD,MAAO,WAACO,EAAAA,CAAaL,YAAU,gBAAgBC,sBAAoB,eAAeC,wBAAsB,gBAAgBC,0BAAwB,uBAC5I,UAACG,EAAAA,CAAcL,sBAAoB,gBAAgBE,0BAAwB,eAC3E,WAACJ,EAAAA,EAAuB,EAACC,YAAU,iBAAiBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8WAA+WD,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,0BAA0BE,0BAAwB,uBAC3gBO,EACD,WAACX,EAAAA,EAAqB,EAACQ,UAAU,oWAAoWN,sBAAoB,wBAAwBE,0BAAwB,uBACvc,UAACQ,EAAAA,CAAKA,CAAAA,CAACV,sBAAoB,QAAQE,0BAAwB,eAC3D,UAACS,OAAAA,CAAKL,UAAU,mBAAU,kBAIpC,CACA,SAASM,EAAa,WACpBN,CAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAACgB,MAAAA,CAAId,YAAU,gBAAgBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+CAAgDD,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,eAAeC,0BAAwB,cAC1L,CACA,SAASY,EAAa,WACpBR,CAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAACgB,MAAAA,CAAId,YAAU,gBAAgBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yDAA0DD,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,eAAeC,0BAAwB,cACpM,CACA,SAASa,EAAY,WACnBT,CAAS,CACT,GAAGT,EACgD,EACnD,MAAO,UAACC,EAAAA,EAAqB,EAACC,YAAU,eAAeO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCD,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cAC5O,CACA,SAASc,EAAkB,WACzBV,CAAS,CACT,GAAGT,EACsD,EACzD,MAAO,UAACC,EAAAA,EAA2B,EAACC,YAAU,qBAAqBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCD,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,cAC/P,uGClEA,SAASe,EAAW,WAClBX,CAAS,UACTG,CAAQ,CACR,GAAGZ,EACmD,EACtD,MAAO,WAACqB,EAAAA,EAAwB,EAACnB,YAAU,cAAcO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,WAAYD,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,2BAA2BC,wBAAsB,aAAaC,0BAAwB,4BAChN,UAACgB,EAAAA,EAA4B,EAACnB,YAAU,uBAAuBO,UAAU,qJAAqJN,sBAAoB,+BAA+BE,0BAAwB,2BACtSO,IAEH,UAACU,EAAAA,CAAUnB,sBAAoB,YAAYE,0BAAwB,oBACnE,UAACgB,EAAAA,EAA0B,EAAClB,sBAAoB,6BAA6BE,0BAAwB,sBAE3G,CACA,SAASiB,EAAU,WACjBb,CAAS,aACTc,EAAc,UAAU,CACxB,GAAGvB,EACkE,EACrE,MAAO,UAACqB,EAAAA,EAAuC,EAACnB,YAAU,wBAAwBqB,YAAaA,EAAad,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qDAAsE,aAAhBa,GAA8B,6CAA8D,eAAhBA,GAAgC,+CAAgDd,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,0CAA0CC,wBAAsB,YAAYC,0BAAwB,2BACvd,UAACgB,EAAAA,EAAmC,EAACnB,YAAU,oBAAoBO,UAAU,yCAAyCN,sBAAoB,sCAAsCE,0BAAwB,qBAE9M,scEZM,EAAY,cAGZ,CAAC,EAA0B,EAAsB,CAAI,OAAkB,CAAC,EAAW,CACvF,IAAiB,CAClB,EACK,CAHiD,CAGhC,QAAiB,CAAC,EAKnC,EAA0C,IAC9C,GAAM,oBAAE,EAAoB,GAAG,EAAiB,CAAI,EAC9C,EAAc,EAAe,GACnC,KAFgD,CAEzC,SAD8C,CAC7B,KAAhB,CAAsB,GAAG,EAAc,GAAG,EAAkB,OAAO,EAAM,CACnF,EAEA,EAAY,YAAc,EAmB1B,EARiC,WAC/B,CAAC,EAA6C,KAC5C,GAAM,oBAAE,EAAoB,GAAG,EAAa,CAAI,EAC1C,EAAc,EAAe,GACnC,CAF4C,KAErC,SAD8C,CAC7B,KAAhB,CAAyB,GAAG,EAAc,GAAG,EAAc,IAAK,EAAc,CACxF,GAGiB,YAdE,EAcY,mBAWjC,IAAM,EAAsD,IAG1D,GAAM,oBAAE,EAAoB,GAAG,EAAY,CAAI,EACzC,EAAc,EAAe,GADQ,MAEpC,SAD8C,CAC7B,KAAhB,CAAwB,GAAG,EAAc,GAAG,EAAa,CACnE,EAEA,EAAkB,YAbE,EAaY,kBAYhC,IAAM,EAA2B,aAC/B,CAAC,EAA6C,KAC5C,GAAM,oBAAE,EAAoB,GAAG,EAAa,CAAI,EAC1C,EAAc,EAAe,GACnC,CAF4C,KAErC,SAD8C,CAC7B,KAAhB,CAAyB,GAAG,EAAc,GAAG,EAAc,IAAK,EAAc,CACxF,GAGF,EAAmB,YAdE,EAcY,mBAMjC,IAAM,EAAe,qBAMf,CAAC,EAA4B,EAA4B,CAC7D,EAAyD,GAOrD,EAA2B,OAPsC,KAOtC,CAC/B,CAAC,EAA6C,EATe,GAU3D,GAAM,oBAAE,WAAoB,EAAU,GAAG,EAAa,CAAI,EACpD,EAAc,EAAe,GAC7B,CAFgD,CAE7B,SAAkC,IADN,EAE/C,EAAe,OAAe,CAAC,EAAc,GAC7C,EAAkB,KADqC,GACrC,CAAwC,IAAI,EAEpE,MACE,UAAiB,KAAhB,CACC,YAAa,EACb,UAAW,EACX,SAAS,eAET,mBAAC,GAA2B,MAAO,YAAoB,EACrD,oBAAiB,KAAhB,CACC,KAAK,cACJ,GAAG,EACH,GAAG,EACJ,IAAK,EACL,gBAAiB,OAAoB,CAAC,EAAa,gBAAiB,IAClE,EAAM,eAAe,EACrB,EAAU,SAAS,MAAM,CAAE,cAAe,EAAK,CAAC,CAClD,CAAC,EACD,qBAAsB,GAAW,EAAM,eAAe,EACtD,kBAAmB,GAAW,EAAM,eAAe,EAQnD,oBAAC,IAAS,CAAT,UAAW,EAAS,EAEnB,UAAC,cAAmB,EAAwB,IAEhD,CACF,GAGN,GAGF,EAAmB,YAAc,EAMjC,IAAM,EAAa,mBAMb,EAAyB,aAC7B,CAAC,EAA2C,KAC1C,GAAM,CAAE,qBAAoB,GAAG,EAAW,CAAI,EACxC,EAAc,EAAe,EADO,CAE1C,MAAO,SAD8C,CAC7B,KAAhB,CAAuB,GAAG,EAAc,GAAG,EAAY,IAAK,EAAc,CACpF,GAGF,EAAiB,YAAc,EAM/B,IAAM,EAAmB,yBAMnB,EAA+B,aAGnC,CAAC,EAAiD,KAClD,GAAM,oBAAE,EAAoB,GAAG,EAAiB,CAAI,EAC9C,EAAc,EAAe,GACnC,KAFgD,CAEzC,SAD8C,CAC7B,KAAhB,CAA6B,GAAG,EAAc,GAAG,EAAkB,IAAK,EAAc,CAChG,CAAC,EAED,EAAuB,YAAc,EAYrC,IAAM,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,oBAAE,EAAoB,GAAG,EAAY,CAAI,EACzC,EAAc,EAAe,GADQ,MAEpC,SAD8C,CAC7B,KAAhB,CAAuB,GAAG,EAAc,GAAG,EAAa,IAAK,EAAc,CACrF,GAGF,EAAkB,YAdE,EAcY,kBAMhC,IAAM,EAAc,oBAKd,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,oBAAE,EAAoB,GAAG,EAAY,CAAI,EACzC,OADqC,IACnC,EAAU,CAAI,EAA6B,EAAa,GAC1D,EAAc,EAAe,GAC7B,EAAM,MAFsE,CAEvD,CAAC,EAAc,GAC1C,MADmD,CAC5C,SAAiB,KAAhB,CAAuB,GAAG,EAAc,GAAG,MAAa,EAAU,CAC5E,GAGF,EAAkB,YAAc,EAQhC,IAAM,EAAwD,CAAC,YAAE,EAAW,IAC1E,IAAM,EAAU,KAAK,EAAY;;mCAAA,EAEE,EAAY,oBAAqB,EAAgB;;0JAAA,EAEsE,EAAY;;sFAAA,EAWtK,OAPM,YAAU,KACS,CAGlB,QAH2B,OAGX,OAHW,CAC9B,EAAW,SAAS,aAAa,kBAAkB,IAEhC,QAAQ,KAAK,EACpC,EAAG,CAAC,EADuC,EACnB,EAEjB,IACT,EAHyB,sBC3PzB,SAASmB,EAAY,CACnB,GAAGxB,EACoD,EACvD,MAAO,UAACyB,EAAyB,CAACvB,EAAD,UAAW,eAAgB,GAAGF,CAAK,CAAEG,sBAAoB,4BAA4BC,wBAAsB,cAAcC,0BAAwB,oBACpL,CAMA,SAASqB,EAAkB,CACzB,GAAG1B,EACsD,EACzD,MAAO,UAACyB,EAA2B,CAACvB,CAHZwB,GAGW,QAAW,sBAAuB,GAAG1B,CAAK,CAAEG,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,oBACrM,CACA,SAASsB,EAAmB,WAC1BlB,CAAS,CACT,GAAGT,EACuD,EAC1D,MAAO,UAACyB,EAA4B,CAACvB,KAAD,OAAW,uBAAuBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0JD,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,+BAA+BC,wBAAsB,qBAAqBC,0BAAwB,oBAC7X,CACA,SAASuB,EAAmB,WAC1BnB,CAAS,CACT,GAAGT,EACuD,EAC1D,MAAO,WAAC0B,EAAiBA,CAACvB,sBAAoB,KAArBuB,eAAyCtB,wBAAsB,qBAAqBC,0BAAwB,6BACjI,UAACsB,EAAkBA,CAACxB,sBAAoB,MAArBwB,eAA0CtB,0BAAwB,qBACrF,UDyOU,ECzOmB,CAACH,KAAD,OAAW,uBAAuBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8WAA+WD,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,+BAA+BE,0BAAwB,uBAEpiB,CACA,SAASwB,EAAkB,CACzBpB,WAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAACgB,MAAAA,CAAId,YAAU,sBAAsBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+CAAgDD,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,oBAAoBC,0BAAwB,oBACrM,CACA,SAASyB,EAAkB,CACzBrB,WAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAACgB,MAAAA,CAAId,YAAU,sBAAsBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yDAA0DD,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,oBAAoBC,0BAAwB,oBAC/M,CACA,SAAS0B,EAAiB,CACxBtB,WAAS,CACT,GAAGT,EACqD,EACxD,MAAO,CAJgB+B,EAIhB,OAACN,EAA0B,CAACvB,GAAD,SAAW,qBAAqBO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,wBAAyBD,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,6BAA6BC,wBAAsB,mBAAmBC,0BAAwB,oBACpP,CACA,SAAS2B,EAAuB,WAC9BvB,CAAS,CACT,GAAGT,EAC2D,EAC9D,MAAO,OAJsBgC,CAItB,EAACP,EAAgC,CAACvB,SAAD,GAAW,2BAA2BO,UAAWC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCD,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,mCAAmCC,wBAAsB,yBAAyBC,0BAAwB,oBACpR,CACA,SAAS4B,EAAkB,WACzBxB,CAAS,CACT,GAAGT,EACsD,EACzD,MAAO,EAJiBiC,CAIjB,OAACR,EAA2B,CAAChB,GAAD,OAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwB,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,GAAIzB,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,oBACjN,CACA,SAAS8B,EAAkB,WACzB1B,CAAS,CACT,GAAGT,EACsD,EACzD,MAAO,EAJiBmC,CAIjB,OAACV,EAA2B,CAAChB,GAAD,OAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwB,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC,CAC/DE,QAAS,SACX,GAAI3B,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,oBACjJ", "sources": ["webpack://next-shadcn-dashboard-starter/./src/components/ui/dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/scroll-area.tsx", "webpack://next-shadcn-dashboard-starter/?b8d0", "webpack://next-shadcn-dashboard-starter/../src/alert-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/alert-dialog.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { XIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot='dialog' {...props} data-sentry-element=\"DialogPrimitive.Root\" data-sentry-component=\"Dialog\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} data-sentry-element=\"DialogPrimitive.Trigger\" data-sentry-component=\"DialogTrigger\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} data-sentry-element=\"DialogPrimitive.Portal\" data-sentry-component=\"DialogPortal\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} data-sentry-element=\"DialogPrimitive.Close\" data-sentry-component=\"DialogClose\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return <DialogPrimitive.Overlay data-slot='dialog-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"DialogPrimitive.Overlay\" data-sentry-component=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return <DialogPortal data-slot='dialog-portal' data-sentry-element=\"DialogPortal\" data-sentry-component=\"DialogContent\" data-sentry-source-file=\"dialog.tsx\">\r\n      <DialogOverlay data-sentry-element=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />\r\n      <DialogPrimitive.Content data-slot='dialog-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg', className)} {...props} data-sentry-element=\"DialogPrimitive.Content\" data-sentry-source-file=\"dialog.tsx\">\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\" data-sentry-element=\"DialogPrimitive.Close\" data-sentry-source-file=\"dialog.tsx\">\r\n          <XIcon data-sentry-element=\"XIcon\" data-sentry-source-file=\"dialog.tsx\" />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>;\n}\nfunction DialogHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-header' className={cn('flex flex-col gap-2 text-center sm:text-left', className)} {...props} data-sentry-component=\"DialogHeader\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-footer' className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)} {...props} data-sentry-component=\"DialogFooter\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return <DialogPrimitive.Title data-slot='dialog-title' className={cn('text-lg leading-none font-semibold', className)} {...props} data-sentry-element=\"DialogPrimitive.Title\" data-sentry-component=\"DialogTitle\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return <DialogPrimitive.Description data-slot='dialog-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"DialogPrimitive.Description\" data-sentry-component=\"DialogDescription\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nexport { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogOverlay, DialogPortal, DialogTitle, DialogTrigger };", "'use client';\n\nimport * as React from 'react';\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';\nimport { cn } from '@/lib/utils';\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return <ScrollAreaPrimitive.Root data-slot='scroll-area' className={cn('relative', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.Root\" data-sentry-component=\"ScrollArea\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.Viewport data-slot='scroll-area-viewport' className='focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1' data-sentry-element=\"ScrollAreaPrimitive.Viewport\" data-sentry-source-file=\"scroll-area.tsx\">\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar data-sentry-element=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n      <ScrollAreaPrimitive.Corner data-sentry-element=\"ScrollAreaPrimitive.Corner\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.Root>;\n}\nfunction ScrollBar({\n  className,\n  orientation = 'vertical',\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return <ScrollAreaPrimitive.ScrollAreaScrollbar data-slot='scroll-area-scrollbar' orientation={orientation} className={cn('flex touch-none p-px transition-colors select-none', orientation === 'vertical' && 'h-full w-2.5 border-l border-l-transparent', orientation === 'horizontal' && 'h-2.5 flex-col border-t border-t-transparent', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaScrollbar\" data-sentry-component=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.ScrollAreaThumb data-slot='scroll-area-thumb' className='bg-border relative flex-1 rounded-full' data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaThumb\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>;\n}\nexport { ScrollArea, ScrollBar };", "\nexport { deleteKeylessAction as \"7f7b45347fd50452ee6e2850ded1018991a7b086f0\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { syncKeylessConfigAction as \"7f909588461cb83e855875f4939d6f26e4ae81b49e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { createOrReadKeylessAction as \"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { invalidateCacheAction as \"7f22efd92a3b59d43d3d12fe480e87910640e1db9e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\server-actions.js\"\n", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { createDialogScope } from '@radix-ui/react-dialog';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Slottable } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst ROOT_NAME = 'AlertDialog';\n\ntype ScopedProps<P> = P & { __scopeAlertDialog?: Scope };\nconst [createAlertDialogContext, createAlertDialogScope] = createContextScope(ROOT_NAME, [\n  createDialogScope,\n]);\nconst useDialogScope = createDialogScope();\n\ntype DialogProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Root>;\ninterface AlertDialogProps extends Omit<DialogProps, 'modal'> {}\n\nconst AlertDialog: React.FC<AlertDialogProps> = (props: ScopedProps<AlertDialogProps>) => {\n  const { __scopeAlertDialog, ...alertDialogProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Root {...dialogScope} {...alertDialogProps} modal={true} />;\n};\n\nAlertDialog.displayName = ROOT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTrigger\n * -----------------------------------------------------------------------------------------------*/\nconst TRIGGER_NAME = 'AlertDialogTrigger';\n\ntype AlertDialogTriggerElement = React.ElementRef<typeof DialogPrimitive.Trigger>;\ntype DialogTriggerProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Trigger>;\ninterface AlertDialogTriggerProps extends DialogTriggerProps {}\n\nconst AlertDialogTrigger = React.forwardRef<AlertDialogTriggerElement, AlertDialogTriggerProps>(\n  (props: ScopedProps<AlertDialogTriggerProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...triggerProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Trigger {...dialogScope} {...triggerProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'AlertDialogPortal';\n\ntype DialogPortalProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Portal>;\ninterface AlertDialogPortalProps extends DialogPortalProps {}\n\nconst AlertDialogPortal: React.FC<AlertDialogPortalProps> = (\n  props: ScopedProps<AlertDialogPortalProps>\n) => {\n  const { __scopeAlertDialog, ...portalProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Portal {...dialogScope} {...portalProps} />;\n};\n\nAlertDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'AlertDialogOverlay';\n\ntype AlertDialogOverlayElement = React.ElementRef<typeof DialogPrimitive.Overlay>;\ntype DialogOverlayProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>;\ninterface AlertDialogOverlayProps extends DialogOverlayProps {}\n\nconst AlertDialogOverlay = React.forwardRef<AlertDialogOverlayElement, AlertDialogOverlayProps>(\n  (props: ScopedProps<AlertDialogOverlayProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...overlayProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Overlay {...dialogScope} {...overlayProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogOverlay.displayName = OVERLAY_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AlertDialogContent';\n\ntype AlertDialogContentContextValue = {\n  cancelRef: React.MutableRefObject<AlertDialogCancelElement | null>;\n};\n\nconst [AlertDialogContentProvider, useAlertDialogContentContext] =\n  createAlertDialogContext<AlertDialogContentContextValue>(CONTENT_NAME);\n\ntype AlertDialogContentElement = React.ElementRef<typeof DialogPrimitive.Content>;\ntype DialogContentProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>;\ninterface AlertDialogContentProps\n  extends Omit<DialogContentProps, 'onPointerDownOutside' | 'onInteractOutside'> {}\n\nconst AlertDialogContent = React.forwardRef<AlertDialogContentElement, AlertDialogContentProps>(\n  (props: ScopedProps<AlertDialogContentProps>, forwardedRef) => {\n    const { __scopeAlertDialog, children, ...contentProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const contentRef = React.useRef<AlertDialogContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const cancelRef = React.useRef<AlertDialogCancelElement | null>(null);\n\n    return (\n      <DialogPrimitive.WarningProvider\n        contentName={CONTENT_NAME}\n        titleName={TITLE_NAME}\n        docsSlug=\"alert-dialog\"\n      >\n        <AlertDialogContentProvider scope={__scopeAlertDialog} cancelRef={cancelRef}>\n          <DialogPrimitive.Content\n            role=\"alertdialog\"\n            {...dialogScope}\n            {...contentProps}\n            ref={composedRefs}\n            onOpenAutoFocus={composeEventHandlers(contentProps.onOpenAutoFocus, (event) => {\n              event.preventDefault();\n              cancelRef.current?.focus({ preventScroll: true });\n            })}\n            onPointerDownOutside={(event) => event.preventDefault()}\n            onInteractOutside={(event) => event.preventDefault()}\n          >\n            {/**\n             * We have to use `Slottable` here as we cannot wrap the `AlertDialogContentProvider`\n             * around everything, otherwise the `DescriptionWarning` would be rendered straight away.\n             * This is because we want the accessibility checks to run only once the content is actually\n             * open and that behaviour is already encapsulated in `DialogContent`.\n             */}\n            <Slottable>{children}</Slottable>\n            {process.env.NODE_ENV === 'development' && (\n              <DescriptionWarning contentRef={contentRef} />\n            )}\n          </DialogPrimitive.Content>\n        </AlertDialogContentProvider>\n      </DialogPrimitive.WarningProvider>\n    );\n  }\n);\n\nAlertDialogContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'AlertDialogTitle';\n\ntype AlertDialogTitleElement = React.ElementRef<typeof DialogPrimitive.Title>;\ntype DialogTitleProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>;\ninterface AlertDialogTitleProps extends DialogTitleProps {}\n\nconst AlertDialogTitle = React.forwardRef<AlertDialogTitleElement, AlertDialogTitleProps>(\n  (props: ScopedProps<AlertDialogTitleProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...titleProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Title {...dialogScope} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'AlertDialogDescription';\n\ntype AlertDialogDescriptionElement = React.ElementRef<typeof DialogPrimitive.Description>;\ntype DialogDescriptionProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>;\ninterface AlertDialogDescriptionProps extends DialogDescriptionProps {}\n\nconst AlertDialogDescription = React.forwardRef<\n  AlertDialogDescriptionElement,\n  AlertDialogDescriptionProps\n>((props: ScopedProps<AlertDialogDescriptionProps>, forwardedRef) => {\n  const { __scopeAlertDialog, ...descriptionProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Description {...dialogScope} {...descriptionProps} ref={forwardedRef} />;\n});\n\nAlertDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACTION_NAME = 'AlertDialogAction';\n\ntype AlertDialogActionElement = React.ElementRef<typeof DialogPrimitive.Close>;\ntype DialogCloseProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogActionProps extends DialogCloseProps {}\n\nconst AlertDialogAction = React.forwardRef<AlertDialogActionElement, AlertDialogActionProps>(\n  (props: ScopedProps<AlertDialogActionProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...actionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Close {...dialogScope} {...actionProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogAction.displayName = ACTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogCancel\n * -----------------------------------------------------------------------------------------------*/\n\nconst CANCEL_NAME = 'AlertDialogCancel';\n\ntype AlertDialogCancelElement = React.ElementRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogCancelProps extends DialogCloseProps {}\n\nconst AlertDialogCancel = React.forwardRef<AlertDialogCancelElement, AlertDialogCancelProps>(\n  (props: ScopedProps<AlertDialogCancelProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...cancelProps } = props;\n    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const ref = useComposedRefs(forwardedRef, cancelRef);\n    return <DialogPrimitive.Close {...dialogScope} {...cancelProps} ref={ref} />;\n  }\n);\n\nAlertDialogCancel.displayName = CANCEL_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<AlertDialogContentElement | null>;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef }) => {\n  const MESSAGE = `\\`${CONTENT_NAME}\\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \\`${CONTENT_NAME}\\` by passing a \\`${DESCRIPTION_NAME}\\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \\`id\\` and passing the same value to the \\`aria-describedby\\` prop in \\`${CONTENT_NAME}\\`. If the description is confusing or duplicative for sighted users, you can use the \\`@radix-ui/react-visually-hidden\\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;\n\n  React.useEffect(() => {\n    const hasDescription = document.getElementById(\n      contentRef.current?.getAttribute('aria-describedby')!\n    );\n    if (!hasDescription) console.warn(MESSAGE);\n  }, [MESSAGE, contentRef]);\n\n  return null;\n};\n\nconst Root = AlertDialog;\nconst Trigger = AlertDialogTrigger;\nconst Portal = AlertDialogPortal;\nconst Overlay = AlertDialogOverlay;\nconst Content = AlertDialogContent;\nconst Action = AlertDialogAction;\nconst Cancel = AlertDialogCancel;\nconst Title = AlertDialogTitle;\nconst Description = AlertDialogDescription;\n\nexport {\n  createAlertDialogScope,\n  //\n  AlertDialog,\n  AlertDialogTrigger,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogContent,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Action,\n  Cancel,\n  Title,\n  Description,\n};\nexport type {\n  AlertDialogProps,\n  AlertDialogTriggerProps,\n  AlertDialogPortalProps,\n  AlertDialogOverlayProps,\n  AlertDialogContentProps,\n  AlertDialogActionProps,\n  AlertDialogCancelProps,\n  AlertDialogTitleProps,\n  AlertDialogDescriptionProps,\n};\n", "'use client';\n\nimport * as React from 'react';\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';\nimport { cn } from '@/lib/utils';\nimport { buttonVariants } from '@/components/ui/button';\nfunction AlertDialog({\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\n  return <AlertDialogPrimitive.Root data-slot='alert-dialog' {...props} data-sentry-element=\"AlertDialogPrimitive.Root\" data-sentry-component=\"AlertDialog\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogTrigger({\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\n  return <AlertDialogPrimitive.Trigger data-slot='alert-dialog-trigger' {...props} data-sentry-element=\"AlertDialogPrimitive.Trigger\" data-sentry-component=\"AlertDialogTrigger\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogPortal({\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\n  return <AlertDialogPrimitive.Portal data-slot='alert-dialog-portal' {...props} data-sentry-element=\"AlertDialogPrimitive.Portal\" data-sentry-component=\"AlertDialogPortal\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\n  return <AlertDialogPrimitive.Overlay data-slot='alert-dialog-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Overlay\" data-sentry-component=\"AlertDialogOverlay\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\n  return <AlertDialogPortal data-sentry-element=\"AlertDialogPortal\" data-sentry-component=\"AlertDialogContent\" data-sentry-source-file=\"alert-dialog.tsx\">\r\n      <AlertDialogOverlay data-sentry-element=\"AlertDialogOverlay\" data-sentry-source-file=\"alert-dialog.tsx\" />\r\n      <AlertDialogPrimitive.Content data-slot='alert-dialog-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg', className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Content\" data-sentry-source-file=\"alert-dialog.tsx\" />\r\n    </AlertDialogPortal>;\n}\nfunction AlertDialogHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='alert-dialog-header' className={cn('flex flex-col gap-2 text-center sm:text-left', className)} {...props} data-sentry-component=\"AlertDialogHeader\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='alert-dialog-footer' className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)} {...props} data-sentry-component=\"AlertDialogFooter\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\n  return <AlertDialogPrimitive.Title data-slot='alert-dialog-title' className={cn('text-lg font-semibold', className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Title\" data-sentry-component=\"AlertDialogTitle\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\n  return <AlertDialogPrimitive.Description data-slot='alert-dialog-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Description\" data-sentry-component=\"AlertDialogDescription\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogAction({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\n  return <AlertDialogPrimitive.Action className={cn(buttonVariants(), className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Action\" data-sentry-component=\"AlertDialogAction\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogCancel({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\n  return <AlertDialogPrimitive.Cancel className={cn(buttonVariants({\n    variant: 'outline'\n  }), className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Cancel\" data-sentry-component=\"AlertDialogCancel\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nexport { AlertDialog, AlertDialogPortal, AlertDialogOverlay, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogFooter, AlertDialogTitle, AlertDialogDescription, AlertDialogAction, AlertDialogCancel };"], "names": ["Dialog", "props", "DialogPrimitive", "data-slot", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "DialogTrigger", "DialogPortal", "DialogOverlay", "className", "cn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "XIcon", "span", "DialogHeader", "div", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogDescription", "ScrollArea", "ScrollAreaPrimitive", "<PERSON><PERSON>Bar", "orientation", "AlertDialog", "AlertDialogPrimitive", "AlertDialogPortal", "AlertDialogOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogTitle", "AlertDialogDescription", "AlertDialogAction", "buttonVariants", "AlertDialogCancel", "variant"], "sourceRoot": ""}