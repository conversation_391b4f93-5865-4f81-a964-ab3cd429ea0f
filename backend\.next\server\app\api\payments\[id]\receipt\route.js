(()=>{var e={};e.id=6734,e.ids=[6734],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1317:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.d(r,{w:()=>a,x:()=>c});var i=t(10724),o=t(68117),n=t(32481),u=e([o]);async function a(e){try{let r=e.headers.get("x-clerk-user-id"),t=e.headers.get("x-user-email");if(!r||!t)return console.log("Missing Clerk user headers"),null;let s=await (0,i.nm0)({config:o.A}),u={id:r,email:t};console.log("Syncing Clerk user with Payload:",u);let a=await (0,n.kw)(s,u);return console.log("Synced Payload user:",a),{payload:s,user:a}}catch(e){return console.error("Payload authentication error:",e),null}}async function c(e,r,t,s={}){let{payload:i,user:o}=e;switch(t){case"find":return await i.find({collection:r,user:o,...s});case"findByID":return await i.findByID({collection:r,user:o,...s});case"create":return await i.create({collection:r,user:o,...s});case"update":return await i.update({collection:r,user:o,...s});case"delete":return await i.delete({collection:r,user:o,...s});default:throw Error(`Unsupported operation: ${t}`)}}o=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30646:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{GET:()=>u,POST:()=>a});var i=t(27492),o=t(1317),n=e([o]);async function u(e,{params:r}){try{let{id:t}=await r,s=await (0,o.w)(e);if(!s)return i.NextResponse.json({error:"Authentication required"},{status:401});let n=await (0,o.x)(s,"payments","findByID",{id:t,depth:3});if(!n)return i.NextResponse.json({error:"Payment not found"},{status:404});if("completed"!==n.paymentStatus)return i.NextResponse.json({error:"Receipt can only be generated for completed payments"},{status:400});let u={receiptNumber:n.receiptNumber,paymentNumber:n.paymentNumber,paymentDate:n.paymentDate,amount:n.amount,paymentMethod:n.paymentMethod,bill:{billNumber:n.bill?.billNumber,description:n.bill?.description,totalAmount:n.bill?.totalAmount,remainingAmount:n.bill?.remainingAmount},patient:{fullName:n.patient?.fullName,phone:n.patient?.phone,email:n.patient?.email},receivedBy:{firstName:n.receivedBy?.firstName,lastName:n.receivedBy?.lastName,email:n.receivedBy?.email},clinic:{name:"北海岸医美诊所",address:"诊所地址",phone:"诊所电话",email:"<EMAIL>"},notes:n.notes,transactionId:n.transactionId,relatedDeposit:n.relatedDeposit?{depositNumber:n.relatedDeposit?.depositNumber,depositType:n.relatedDeposit?.depositType}:null,generatedAt:new Date().toISOString(),generatedBy:{firstName:s.user.firstName,lastName:s.user.lastName,email:s.user.email}};return i.NextResponse.json({success:!0,receipt:u})}catch(e){return console.error("Error generating receipt:",e),i.NextResponse.json({error:"Failed to generate receipt"},{status:500})}}async function a(e,{params:r}){try{let{id:t}=await r,s=await (0,o.w)(e);if(!s)return i.NextResponse.json({error:"Authentication required"},{status:401});if("admin"!==s.user.role)return i.NextResponse.json({error:"Only administrators can regenerate receipts"},{status:403});let n=new Date,u=n.getFullYear(),a=String(n.getMonth()+1).padStart(2,"0"),c=String(n.getDate()).padStart(2,"0"),p=n.getTime().toString().slice(-6),d=`REC-${u}${a}${c}-${p}`,l=await (0,o.x)(s,"payments","update",{id:t,data:{receiptNumber:d}});return i.NextResponse.json({success:!0,message:"Receipt number regenerated successfully",receiptNumber:d,payment:l})}catch(e){return console.error("Error regenerating receipt:",e),i.NextResponse.json({error:"Failed to regenerate receipt"},{status:500})}}o=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},32467:e=>{"use strict";e.exports=require("node:http2")},32481:(e,r,t)=>{"use strict";async function s(e,r,t="front-desk"){try{let s=await e.find({collection:"users",where:{clerkId:{equals:r.id}},limit:1});if(!(s.docs.length>0))return await e.create({collection:"users",data:{role:t,clerkId:r.id}});{let r=s.docs[0];return await e.update({collection:"users",id:r.id,data:{}})}}catch(e){throw console.error("Error syncing Clerk user with Payload:",e),Error("Failed to sync user authentication")}}t.d(r,{kw:()=>s})},32785:e=>{"use strict";e.exports=import("prettier")},33312:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>c,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var i=t(70293),o=t(32498),n=t(83889),u=t(30646),a=e([u]);u=(a.then?(await a)():a)[0];let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/payments/[id]/receipt/route",pathname:"/api/payments/[id]/receipt",filename:"route",bundlePath:"app/api/payments/[id]/receipt/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\payments\\[id]\\receipt\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=p;function c(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},34869:()=>{},35672:e=>{"use strict";e.exports=require("dns/promises")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},37830:e=>{"use strict";e.exports=require("node:stream/web")},38522:e=>{"use strict";e.exports=require("node:zlib")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},93077:()=>{},94735:e=>{"use strict";e.exports=require("events")},98995:e=>{"use strict";e.exports=require("node:module")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[3889,2481,9556,8754],()=>t(33312));module.exports=s})();