"use strict";exports.id=4848,exports.ids=[4848],exports.modules={84848:(e,l,a)=>{a.r(l),a.d(l,{RelationshipComponent:()=>g});var s=a(77672),t=a(42148),o=a(90420),i=a(79097),n=a(1062),r=a(16826),c=a(43232),d=a(93779),u=a(28709),p=a(47186),m=a(26620),b="lexical-relationship",h={depth:0},x=e=>{let{data:{relationTo:l,value:a},nodeKey:x}=e;if("object"==typeof a)throw Error("Relationship value should be a string or number. The Lexical Relationship component should not receive the populated value object.");let g=(0,m.useRef)(null),[v]=(0,n.DF)(),{fieldProps:{readOnly:_}}=(0,o.b)(),{config:{routes:{api:f},serverURL:j},getEntityConfig:$}=(0,c.b)(),[w]=(0,m.useState)(()=>$({collectionSlug:l})),{i18n:R,t:y}=(0,d.d)(),[D,N]=(0,m.useReducer)(e=>e+1,0),[{data:C},{setParams:S}]=(0,u.usePayloadAPI)(`${j}${f}/${w.slug}/${a}`,{initialParams:h}),{closeDocumentDrawer:B,DocumentDrawer:P,DocumentDrawerToggler:k}=(0,t.a)({id:a,collectionSlug:w.slug}),E=(0,m.useCallback)(()=>{v.update(()=>{(0,p.nsf)(x)?.remove()})},[v,x]),T=m.useCallback(()=>{S({...h,cacheBust:D}),B(),N()},[D,S,B]);return(0,i.jsxs)("div",{className:b,contentEditable:!1,ref:g,children:[(0,i.jsxs)("div",{className:`${b}__wrap`,children:[(0,i.jsx)("p",{className:`${b}__label`,children:y("fields:labelRelationship",{label:w.labels?.singular?(0,r.s)(w.labels?.singular,R):w.slug})}),(0,i.jsx)(k,{className:`${b}__doc-drawer-toggler`,children:(0,i.jsx)("p",{className:`${b}__title`,children:C?C[w?.admin?.useAsTitle||"id"]:a})})]}),v.isEditable()&&(0,i.jsxs)("div",{className:`${b}__actions`,children:[(0,i.jsx)(u.Button,{buttonStyle:"icon-label",className:`${b}__swapButton`,disabled:_,el:"button",icon:"swap",onClick:()=>{x&&v.dispatchCommand(s.a,{replace:{nodeKey:x}})},round:!0,tooltip:y("fields:swapRelationship")}),(0,i.jsx)(u.Button,{buttonStyle:"icon-label",className:`${b}__removeButton`,disabled:_,icon:"x",onClick:e=>{e.preventDefault(),E()},round:!0,tooltip:y("fields:removeRelationship")})]}),!!a&&(0,i.jsx)(P,{onSave:T})]})},g=e=>(0,i.jsx)(x,{...e})}};