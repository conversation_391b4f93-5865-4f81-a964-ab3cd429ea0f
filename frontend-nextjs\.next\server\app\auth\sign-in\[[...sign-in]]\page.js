try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c8042bd2-a6c0-453a-935e-a74467d69bea",e._sentryDebugIdIdentifier="sentry-dbid-c8042bd2-a6c0-453a-935e-a74467d69bea")}catch(e){}(()=>{var e={};e.id=6059,e.ids=[6059],e.modules={2099:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>j,generateImageMetadata:()=>y,generateMetadata:()=>b,generateViewport:()=>w,metadata:()=>h});var i=r(63033),n=r(78869),a=r(22762),o=r(19557),d=r(98451),l=r(27025),c=r(60936),u=r(7944),p=r.n(u);function x({stars:e}){return(0,n.jsxs)("div",{className:"relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0","data-sentry-component":"SignInViewPage","data-sentry-source-file":"sign-in-view.tsx",children:[(0,n.jsx)(p(),{href:"/examples/authentication",className:(0,o.cn)((0,a.r)({variant:"ghost"}),"absolute top-4 right-4 hidden md:top-8 md:right-8"),"data-sentry-element":"Link","data-sentry-source-file":"sign-in-view.tsx",children:"Login"}),(0,n.jsxs)("div",{className:"bg-muted relative hidden h-full flex-col p-10 text-white lg:flex dark:border-r",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-zinc-900"}),(0,n.jsxs)("div",{className:"relative z-20 flex items-center text-lg font-medium",children:[(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2 h-6 w-6","data-sentry-element":"svg","data-sentry-source-file":"sign-in-view.tsx",children:(0,n.jsx)("path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3","data-sentry-element":"path","data-sentry-source-file":"sign-in-view.tsx"})}),"Logo"]}),(0,n.jsx)("div",{className:"relative z-20 mt-auto",children:(0,n.jsxs)("blockquote",{className:"space-y-2",children:[(0,n.jsx)("p",{className:"text-lg",children:"“This starter template has saved me countless hours of work and helped me deliver projects to my clients faster than ever before.”"}),(0,n.jsx)("footer",{className:"text-sm",children:"Random Dude"})]})})]}),(0,n.jsx)("div",{className:"flex h-full items-center justify-center p-4 lg:p-8",children:(0,n.jsxs)("div",{className:"flex w-full max-w-md flex-col items-center justify-center space-y-6",children:[(0,n.jsxs)(p(),{className:(0,o.cn)("group inline-flex hover:text-yellow-200"),target:"_blank",href:"https://github.com/kiranism/next-shadcn-dashboard-starter","data-sentry-element":"Link","data-sentry-source-file":"sign-in-view.tsx",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(l.ERf,{className:"size-4","data-sentry-element":"GitHubLogoIcon","data-sentry-source-file":"sign-in-view.tsx"}),(0,n.jsx)("span",{className:"ml-1 inline",children:"Star on GitHub"})," "]}),(0,n.jsxs)("div",{className:"ml-2 flex items-center gap-1 text-sm md:flex",children:[(0,n.jsx)(c.A,{className:"size-4 text-gray-500 transition-all duration-300 group-hover:text-yellow-300",fill:"currentColor","data-sentry-element":"IconStar","data-sentry-source-file":"sign-in-view.tsx"}),(0,n.jsx)("span",{className:"font-display font-medium",children:e})]})]}),(0,n.jsx)(d.SignIn,{initialValues:{emailAddress:"<EMAIL>"},"data-sentry-element":"ClerkSignInForm","data-sentry-source-file":"sign-in-view.tsx"}),(0,n.jsxs)("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["By clicking continue, you agree to our"," ",(0,n.jsx)(p(),{href:"/terms",className:"hover:text-primary underline underline-offset-4","data-sentry-element":"Link","data-sentry-source-file":"sign-in-view.tsx",children:"Terms of Service"})," ","and"," ",(0,n.jsx)(p(),{href:"/privacy",className:"hover:text-primary underline underline-offset-4","data-sentry-element":"Link","data-sentry-source-file":"sign-in-view.tsx",children:"Privacy Policy"}),"."]})]})})]})}var m=r(19761);let h={title:"Authentication | Sign In",description:"Sign In page for authentication."};async function g(){let e=3e3;try{let t=await fetch("https://api.github.com/repos/kiranism/next-shadcn-dashboard-starter",{next:{revalidate:86400}});t.ok&&(e=(await t.json()).stargazers_count||e)}catch(e){}return(0,n.jsx)(x,{stars:e,"data-sentry-element":"SignInViewPage","data-sentry-component":"Page","data-sentry-source-file":"page.tsx"})}let v={...i},f="workUnitAsyncStorage"in v?v.workUnitAsyncStorage:"requestAsyncStorage"in v?v.requestAsyncStorage:void 0;s=new Proxy(g,{apply:(e,t,r)=>{let s,i,n;try{let e=f?.getStore();s=e?.headers.get("sentry-trace")??void 0,i=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return m.wrapServerComponentWithSentry(e,{componentRoute:"/auth/sign-in/[[...sign-in]]",componentType:"Page",sentryTraceHeader:s,baggageHeader:i,headers:n}).apply(t,r)}});let b=void 0,y=void 0,w=void 0,j=s},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21692:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f7b45347fd50452ee6e2850ded1018991a7b086f0":()=>s.at,"7f909588461cb83e855875f4939d6f26e4ae81b49e":()=>s.ot,"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261":()=>s.ai});var s=r(64965)},21820:e=>{"use strict";e.exports=require("os")},22483:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f22efd92a3b59d43d3d12fe480e87910640e1db9e":()=>s.y});var s=r(41372)},22762:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>o});var s=r(78869);r(22576);var i=r(41488),n=r(31963),a=r(19557);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:n=!1,...d}){let l=n?i.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,a.cn)(o({variant:t,size:r,className:e})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},46437:(e,t,r)=>{Promise.resolve().then(r.bind(r,21830)),Promise.resolve().then(r.bind(r,55439)),Promise.resolve().then(r.bind(r,40112)),Promise.resolve().then(r.bind(r,15349)),Promise.resolve().then(r.bind(r,90339)),Promise.resolve().then(r.bind(r,98451)),Promise.resolve().then(r.t.bind(r,7944,23))},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59589:(e,t,r)=>{Promise.resolve().then(r.bind(r,52943)),Promise.resolve().then(r.bind(r,17893)),Promise.resolve().then(r.bind(r,8074)),Promise.resolve().then(r.bind(r,27144)),Promise.resolve().then(r.bind(r,79153)),Promise.resolve().then(r.bind(r,95614)),Promise.resolve().then(r.t.bind(r,38250,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89078:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var s=r(29703),i=r(85544),n=r(62458),a=r(77821),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);r.d(t,o);let d={children:["",{children:["auth",{children:["sign-in",{children:["[[...sign-in]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2099)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\auth\\sign-in\\[[...sign-in]]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\auth\\sign-in\\[[...sign-in]]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/auth/sign-in/[[...sign-in]]/page",pathname:"/auth/sign-in/[[...sign-in]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[55,3738,1950,7927,6451,5618,2584,7988,2173,8774],()=>r(89078));module.exports=s})();
//# sourceMappingURL=page.js.map