{"version": 3, "file": "1995.js", "mappings": "kjBAGA,eACA,eAkBA,WACA,MAAe,QAAe,GAC9B,EAAc,QAAc,GAC5B,yBACA,EACA,SACA,0BACA,uBACA,KACA,aACA,iBAAU,GAAe,CAAE,OAAa,GACxC,IACA,IACA,EADA,MAEA,IACA,OAAiD,iBAAmB,MAAQ,CAC5E,CAAM,MACN,KAAwB,CACxB,CACA,oBACA,CAAI,MACJ,MACA,CACA,CACA,WACA,kBAAU,GAAgB,CAAE,OAAa,GACzC,KACA,EACA,EAEA,iFACA,CACA,gBACA,YACA,QACA,EAEA,EACA,OACA,WAAU,GAAS,CAAE,OAAa,GAClC,IACA,KAAyB,sBAA8B,CACvD,CAAI,MACJ,CACA,IACA,EACA,OACA,IAAU,cAAa,CAAE,OAAa,GACtC,cACA,EACA,mBACA,kBAAU,eAA2B,CAAE,OAAa,GACpD,OACA,YAEA,IACA,UACA,MACA,QAA4C,aAAiB,EA3E7D,WACA,eAAU,mDAA0D,CAAE,OAAa,GACnF,EAAe,QAAe,GAC9B,EAAc,QAAc,GAC5B,0BACA,OACA,QAIA,EAFA,WAEA,iBACA;;GAEG,aAAa;CAChB,CAEA,IA6DA,UACA,mEAEA,OADA,IACA,EAEA,MAAiB,OAA4B,GAAG,EAChD,gGAkBA,OAjBA,IACA,uBACA,gBACA,YACA,QACA,CAAK,EAKL,IAJA;AACA;AACA;AACA,IACA,CACA,gBACA,YACA,QACA,CAAK,GAEL,IACA,CACA,CACA,aACA,WAAU,GAAS,CAAE,OAAa,GAClC,SAGA,IACA,IACA,OAA6B,sBAA8B,CAC3D,CAAI,MACJ,CACA,IACA,uDCvHA,iCCmBA,MDlBA,UCEuC,SDDvC,KCiBuC,EDjBvC,sBAA2D,YAAa,sBACxE,ECAuC,CACvC,0CACA,eAAY,wEAA6E,EAAU,KAAS,CAW5G,WAXkG,CAW3E,GATvB,CACA,aACA,gBACA,eACA,iBACA,YACA,QACA,EAEuB,KAVN,EAAQ,KAAW,EAUb,IADvB,MATwB,MASxB,KACuB,CACvB,CACA,CAAC,IChBD,MACA,sBAA4B,GAAQ,wEACpC,EACA,OACA,EAAkB,IAClB,QADkB,EAGE,IAEpB,OACO,CAHa,CAGF,MAClB,MADkB,IAGT,EAAW,MAEpB,MAFoB,CAGpB,EAAkB,KAClB,OADkB,EAGT,EAAW,sBCpBpB,cACA,qBACA,IAAY,iBAAe,cAC3B,KACA,WAEA,QACA,CACA,CACA,cACA,eACA,SAAY,YAAe,QAC3B,KACA,WAEA,QACA,CACA,gECHA,YAyFA,sGAxFA,QACA,cACI,QAAY,SACZ,QAAY,qCACZ,QAAY,QANhB,MAOA,CACA,oBACA,IAAS,QAAY,WACrB,SAEA,iBACA,EAAgB,QAAe,wBAC/B,EAAkB,QAAY,gBAC9B,OACA,OACA,GAAW,QAAY,WACvB,KACA,EACA,qBAA2B,QAAY,2BACvC,CAEA,GADA,OAAoD,QAAY,SAChE,CACA,MAA2B,QAAY,UACvC,aACA,qBAA2B,QAAY,2BACvC,CACA,SACA,CACA,EACA,cACA,cACA,cAKA,cACA,IAAU,0BAAqC,EAC/C,GACA,KACA,MAEA,sBACA,aACA,KACA,KACK,sBAEL,EACA,aACA,2BAA2C,QAAY,iBACvD,EAGA,cAFA,EAGA,EACA,aACA,8BACA,SAEA,0BACA,MACA,SAEA,IACA,aAGA,OAFA,eACA,gBACA,EACA,CAAI,SAMJ,OAHA,aAFA,cACA,wEACA,YACA,aAAyB,QAAY,UAErC,EACA,CACA,EAGA,OACA,eACA,gBAIA,sCACA,EAEA,QACA,eACI,QAAY,SACZ,QAAY,SACZ,QAAY,SACZ,QAAY,UAAoB,EAChC,QAAY,YACZ,QAAY,SACZ,QAAY,SAChB,+CACA,4CACA,wBACA,kBACA,oBACK,EACL,2CAGM,QAAY,yCAFZ,QAAY,yBAId,QAAY,mBACZ,QAAY,iCACZ,QAAY,6CAChB,MAAsB,QAAmB,kBACzC,KACM,QAAY,sCAElB,cACM,QAAY,gDAEd,QAAY,cAChB,CACA,wBACoB,gBAAZ,QAAY,uBAGZ,QAAY,gDAA8D,QAAQ,wCAG1F,yDAIA,CACA,cACA,MAAW,QAAY,6CAA2D,QAAQ,mCAC1F,CACA,UACA,MAA4B,QAAe,wCACvC,QAAe,gCACV,QAAe,8CAGpB,QAAY,iBACZ,QAAe,sBACnB,CACA,EACA,cACA,cACA,cACA,cACA,cACA,cACA,gBACA,sCAA4C,QAAe,yBAC3D,EACA,gBACA,0BAEA,EADA,IAAoC,QAAY,4CAIhD,CAAU,QAAY,4BACtB,EACA,aACA,yCACI,QAAe,sBAInB,GADuB,QAAY,iBAA0B,QAAY,uBACzE,CACQ,QAAY,UACpB,yEACa,QAAY,UAErB,QAAe,sBACnB,MACA,CACM,QAAY,WAGlB,+BACI,QAAY,iCACV,QAAe,qBACrB,CAAK,GAED,QAAY,wBACV,QAAe,qBACrB,CAAK,KAEL,EACA,aACA,0BAA6B,QAAY,oBACzC,cAEA,qBACA,OAAc,QAAY,QAC1B,CAAK,EACL,SACA,iCACA,CACA,CAAG,8BACC,QAAY,WAChB,CAAG,mBACH,EAIA,gBACA,eAGA,iCACA,8CACA,eACA,oBAEA,qCAEA,EAMA,aACA,OACA,KAAU,QAAY,aACtB,QAAa,QAAY,qBAKzB,MAHA,0CACA,IAAoB,+CAEpB,CACA,EAIA,gBACA,MAAsB,QAAe,sBACrC,OACA,QACA,GAAQ,QAAY,0BACpB,GAAQ,QAAY,0BACpB,WACA,eACA,GAAO,QAAY,yBAAqC,GAAI,QAAY,yBAAmC,EAAI,CAC/G,GAAO,QAAY,oBAAgC,GAAI,QAAY,oBAA8B,EAAI,CACrG,SACA,CACA,EE5PmB,OAAgB,CAAC,IAAW,iBCC/C,OACA,UAAa,IAAU,CACvB,eAAkB,IAAe,CACjC,OAAU,IAAO,CACjB,WAAc,IAAW,CACzB,aAAgB,gBAAgB,GAAG,EACnC,QAD6C,CACjC,IAAS,CACrB,OAAU,IAAM,CAChB,YAAe,IAAY,CAC3B,YAAe,IAAY,CAC3B,WACA,SAAc,IAAkB,CAChC,MAAW,IAAe,CAE1B,EACA,KAAkD,CDflD,YACA,OAAiB,MACjB,EAAoB,QAAsB,IAC1C,EAAuB,QAAyB,EAAG,sBAA0B,EAC7E,MAAwB,EAAkB,CAC1C,eAD0C,eAE1C,iBACA,sBACA,gBACA,kBAA4B,yDAAmE,EAC/F,CAAG,EACH,OACA,KACA,KACA,WACA,EACA,ECDmE,CAAG,UAA0C", "sources": ["webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/keyless-node.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/chunk-BUSYA2B4.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/runtime/node/safe-node-apis.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/fs/utils.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/chunk-P263NW7Z.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-L6WULBPV.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/telemetry.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/index.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/createClerkClient.js"], "sourcesContent": ["import \"../chunk-BUSYA2B4.js\";\nimport { createClerkClientWithOptions } from \"./createClerkClient\";\nimport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow } from \"./fs/utils\";\nconst CLERK_HIDDEN = \".clerk\";\nconst CLERK_LOCK = \"clerk.lock\";\nfunction updateGitignore() {\n  const { existsSync, writeFileSync, readFileSync, appendFileSync } = nodeFsOrThrow();\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  const gitignorePath = path.join(cwd(), \".gitignore\");\n  if (!existsSync(gitignorePath)) {\n    writeFileSync(gitignorePath, \"\");\n  }\n  const gitignoreContent = readFileSync(gitignorePath, \"utf-8\");\n  const COMMENT = `# clerk configuration (can include secrets)`;\n  if (!gitignoreContent.includes(CLERK_HIDDEN + \"/\")) {\n    appendFileSync(gitignorePath, `\n${COMMENT}\n/${CLERK_HIDDEN}/\n`);\n  }\n}\nconst generatePath = (...slugs) => {\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  return path.join(cwd(), CLERK_HIDDEN, ...slugs);\n};\nconst _TEMP_DIR_NAME = \".tmp\";\nconst getKeylessConfigurationPath = () => generatePath(_TEMP_DIR_NAME, \"keyless.json\");\nconst getKeylessReadMePath = () => generatePath(_TEMP_DIR_NAME, \"README.md\");\nlet isCreatingFile = false;\nfunction safeParseClerkFile() {\n  const { readFileSync } = nodeFsOrThrow();\n  try {\n    const CONFIG_PATH = getKeylessConfigurationPath();\n    let fileAsString;\n    try {\n      fileAsString = readFileSync(CONFIG_PATH, { encoding: \"utf-8\" }) || \"{}\";\n    } catch {\n      fileAsString = \"{}\";\n    }\n    return JSON.parse(fileAsString);\n  } catch {\n    return void 0;\n  }\n}\nconst lockFileWriting = () => {\n  const { writeFileSync } = nodeFsOrThrow();\n  isCreatingFile = true;\n  writeFileSync(\n    CLERK_LOCK,\n    // In the rare case, the file persists give the developer enough context.\n    \"This file can be deleted. Please delete this file and refresh your application\",\n    {\n      encoding: \"utf8\",\n      mode: \"0777\",\n      flag: \"w\"\n    }\n  );\n};\nconst unlockFileWriting = () => {\n  const { rmSync } = nodeFsOrThrow();\n  try {\n    rmSync(CLERK_LOCK, { force: true, recursive: true });\n  } catch {\n  }\n  isCreatingFile = false;\n};\nconst isFileWritingLocked = () => {\n  const { existsSync } = nodeFsOrThrow();\n  return isCreatingFile || existsSync(CLERK_LOCK);\n};\nasync function createOrReadKeyless() {\n  const { writeFileSync, mkdirSync } = nodeFsOrThrow();\n  if (isFileWritingLocked()) {\n    return null;\n  }\n  lockFileWriting();\n  const CONFIG_PATH = getKeylessConfigurationPath();\n  const README_PATH = getKeylessReadMePath();\n  mkdirSync(generatePath(_TEMP_DIR_NAME), { recursive: true });\n  updateGitignore();\n  const envVarsMap = safeParseClerkFile();\n  if ((envVarsMap == null ? void 0 : envVarsMap.publishableKey) && (envVarsMap == null ? void 0 : envVarsMap.secretKey)) {\n    unlockFileWriting();\n    return envVarsMap;\n  }\n  const client = createClerkClientWithOptions({});\n  const accountlessApplication = await client.__experimental_accountlessApplications.createAccountlessApplication().catch(() => null);\n  if (accountlessApplication) {\n    writeFileSync(CONFIG_PATH, JSON.stringify(accountlessApplication), {\n      encoding: \"utf8\",\n      mode: \"0777\",\n      flag: \"w\"\n    });\n    const README_NOTIFICATION = `\n## DO NOT COMMIT\nThis directory is auto-generated from \\`@clerk/nextjs\\` because you are running in Keyless mode. Avoid committing the \\`.clerk/\\` directory as it includes the secret key of the unclaimed instance.\n  `;\n    writeFileSync(README_PATH, README_NOTIFICATION, {\n      encoding: \"utf8\",\n      mode: \"0777\",\n      flag: \"w\"\n    });\n  }\n  unlockFileWriting();\n  return accountlessApplication;\n}\nfunction removeKeyless() {\n  const { rmSync } = nodeFsOrThrow();\n  if (isFileWritingLocked()) {\n    return void 0;\n  }\n  lockFileWriting();\n  try {\n    rmSync(generatePath(), { force: true, recursive: true });\n  } catch {\n  }\n  unlockFileWriting();\n}\nexport {\n  createOrReadKeyless,\n  removeKeyless,\n  safeParseClerkFile\n};\n//# sourceMappingURL=keyless-node.js.map", "var __getOwnPropNames = Object.getOwnPropertyNames;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\n\nexport {\n  __commonJS\n};\n//# sourceMappingURL=chunk-BUSYA2B4.js.map", "import {\n  __commonJS\n} from \"../../chunk-BUSYA2B4.js\";\nvar require_safe_node_apis = __commonJS({\n  \"src/runtime/node/safe-node-apis.js\"(exports, module) {\n    const { existsSync, writeFileSync, readFileSync, appendFileSync, mkdirSync, rmSync } = require(\"node:fs\");\n    const path = require(\"node:path\");\n    const fs = {\n      existsSync,\n      writeFileSync,\n      readFileSync,\n      appendFileSync,\n      mkdirSync,\n      rmSync\n    };\n    const cwd = () => process.cwd();\n    module.exports = { fs, path, cwd };\n  }\n});\nexport default require_safe_node_apis();\n//# sourceMappingURL=safe-node-apis.js.map", "import \"../../chunk-BUSYA2B4.js\";\nimport nodeRuntime from \"#safe-node-apis\";\nconst throwMissingFsModule = (module) => {\n  throw new Error(`Clerk: ${module} is missing. This is an internal error. Please contact Clerk's support.`);\n};\nconst nodeFsOrThrow = () => {\n  if (!nodeRuntime.fs) {\n    throwMissingFsModule(\"fs\");\n  }\n  return nodeRuntime.fs;\n};\nconst nodePathOrThrow = () => {\n  if (!nodeRuntime.path) {\n    throwMissingFsModule(\"path\");\n  }\n  return nodeRuntime.path;\n};\nconst nodeCwdOrThrow = () => {\n  if (!nodeRuntime.cwd) {\n    throwMissingFsModule(\"cwd\");\n  }\n  return nodeRuntime.cwd;\n};\nexport {\n  nodeCwdOrThrow,\n  nodeFsOrThrow,\n  nodePathOrThrow\n};\n//# sourceMappingURL=utils.js.map", "// src/jwt/legacyReturn.ts\nfunction withLegacyReturn(cb) {\n  return async (...args) => {\n    const { data, errors } = await cb(...args);\n    if (errors) {\n      throw errors[0];\n    }\n    return data;\n  };\n}\nfunction withLegacySyncReturn(cb) {\n  return (...args) => {\n    const { data, errors } = cb(...args);\n    if (errors) {\n      throw errors[0];\n    }\n    return data;\n  };\n}\n\nexport {\n  withLegacyReturn,\n  withLegacySyncReturn\n};\n//# sourceMappingURL=chunk-P263NW7Z.mjs.map", "import {\n  isTruthy\n} from \"./chunk-RWYTRAIK.mjs\";\nimport {\n  parsePublishableKey\n} from \"./chunk-G3VP5PJE.mjs\";\nimport {\n  __privateAdd,\n  __privateGet,\n  __privateMethod,\n  __privateSet\n} from \"./chunk-7ELT755Q.mjs\";\n\n// src/telemetry/throttler.ts\nvar DEFAULT_CACHE_TTL_MS = 864e5;\nvar _storageKey, _cacheTtl, _TelemetryEventThrottler_instances, generateKey_fn, cache_get, isValidBrowser_get;\nvar TelemetryEventThrottler = class {\n  constructor() {\n    __privateAdd(this, _TelemetryEventThrottler_instances);\n    __privateAdd(this, _storageKey, \"clerk_telemetry_throttler\");\n    __privateAdd(this, _cacheTtl, DEFAULT_CACHE_TTL_MS);\n  }\n  isEventThrottled(payload) {\n    if (!__privateGet(this, _TelemetryEventThrottler_instances, isValidBrowser_get)) {\n      return false;\n    }\n    const now = Date.now();\n    const key = __privateMethod(this, _TelemetryEventThrottler_instances, generateKey_fn).call(this, payload);\n    const entry = __privateGet(this, _TelemetryEventThrottler_instances, cache_get)?.[key];\n    if (!entry) {\n      const updatedCache = {\n        ...__privateGet(this, _TelemetryEventThrottler_instances, cache_get),\n        [key]: now\n      };\n      localStorage.setItem(__privateGet(this, _storageKey), JSON.stringify(updatedCache));\n    }\n    const shouldInvalidate = entry && now - entry > __privateGet(this, _cacheTtl);\n    if (shouldInvalidate) {\n      const updatedCache = __privateGet(this, _TelemetryEventThrottler_instances, cache_get);\n      delete updatedCache[key];\n      localStorage.setItem(__privateGet(this, _storageKey), JSON.stringify(updatedCache));\n    }\n    return !!entry;\n  }\n};\n_storageKey = new WeakMap();\n_cacheTtl = new WeakMap();\n_TelemetryEventThrottler_instances = new WeakSet();\n/**\n * Generates a consistent unique key for telemetry events by sorting payload properties.\n * This ensures that payloads with identical content in different orders produce the same key.\n */\ngenerateKey_fn = function(event) {\n  const { sk: _sk, pk: _pk, payload, ...rest } = event;\n  const sanitizedEvent = {\n    ...payload,\n    ...rest\n  };\n  return JSON.stringify(\n    Object.keys({\n      ...payload,\n      ...rest\n    }).sort().map((key) => sanitizedEvent[key])\n  );\n};\ncache_get = function() {\n  const cacheString = localStorage.getItem(__privateGet(this, _storageKey));\n  if (!cacheString) {\n    return {};\n  }\n  return JSON.parse(cacheString);\n};\nisValidBrowser_get = function() {\n  if (typeof window === \"undefined\") {\n    return false;\n  }\n  const storage = window.localStorage;\n  if (!storage) {\n    return false;\n  }\n  try {\n    const testKey = \"test\";\n    storage.setItem(testKey, testKey);\n    storage.removeItem(testKey);\n    return true;\n  } catch (err) {\n    const isQuotaExceededError = err instanceof DOMException && // Check error names for different browsers\n    (err.name === \"QuotaExceededError\" || err.name === \"NS_ERROR_DOM_QUOTA_REACHED\");\n    if (isQuotaExceededError && storage.length > 0) {\n      storage.removeItem(__privateGet(this, _storageKey));\n    }\n    return false;\n  }\n};\n\n// src/telemetry/collector.ts\nvar DEFAULT_CONFIG = {\n  samplingRate: 1,\n  maxBufferSize: 5,\n  // Production endpoint: https://clerk-telemetry.com\n  // Staging endpoint: https://staging.clerk-telemetry.com\n  // Local: http://localhost:8787\n  endpoint: \"https://clerk-telemetry.com\"\n};\nvar _config, _eventThrottler, _metadata, _buffer, _pendingFlush, _TelemetryCollector_instances, shouldRecord_fn, shouldBeSampled_fn, scheduleFlush_fn, flush_fn, logEvent_fn, getSDKMetadata_fn, preparePayload_fn;\nvar TelemetryCollector = class {\n  constructor(options) {\n    __privateAdd(this, _TelemetryCollector_instances);\n    __privateAdd(this, _config);\n    __privateAdd(this, _eventThrottler);\n    __privateAdd(this, _metadata, {});\n    __privateAdd(this, _buffer, []);\n    __privateAdd(this, _pendingFlush);\n    __privateSet(this, _config, {\n      maxBufferSize: options.maxBufferSize ?? DEFAULT_CONFIG.maxBufferSize,\n      samplingRate: options.samplingRate ?? DEFAULT_CONFIG.samplingRate,\n      disabled: options.disabled ?? false,\n      debug: options.debug ?? false,\n      endpoint: DEFAULT_CONFIG.endpoint\n    });\n    if (!options.clerkVersion && typeof window === \"undefined\") {\n      __privateGet(this, _metadata).clerkVersion = \"\";\n    } else {\n      __privateGet(this, _metadata).clerkVersion = options.clerkVersion ?? \"\";\n    }\n    __privateGet(this, _metadata).sdk = options.sdk;\n    __privateGet(this, _metadata).sdkVersion = options.sdkVersion;\n    __privateGet(this, _metadata).publishableKey = options.publishableKey ?? \"\";\n    const parsedKey = parsePublishableKey(options.publishableKey);\n    if (parsedKey) {\n      __privateGet(this, _metadata).instanceType = parsedKey.instanceType;\n    }\n    if (options.secretKey) {\n      __privateGet(this, _metadata).secretKey = options.secretKey.substring(0, 16);\n    }\n    __privateSet(this, _eventThrottler, new TelemetryEventThrottler());\n  }\n  get isEnabled() {\n    if (__privateGet(this, _metadata).instanceType !== \"development\") {\n      return false;\n    }\n    if (__privateGet(this, _config).disabled || typeof process !== \"undefined\" && isTruthy(process.env.CLERK_TELEMETRY_DISABLED)) {\n      return false;\n    }\n    if (typeof window !== \"undefined\" && !!window?.navigator?.webdriver) {\n      return false;\n    }\n    return true;\n  }\n  get isDebug() {\n    return __privateGet(this, _config).debug || typeof process !== \"undefined\" && isTruthy(process.env.CLERK_TELEMETRY_DEBUG);\n  }\n  record(event) {\n    const preparedPayload = __privateMethod(this, _TelemetryCollector_instances, preparePayload_fn).call(this, event.event, event.payload);\n    __privateMethod(this, _TelemetryCollector_instances, logEvent_fn).call(this, preparedPayload.event, preparedPayload);\n    if (!__privateMethod(this, _TelemetryCollector_instances, shouldRecord_fn).call(this, preparedPayload, event.eventSamplingRate)) {\n      return;\n    }\n    __privateGet(this, _buffer).push(preparedPayload);\n    __privateMethod(this, _TelemetryCollector_instances, scheduleFlush_fn).call(this);\n  }\n};\n_config = new WeakMap();\n_eventThrottler = new WeakMap();\n_metadata = new WeakMap();\n_buffer = new WeakMap();\n_pendingFlush = new WeakMap();\n_TelemetryCollector_instances = new WeakSet();\nshouldRecord_fn = function(preparedPayload, eventSamplingRate) {\n  return this.isEnabled && !this.isDebug && __privateMethod(this, _TelemetryCollector_instances, shouldBeSampled_fn).call(this, preparedPayload, eventSamplingRate);\n};\nshouldBeSampled_fn = function(preparedPayload, eventSamplingRate) {\n  const randomSeed = Math.random();\n  const toBeSampled = randomSeed <= __privateGet(this, _config).samplingRate && (typeof eventSamplingRate === \"undefined\" || randomSeed <= eventSamplingRate);\n  if (!toBeSampled) {\n    return false;\n  }\n  return !__privateGet(this, _eventThrottler).isEventThrottled(preparedPayload);\n};\nscheduleFlush_fn = function() {\n  if (typeof window === \"undefined\") {\n    __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    return;\n  }\n  const isBufferFull = __privateGet(this, _buffer).length >= __privateGet(this, _config).maxBufferSize;\n  if (isBufferFull) {\n    if (__privateGet(this, _pendingFlush)) {\n      const cancel = typeof cancelIdleCallback !== \"undefined\" ? cancelIdleCallback : clearTimeout;\n      cancel(__privateGet(this, _pendingFlush));\n    }\n    __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    return;\n  }\n  if (__privateGet(this, _pendingFlush)) {\n    return;\n  }\n  if (\"requestIdleCallback\" in window) {\n    __privateSet(this, _pendingFlush, requestIdleCallback(() => {\n      __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    }));\n  } else {\n    __privateSet(this, _pendingFlush, setTimeout(() => {\n      __privateMethod(this, _TelemetryCollector_instances, flush_fn).call(this);\n    }, 0));\n  }\n};\nflush_fn = function() {\n  fetch(new URL(\"/v1/event\", __privateGet(this, _config).endpoint), {\n    method: \"POST\",\n    // TODO: We send an array here with that idea that we can eventually send multiple events.\n    body: JSON.stringify({\n      events: __privateGet(this, _buffer)\n    }),\n    headers: {\n      \"Content-Type\": \"application/json\"\n    }\n  }).catch(() => void 0).then(() => {\n    __privateSet(this, _buffer, []);\n  }).catch(() => void 0);\n};\n/**\n * If running in debug mode, log the event and its payload to the console.\n */\nlogEvent_fn = function(event, payload) {\n  if (!this.isDebug) {\n    return;\n  }\n  if (typeof console.groupCollapsed !== \"undefined\") {\n    console.groupCollapsed(\"[clerk/telemetry]\", event);\n    console.log(payload);\n    console.groupEnd();\n  } else {\n    console.log(\"[clerk/telemetry]\", event, payload);\n  }\n};\n/**\n * If in browser, attempt to lazily grab the SDK metadata from the Clerk singleton, otherwise fallback to the initially passed in values.\n *\n * This is necessary because the sdkMetadata can be set by the host SDK after the TelemetryCollector is instantiated.\n */\ngetSDKMetadata_fn = function() {\n  let sdkMetadata = {\n    name: __privateGet(this, _metadata).sdk,\n    version: __privateGet(this, _metadata).sdkVersion\n  };\n  if (typeof window !== \"undefined\" && window.Clerk) {\n    sdkMetadata = { ...sdkMetadata, ...window.Clerk.constructor.sdkMetadata };\n  }\n  return sdkMetadata;\n};\n/**\n * Append relevant metadata from the Clerk singleton to the event payload.\n */\npreparePayload_fn = function(event, payload) {\n  const sdkMetadata = __privateMethod(this, _TelemetryCollector_instances, getSDKMetadata_fn).call(this);\n  return {\n    event,\n    cv: __privateGet(this, _metadata).clerkVersion ?? \"\",\n    it: __privateGet(this, _metadata).instanceType ?? \"\",\n    sdk: sdkMetadata.name,\n    sdkv: sdkMetadata.version,\n    ...__privateGet(this, _metadata).publishableKey ? { pk: __privateGet(this, _metadata).publishableKey } : {},\n    ...__privateGet(this, _metadata).secretKey ? { sk: __privateGet(this, _metadata).secretKey } : {},\n    payload\n  };\n};\n\n// src/telemetry/events/component-mounted.ts\nvar EVENT_COMPONENT_MOUNTED = \"COMPONENT_MOUNTED\";\nvar EVENT_COMPONENT_OPENED = \"COMPONENT_OPENED\";\nvar EVENT_SAMPLING_RATE = 0.1;\nfunction createPrebuiltComponentEvent(event) {\n  return function(component, props, additionalPayload) {\n    return {\n      event,\n      eventSamplingRate: EVENT_SAMPLING_RATE,\n      payload: {\n        component,\n        appearanceProp: Boolean(props?.appearance),\n        baseTheme: Boolean(props?.appearance?.baseTheme),\n        elements: Boolean(props?.appearance?.elements),\n        variables: Boolean(props?.appearance?.variables),\n        ...additionalPayload\n      }\n    };\n  };\n}\nfunction eventPrebuiltComponentMounted(component, props, additionalPayload) {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_MOUNTED)(component, props, additionalPayload);\n}\nfunction eventPrebuiltComponentOpened(component, props, additionalPayload) {\n  return createPrebuiltComponentEvent(EVENT_COMPONENT_OPENED)(component, props, additionalPayload);\n}\nfunction eventComponentMounted(component, props = {}) {\n  return {\n    event: EVENT_COMPONENT_MOUNTED,\n    eventSamplingRate: EVENT_SAMPLING_RATE,\n    payload: {\n      component,\n      ...props\n    }\n  };\n}\n\n// src/telemetry/events/method-called.ts\nvar EVENT_METHOD_CALLED = \"METHOD_CALLED\";\nfunction eventMethodCalled(method, payload) {\n  return {\n    event: EVENT_METHOD_CALLED,\n    payload: {\n      method,\n      ...payload\n    }\n  };\n}\n\n// src/telemetry/events/framework-metadata.ts\nvar EVENT_FRAMEWORK_METADATA = \"FRAMEWORK_METADATA\";\nvar EVENT_SAMPLING_RATE2 = 0.1;\nfunction eventFrameworkMetadata(payload) {\n  return {\n    event: EVENT_FRAMEWORK_METADATA,\n    eventSamplingRate: EVENT_SAMPLING_RATE2,\n    payload\n  };\n}\n\nexport {\n  TelemetryCollector,\n  eventPrebuiltComponentMounted,\n  eventPrebuiltComponentOpened,\n  eventComponentMounted,\n  eventMethodCalled,\n  eventFrameworkMetadata\n};\n//# sourceMappingURL=chunk-L6WULBPV.mjs.map", "import {\n  TelemetryCollector,\n  eventComponentMounted,\n  eventFrameworkMetadata,\n  eventMethodCalled,\n  eventPrebuiltComponentMounted,\n  eventPrebuiltComponentOpened\n} from \"./chunk-L6WULBPV.mjs\";\nimport \"./chunk-RWYTRAIK.mjs\";\nimport \"./chunk-G3VP5PJE.mjs\";\nimport \"./chunk-TETGTEI2.mjs\";\nimport \"./chunk-KOH7GTJO.mjs\";\nimport \"./chunk-I6MTSTOF.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  TelemetryCollector,\n  eventComponentMounted,\n  eventFrameworkMetadata,\n  eventMethodCalled,\n  eventPrebuiltComponentMounted,\n  eventPrebuiltComponentOpened\n};\n//# sourceMappingURL=telemetry.mjs.map", "import {\n  createAuthenticateRequest,\n  createBackendApiClient,\n  verifyToken\n} from \"./chunk-H5XWF6TY.mjs\";\nimport {\n  withLegacyReturn\n} from \"./chunk-P263NW7Z.mjs\";\nimport \"./chunk-AT3FJU3M.mjs\";\nimport \"./chunk-5JS2VYLU.mjs\";\n\n// src/index.ts\nimport { TelemetryCollector } from \"@clerk/shared/telemetry\";\nvar verifyToken2 = withLegacyReturn(verifyToken);\nfunction createClerkClient(options) {\n  const opts = { ...options };\n  const apiClient = createBackendApiClient(opts);\n  const requestState = createAuthenticateRequest({ options: opts, apiClient });\n  const telemetry = new TelemetryCollector({\n    ...options.telemetry,\n    publishableKey: opts.publishableKey,\n    secretKey: opts.secretKey,\n    samplingRate: 0.1,\n    ...opts.sdkMetadata ? { sdk: opts.sdkMetadata.name, sdkVersion: opts.sdkMetadata.version } : {}\n  });\n  return {\n    ...apiClient,\n    ...requestState,\n    telemetry\n  };\n}\nexport {\n  createClerkClient,\n  verifyToken2 as verifyToken\n};\n//# sourceMappingURL=index.mjs.map", "import \"../chunk-BUSYA2B4.js\";\nimport { createClerkClient } from \"@clerk/backend\";\nimport {\n  API_URL,\n  API_VERSION,\n  DOMAIN,\n  IS_SATELLITE,\n  PROXY_URL,\n  PUBLISHABLE_KEY,\n  SDK_METADATA,\n  SECRET_KEY,\n  TELEMETRY_DEBUG,\n  TELEMETRY_DISABLED\n} from \"./constants\";\nconst clerkClientDefaultOptions = {\n  secretKey: SECRET_KEY,\n  publishableKey: PUBLISHABLE_KEY,\n  apiUrl: API_URL,\n  apiVersion: API_VERSION,\n  userAgent: `${\"@clerk/nextjs\"}@${\"6.12.12\"}`,\n  proxyUrl: PROXY_URL,\n  domain: DOMAIN,\n  isSatellite: IS_SATELLITE,\n  sdkMetadata: SDK_METADATA,\n  telemetry: {\n    disabled: TELEMETRY_DISABLED,\n    debug: TELEMETRY_DEBUG\n  }\n};\nconst createClerkClientWithOptions = (options) => createClerkClient({ ...clerkClientDefaultOptions, ...options });\nexport {\n  createClerkClientWithOptions\n};\n//# sourceMappingURL=createClerkClient.js.map"], "names": [], "sourceRoot": ""}