(()=>{var e={};e.id=4602,e.ids=[4602],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1317:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{w:()=>a,x:()=>c});var o=r(10724),i=r(68117),n=r(32481),u=e([i]);async function a(e){try{let t=e.headers.get("x-clerk-user-id"),r=e.headers.get("x-user-email");if(!t||!r)return console.log("Missing Clerk user headers"),null;let s=await (0,o.nm0)({config:i.A}),u={id:t,email:r};console.log("Syncing Clerk user with Payload:",u);let a=await (0,n.kw)(s,u);return console.log("Synced Payload user:",a),{payload:s,user:a}}catch(e){return console.error("Payload authentication error:",e),null}}async function c(e,t,r,s={}){let{payload:o,user:i}=e;switch(r){case"find":return await o.find({collection:t,user:i,...s});case"findByID":return await o.findByID({collection:t,user:i,...s});case"create":return await o.create({collection:t,user:i,...s});case"update":return await o.update({collection:t,user:i,...s});case"delete":return await o.delete({collection:t,user:i,...s});default:throw Error(`Unsupported operation: ${r}`)}}i=(u.then?(await u)():u)[0],s()}catch(e){s(e)}})},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4984:e=>{"use strict";e.exports=require("readline")},8086:e=>{"use strict";e.exports=require("module")},9288:e=>{"use strict";e.exports=require("sharp")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},32481:(e,t,r)=>{"use strict";async function s(e,t,r="front-desk"){try{let s=await e.find({collection:"users",where:{clerkId:{equals:t.id}},limit:1});if(!(s.docs.length>0))return await e.create({collection:"users",data:{role:r,clerkId:t.id}});{let t=s.docs[0];return await e.update({collection:"users",id:t.id,data:{}})}}catch(e){throw console.error("Error syncing Clerk user with Payload:",e),Error("Failed to sync user authentication")}}r.d(t,{kw:()=>s})},32785:e=>{"use strict";e.exports=import("prettier")},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},34869:()=>{},35672:e=>{"use strict";e.exports=require("dns/promises")},37067:e=>{"use strict";e.exports=require("node:http")},37540:e=>{"use strict";e.exports=require("node:console")},37830:e=>{"use strict";e.exports=require("node:stream/web")},38522:e=>{"use strict";e.exports=require("node:zlib")},40610:e=>{"use strict";e.exports=require("node:dns")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46556:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var o=r(70293),i=r(32498),n=r(83889),u=r(51866),a=e([u]);u=(a.then?(await a)():a)[0];let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/reports/analytics/route",pathname:"/api/reports/analytics",filename:"route",bundlePath:"app/api/reports/analytics/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\reports\\analytics\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:m}=d;function c(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},48161:e=>{"use strict";e.exports=require("node:os")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},51866:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>u});var o=r(27492),i=r(1317),n=e([i]);async function u(e){try{let t=await (0,i.w)(e);if(!t)return o.NextResponse.json({error:"Authentication required"},{status:401});if(!["admin","front-desk"].includes(t.user.role))return o.NextResponse.json({error:"Insufficient permissions to view analytics"},{status:403});let{searchParams:r}=new URL(e.url),s=r.get("startDate"),n=r.get("endDate"),u=r.get("period")||"30",a=n?new Date(n):new Date,c=new Date(s||Date.now()-24*parseInt(u)*36e5),d={createdAt:{greater_than_equal:c.toISOString(),less_than_equal:a.toISOString()}},[p,l,m,x,h]=await Promise.all([(0,i.x)(t,"bills","find",{where:d,limit:1e4,depth:1}),(0,i.x)(t,"payments","find",{where:d,limit:1e4,depth:1}),(0,i.x)(t,"deposits","find",{where:d,limit:1e4,depth:1}),(0,i.x)(t,"appointments","find",{where:d,limit:1e4,depth:1}),(0,i.x)(t,"patients","find",{where:d,limit:1e4})]),g={totalRevenue:l.docs.reduce((e,t)=>e+(t.amount||0),0),averageTransactionValue:l.docs.length>0?l.docs.reduce((e,t)=>e+(t.amount||0),0)/l.docs.length:0,totalBilled:p.docs.reduce((e,t)=>e+(t.totalAmount||0),0),totalOutstanding:p.docs.reduce((e,t)=>e+(t.remainingAmount||0),0),collectionRate:p.docs.reduce((e,t)=>e+(t.totalAmount||0),0)>0?l.docs.reduce((e,t)=>e+(t.amount||0),0)/p.docs.reduce((e,t)=>e+(t.totalAmount||0),0)*100:0},q={};l.docs.forEach(e=>{let t=e.paymentMethod||"unknown";q[t]||(q[t]={count:0,amount:0}),q[t].count++,q[t].amount+=e.amount||0});let y={};p.docs.forEach(e=>{let t=e.status||"unknown";y[t]||(y[t]={count:0,amount:0}),y[t].count++,y[t].amount+=e.totalAmount||0});let f={};p.docs.forEach(e=>{let t=e.billType||"unknown";f[t]||(f[t]={count:0,amount:0}),f[t].count++,f[t].amount+=e.totalAmount||0});let w={totalDeposits:m.docs.reduce((e,t)=>e+(t.amount||0),0),totalUsedDeposits:m.docs.reduce((e,t)=>e+(t.usedAmount||0),0),totalRemainingDeposits:m.docs.reduce((e,t)=>e+(t.remainingAmount||0),0),utilizationRate:m.docs.reduce((e,t)=>e+(t.amount||0),0)>0?m.docs.reduce((e,t)=>e+(t.usedAmount||0),0)/m.docs.reduce((e,t)=>e+(t.amount||0),0)*100:0},v={newPatients:h.docs.filter(e=>"patient"===e.userType).length,consultationUsers:h.docs.filter(e=>"consultation"===e.userType).length,conversionRate:h.docs.filter(e=>"consultation"===e.userType).length>0?h.docs.filter(e=>"converted"===e.status).length/h.docs.filter(e=>"consultation"===e.userType).length*100:0,averageRevenuePerPatient:h.docs.length>0?g.totalRevenue/h.docs.length:0},k={totalAppointments:x.docs.length,consultationAppointments:x.docs.filter(e=>"consultation"===e.appointmentType).length,treatmentAppointments:x.docs.filter(e=>"treatment"===e.appointmentType).length,completedAppointments:x.docs.filter(e=>"completed"===e.status).length,completionRate:x.docs.length>0?x.docs.filter(e=>"completed"===e.status).length/x.docs.length*100:0},A={},D=new Date(c);for(;D<=a;)A[D.toISOString().split("T")[0]]={revenue:0,bills:0,payments:0,appointments:0},D.setDate(D.getDate()+1);l.docs.forEach(e=>{let t=new Date(e.createdAt).toISOString().split("T")[0];A[t]&&(A[t].revenue+=e.amount||0,A[t].payments++)}),p.docs.forEach(e=>{let t=new Date(e.createdAt).toISOString().split("T")[0];A[t]&&A[t].bills++}),x.docs.forEach(e=>{let t=new Date(e.createdAt).toISOString().split("T")[0];A[t]&&A[t].appointments++});let S={highestRevenueDay:Object.entries(A).reduce((e,[t,r])=>(r.revenue||0)>(e.revenue||0)?{date:t,...r}:e,{}),mostActiveDay:Object.entries(A).reduce((e,[t,r])=>(r.appointments||0)>(e.appointments||0)?{date:t,...r}:e,{})};return o.NextResponse.json({period:{startDate:c.toISOString(),endDate:a.toISOString(),days:Math.ceil((a.getTime()-c.getTime())/864e5)},revenue:g,paymentMethods:q,billStatus:y,treatments:f,deposits:w,patients:v,appointments:k,trends:{daily:A,topMetrics:S},summary:{totalTransactions:l.docs.length,totalBills:p.docs.length,totalDeposits:m.docs.length,totalAppointments:x.docs.length,totalPatients:h.docs.length}})}catch(e){return console.error("Error generating analytics:",e),o.NextResponse.json({error:"Failed to generate analytics"},{status:500})}}i=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80099:e=>{"use strict";e.exports=require("node:sqlite")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},93077:()=>{},94735:e=>{"use strict";e.exports=require("events")},98995:e=>{"use strict";e.exports=require("node:module")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[3889,2481,9556,8754],()=>r(46556));module.exports=s})();