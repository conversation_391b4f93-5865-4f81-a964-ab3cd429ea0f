"use strict";exports.id=5140,exports.ids=[5140],exports.modules={55140:(e,t,o)=>{o.d(t,{CognitoIdentityClient:()=>Y.D,GetCredentialsForIdentityCommand:()=>J,GetIdCommand:()=>V});var n=o(38353),a=o(17972),i=o(72734),r=o(3706);class c extends i.ServiceException{constructor(e){super(e),Object.setPrototypeOf(this,c.prototype)}}class s extends c{name="InternalErrorException";$fault="server";constructor(e){super({name:"InternalErrorException",$fault:"server",...e}),Object.setPrototypeOf(this,s.prototype)}}class d extends c{name="InvalidParameterException";$fault="client";constructor(e){super({name:"InvalidParameterException",$fault:"client",...e}),Object.setPrototypeOf(this,d.prototype)}}class p extends c{name="LimitExceededException";$fault="client";constructor(e){super({name:"LimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype)}}class l extends c{name="NotAuthorizedException";$fault="client";constructor(e){super({name:"NotAuthorizedException",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}class u extends c{name="ResourceConflictException";$fault="client";constructor(e){super({name:"ResourceConflictException",$fault:"client",...e}),Object.setPrototypeOf(this,u.prototype)}}class y extends c{name="TooManyRequestsException";$fault="client";constructor(e){super({name:"TooManyRequestsException",$fault:"client",...e}),Object.setPrototypeOf(this,y.prototype)}}class x extends c{name="ResourceNotFoundException";$fault="client";constructor(e){super({name:"ResourceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,x.prototype)}}class m extends c{name="ExternalServiceException";$fault="client";constructor(e){super({name:"ExternalServiceException",$fault:"client",...e}),Object.setPrototypeOf(this,m.prototype)}}class E extends c{name="InvalidIdentityPoolConfigurationException";$fault="client";constructor(e){super({name:"InvalidIdentityPoolConfigurationException",$fault:"client",...e}),Object.setPrototypeOf(this,E.prototype)}}class f extends c{name="DeveloperUserAlreadyRegisteredException";$fault="client";constructor(e){super({name:"DeveloperUserAlreadyRegisteredException",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype)}}class w extends c{name="ConcurrentModificationException";$fault="client";constructor(e){super({name:"ConcurrentModificationException",$fault:"client",...e}),Object.setPrototypeOf(this,w.prototype)}}let I=e=>({...e,...e.Logins&&{Logins:i.SENSITIVE_STRING}}),S=e=>({...e,...e.SecretKey&&{SecretKey:i.SENSITIVE_STRING}}),h=e=>({...e,...e.Credentials&&{Credentials:S(e.Credentials)}}),g=e=>({...e,...e.Logins&&{Logins:i.SENSITIVE_STRING}});var v=o(90841),C=o(96126);let $=async(e,t)=>W(t,k("GetCredentialsForIdentity"),"/",void 0,JSON.stringify((0,i._json)(e))),b=async(e,t)=>W(t,k("GetId"),"/",void 0,JSON.stringify((0,i._json)(e))),j=async(e,t)=>{if(e.statusCode>=300)return z(e,t);let o=await (0,v.Y2)(e.body,t),n={};return n=B(o,t),{$metadata:K(e),...n}},O=async(e,t)=>{if(e.statusCode>=300)return z(e,t);let o=await (0,v.Y2)(e.body,t),n={};return n=(0,i._json)(o),{$metadata:K(e),...n}},z=async(e,t)=>{let o={...e,body:await (0,v.CG)(e.body,t)},n=(0,v.cJ)(e,o.body);switch(n){case"InternalErrorException":case"com.amazonaws.cognitoidentity#InternalErrorException":throw await _(o,t);case"InvalidParameterException":case"com.amazonaws.cognitoidentity#InvalidParameterException":throw await A(o,t);case"LimitExceededException":case"com.amazonaws.cognitoidentity#LimitExceededException":throw await G(o,t);case"NotAuthorizedException":case"com.amazonaws.cognitoidentity#NotAuthorizedException":throw await q(o,t);case"ResourceConflictException":case"com.amazonaws.cognitoidentity#ResourceConflictException":throw await F(o,t);case"TooManyRequestsException":case"com.amazonaws.cognitoidentity#TooManyRequestsException":throw await M(o,t);case"ResourceNotFoundException":case"com.amazonaws.cognitoidentity#ResourceNotFoundException":throw await L(o,t);case"ExternalServiceException":case"com.amazonaws.cognitoidentity#ExternalServiceException":throw await N(o,t);case"InvalidIdentityPoolConfigurationException":case"com.amazonaws.cognitoidentity#InvalidIdentityPoolConfigurationException":throw await T(o,t);case"DeveloperUserAlreadyRegisteredException":case"com.amazonaws.cognitoidentity#DeveloperUserAlreadyRegisteredException":throw await R(o,t);case"ConcurrentModificationException":case"com.amazonaws.cognitoidentity#ConcurrentModificationException":throw await P(o,t);default:return U({output:e,parsedBody:o.body,errorCode:n})}},P=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new w({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},R=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new f({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},N=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new m({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},_=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new s({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},T=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new E({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},A=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new d({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},G=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new p({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},q=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new l({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},F=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new u({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},L=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new x({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},M=async(e,t)=>{let o=e.body,n=(0,i._json)(o),a=new y({$metadata:K(e),...n});return(0,i.decorateServiceException)(a,o)},D=(e,t)=>(0,i.take)(e,{AccessKeyId:i.expectString,Expiration:e=>(0,i.expectNonNull)((0,i.parseEpochTimestamp)((0,i.expectNumber)(e))),SecretKey:i.expectString,SessionToken:i.expectString}),B=(e,t)=>(0,i.take)(e,{Credentials:e=>D(e,t),IdentityId:i.expectString}),K=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),U=(0,i.withBaseException)(c),W=async(e,t,o,n,a)=>{let{hostname:i,protocol:r="https",port:c,path:s}=await e.endpoint(),d={protocol:r,hostname:i,port:c,method:"POST",path:s.endsWith("/")?s.slice(0,-1)+o:s+o,headers:t};return void 0!==n&&(d.hostname=n),void 0!==a&&(d.body=a),new C.HttpRequest(d)};function k(e){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`AWSCognitoIdentityService.${e}`}}class J extends i.Command.classBuilder().ep(r.S).m(function(e,t,o,i){return[(0,a.getSerdePlugin)(o,this.serialize,this.deserialize),(0,n.rD)(o,e.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetCredentialsForIdentity",{}).n("CognitoIdentityClient","GetCredentialsForIdentityCommand").f(I,h).ser($).de(j).build(){}class V extends i.Command.classBuilder().ep(r.S).m(function(e,t,o,i){return[(0,a.getSerdePlugin)(o,this.serialize,this.deserialize),(0,n.rD)(o,e.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetId",{}).n("CognitoIdentityClient","GetIdCommand").f(g,void 0).ser(b).de(O).build(){}var Y=o(12057)}};