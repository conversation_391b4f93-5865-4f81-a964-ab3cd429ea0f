try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="3e1d3fdf-f377-4774-ad30-85d76a04e5fe",e._sentryDebugIdIdentifier="sentry-dbid-3e1d3fdf-f377-4774-ad30-85d76a04e5fe")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8308],{39552:(e,t,r)=>{"use strict";r.d(t,{s:()=>o,t:()=>s});var n=r(99004);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function o(...e){return n.useCallback(s(...e),e)}},40548:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>i,TN:()=>d,XL:()=>l});var n=r(52880);r(99004);var a=r(85017),s=r(54651);let o=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,s.cn)(o({variant:r}),t),...a,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"alert-title",className:(0,s.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"alert-description",className:(0,s.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},47211:(e,t,r)=>{Promise.resolve().then(r.bind(r,93299))},49202:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(99004),a={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let s=(e,t,r,s)=>{let o=(0,n.forwardRef)((r,o)=>{let{color:i="currentColor",size:l=24,stroke:d=2,title:c,className:u,children:f,...m}=r;return(0,n.createElement)("svg",{ref:o,...a[e],width:l,height:l,className:["tabler-icon","tabler-icon-".concat(t),u].join(" "),..."filled"===e?{fill:i}:{strokeWidth:d,stroke:i},...m},[c&&(0,n.createElement)("title",{key:"svg-title"},c),...s.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(f)?f:[f]])});return o.displayName="".concat(r),o}},50516:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o,xV:()=>l});var n=r(99004),a=r(39552),s=r(52880),o=n.forwardRef((e,t)=>{let{children:r,...a}=e,o=n.Children.toArray(r),l=o.find(d);if(l){let e=l.props.children,r=o.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(i,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,s.jsx)(i,{...a,ref:t,children:r})});o.displayName="Slot";var i=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let a=e[n],s=t[n];/^on[A-Z]/.test(n)?a&&s?r[n]=(...e)=>{s(...e),a(...e)}:a&&(r[n]=a):"style"===n?r[n]={...a,...s}:"className"===n&&(r[n]=[a,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,a.t)(t,e):e),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});i.displayName="SlotClone";var l=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===l}},54651:(e,t,r)=>{"use strict";r.d(t,{Jv:()=>d,cn:()=>s,fw:()=>l,r6:()=>i,z3:()=>o});var n=r(97921),a=r(56309);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function o(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:s="normal"}=n;if(0===e)return"0 Byte";let o=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,o)).toFixed(a)," ").concat("accurate"===s?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][o])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][o])?r:"Bytes")}function i(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function l(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let r=Math.floor(t/60);if(r<60)return"".concat(r,"分钟前");let n=Math.floor(r/60);if(n<24)return"".concat(n,"小时前");let a=Math.floor(n/24);if(a<7)return"".concat(a,"天前");let s=Math.floor(a/7);if(s<4)return"".concat(s,"周前");let o=Math.floor(a/30);if(o<12)return"".concat(o,"个月前");let i=Math.floor(a/365);return"".concat(i,"年前")}function d(e){return("string"==typeof e?new Date(e):e)<new Date}},62054:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>i});var n=r(52880);r(99004);var a=r(50516),s=r(85017),o=r(54651);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:s,asChild:l=!1,...d}=e,c=l?a.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:s,className:t})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},85017:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(97921);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:i}=t,l=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let s=a(t)||a(n);return o[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return s(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...d}[t]):({...i,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},86540:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,X9:()=>d,ZB:()=>i,Zp:()=>s,aR:()=>o,wL:()=>u});var n=r(52880);r(99004);var a=r(54651);function s(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-action",className:(0,a.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...r,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function u(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},92708:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var n=(0,r(49202).A)("outline","alert-circle","IconAlertCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])},93299:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(52880),a=r(40548),s=r(62054),o=r(86540),i=r(92708),l=r(95181),d=r(99004),c=r(63610);function u(e){let{error:t,reset:r}=e,u=(0,l.useRouter)(),[f,m]=(0,d.useTransition)();(0,d.useEffect)(()=>{c.Cp(t)},[t]);let p=()=>{m(()=>{u.refresh(),r()})};return(0,n.jsxs)(o.Zp,{className:"border-red-500","data-sentry-element":"Card","data-sentry-component":"StatsError","data-sentry-source-file":"error.tsx",children:[(0,n.jsx)(o.aR,{className:"flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row","data-sentry-element":"CardHeader","data-sentry-source-file":"error.tsx",children:(0,n.jsx)("div",{className:"flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6",children:(0,n.jsxs)(a.Fc,{variant:"destructive",className:"border-none","data-sentry-element":"Alert","data-sentry-source-file":"error.tsx",children:[(0,n.jsx)(i.A,{className:"h-4 w-4","data-sentry-element":"IconAlertCircle","data-sentry-source-file":"error.tsx"}),(0,n.jsx)(a.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,n.jsxs)(a.TN,{className:"mt-2","data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load statistics: ",t.message]})]})})}),(0,n.jsx)(o.Wu,{className:"flex h-[316px] items-center justify-center p-6","data-sentry-element":"CardContent","data-sentry-source-file":"error.tsx",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-muted-foreground mb-4 text-sm",children:"Unable to display statistics at this time"}),(0,n.jsx)(s.$,{onClick:()=>p(),variant:"outline",className:"min-w-[120px]",disabled:f,"data-sentry-element":"Button","data-sentry-source-file":"error.tsx",children:"Try again"})]})})]})}},95181:(e,t,r)=>{"use strict";var n=r(4377);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}}),r.o(n,"useSelectedLayoutSegments")&&r.d(t,{useSelectedLayoutSegments:function(){return n.useSelectedLayoutSegments}})}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,9442,4579,9253,7358],()=>t(47211)),_N_E=e.O()}]);