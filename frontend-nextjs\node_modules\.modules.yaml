hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.4.3':
    '@adobe/css-tools': public
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': public
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': public
  '@asamuzakjp/css-color@3.2.0':
    '@asamuzakjp/css-color': public
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': public
  '@babel/compat-data@7.27.2':
    '@babel/compat-data': public
  '@babel/core@7.27.1':
    '@babel/core': public
  '@babel/core@7.28.0':
    '@babel/core': public
  '@babel/generator@7.27.1':
    '@babel/generator': public
  '@babel/generator@7.28.0':
    '@babel/generator': public
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': public
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': public
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': public
  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-module-transforms': public
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': public
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': public
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': public
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': public
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': public
  '@babel/helpers@7.27.1':
    '@babel/helpers': public
  '@babel/helpers@7.27.6':
    '@babel/helpers': public
  '@babel/parser@7.27.2':
    '@babel/parser': public
  '@babel/parser@7.28.0':
    '@babel/parser': public
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': public
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': public
  '@babel/runtime@7.27.0':
    '@babel/runtime': public
  '@babel/template@7.27.2':
    '@babel/template': public
  '@babel/traverse@7.27.1':
    '@babel/traverse': public
  '@babel/traverse@7.28.0':
    '@babel/traverse': public
  '@babel/types@7.27.1':
    '@babel/types': public
  '@babel/types@7.28.0':
    '@babel/types': public
  '@bundled-es-modules/cookie@2.0.1':
    '@bundled-es-modules/cookie': public
  '@bundled-es-modules/statuses@1.0.1':
    '@bundled-es-modules/statuses': public
  '@bundled-es-modules/tough-cookie@0.1.6':
    '@bundled-es-modules/tough-cookie': public
  '@clerk/backend@1.25.8(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@clerk/backend': public
  '@clerk/clerk-react@5.25.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@clerk/clerk-react': public
  '@clerk/shared@3.2.3(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@clerk/shared': public
  '@clerk/types@4.50.1':
    '@clerk/types': public
  '@csstools/color-helpers@5.0.2':
    '@csstools/color-helpers': public
  '@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-calc': public
  '@csstools/css-color-parser@3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-color-parser': public
  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-parser-algorithms': public
  '@csstools/css-tokenizer@3.0.4':
    '@csstools/css-tokenizer': public
  '@dnd-kit/accessibility@3.1.1(react@19.0.0)':
    '@dnd-kit/accessibility': public
  '@esbuild/win32-x64@0.25.6':
    '@esbuild/win32-x64': public
  '@eslint-community/eslint-utils@4.5.1(eslint@8.48.0)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': public
  '@eslint/js@8.48.0':
    '@eslint/js': public
  '@fastify/otel@https://codeload.github.com/getsentry/fastify-otel/tar.gz/ae3088d65e286bdc94ac5d722573537d6a6671bb(@opentelemetry/api@1.9.0)':
    '@fastify/otel': public
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': public
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': public
  '@floating-ui/react-dom@2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@floating-ui/react-dom': public
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': public
  '@humanwhocodes/config-array@0.11.14':
    '@humanwhocodes/config-array': public
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': public
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': public
  '@img/sharp-win32-x64@0.33.5':
    '@img/sharp-win32-x64': public
  '@inquirer/confirm@5.1.13(@types/node@22.10.2)':
    '@inquirer/confirm': public
  '@inquirer/core@10.1.14(@types/node@22.10.2)':
    '@inquirer/core': public
  '@inquirer/figures@1.0.12':
    '@inquirer/figures': public
  '@inquirer/type@3.0.7(@types/node@22.10.2)':
    '@inquirer/type': public
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': public
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': public
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': public
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': public
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': public
  '@mswjs/interceptors@0.39.2':
    '@mswjs/interceptors': public
  '@next/env@15.3.2':
    '@next/env': public
  '@next/eslint-plugin-next@15.1.0':
    '@next/eslint-plugin-next': public
  '@next/swc-win32-x64-msvc@15.3.2':
    '@next/swc-win32-x64-msvc': public
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': public
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': public
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': public
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': public
  '@open-draft/deferred-promise@2.2.0':
    '@open-draft/deferred-promise': public
  '@open-draft/logger@0.3.0':
    '@open-draft/logger': public
  '@open-draft/until@2.1.0':
    '@open-draft/until': public
  '@opentelemetry/api-logs@0.57.2':
    '@opentelemetry/api-logs': public
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': public
  '@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/context-async-hooks': public
  '@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/core': public
  '@opentelemetry/instrumentation-amqplib@0.46.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-amqplib': public
  '@opentelemetry/instrumentation-connect@0.43.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-connect': public
  '@opentelemetry/instrumentation-dataloader@0.16.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-dataloader': public
  '@opentelemetry/instrumentation-express@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-express': public
  '@opentelemetry/instrumentation-fs@0.19.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-fs': public
  '@opentelemetry/instrumentation-generic-pool@0.43.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-generic-pool': public
  '@opentelemetry/instrumentation-graphql@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-graphql': public
  '@opentelemetry/instrumentation-hapi@0.45.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-hapi': public
  '@opentelemetry/instrumentation-http@0.57.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-http': public
  '@opentelemetry/instrumentation-ioredis@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-ioredis': public
  '@opentelemetry/instrumentation-kafkajs@0.7.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-kafkajs': public
  '@opentelemetry/instrumentation-knex@0.44.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-knex': public
  '@opentelemetry/instrumentation-koa@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-koa': public
  '@opentelemetry/instrumentation-lru-memoizer@0.44.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-lru-memoizer': public
  '@opentelemetry/instrumentation-mongodb@0.52.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongodb': public
  '@opentelemetry/instrumentation-mongoose@0.46.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongoose': public
  '@opentelemetry/instrumentation-mysql2@0.45.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql2': public
  '@opentelemetry/instrumentation-mysql@0.45.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql': public
  '@opentelemetry/instrumentation-pg@0.51.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-pg': public
  '@opentelemetry/instrumentation-redis-4@0.46.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-redis-4': public
  '@opentelemetry/instrumentation-tedious@0.18.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-tedious': public
  '@opentelemetry/instrumentation-undici@0.10.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-undici': public
  '@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation': public
  '@opentelemetry/redis-common@0.36.2':
    '@opentelemetry/redis-common': public
  '@opentelemetry/resources@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resources': public
  '@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-trace-base': public
  '@opentelemetry/semantic-conventions@1.33.0':
    '@opentelemetry/semantic-conventions': public
  '@opentelemetry/sql-common@0.40.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sql-common': public
  '@popperjs/core@2.11.8':
    '@popperjs/core': public
  '@prisma/instrumentation@6.7.0(@opentelemetry/api@1.9.0)':
    '@prisma/instrumentation': public
  '@radix-ui/number@1.1.0':
    '@radix-ui/number': public
  '@radix-ui/primitive@1.1.1':
    '@radix-ui/primitive': public
  '@radix-ui/react-arrow@1.1.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-arrow': public
  '@radix-ui/react-collection@1.1.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-collection': public
  '@radix-ui/react-compose-refs@1.1.1(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-compose-refs': public
  '@radix-ui/react-context@1.1.1(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-context': public
  '@radix-ui/react-direction@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-direction': public
  '@radix-ui/react-dismissable-layer@1.1.5(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-dismissable-layer': public
  '@radix-ui/react-focus-guards@1.1.1(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-focus-guards': public
  '@radix-ui/react-focus-scope@1.1.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-focus-scope': public
  '@radix-ui/react-id@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-id': public
  '@radix-ui/react-menu@2.1.6(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-menu': public
  '@radix-ui/react-popper@1.2.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-popper': public
  '@radix-ui/react-portal@1.1.4(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-portal': public
  '@radix-ui/react-presence@1.1.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-presence': public
  '@radix-ui/react-primitive@2.0.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-primitive': public
  '@radix-ui/react-roving-focus@1.1.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-roving-focus': public
  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-callback-ref': public
  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-controllable-state': public
  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-escape-keydown': public
  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-layout-effect': public
  '@radix-ui/react-use-previous@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-previous': public
  '@radix-ui/react-use-rect@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-rect': public
  '@radix-ui/react-use-size@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-size': public
  '@radix-ui/react-visually-hidden@1.1.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-visually-hidden': public
  '@radix-ui/rect@1.1.0':
    '@radix-ui/rect': public
  '@reach/observe-rect@1.2.0':
    '@reach/observe-rect': public
  '@restart/hooks@0.4.16(react@19.0.0)':
    '@restart/hooks': public
  '@rolldown/pluginutils@1.0.0-beta.19':
    '@rolldown/pluginutils': public
  '@rollup/plugin-commonjs@28.0.1(rollup@4.35.0)':
    '@rollup/plugin-commonjs': public
  '@rollup/pluginutils@5.1.4(rollup@4.35.0)':
    '@rollup/pluginutils': public
  '@rollup/rollup-win32-x64-msvc@4.35.0':
    '@rollup/rollup-win32-x64-msvc': public
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': public
  '@rushstack/eslint-patch@1.11.0':
    '@rushstack/eslint-patch': public
  '@sentry-internal/browser-utils@9.19.0':
    '@sentry-internal/browser-utils': public
  '@sentry-internal/feedback@9.19.0':
    '@sentry-internal/feedback': public
  '@sentry-internal/replay-canvas@9.19.0':
    '@sentry-internal/replay-canvas': public
  '@sentry-internal/replay@9.19.0':
    '@sentry-internal/replay': public
  '@sentry/babel-plugin-component-annotate@3.3.1':
    '@sentry/babel-plugin-component-annotate': public
  '@sentry/browser@9.19.0':
    '@sentry/browser': public
  '@sentry/bundler-plugin-core@3.3.1':
    '@sentry/bundler-plugin-core': public
  '@sentry/cli-win32-x64@2.42.2':
    '@sentry/cli-win32-x64': public
  '@sentry/cli@2.42.2':
    '@sentry/cli': public
  '@sentry/core@9.19.0':
    '@sentry/core': public
  '@sentry/node@9.19.0':
    '@sentry/node': public
  '@sentry/opentelemetry@9.19.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.33.0)':
    '@sentry/opentelemetry': public
  '@sentry/react@9.19.0(react@19.0.0)':
    '@sentry/react': public
  '@sentry/vercel-edge@9.19.0':
    '@sentry/vercel-edge': public
  '@sentry/webpack-plugin@3.3.1(webpack@5.99.8)':
    '@sentry/webpack-plugin': public
  '@swc/counter@0.1.3':
    '@swc/counter': public
  '@swc/helpers@0.5.15':
    '@swc/helpers': public
  '@tabler/icons@3.31.0':
    '@tabler/icons': public
  '@tailwindcss/node@4.0.17':
    '@tailwindcss/node': public
  '@tailwindcss/oxide-win32-x64-msvc@4.0.17':
    '@tailwindcss/oxide-win32-x64-msvc': public
  '@tailwindcss/oxide@4.0.17':
    '@tailwindcss/oxide': public
  '@tanstack/query-core@5.82.0':
    '@tanstack/query-core': public
  '@tanstack/table-core@8.21.2':
    '@tanstack/table-core': public
  '@testing-library/dom@10.4.0':
    '@testing-library/dom': public
  '@types/aria-query@5.0.4':
    '@types/aria-query': public
  '@types/babel__core@7.20.5':
    '@types/babel__core': public
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': public
  '@types/babel__template@7.4.4':
    '@types/babel__template': public
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': public
  '@types/chai@5.2.2':
    '@types/chai': public
  '@types/connect@3.4.38':
    '@types/connect': public
  '@types/cookie@0.6.0':
    '@types/cookie': public
  '@types/d3-array@3.2.1':
    '@types/d3-array': public
  '@types/d3-color@3.1.3':
    '@types/d3-color': public
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': public
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': public
  '@types/d3-path@3.1.1':
    '@types/d3-path': public
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': public
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': public
  '@types/d3-time@3.0.4':
    '@types/d3-time': public
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': public
  '@types/date-arithmetic@4.1.4':
    '@types/date-arithmetic': public
  '@types/deep-eql@4.0.2':
    '@types/deep-eql': public
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': public
  '@types/eslint@9.6.1':
    '@types/eslint': public
  '@types/estree@1.0.6':
    '@types/estree': public
  '@types/json-schema@7.0.15':
    '@types/json-schema': public
  '@types/json5@0.0.29':
    '@types/json5': public
  '@types/mysql@2.15.26':
    '@types/mysql': public
  '@types/pg-pool@2.0.6':
    '@types/pg-pool': public
  '@types/pg@8.6.1':
    '@types/pg': public
  '@types/prop-types@15.7.15':
    '@types/prop-types': public
  '@types/semver@7.7.0':
    '@types/semver': public
  '@types/shimmer@1.2.0':
    '@types/shimmer': public
  '@types/statuses@2.0.6':
    '@types/statuses': public
  '@types/tedious@4.0.14':
    '@types/tedious': public
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': public
  '@types/warning@3.0.3':
    '@types/warning': public
  '@typescript-eslint/parser@8.28.0(eslint@8.48.0)(typescript@5.7.2)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@6.21.0(eslint@8.48.0)(typescript@5.7.2)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.28.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.7.2)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/typescript-estree@8.28.0(typescript@5.7.2)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@6.21.0(eslint@8.48.0)(typescript@5.7.2)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': public
  '@unrs/resolver-binding-win32-x64-msvc@1.3.2':
    '@unrs/resolver-binding-win32-x64-msvc': public
  '@vitest/expect@3.2.4':
    '@vitest/expect': public
  '@vitest/mocker@3.2.4(msw@2.10.3(@types/node@22.10.2)(typescript@5.7.2))(vite@7.0.3(@types/node@22.10.2)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.2)(yaml@2.7.0))':
    '@vitest/mocker': public
  '@vitest/pretty-format@3.2.4':
    '@vitest/pretty-format': public
  '@vitest/runner@3.2.4':
    '@vitest/runner': public
  '@vitest/snapshot@3.2.4':
    '@vitest/snapshot': public
  '@vitest/spy@3.2.4':
    '@vitest/spy': public
  '@vitest/utils@3.2.4':
    '@vitest/utils': public
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': public
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': public
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': public
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': public
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': public
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': public
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': public
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': public
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': public
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': public
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': public
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': public
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': public
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': public
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': public
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': public
  '@xtuc/long@4.2.2':
    '@xtuc/long': public
  acorn-import-attributes@1.9.5(acorn@8.14.1):
    acorn-import-attributes: public
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: public
  acorn@8.14.1:
    acorn: public
  agent-base@6.0.2:
    agent-base: public
  agent-base@7.1.4:
    agent-base: public
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: public
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: public
  ajv@6.12.6:
    ajv: public
  ansi-escapes@4.3.2:
    ansi-escapes: public
  ansi-escapes@7.0.0:
    ansi-escapes: public
  ansi-regex@5.0.1:
    ansi-regex: public
  ansi-styles@4.3.0:
    ansi-styles: public
  anymatch@3.1.3:
    anymatch: public
  argparse@2.0.1:
    argparse: public
  aria-hidden@1.2.4:
    aria-hidden: public
  aria-query@5.3.2:
    aria-query: public
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: public
  array-includes@3.1.8:
    array-includes: public
  array-union@2.1.0:
    array-union: public
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: public
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: public
  array.prototype.flat@1.3.3:
    array.prototype.flat: public
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: public
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: public
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: public
  assertion-error@2.0.1:
    assertion-error: public
  ast-types-flow@0.0.8:
    ast-types-flow: public
  async-function@1.0.0:
    async-function: public
  attr-accept@2.2.5:
    attr-accept: public
  available-typed-arrays@1.0.7:
    available-typed-arrays: public
  axe-core@4.10.3:
    axe-core: public
  axobject-query@4.1.0:
    axobject-query: public
  balanced-match@1.0.2:
    balanced-match: public
  binary-extensions@2.3.0:
    binary-extensions: public
  brace-expansion@1.1.11:
    brace-expansion: public
  braces@3.0.3:
    braces: public
  browserslist@4.24.5:
    browserslist: public
  buffer-from@1.1.2:
    buffer-from: public
  busboy@1.6.0:
    busboy: public
  cac@6.7.14:
    cac: public
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: public
  call-bind@1.0.8:
    call-bind: public
  call-bound@1.0.4:
    call-bound: public
  callsites@3.1.0:
    callsites: public
  caniuse-lite@1.0.30001718:
    caniuse-lite: public
  chai@5.2.1:
    chai: public
  chalk@3.0.0:
    chalk: public
  check-error@2.1.1:
    check-error: public
  chokidar@3.6.0:
    chokidar: public
  chrome-trace-event@1.0.4:
    chrome-trace-event: public
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: public
  cli-cursor@5.0.0:
    cli-cursor: public
  cli-truncate@4.0.0:
    cli-truncate: public
  cli-width@4.1.0:
    cli-width: public
  client-only@0.0.1:
    client-only: public
  cliui@8.0.1:
    cliui: public
  color-convert@2.0.1:
    color-convert: public
  color-name@1.1.4:
    color-name: public
  color-string@1.9.1:
    color-string: public
  color@4.2.3:
    color: public
  colorette@2.0.20:
    colorette: public
  commander@13.1.0:
    commander: public
  commondir@1.0.1:
    commondir: public
  concat-map@0.0.1:
    concat-map: public
  convert-source-map@2.0.0:
    convert-source-map: public
  cookie@0.7.2:
    cookie: public
  cookie@1.0.2:
    cookie: public
  cross-spawn@7.0.6:
    cross-spawn: public
  css-mediaquery@0.1.2:
    css-mediaquery: public
  css.escape@1.5.1:
    css.escape: public
  cssstyle@4.6.0:
    cssstyle: public
  csstype@3.1.3:
    csstype: public
  d3-array@3.2.4:
    d3-array: public
  d3-color@3.1.0:
    d3-color: public
  d3-ease@3.0.1:
    d3-ease: public
  d3-format@3.1.0:
    d3-format: public
  d3-interpolate@3.0.1:
    d3-interpolate: public
  d3-path@3.1.0:
    d3-path: public
  d3-scale@4.0.2:
    d3-scale: public
  d3-shape@3.2.0:
    d3-shape: public
  d3-time-format@4.1.0:
    d3-time-format: public
  d3-time@3.1.0:
    d3-time: public
  d3-timer@3.0.1:
    d3-timer: public
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: public
  data-urls@5.0.0:
    data-urls: public
  data-view-buffer@1.0.2:
    data-view-buffer: public
  data-view-byte-length@1.0.2:
    data-view-byte-length: public
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: public
  date-arithmetic@4.1.0:
    date-arithmetic: public
  dayjs@1.11.13:
    dayjs: public
  debug@4.4.0:
    debug: public
  decimal.js-light@2.5.1:
    decimal.js-light: public
  decimal.js@10.6.0:
    decimal.js: public
  deep-eql@5.0.2:
    deep-eql: public
  deep-is@0.1.4:
    deep-is: public
  define-data-property@1.1.4:
    define-data-property: public
  define-properties@1.2.1:
    define-properties: public
  dequal@2.0.3:
    dequal: public
  detect-libc@2.0.3:
    detect-libc: public
  detect-node-es@1.1.0:
    detect-node-es: public
  dir-glob@3.0.1:
    dir-glob: public
  doctrine@3.0.0:
    doctrine: public
  dom-accessibility-api@0.6.3:
    dom-accessibility-api: public
  dom-helpers@5.2.1:
    dom-helpers: public
  dot-case@3.0.4:
    dot-case: public
  dotenv@16.5.0:
    dotenv: public
  dunder-proto@1.0.1:
    dunder-proto: public
  electron-to-chromium@1.5.155:
    electron-to-chromium: public
  emoji-regex@9.2.2:
    emoji-regex: public
  enhanced-resolve@5.18.1:
    enhanced-resolve: public
  entities@6.0.1:
    entities: public
  environment@1.1.0:
    environment: public
  es-abstract@1.23.9:
    es-abstract: public
  es-define-property@1.0.1:
    es-define-property: public
  es-errors@1.3.0:
    es-errors: public
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: public
  es-module-lexer@1.7.0:
    es-module-lexer: public
  es-object-atoms@1.1.1:
    es-object-atoms: public
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: public
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: public
  es-to-primitive@1.3.0:
    es-to-primitive: public
  esbuild@0.25.6:
    esbuild: public
  escalade@3.2.0:
    escalade: public
  escape-string-regexp@4.0.0:
    escape-string-regexp: public
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-import-resolver-typescript@3.10.0(eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.28.0(eslint@8.48.0)(typescript@5.7.2))(eslint@8.48.0))(eslint@8.48.0):
    eslint-import-resolver-typescript: public
  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.28.0(eslint@8.48.0)(typescript@5.7.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.0(eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.28.0(eslint@8.48.0)(typescript@5.7.2))(eslint@8.48.0))(eslint@8.48.0))(eslint@8.48.0):
    eslint-module-utils: public
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.28.0(eslint@8.48.0)(typescript@5.7.2))(eslint-import-resolver-typescript@3.10.0(eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.28.0(eslint@8.48.0)(typescript@5.7.2))(eslint@8.48.0))(eslint@8.48.0))(eslint@8.48.0):
    eslint-plugin-import: public
  eslint-plugin-jsx-a11y@6.10.2(eslint@8.48.0):
    eslint-plugin-jsx-a11y: public
  eslint-plugin-react-hooks@5.2.0(eslint@8.48.0):
    eslint-plugin-react-hooks: public
  eslint-plugin-react@7.37.4(eslint@8.48.0):
    eslint-plugin-react: public
  eslint-scope@7.2.2:
    eslint-scope: public
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: public
  espree@9.6.1:
    espree: public
  esquery@1.6.0:
    esquery: public
  esrecurse@4.3.0:
    esrecurse: public
  estraverse@5.3.0:
    estraverse: public
  estree-walker@2.0.2:
    estree-walker: public
  esutils@2.0.3:
    esutils: public
  eventemitter3@4.0.7:
    eventemitter3: public
  events@3.3.0:
    events: public
  execa@8.0.1:
    execa: public
  expect-type@1.2.2:
    expect-type: public
  fast-deep-equal@3.1.3:
    fast-deep-equal: public
  fast-equals@2.0.4:
    fast-equals: public
  fast-glob@3.3.1:
    fast-glob: public
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: public
  fast-levenshtein@2.0.6:
    fast-levenshtein: public
  fast-uri@3.0.6:
    fast-uri: public
  fastq@1.19.1:
    fastq: public
  fdir@6.4.3(picomatch@4.0.2):
    fdir: public
  fdir@6.4.6(picomatch@4.0.2):
    fdir: public
  file-entry-cache@6.0.1:
    file-entry-cache: public
  file-selector@2.1.2:
    file-selector: public
  fill-range@7.1.1:
    fill-range: public
  find-up@5.0.0:
    find-up: public
  flat-cache@3.2.0:
    flat-cache: public
  flatted@3.3.3:
    flatted: public
  for-each@0.3.5:
    for-each: public
  forwarded-parse@2.1.2:
    forwarded-parse: public
  framer-motion@11.18.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    framer-motion: public
  fs.realpath@1.0.0:
    fs.realpath: public
  function-bind@1.1.2:
    function-bind: public
  function.prototype.name@1.1.8:
    function.prototype.name: public
  functions-have-names@1.2.3:
    functions-have-names: public
  fuse.js@6.6.2:
    fuse.js: public
  gensync@1.0.0-beta.2:
    gensync: public
  get-caller-file@2.0.5:
    get-caller-file: public
  get-east-asian-width@1.3.0:
    get-east-asian-width: public
  get-intrinsic@1.3.0:
    get-intrinsic: public
  get-nonce@1.0.1:
    get-nonce: public
  get-proto@1.0.1:
    get-proto: public
  get-stream@8.0.1:
    get-stream: public
  get-symbol-description@1.1.0:
    get-symbol-description: public
  get-tsconfig@4.10.0:
    get-tsconfig: public
  glob-parent@6.0.2:
    glob-parent: public
  glob-to-regexp@0.4.1:
    glob-to-regexp: public
  glob@9.3.5:
    glob: public
  globalize@0.1.1:
    globalize: public
  globals@13.24.0:
    globals: public
  globalthis@1.0.4:
    globalthis: public
  globby@11.1.0:
    globby: public
  gopd@1.2.0:
    gopd: public
  graceful-fs@4.2.11:
    graceful-fs: public
  graphemer@1.4.0:
    graphemer: public
  graphql@16.11.0:
    graphql: public
  has-bigints@1.1.0:
    has-bigints: public
  has-flag@4.0.0:
    has-flag: public
  has-property-descriptors@1.0.2:
    has-property-descriptors: public
  has-proto@1.2.0:
    has-proto: public
  has-symbols@1.1.0:
    has-symbols: public
  has-tostringtag@1.0.2:
    has-tostringtag: public
  hasown@2.0.2:
    hasown: public
  headers-polyfill@4.0.3:
    headers-polyfill: public
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: public
  html-encoding-sniffer@4.0.0:
    html-encoding-sniffer: public
  http-proxy-agent@7.0.2:
    http-proxy-agent: public
  https-proxy-agent@5.0.1:
    https-proxy-agent: public
  https-proxy-agent@7.0.6:
    https-proxy-agent: public
  human-signals@5.0.0:
    human-signals: public
  hyphenate-style-name@1.1.0:
    hyphenate-style-name: public
  iconv-lite@0.6.3:
    iconv-lite: public
  ignore@5.3.2:
    ignore: public
  import-fresh@3.3.1:
    import-fresh: public
  import-in-the-middle@1.13.2:
    import-in-the-middle: public
  imurmurhash@0.1.4:
    imurmurhash: public
  indent-string@4.0.0:
    indent-string: public
  inflight@1.0.6:
    inflight: public
  inherits@2.0.4:
    inherits: public
  internal-slot@1.1.0:
    internal-slot: public
  internmap@2.0.3:
    internmap: public
  invariant@2.2.4:
    invariant: public
  is-array-buffer@3.0.5:
    is-array-buffer: public
  is-arrayish@0.3.2:
    is-arrayish: public
  is-async-function@2.1.1:
    is-async-function: public
  is-bigint@1.1.0:
    is-bigint: public
  is-binary-path@2.1.0:
    is-binary-path: public
  is-boolean-object@1.2.2:
    is-boolean-object: public
  is-bun-module@2.0.0:
    is-bun-module: public
  is-callable@1.2.7:
    is-callable: public
  is-core-module@2.16.1:
    is-core-module: public
  is-data-view@1.0.2:
    is-data-view: public
  is-date-object@1.1.0:
    is-date-object: public
  is-extglob@2.1.1:
    is-extglob: public
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: public
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: public
  is-fullwidth-code-point@4.0.0:
    is-fullwidth-code-point: public
  is-generator-function@1.1.0:
    is-generator-function: public
  is-glob@4.0.3:
    is-glob: public
  is-map@2.0.3:
    is-map: public
  is-node-process@1.2.0:
    is-node-process: public
  is-number-object@1.1.1:
    is-number-object: public
  is-number@7.0.0:
    is-number: public
  is-path-inside@3.0.3:
    is-path-inside: public
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: public
  is-reference@1.2.1:
    is-reference: public
  is-regex@1.2.1:
    is-regex: public
  is-set@2.0.3:
    is-set: public
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: public
  is-stream@3.0.0:
    is-stream: public
  is-string@1.1.1:
    is-string: public
  is-symbol@1.1.1:
    is-symbol: public
  is-typed-array@1.1.15:
    is-typed-array: public
  is-weakmap@2.0.2:
    is-weakmap: public
  is-weakref@1.1.1:
    is-weakref: public
  is-weakset@2.0.4:
    is-weakset: public
  isarray@2.0.5:
    isarray: public
  isexe@2.0.0:
    isexe: public
  iterator.prototype@1.1.5:
    iterator.prototype: public
  jest-worker@27.5.1:
    jest-worker: public
  jiti@2.4.2:
    jiti: public
  js-cookie@3.0.5:
    js-cookie: public
  js-tokens@4.0.0:
    js-tokens: public
  js-yaml@4.1.0:
    js-yaml: public
  jsesc@3.1.0:
    jsesc: public
  json-buffer@3.0.1:
    json-buffer: public
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: public
  json-schema-traverse@0.4.1:
    json-schema-traverse: public
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: public
  json5@1.0.2:
    json5: public
  json5@2.2.3:
    json5: public
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: public
  keyv@4.5.4:
    keyv: public
  language-subtag-registry@0.3.23:
    language-subtag-registry: public
  language-tags@1.0.9:
    language-tags: public
  levn@0.4.1:
    levn: public
  lightningcss-win32-x64-msvc@1.29.2:
    lightningcss-win32-x64-msvc: public
  lightningcss@1.29.2:
    lightningcss: public
  lilconfig@3.1.3:
    lilconfig: public
  listr2@8.2.5:
    listr2: public
  loader-runner@4.3.0:
    loader-runner: public
  locate-path@6.0.0:
    locate-path: public
  lodash-es@4.17.21:
    lodash-es: public
  lodash.merge@4.6.2:
    lodash.merge: public
  lodash@4.17.21:
    lodash: public
  log-update@6.1.0:
    log-update: public
  loose-envify@1.4.0:
    loose-envify: public
  loupe@3.1.4:
    loupe: public
  lower-case@2.0.2:
    lower-case: public
  lru-cache@10.4.3:
    lru-cache: public
  lru-cache@5.1.1:
    lru-cache: public
  luxon@3.7.1:
    luxon: public
  lz-string@1.5.0:
    lz-string: public
  magic-string@0.30.17:
    magic-string: public
  map-obj@4.3.0:
    map-obj: public
  matchmediaquery@0.4.2:
    matchmediaquery: public
  math-intrinsics@1.1.0:
    math-intrinsics: public
  memoize-one@6.0.0:
    memoize-one: public
  merge-stream@2.0.0:
    merge-stream: public
  merge2@1.4.1:
    merge2: public
  micromatch@4.0.8:
    micromatch: public
  mime-db@1.52.0:
    mime-db: public
  mime-types@2.1.35:
    mime-types: public
  mimic-fn@4.0.0:
    mimic-fn: public
  mimic-function@5.0.1:
    mimic-function: public
  min-indent@1.0.1:
    min-indent: public
  minimatch@3.1.2:
    minimatch: public
  minimist@1.2.8:
    minimist: public
  minipass@4.2.8:
    minipass: public
  mitt@3.0.1:
    mitt: public
  module-details-from-path@1.0.4:
    module-details-from-path: public
  moment-timezone@0.5.48:
    moment-timezone: public
  moment@2.30.1:
    moment: public
  motion-dom@11.18.1:
    motion-dom: public
  motion-utils@11.18.1:
    motion-utils: public
  ms@2.1.3:
    ms: public
  mute-stream@2.0.0:
    mute-stream: public
  nanoid@3.3.11:
    nanoid: public
  natural-compare@1.4.0:
    natural-compare: public
  neo-async@2.6.2:
    neo-async: public
  no-case@3.0.4:
    no-case: public
  node-fetch@2.7.0:
    node-fetch: public
  node-releases@2.0.19:
    node-releases: public
  normalize-path@3.0.0:
    normalize-path: public
  npm-run-path@5.3.0:
    npm-run-path: public
  nprogress@0.2.0:
    nprogress: public
  nwsapi@2.2.20:
    nwsapi: public
  object-assign@4.1.1:
    object-assign: public
  object-inspect@1.13.4:
    object-inspect: public
  object-keys@1.1.1:
    object-keys: public
  object-path@0.6.0:
    object-path: public
  object.assign@4.1.7:
    object.assign: public
  object.entries@1.1.9:
    object.entries: public
  object.fromentries@2.0.8:
    object.fromentries: public
  object.groupby@1.0.3:
    object.groupby: public
  object.values@1.2.1:
    object.values: public
  once@1.4.0:
    once: public
  onetime@6.0.0:
    onetime: public
  optionator@0.9.4:
    optionator: public
  outvariant@1.4.3:
    outvariant: public
  own-keys@1.0.1:
    own-keys: public
  p-limit@3.1.0:
    p-limit: public
  p-locate@5.0.0:
    p-locate: public
  parent-module@1.0.1:
    parent-module: public
  parse5@7.3.0:
    parse5: public
  path-exists@4.0.0:
    path-exists: public
  path-is-absolute@1.0.1:
    path-is-absolute: public
  path-key@3.1.1:
    path-key: public
  path-parse@1.0.7:
    path-parse: public
  path-scurry@1.11.1:
    path-scurry: public
  path-to-regexp@6.3.0:
    path-to-regexp: public
  path-type@4.0.0:
    path-type: public
  pathe@2.0.3:
    pathe: public
  pathval@2.0.1:
    pathval: public
  pg-int8@1.0.1:
    pg-int8: public
  pg-protocol@1.10.0:
    pg-protocol: public
  pg-types@2.2.0:
    pg-types: public
  picocolors@1.1.1:
    picocolors: public
  picomatch@4.0.2:
    picomatch: public
  pidtree@0.6.0:
    pidtree: public
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: public
  postgres-array@2.0.0:
    postgres-array: public
  postgres-bytea@1.0.0:
    postgres-bytea: public
  postgres-date@1.0.7:
    postgres-date: public
  postgres-interval@1.2.0:
    postgres-interval: public
  prelude-ls@1.2.1:
    prelude-ls: public
  pretty-format@27.5.1:
    pretty-format: public
  progress@2.0.3:
    progress: public
  prop-types@15.8.1:
    prop-types: public
  proxy-from-env@1.1.0:
    proxy-from-env: public
  psl@1.15.0:
    psl: public
  punycode@2.3.1:
    punycode: public
  querystringify@2.2.0:
    querystringify: public
  queue-microtask@1.2.3:
    queue-microtask: public
  randombytes@2.1.0:
    randombytes: public
  react-is@18.3.1:
    react-is: public
  react-lifecycles-compat@3.0.4:
    react-lifecycles-compat: public
  react-overlays@5.2.1(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    react-overlays: public
  react-refresh@0.17.0:
    react-refresh: public
  react-remove-scroll-bar@2.3.8(@types/react@19.0.1)(react@19.0.0):
    react-remove-scroll-bar: public
  react-remove-scroll@2.6.3(@types/react@19.0.1)(react@19.0.0):
    react-remove-scroll: public
  react-smooth@4.0.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    react-smooth: public
  react-style-singleton@2.2.3(@types/react@19.0.1)(react@19.0.0):
    react-style-singleton: public
  react-transition-group@4.4.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    react-transition-group: public
  react-virtual@2.10.4(react@19.0.0):
    react-virtual: public
  readdirp@3.6.0:
    readdirp: public
  recharts-scale@0.4.5:
    recharts-scale: public
  redent@3.0.0:
    redent: public
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: public
  regenerator-runtime@0.14.1:
    regenerator-runtime: public
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: public
  remove-accents@0.5.0:
    remove-accents: public
  require-directory@2.1.1:
    require-directory: public
  require-from-string@2.0.2:
    require-from-string: public
  require-in-the-middle@7.5.2:
    require-in-the-middle: public
  requires-port@1.0.0:
    requires-port: public
  resolve-from@4.0.0:
    resolve-from: public
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: public
  resolve@1.22.8:
    resolve: public
  restore-cursor@5.1.0:
    restore-cursor: public
  reusify@1.1.0:
    reusify: public
  rfdc@1.4.1:
    rfdc: public
  rimraf@3.0.2:
    rimraf: public
  rollup@4.35.0:
    rollup: public
  rollup@4.44.2:
    rollup: public
  rrweb-cssom@0.8.0:
    rrweb-cssom: public
  run-parallel@1.2.0:
    run-parallel: public
  safe-array-concat@1.1.3:
    safe-array-concat: public
  safe-buffer@5.2.1:
    safe-buffer: public
  safe-push-apply@1.0.0:
    safe-push-apply: public
  safe-regex-test@1.1.0:
    safe-regex-test: public
  safer-buffer@2.1.2:
    safer-buffer: public
  saxes@6.0.0:
    saxes: public
  scheduler@0.25.0:
    scheduler: public
  schema-utils@4.3.2:
    schema-utils: public
  semver@7.7.1:
    semver: public
  serialize-javascript@6.0.2:
    serialize-javascript: public
  server-only@0.0.1:
    server-only: public
  set-function-length@1.2.2:
    set-function-length: public
  set-function-name@2.0.2:
    set-function-name: public
  set-proto@1.0.0:
    set-proto: public
  shallow-equal@3.1.0:
    shallow-equal: public
  shebang-command@2.0.0:
    shebang-command: public
  shebang-regex@3.0.0:
    shebang-regex: public
  shimmer@1.2.1:
    shimmer: public
  side-channel-list@1.0.0:
    side-channel-list: public
  side-channel-map@1.0.1:
    side-channel-map: public
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: public
  side-channel@1.1.0:
    side-channel: public
  siginfo@2.0.0:
    siginfo: public
  signal-exit@4.1.0:
    signal-exit: public
  simple-swizzle@0.2.2:
    simple-swizzle: public
  slash@3.0.0:
    slash: public
  slice-ansi@5.0.0:
    slice-ansi: public
  snake-case@3.0.4:
    snake-case: public
  snakecase-keys@8.0.1:
    snakecase-keys: public
  source-map-js@1.2.1:
    source-map-js: public
  source-map-support@0.5.21:
    source-map-support: public
  source-map@0.6.1:
    source-map: public
  stable-hash@0.0.5:
    stable-hash: public
  stackback@0.0.2:
    stackback: public
  stacktrace-parser@0.1.11:
    stacktrace-parser: public
  statuses@2.0.2:
    statuses: public
  std-env@3.8.1:
    std-env: public
  std-env@3.9.0:
    std-env: public
  streamsearch@1.1.0:
    streamsearch: public
  strict-event-emitter@0.5.1:
    strict-event-emitter: public
  string-argv@0.3.2:
    string-argv: public
  string-width@4.2.3:
    string-width: public
  string-width@7.2.0:
    string-width: public
  string.prototype.includes@2.0.1:
    string.prototype.includes: public
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: public
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: public
  string.prototype.trim@1.2.10:
    string.prototype.trim: public
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: public
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: public
  strip-ansi@6.0.1:
    strip-ansi: public
  strip-bom@3.0.0:
    strip-bom: public
  strip-final-newline@3.0.0:
    strip-final-newline: public
  strip-indent@3.0.0:
    strip-indent: public
  strip-json-comments@3.1.1:
    strip-json-comments: public
  strip-literal@3.0.0:
    strip-literal: public
  styled-jsx@5.1.6(@babel/core@7.27.1)(react@19.0.0):
    styled-jsx: public
  supports-color@7.2.0:
    supports-color: public
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: public
  swr@2.3.3(react@19.0.0):
    swr: public
  symbol-tree@3.2.4:
    symbol-tree: public
  tapable@2.2.1:
    tapable: public
  terser-webpack-plugin@5.3.14(webpack@5.99.8):
    terser-webpack-plugin: public
  terser@5.39.2:
    terser: public
  text-table@0.2.0:
    text-table: public
  tiny-invariant@1.3.3:
    tiny-invariant: public
  tinybench@2.9.0:
    tinybench: public
  tinyexec@0.3.2:
    tinyexec: public
  tinyglobby@0.2.12:
    tinyglobby: public
  tinyglobby@0.2.14:
    tinyglobby: public
  tinypool@1.1.1:
    tinypool: public
  tinyrainbow@2.0.0:
    tinyrainbow: public
  tinyspy@4.0.3:
    tinyspy: public
  tldts-core@6.1.86:
    tldts-core: public
  tldts@6.1.86:
    tldts: public
  to-regex-range@5.0.1:
    to-regex-range: public
  tough-cookie@5.1.2:
    tough-cookie: public
  tr46@0.0.3:
    tr46: public
  tr46@5.1.1:
    tr46: public
  ts-api-utils@1.4.3(typescript@5.7.2):
    ts-api-utils: public
  tsconfig-paths@3.15.0:
    tsconfig-paths: public
  tslib@2.8.1:
    tslib: public
  type-check@0.4.0:
    type-check: public
  type-fest@0.20.2:
    type-fest: public
  type-fest@4.39.0:
    type-fest: public
  typed-array-buffer@1.0.3:
    typed-array-buffer: public
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: public
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: public
  typed-array-length@1.0.7:
    typed-array-length: public
  unbox-primitive@1.1.0:
    unbox-primitive: public
  uncontrollable@7.2.1(react@19.0.0):
    uncontrollable: public
  undici-types@6.20.0:
    undici-types: public
  universalify@0.2.0:
    universalify: public
  unplugin@1.0.1:
    unplugin: public
  unrs-resolver@1.3.2:
    unrs-resolver: public
  update-browserslist-db@1.1.3(browserslist@4.24.5):
    update-browserslist-db: public
  uri-js@4.4.1:
    uri-js: public
  url-parse@1.5.10:
    url-parse: public
  use-callback-ref@1.3.3(@types/react@19.0.1)(react@19.0.0):
    use-callback-ref: public
  use-sidecar@1.1.3(@types/react@19.0.1)(react@19.0.0):
    use-sidecar: public
  use-sync-external-store@1.5.0(react@19.0.0):
    use-sync-external-store: public
  victory-vendor@36.9.2:
    victory-vendor: public
  vite-node@3.2.4(@types/node@22.10.2)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.2)(yaml@2.7.0):
    vite-node: public
  vite@7.0.3(@types/node@22.10.2)(jiti@2.4.2)(lightningcss@1.29.2)(terser@5.39.2)(yaml@2.7.0):
    vite: public
  w3c-xmlserializer@5.0.0:
    w3c-xmlserializer: public
  warning@4.0.3:
    warning: public
  watchpack@2.4.2:
    watchpack: public
  webidl-conversions@3.0.1:
    webidl-conversions: public
  webidl-conversions@7.0.0:
    webidl-conversions: public
  webpack-sources@3.2.3:
    webpack-sources: public
  webpack-virtual-modules@0.5.0:
    webpack-virtual-modules: public
  webpack@5.99.8:
    webpack: public
  whatwg-encoding@3.1.1:
    whatwg-encoding: public
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: public
  whatwg-url@14.2.0:
    whatwg-url: public
  whatwg-url@5.0.0:
    whatwg-url: public
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: public
  which-builtin-type@1.2.1:
    which-builtin-type: public
  which-collection@1.0.2:
    which-collection: public
  which-typed-array@1.1.19:
    which-typed-array: public
  which@2.0.2:
    which: public
  why-is-node-running@2.3.0:
    why-is-node-running: public
  word-wrap@1.2.5:
    word-wrap: public
  wrap-ansi@9.0.0:
    wrap-ansi: public
  wrappy@1.0.2:
    wrappy: public
  ws@8.18.3:
    ws: public
  xml-name-validator@5.0.0:
    xml-name-validator: public
  xmlchars@2.2.0:
    xmlchars: public
  xtend@4.0.2:
    xtend: public
  y18n@5.0.8:
    y18n: public
  yallist@3.1.1:
    yallist: public
  yaml@2.7.0:
    yaml: public
  yargs-parser@21.1.1:
    yargs-parser: public
  yargs@17.7.2:
    yargs: public
  yocto-queue@0.1.0:
    yocto-queue: public
  yoctocolors-cjs@2.1.2:
    yoctocolors-cjs: public
ignoredBuilds:
  - msw
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Tue, 08 Jul 2025 22:31:55 GMT
publicHoistPattern:
  - '*'
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.3.1'
  - '@emnapi/runtime@1.3.1'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.1'
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-arm64@0.25.6'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.25.6'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-arm64@0.34.1'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-darwin-x64@0.34.1'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm64@0.34.1'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-arm@0.34.1'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-s390x@0.34.1'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linux-x64@0.34.1'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.34.1'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.34.1'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-wasm32@0.34.1'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-ia32@0.34.1'
  - '@napi-rs/wasm-runtime@0.2.7'
  - '@next/swc-darwin-arm64@15.3.2'
  - '@next/swc-darwin-x64@15.3.2'
  - '@next/swc-linux-arm64-gnu@15.3.2'
  - '@next/swc-linux-arm64-musl@15.3.2'
  - '@next/swc-linux-x64-gnu@15.3.2'
  - '@next/swc-linux-x64-musl@15.3.2'
  - '@next/swc-win32-arm64-msvc@15.3.2'
  - '@rollup/rollup-android-arm-eabi@4.35.0'
  - '@rollup/rollup-android-arm-eabi@4.44.2'
  - '@rollup/rollup-android-arm64@4.35.0'
  - '@rollup/rollup-android-arm64@4.44.2'
  - '@rollup/rollup-darwin-arm64@4.35.0'
  - '@rollup/rollup-darwin-arm64@4.44.2'
  - '@rollup/rollup-darwin-x64@4.35.0'
  - '@rollup/rollup-darwin-x64@4.44.2'
  - '@rollup/rollup-freebsd-arm64@4.35.0'
  - '@rollup/rollup-freebsd-arm64@4.44.2'
  - '@rollup/rollup-freebsd-x64@4.35.0'
  - '@rollup/rollup-freebsd-x64@4.44.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.35.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.35.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.2'
  - '@rollup/rollup-linux-arm64-gnu@4.35.0'
  - '@rollup/rollup-linux-arm64-gnu@4.44.2'
  - '@rollup/rollup-linux-arm64-musl@4.35.0'
  - '@rollup/rollup-linux-arm64-musl@4.44.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.35.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.35.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.35.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-musl@4.44.2'
  - '@rollup/rollup-linux-s390x-gnu@4.35.0'
  - '@rollup/rollup-linux-s390x-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-gnu@4.35.0'
  - '@rollup/rollup-linux-x64-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-musl@4.35.0'
  - '@rollup/rollup-linux-x64-musl@4.44.2'
  - '@rollup/rollup-win32-arm64-msvc@4.35.0'
  - '@rollup/rollup-win32-arm64-msvc@4.44.2'
  - '@rollup/rollup-win32-ia32-msvc@4.35.0'
  - '@rollup/rollup-win32-ia32-msvc@4.44.2'
  - '@sentry/cli-darwin@2.42.2'
  - '@sentry/cli-linux-arm64@2.42.2'
  - '@sentry/cli-linux-arm@2.42.2'
  - '@sentry/cli-linux-i686@2.42.2'
  - '@sentry/cli-linux-x64@2.42.2'
  - '@sentry/cli-win32-i686@2.42.2'
  - '@tailwindcss/oxide-android-arm64@4.0.17'
  - '@tailwindcss/oxide-darwin-arm64@4.0.17'
  - '@tailwindcss/oxide-darwin-x64@4.0.17'
  - '@tailwindcss/oxide-freebsd-x64@4.0.17'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.0.17'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.0.17'
  - '@tailwindcss/oxide-linux-arm64-musl@4.0.17'
  - '@tailwindcss/oxide-linux-x64-gnu@4.0.17'
  - '@tailwindcss/oxide-linux-x64-musl@4.0.17'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.0.17'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-darwin-arm64@1.3.2'
  - '@unrs/resolver-binding-darwin-x64@1.3.2'
  - '@unrs/resolver-binding-freebsd-x64@1.3.2'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.3.2'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.3.2'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.3.2'
  - '@unrs/resolver-binding-linux-arm64-musl@1.3.2'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.3.2'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.3.2'
  - '@unrs/resolver-binding-linux-x64-gnu@1.3.2'
  - '@unrs/resolver-binding-linux-x64-musl@1.3.2'
  - '@unrs/resolver-binding-wasm32-wasi@1.3.2'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.3.2'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.3.2'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.29.2
  - lightningcss-darwin-x64@1.29.2
  - lightningcss-freebsd-x64@1.29.2
  - lightningcss-linux-arm-gnueabihf@1.29.2
  - lightningcss-linux-arm64-gnu@1.29.2
  - lightningcss-linux-arm64-musl@1.29.2
  - lightningcss-linux-x64-gnu@1.29.2
  - lightningcss-linux-x64-musl@1.29.2
  - lightningcss-win32-arm64-msvc@1.29.2
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\nord-coast\frontend-nextjs\node_modules\.pnpm
virtualStoreDirMaxLength: 60
