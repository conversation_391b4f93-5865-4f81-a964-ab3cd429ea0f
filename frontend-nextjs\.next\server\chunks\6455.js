try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},l=(new e.Error).stack;l&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[l]="ced611ac-2080-4e3e-874c-b44d9554e2b7",e._sentryDebugIdIdentifier="sentry-dbid-ced611ac-2080-4e3e-874c-b44d9554e2b7")}catch(e){}"use strict";exports.id=6455,exports.ids=[6455],exports.modules={6455:(e,l,s)=>{s.r(l),s.d(l,{KeylessCreatorOrReader:()=>d});var t=s(34769),n=s(60222),r=s.n(n),i=s(67909);let d=e=>{var l;let{children:s}=e,d=(null==(l=(0,t.useSelectedLayoutSegments)()[0])?void 0:l.startsWith("/_not-found"))||!1,[a,o]=r().useActionState(i.a,null);return((0,n.useEffect)(()=>{d||r().startTransition(()=>{o()})},[d]),r().isValidElement(s))?r().cloneElement(s,{key:null==a?void 0:a.publishableKey,publishableKey:null==a?void 0:a.publishableKey,__internal_keyless_claimKeylessApplicationUrl:null==a?void 0:a.claimUrl,__internal_keyless_copyInstanceKeysUrl:null==a?void 0:a.apiKeysUrl,__internal_bypassMissingPublishableKey:!0}):s}},67909:(e,l,s)=>{s.d(l,{a:()=>n});var t=s(56495);let n=(0,t.createServerReference)("7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261",t.callServer,void 0,t.findSourceMapURL,"createOrReadKeylessAction")}};
//# sourceMappingURL=6455.js.map