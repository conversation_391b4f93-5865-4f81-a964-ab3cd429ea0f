"use strict";exports.id=3742,exports.ids=[3742],exports.modules={22584:(t,e,a)=>{function r(t){return(e,a={})=>{let r,n=a.width,i=n&&t.matchPatterns[n]||t.matchPatterns[t.defaultMatchWidth],o=e.match(i);if(!o)return null;let u=o[0],l=n&&t.parsePatterns[n]||t.parsePatterns[t.defaultParseWidth],s=Array.isArray(l)?function(t,e){for(let a=0;a<t.length;a++)if(e(t[a]))return a}(l,t=>t.test(u)):function(t,e){for(let a in t)if(Object.prototype.hasOwnProperty.call(t,a)&&e(t[a]))return a}(l,t=>t.test(u));return r=t.valueCallback?t.valueCallback(s):s,{value:r=a.valueCallback?a.valueCallback(r):r,rest:e.slice(u.length)}}}a.d(e,{A:()=>r})},30804:(t,e,a)=>{a.d(e,{w:()=>n});var r=a(78904);function n(t,e){return"function"==typeof t?t(e):t&&"object"==typeof t&&r._P in t?t[r._P](e):t instanceof Date?new t.constructor(e):new Date(e)}},31456:(t,e,a)=>{a.d(e,{x:()=>n});var r=a(30804);function n(t,...e){let a=r.w.bind(null,t||e.find(t=>"object"==typeof t));return e.map(a)}},37549:(t,e,a)=>{a.d(e,{q:()=>n});let r={};function n(){return r}},43079:(t,e,a)=>{a.d(e,{R:()=>i});var r=a(31456),n=a(91520);function i(t,e,a){let[i,o]=(0,r.x)(a?.in,t,e);return+(0,n.k)(i,a)==+(0,n.k)(o,a)}},43416:(t,e,a)=>{a.d(e,{K:()=>r});function r(t){return(e,a={})=>{let r=e.match(t.matchPattern);if(!r)return null;let n=r[0],i=e.match(t.parsePattern);if(!i)return null;let o=t.valueCallback?t.valueCallback(i[0]):i[0];return{value:o=a.valueCallback?a.valueCallback(o):o,rest:e.slice(n.length)}}}},53742:(t,e,a)=>{a.r(e),a.d(e,{bg:()=>y,default:()=>w});let r={lessThanXSeconds:{one:"по-малко от секунда",other:"по-малко от {{count}} секунди"},xSeconds:{one:"1 секунда",other:"{{count}} секунди"},halfAMinute:"половин минута",lessThanXMinutes:{one:"по-малко от минута",other:"по-малко от {{count}} минути"},xMinutes:{one:"1 минута",other:"{{count}} минути"},aboutXHours:{one:"около час",other:"около {{count}} часа"},xHours:{one:"1 час",other:"{{count}} часа"},xDays:{one:"1 ден",other:"{{count}} дни"},aboutXWeeks:{one:"около седмица",other:"около {{count}} седмици"},xWeeks:{one:"1 седмица",other:"{{count}} седмици"},aboutXMonths:{one:"около месец",other:"около {{count}} месеца"},xMonths:{one:"1 месец",other:"{{count}} месеца"},aboutXYears:{one:"около година",other:"около {{count}} години"},xYears:{one:"1 година",other:"{{count}} години"},overXYears:{one:"над година",other:"над {{count}} години"},almostXYears:{one:"почти година",other:"почти {{count}} години"}};var n=a(89500);let i={date:(0,n.k)({formats:{full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd.MM.yyyy"},defaultWidth:"full"}),time:(0,n.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,n.k)({formats:{any:"{{date}} {{time}}"},defaultWidth:"any"})};var o=a(43079),u=a(61200);let l=["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"];function s(t){let e=l[t];return 2===t?"'във "+e+" в' p":"'в "+e+" в' p"}let d={lastWeek:(t,e,a)=>{let r=(0,u.a)(t),n=r.getDay();return(0,o.R)(r,e,a)?s(n):function(t){let e=l[t];switch(t){case 0:case 3:case 6:return"'миналата "+e+" в' p";case 1:case 2:case 4:case 5:return"'миналия "+e+" в' p"}}(n)},yesterday:"'вчера в' p",today:"'днес в' p",tomorrow:"'утре в' p",nextWeek:(t,e,a)=>{let r=(0,u.a)(t),n=r.getDay();return(0,o.R)(r,e,a)?s(n):function(t){let e=l[t];switch(t){case 0:case 3:case 6:return"'следващата "+e+" в' p";case 1:case 2:case 4:case 5:return"'следващия "+e+" в' p"}}(n)},other:"P"};var c=a(84246);function h(t,e,a,r,n){return t+"-"+("quarter"===e?n:"year"===e||"week"===e||"minute"===e||"second"===e?r:a)}let f={ordinalNumber:(t,e)=>{let a=Number(t),r=e?.unit;if(0===a)return h(0,r,"ев","ева","ево");if(a%1e3==0)return h(a,r,"ен","на","но");if(a%100==0)return h(a,r,"тен","тна","тно");let n=a%100;if(n>20||n<10)switch(n%10){case 1:return h(a,r,"ви","ва","во");case 2:return h(a,r,"ри","ра","ро");case 7:case 8:return h(a,r,"ми","ма","мо")}return h(a,r,"ти","та","то")},era:(0,c.o)({values:{narrow:["пр.н.е.","н.е."],abbreviated:["преди н. е.","н. е."],wide:["преди новата ера","новата ера"]},defaultWidth:"wide"}),quarter:(0,c.o)({values:{narrow:["1","2","3","4"],abbreviated:["1-во тримес.","2-ро тримес.","3-то тримес.","4-то тримес."],wide:["1-во тримесечие","2-ро тримесечие","3-то тримесечие","4-то тримесечие"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:(0,c.o)({values:{abbreviated:["яну","фев","мар","апр","май","юни","юли","авг","сеп","окт","ное","дек"],wide:["януари","февруари","март","април","май","юни","юли","август","септември","октомври","ноември","декември"]},defaultWidth:"wide"}),day:(0,c.o)({values:{narrow:["Н","П","В","С","Ч","П","С"],short:["нд","пн","вт","ср","чт","пт","сб"],abbreviated:["нед","пон","вто","сря","чет","пет","съб"],wide:["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"]},defaultWidth:"wide"}),dayPeriod:(0,c.o)({values:{wide:{am:"преди обяд",pm:"след обяд",midnight:"в полунощ",noon:"на обяд",morning:"сутринта",afternoon:"следобед",evening:"вечерта",night:"през нощта"}},defaultWidth:"wide"})};var m=a(22584);let y={code:"bg",formatDistance:(t,e,a)=>{let n,i=r[t];if(n="string"==typeof i?i:1===e?i.one:i.other.replace("{{count}}",String(e)),a?.addSuffix)if(a.comparison&&a.comparison>0)return"след "+n;else return"преди "+n;return n},formatLong:i,formatRelative:(t,e,a,r)=>{let n=d[t];return"function"==typeof n?n(e,a,r):n},localize:f,match:{ordinalNumber:(0,a(43416).K)({matchPattern:/^(\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:(0,m.A)({matchPatterns:{narrow:/^((пр)?н\.?\s?е\.?)/i,abbreviated:/^((пр)?н\.?\s?е\.?)/i,wide:/^(преди новата ера|новата ера|нова ера)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^п/i,/^н/i]},defaultParseWidth:"any"}),quarter:(0,m.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^[1234](-?[врт]?o?)? тримес.?/i,wide:/^[1234](-?[врт]?о?)? тримесечие/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:(0,m.A)({matchPatterns:{abbreviated:/^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,wide:/^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^май/i,/^юн/i,/^юл/i,/^ав/i,/^се/i,/^окт/i,/^но/i,/^де/i]},defaultParseWidth:"any"}),day:(0,m.A)({matchPatterns:{narrow:/^[нпвсч]/i,short:/^(нд|пн|вт|ср|чт|пт|сб)/i,abbreviated:/^(нед|пон|вто|сря|чет|пет|съб)/i,wide:/^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н[ед]/i,/^п[он]/i,/^вт/i,/^ср/i,/^ч[ет]/i,/^п[ет]/i,/^с[ъб]/i]},defaultParseWidth:"any"}),dayPeriod:(0,m.A)({matchPatterns:{any:/^(преди о|след о|в по|на о|през|веч|сут|следо)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^преди о/i,pm:/^след о/i,midnight:/^в пол/i,noon:/^на об/i,morning:/^сут/i,afternoon:/^следо/i,evening:/^веч/i,night:/^през н/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:1}},w=y},61200:(t,e,a)=>{a.d(e,{a:()=>n});var r=a(30804);function n(t,e){return(0,r.w)(e||t,t)}},78904:(t,e,a)=>{a.d(e,{_P:()=>i,my:()=>r,w4:()=>n});let r=6048e5,n=864e5,i=Symbol.for("constructDateFrom")},84246:(t,e,a)=>{a.d(e,{o:()=>r});function r(t){return(e,a)=>{let r;if("formatting"===(a?.context?String(a.context):"standalone")&&t.formattingValues){let e=t.defaultFormattingWidth||t.defaultWidth,n=a?.width?String(a.width):e;r=t.formattingValues[n]||t.formattingValues[e]}else{let e=t.defaultWidth,n=a?.width?String(a.width):t.defaultWidth;r=t.values[n]||t.values[e]}return r[t.argumentCallback?t.argumentCallback(e):e]}}},89500:(t,e,a)=>{a.d(e,{k:()=>r});function r(t){return (e={})=>{let a=e.width?String(e.width):t.defaultWidth;return t.formats[a]||t.formats[t.defaultWidth]}}},91520:(t,e,a)=>{a.d(e,{k:()=>i});var r=a(37549),n=a(61200);function i(t,e){let a=(0,r.q)(),i=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??a.weekStartsOn??a.locale?.options?.weekStartsOn??0,o=(0,n.a)(t,e?.in),u=o.getDay();return o.setDate(o.getDate()-(7*(u<i)+u-i)),o.setHours(0,0,0,0),o}}};