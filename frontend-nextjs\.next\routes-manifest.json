{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/appointments/[id]", "regex": "^/api/appointments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/appointments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/bill-items/[id]", "regex": "^/api/bill\\-items/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/bill\\-items/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/bills/[id]", "regex": "^/api/bills/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/bills/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/patient-interactions/[id]", "regex": "^/api/patient\\-interactions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patient\\-interactions/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/patient-tasks/[id]", "regex": "^/api/patient\\-tasks/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patient\\-tasks/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/patients/[id]", "regex": "^/api/patients/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patients/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/patients/[id]/interactions", "regex": "^/api/patients/([^/]+?)/interactions(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patients/(?<nxtPid>[^/]+?)/interactions(?:/)?$"}, {"page": "/api/patients/[id]/tasks", "regex": "^/api/patients/([^/]+?)/tasks(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patients/(?<nxtPid>[^/]+?)/tasks(?:/)?$"}, {"page": "/api/patients/[id]/timeline", "regex": "^/api/patients/([^/]+?)/timeline(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patients/(?<nxtPid>[^/]+?)/timeline(?:/)?$"}, {"page": "/api/payments/[id]", "regex": "^/api/payments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/payments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/treatments/[id]", "regex": "^/api/treatments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/treatments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/auth/sign-in/[[...sign-in]]", "regex": "^/auth/sign\\-in(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignin": "nxtPsign-in"}, "namedRegex": "^/auth/sign\\-in(?:/(?<nxtPsignin>.+?))?(?:/)?$"}, {"page": "/auth/sign-up/[[...sign-up]]", "regex": "^/auth/sign\\-up(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPsignup": "nxtPsign-up"}, "namedRegex": "^/auth/sign\\-up(?:/(?<nxtPsignup>.+?))?(?:/)?$"}, {"page": "/dashboard/patients/[id]", "regex": "^/dashboard/patients/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/patients/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/product/[productId]", "regex": "^/dashboard/product/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/dashboard/product/(?<nxtPproductId>[^/]+?)(?:/)?$"}, {"page": "/dashboard/profile/[[...profile]]", "regex": "^/dashboard/profile(?:/(.+?))?(?:/)?$", "routeKeys": {"nxtPprofile": "nxtPprofile"}, "namedRegex": "^/dashboard/profile(?:/(?<nxtPprofile>.+?))?(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/admin", "regex": "^/dashboard/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/admin(?:/)?$"}, {"page": "/dashboard/appointments", "regex": "^/dashboard/appointments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/appointments(?:/)?$"}, {"page": "/dashboard/billing", "regex": "^/dashboard/billing(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/billing(?:/)?$"}, {"page": "/dashboard/kanban", "regex": "^/dashboard/kanban(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/kanban(?:/)?$"}, {"page": "/dashboard/overview", "regex": "^/dashboard/overview(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/overview(?:/)?$"}, {"page": "/dashboard/patients", "regex": "^/dashboard/patients(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/patients(?:/)?$"}, {"page": "/dashboard/product", "regex": "^/dashboard/product(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/product(?:/)?$"}, {"page": "/dashboard/treatments", "regex": "^/dashboard/treatments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/treatments(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/monitoring(/?)", "has": [{"type": "query", "key": "o", "value": "(?<orgid>\\d*)"}, {"type": "query", "key": "p", "value": "(?<projectid>\\d*)"}, {"type": "query", "key": "r", "value": "(?<region>[a-z]{2})"}], "destination": "https://o:orgid.ingest.:region.sentry.io/api/:projectid/envelope/?hsts=0", "regex": "^/monitoring(/?)(?:/)?$"}, {"source": "/monitoring(/?)", "has": [{"type": "query", "key": "o", "value": "(?<orgid>\\d*)"}, {"type": "query", "key": "p", "value": "(?<projectid>\\d*)"}], "destination": "https://o:orgid.ingest.sentry.io/api/:projectid/envelope/?hsts=0", "regex": "^/monitoring(/?)(?:/)?$"}]}