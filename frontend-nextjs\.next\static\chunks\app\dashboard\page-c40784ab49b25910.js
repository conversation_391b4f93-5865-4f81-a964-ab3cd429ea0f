try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="5666bf12-e93b-4929-8722-40ca08748c5a",e._sentryDebugIdIdentifier="sentry-dbid-5666bf12-e93b-4929-8722-40ca08748c5a")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{7964:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(49202).A)("outline","trending-up","IconTrendingUp",[["path",{d:"M3 17l6 -6l4 4l8 -8",key:"svg-0"}],["path",{d:"M14 7l7 0l0 7",key:"svg-1"}]])},26230:(e,t,a)=>{"use strict";a.d(t,{t:()=>s});let r={nav:{dashboard:"仪表板",appointments:"预约管理",patients:"患者管理",treatments:"治疗项目",admin:"系统管理",account:"账户",profile:"个人资料",login:"登录",overview:"概览"},dashboard:{title:"诊所控制台 \uD83C\uDFE5",subtitle:"欢迎使用您的诊所管理系统",metrics:{todayAppointments:"今日预约",recentPatients:"近期患者",totalPatients:"患者总数",activetreatments:"可用治疗",scheduledForToday:"今日安排",appointmentsScheduledForToday:"今日安排的预约",newPatientsThisWeek:"本周新患者",patientsRegisteredInLast7Days:"过去7天注册的患者",totalRegisteredPatients:"注册患者总数",completePatientDatabase:"完整患者数据库",treatmentOptionsAvailable:"可用治疗选项",fullServiceCatalog:"完整服务目录",active:"活跃",last7Days:"过去7天",allTime:"全部时间",available:"可用"},errors:{loadingDashboard:"加载仪表板时出错",failedToLoadMetrics:"无法加载仪表板数据"}},appointments:{title:"预约管理",subtitle:"管理患者预约和排程",newAppointment:"新建预约",editAppointment:"编辑预约",appointmentDetails:"预约详情",appointmentsCount:"个预约",loadingAppointments:"加载预约中...",noAppointments:"暂无预约",filters:{all:"全部",today:"今天",thisWeek:"本周",thisMonth:"本月",status:"状态",dateRange:"日期范围"},status:{scheduled:"已安排",confirmed:"已确认",inProgress:"进行中",completed:"已完成",cancelled:"已取消",noShow:"未到场"},form:{patient:"患者",selectPatient:"选择患者",treatment:"治疗项目",selectTreatment:"选择治疗项目",date:"日期",time:"时间",notes:"备注",notesPlaceholder:"预约备注（可选）",status:"状态"}},patients:{title:"患者管理",subtitle:"管理患者信息和病历",newPatient:"新建患者",editPatient:"编辑患者",patientDetails:"患者详情",patientsCount:"位患者",loadingPatients:"加载患者中...",noPatients:"暂无患者",searchPlaceholder:"按姓名、电话或邮箱搜索患者",form:{fullName:"姓名",fullNamePlaceholder:"请输入患者姓名",phone:"电话",phonePlaceholder:"请输入电话号码",email:"邮箱",emailPlaceholder:"请输入邮箱地址（可选）",medicalNotes:"病历备注",medicalNotesPlaceholder:"请输入病历备注（可选）"}},treatments:{title:"治疗项目",subtitle:"管理诊所治疗服务",newTreatment:"新建治疗",editTreatment:"编辑治疗",treatmentDetails:"治疗详情",treatmentsCount:"个治疗项目",loadingTreatments:"加载治疗项目中...",noTreatments:"暂无治疗项目",form:{name:"治疗名称",namePlaceholder:"请输入治疗名称",description:"治疗描述",descriptionPlaceholder:"请输入治疗描述",duration:"治疗时长",durationPlaceholder:"请输入治疗时长（分钟）",price:"价格",pricePlaceholder:"请输入价格"}},admin:{title:"系统管理",subtitle:"管理用户权限和系统设置",userManagement:"用户管理",roleManagement:"角色管理",systemSettings:"系统设置",users:"用户",roles:{admin:"管理员",doctor:"医生",frontDesk:"前台"}},common:{actions:{save:"保存",cancel:"取消",edit:"编辑",delete:"删除",view:"查看",search:"搜索",filter:"筛选",reset:"重置",submit:"提交",close:"关闭",confirm:"确认",back:"返回",next:"下一步",previous:"上一步",add:"添加",remove:"移除",update:"更新",create:"创建"},status:{loading:"加载中...",success:"成功",error:"错误",warning:"警告",info:"信息",pending:"待处理",active:"活跃",inactive:"非活跃",enabled:"已启用",disabled:"已禁用"},time:{today:"今天",yesterday:"昨天",tomorrow:"明天",thisWeek:"本周",lastWeek:"上周",nextWeek:"下周",thisMonth:"本月",lastMonth:"上月",nextMonth:"下月",thisYear:"今年",lastYear:"去年",nextYear:"明年"},confirmDialog:{title:"确认操作",deleteTitle:"确认删除",deleteMessage:"您确定要删除这个项目吗？此操作无法撤销。",cancelTitle:"确认取消",cancelMessage:"您确定要取消吗？未保存的更改将丢失。",saveTitle:"确认保存",saveMessage:"您确定要保存这些更改吗？"}},validation:{required:"此字段为必填项",email:"请输入有效的邮箱地址",phone:"请输入有效的电话号码",minLength:"至少需要 {min} 个字符",maxLength:"最多允许 {max} 个字符",number:"请输入有效的数字",positive:"请输入正数",date:"请选择有效的日期",time:"请选择有效的时间"},errors:{general:"发生了未知错误，请稍后重试",network:"网络连接错误，请检查您的网络连接",unauthorized:"您没有权限执行此操作",notFound:"请求的资源未找到",serverError:"服务器错误，请稍后重试",validationError:"输入数据验证失败",loadFailed:"加载数据失败",saveFailed:"保存数据失败",deleteFailed:"删除数据失败",updateFailed:"更新数据失败",createFailed:"创建数据失败"},success:{saved:"保存成功",deleted:"删除成功",updated:"更新成功",created:"创建成功",sent:"发送成功",uploaded:"上传成功",downloaded:"下载成功"}};function s(e,t){let a=e.split("."),s=r;for(let t of a)if(!s||"object"!=typeof s||!(t in s))return console.warn("Translation key not found: ".concat(e)),e;else s=s[t];return"string"!=typeof s?(console.warn("Translation value is not a string: ".concat(e)),e):t?s.replace(/\{(\w+)\}/g,(e,a)=>{var r;return(null==(r=t[a])?void 0:r.toString())||e}):s}},47889:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(49202).A)("outline","stethoscope","IconStethoscope",[["path",{d:"M6 4h-1a2 2 0 0 0 -2 2v3.5h0a5.5 5.5 0 0 0 11 0v-3.5a2 2 0 0 0 -2 -2h-1",key:"svg-0"}],["path",{d:"M8 15a6 6 0 1 0 12 0v-3",key:"svg-1"}],["path",{d:"M11 3v2",key:"svg-2"}],["path",{d:"M6 3v2",key:"svg-3"}],["path",{d:"M20 10m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-4"}]])},49202:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(99004),s={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let n=(e,t,a,n)=>{let i=(0,r.forwardRef)((a,i)=>{let{color:d="currentColor",size:l=24,stroke:o=2,title:c,className:m,children:u,...h}=a;return(0,r.createElement)("svg",{ref:i,...s[e],width:l,height:l,className:["tabler-icon","tabler-icon-".concat(t),m].join(" "),..."filled"===e?{fill:d}:{strokeWidth:o,stroke:d},...h},[c&&(0,r.createElement)("title",{key:"svg-title"},c),...n.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(u)?u:[u]])});return i.displayName="".concat(a),i}},54651:(e,t,a)=>{"use strict";a.d(t,{Jv:()=>o,cn:()=>n,fw:()=>l,r6:()=>d,z3:()=>i});var r=a(97921),s=a(56309);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function i(e){var t,a;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:s=0,sizeType:n="normal"}=r;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(s)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][i])?a:"Bytes")}function d(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function l(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let a=Math.floor(t/60);if(a<60)return"".concat(a,"分钟前");let r=Math.floor(a/60);if(r<24)return"".concat(r,"小时前");let s=Math.floor(r/24);if(s<7)return"".concat(s,"天前");let n=Math.floor(s/7);if(n<4)return"".concat(n,"周前");let i=Math.floor(s/30);if(i<12)return"".concat(i,"个月前");let d=Math.floor(s/365);return"".concat(d,"年前")}function o(e){return("string"==typeof e?new Date(e):e)<new Date}},56582:(e,t,a)=>{"use strict";a.d(t,{default:()=>h});var r=a(52880),s=a(99004),n=a(88151),i=a(86540),d=a(72486),l=a(7964),o=a(68290),c=a(47889),m=a(87378),u=a(26230);function h(){let[e,t]=(0,s.useState)({todayAppointments:0,recentPatients:0,totalPatients:0,activetreatments:0}),[a,h]=(0,s.useState)(!0),[p,y]=(0,s.useState)(null);return((0,s.useEffect)(()=>{!async function(){try{h(!0);let e=await (0,m.cU)();t(e),y(null)}catch(e){console.error("Failed to fetch dashboard metrics:",e),y((0,u.t)("dashboard.errors.failedToLoadMetrics"))}finally{h(!1)}}()},[]),a)?(0,r.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(i.Zp,{className:"@container/card",children:(0,r.jsxs)(i.aR,{children:[(0,r.jsxs)(i.BT,{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-4 w-4 bg-muted animate-pulse rounded"}),(0,r.jsx)("div",{className:"h-4 w-24 bg-muted animate-pulse rounded"})]}),(0,r.jsx)(i.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:(0,r.jsx)("div",{className:"h-8 w-16 bg-muted animate-pulse rounded"})})]})},t))}):p?(0,r.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4",children:(0,r.jsx)(i.Zp,{className:"col-span-full",children:(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{className:"text-destructive",children:(0,u.t)("dashboard.errors.loadingDashboard")}),(0,r.jsx)(i.BT,{children:p})]})})}):(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4","data-sentry-component":"DashboardMetrics","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)(i.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)(i.BT,{className:"flex items-center gap-2","data-sentry-element":"CardDescription","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsx)(d.A,{className:"size-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"dashboard-metrics.tsx"}),(0,u.t)("dashboard.metrics.todayAppointments")]}),(0,r.jsx)(i.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl","data-sentry-element":"CardTitle","data-sentry-source-file":"dashboard-metrics.tsx",children:e.todayAppointments}),(0,r.jsx)(i.X9,{"data-sentry-element":"CardAction","data-sentry-source-file":"dashboard-metrics.tsx",children:(0,r.jsxs)(n.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsx)(l.A,{"data-sentry-element":"IconTrendingUp","data-sentry-source-file":"dashboard-metrics.tsx"}),(0,u.t)("dashboard.metrics.active")]})})]}),(0,r.jsxs)(i.wL,{className:"flex-col items-start gap-1.5 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:[(0,u.t)("dashboard.metrics.scheduledForToday")," ",(0,r.jsx)(d.A,{className:"size-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"dashboard-metrics.tsx"})]}),(0,r.jsx)("div",{className:"text-muted-foreground",children:(0,u.t)("dashboard.metrics.appointmentsScheduledForToday")})]})]}),(0,r.jsxs)(i.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)(i.BT,{className:"flex items-center gap-2","data-sentry-element":"CardDescription","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsx)(o.A,{className:"size-4","data-sentry-element":"IconUsers","data-sentry-source-file":"dashboard-metrics.tsx"}),(0,u.t)("dashboard.metrics.recentPatients")]}),(0,r.jsx)(i.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl","data-sentry-element":"CardTitle","data-sentry-source-file":"dashboard-metrics.tsx",children:e.recentPatients}),(0,r.jsx)(i.X9,{"data-sentry-element":"CardAction","data-sentry-source-file":"dashboard-metrics.tsx",children:(0,r.jsxs)(n.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsx)(l.A,{"data-sentry-element":"IconTrendingUp","data-sentry-source-file":"dashboard-metrics.tsx"}),(0,u.t)("dashboard.metrics.last7Days")]})})]}),(0,r.jsxs)(i.wL,{className:"flex-col items-start gap-1.5 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:[(0,u.t)("dashboard.metrics.newPatientsThisWeek")," ",(0,r.jsx)(o.A,{className:"size-4","data-sentry-element":"IconUsers","data-sentry-source-file":"dashboard-metrics.tsx"})]}),(0,r.jsx)("div",{className:"text-muted-foreground",children:(0,u.t)("dashboard.metrics.patientsRegisteredInLast7Days")})]})]}),(0,r.jsxs)(i.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)(i.BT,{className:"flex items-center gap-2","data-sentry-element":"CardDescription","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsx)(o.A,{className:"size-4","data-sentry-element":"IconUsers","data-sentry-source-file":"dashboard-metrics.tsx"}),(0,u.t)("dashboard.metrics.totalPatients")]}),(0,r.jsx)(i.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl","data-sentry-element":"CardTitle","data-sentry-source-file":"dashboard-metrics.tsx",children:e.totalPatients}),(0,r.jsx)(i.X9,{"data-sentry-element":"CardAction","data-sentry-source-file":"dashboard-metrics.tsx",children:(0,r.jsxs)(n.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsx)(l.A,{"data-sentry-element":"IconTrendingUp","data-sentry-source-file":"dashboard-metrics.tsx"}),(0,u.t)("dashboard.metrics.allTime")]})})]}),(0,r.jsxs)(i.wL,{className:"flex-col items-start gap-1.5 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:[(0,u.t)("dashboard.metrics.totalRegisteredPatients")," ",(0,r.jsx)(o.A,{className:"size-4","data-sentry-element":"IconUsers","data-sentry-source-file":"dashboard-metrics.tsx"})]}),(0,r.jsx)("div",{className:"text-muted-foreground",children:(0,u.t)("dashboard.metrics.completePatientDatabase")})]})]}),(0,r.jsxs)(i.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)(i.BT,{className:"flex items-center gap-2","data-sentry-element":"CardDescription","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsx)(c.A,{className:"size-4","data-sentry-element":"IconStethoscope","data-sentry-source-file":"dashboard-metrics.tsx"}),(0,u.t)("dashboard.metrics.activetreatments")]}),(0,r.jsx)(i.ZB,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl","data-sentry-element":"CardTitle","data-sentry-source-file":"dashboard-metrics.tsx",children:e.activetreatments}),(0,r.jsx)(i.X9,{"data-sentry-element":"CardAction","data-sentry-source-file":"dashboard-metrics.tsx",children:(0,r.jsxs)(n.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsx)(l.A,{"data-sentry-element":"IconTrendingUp","data-sentry-source-file":"dashboard-metrics.tsx"}),(0,u.t)("dashboard.metrics.available")]})})]}),(0,r.jsxs)(i.wL,{className:"flex-col items-start gap-1.5 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"dashboard-metrics.tsx",children:[(0,r.jsxs)("div",{className:"line-clamp-1 flex gap-2 font-medium",children:[(0,u.t)("dashboard.metrics.treatmentOptionsAvailable")," ",(0,r.jsx)(c.A,{className:"size-4","data-sentry-element":"IconStethoscope","data-sentry-source-file":"dashboard-metrics.tsx"})]}),(0,r.jsx)("div",{className:"text-muted-foreground",children:(0,u.t)("dashboard.metrics.fullServiceCatalog")})]})]})]})}},68290:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(49202).A)("outline","users","IconUsers",[["path",{d:"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"svg-2"}],["path",{d:"M21 21v-2a4 4 0 0 0 -3 -3.85",key:"svg-3"}]])},72486:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var r=(0,a(49202).A)("outline","calendar","IconCalendar",[["path",{d:"M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z",key:"svg-0"}],["path",{d:"M16 3v4",key:"svg-1"}],["path",{d:"M8 3v4",key:"svg-2"}],["path",{d:"M4 11h16",key:"svg-3"}],["path",{d:"M11 15h1",key:"svg-4"}],["path",{d:"M12 15v3",key:"svg-5"}]])},80726:(e,t,a)=>{Promise.resolve().then(a.bind(a,56582)),Promise.resolve().then(a.bind(a,90917))},85017:(e,t,a)=>{"use strict";a.d(t,{F:()=>i});var r=a(97921);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=r.$,i=(e,t)=>a=>{var r;if((null==t?void 0:t.variants)==null)return n(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:i,defaultVariants:d}=t,l=Object.keys(i).map(e=>{let t=null==a?void 0:a[e],r=null==d?void 0:d[e];if(null===t)return null;let n=s(t)||s(r);return i[e][n]}),o=a&&Object.entries(a).reduce((e,t)=>{let[a,r]=t;return void 0===r||(e[a]=r),e},{});return n(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:a,className:r,...s}=t;return Object.entries(s).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...d,...o}[t]):({...d,...o})[t]===a})?[...e,a,r]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}},86540:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,X9:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>m});var r=a(52880);a(99004);var s=a(54651);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...a,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function m(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},87378:(e,t,a)=>{"use strict";async function r(e,t){let a="/api".concat(e);try{let e=await fetch(a,{headers:{"Content-Type":"application/json",...null==t?void 0:t.headers},...t}),r=await e.json();if(!e.ok){if(r.error)throw Error(r.error);throw Error("API request failed: ".concat(e.status," ").concat(e.statusText))}return r}catch(e){throw console.error("API request to ".concat(a," failed:"),e),e}}a.d(t,{RG:()=>s,_M:()=>i,cU:()=>d,fJ:()=>n});let s={getAll:async e=>{let t=new URLSearchParams;if((null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.page)&&t.append("page",e.page.toString()),null==e?void 0:e.where){var a;(null==(a=e.where.patient)?void 0:a.equals)&&t.append("where[patient][equals]",e.where.patient.equals)}let s=t.toString()?"?".concat(t.toString()):"";return r("/appointments".concat(s))},getById:async e=>r("/appointments/".concat(e)),create:async e=>r("/appointments",{method:"POST",body:JSON.stringify(e)}),update:async(e,t)=>r("/appointments/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),delete:async e=>r("/appointments/".concat(e),{method:"DELETE"})},n={getAll:async e=>{let t=new URLSearchParams;(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.search)&&t.append("where[or][0][fullName][contains]",e.search);let a=t.toString()?"?".concat(t.toString()):"";return r("/patients".concat(a))},getById:async e=>r("/patients/".concat(e)),create:async e=>r("/patients",{method:"POST",body:JSON.stringify(e)}),update:async(e,t)=>r("/patients/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),delete:async e=>r("/patients/".concat(e),{method:"DELETE"})},i={getAll:async e=>{let t=new URLSearchParams;(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.page)&&t.append("page",e.page.toString());let a=t.toString()?"?".concat(t.toString()):"";return r("/treatments".concat(a))},getById:async e=>r("/treatments/".concat(e)),create:async e=>r("/treatments",{method:"POST",body:JSON.stringify(e)}),update:async(e,t)=>r("/treatments/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),delete:async e=>r("/treatments/".concat(e),{method:"DELETE"})},d=async()=>{try{let e=new Date().toISOString().split("T")[0],t=(await s.getAll({limit:1e3})).docs.filter(t=>t.appointmentDate.startsWith(e)).length,a=new Date;a.setDate(a.getDate()-7);let r=await n.getAll({limit:1e3}),d=r.docs.filter(e=>new Date(e.createdAt)>=a).length,l=await i.getAll({limit:1e3});return{todayAppointments:t,recentPatients:d,totalPatients:r.totalDocs,activetreatments:l.totalDocs}}catch(e){return console.error("Failed to fetch dashboard metrics:",e),{todayAppointments:0,recentPatients:0,totalPatients:0,activetreatments:0}}}},88151:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var r=a(52880);a(99004);var s=a(50516),n=a(85017),i=a(54651);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:n=!1,...l}=e,o=n?s.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(d({variant:a}),t),...l,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},90917:(e,t,a)=>{"use strict";a.d(t,{$:()=>d,ScrollArea:()=>i});var r=a(52880);a(99004);var s=a(71359),n=a(54651);function i(e){let{className:t,children:a,...i}=e;return(0,r.jsxs)(s.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",t),...i,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,r.jsx)(s.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:a}),(0,r.jsx)(d,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,r.jsx)(s.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function d(e){let{className:t,orientation:a="vertical",...i}=e;return(0,r.jsx)(s.VM,{"data-slot":"scroll-area-scrollbar",orientation:a,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===a&&"h-full w-2.5 border-l border-l-transparent","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent",t),...i,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,r.jsx)(s.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,1359,9442,4579,9253,7358],()=>t(80726)),_N_E=e.O()}]);