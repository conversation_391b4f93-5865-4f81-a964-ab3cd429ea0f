{"version": 3, "file": "../pages/_error.js", "mappings": "qhBAoBA,qCAAwBA,aAnBuC,OAgBzDC,EAAuC,KAAO,EAC9CC,EADkDC,KACV,EADUA,EACNC,OAE1BJ,CAHUK,CAGCC,CAAsB,CAF7BD,EAD2C,GACZ,EAGzD,GAAM,aAAEE,CAAW,yBAAEC,CAAuB,CAAE,CAAGF,EAEjD,SAASG,IACP,GAAIF,GAAeA,EAAYG,gBAAgB,CAAE,CAC/C,IAAMC,EAAeC,EAAAA,QAAQ,CAACC,OAAO,CACnCC,MAAMC,IAAI,CAACR,EAAYG,gBAAgB,EAA0BM,MAAM,CACrEC,UAGJV,EAAYW,UAAU,CAACV,EAAwBG,EAAcL,GAC/D,CACF,CA2CA,OAxCEC,MAAAA,CAAAA,EAAAA,GAAAA,GAAAA,CAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EACjDX,IAGFR,EAA0B,SACxBM,EACA,OADAA,MAAAA,CAAAA,EAAAA,GAAAA,GAAAA,GAAAA,EAAaG,gBAAAA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EAC1C,SACLb,CAAAA,OAAAA,GAAAA,EAAAA,KAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+Bc,MAAM,CAACf,EAAMc,QAAQ,CACtD,CACF,GAOAnB,EAA0B,KACpBM,IACFA,EAAYe,OADG,OACW,CAAGb,CAAAA,EAExB,KACDF,IACFA,EAAYe,OADG,OACW,CAAGb,CAAAA,CAEjC,IAGFP,EAAoB,KACdK,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,MAExB,KACDf,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,KAEjC,IAGK,IACT,YC5EA,0ICWSC,qCAAAA,KAXT,IAAIA,EAAYC,IAAe,4ZCS/B,MAAe,OAAK,CAAC,EAAQ,UAAY,CAElC,CAFmC,CAEZ,OAAK,CAAC,EAAQ,kBACrC,EAAuB,OAAK,CAAC,EAAQ,kBADA,EAEV,OAAK,CAAC,EAAQ,kBADJ,IAErC,EAAe,OAAK,CAAC,EAAQ,UAC7B,EAAwB,EAFiB,CAEjB,IAAK,CAAC,EAAQ,mBAEtC,EAAgC,OAAK,CAAC,EAAQ,iBAFR,UAGtC,EAAgC,OAAK,CAAC,EAAQ,SADA,kBAE9C,EAAiC,OAAK,CAAC,EAAQ,SADD,mBAE9C,EAAgC,OAAK,CAAC,EAAQ,QADC,mBAE/C,EAAoC,OAAK,CAAC,EAAQ,SADJ,sBAG9C,MAAwB,WAF0B,OAEV,EAC/C,YACA,KAAc,GAAS,OACvB,eACA,mBAEA,cACA,WACA,CAAK,CACL,YAEA,IAAa,IACb,SAAkB,GAClB,CAAK,CACL,QAAY,EACZ,CAAC,aCtCD,iBDkCwB,CClCxB,iBDmCkC,EEnClC,mNCiCqBC,sCAjCH,YAWkB,OAcpC,eAAeC,EAAmB,CAGrB,EAHqB,cAChCC,CAAS,KACTC,CAAG,CACQ,CAHqB,EAKhC,MAAO,CAAEC,UADS,MAAMC,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBH,EAAWC,EACpC,CACrB,CAEe,MAAMH,UAAsCM,EAAAA,OAAK,CAACJ,SAAS,CAOxEK,QAAS,CACP,GAAM,WAAEL,CAAS,WAAEE,CAAS,CAAE,CAAG,IAAI,CAACvB,KAAK,CAE3C,MAAO,UAACqB,EAAAA,CAAW,GAAGE,CAAS,EACjC,CACF,CAZqBJ,EAIZQ,mBAAAA,CAAsBP,EAJVD,EAKZS,eAAAA,CAAkBR,0OCrC3B,+CAAiF,gBCSjF,4BAAwC,CACxC,cACA,eACA,OAGA,uBAEA,OACA,KAIA,sCACA,kBAIA,oCACA,QAIA,CAnBA,CACA,CAAC,EAAC,wJCwLF,OAAmB,mBAAnB,GA1LgBS,WAAW,mBAAXA,iDAX4B,gBACzB,WACa,WACG,WACP,OAOrB,SAASA,EAAYC,CAAiB,EAAjBA,KAAAA,IAAAA,IAAAA,GAAY,GACtC,IAAMC,EAAO,CAAC,UAACC,OAAAA,CAAKC,QAAQ,SAAY,WAAa,CAMrD,OALI,GACFF,EAAKG,IAAI,CACP,CAFY,EAEZ,OAACF,OAAAA,CAAKG,KAAK,WAAWC,QAAQ,sBAAyB,aAGpDL,CACT,CAEA,SAASM,EACPC,CAAoC,CACpCC,CAA2C,QAG3C,UAAI,OAAOA,GAAsB,UAA2B,OAApBA,EAC/BD,EAGLC,EAAMC,IAAI,GAAKf,EAAAA,OAAK,CAACgB,QAAQ,CACxBH,CAD0B,CACrBI,MAAM,CAChB,EACAjB,OAAK,CAACnB,QAAQ,CAACC,OAAO,CAACgC,EAAMvC,KAAK,CAACc,QAAQ,EAAE6B,MAAM,CACjD,CAEEC,EACAC,IAEA,UACE,OAAOA,GACkB,UACzB,OADOA,EAEAD,EAEFA,EAAaF,MAAM,CAACG,GAE7B,EAAE,GAIDP,EAAKI,MAAM,CAACH,EACrB,GA/CyB,OAiDzB,IAAMO,EAAY,CAAC,OAAQ,YAAa,UAAW,WAAW,CAsE9D,SAASC,EACPC,CAAoD,CACpDhD,CAAQ,EAER,GAAM,CAAE8B,WAAS,CAAE,CAAG9B,EACtB,OAAOgD,EACJL,MAAM,CAACN,EAAkB,EAAE,EAC3BY,OAAO,GACPP,MAAM,CAACb,EAAYC,GAAWmB,OAAO,IACrCvC,MAAM,CAACwC,SAxEHA,EACP,IAAMC,EAAO,IAAIC,IACXC,EAAO,IAAID,IACXE,EAAY,IAAIF,IAChBG,EAAsD,CAAC,EAE7D,OAAQC,IACN,IAAIC,GAAW,EACXC,GAAS,EAEb,GAAIF,EAAEG,GAAG,EAAqB,UAAjB,OAAOH,EAAEG,GAAG,EAAiBH,EAAEG,GAAG,CAACC,OAAO,CAAC,KAAO,EAAG,CAChEF,GAAS,EACT,IAAMC,EAAMH,EAAEG,GAAG,CAACE,KAAK,CAACL,EAAEG,GAAG,CAACC,OAAO,CAAC,KAAO,GACzCT,EAAKW,GAAG,CAACH,GACXF,GAAW,EAEXN,EAAKtC,GAAG,CAAC8C,EAEb,CAGA,OAAQH,EAAEhB,IAAI,EACZ,IAAK,QACL,IAAK,OACCa,EAAKS,GAAG,CAACN,EAAEhB,IAAI,EACjBiB,CADoB,EACT,EAEXJ,EAAKxC,GAAG,CAAC2C,EAAEhB,IAAI,EAEjB,KACF,KAAK,OACH,IAAK,IAAIuB,EAAI,EAAGC,EAAMlB,EAAUmB,MAAM,CAAEF,EAAIC,EAAKD,IAAK,CACpD,IAAMG,EAAWpB,CAAS,CAACiB,EAAE,CAC7B,GAAKP,CAAD,CAAGxD,KAAK,CAACmE,cAAc,CAACD,GAE5B,GAAIA,KAFmC,MAEX,GACtBZ,EAAUQ,GAAG,CAACI,GAChBT,GAAW,EAEXH,EAAUzC,CAHiB,EAGd,CAACqD,OAEX,CACL,IAAME,EAAWZ,EAAExD,KAAK,CAACkE,EAAS,CAC5BG,EAAad,CAAc,CAACW,EAAS,EAAI,IAAId,GAC9Cc,EAAa,SAAbA,GAAuB,CAACR,CAAAA,CAAAA,CAAK,CAAMW,EAAWP,GAAG,CAACM,GACrDX,GAAW,GAEXY,EAHgE,GAGlD,CAACD,GACfb,CAAc,CAACW,EAAS,CAAGG,EAE/B,CACF,CAEJ,CAEA,OAAOZ,CACT,CACF,KAgBKR,OAAO,GACPqB,GAAG,CAAC,CAACC,EAA4BR,KAChC,IAAMJ,EAAMY,EAAEZ,GAAG,EAAII,EACrB,GACES,CAAAA,GAAoB,IACZC,GAAG,CAACC,qBAAqB,EACjC,CAAC5C,GAGY,QAFb,CAEEyC,EAAE/B,IAAI,EACN+B,EAAEvE,KAAK,CAAC,IAAO,EAEf,CAAC,CADD,kCACqC,2BAA2B,CAAC2E,IAAI,CACnE,GAASJ,EAAEvE,KAAK,CAAC,IAAO,CAAC4E,OAF+D,GAErD,CAACC,IAEtC,CACA,IAAMC,EAAW,CAAE,GAAIP,EAAEvE,KAAK,EAAI,CAAC,CAAC,EAOpC,OANA8E,CAAQ,CAAC,YAAY,CAAGA,EAAS,IAAO,CACxCA,CADgC,CACvB,IAAO,EAAR,IAAWC,EAGnBD,CAAQ,CAAC,uBAAuB,EAAG,EAE5BrD,EAAAA,OAAK,CAACuD,YAAY,CAACT,EAAGO,EAC/B,CAiBF,OAAOrD,EAAAA,OAAK,CAACuD,YAAY,CAACT,EAAG,KAAEZ,CAAI,EACrC,EACJ,KAoBA,EAdA,SAASsB,CAAgD,EAA3C,aAAEnE,CAAQ,CAAiC,CAA3C,EACNoE,EAAWC,CAAAA,EAAAA,EAAAA,UAAAA,EAAWC,EAAAA,eAAe,EACrCnF,EAAckF,CAAAA,EAAAA,EAAAA,UAAAA,EAAWE,EAAAA,kBAAkB,EACjD,MACE,UAACC,EAAAA,OAAM,EACLpF,wBAAyB6C,EACzB9C,YAAaA,EACb6B,UAAWyD,CAAAA,EAAAA,EAAAA,WAAAA,EAAYL,YAEtBpE,GAGP,oOCrMA,uDCEA,cACA,0CAEA,kBACA,cAEA,qBACA,YACA,EAAK,GACL,CA0BA,GAAS,CAzBT,cACA,gCACA,6DAAuF,WAEvF,WAEA,+BAEA,OAAmB,gBACnB,yDAEA,eACA,6DACA,iDACA,gDACA,UAQA,OAJA,YAEA,cAEA,CACA,mBCnCA,uDAAyF,gBCAzF,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAQF,cACA,0CACA,cACA,SACK,CACL,EACA,GACA,6BACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,6BACA,QACA,CAAK,CACL,0BACA,QACA,CACA,CAAC,EACD,4CACA,gBACA,eACA,+BACA,CACA,gBAEA,OADA,OACA,CACA,CACA,kBACA,WAEA,OADA,OACA,MACA,CACA,gBACA,WAEA,OADA,YACA,MACA,2IC4BqB0E,sCAhFH,gBACD,QAGXC,EAA0C,CAC9C,IAAK,cACL,IAAK,+BACL,IAAK,qBACL,IAAK,uBACP,EASA,SAASC,EAAiB,CAIR,EAJQ,IAQpBC,EARoB,KACxBC,CAAG,KACHC,CAAG,KACHC,CAAG,CACa,CAJQ,EAKlBC,EACJF,GAAOA,EAAIE,UAAU,CAAGF,EAAIE,UAAU,CAAGD,EAAMA,EAAIC,UAAU,CAAI,IAM5D,GAAIH,EAAK,CACd,GAAM,gBAAEI,CAAc,CAAE,CACtBC,EAAQ,KAAwB,EAE5BC,EAAUF,EAAeJ,EAAK,IAF3BK,OAGLC,IAEFP,EADY,GADD,CACKQ,IAAID,GACLP,QAAAA,CAEnB,CAEA,MAAO,YAAEI,WAAYJ,CAAS,CAChC,CAEA,IAAMS,EAA8C,CAClDC,MAAO,CAELC,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,WAAY,MACd,EACAC,GAAI,CACFN,QAAS,eACTO,OAAQ,aACRC,aAAc,GACdC,SAAU,GACVC,WAAY,IACZC,cAAe,KACjB,EACAC,GAAI,CACFH,SAAU,GACVC,WAAY,IACZL,WAAY,MACd,EACAQ,KAAM,CACJb,QAAS,cACX,CACF,CAKe,OAAMjB,UAAsB/D,EAAAA,OAAK,CAACJ,SAAS,CAMxDK,QAAS,CACP,GAAM,YAAEqE,CAAU,cAAEwB,GAAe,CAAI,CAAE,CAAG,IAAI,CAACvH,KAAK,CAChDwH,EACJ,IAAI,CAACxH,KAAK,CAACwH,KAAK,EAChB/B,CAAW,CAACM,EAAW,EACvB,mCAEF,MACE,WAAC0B,MAAAA,CAAIC,MAAOtB,EAAOC,KAAK,WACtB,UAACpB,EAAAA,OAAI,WACH,UAACuC,QAAAA,UACEzB,EACMA,EAAW,KAAIyB,EAClB,8DAGR,WAACC,MAAAA,CAAIC,MAAOtB,EAAOS,IAAI,WACrB,UAACa,QAAAA,CACCC,wBAAyB,CAkBvBC,OAAS,kGACPL,CAAAA,CACI,kIACA,GAER,CAFS,GAKVxB,EACC,UAACgB,CADFhB,IACEgB,CAAGc,MADL9B,IACe,gBAAgB2B,MAAOtB,EAAOW,EAAE,UAC3ChB,IAED,KACJ,UAAC0B,MAAAA,CAAIC,MAAOtB,EAAOkB,IAAI,UACrB,WAACD,KAAAA,CAAGK,MAAOtB,EAAOiB,EAAE,WACjB,IAAI,CAACrH,KAAK,CAACwH,KAAK,EAAIzB,EACnByB,EAEA,iBAFAA,IAEA,YAAE,0DACwD,KACvD7G,CAAQ,IAAI,CAACX,KAAK,CAAC2F,QAAQ,EAC1B,cAD0B,OAC1B,YAAE,iBAAe,IAAI,CAAC3F,KAAK,CAAC2F,QAAQ,IACnC,IAAI,oDAGT,cAOd,CACF,CA3EqBH,EACZsC,WAAAA,CAAc,YADFtC,EAGZ5D,eAAAA,CAAkB8D,EAHNF,EAIZ7D,mBAAAA,CAAsB+D,wOCpFxB,SAASH,EAAY,gBAC1BwC,GAAW,CAAK,QAChBC,GAAS,CAAK,UACdC,GAAW,CAAK,CACjB,CAJ2B,WAIxB,CAAC,EAJuB,EAK1B,OAAOF,GAAaC,GAAUC,CAChC,wFANgB1C,qCAAAA,gBCAhB,0CCIA,4BAA4C,CAC5C,cACA,eACA,QACA,CACA,CAAC,EACD,kBAkBA,OAfA,gBAGA,wBAIA,sBAIA,wBAGA,gBACA,CACA,CAAC,GAAG", "sources": ["webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/side-effect.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"react/jsx-runtime\"", "webpack://next-shadcn-dashboard-starter/../../../../src/shared/lib/utils/warn-once.ts", "webpack://next-shadcn-dashboard-starter/?17ba", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/pages.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/../../src/pages/_app.tsx", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/route-modules/pages/vendored/contexts/amp-context.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/build/templates/helpers.js", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/head.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"@opentelemetry/api\"", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/route-modules/pages/vendored/contexts/head-manager-context.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/request-meta.js", "webpack://next-shadcn-dashboard-starter/../../src/pages/_error.tsx", "webpack://next-shadcn-dashboard-starter/../../../src/shared/lib/amp-mode.ts", "webpack://next-shadcn-dashboard-starter/external commonjs \"react\"", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/route-kind.js"], "sourcesContent": ["import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n", "module.exports = require(\"react/jsx-runtime\");", "let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n", "import { PagesRouteModule } from \"next/dist/server/route-modules/pages/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { hoist } from \"next/dist/build/templates/helpers\";\n// Import the app and document modules.\nimport * as document from \"next/dist/pages/_document\";\nimport * as app from \"next/dist/pages/_app\";\n// Import the userland code.\nimport * as userland from \"next/dist/pages/_error\";\n// Re-export the component (should be the default export).\nexport default hoist(userland, 'default');\n// Re-export methods.\nexport const getStaticProps = hoist(userland, 'getStaticProps');\nexport const getStaticPaths = hoist(userland, 'getStaticPaths');\nexport const getServerSideProps = hoist(userland, 'getServerSideProps');\nexport const config = hoist(userland, 'config');\nexport const reportWebVitals = hoist(userland, 'reportWebVitals');\n// Re-export legacy methods.\nexport const unstable_getStaticProps = hoist(userland, 'unstable_getStaticProps');\nexport const unstable_getStaticPaths = hoist(userland, 'unstable_getStaticPaths');\nexport const unstable_getStaticParams = hoist(userland, 'unstable_getStaticParams');\nexport const unstable_getServerProps = hoist(userland, 'unstable_getServerProps');\nexport const unstable_getServerSideProps = hoist(userland, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesRouteModule({\n    definition: {\n        kind: RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: app.default,\n        Document: document.default\n    },\n    userland\n});\n\n//# sourceMappingURL=pages.js.map", "module.exports = require(\"path\");", "module.exports = require(\"next/dist/compiled/next-server/pages.runtime.prod.js\");", "import React from 'react'\n\nimport type {\n  AppContextType,\n  AppInitialProps,\n  AppPropsType,\n  NextWebVitalsMetric,\n  AppType,\n} from '../shared/lib/utils'\nimport type { Router } from '../client/router'\n\nimport { loadGetInitialProps } from '../shared/lib/utils'\n\nexport type { AppInitialProps, AppType }\n\nexport type { NextWebVitalsMetric }\n\nexport type AppContext = AppContextType<Router>\n\nexport type AppProps<P = any> = AppPropsType<Router, P>\n\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */\nasync function appGetInitialProps({\n  Component,\n  ctx,\n}: AppContext): Promise<AppInitialProps> {\n  const pageProps = await loadGetInitialProps(Component, ctx)\n  return { pageProps }\n}\n\nexport default class App<P = any, CP = {}, S = {}> extends React.Component<\n  P & AppProps<CP>,\n  S\n> {\n  static origGetInitialProps = appGetInitialProps\n  static getInitialProps = appGetInitialProps\n\n  render() {\n    const { Component, pageProps } = this.props as AppProps<CP>\n\n    return <Component {...pageProps} />\n  }\n}\n", "\"use strict\";\nmodule.exports = require('../../module.compiled').vendored['contexts'].AmpContext;\n\n//# sourceMappingURL=amp-context.js.map", "/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */ \"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"hoist\", {\n    enumerable: true,\n    get: function() {\n        return hoist;\n    }\n});\nfunction hoist(module, name) {\n    // If the name is available in the module, return it.\n    if (name in module) {\n        return module[name];\n    }\n    // If a property called `then` exists, assume it's a promise and\n    // return a promise that resolves to the name.\n    if ('then' in module && typeof module.then === 'function') {\n        return module.then((mod)=>hoist(mod, name));\n    }\n    // If we're trying to hoise the default export, and the module is a function,\n    // return the module itself.\n    if (typeof module === 'function' && name === 'default') {\n        return module;\n    }\n    // Otherwise, return undefined.\n    return undefined;\n}\n\n//# sourceMappingURL=helpers.js.map", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (\n        process.env.NODE_ENV !== 'development' &&\n        process.env.__NEXT_OPTIMIZE_FONTS &&\n        !inAmpMode\n      ) {\n        if (\n          c.type === 'link' &&\n          c.props['href'] &&\n          // TODO(prateekbh@): Replace this with const from `constants` when the tree shaking works.\n          ['https://fonts.googleapis.com/css', 'https://use.typekit.net/'].some(\n            (url) => c.props['href'].startsWith(url)\n          )\n        ) {\n          const newProps = { ...(c.props || {}) }\n          newProps['data-href'] = newProps['href']\n          newProps['href'] = undefined\n\n          // Add this attribute to make it easy to identify optimized tags\n          newProps['data-optimized-fonts'] = true\n\n          return React.cloneElement(c, newProps)\n        }\n      }\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n", "module.exports = require(\"@opentelemetry/api\");", "\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n", "\"use strict\";\nmodule.exports = require('../../module.compiled').vendored['contexts'].HeadManagerContext;\n\n//# sourceMappingURL=head-manager-context.js.map", "/* eslint-disable no-redeclare */ \"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    NEXT_REQUEST_META: null,\n    addRequestMeta: null,\n    getRequestMeta: null,\n    removeRequestMeta: null,\n    setRequestMeta: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    NEXT_REQUEST_META: function() {\n        return NEXT_REQUEST_META;\n    },\n    addRequestMeta: function() {\n        return addRequestMeta;\n    },\n    getRequestMeta: function() {\n        return getRequestMeta;\n    },\n    removeRequestMeta: function() {\n        return removeRequestMeta;\n    },\n    setRequestMeta: function() {\n        return setRequestMeta;\n    }\n});\nconst NEXT_REQUEST_META = Symbol.for('NextInternalRequestMeta');\nfunction getRequestMeta(req, key) {\n    const meta = req[NEXT_REQUEST_META] || {};\n    return typeof key === 'string' ? meta[key] : meta;\n}\nfunction setRequestMeta(req, meta) {\n    req[NEXT_REQUEST_META] = meta;\n    return meta;\n}\nfunction addRequestMeta(request, key, value) {\n    const meta = getRequestMeta(request);\n    meta[key] = value;\n    return setRequestMeta(request, meta);\n}\nfunction removeRequestMeta(request, key) {\n    const meta = getRequestMeta(request);\n    delete meta[key];\n    return setRequestMeta(request, meta);\n}\n\n//# sourceMappingURL=request-meta.js.map", "import React from 'react'\nimport Head from '../shared/lib/head'\nimport type { NextPageContext } from '../shared/lib/utils'\n\nconst statusCodes: { [code: number]: string } = {\n  400: 'Bad Request',\n  404: 'This page could not be found',\n  405: 'Method Not Allowed',\n  500: 'Internal Server Error',\n}\n\nexport type ErrorProps = {\n  statusCode: number\n  hostname?: string\n  title?: string\n  withDarkMode?: boolean\n}\n\nfunction _getInitialProps({\n  req,\n  res,\n  err,\n}: NextPageContext): Promise<ErrorProps> | ErrorProps {\n  const statusCode =\n    res && res.statusCode ? res.statusCode : err ? err.statusCode! : 404\n\n  let hostname\n\n  if (typeof window !== 'undefined') {\n    hostname = window.location.hostname\n  } else if (req) {\n    const { getRequestMeta } =\n      require('../server/request-meta') as typeof import('../server/request-meta')\n\n    const initUrl = getRequestMeta(req, 'initURL')\n    if (initUrl) {\n      const url = new URL(initUrl)\n      hostname = url.hostname\n    }\n  }\n\n  return { statusCode, hostname }\n}\n\nconst styles: Record<string, React.CSSProperties> = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  desc: {\n    lineHeight: '48px',\n  },\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    paddingRight: 23,\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top',\n  },\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '28px',\n  },\n  wrap: {\n    display: 'inline-block',\n  },\n}\n\n/**\n * `Error` component used for handling errors.\n */\nexport default class Error<P = {}> extends React.Component<P & ErrorProps> {\n  static displayName = 'ErrorPage'\n\n  static getInitialProps = _getInitialProps\n  static origGetInitialProps = _getInitialProps\n\n  render() {\n    const { statusCode, withDarkMode = true } = this.props\n    const title =\n      this.props.title ||\n      statusCodes[statusCode] ||\n      'An unexpected error has occurred'\n\n    return (\n      <div style={styles.error}>\n        <Head>\n          <title>\n            {statusCode\n              ? `${statusCode}: ${title}`\n              : 'Application error: a client-side exception has occurred'}\n          </title>\n        </Head>\n        <div style={styles.desc}>\n          <style\n            dangerouslySetInnerHTML={{\n              /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */\n              __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}${\n                withDarkMode\n                  ? '@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}'\n                  : ''\n              }`,\n            }}\n          />\n\n          {statusCode ? (\n            <h1 className=\"next-error-h1\" style={styles.h1}>\n              {statusCode}\n            </h1>\n          ) : null}\n          <div style={styles.wrap}>\n            <h2 style={styles.h2}>\n              {this.props.title || statusCode ? (\n                title\n              ) : (\n                <>\n                  Application error: a client-side exception has occurred{' '}\n                  {Boolean(this.props.hostname) && (\n                    <>while loading {this.props.hostname}</>\n                  )}{' '}\n                  (see the browser console for more information)\n                </>\n              )}\n              .\n            </h2>\n          </div>\n        </div>\n      </div>\n    )\n  }\n}\n", "export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n", "module.exports = require(\"react\");", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"RouteKind\", {\n    enumerable: true,\n    get: function() {\n        return RouteKind;\n    }\n});\nvar RouteKind = /*#__PURE__*/ function(RouteKind) {\n    /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */ RouteKind[\"PAGES\"] = \"PAGES\";\n    /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */ RouteKind[\"PAGES_API\"] = \"PAGES_API\";\n    /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */ RouteKind[\"APP_PAGE\"] = \"APP_PAGE\";\n    /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */ RouteKind[\"APP_ROUTE\"] = \"APP_ROUTE\";\n    /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */ RouteKind[\"IMAGE\"] = \"IMAGE\";\n    return RouteKind;\n}({});\n\n//# sourceMappingURL=route-kind.js.map"], "names": ["SideEffect", "useClientOnlyLayoutEffect", "useClientOnlyEffect", "useLayoutEffect", "useEffect", "isServer", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "Children", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate", "warnOnce", "_", "App", "appGetInitialProps", "Component", "ctx", "pageProps", "loadGetInitialProps", "React", "render", "origGetInitialProps", "getInitialProps", "defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "type", "Fragment", "concat", "reduce", "fragmentList", "fragmentChild", "METATYPES", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "h", "isUnique", "<PERSON><PERSON><PERSON>", "key", "indexOf", "slice", "has", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "map", "c", "process", "env", "__NEXT_OPTIMIZE_FONTS", "some", "startsWith", "url", "newProps", "undefined", "cloneElement", "Head", "ampState", "useContext", "AmpStateContext", "HeadManagerContext", "Effect", "isInAmpMode", "Error", "statusCodes", "_getInitialProps", "hostname", "req", "res", "err", "statusCode", "getRequestMeta", "require", "initUrl", "URL", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "lineHeight", "h1", "margin", "paddingRight", "fontSize", "fontWeight", "verticalAlign", "h2", "wrap", "withDarkMode", "title", "div", "style", "dangerouslySetInnerHTML", "__html", "className", "displayName", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}