import { http, HttpResponse } from 'msw'
import {
  mockAppointments,
  mockPatients,
  mockTreatments,
  mockUsers,
  mockBills,
  mockBillItems,
  mockPayments,
  mockDeposits
} from './data'

export const handlers = [
  // Appointments endpoints
  http.get('/api/appointments', () => {
    return HttpResponse.json({
      docs: mockAppointments,
      totalDocs: mockAppointments.length,
      limit: 10,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
    })
  }),

  http.get('/api/appointments/:id', ({ params }) => {
    const appointment = mockAppointments.find(a => a.id === params.id)
    if (!appointment) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(appointment)
  }),

  http.post('/api/appointments', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newAppointment = {
      id: `appointment-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newAppointment, { status: 201 })
  }),

  http.put('/api/appointments/:id', async ({ params, request }) => {
    const body = await request.json() as Record<string, any>
    const appointment = mockAppointments.find(a => a.id === params.id)
    if (!appointment) {
      return new HttpResponse(null, { status: 404 })
    }
    const updatedAppointment = {
      ...appointment,
      ...body,
      updatedAt: new Date().toISOString(),
    }
    return HttpResponse.json(updatedAppointment)
  }),

  http.delete('/api/appointments/:id', ({ params }) => {
    const appointment = mockAppointments.find(a => a.id === params.id)
    if (!appointment) {
      return new HttpResponse(null, { status: 404 })
    }
    return new HttpResponse(null, { status: 204 })
  }),

  // Patients endpoints
  http.get('/api/patients', () => {
    return HttpResponse.json({
      docs: mockPatients,
      totalDocs: mockPatients.length,
      limit: 10,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
    })
  }),

  http.get('/api/patients/:id', ({ params }) => {
    const patient = mockPatients.find(p => p.id === params.id)
    if (!patient) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(patient)
  }),

  http.post('/api/patients', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newPatient = {
      id: `patient-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newPatient, { status: 201 })
  }),

  http.put('/api/patients/:id', async ({ params, request }) => {
    const body = await request.json() as Record<string, any>
    const patient = mockPatients.find(p => p.id === params.id)
    if (!patient) {
      return new HttpResponse(null, { status: 404 })
    }
    const updatedPatient = {
      ...patient,
      ...body,
      updatedAt: new Date().toISOString(),
    }
    return HttpResponse.json(updatedPatient)
  }),

  http.delete('/api/patients/:id', ({ params }) => {
    const patient = mockPatients.find(p => p.id === params.id)
    if (!patient) {
      return new HttpResponse(null, { status: 404 })
    }
    return new HttpResponse(null, { status: 204 })
  }),

  // Treatments endpoints
  http.get('/api/treatments', () => {
    return HttpResponse.json({
      docs: mockTreatments,
      totalDocs: mockTreatments.length,
      limit: 10,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
    })
  }),

  http.get('/api/treatments/:id', ({ params }) => {
    const treatment = mockTreatments.find(t => t.id === params.id)
    if (!treatment) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(treatment)
  }),

  http.post('/api/treatments', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newTreatment = {
      id: `treatment-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newTreatment, { status: 201 })
  }),

  http.put('/api/treatments/:id', async ({ params, request }) => {
    const body = await request.json() as Record<string, any>
    const treatment = mockTreatments.find(t => t.id === params.id)
    if (!treatment) {
      return new HttpResponse(null, { status: 404 })
    }
    const updatedTreatment = {
      ...treatment,
      ...body,
      updatedAt: new Date().toISOString(),
    }
    return HttpResponse.json(updatedTreatment)
  }),

  http.delete('/api/treatments/:id', ({ params }) => {
    const treatment = mockTreatments.find(t => t.id === params.id)
    if (!treatment) {
      return new HttpResponse(null, { status: 404 })
    }
    return new HttpResponse(null, { status: 204 })
  }),

  // Users endpoints
  http.get('/api/users', () => {
    return HttpResponse.json({
      docs: mockUsers,
      totalDocs: mockUsers.length,
      limit: 10,
      totalPages: 1,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
    })
  }),

  // Auth sync endpoint
  http.post('/api/auth/sync', () => {
    return HttpResponse.json({ success: true })
  }),

  // Bills endpoints
  http.get('/api/bills', ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const status = url.searchParams.get('status')
    const patientId = url.searchParams.get('patientId')
    const search = url.searchParams.get('search')

    let filteredBills = [...mockBills]

    // Apply filters
    if (status) {
      filteredBills = filteredBills.filter(bill => bill.status === status)
    }
    if (patientId) {
      filteredBills = filteredBills.filter(bill => bill.patientId === patientId)
    }
    if (search) {
      filteredBills = filteredBills.filter(bill =>
        bill.billNumber.toLowerCase().includes(search.toLowerCase()) ||
        bill.description.toLowerCase().includes(search.toLowerCase()) ||
        bill.patient?.fullName.toLowerCase().includes(search.toLowerCase())
      )
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedBills = filteredBills.slice(startIndex, endIndex)

    return HttpResponse.json({
      docs: paginatedBills,
      totalDocs: filteredBills.length,
      limit,
      totalPages: Math.ceil(filteredBills.length / limit),
      page,
      pagingCounter: startIndex + 1,
      hasPrevPage: page > 1,
      hasNextPage: endIndex < filteredBills.length,
    })
  }),

  http.get('/api/bills/:id', ({ params }) => {
    const bill = mockBills.find(b => b.id === params.id)
    if (!bill) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(bill)
  }),

  http.post('/api/bills', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newBill = {
      id: `bill-${Date.now()}`,
      billNumber: `BILL-2024-${String(mockBills.length + 1).padStart(3, '0')}`,
      status: 'draft',
      remainingAmount: body.totalAmount || 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newBill, { status: 201 })
  }),

  http.patch('/api/bills/:id', async ({ params, request }) => {
    const body = await request.json() as Record<string, any>
    const bill = mockBills.find(b => b.id === params.id)
    if (!bill) {
      return new HttpResponse(null, { status: 404 })
    }
    const updatedBill = {
      ...bill,
      ...body,
      updatedAt: new Date().toISOString(),
    }
    return HttpResponse.json(updatedBill)
  }),

  http.delete('/api/bills/:id', ({ params }) => {
    const bill = mockBills.find(b => b.id === params.id)
    if (!bill) {
      return new HttpResponse(null, { status: 404 })
    }
    return new HttpResponse(null, { status: 204 })
  }),

  // Bill Items endpoints
  http.get('/api/bill-items', ({ request }) => {
    const url = new URL(request.url)
    const billId = url.searchParams.get('billId')

    let filteredItems = [...mockBillItems]
    if (billId) {
      filteredItems = filteredItems.filter(item => item.billId === billId)
    }

    return HttpResponse.json({
      docs: filteredItems,
      totalDocs: filteredItems.length,
    })
  }),

  http.post('/api/bill-items', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newItem = {
      id: `bill-item-${Date.now()}`,
      createdAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newItem, { status: 201 })
  }),

  // Payments endpoints
  http.get('/api/payments', ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const billId = url.searchParams.get('billId')
    const patientId = url.searchParams.get('patientId')
    const status = url.searchParams.get('status')

    let filteredPayments = [...mockPayments]

    if (billId) {
      filteredPayments = filteredPayments.filter(payment => payment.billId === billId)
    }
    if (patientId) {
      filteredPayments = filteredPayments.filter(payment => payment.patientId === patientId)
    }
    if (status) {
      filteredPayments = filteredPayments.filter(payment => payment.paymentStatus === status)
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPayments = filteredPayments.slice(startIndex, endIndex)

    return HttpResponse.json({
      docs: paginatedPayments,
      totalDocs: filteredPayments.length,
      limit,
      totalPages: Math.ceil(filteredPayments.length / limit),
      page,
      pagingCounter: startIndex + 1,
      hasPrevPage: page > 1,
      hasNextPage: endIndex < filteredPayments.length,
    })
  }),

  http.get('/api/payments/:id', ({ params }) => {
    const payment = mockPayments.find(p => p.id === params.id)
    if (!payment) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(payment)
  }),

  http.post('/api/payments', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newPayment = {
      id: `payment-${Date.now()}`,
      paymentNumber: `PAY-2024-${String(mockPayments.length + 1).padStart(3, '0')}`,
      paymentStatus: 'completed',
      paymentDate: new Date().toISOString(),
      receiptNumber: `RCP-2024-${String(mockPayments.length + 1).padStart(3, '0')}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newPayment, { status: 201 })
  }),

  http.patch('/api/payments/:id', async ({ params, request }) => {
    const body = await request.json() as Record<string, any>
    const payment = mockPayments.find(p => p.id === params.id)
    if (!payment) {
      return new HttpResponse(null, { status: 404 })
    }
    const updatedPayment = {
      ...payment,
      ...body,
      updatedAt: new Date().toISOString(),
    }
    return HttpResponse.json(updatedPayment)
  }),

  // Deposits endpoints
  http.get('/api/deposits', ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const patientId = url.searchParams.get('patientId')
    const status = url.searchParams.get('status')

    let filteredDeposits = [...mockDeposits]

    if (patientId) {
      filteredDeposits = filteredDeposits.filter(deposit => deposit.patientId === patientId)
    }
    if (status) {
      filteredDeposits = filteredDeposits.filter(deposit => deposit.status === status)
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedDeposits = filteredDeposits.slice(startIndex, endIndex)

    return HttpResponse.json({
      docs: paginatedDeposits,
      totalDocs: filteredDeposits.length,
      limit,
      totalPages: Math.ceil(filteredDeposits.length / limit),
      page,
      pagingCounter: startIndex + 1,
      hasPrevPage: page > 1,
      hasNextPage: endIndex < filteredDeposits.length,
    })
  }),

  http.get('/api/deposits/:id', ({ params }) => {
    const deposit = mockDeposits.find(d => d.id === params.id)
    if (!deposit) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(deposit)
  }),

  http.post('/api/deposits', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const newDeposit = {
      id: `deposit-${Date.now()}`,
      depositNumber: `DEP-2024-${String(mockDeposits.length + 1).padStart(3, '0')}`,
      status: 'active',
      usedAmount: 0,
      remainingAmount: body.amount || 0,
      depositDate: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...body,
    }
    return HttpResponse.json(newDeposit, { status: 201 })
  }),

  http.patch('/api/deposits/:id', async ({ params, request }) => {
    const body = await request.json() as Record<string, any>
    const deposit = mockDeposits.find(d => d.id === params.id)
    if (!deposit) {
      return new HttpResponse(null, { status: 404 })
    }
    const updatedDeposit = {
      ...deposit,
      ...body,
      updatedAt: new Date().toISOString(),
    }
    return HttpResponse.json(updatedDeposit)
  }),

  // Special billing endpoints
  http.post('/api/bills/generate-from-appointment', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const appointment = mockAppointments.find(a => a.id === body.appointmentId)
    if (!appointment) {
      return new HttpResponse(null, { status: 404 })
    }

    const newBill = {
      id: `bill-${Date.now()}`,
      billNumber: `BILL-2024-${String(mockBills.length + 1).padStart(3, '0')}`,
      patientId: appointment.patient.id,
      appointmentId: appointment.id,
      treatmentId: appointment.treatment?.id || '',
      billType: 'treatment',
      status: 'draft',
      subtotal: appointment.price,
      discountAmount: 0,
      taxAmount: 0,
      totalAmount: appointment.price,
      remainingAmount: appointment.price,
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      description: `${appointment.treatment?.name || '治疗'}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 'test-user-id',
      ...body,
    }
    return HttpResponse.json(newBill, { status: 201 })
  }),

  http.post('/api/deposits/apply-to-bill', async ({ request }) => {
    const body = await request.json() as Record<string, any>
    const { depositId, billId, amount } = body

    const deposit = mockDeposits.find(d => d.id === depositId)
    const bill = mockBills.find(b => b.id === billId)

    if (!deposit || !bill) {
      return new HttpResponse(null, { status: 404 })
    }

    if (deposit.remainingAmount < amount) {
      return HttpResponse.json(
        { error: '预付款余额不足' },
        { status: 400 }
      )
    }

    // Simulate applying deposit to bill
    const updatedDeposit = {
      ...deposit,
      usedAmount: deposit.usedAmount + amount,
      remainingAmount: deposit.remainingAmount - amount,
      status: deposit.remainingAmount - amount === 0 ? 'used' : 'active',
      updatedAt: new Date().toISOString(),
    }

    const updatedBill = {
      ...bill,
      remainingAmount: bill.remainingAmount - amount,
      status: bill.remainingAmount - amount === 0 ? 'paid' : bill.status,
      updatedAt: new Date().toISOString(),
    }

    return HttpResponse.json({
      deposit: updatedDeposit,
      bill: updatedBill,
      appliedAmount: amount,
    })
  }),
]
