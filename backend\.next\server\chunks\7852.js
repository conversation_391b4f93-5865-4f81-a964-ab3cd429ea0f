"use strict";exports.id=7852,exports.ids=[5097,7852],exports.modules={22584:(e,t,n)=>{function r(e){return(t,n={})=>{let r,a=n.width,o=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],i=t.match(o);if(!i)return null;let l=i[0],s=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(s)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(s,e=>e.test(l)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(s,e=>e.test(l));return r=e.valueCallback?e.valueCallback(u):u,{value:r=n.valueCallback?n.valueCallback(r):r,rest:t.slice(l.length)}}}n.d(t,{A:()=>r})},30160:(e,t,n)=>{n.d(t,{D4:()=>c,F7:()=>g,Pe:()=>p,gd:()=>h,h1:()=>u,kg:()=>m,lF:()=>d,oE:()=>f});var r=n(32293);let a=Object.prototype.hasOwnProperty,o=Array.isArray,i=function(){let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),l=function(e){for(;e.length>1;){let t=e.pop(),n=t.obj[t.prop];if(o(n)){let e=[];for(let t=0;t<n.length;++t)void 0!==n[t]&&e.push(n[t]);t.obj[t.prop]=e}}},s=function(e,t){let n=t&&t.plainObjects?Object.create(null):{};for(let t=0;t<e.length;++t)void 0!==e[t]&&(n[t]=e[t]);return n},u=function e(t,n,r){if(!n)return t;if("object"!=typeof n){if(o(t))t.push(n);else{if(!t||"object"!=typeof t)return[t,n];(r&&(r.plainObjects||r.allowPrototypes)||!a.call(Object.prototype,n))&&(t[n]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(n);let i=t;return(o(t)&&!o(n)&&(i=s(t,r)),o(t)&&o(n))?(n.forEach(function(n,o){if(a.call(t,o)){let a=t[o];a&&"object"==typeof a&&n&&"object"==typeof n?t[o]=e(a,n,r):t.push(n)}else t[o]=n}),t):Object.keys(n).reduce(function(t,o){let i=n[o];return a.call(t,o)?t[o]=e(t[o],i,r):t[o]=i,t},i)},c=function(e,t,n){let r=e.replace(/\+/g," ");if("iso-8859-1"===n)return r.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(r)}catch(e){return r}},d=function(e,t,n,a,o){if(0===e.length)return e;let l=e;if("symbol"==typeof e?l=Symbol.prototype.toString.call(e):"string"!=typeof e&&(l=String(e)),"iso-8859-1"===n)return escape(l).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let s="";for(let e=0;e<l.length;e+=1024){let t=l.length>=1024?l.slice(e,e+1024):l,n=[];for(let e=0;e<t.length;++e){let a=t.charCodeAt(e);if(45===a||46===a||95===a||126===a||a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||o===r.j1&&(40===a||41===a)){n[n.length]=t.charAt(e);continue}if(a<128){n[n.length]=i[a];continue}if(a<2048){n[n.length]=i[192|a>>6]+i[128|63&a];continue}if(a<55296||a>=57344){n[n.length]=i[224|a>>12]+i[128|a>>6&63]+i[128|63&a];continue}e+=1,a=65536+((1023&a)<<10|1023&t.charCodeAt(e)),n[n.length]=i[240|a>>18]+i[128|a>>12&63]+i[128|a>>6&63]+i[128|63&a]}s+=n.join("")}return s},f=function(e){let t=[{obj:{o:e},prop:"o"}],n=[];for(let e=0;e<t.length;++e){let r=t[e],a=r.obj[r.prop],o=Object.keys(a);for(let e=0;e<o.length;++e){let r=o[e],i=a[r];"object"==typeof i&&null!==i&&-1===n.indexOf(i)&&(t.push({obj:a,prop:r}),n.push(i))}}return l(t),e},h=function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},p=function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},m=function(e,t){return[].concat(e,t)},g=function(e,t){if(o(e)){let n=[];for(let r=0;r<e.length;r+=1)n.push(t(e[r]));return n}return t(e)}},30804:(e,t,n)=>{n.d(t,{w:()=>a});var r=n(78904);function a(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&r._P in e?e[r._P](t):e instanceof Date?new e.constructor(t):new Date(t)}},31456:(e,t,n)=>{n.d(t,{x:()=>a});var r=n(30804);function a(e,...t){let n=r.w.bind(null,e||t.find(e=>"object"==typeof e));return t.map(n)}},32293:(e,t,n)=>{n.d(t,{Ay:()=>s,_J:()=>i,j1:()=>l});let r=String.prototype.replace,a=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"},i={RFC1738:function(e){return r.call(e,a,"+")},RFC3986:function(e){return String(e)}},l=o.RFC1738;o.RFC3986;let s=o.RFC3986},34922:(e,t,n)=>{n.d(t,{Y:()=>a});var r=n(89140);let a=({config:e,cookies:t,headers:n})=>{let a=Object.keys(e.i18n.supportedLanguages),o=t.get(`${e.cookiePrefix||"payload"}-lng`),i="string"==typeof o?o:o?.value;if(i&&a.includes(i))return i;let l=n.get("Accept-Language")?(0,r.R8)(n.get("Accept-Language")):void 0;return l&&a.includes(l)?l:e.i18n.fallbackLanguage}},34972:(e,t,n)=>{n.d(t,{A:()=>g});var r=n(30160),a=n(32293);let o=Object.prototype.hasOwnProperty,i={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},l=Array.isArray,s=Array.prototype.push,u=function(e,t){s.apply(e,l(t)?t:[t])},c=Date.prototype.toISOString,d=a.Ay,f={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:r.lF,encodeValuesOnly:!1,format:d,formatter:a._J[d],indices:!1,serializeDate:function(e){return c.call(e)},skipNulls:!1,strictNullHandling:!1},h={},p=function(e,t,n,a,o,i,s,c,d,m,g,y,w,b,v,x,M,D){var C;let T,k=e,E=D,j=0,P=!1;for(;void 0!==(E=E.get(h))&&!P;){let t=E.get(e);if(j+=1,void 0!==t)if(t===j)throw RangeError("Cyclic object value");else P=!0;void 0===E.get(h)&&(j=0)}if("function"==typeof m?k=m(t,k):k instanceof Date?k=w(k):"comma"===n&&l(k)&&(k=r.F7(k,function(e){return e instanceof Date?w(e):e})),null===k){if(i)return d&&!x?d(t,f.encoder,M,"key",b):t;k=""}if("string"==typeof(C=k)||"number"==typeof C||"boolean"==typeof C||"symbol"==typeof C||"bigint"==typeof C||r.Pe(k))return d?[v(x?t:d(t,f.encoder,M,"key",b))+"="+v(d(k,f.encoder,M,"value",b))]:[v(t)+"="+v(String(k))];let S=[];if(void 0===k)return S;if("comma"===n&&l(k))x&&d&&(k=r.F7(k,d)),T=[{value:k.length>0?k.join(",")||null:void 0}];else if(l(m))T=m;else{let e=Object.keys(k);T=g?e.sort(g):e}let O=c?t.replace(/\./g,"%2E"):t,A=a&&l(k)&&1===k.length?O+"[]":O;if(o&&l(k)&&0===k.length)return A+"[]";for(let t=0;t<T.length;++t){let r=T[t],f="object"==typeof r&&void 0!==r.value?r.value:k[r];if(s&&null===f)continue;let C=y&&c?r.replace(/\./g,"%2E"):r,E=l(k)?"function"==typeof n?n(A,C):A:A+(y?"."+C:"["+C+"]");D.set(e,j);let P=new WeakMap;P.set(h,D),u(S,p(f,E,n,a,o,i,s,c,"comma"===n&&x&&l(k)?null:d,m,g,y,w,b,v,x,M,P))}return S},m=function(e){let t;if(!e)return f;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let n=e.charset||f.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let r=a.Ay;if(void 0!==e.format){if(!o.call(a._J,e.format))throw TypeError("Unknown format option provided.");r=e.format}let s=a._J[r],u=f.filter;if(("function"==typeof e.filter||l(e.filter))&&(u=e.filter),t=e.arrayFormat in i?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":f.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let c=void 0===e.allowDots?!0===e.encodeDotInKeys||f.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:f.addQueryPrefix,allowDots:c,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:f.allowEmptyArrays,arrayFormat:t,charset:n,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:f.charsetSentinel,commaRoundTrip:e.commaRoundTrip,delimiter:void 0===e.delimiter?f.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:f.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:f.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:f.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:f.encodeValuesOnly,filter:u,format:r,formatter:s,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:f.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:f.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:f.strictNullHandling}};function g(e,t){let n,r,a=e,o=m(t);"function"==typeof o.filter?a=(0,o.filter)("",a):l(o.filter)&&(n=o.filter);let s=[];if("object"!=typeof a||null===a)return"";let c=i[o.arrayFormat],d="comma"===c&&o.commaRoundTrip;n||(n=Object.keys(a)),o.sort&&n.sort(o.sort);let f=new WeakMap;for(let e=0;e<n.length;++e){let t=n[e];o.skipNulls&&null===a[t]||u(s,p(a[t],t,c,d,o.allowEmptyArrays,o.strictNullHandling,o.skipNulls,o.encodeDotInKeys,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,f))}let h=s.join(o.delimiter),g=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&("iso-8859-1"===o.charset?g+="utf8=%26%2310003%3B&":g+="utf8=%E2%9C%93&"),h.length>0?g+h:""}},35418:(e,t,n)=>{n.d(t,{f:()=>s});var r=n(90099),a=n(48671),o=n(10150),i=n(58751);n(98822);var l=n(66834);let s=({clientProps:e={},Component:t,Fallback:n,importMap:u,key:c,serverProps:d})=>{if(Array.isArray(t))return t.map((t,n)=>s({clientProps:e,Component:t,importMap:u,key:n,serverProps:d}));if("function"==typeof t){let n=(0,a.r_)(t),o=(0,l.B)({...e,...n?d:{}});return(0,r.jsx)(t,{...o},c)}if("string"==typeof t||(0,o.Q)(t)){let n=(0,i.g)({importMap:u,PayloadComponent:t,schemaPath:""});if(n){let o=(0,a.r_)(n),i=(0,l.B)({...e,...o?d:{},...o&&"object"==typeof t&&t?.serverProps?t.serverProps:{},..."object"==typeof t&&t?.clientProps?t.clientProps:{}});return(0,r.jsx)(n,{...i},c)}}return n?s({clientProps:e,Component:n,importMap:u,key:c,serverProps:d}):null}},37549:(e,t,n)=>{n.d(t,{q:()=>a});let r={};function a(){return r}},42626:(e,t,n)=>{n.d(t,{MM:()=>i,YW:()=>r});function r(e,t){var n,r,a,o,l,s=[];return n=i(e,s,t),r=s,void 0===(a=t)&&(a={}),l=void 0===(o=a.decode)?function(e){return e}:o,function(e){var t=n.exec(e);if(!t)return!1;for(var a=t[0],o=t.index,i=Object.create(null),s=1;s<t.length;s++)!function(e){if(void 0!==t[e]){var n=r[e-1];"*"===n.modifier||"+"===n.modifier?i[n.name]=t[e].split(n.prefix+n.suffix).map(function(e){return l(e,n)}):i[n.name]=l(t[e],n)}}(s);return{path:a,index:o,params:i}}}function a(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function o(e){return e&&e.sensitive?"":"i"}function i(e,t,n){if(e instanceof RegExp){var r;if(!t)return e;for(var l=/\((?:\?<(.*?)>)?(?!\?)/g,s=0,u=l.exec(e.source);u;)t.push({name:u[1]||s++,prefix:"",suffix:"",modifier:"",pattern:""}),u=l.exec(e.source);return e}return Array.isArray(e)?(r=e.map(function(e){return i(e,t,n).source}),new RegExp("(?:".concat(r.join("|"),")"),o(n))):function(e,t,n){void 0===n&&(n={});for(var r=n.strict,i=void 0!==r&&r,l=n.start,s=n.end,u=n.encode,c=void 0===u?function(e){return e}:u,d=n.delimiter,f=n.endsWith,h="[".concat(a(void 0===f?"":f),"]|$"),p="[".concat(a(void 0===d?"/#?":d),"]"),m=void 0===l||l?"^":"",g=0;g<e.length;g++){var y=e[g];if("string"==typeof y)m+=a(c(y));else{var w=a(c(y.prefix)),b=a(c(y.suffix));if(y.pattern)if(t&&t.push(y),w||b)if("+"===y.modifier||"*"===y.modifier){var v="*"===y.modifier?"?":"";m+="(?:".concat(w,"((?:").concat(y.pattern,")(?:").concat(b).concat(w,"(?:").concat(y.pattern,"))*)").concat(b,")").concat(v)}else m+="(?:".concat(w,"(").concat(y.pattern,")").concat(b,")").concat(y.modifier);else{if("+"===y.modifier||"*"===y.modifier)throw TypeError('Can not repeat "'.concat(y.name,'" without a prefix and suffix'));m+="(".concat(y.pattern,")").concat(y.modifier)}else m+="(?:".concat(w).concat(b,")").concat(y.modifier)}}if(void 0===s||s)i||(m+="".concat(p,"?")),m+=n.endsWith?"(?=".concat(h,")"):"$";else{var x=e[e.length-1],M="string"==typeof x?p.indexOf(x[x.length-1])>-1:void 0===x;i||(m+="(?:".concat(p,"(?=").concat(h,"))?")),M||(m+="(?=".concat(p,"|").concat(h,")"))}return new RegExp(m,o(n))}(function(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:n,value:e[n++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if("{"===r){t.push({type:"OPEN",index:n,value:e[n++]});continue}if("}"===r){t.push({type:"CLOSE",index:n,value:e[n++]});continue}if(":"===r){for(var a="",o=n+1;o<e.length;){var i=e.charCodeAt(o);if(i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122||95===i){a+=e[o++];continue}break}if(!a)throw TypeError("Missing parameter name at ".concat(n));t.push({type:"NAME",index:n,value:a}),n=o;continue}if("("===r){var l=1,s="",o=n+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '.concat(o));for(;o<e.length;){if("\\"===e[o]){s+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--l){o++;break}}else if("("===e[o]&&(l++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at ".concat(o));s+=e[o++]}if(l)throw TypeError("Unbalanced pattern at ".concat(n));if(!s)throw TypeError("Missing pattern at ".concat(n));t.push({type:"PATTERN",index:n,value:s}),n=o;continue}t.push({type:"CHAR",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),r=t.prefixes,o=void 0===r?"./":r,i=t.delimiter,l=void 0===i?"/#?":i,s=[],u=0,c=0,d="",f=function(e){if(c<n.length&&n[c].type===e)return n[c++].value},h=function(e){var t=f(e);if(void 0!==t)return t;var r=n[c],a=r.type,o=r.index;throw TypeError("Unexpected ".concat(a," at ").concat(o,", expected ").concat(e))},p=function(){for(var e,t="";e=f("CHAR")||f("ESCAPED_CHAR");)t+=e;return t},m=function(e){for(var t=0;t<l.length;t++){var n=l[t];if(e.indexOf(n)>-1)return!0}return!1},g=function(e){var t=s[s.length-1],n=e||(t&&"string"==typeof t?t:"");if(t&&!n)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!n||m(n)?"[^".concat(a(l),"]+?"):"(?:(?!".concat(a(n),")[^").concat(a(l),"])+?")};c<n.length;){var y=f("CHAR"),w=f("NAME"),b=f("PATTERN");if(w||b){var v=y||"";-1===o.indexOf(v)&&(d+=v,v=""),d&&(s.push(d),d=""),s.push({name:w||u++,prefix:v,suffix:"",pattern:b||g(v),modifier:f("MODIFIER")||""});continue}var x=y||f("ESCAPED_CHAR");if(x){d+=x;continue}if(d&&(s.push(d),d=""),f("OPEN")){var v=p(),M=f("NAME")||"",D=f("PATTERN")||"",C=p();h("CLOSE"),s.push({name:M||(D?u++:""),pattern:M&&!D?g(v):D,prefix:v,suffix:C,modifier:f("MODIFIER")||""});continue}h("END")}return s}(e,n),t,n)}},43416:(e,t,n)=>{n.d(t,{K:()=>r});function r(e){return(t,n={})=>{let r=t.match(e.matchPattern);if(!r)return null;let a=r[0],o=t.match(e.parsePattern);if(!o)return null;let i=e.valueCallback?e.valueCallback(o[0]):o[0];return{value:i=n.valueCallback?n.valueCallback(i):i,rest:t.slice(a.length)}}}},54642:(e,t,n)=>{n.d(t,{ck:()=>X,Ou:()=>Y,YY:()=>q,wD:()=>H,di:()=>I,Yq:()=>V,d5:()=>J,Jy:()=>ee,JN:()=>et,$C:()=>en});var r=n(90099);n(98822),n(34972);var a=n(30804),o=n(85097),i=n(37549),l=n(61200);function s(e){let t=(0,l.a)(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),e-n}var u=n(31456),c=n(78904);function d(e,t){let n=(0,l.a)(e,t?.in);return n.setHours(0,0,0,0),n}var f=n(91520);function h(e,t){return(0,f.k)(e,{...t,weekStartsOn:1})}function p(e,t){let n=(0,l.a)(e,t?.in),r=n.getFullYear(),o=(0,a.w)(n,0);o.setFullYear(r+1,0,4),o.setHours(0,0,0,0);let i=h(o),s=(0,a.w)(n,0);s.setFullYear(r,0,4),s.setHours(0,0,0,0);let u=h(s);return n.getTime()>=i.getTime()?r+1:n.getTime()>=u.getTime()?r:r-1}function m(e,t){let n=(0,l.a)(e,t?.in),r=n.getFullYear(),o=(0,i.q)(),s=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??o.firstWeekContainsDate??o.locale?.options?.firstWeekContainsDate??1,u=(0,a.w)(t?.in||e,0);u.setFullYear(r+1,0,s),u.setHours(0,0,0,0);let c=(0,f.k)(u,t),d=(0,a.w)(t?.in||e,0);d.setFullYear(r,0,s),d.setHours(0,0,0,0);let h=(0,f.k)(d,t);return+n>=+c?r+1:+n>=+h?r:r-1}function g(e,t){let n=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+n}let y={y(e,t){let n=e.getFullYear(),r=n>0?n:1-n;return g("yy"===t?r%100:r,t.length)},M(e,t){let n=e.getMonth();return"M"===t?String(n+1):g(n+1,2)},d:(e,t)=>g(e.getDate(),t.length),a(e,t){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>g(e.getHours()%12||12,t.length),H:(e,t)=>g(e.getHours(),t.length),m:(e,t)=>g(e.getMinutes(),t.length),s:(e,t)=>g(e.getSeconds(),t.length),S(e,t){let n=t.length;return g(Math.trunc(e.getMilliseconds()*Math.pow(10,n-3)),t.length)}},w={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},b={G:function(e,t,n){let r=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if("yo"===t){let t=e.getFullYear();return n.ordinalNumber(t>0?t:1-t,{unit:"year"})}return y.y(e,t)},Y:function(e,t,n,r){let a=m(e,r),o=a>0?a:1-a;return"YY"===t?g(o%100,2):"Yo"===t?n.ordinalNumber(o,{unit:"year"}):g(o,t.length)},R:function(e,t){return g(p(e),t.length)},u:function(e,t){return g(e.getFullYear(),t.length)},Q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return g(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return g(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){let r=e.getMonth();switch(t){case"M":case"MM":return y.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){let r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return g(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){let o=function(e,t){let n=(0,l.a)(e,t?.in);return Math.round(((0,f.k)(n,t)-function(e,t){let n=(0,i.q)(),r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,o=m(e,t),l=(0,a.w)(t?.in||e,0);return l.setFullYear(o,0,r),l.setHours(0,0,0,0),(0,f.k)(l,t)}(n,t))/c.my)+1}(e,r);return"wo"===t?n.ordinalNumber(o,{unit:"week"}):g(o,t.length)},I:function(e,t,n){let r=function(e,t){let n=(0,l.a)(e,void 0);return Math.round((h(n)-function(e,t){let n=p(e,void 0),r=(0,a.w)(e,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),h(r)}(n))/c.my)+1}(e);return"Io"===t?n.ordinalNumber(r,{unit:"week"}):g(r,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):y.d(e,t)},D:function(e,t,n){let r=function(e,t){let n=(0,l.a)(e,void 0);return function(e,t,n){let[r,a]=(0,u.x)(void 0,e,t),o=d(r),i=d(a);return Math.round((o-s(o)-(i-s(i)))/c.w4)}(n,function(e,t){let n=(0,l.a)(e,void 0);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}(n))+1}(e);return"Do"===t?n.ordinalNumber(r,{unit:"dayOfYear"}):g(r,t.length)},E:function(e,t,n){let r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){let a=e.getDay(),o=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return g(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){let a=e.getDay(),o=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return g(o,t.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,n){let r=e.getDay(),a=0===r?7:r;switch(t){case"i":return String(a);case"ii":return g(a,t.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){let r,a=e.getHours();switch(r=12===a?w.noon:0===a?w.midnight:a/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,t,n){let r,a=e.getHours();switch(r=a>=17?w.evening:a>=12?w.afternoon:a>=4?w.morning:w.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return y.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):y.H(e,t)},K:function(e,t,n){let r=e.getHours()%12;return"Ko"===t?n.ordinalNumber(r,{unit:"hour"}):g(r,t.length)},k:function(e,t,n){let r=e.getHours();return(0===r&&(r=24),"ko"===t)?n.ordinalNumber(r,{unit:"hour"}):g(r,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):y.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):y.s(e,t)},S:function(e,t){return y.S(e,t)},X:function(e,t,n){let r=e.getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return x(r);case"XXXX":case"XX":return M(r);default:return M(r,":")}},x:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"x":return x(r);case"xxxx":case"xx":return M(r);default:return M(r,":")}},O:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+v(r,":");default:return"GMT"+M(r,":")}},z:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+v(r,":");default:return"GMT"+M(r,":")}},t:function(e,t,n){return g(Math.trunc(e/1e3),t.length)},T:function(e,t,n){return g(+e,t.length)}};function v(e,t=""){let n=e>0?"-":"+",r=Math.abs(e),a=Math.trunc(r/60),o=r%60;return 0===o?n+String(a):n+String(a)+t+g(o,2)}function x(e,t){return e%60==0?(e>0?"-":"+")+g(Math.abs(e)/60,2):M(e,t)}function M(e,t=""){let n=Math.abs(e);return(e>0?"-":"+")+g(Math.trunc(n/60),2)+t+g(n%60,2)}let D=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},C=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},T={p:C,P:(e,t)=>{let n,r=e.match(/(P+)(p+)?/)||[],a=r[1],o=r[2];if(!o)return D(e,t);switch(a){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",D(a,t)).replace("{{time}}",C(o,t))}},k=/^D+$/,E=/^Y+$/,j=["D","DD","YY","YYYY"],P=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,S=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,O=/^'([^]*?)'?$/,A=/''/g,N=/[a-zA-Z]/;function F(e,t,n){let r=(0,i.q)(),a=n?.locale??r.locale??o.enUS,s=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,u=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,c=(0,l.a)(e,n?.in);if(!(c instanceof Date||"object"==typeof c&&"[object Date]"===Object.prototype.toString.call(c))&&"number"!=typeof c||isNaN(+(0,l.a)(c)))throw RangeError("Invalid time value");let d=t.match(S).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,T[t])(e,a.formatLong):e}).join("").match(P).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(O);return t?t[1].replace(A,"'"):e}(e)};if(b[t])return{isToken:!0,value:e};if(t.match(N))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});a.localize.preprocessor&&(d=a.localize.preprocessor(c,d));let f={firstWeekContainsDate:s,weekStartsOn:u,locale:a};return d.map(r=>{if(!r.isToken)return r.value;let o=r.value;return(!n?.useAdditionalWeekYearTokens&&E.test(o)||!n?.useAdditionalDayOfYearTokens&&k.test(o))&&function(e,t,n){let r=function(e,t,n){let r="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,n);if(console.warn(r),j.includes(e))throw RangeError(r)}(o,t,String(e)),(0,b[o[0]])(c,o,a.localize,f)}).join("")}var W=n(58560),L=({elements:e,translationString:t})=>{let n=t.split(/(<[^>]+>.*?<\/[^>]+>)/g);return(0,r.jsx)("span",{children:n.map((t,n)=>{if(e&&t.startsWith("<")&&t.endsWith(">")){let a=t[1],o=e[a];if(o){let e=RegExp(`<${a}>(.*?)</${a}>`,"g"),i=t.replace(e,(e,t)=>t);return(0,r.jsx)(o,{children:(0,r.jsx)(L,{translationString:i})},n)}}return t})})},H=({elements:e,i18nKey:t,t:n,variables:a})=>{let o=n(t,a||{});return e?(0,r.jsx)(L,{elements:e,translationString:o}):o},Y=({fill:e})=>{let t=e||"var(--theme-elevation-1000)";return(0,r.jsxs)("svg",{className:"graphic-icon",height:"100%",viewBox:"0 0 25 25",width:"100%",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M11.8673 21.2336L4.40922 16.9845C4.31871 16.9309 4.25837 16.8355 4.25837 16.7282V10.1609C4.25837 10.0477 4.38508 9.97616 4.48162 10.0298L13.1404 14.9642C13.2611 15.0358 13.412 14.9464 13.412 14.8093V11.6091C13.412 11.4839 13.3456 11.3647 13.2309 11.2992L2.81624 5.36353C2.72573 5.30989 2.60505 5.30989 2.51454 5.36353L1.15085 6.14422C1.06034 6.19786 1 6.29321 1 6.40048V18.5995C1 18.7068 1.06034 18.8021 1.15085 18.8558L11.8491 24.9583C11.9397 25.0119 12.0603 25.0119 12.1509 24.9583L21.1355 19.8331C21.2562 19.7616 21.2562 19.5948 21.1355 19.5232L18.3357 17.9261C18.2211 17.8605 18.0883 17.8605 17.9737 17.9261L12.175 21.2336C12.0845 21.2872 11.9638 21.2872 11.8733 21.2336H11.8673Z",fill:t}),(0,r.jsx)("path",{d:"M22.8491 6.13827L12.1508 0.0417218C12.0603 -0.0119135 11.9397 -0.0119135 11.8491 0.0417218L6.19528 3.2658C6.0746 3.33731 6.0746 3.50418 6.19528 3.57569L8.97092 5.16091C9.08557 5.22647 9.21832 5.22647 9.33296 5.16091L11.8672 3.71872C11.9578 3.66508 12.0784 3.66508 12.1689 3.71872L19.627 7.96782C19.7175 8.02146 19.7778 8.11681 19.7778 8.22408V14.8212C19.7778 14.9464 19.8442 15.0656 19.9589 15.1311L22.7345 16.7104C22.8552 16.7819 23.006 16.6925 23.006 16.5554V6.40048C23.006 6.29321 22.9457 6.19786 22.8552 6.14423L22.8491 6.13827Z",fill:t})]})},R=`
  .graphic-logo path {
    fill: var(--theme-elevation-1000);
  }
`,q=()=>(0,r.jsxs)("svg",{className:"graphic-logo",fill:"none",height:"43.5",id:"b",viewBox:"0 0 193.38 43.5",width:"193.38",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("style",{children:R}),(0,r.jsxs)("g",{id:"c",children:[(0,r.jsx)("path",{d:"M18.01,35.63l-12.36-7.13c-.15-.09-.25-.25-.25-.43v-11.02c0-.19.21-.31.37-.22l14.35,8.28c.2.12.45-.03.45-.26v-5.37c0-.21-.11-.41-.3-.52L3.01,9c-.15-.09-.35-.09-.5,0l-2.26,1.31c-.15.09-.25.25-.25.43v20.47c0,.18.1.34.25.43l17.73,10.24c.15.09.35.09.5,0l14.89-8.6c.2-.12.2-.4,0-.52l-4.64-2.68c-.19-.11-.41-.11-.6,0l-9.61,5.55c-.15.09-.35.09-.5,0Z"}),(0,r.jsx)("path",{d:"M36.21,10.3L18.48.07c-.15-.09-.35-.09-.5,0l-9.37,5.41c-.2.12-.2.4,0,.52l4.6,2.66c.19.11.41.11.6,0l4.2-2.42c.15-.09.35-.09.5,0l12.36,7.13c.15.09.25.25.25.43v11.07c0,.21.11.41.3.52l4.6,2.65c.2.12.45-.03.45-.26V10.74c0-.18-.1-.34-.25-.43Z"}),(0,r.jsxs)("g",{id:"d",children:[(0,r.jsx)("path",{d:"M193.38,9.47c0,1.94-1.48,3.32-3.3,3.32s-3.31-1.39-3.31-3.32,1.49-3.31,3.31-3.31,3.3,1.39,3.3,3.31ZM192.92,9.47c0-1.68-1.26-2.88-2.84-2.88s-2.84,1.2-2.84,2.88,1.26,2.89,2.84,2.89,2.84-1.2,2.84-2.89ZM188.69,11.17v-3.51h1.61c.85,0,1.35.39,1.35,1.15,0,.53-.3.86-.67,1.02l.79,1.35h-.89l-.72-1.22h-.64v1.22h-.82ZM190.18,9.31c.46,0,.64-.16.64-.5s-.19-.49-.64-.49h-.67v.99h.67Z"}),(0,r.jsx)("path",{d:"M54.72,24.84v10.93h-5.4V6.1h12.26c7.02,0,11.1,3.2,11.1,9.39s-4.07,9.35-11.06,9.35h-6.9,0ZM61.12,20.52c4.07,0,6.11-1.66,6.11-5.03s-2.04-5.03-6.11-5.03h-6.4v10.06h6.4Z"}),(0,r.jsx)("path",{d:"M85.94,32.45c-1,2.41-3.66,3.78-7.02,3.78-4.11,0-7.11-2.29-7.11-6.11,0-4.24,3.32-5.98,7.61-6.48l6.32-.71v-1c0-2.58-1.58-3.82-3.99-3.82s-3.74,1.29-3.91,3.24h-5.11c.46-4.53,3.99-7.19,9.18-7.19,5.74,0,9.02,2.7,9.02,8.19v8.15c0,1.95.08,3.58.42,5.28h-5.11c-.21-1.16-.29-2.29-.29-3.32h0ZM85.73,27.58v-1.29l-4.7.54c-2.24.29-3.95.79-3.95,2.99,0,1.66,1.16,2.7,3.28,2.7,2.74,0,5.36-1.62,5.36-4.95h0Z"}),(0,r.jsx)("path",{d:"M90.39,14.66h5.4l5.86,15.92h.08l5.57-15.92h5.28l-8.23,21.49c-2,5.28-4.45,7.32-8.89,7.36-.71,0-1.7-.08-2.45-.21v-4.03c.62.13.96.13,1.41.13,2.16,0,3.07-.75,4.2-3.66l-8.23-21.07h0Z"}),(0,r.jsx)("path",{d:"M113.46,35.77V6.1h5.32v29.67h-5.32Z"}),(0,r.jsx)("path",{d:"M130.79,36.27c-6.23,0-10.68-4.2-10.68-11.05s4.45-11.05,10.68-11.05,10.68,4.24,10.68,11.05-4.45,11.05-10.68,11.05ZM130.79,32.32c3.41,0,5.36-2.66,5.36-7.11s-1.95-7.11-5.36-7.11-5.36,2.7-5.36,7.11,1.91,7.11,5.36,7.11Z"}),(0,r.jsx)("path",{d:"M156.19,32.45c-1,2.41-3.66,3.78-7.02,3.78-4.11,0-7.11-2.29-7.11-6.11,0-4.24,3.32-5.98,7.61-6.48l6.32-.71v-1c0-2.58-1.58-3.82-3.99-3.82s-3.74,1.29-3.91,3.24h-5.11c.46-4.53,3.99-7.19,9.19-7.19,5.74,0,9.02,2.7,9.02,8.19v8.15c0,1.95.08,3.58.42,5.28h-5.11c-.21-1.16-.29-2.29-.29-3.32h0ZM155.98,27.58v-1.29l-4.7.54c-2.24.29-3.95.79-3.95,2.99,0,1.66,1.16,2.7,3.28,2.7,2.74,0,5.36-1.62,5.36-4.95h0Z"}),(0,r.jsx)("path",{d:"M178.5,32.41c-1.04,2.12-3.58,3.87-6.78,3.87-5.53,0-9.31-4.49-9.31-11.05s3.78-11.05,9.31-11.05c3.28,0,5.69,1.83,6.69,3.95V6.1h5.32v29.67h-5.24v-3.37h0ZM178.55,24.84c0-4.11-1.95-6.78-5.32-6.78s-5.45,2.83-5.45,7.15,2,7.15,5.45,7.15,5.32-2.66,5.32-6.78v-.75h0Z"})]})]})]}),I=(e,t)=>e?.locales&&0!==e.locales.length?e.locales.find(e=>e?.code===t):null,z={},U={};function Z(e,t){try{let n=(z[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";return n in U?U[n]:B(n,n.split(":"))}catch{if(e in U)return U[e];let t=e?.match(Q);return t?B(e,t.slice(1)):NaN}}var Q=/([+-]\d\d):?(\d\d)?/;function B(e,t){let n=+t[0],r=+(t[1]||0);return U[e]=n>0?60*n+r:60*n-r}var K=class e extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(Z(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),G(this,NaN),_(this)):this.setTime(Date.now())}static tz(t,...n){return n.length?new e(...n,t):new e(Date.now(),t)}withTimeZone(t){return new e(+this,t)}getTimezoneOffset(){return-Z(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),_(this),+this}[Symbol.for("constructDateFrom")](t){return new e(+new Date(t),this.timeZone)}},$=/^(get|set)(?!UTC)/;function _(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function G(e){let t=Z(e.timeZone,e),n=new Date(+e);n.setUTCHours(n.getUTCHours()-1);let r=-new Date(+e).getTimezoneOffset(),a=r- -new Date(+n).getTimezoneOffset(),o=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();a&&o&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+a);let i=r-t;i&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+i);let l=Z(e.timeZone,e),s=-new Date(+e).getTimezoneOffset()-l-i;if(l!==t&&s){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+s);let t=l-Z(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!$.test(e))return;let t=e.replace($,"$1UTC");K.prototype[t]&&(e.startsWith("get")?K.prototype[e]=function(){return this.internal[t]()}:(K.prototype[e]=function(){var e;return Date.prototype[t].apply(this.internal,arguments),e=this,Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),G(e),+this},K.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),_(this),+this}))});var V=({date:e,i18n:t,pattern:n,timezone:r})=>{let o=new K(new Date(e));if(r){let e=K.tz(r),i=function(e,t){var n;let r="function"==typeof(n=t)&&n.prototype?.constructor===n?new t(0):(0,a.w)(t,0);return r.setFullYear(e.getFullYear(),e.getMonth(),e.getDate()),r.setHours(e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()),r}(o.withTimeZone(r),e);return t.dateFNS?F(i,n,{locale:t.dateFNS}):`${t.t("general:loading")}...`}return t.dateFNS?F(o,n,{locale:t.dateFNS}):`${t.t("general:loading")}...`},X=function(e){return e.collection="collections",e.global="globals",e}({});function J(e,t,n){return e.reduce((e,r)=>{if(r.entity?.admin?.group===!1)return e;if(t?.[r.type.toLowerCase()]?.[r.entity.slug]?.read){let t=(0,W.s)(r.entity.admin.group,n),a="labels"in r.entity?r.entity.labels.plural:r.entity.label,o="function"==typeof a?a({i18n:n,t:n.t}):a;if(r.entity.admin.group){let a=e.find(e=>(0,W.s)(e.label,n)===t),i=a;a||(i={entities:[],label:t},e.push(i)),i.entities.push({slug:r.entity.slug,type:r.type,label:o})}else e.find(e=>(0,W.s)(e.label,n)===n.t(`general:${r.type}`)).entities.push({slug:r.entity.slug,type:r.type,label:o})}return e},[{entities:[],label:n.t("general:collections")},{entities:[],label:n.t("general:globals")}]).filter(e=>e.entities.length>0)}var ee=e=>{let{collectionSlug:t,docPermissions:n,globalSlug:r,isEditing:a}=e;return t?!!(a&&n?.update||!a&&n?.create):!!r&&!!n?.update},et=({id:e,collectionSlug:t,globalSlug:n})=>!!(n||t&&e);function en(e){return void 0===e||"number"==typeof e?e:decodeURIComponent(e)}},58751:(e,t,n)=>{n.d(t,{g:()=>a});var r=n(65382);let a=e=>{let{importMap:t,PayloadComponent:n,schemaPath:a,silent:o}=e,{exportName:i,path:l}=(0,r.R)(n),s=l+"#"+i,u=t[s];return u||o||console.error("getFromImportMap: PayloadComponent not found in importMap",{key:s,PayloadComponent:n,schemaPath:a},"You may need to run the `payload generate:importmap` command to generate the importMap ahead of runtime."),u}},61200:(e,t,n)=>{n.d(t,{a:()=>a});var r=n(30804);function a(e,t){return(0,r.w)(t||e,e)}},66834:(e,t,n)=>{n.d(t,{B:()=>r});function r(e){return Object.fromEntries(Object.entries(e).filter(([,e])=>void 0!==e))}},78904:(e,t,n)=>{n.d(t,{_P:()=>o,my:()=>r,w4:()=>a});let r=6048e5,a=864e5,o=Symbol.for("constructDateFrom")},84246:(e,t,n)=>{n.d(t,{o:()=>r});function r(e){return(t,n)=>{let r;if("formatting"===(n?.context?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,a=n?.width?String(n.width):t;r=e.formattingValues[a]||e.formattingValues[t]}else{let t=e.defaultWidth,a=n?.width?String(n.width):e.defaultWidth;r=e.values[a]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}},85097:(e,t,n)=>{n.r(t),n.d(t,{default:()=>d,enUS:()=>c});let r={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};var a=n(89500);let o={date:(0,a.k)({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:(0,a.k)({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:(0,a.k)({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},i={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};var l=n(84246);let s={ordinalNumber:(e,t)=>{let n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:(0,l.o)({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:(0,l.o)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,l.o)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:(0,l.o)({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:(0,l.o)({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};var u=n(22584);let c={code:"en-US",formatDistance:(e,t,n)=>{let a,o=r[e];if(a="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",t.toString()),n?.addSuffix)if(n.comparison&&n.comparison>0)return"in "+a;else return a+" ago";return a},formatLong:o,formatRelative:(e,t,n,r)=>i[e],localize:s,match:{ordinalNumber:(0,n(43416).K)({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,u.A)({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:(0,u.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,u.A)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,u.A)({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,u.A)({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},d=c},86933:(e,t,n)=>{n.d(t,{v:()=>o});var r=n(36720),a=n(68499);let o=e=>{if(e){let t=Object.getPrototypeOf(e);if((t.constructor.name===a.h||t.constructor.name===r.i)&&e.data)return{errors:[{name:e.name,data:e.data,message:e.message}]};if(t.constructor.name===a.h&&"errors"in e&&e.errors)return{errors:Object.keys(e.errors).reduce((t,n)=>(t.push({field:e.errors[n].path,message:e.errors[n].message}),t),[])};if(Array.isArray(e.message))return{errors:e.message};if(e.name)return{errors:[{message:e.message}]}}return{errors:[{message:"An unknown error occurred."}]}}},89140:(e,t,n)=>{n.d(t,{R8:()=>o,aG:()=>r});let r=["ar","fa","he"],a=["ar","az","bg","bn-BD","bn-IN","ca","cs","bn-BD","bn-IN","da","de","en","es","et","fa","fr","he","hr","hu","hy","it","ja","ko","lt","lv","my","nb","nl","pl","pt","ro","rs","rs-latin","ru","sk","sl","sv","th","tr","uk","vi","zh","zh-TW"];function o(e){let t;for(let{language:n}of e.split(",").map(e=>{let[t,n]=e.trim().split(";q=");return{language:t,quality:n?parseFloat(n):1}}).sort((e,t)=>t.quality-e.quality))!t&&a.includes(n)&&(t=n);return t}},89500:(e,t,n)=>{n.d(t,{k:()=>r});function r(e){return (t={})=>{let n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}},90990:(e,t,n)=>{n.d(t,{q:()=>f});var r=n(30160);let a=Object.prototype.hasOwnProperty,o=Array.isArray,i={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:r.D4,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},l=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},s=function(e,t){let n,s={__proto__:null},u=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,c=t.parameterLimit===1/0?void 0:t.parameterLimit,d=u.split(t.delimiter,c),f=-1,h=t.charset;if(t.charsetSentinel)for(n=0;n<d.length;++n)0===d[n].indexOf("utf8=")&&("utf8=%E2%9C%93"===d[n]?h="utf-8":"utf8=%26%2310003%3B"===d[n]&&(h="iso-8859-1"),f=n,n=d.length);for(n=0;n<d.length;++n){let e,u;if(n===f)continue;let c=d[n],p=c.indexOf("]="),m=-1===p?c.indexOf("="):p+1;-1===m?(e=t.decoder(c,i.decoder,h,"key"),u=t.strictNullHandling?null:""):(e=t.decoder(c.slice(0,m),i.decoder,h,"key"),u=r.F7(l(c.slice(m+1),t),function(e){return t.decoder(e,i.decoder,h,"value")})),u&&t.interpretNumericEntities&&"iso-8859-1"===h&&(u=u.replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),c.indexOf("[]=")>-1&&(u=o(u)?[u]:u);let g=a.call(s,e);g&&"combine"===t.duplicates?s[e]=r.kg(s[e],u):g&&"last"!==t.duplicates||(s[e]=u)}return s},u=function(e,t,n,r){let a=r?t:l(t,n);for(let t=e.length-1;t>=0;--t){let r,o=e[t];if("[]"===o&&n.parseArrays)r=n.allowEmptyArrays&&""===a?[]:[].concat(a);else{r=n.plainObjects?Object.create(null):{};let e="["===o.charAt(0)&&"]"===o.charAt(o.length-1)?o.slice(1,-1):o,t=n.decodeDotInKeys?e.replace(/%2E/g,"."):e,i=parseInt(t,10);n.parseArrays||""!==t?!isNaN(i)&&o!==t&&String(i)===t&&i>=0&&n.parseArrays&&i<=n.arrayLimit?(r=[])[i]=a:"__proto__"!==t&&(r[t]=a):r={0:a}}a=r}return a},c=function(e,t,n,r){if(!e)return;let o=n.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,i=/(\[[^[\]]*])/g,l=n.depth>0&&/(\[[^[\]]*])/.exec(o),s=l?o.slice(0,l.index):o,c=[];if(s){if(!n.plainObjects&&a.call(Object.prototype,s)&&!n.allowPrototypes)return;c.push(s)}let d=0;for(;n.depth>0&&null!==(l=i.exec(o))&&d<n.depth;){if(d+=1,!n.plainObjects&&a.call(Object.prototype,l[1].slice(1,-1))&&!n.allowPrototypes)return;c.push(l[1])}return l&&c.push("["+o.slice(l.index)+"]"),u(c,t,n,r)},d=function(e){if(!e)return i;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let t=void 0===e.charset?i.charset:e.charset,n=void 0===e.duplicates?i.duplicates:e.duplicates;if("combine"!==n&&"first"!==n&&"last"!==n)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||i.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:i.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:i.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:i.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:i.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:i.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:i.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:i.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:i.decoder,delimiter:"string"==typeof e.delimiter||r.gd(e.delimiter)?e.delimiter:i.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:i.depth,duplicates:n,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:i.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:i.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:i.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:i.strictNullHandling}};function f(e,t){let n=d(t);if(""===e||null==e)return n.plainObjects?Object.create(null):{};let a="string"==typeof e?s(e,n):e,o=n.plainObjects?Object.create(null):{},i=Object.keys(a);for(let t=0;t<i.length;++t){let l=i[t],s=c(l,a[l],n,"string"==typeof e);o=r.h1(o,s,n)}return!0===n.allowSparse?o:r.oE(o)}},91520:(e,t,n)=>{n.d(t,{k:()=>o});var r=n(37549),a=n(61200);function o(e,t){let n=(0,r.q)(),o=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,i=(0,a.a)(e,t?.in),l=i.getDay();return i.setDate(i.getDate()-(7*(l<o)+l-o)),i.setHours(0,0,0,0),i}}};