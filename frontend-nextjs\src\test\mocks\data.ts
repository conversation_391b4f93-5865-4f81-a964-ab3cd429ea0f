import { Appointment, Patient, Treatment, User, Bill, BillItem, Payment, Deposit } from '@/types/clinic'

export const mockUsers: User[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    role: 'admin',
    clerkId: 'clerk-user-1',
    firstName: 'Admin',
    lastName: 'User',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    role: 'doctor',
    clerkId: 'clerk-user-2',
    firstName: 'Dr. <PERSON>',
    lastName: '<PERSON>',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
]

export const mockTreatments: Treatment[] = [
  {
    id: 'treatment-1',
    name: 'Botox Injection',
    description: 'Anti-aging botox treatment for wrinkles',
    defaultPrice: 300,
    defaultDurationInMinutes: 30,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'treatment-2',
    name: 'Der<PERSON>ller',
    description: 'Hyaluronic acid filler for volume restoration',
    defaultPrice: 500,
    defaultDurationInMinutes: 45,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'treatment-3',
    name: 'Chemical Peel',
    description: 'Exfoliating treatment for skin renewal',
    defaultPrice: 150,
    defaultDurationInMinutes: 60,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
]

export const mockPatients: Patient[] = [
  {
    id: 'patient-1',
    fullName: 'Alice Johnson',
    phone: '******-0101',
    email: '<EMAIL>',
    medicalNotes: 'No known allergies',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'patient-2',
    fullName: 'Bob Wilson',
    phone: '******-0102',
    email: '<EMAIL>',
    medicalNotes: 'Sensitive skin',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'patient-3',
    fullName: 'Carol Davis',
    phone: '******-0103',
    email: '<EMAIL>',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
]

export const mockAppointments: Appointment[] = [
  {
    id: 'appointment-1',
    appointmentDate: '2024-07-10T10:00:00.000Z',
    appointmentType: 'treatment',
    status: 'scheduled',
    treatment: mockTreatments[0],
    price: 300,
    durationInMinutes: 30,
    patient: mockPatients[0],
    practitioner: mockUsers[1],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'appointment-2',
    appointmentDate: '2024-07-10T14:00:00.000Z',
    appointmentType: 'treatment',
    status: 'completed',
    treatment: mockTreatments[1],
    price: 500,
    durationInMinutes: 45,
    patient: mockPatients[1],
    practitioner: mockUsers[1],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'appointment-3',
    appointmentDate: '2024-07-11T09:00:00.000Z',
    appointmentType: 'consultation',
    status: 'scheduled',
    treatment: mockTreatments[2],
    price: 150,
    durationInMinutes: 60,
    patient: mockPatients[2],
    practitioner: mockUsers[1],
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  },
]

// Mock billing data
export const mockBillItems: BillItem[] = [
  {
    id: 'bill-item-1',
    billId: 'bill-1',
    itemType: 'treatment',
    itemId: 'treatment-1',
    itemName: 'Botox注射治疗',
    description: '面部除皱Botox注射',
    quantity: 1,
    unitPrice: 300.00,
    discountRate: 0,
    totalPrice: 300.00,
    createdAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'bill-item-2',
    billId: 'bill-1',
    itemType: 'consultation',
    itemName: '专家咨询费',
    description: '医美专家咨询服务',
    quantity: 1,
    unitPrice: 100.00,
    discountRate: 10,
    totalPrice: 90.00,
    createdAt: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'bill-item-3',
    billId: 'bill-2',
    itemType: 'treatment',
    itemId: 'treatment-2',
    itemName: '玻尿酸填充',
    description: '面部玻尿酸填充治疗',
    quantity: 2,
    unitPrice: 500.00,
    discountRate: 5,
    totalPrice: 950.00,
    createdAt: '2024-01-02T00:00:00.000Z',
  },
]

export const mockBills: Bill[] = [
  {
    id: 'bill-1',
    billNumber: 'BILL-2024-001',
    patientId: 'patient-1',
    appointmentId: 'appointment-1',
    treatmentId: 'treatment-1',
    billType: 'treatment',
    status: 'confirmed',
    subtotal: 390.00,
    discountAmount: 10.00,
    taxAmount: 0.00,
    totalAmount: 390.00,
    paidAmount: 200.00,
    remainingAmount: 190.00,
    issueDate: '2024-01-01T00:00:00.000Z',
    dueDate: '2024-07-31T00:00:00.000Z',
    description: 'Botox治疗及专家咨询',
    notes: '患者首次治疗，给予咨询费折扣',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    createdBy: 'user-1',
    patient: mockPatients[0],
    appointment: mockAppointments[0],
    treatment: mockTreatments[0],
    items: [mockBillItems[0], mockBillItems[1]],
  },
  {
    id: 'bill-2',
    billNumber: 'BILL-2024-002',
    patientId: 'patient-2',
    appointmentId: 'appointment-2',
    treatmentId: 'treatment-2',
    billType: 'treatment',
    status: 'paid',
    subtotal: 950.00,
    discountAmount: 50.00,
    taxAmount: 0.00,
    totalAmount: 950.00,
    paidAmount: 950.00,
    remainingAmount: 0.00,
    issueDate: '2024-01-02T00:00:00.000Z',
    dueDate: '2024-07-31T00:00:00.000Z',
    description: '玻尿酸填充治疗',
    notes: 'VIP客户享受折扣',
    createdAt: '2024-01-02T00:00:00.000Z',
    updatedAt: '2024-01-02T00:00:00.000Z',
    createdBy: 'user-1',
    patient: mockPatients[1],
    appointment: mockAppointments[1],
    treatment: mockTreatments[1],
    items: [mockBillItems[2]],
  },
  {
    id: 'bill-3',
    billNumber: 'BILL-2024-003',
    patientId: 'patient-3',
    billType: 'consultation',
    status: 'draft',
    subtotal: 200.00,
    discountAmount: 0.00,
    taxAmount: 0.00,
    totalAmount: 200.00,
    paidAmount: 0.00,
    remainingAmount: 200.00,
    issueDate: '2024-01-03T00:00:00.000Z',
    dueDate: '2024-08-15T00:00:00.000Z',
    description: '初诊咨询评估',
    notes: '新患者初诊',
    createdAt: '2024-01-03T00:00:00.000Z',
    updatedAt: '2024-01-03T00:00:00.000Z',
    createdBy: 'user-2',
    patient: mockPatients[2],
    items: [],
  },
]

export const mockPayments: Payment[] = [
  {
    id: 'payment-1',
    paymentNumber: 'PAY-2024-001',
    billId: 'bill-1',
    patientId: 'patient-1',
    amount: 200.00,
    paymentMethod: 'cash',
    paymentStatus: 'completed',
    transactionId: 'TXN-CASH-001',
    paymentDate: '2024-01-01T10:00:00.000Z',
    receivedBy: 'user-1',
    notes: '现金支付部分账单',
    receiptNumber: 'RCP-2024-001',
    createdAt: '2024-01-01T10:00:00.000Z',
    updatedAt: '2024-01-01T10:00:00.000Z',
    bill: mockBills[0],
    patient: mockPatients[0],
  },
  {
    id: 'payment-2',
    paymentNumber: 'PAY-2024-002',
    billId: 'bill-2',
    patientId: 'patient-2',
    amount: 950.00,
    paymentMethod: 'wechat',
    paymentStatus: 'completed',
    transactionId: 'WX-TXN-20240102001',
    paymentDate: '2024-01-02T14:30:00.000Z',
    receivedBy: 'user-1',
    notes: '微信支付全额',
    receiptNumber: 'RCP-2024-002',
    createdAt: '2024-01-02T14:30:00.000Z',
    updatedAt: '2024-01-02T14:30:00.000Z',
    bill: mockBills[1],
    patient: mockPatients[1],
  },
  {
    id: 'payment-3',
    paymentNumber: 'PAY-2024-003',
    billId: 'bill-1',
    patientId: 'patient-1',
    amount: 50.00,
    paymentMethod: 'card',
    paymentStatus: 'pending',
    transactionId: 'CARD-TXN-003',
    paymentDate: '2024-01-03T09:15:00.000Z',
    receivedBy: 'user-2',
    notes: '银行卡支付处理中',
    createdAt: '2024-01-03T09:15:00.000Z',
    updatedAt: '2024-01-03T09:15:00.000Z',
    bill: mockBills[0],
    patient: mockPatients[0],
  },
]

export const mockDeposits: Deposit[] = [
  {
    id: 'deposit-1',
    depositNumber: 'DEP-2024-001',
    patientId: 'patient-1',
    appointmentId: 'appointment-1',
    treatmentId: 'treatment-1',
    depositType: 'treatment',
    amount: 500.00,
    status: 'active',
    usedAmount: 200.00,
    remainingAmount: 300.00,
    depositDate: '2023-12-15T00:00:00.000Z',
    expiryDate: '2024-12-15T00:00:00.000Z',
    purpose: 'Botox治疗预付款',
    notes: '年度套餐预付',
    createdAt: '2023-12-15T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    patient: mockPatients[0],
  },
  {
    id: 'deposit-2',
    depositNumber: 'DEP-2024-002',
    patientId: 'patient-2',
    depositType: 'treatment',
    amount: 1000.00,
    status: 'used',
    usedAmount: 1000.00,
    remainingAmount: 0.00,
    depositDate: '2024-01-01T00:00:00.000Z',
    usedDate: '2024-01-02T00:00:00.000Z',
    purpose: '玻尿酸治疗预付款',
    notes: '已全部使用',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-02T00:00:00.000Z',
    patient: mockPatients[1],
  },
  {
    id: 'deposit-3',
    depositNumber: 'DEP-2024-003',
    patientId: 'patient-3',
    depositType: 'appointment',
    amount: 300.00,
    status: 'active',
    usedAmount: 0.00,
    remainingAmount: 300.00,
    depositDate: '2024-01-03T00:00:00.000Z',
    expiryDate: '2024-07-03T00:00:00.000Z',
    purpose: '预约保证金',
    notes: '新客户预约保证金',
    createdAt: '2024-01-03T00:00:00.000Z',
    updatedAt: '2024-01-03T00:00:00.000Z',
    patient: mockPatients[2],
  },
]
