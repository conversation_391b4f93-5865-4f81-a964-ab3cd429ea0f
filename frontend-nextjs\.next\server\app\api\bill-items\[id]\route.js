try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="fedd2709-6eac-48f6-a9ff-5607a75cd53e",e._sentryDebugIdIdentifier="sentry-dbid-fedd2709-6eac-48f6-a9ff-5607a75cd53e")}catch(e){}(()=>{var e={};e.id=8608,e.ids=[8608],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7760:()=>{},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45962:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=45962,e.exports=r},47869:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>A,routeModule:()=>k,serverHooks:()=>R,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>b});var s={};t.r(s),t.d(s,{DELETE:()=>v,GET:()=>m,HEAD:()=>g,OPTIONS:()=>_,PATCH:()=>w,POST:()=>E,PUT:()=>j});var i=t(86047),o=t(85544),n=t(36135),a=t(63033),u=t(35886),p=t(79615),c=t(19761);let d="http://localhost:8002";async function l(e,{params:r}){try{let{userId:e}=await (0,p.j)();if(!e)return u.NextResponse.json({error:"Authentication required"},{status:401});let{id:t}=await r,s=await fetch(`https://api.clerk.com/v1/users/${e}`,{headers:{Authorization:`Bearer ${process.env.CLERK_SECRET_KEY}`}}).then(e=>e.json()),i=`${d}/api/bill-items/${t}`,o=await fetch(i,{method:"GET",headers:{"Content-Type":"application/json","x-clerk-user-id":e,"x-user-email":s.email_addresses[0]?.email_address||""}}),n=await o.json();if(!o.ok)return u.NextResponse.json(n,{status:o.status});return u.NextResponse.json(n)}catch(e){return console.error("Error proxying bill-items request:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let{userId:t}=await (0,p.j)();if(!t)return u.NextResponse.json({error:"Authentication required"},{status:401});let{id:s}=await r,i=await fetch(`https://api.clerk.com/v1/users/${t}`,{headers:{Authorization:`Bearer ${process.env.CLERK_SECRET_KEY}`}}).then(e=>e.json()),o=await e.json(),n=`${d}/api/bill-items/${s}`,a=await fetch(n,{method:"PATCH",headers:{"Content-Type":"application/json","x-clerk-user-id":t,"x-user-email":i.email_addresses[0]?.email_address||""},body:JSON.stringify(o)}),c=await a.json();if(!a.ok)return u.NextResponse.json(c,{status:a.status});return u.NextResponse.json(c)}catch(e){return console.error("Error proxying bill-items request:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:r}){try{let{userId:e}=await (0,p.j)();if(!e)return u.NextResponse.json({error:"Authentication required"},{status:401});let{id:t}=await r,s=await fetch(`https://api.clerk.com/v1/users/${e}`,{headers:{Authorization:`Bearer ${process.env.CLERK_SECRET_KEY}`}}).then(e=>e.json()),i=`${d}/api/bill-items/${t}`,o=await fetch(i,{method:"DELETE",headers:{"Content-Type":"application/json","x-clerk-user-id":e,"x-user-email":s.email_addresses[0]?.email_address||""}}),n=await o.json();if(!o.ok)return u.NextResponse.json(n,{status:o.status});return u.NextResponse.json(n)}catch(e){return console.error("Error proxying bill-items request:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let f={...a},q="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;function y(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let i;try{let e=q?.getStore();i=e?.headers}catch(e){}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/bill-items/[id]",headers:i}).apply(t,s)}})}let m=y(l,"GET"),E=y(void 0,"POST"),j=y(void 0,"PUT"),w=y(x,"PATCH"),v=y(h,"DELETE"),g=y(void 0,"HEAD"),_=y(void 0,"OPTIONS"),k=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/bill-items/[id]/route",pathname:"/api/bill-items/[id]",filename:"route",bundlePath:"app/api/bill-items/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bill-items\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:T,workUnitAsyncStorage:b,serverHooks:R}=k;function A(){return(0,n.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:b})}},48161:e=>{"use strict";e.exports=require("node:os")},49616:()=>{},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86047:(e,r,t)=>{"use strict";e.exports=t(44870)},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[55,3738,1950,5886,9615],()=>t(47869));module.exports=s})();
//# sourceMappingURL=route.js.map