try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ed4af23e-20cc-48cc-8822-f4019db01c5f",e._sentryDebugIdIdentifier="sentry-dbid-ed4af23e-20cc-48cc-8822-f4019db01c5f")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{6:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},s=t.split(n),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var s=n||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!i.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},87:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,s=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),f=Symbol.iterator,g=Object.prototype.hasOwnProperty,m=Object.assign;function y(e,t,r,n,i,a){return{$$typeof:s,type:e,key:t,ref:void 0!==(r=a.ref)?r:null,props:a}}function v(e){return"object"==typeof e&&null!==e&&e.$$typeof===s}var _=/\/+/g;function b(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function w(){}function S(e,t,r){if(null==e)return e;var o=[],l=0;return!function e(t,r,o,l,u){var c,d,h,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var m=!1;if(null===t)m=!0;else switch(g){case"bigint":case"string":case"number":m=!0;break;case"object":switch(t.$$typeof){case s:case a:m=!0;break;case p:return e((m=t._init)(t._payload),r,o,l,u)}}if(m)return u=u(t),m=""===l?"."+b(t,0):l,i(u)?(o="",null!=m&&(o=m.replace(_,"$&/")+"/"),e(u,r,o,"",function(e){return e})):null!=u&&(v(u)&&(c=u,d=o+(null==u.key||t&&t.key===u.key?"":(""+u.key).replace(_,"$&/")+"/")+m,u=y(c.type,d,void 0,void 0,void 0,c.props)),r.push(u)),1;m=0;var S=""===l?".":l+":";if(i(t))for(var k=0;k<t.length;k++)g=S+b(l=t[k],k),m+=e(l,r,o,g,u);else if("function"==typeof(k=null===(h=t)||"object"!=typeof h?null:"function"==typeof(h=f&&h[f]||h["@@iterator"])?h:null))for(t=k.call(t),k=0;!(l=t.next()).done;)g=S+b(l=l.value,k++),m+=e(l,r,o,g,u);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(w,w):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,o,l,u);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return m}(e,o,"","",function(e){return t.call(r,e,l++)}),o}function k(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function E(){return new WeakMap}function x(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:S,forEach:function(e,t,r){S(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return S(e,function(){t++}),t},toArray:function(e){return S(e,function(e){return e})||[]},only:function(e){if(!v(e))throw Error(n(143));return e}},t.Fragment=o,t.Profiler=u,t.StrictMode=l,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(E);void 0===(t=n.get(e))&&(t=x(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var s=arguments[n];if("function"==typeof s||"object"==typeof s&&null!==s){var a=t.o;null===a&&(t.o=a=new WeakMap),void 0===(t=a.get(s))&&(t=x(),a.set(s,t))}else null===(a=t.p)&&(t.p=a=new Map),void 0===(t=a.get(s))&&(t=x(),a.set(s,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var o=e.apply(null,arguments);return(n=t).s=1,n.v=o}catch(e){throw(o=t).s=2,o.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=m({},e.props),s=e.key,a=void 0;if(null!=t)for(o in void 0!==t.ref&&(a=void 0),void 0!==t.key&&(s=""+t.key),t)g.call(t,o)&&"key"!==o&&"__self"!==o&&"__source"!==o&&("ref"!==o||void 0!==t.ref)&&(i[o]=t[o]);var o=arguments.length-2;if(1===o)i.children=r;else if(1<o){for(var l=Array(o),u=0;u<o;u++)l[u]=arguments[u+2];i.children=l}return y(e.type,s,void 0,void 0,a,i)},t.createElement=function(e,t,r){var n,i={},s=null;if(null!=t)for(n in void 0!==t.key&&(s=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var a=arguments.length-2;if(1===a)i.children=r;else if(1<a){for(var o=Array(a),l=0;l<a;l++)o[l]=arguments[l+2];i.children=o}if(e&&e.defaultProps)for(n in a=e.defaultProps)void 0===i[n]&&(i[n]=a[n]);return y(e,s,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=v,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:k}},t.memo=function(e,t){return{$$typeof:h,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.2.0-canary-3fbfb9ba-20250409"},212:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(968).xl)()},246:(e,t,r)=>{"use strict";r.d(t,{Ck:()=>l,K8:()=>c,hm:()=>d});var n=r(433),i=r(352),s=r(542),a=r(833);class o extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new o}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return o.callable;default:return i.l.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");class c{static wrap(e,t){let r=new n.VO(new Headers);for(let t of e.getAll())r.set(t);let a=[],o=new Set,l=()=>{let e=s.J.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of a){let r=new n.VO(new Headers);r.set(t),e.push(r.toString())}t(e)}},c=new Proxy(r,{get(e,t,r){switch(t){case u:return a;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),c}finally{l()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),c}finally{l()}};default:return i.l.get(e,t,r)}}});return c}}function d(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return h("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return h("cookies().set"),e.set(...r),t};default:return i.l.get(e,r,n)}}});return t}function h(e){if("action"!==(0,a.XN)(e).phase)throw new o}},253:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return s},wrapRequestHandler:function(){return a}});let n=r(349),i=r(564);function s(){return(0,i.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},349:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return s}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function s(e,t,r){let s=i(e,t);return s?n.run(s,r):r()}function a(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},352:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},356:e=>{"use strict";e.exports=require("node:buffer")},400:(e,t,r)=>{"use strict";r.d(t,{iC:()=>i}),r(606);var n=r(212);function i(){let e=n.Z.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},433:(e,t,r)=>{"use strict";r.d(t,{Ud:()=>n.stringifyCookie,VO:()=>n.ResponseCookies,tm:()=>n.RequestCookies});var n=r(944)},483:(e,t,r)=>{"use strict";r.d(t,{nJ:()=>i});var n=r(689);function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,s=t.slice(2,-2).join(";"),a=Number(t.at(-2));return"NEXT_REDIRECT"===r&&("replace"===i||"push"===i)&&"string"==typeof s&&!isNaN(a)&&a in n.Q}},484:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n=(0,r(749).xl)()},500:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,h:()=>s});let n="DYNAMIC_SERVER_USAGE";class i extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=n}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===n}},505:(e,t,r)=>{"use strict";r.d(t,{headers:()=>_}),r(246),r(433);var n=r(542),i=r(833),s=r(601),a=r(606),o=r(797),l=r(995);let u={current:null},c="function"==typeof l.cache?l.cache:e=>e,d=console.warn;function h(e){return function(...t){d(e(...t))}}c(e=>{try{d(u.current)}finally{u.current=null}});var p=r(400);let f=new WeakMap,g=h(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})});function m(){return this.getAll().map(e=>[e.name,e]).values()}function y(e){for(let e of this.getAll())this.delete(e.name);return e}var v=r(825);function _(){let e=n.J.getStore(),t=i.FP.getStore();if(e){if(t&&"after"===t.phase&&!(0,p.iC)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return w(v.o.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new a.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,l=t;let n=b.get(l);if(n)return n;let i=(0,o.W)(l.renderSignal,"`headers()`");return b.set(l,i),Object.defineProperties(i,{append:{value:function(){let e=`\`headers().append(${S(arguments[0])}, ...)\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},delete:{value:function(){let e=`\`headers().delete(${S(arguments[0])})\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},get:{value:function(){let e=`\`headers().get(${S(arguments[0])})\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},has:{value:function(){let e=`\`headers().has(${S(arguments[0])})\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},set:{value:function(){let e=`\`headers().set(${S(arguments[0])}, ...)\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=E(r,e);(0,s.t3)(r,e,t,l)}},keys:{value:function(){let e="`headers().keys()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},values:{value:function(){let e="`headers().values()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},entries:{value:function(){let e="`headers().entries()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=E(r,e);(0,s.t3)(r,e,t,l)}}}),i}else"prerender-ppr"===t.type?(0,s.Ui)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,s.xI)("headers",e,t);(0,s.Pk)(e,t)}return w((0,i.XN)("headers").headers)}let b=new WeakMap;function w(e){let t=b.get(e);if(t)return t;let r=Promise.resolve(e);return b.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function S(e){return"string"==typeof e?`'${e}'`:"..."}let k=h(E);function E(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}function x(){let e=workAsyncStorage.getStore(),t=workUnitAsyncStorage.getStore();switch((!e||!t)&&throwForMissingRequestStore("draftMode"),t.type){case"request":return T(t.draftMode,e);case"cache":case"unstable-cache":let r=getDraftModeProviderForCacheScope(e,t);if(r)return T(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return C(null);default:return t}}function T(e,t){let r,n=R.get(x);return n||(r=C(e),R.set(e,r),r)}r(500);let R=new WeakMap;function C(e){let t=new O(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class O{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){P("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){P("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let I=h(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function P(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},521:e=>{"use strict";e.exports=require("node:async_hooks")},542:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});let n=(0,r(749).xl)()},564:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return s}});let i=r(349),s={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:i,headers:s,body:a,cache:o,credentials:l,integrity:u,mode:c,redirect:d,referrer:h,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(s),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?n.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:u,mode:c,redirect:d,referrer:h,referrerPolicy:p}}}async function o(e,t){let r=(0,i.getTestReqInfo)(t,s);if(!r)return e(t);let{testData:o,proxyPort:l}=r,u=await a(o,t),c=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(u),next:{internal:!0}});if(!c.ok)throw Object.defineProperty(Error(`Proxy request failed: ${c.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await c.json(),{api:h}=d;switch(h){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:p,headers:f,body:g}=d.response;return new Response(g?n.from(g,"base64"):null,{status:p,headers:new Headers(f)})}function l(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},601:(e,t,r)=>{"use strict";r.d(t,{t3:()=>l,I3:()=>d,Ui:()=>u,xI:()=>a,Pk:()=>o});var n=r(995),i=r(500);r(606),r(833),r(542),r(797);let s="function"==typeof n.unstable_postpone;function a(e,t,r){let n=Object.defineProperty(new i.F(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function o(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function l(e,t,r,n){if(!1===n.controller.signal.aborted){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n)}throw p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function u(e,t,r){(function(){if(!s)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.unstable_postpone(c(e,t))}function c(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function d(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&h(e.message)}function h(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===h(c("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function p(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest="NEXT_PRERENDER_INTERRUPTED",t}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`)},606:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});class n extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}},627:(e,t,r)=>{"use strict";r.d(t,{RM:()=>s,s8:()=>i});let n=new Set(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401})),i="NEXT_HTTP_ERROR_FALLBACK";function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}},637:(e,t,r)=>{"use strict";let n=r(945),{snakeCase:i}=r(651),s={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==s))throw Error("obj must be array of plain objects")}else if(e.constructor!==s)throw Error("obj must be an plain object");return n(e,function(e,r){var n,s,a,o,l;return[(n=t.exclude,s=e,n.some(function(e){return"string"==typeof e?e===s:e.test(s)}))?e:i(e,t.parsingOptions),r,(a=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(a,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},644:(e,t,r)=>{var n;(()=>{var i={226:function(i,s){!function(a,o){"use strict";var l="function",u="undefined",c="object",d="string",h="major",p="model",f="name",g="type",m="vendor",y="version",v="architecture",_="console",b="mobile",w="tablet",S="smarttv",k="wearable",E="embedded",x="Amazon",T="Apple",R="ASUS",C="BlackBerry",O="Browser",I="Chrome",P="Firefox",A="Google",N="Huawei",U="Microsoft",j="Motorola",L="Opera",M="Samsung",D="Sharp",q="Sony",z="Xiaomi",$="Zebra",H="Facebook",B="Chromium OS",K="Mac OS",W=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},J=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},F=function(e,t){return typeof e===d&&-1!==V(t).indexOf(V(e))},V=function(e){return e.toLowerCase()},X=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},G=function(e,t){for(var r,n,i,s,a,u,d=0;d<t.length&&!a;){var h=t[d],p=t[d+1];for(r=n=0;r<h.length&&!a&&h[r];)if(a=h[r++].exec(e))for(i=0;i<p.length;i++)u=a[++n],typeof(s=p[i])===c&&s.length>0?2===s.length?typeof s[1]==l?this[s[0]]=s[1].call(this,u):this[s[0]]=s[1]:3===s.length?typeof s[1]!==l||s[1].exec&&s[1].test?this[s[0]]=u?u.replace(s[1],s[2]):void 0:this[s[0]]=u?s[1].call(this,u,s[2]):void 0:4===s.length&&(this[s[0]]=u?s[3].call(this,u.replace(s[1],s[2])):o):this[s]=u||o;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===c&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(F(t[r][n],e))return"?"===r?o:r}else if(F(t[r],e))return"?"===r?o:r;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,y],[/opios[\/ ]+([\w\.]+)/i],[y,[f,L+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[f,L]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[f,"UC"+O]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+O],y],[/\bfocus\/([\w\.]+)/i],[y,[f,P+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[f,L+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[f,L+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[f,"MIUI "+O]],[/fxios\/([-\w\.]+)/i],[y,[f,P]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+O]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+O],y],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,H],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[f,I+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,I+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[f,"Android "+O]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[y,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[f,P+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,y],[/(cobalt)\/([\w\.]+)/i],[f,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,V]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,"",V]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,V]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[m,M],[g,w]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[m,M],[g,b]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[m,T],[g,b]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[m,T],[g,w]],[/(macintosh);/i],[p,[m,T]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[m,D],[g,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[m,N],[g,w]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[m,N],[g,b]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[m,z],[g,b]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[m,z],[g,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[m,"OPPO"],[g,b]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[m,"Vivo"],[g,b]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[m,"Realme"],[g,b]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[m,j],[g,b]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[m,j],[g,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[m,"LG"],[g,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[m,"LG"],[g,b]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[m,"Lenovo"],[g,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[m,"Nokia"],[g,b]],[/(pixel c)\b/i],[p,[m,A],[g,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[m,A],[g,b]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[m,q],[g,b]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[m,q],[g,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[m,"OnePlus"],[g,b]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[m,x],[g,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[m,x],[g,b]],[/(playbook);[-\w\),; ]+(rim)/i],[p,m,[g,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[m,C],[g,b]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[m,R],[g,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[m,R],[g,b]],[/(nexus 9)/i],[p,[m,"HTC"],[g,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[p,/_/g," "],[g,b]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[m,"Acer"],[g,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[m,"Meizu"],[g,b]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,p,[g,b]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,p,[g,w]],[/(surface duo)/i],[p,[m,U],[g,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[m,"Fairphone"],[g,b]],[/(u304aa)/i],[p,[m,"AT&T"],[g,b]],[/\bsie-(\w*)/i],[p,[m,"Siemens"],[g,b]],[/\b(rct\w+) b/i],[p,[m,"RCA"],[g,w]],[/\b(venue[\d ]{2,7}) b/i],[p,[m,"Dell"],[g,w]],[/\b(q(?:mv|ta)\w+) b/i],[p,[m,"Verizon"],[g,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[m,"Barnes & Noble"],[g,w]],[/\b(tm\d{3}\w+) b/i],[p,[m,"NuVision"],[g,w]],[/\b(k88) b/i],[p,[m,"ZTE"],[g,w]],[/\b(nx\d{3}j) b/i],[p,[m,"ZTE"],[g,b]],[/\b(gen\d{3}) b.+49h/i],[p,[m,"Swiss"],[g,b]],[/\b(zur\d{3}) b/i],[p,[m,"Swiss"],[g,w]],[/\b((zeki)?tb.*\b) b/i],[p,[m,"Zeki"],[g,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],p,[g,w]],[/\b(ns-?\w{0,9}) b/i],[p,[m,"Insignia"],[g,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[m,"NextBook"],[g,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],p,[g,b]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],p,[g,b]],[/\b(ph-1) /i],[p,[m,"Essential"],[g,b]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[m,"Envizen"],[g,w]],[/\b(trio[-\w\. ]+) b/i],[p,[m,"MachSpeed"],[g,w]],[/\btu_(1491) b/i],[p,[m,"Rotor"],[g,w]],[/(shield[\w ]+) b/i],[p,[m,"Nvidia"],[g,w]],[/(sprint) (\w+)/i],[m,p,[g,b]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[m,U],[g,b]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[m,$],[g,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[m,$],[g,b]],[/smart-tv.+(samsung)/i],[m,[g,S]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[m,M],[g,S]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,S]],[/(apple) ?tv/i],[m,[p,T+" TV"],[g,S]],[/crkey/i],[[p,I+"cast"],[m,A],[g,S]],[/droid.+aft(\w)( bui|\))/i],[p,[m,x],[g,S]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[m,D],[g,S]],[/(bravia[\w ]+)( bui|\))/i],[p,[m,q],[g,S]],[/(mitv-\w{5}) bui/i],[p,[m,z],[g,S]],[/Hbbtv.*(technisat) (.*);/i],[m,p,[g,S]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,X],[p,X],[g,S]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,S]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,p,[g,_]],[/droid.+; (shield) bui/i],[p,[m,"Nvidia"],[g,_]],[/(playstation [345portablevi]+)/i],[p,[m,q],[g,_]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[m,U],[g,_]],[/((pebble))app/i],[m,p,[g,k]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[m,T],[g,k]],[/droid.+; (glass) \d/i],[p,[m,A],[g,k]],[/droid.+; (wt63?0{2,3})\)/i],[p,[m,$],[g,k]],[/(quest( 2| pro)?)/i],[p,[m,H],[g,k]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,E]],[/(aeobc)\b/i],[p,[m,x],[g,E]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[g,b]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[g,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,b]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[y,Y,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[y,Y,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,K],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,y],[/\(bb(10);/i],[y,[f,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[f,P+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[f,I+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,B],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,y],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,y]]},ee=function(e,t){if(typeof e===c&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==u&&a.navigator?a.navigator:o,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:o,s=t?W(Z,t):Z,_=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=o,t[y]=o,G.call(t,n,s.browser),t[h]=typeof(e=t[y])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:o,_&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[v]=o,G.call(e,n,s.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[p]=o,e[g]=o,G.call(e,n,s.device),_&&!e[g]&&i&&i.mobile&&(e[g]=b),_&&"Macintosh"==e[p]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[g]=w),e},this.getEngine=function(){var e={};return e[f]=o,e[y]=o,G.call(e,n,s.engine),e},this.getOS=function(){var e={};return e[f]=o,e[y]=o,G.call(e,n,s.os),_&&!e[f]&&i&&"Unknown"!=i.platform&&(e[f]=i.platform.replace(/chrome os/i,B).replace(/macos/i,K)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?X(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=J([f,y,h]),ee.CPU=J([v]),ee.DEVICE=J([p,m,g,_,b,S,w,k,E]),ee.ENGINE=ee.OS=J([f,y]),typeof s!==u?(i.exports&&(s=i.exports=ee),s.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof a!==u&&(a.UAParser=ee);var et=typeof a!==u&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},s={};function a(e){var t=s[e];if(void 0!==t)return t.exports;var r=s[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,a),n=!1}finally{n&&delete s[e]}return r.exports}a.ab="//",e.exports=a(226)})()},651:(e,t,r)=>{"use strict";r.r(t),r.d(t,{snakeCase:()=>l});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function i(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var s=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],a=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=n({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=t.stripRegexp,l=t.transform,u=t.delimiter,c=o(o(e,void 0===r?s:r,"$1\0$2"),void 0===n?a:n,"\0"),d=0,h=c.length;"\0"===c.charAt(d);)d++;for(;"\0"===c.charAt(h-1);)h--;return c.slice(d,h).split("\0").map(void 0===l?i:l).join(void 0===u?" ":u)}(e,n({delimiter:"."},r))}},682:(e,t)=>{"use strict";t.qg=function(e,t){let a=new r,o=e.length;if(o<2)return a;let l=t?.decode||s,u=0;do{let t=e.indexOf("=",u);if(-1===t)break;let r=e.indexOf(";",u),s=-1===r?o:r;if(t>s){u=e.lastIndexOf(";",t-1)+1;continue}let c=n(e,u,t),d=i(e,t,c),h=e.slice(c,d);if(void 0===a[h]){let r=n(e,t+1,s),o=i(e,s,r),u=l(e.slice(r,o));a[h]=u}u=s+1}while(u<o);return a},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function n(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function i(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function s(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},689:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});var n=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({})},749:(e,t,r)=>{"use strict";r.d(t,{xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}},797:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===i}r.d(t,{T:()=>n,W:()=>o});let i="HANGING_PROMISE_REJECTION";class s extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=i}}let a=new WeakMap;function o(e,t){if(e.aborted)return Promise.reject(new s(t));{let r=new Promise((r,n)=>{let i=n.bind(null,new s(t)),o=a.get(e);if(o)o.push(i);else{let t=[i];a.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(l),r}}function l(){}},798:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function s(e,t,n,s,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var o=new i(n,s||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function o(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,s=n.length,a=Array(s);i<s;i++)a[i]=n[i].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},o.prototype.emit=function(e,t,n,i,s,a){var o=r?r+e:e;if(!this._events[o])return!1;var l,u,c=this._events[o],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,n),!0;case 4:return c.fn.call(c.context,t,n,i),!0;case 5:return c.fn.call(c.context,t,n,i,s),!0;case 6:return c.fn.call(c.context,t,n,i,s,a),!0}for(u=1,l=Array(d-1);u<d;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var h,p=c.length;for(u=0;u<p;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),d){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,n);break;case 4:c[u].fn.call(c[u].context,t,n,i);break;default:if(!l)for(h=1,l=Array(d-1);h<d;h++)l[h-1]=arguments[h];c[u].fn.apply(c[u].context,l)}}return!0},o.prototype.on=function(e,t,r){return s(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return s(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,n,i){var s=r?r+e:e;if(!this._events[s])return this;if(!t)return a(this,s),this;var o=this._events[s];if(o.fn)o.fn!==t||i&&!o.once||n&&o.context!==n||a(this,s);else{for(var l=0,u=[],c=o.length;l<c;l++)(o[l].fn!==t||i&&!o[l].once||n&&o[l].context!==n)&&u.push(o[l]);u.length?this._events[s]=1===u.length?u[0]:u:a(this,s)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let s=i/2|0,a=n+s;0>=r(e[a],t)?(n=++a,i-=s+1):i=s}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let s=(e,t,r)=>new Promise((s,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void s(e);let o=setTimeout(()=>{if("function"==typeof r){try{s(r())}catch(e){a(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),a(o)},t);n(e.then(s,a),()=>{clearTimeout(o)})});e.exports=s,e.exports.default=s,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),s=()=>{},a=new t.TimeoutError;class o extends e{constructor(e){var t,n,i,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=s,this._resolveIdle=s,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(i=e.interval)?void 0:i.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=s,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=s,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let s=async()=>{this._pendingCount++,this._intervalCount++;try{let s=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(a)});n(await s)}catch(e){i(e)}this._next()};this._queue.enqueue(s,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=o})(),e.exports=i})()},825:(e,t,r)=>{"use strict";r.d(t,{o:()=>s});var n=r(352);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.l.get(t,r,i);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==a)return n.l.get(t,a,i)},set(t,r,i,s){if("symbol"==typeof r)return n.l.set(t,r,i,s);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return n.l.set(t,o??r,i,s)},has(t,r){if("symbol"==typeof r)return n.l.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&n.l.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return n.l.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||n.l.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.l.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},826:(e,t,r)=>{"use strict";r.d(t,{X:()=>function e(t){if((0,s.p)(t)||"object"==typeof t&&null!==t&&"digest"in t&&"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t.digest||(0,o.h)(t)||(0,a.I3)(t)||"object"==typeof t&&null!==t&&t.$$typeof===i||(0,n.T)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}});var n=r(797);let i=Symbol.for("react.postpone");var s=r(931),a=r(601),o=r(500)},833:(e,t,r)=>{"use strict";r.d(t,{XN:()=>i,FP:()=>n});let n=(0,r(749).xl)();function i(e){let t=n.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}},843:(e,t,r)=>{"use strict";let n,i,s;r.r(t),r.d(t,{default:()=>oI});var a={};async function o(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(a),r.d(a,{config:()=>ok,default:()=>oT,middleware:()=>ox});let l=null;async function u(){if("phase-production-build"===process.env.NEXT_PHASE)return;l||(l=o());let e=await l;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function c(...e){let t=await o();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let d=null;function h(){return d||(d=u()),d}function p(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(p(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(p(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(p(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),h();class f extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class g extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class m extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let y="_N_T_",v={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function _(e){var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function b(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(..._(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function w(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...v,GROUP:{builtinReact:[v.reactServerComponents,v.actionBrowser],serverOnly:[v.reactServerComponents,v.actionBrowser,v.instrument,v.middleware],neutralTarget:[v.apiNode,v.apiEdge],clientOnly:[v.serverSideRendering,v.appPagesBrowser],bundled:[v.reactServerComponents,v.actionBrowser,v.serverSideRendering,v.appPagesBrowser,v.shared,v.instrument,v.middleware],appPages:[v.reactServerComponents,v.serverSideRendering,v.appPagesBrowser,v.actionBrowser]}});let S=Symbol("response"),k=Symbol("passThrough"),E=Symbol("waitUntil");class x{constructor(e,t){this[k]=!1,this[E]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[S]||(this[S]=Promise.resolve(e))}passThroughOnException(){this[k]=!0}waitUntil(e){if("external"===this[E].kind)return(0,this[E].function)(e);this[E].promises.push(e)}}class T extends x{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new f({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new f({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function R(e){return e.replace(/\/$/,"")||"/"}function C(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function O(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=C(e);return""+t+r+n+i}function I(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=C(e);return""+r+t+n+i}function P(e,t){if("string"!=typeof e)return!1;let{pathname:r}=C(e);return r===t||r.startsWith(t+"/")}let A=new WeakMap;function N(e,t){let r;if(!t)return{pathname:e};let n=A.get(t);n||(n=t.map(e=>e.toLowerCase()),A.set(t,n));let i=e.split("/",2);if(!i[1])return{pathname:e};let s=i[1].toLowerCase(),a=n.indexOf(s);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let U=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function j(e,t){return new URL(String(e).replace(U,"localhost"),t&&String(t).replace(U,"localhost"))}let L=Symbol("NextURLInternal");class M{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[L]={url:j(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let s=function(e,t){var r,n;let{basePath:i,i18n:s,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};i&&P(o.pathname,i)&&(o.pathname=function(e,t){if(!P(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,i),o.basePath=i);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");o.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):N(o.pathname,s.locales);o.locale=e.detectedLocale,o.pathname=null!=(n=e.pathname)?n:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):N(l,s.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[L].url.pathname,{nextConfig:this[L].options.nextConfig,parseData:!0,i18nProvider:this[L].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[L].url,this[L].options.headers);this[L].domainLocale=this[L].options.i18nProvider?this[L].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=s.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(i=s.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return s}}(null==(t=this[L].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[L].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[L].options.nextConfig)||null==(n=i.i18n)?void 0:n.defaultLocale);this[L].url.pathname=s.pathname,this[L].defaultLocale=o,this[L].basePath=s.basePath??"",this[L].buildId=s.buildId,this[L].locale=s.locale??o,this[L].trailingSlash=s.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(P(i,"/api")||P(i,"/"+t.toLowerCase()))?e:O(e,"/"+t)}((e={basePath:this[L].basePath,buildId:this[L].buildId,defaultLocale:this[L].options.forceLocale?void 0:this[L].defaultLocale,locale:this[L].locale,pathname:this[L].url.pathname,trailingSlash:this[L].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=R(t)),e.buildId&&(t=I(O(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=O(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:I(t,"/"):R(t)}formatSearch(){return this[L].url.search}get buildId(){return this[L].buildId}set buildId(e){this[L].buildId=e}get locale(){return this[L].locale??""}set locale(e){var t,r;if(!this[L].locale||!(null==(r=this[L].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[L].locale=e}get defaultLocale(){return this[L].defaultLocale}get domainLocale(){return this[L].domainLocale}get searchParams(){return this[L].url.searchParams}get host(){return this[L].url.host}set host(e){this[L].url.host=e}get hostname(){return this[L].url.hostname}set hostname(e){this[L].url.hostname=e}get port(){return this[L].url.port}set port(e){this[L].url.port=e}get protocol(){return this[L].url.protocol}set protocol(e){this[L].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[L].url=j(e),this.analyze()}get origin(){return this[L].url.origin}get pathname(){return this[L].url.pathname}set pathname(e){this[L].url.pathname=e}get hash(){return this[L].url.hash}set hash(e){this[L].url.hash=e}get search(){return this[L].url.search}set search(e){this[L].url.search=e}get password(){return this[L].url.password}set password(e){this[L].url.password=e}get username(){return this[L].url.username}set username(e){this[L].url.username=e}get basePath(){return this[L].basePath}set basePath(e){this[L].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new M(String(this),this[L].options)}}var D=r(433);let q=Symbol("internal request");class z extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);w(r),e instanceof Request?super(e,t):super(r,t);let n=new M(r,{headers:b(this.headers),nextConfig:t.nextConfig});this[q]={cookies:new D.tm(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[q].cookies}get nextUrl(){return this[q].nextUrl}get page(){throw new g}get ua(){throw new m}get url(){return this[q].url}}var $=r(352);let H=Symbol("internal response"),B=new Set([301,302,303,307,308]);function K(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class W extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new D.VO(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let s=Reflect.apply(e[n],e,i),a=new Headers(r);return s instanceof D.VO&&r.set("x-middleware-set-cookie",s.getAll().map(e=>(0,D.Ud)(e)).join(",")),K(t,a),s};default:return $.l.get(e,n,i)}}});this[H]={cookies:n,url:t.url?new M(t.url,{headers:b(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[H].cookies}static json(e,t){let r=Response.json(e,t);return new W(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!B.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",w(e)),new W(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",w(e)),K(t,r),new W(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),K(e,t),new W(null,{...e,headers:t})}}function J(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}let F="Next-Router-Prefetch",V=["RSC","Next-Router-State-Tree",F,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],X="_rsc";var G=r(825),Y=r(246),Q=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(Q||{}),Z=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(Z||{}),ee=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(ee||{}),et=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(et||{}),er=function(e){return e.startServer="startServer.startServer",e}(er||{}),en=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(en||{}),ei=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(ei||{}),es=function(e){return e.executeRoute="Router.executeRoute",e}(es||{}),ea=function(e){return e.runHandler="Node.runHandler",e}(ea||{}),eo=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(eo||{}),el=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(el||{}),eu=function(e){return e.execute="Middleware.execute",e}(eu||{});let ec=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],ed=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eh(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:ep,propagation:ef,trace:eg,SpanStatusCode:em,SpanKind:ey,ROOT_CONTEXT:ev}=n=r(930);class e_ extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eb=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof e_})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:em.ERROR,message:null==t?void 0:t.message})),e.end()},ew=new Map,eS=n.createContextKey("next.rootSpanId"),ek=0,eE=()=>ek++,ex={set(e,t,r){e.push({key:t,value:r})}};class eT{getTracerInstance(){return eg.getTracer("next.js","0.0.1")}getContext(){return ep}getTracePropagationData(){let e=ep.active(),t=[];return ef.inject(e,t,ex),t}getActiveScopeSpan(){return eg.getSpan(null==ep?void 0:ep.active())}withPropagatedContext(e,t,r){let n=ep.active();if(eg.getSpanContext(n))return t();let i=ef.extract(n,e,r);return ep.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:s,options:a}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},o=a.spanName??r;if(!ec.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return s();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),u=!1;l?(null==(t=eg.getSpanContext(l))?void 0:t.isRemote)&&(u=!0):(l=(null==ep?void 0:ep.active())??ev,u=!0);let c=eE();return a.attributes={"next.span_name":o,"next.span_type":r,...a.attributes},ep.with(l.setValue(eS,c),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{ew.delete(c),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&ed.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};u&&ew.set(c,new Map(Object.entries(a.attributes??{})));try{if(s.length>1)return s(e,t=>eb(e,t));let t=s(e);if(eh(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eb(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eb(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return ec.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let s=arguments.length-1,a=arguments[s];if("function"!=typeof a)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(ep.active(),a);return t.trace(r,e,(e,t)=>(arguments[s]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?eg.setSpan(ep.active(),e):void 0}getRootSpanAttributes(){let e=ep.active().getValue(eS);return ew.get(e)}setRootSpanAttribute(e,t){let r=ep.active().getValue(eS),n=ew.get(r);n&&n.set(e,t)}}let eR=(()=>{let e=new eT;return()=>e})(),eC="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eC);class eO{constructor(e,t,r,n){var i;let s=e&&function(e,t){let r=G.o.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(i=r.get(eC))?void 0:i.value;this._isEnabled=!!(!s&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:eC,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:eC,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eI(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of _(r))n.append("set-cookie",e);for(let e of new D.VO(n).getAll())t.set(e)}}var eP=r(833),eA=r(798),eN=r.n(eA);class eU extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}var ej=r(542);class eL{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new eL(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let eM=Symbol.for("@next/cache-handlers-map"),eD=Symbol.for("@next/cache-handlers-set"),eq=globalThis;function ez(){if(eq[eM])return eq[eM].entries()}async function e$(e,t){if(!e)return t();let r=eH(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eH(e));await eK(e,t)}}function eH(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function eB(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eq[eD])return eq[eD].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function eK(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([eB(r,e.incrementalCache),...Object.values(n),...i])}var eW=r(968),eJ=r(212);class eF{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eN()),this.callbackQueue.pause()}after(e){if(eh(e))this.waitUntil||eV(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){this.waitUntil||eV();let t=eP.FP.getStore();t&&this.workUnitStores.add(t);let r=eJ.Z.getStore(),n=r?r.rootTaskSpawnPhase:null==t?void 0:t.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let i=(0,eW.cg)(async()=>{try{await eJ.Z.run({rootTaskSpawnPhase:n},()=>e())}catch(e){this.reportTaskError("function",e)}});this.callbackQueue.add(i)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=ej.J.getStore();if(!e)throw Object.defineProperty(new eU("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return e$(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new eU("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function eV(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function eX(e){let t,r={then:(n,i)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,i))};return r}class eG{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function eY(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let eQ=Symbol.for("@next/request-context"),eZ=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t};async function e0(e,t,r){let n=[],i=r&&r.size>0;for(let t of eZ(e))t=`${y}${t}`,n.push(t);if(t.pathname&&!i){let e=`${y}${t.pathname}`;n.push(e)}return{tags:n,expirationsByCacheKind:function(e){let t=new Map,r=ez();if(r)for(let[n,i]of r)"getExpiration"in i&&t.set(n,eX(async()=>i.getExpiration(...e)));return t}(n)}}class e1 extends z{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new f({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new f({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new f({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let e2={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},e4=(e,t)=>eR().withPropagatedContext(e.headers,t,e2),e3=!1;async function e5(e){var t;let n,i;if(!e3&&(e3=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(253);e(),e4=t(e4)}await h();let s=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new M(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let o=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),u=l.has("x-nextjs-data"),c="1"===l.get("RSC");u&&"/index"===a.pathname&&(a.pathname="/");let d=new Map;if(!s)for(let e of V){let t=e.toLowerCase(),r=l.get(t);null!==r&&(d.set(t,r),l.delete(t))}let p=new e1({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(X),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});u&&Object.defineProperty(p,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:eY()})}));let f=e.request.waitUntil??(null==(t=function(){let e=globalThis[eQ];return null==e?void 0:e.get()}())?void 0:t.waitUntil),g=new T({request:p,page:e.page,context:f?{waitUntil:f}:void 0});if((n=await e4(p,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=g.waitUntil.bind(g),r=new eG;return eR().trace(eu.execute,{spanName:`middleware ${p.method} ${p.nextUrl.pathname}`,attributes:{"http.target":p.nextUrl.pathname,"http.method":p.method}},async()=>{try{var n,s,a,l,u,c;let d=eY(),h=await e0("/",p.nextUrl,null),f=(u=p.nextUrl,c=e=>{i=e},function(e,t,r,n,i,s,a,o,l,u,c){function d(e){r&&r.setHeader("Set-Cookie",e)}let h={};return{type:"request",phase:e,implicitTags:s,url:{pathname:n.pathname,search:n.search??""},rootParams:i,get headers(){return h.headers||(h.headers=function(e){let t=G.o.from(e);for(let e of V)t.delete(e.toLowerCase());return G.o.seal(t)}(t.headers)),h.headers},get cookies(){if(!h.cookies){let e=new D.tm(G.o.from(t.headers));eI(t,e),h.cookies=Y.Ck.seal(e)}return h.cookies},set cookies(value){h.cookies=value},get mutableCookies(){if(!h.mutableCookies){let e=function(e,t){let r=new D.tm(G.o.from(e));return Y.K8.wrap(r,t)}(t.headers,a||(r?d:void 0));eI(t,e),h.mutableCookies=e}return h.mutableCookies},get userspaceMutableCookies(){return h.userspaceMutableCookies||(h.userspaceMutableCookies=(0,Y.hm)(this.mutableCookies)),h.userspaceMutableCookies},get draftMode(){return h.draftMode||(h.draftMode=new eO(l,t,this.cookies,this.mutableCookies)),h.draftMode},renderResumeDataCache:o??null,isHmrRefresh:u,serverComponentsHmrCache:c||globalThis.__serverComponentsHmrCache}}("action",p,void 0,u,{},h,c,void 0,d,!1,void 0)),m=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:s,previouslyRevalidatedTags:a}){var o;let l={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(o=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?o:"/"+o,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:s,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new eF({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=ez();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,eX(async()=>n.refreshTags()));return e}()};return r.store=l,l}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(s=e.request.nextConfig)||null==(n=s.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:p.headers.has(F),buildId:o??"",previouslyRevalidatedTags:[]});return await ej.J.run(m,()=>eP.FP.run(f,e.handler,p,g))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(p,g)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let m=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&m&&(c||!s)){let t=new M(m,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});s||t.host!==p.nextUrl.host||(t.buildId=o||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=J(t.toString(),a.toString());!s&&u&&n.headers.set("x-nextjs-rewrite",r),c&&i&&(a.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let y=null==n?void 0:n.headers.get("Location");if(n&&y&&!s){let t=new M(y,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===a.host&&(t.buildId=o||t.buildId,n.headers.set("Location",t.toString())),u&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",J(t.toString(),a.toString()).url))}let v=n||W.next(),_=v.headers.get("x-middleware-override-headers"),b=[];if(_){for(let[e,t]of d)v.headers.set(`x-middleware-request-${e}`,t),b.push(e);b.length>0&&v.headers.set("x-middleware-override-headers",_+","+b.join(","))}return{response:v,waitUntil:("internal"===g[E].kind?Promise.all(g[E].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:p.fetchMetrics}}function e6(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function e8(e){return e&&e.sensitive?"":"i"}function e9(e,t,r){var n;return e instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,i=r.exec(e.source);i;)t.push({name:i[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(e,t):Array.isArray(e)?(n=e.map(function(e){return e9(e,t,r).source}),new RegExp("(?:".concat(n.join("|"),")"),e8(r))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,s=r.start,a=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,u=r.delimiter,c=r.endsWith,d="[".concat(e6(void 0===c?"":c),"]|$"),h="[".concat(e6(void 0===u?"/#?":u),"]"),p=void 0===s||s?"^":"",f=0;f<e.length;f++){var g=e[f];if("string"==typeof g)p+=e6(l(g));else{var m=e6(l(g.prefix)),y=e6(l(g.suffix));if(g.pattern)if(t&&t.push(g),m||y)if("+"===g.modifier||"*"===g.modifier){var v="*"===g.modifier?"?":"";p+="(?:".concat(m,"((?:").concat(g.pattern,")(?:").concat(y).concat(m,"(?:").concat(g.pattern,"))*)").concat(y,")").concat(v)}else p+="(?:".concat(m,"(").concat(g.pattern,")").concat(y,")").concat(g.modifier);else{if("+"===g.modifier||"*"===g.modifier)throw TypeError('Can not repeat "'.concat(g.name,'" without a prefix and suffix'));p+="(".concat(g.pattern,")").concat(g.modifier)}else p+="(?:".concat(m).concat(y,")").concat(g.modifier)}}if(void 0===a||a)i||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(d,")"):"$";else{var _=e[e.length-1],b="string"==typeof _?h.indexOf(_[_.length-1])>-1:void 0===_;i||(p+="(?:".concat(h,"(?=").concat(d,"))?")),b||(p+="(?=".concat(h,"|").concat(d,")"))}return new RegExp(p,e8(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[s++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=s;continue}if("("===n){var o=1,l="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '.concat(s));for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at ".concat(s));l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,s=t.delimiter,a=void 0===s?"/#?":s,o=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u],i=n.type,s=n.index;throw TypeError("Unexpected ".concat(i," at ").concat(s,", expected ").concat(e))},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t},f=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},g=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||f(r)?"[^".concat(e6(a),"]+?"):"(?:(?!".concat(e6(r),")[^").concat(e6(a),"])+?")};u<r.length;){var m=d("CHAR"),y=d("NAME"),v=d("PATTERN");if(y||v){var _=m||"";-1===i.indexOf(_)&&(c+=_,_=""),c&&(o.push(c),c=""),o.push({name:y||l++,prefix:_,suffix:"",pattern:v||g(_),modifier:d("MODIFIER")||""});continue}var b=m||d("ESCAPED_CHAR");if(b){c+=b;continue}if(c&&(o.push(c),c=""),d("OPEN")){var _=p(),w=d("NAME")||"",S=d("PATTERN")||"",k=p();h("CLOSE"),o.push({name:w||(S?l++:""),pattern:w&&!S?g(_):S,prefix:_,suffix:k,modifier:d("MODIFIER")||""});continue}h("END")}return o}(e,r),t,r)}var e7=e=>{try{return e9(e)}catch(t){throw Error(`Invalid path: ${e}.
Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${t.message}`)}};function te(e,t){try{var r,n,i,s,a,o;return r=[],n=e9(e,r,t),i=r,s=t,void 0===s&&(s={}),a=s.decode,o=void 0===a?function(e){return e}:a,function(e){var t=n.exec(e);if(!t)return!1;for(var r=t[0],s=t.index,a=Object.create(null),l=1;l<t.length;l++)!function(e){if(void 0!==t[e]){var r=i[e-1];"*"===r.modifier||"+"===r.modifier?a[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return o(e,r)}):a[r.name]=o(t[e],r)}}(l);return{path:r,index:s,params:a}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}var tt=e=>e.map(e=>e instanceof RegExp?e:e7(e)),tr=e=>{let t=tt([e||""].flat().filter(Boolean));return e=>t.some(t=>t.test(e))},tn=Object.defineProperty,ti=Object.getOwnPropertyDescriptor,ts=Object.getOwnPropertyNames,ta=Object.prototype.hasOwnProperty,to=e=>{throw TypeError(e)},tl=(e,t,r)=>t.has(e)||to("Cannot "+r),tu=(e,t,r)=>(tl(e,t,"read from private field"),r?r.call(e):t.get(e)),tc=(e,t,r)=>t.has(e)?to("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),td=(e,t,r,n)=>(tl(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),th=(e,t,r)=>(tl(e,t,"access private method"),r),tp={InvalidSecretKey:"clerk_key_invalid"},tf={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},tg={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable."},tm=class e extends Error{constructor({action:t,message:r,reason:n}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=n,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}};let ty=crypto;var tv=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,t_=fetch.bind(globalThis),tb={crypto:ty,get fetch(){return t_},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},tw={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let n=e.length;for(;"="===e[n-1];)if(--n,!r.loose&&!((e.length-n)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(n*t.bits/8|0),s=0,a=0,o=0;for(let r=0;r<n;++r){let n=t.codes[e[r]];if(void 0===n)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|n,(s+=t.bits)>=8&&(s-=8,i[o++]=255&a>>s)}if(s>=t.bits||255&a<<8-s)throw SyntaxError("Unexpected end of data");return i})(e,tS,t)},tS={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},tk={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},tE="RSASSA-PKCS1-v1_5",tx={RS256:tE,RS384:tE,RS512:tE},tT=Object.keys(tk),tR=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),tC=(e,t)=>{let r=[t].flat().filter(e=>!!e),n=[e].flat().filter(e=>!!e);if(r.length>0&&n.length>0){if("string"==typeof e){if(!r.includes(e))throw new tm({action:tg.EnsureClerkJWT,reason:tf.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(tR(e)&&!e.some(e=>r.includes(e)))throw new tm({action:tg.EnsureClerkJWT,reason:tf.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},tO=e=>{if(void 0!==e&&"JWT"!==e)throw new tm({action:tg.EnsureClerkJWT,reason:tf.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},tI=e=>{if(!tT.includes(e))throw new tm({action:tg.EnsureClerkJWT,reason:tf.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${tT}.`})},tP=e=>{if("string"!=typeof e)throw new tm({action:tg.EnsureClerkJWT,reason:tf.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},tA=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new tm({reason:tf.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},tN=(e,t)=>{if("number"!=typeof e)throw new tm({action:tg.EnsureClerkJWT,reason:tf.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()<=r.getTime()-t)throw new tm({reason:tf.TokenExpired,message:`JWT is expired. Expiry date: ${n.toUTCString()}, Current date: ${r.toUTCString()}.`})},tU=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new tm({action:tg.EnsureClerkJWT,reason:tf.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new tm({reason:tf.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})},tj=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new tm({action:tg.EnsureClerkJWT,reason:tf.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new tm({reason:tf.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})};async function tL(e,t){let{header:r,signature:n,raw:i}=e,s=new TextEncoder().encode([i.header,i.payload].join(".")),a=function(e){let t=tk[e],r=tx[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${tT.join(",")}.`);return{hash:{name:tk[e]},name:tx[e]}}(r.alg);try{let e=await function(e,t,r){if("object"==typeof e)return tb.crypto.subtle.importKey("jwk",e,t,!1,[r]);let n=function(e){let t=tv(e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,"")),r=new Uint8Array(new ArrayBuffer(t.length));for(let e=0,n=t.length;e<n;e++)r[e]=t.charCodeAt(e);return r}(e),i="sign"===r?"pkcs8":"spki";return tb.crypto.subtle.importKey(i,n,t,!1,[r])}(t,a,"verify");return{data:await tb.crypto.subtle.verify(a.name,e,n,s)}}catch(e){return{errors:[new tm({reason:tf.TokenInvalidSignature,message:e?.message})]}}}function tM(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new tm({reason:tf.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,n,i]=t,s=new TextDecoder,a=JSON.parse(s.decode(tw.parse(r,{loose:!0}))),o=JSON.parse(s.decode(tw.parse(n,{loose:!0})));return{data:{header:a,payload:o,signature:tw.parse(i,{loose:!0}),raw:{header:r,payload:n,signature:i,text:e}}}}async function tD(e,t){let{audience:r,authorizedParties:n,clockSkewInMs:i,key:s}=t,a=i||5e3,{data:o,errors:l}=tM(e);if(l)return{errors:l};let{header:u,payload:c}=o;try{let{typ:e,alg:t}=u;tO(e),tI(t);let{azp:i,sub:s,aud:o,iat:l,exp:d,nbf:h}=c;tP(s),tC([o],[r]),tA(i,n),tN(d,a),tU(h,a),tj(l,a)}catch(e){return{errors:[e]}}let{data:d,errors:h}=await tL(o,s);return h?{errors:[new tm({action:tg.EnsureClerkJWT,reason:tf.TokenVerificationFailed,message:`Error verifying JWT signature. ${h[0]}`})]}:d?{data:c}:{errors:[new tm({reason:tf.TokenInvalidSignature,message:"JWT signature is invalid."})]}}var tq={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!0,jitter:!0},tz=async e=>new Promise(t=>setTimeout(t,e)),t$=(e,t)=>t?e*(1+Math.random()):e,tH=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=t$(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await tz(r()),t++}},tB=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:s,factor:a,retryImmediately:o,jitter:l}={...tq,...t},u=tH({initialDelay:i,maxDelayBetweenRetries:s,factor:a,jitter:l});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;o&&1===r?await tz(t$(100,l)):await u()}},tK=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,tW=[".lcl.dev",".lclstage.dev",".lclclerk.com"],tJ=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],tF=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],tV=[".accountsstage.dev"],tX="https://api.clerk.com",tG="pk_live_";function tY(e,t={}){if(!(e=e||"")||!tQ(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!tQ(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(tG)?"production":"development",n=tv(e.split("_")[2]);return n=n.slice(0,-1),t.proxyUrl?n=t.proxyUrl:"development"!==r&&t.domain&&(n=`clerk.${t.domain}`),{instanceType:r,frontendApi:n}}function tQ(e=""){try{let t=e.startsWith(tG)||e.startsWith("pk_test_"),r=tv(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function tZ(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function t0(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return tK(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var t1=(e,t)=>`${e}_${t}`,t2=()=>!1,t4=()=>{try{return!0}catch{}return!1},t3=new Set,t5=(e,t,r)=>{let n=t2()||t4(),i=r??e;t3.has(i)||n||(t3.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))};function t6(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn}}}var t8=class e extends Error{constructor(t,{data:r,status:n,clerkTraceId:i}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=n,this.message=t,this.clerkTraceId=i,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(t6):[]}(r)}},t9=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function t7({packageName:e,customMessages:t}){let r=e,n={...t9,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(n.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}var re=r(637),rt={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},rr=new Set(["first_factor","second_factor","multi_factor"]),rn=new Set(["strict_mfa","strict","moderate","lax"]),ri=e=>"number"==typeof e&&e>0,rs=e=>rr.has(e),ra=e=>rn.has(e),ro=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(e.permission):e.role?n===e.role:null:null},rl=e=>{if(!e)return!1;let t="string"==typeof e&&ra(e),r="object"==typeof e&&rs(e.level)&&ri(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?rt[e]:e).bind(null,e)},ru=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=rl(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[s,a]=t,o=-1!==s?i>s:null,l=-1!==a?i>a:null;switch(n){case"first_factor":return o;case"second_factor":return -1!==a?l:o;case"multi_factor":return -1===a?o:o&&l}},rc=e=>t=>{if(!e.userId)return!1;let r=ro(t,e),n=ru(t,e);return[r,n].some(e=>null===e)?[r,n].some(e=>!0===e):[r,n].every(e=>!0===e)},rd=r(682),rh="https://api.clerk.com",rp="@clerk/backend@1.25.8",rf="2024-10-01",rg={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count"},rm={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:rg.DevBrowser,Handshake:rg.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason"},ry={Cookies:rg,Headers:{AuthToken:"x-clerk-auth-token",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthReason:"x-clerk-auth-reason",AuthMessage:"x-clerk-auth-message",ClerkUrl:"x-clerk-clerk-url",EnableDebug:"x-clerk-debug",ClerkRequestData:"x-clerk-request-data",ClerkRedirectTo:"x-clerk-redirect-to",CloudFrontForwardedProto:"cloudfront-forwarded-proto",Authorization:"authorization",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",ForwardedHost:"x-forwarded-host",Accept:"accept",Referrer:"referer",UserAgent:"user-agent",Origin:"origin",Host:"host",ContentType:"content-type",SecFetchDest:"sec-fetch-dest",Location:"location",CacheControl:"cache-control"},ContentTypes:{Json:"application/json"},QueryParameters:rm},rv=RegExp("(?<!:)/{1,}","g");function r_(...e){return e.filter(e=>e).join("/").replace(rv,"/")}var rb=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},rw="/accountless_applications",rS=class extends rb{async createAccountlessApplication(){return this.request({method:"POST",path:rw})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:r_(rw,"complete")})}},rk="/allowlist_identifiers",rE=class extends rb{async getAllowlistIdentifierList(){return this.request({method:"GET",path:rk,queryParams:{paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:rk,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:r_(rk,e)})}},rx="/clients",rT=class extends rb{async getClientList(e={}){return this.request({method:"GET",path:rx,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:r_(rx,e)})}verifyClient(e){return this.request({method:"POST",path:r_(rx,"verify"),bodyParams:{token:e}})}},rR=class extends rb{async deleteDomain(e){return this.request({method:"DELETE",path:r_("/domains",e)})}},rC="/email_addresses",rO=class extends rb{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:r_(rC,e)})}async createEmailAddress(e){return this.request({method:"POST",path:rC,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:r_(rC,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:r_(rC,e)})}},rI="/invitations",rP=class extends rb{async getInvitationList(e={}){return this.request({method:"GET",path:rI,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:rI,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:r_(rI,e,"revoke")})}},rA="/organizations",rN=class extends rb{async getOrganizationList(e){return this.request({method:"GET",path:rA,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:rA,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:r_(rA,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:r_(rA,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new tb.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:r_(rA,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:r_(rA,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:r_(rA,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:r_(rA,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:r_(rA,t,"memberships"),queryParams:r})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:r_(rA,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...n}=e;return this.requireId(t),this.request({method:"PATCH",path:r_(rA,t,"memberships",r),bodyParams:n})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...n}=e;return this.request({method:"PATCH",path:r_(rA,t,"memberships",r,"metadata"),bodyParams:n})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:r_(rA,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:r_(rA,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:r_(rA,t,"invitations"),bodyParams:r})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:r_(rA,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...n}=e;return this.requireId(t),this.request({method:"POST",path:r_(rA,t,"invitations",r,"revoke"),bodyParams:n})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:r_(rA,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:r_(rA,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...n}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:r_(rA,t,"domains",r),bodyParams:n})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:r_(rA,t,"domains",r)})}},rU="/phone_numbers",rj=class extends rb{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:r_(rU,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:rU,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:r_(rU,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:r_(rU,e)})}},rL="/redirect_urls",rM=class extends rb{async getRedirectUrlList(){return this.request({method:"GET",path:rL,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:r_(rL,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:rL,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:r_(rL,e)})}},rD="/sessions",rq=class extends rb{async getSessionList(e={}){return this.request({method:"GET",path:rD,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:r_(rD,e)})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:r_(rD,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:r_(rD,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),this.request({method:"POST",path:r_(rD,e,"tokens",t||"")})}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...n}=t;return this.request({method:"POST",path:r_(rD,e,"refresh"),bodyParams:n,queryParams:{suffixed_cookies:r}})}},rz="/sign_in_tokens",r$=class extends rb{async createSignInToken(e){return this.request({method:"POST",path:rz,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:r_(rz,e,"revoke")})}},rH=t7({packageName:"@clerk/backend"}),{isDevOrStagingUrl:rB}=function(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=tJ.some(e=>r.endsWith(e)),e.set(r,n)),n}}}(),rK="/users",rW=class extends rb{async getUserList(e={}){let{limit:t,offset:r,orderBy:n,...i}=e,[s,a]=await Promise.all([this.request({method:"GET",path:rK,queryParams:e}),this.getCount(i)]);return{data:s,totalCount:a}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:r_(rK,e)})}async createUser(e){return this.request({method:"POST",path:rK,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:r_(rK,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new tb.FormData;return r.append("file",t?.file),this.request({method:"POST",path:r_(rK,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:r_(rK,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:r_(rK,e)})}async getCount(e={}){return this.request({method:"GET",path:r_(rK,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),n=r?t:`oauth_${t}`;return r&&t5("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:r_(rK,e,"oauth_access_tokens",n),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:r_(rK,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:r_(rK,t,"organization_memberships"),queryParams:{limit:r,offset:n}})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:r_(rK,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:r_(rK,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:r_(rK,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:r_(rK,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:r_(rK,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:r_(rK,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:r_(rK,e,"profile_image")})}},rJ="/saml_connections",rF=class extends rb{async getSamlConnectionList(e={}){return this.request({method:"GET",path:rJ,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:rJ,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:r_(rJ,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:r_(rJ,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:r_(rJ,e)})}},rV=class extends rb{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}};function rX(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var rG=class e{constructor(e,t,r,n){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=n}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},rY=class e{constructor(e,t,r,n,i){this.id=e,this.identifier=t,this.createdAt=r,this.updatedAt=n,this.invitationId=i}static fromJSON(t){return new e(t.id,t.identifier,t.created_at,t.updated_at,t.invitation_id)}},rQ=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=n,this.country=i,this.browserVersion=s,this.browserName=a,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},rZ=class e{constructor(e,t,r,n,i,s,a,o,l,u,c,d=null){this.id=e,this.clientId=t,this.userId=r,this.status=n,this.lastActiveAt=i,this.expireAt=s,this.abandonAt=a,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=u,this.latestActivity=c,this.actor=d}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&rQ.fromJSON(t.latest_activity),t.actor)}},r0=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=n,this.signUpId=i,this.lastActiveSessionId=s,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>rZ.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},r1=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},r2=class e{constructor(e,t,r,n){this.object=e,this.id=t,this.slug=r,this.deleted=n}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},r4=class e{constructor(e,t,r,n,i,s,a,o,l,u,c){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=n,this.subject=i,this.body=s,this.bodyPlain=a,this.status=o,this.slug=l,this.data=u,this.deliveredByClerk=c}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},r3=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},r5=class e{constructor(e,t,r=null,n=null,i=null,s=null,a=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=n,this.expireAt=i,this.nonce=s,this.message=a}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},r6=class e{constructor(e,t,r,n){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&r5.fromJSON(t.verification),t.linked_to.map(e=>r3.fromJSON(e)))}},r8=class e{constructor(e,t,r,n,i,s,a,o,l,u,c={},d,h){this.id=e,this.provider=t,this.identificationId=r,this.externalId=n,this.approvedScopes=i,this.emailAddress=s,this.firstName=a,this.lastName=o,this.imageUrl=l,this.username=u,this.publicMetadata=c,this.label=d,this.verification=h}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.public_metadata,t.label,t.verification&&r5.fromJSON(t.verification))}},r9=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=n,this.updatedAt=i,this.status=s,this.url=a,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},r7={AccountlessApplication:"accountless_application",AllowlistIdentifier:"allowlist_identifier",Client:"client",Cookies:"cookies",Email:"email",EmailAddress:"email_address",Invitation:"invitation",OauthAccessToken:"oauth_access_token",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",PhoneNumber:"phone_number",RedirectUrl:"redirect_url",Session:"session",SignInToken:"sign_in_token",SmsMessage:"sms_message",User:"user",Token:"token",TotalCount:"total_count"},ne=class e{constructor(e,t,r,n={},i,s,a){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=n,this.label=i,this.scopes=s,this.tokenSecret=a}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret)}},nt=class e{constructor(e,t,r,n,i,s,a,o={},l={},u,c,d,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=n,this.hasImage=i,this.createdAt=s,this.updatedAt=a,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=u,this.adminDeleteEnabled=c,this.membersCount=d,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},nr=class e{constructor(e,t,r,n,i,s,a,o={},l={}){this.id=e,this.emailAddress=t,this.role=r,this.organizationId=n,this.createdAt=i,this.updatedAt=s,this.status=a,this.publicMetadata=o,this.privateMetadata=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.organization_id,t.created_at,t.updated_at,t.status,t.public_metadata,t.private_metadata);return r._raw=t,r}},nn=class e{constructor(e,t,r,n={},i={},s,a,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=n,this.privateMetadata=i,this.createdAt=s,this.updatedAt=a,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,nt.fromJSON(t.organization),ni.fromJSON(t.public_user_data));return r._raw=t,r}},ni=class e{constructor(e,t,r,n,i,s){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=n,this.hasImage=i,this.userId=s}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},ns=class e{constructor(e,t,r,n,i,s){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=n,this.verification=i,this.linkedTo=s}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&r5.fromJSON(t.verification),t.linked_to.map(e=>r3.fromJSON(e)))}},na=class e{constructor(e,t,r,n){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},no=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.userId=t,this.token=r,this.status=n,this.url=i,this.createdAt=s,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},nl=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=n,this.status=i,this.phoneNumberId=s,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},nu=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},nc=class e{constructor(e,t,r,n,i,s,a,o,l,u){this.id=e,this.name=t,this.domain=r,this.active=n,this.provider=i,this.syncUserAttributes=s,this.allowSubdomains=a,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=u}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},nd=class e{constructor(e,t,r,n,i,s,a,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=n,this.emailAddress=i,this.firstName=s,this.lastName=a,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&r5.fromJSON(t.verification),t.saml_connection&&nc.fromJSON(t.saml_connection))}},nh=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&r5.fromJSON(t.verification))}},np=class e{constructor(e,t,r,n,i,s,a,o,l,u,c,d,h,p,f,g,m,y,v,_={},b={},w={},S=[],k=[],E=[],x=[],T=[],R,C,O=null,I,P){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=n,this.twoFactorEnabled=i,this.banned=s,this.locked=a,this.createdAt=o,this.updatedAt=l,this.imageUrl=u,this.hasImage=c,this.primaryEmailAddressId=d,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=g,this.username=m,this.firstName=y,this.lastName=v,this.publicMetadata=_,this.privateMetadata=b,this.unsafeMetadata=w,this.emailAddresses=S,this.phoneNumbers=k,this.web3Wallets=E,this.externalAccounts=x,this.samlAccounts=T,this.lastActiveAt=R,this.createOrganizationEnabled=C,this.createOrganizationsLimit=O,this.deleteSelfEnabled=I,this.legalAcceptedAt=P,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>r6.fromJSON(e)),(t.phone_numbers||[]).map(e=>ns.fromJSON(e)),(t.web3_wallets||[]).map(e=>nh.fromJSON(e)),(t.external_accounts||[]).map(e=>r8.fromJSON(e)),(t.saml_accounts||[]).map(e=>nd.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}};function nf(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return r2.fromJSON(e);switch(e.object){case r7.AccountlessApplication:return rG.fromJSON(e);case r7.AllowlistIdentifier:return rY.fromJSON(e);case r7.Client:return r0.fromJSON(e);case r7.Cookies:return r1.fromJSON(e);case r7.EmailAddress:return r6.fromJSON(e);case r7.Email:return r4.fromJSON(e);case r7.Invitation:return r9.fromJSON(e);case r7.OauthAccessToken:return ne.fromJSON(e);case r7.Organization:return nt.fromJSON(e);case r7.OrganizationInvitation:return nr.fromJSON(e);case r7.OrganizationMembership:return nn.fromJSON(e);case r7.PhoneNumber:return ns.fromJSON(e);case r7.RedirectUrl:return na.fromJSON(e);case r7.SignInToken:return no.fromJSON(e);case r7.Session:return rZ.fromJSON(e);case r7.SmsMessage:return nl.fromJSON(e);case r7.Token:return nu.fromJSON(e);case r7.TotalCount:return e.total_count;case r7.User:return np.fromJSON(e);default:return e}}function ng(e){var t;return t=async t=>{let r,{secretKey:n,requireSecretKey:i=!0,apiUrl:s=rh,apiVersion:a="v1",userAgent:o=rp}=e,{path:l,method:u,queryParams:c,headerParams:d,bodyParams:h,formData:p}=t;i&&rX(n);let f=new URL(r_(s,a,l));if(c)for(let[e,t]of Object.entries(re({...c})))t&&[t].flat().forEach(t=>f.searchParams.append(e,t));let g={"Clerk-API-Version":rf,"User-Agent":o,...d};n&&(g.Authorization=`Bearer ${n}`);try{var m;if(p)r=await tb.fetch(f.href,{method:u,headers:g,body:p});else{g["Content-Type"]="application/json";let e="GET"!==u&&h&&Object.keys(h).length>0?{body:JSON.stringify(re(h,{deep:!1}))}:null;r=await tb.fetch(f.href,{method:u,headers:g,...e})}let e=r?.headers&&r.headers?.get(ry.Headers.ContentType)===ry.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:ny(t),status:r?.status,statusText:r?.statusText,clerkTraceId:nm(t,r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>nf(e))}:(m=t)&&"object"==typeof m&&"data"in m&&Array.isArray(m.data)&&void 0!==m.data?{data:t.data.map(e=>nf(e)),totalCount:t.total_count}:{data:nf(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:nm(e,r?.headers)};return{data:null,errors:ny(e),status:r?.status,statusText:r?.statusText,clerkTraceId:nm(e,r?.headers)}}},async(...e)=>{let{data:r,errors:n,totalCount:i,status:s,statusText:a,clerkTraceId:o}=await t(...e);if(n){let e=new t8(a||"",{data:[],status:s,clerkTraceId:o});throw e.errors=n,e}return void 0!==i?{data:r,totalCount:i}:r}}function nm(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function ny(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(t6):[]}return[]}function nv(e){let t=ng(e);return{__experimental_accountlessApplications:new rS(ng({...e,requireSecretKey:!1})),allowlistIdentifiers:new rE(t),clients:new rT(t),emailAddresses:new rO(t),invitations:new rP(t),organizations:new rN(t),phoneNumbers:new rj(t),redirectUrls:new rM(t),sessions:new rq(t),signInTokens:new r$(t),users:new rW(t),domains:new rR(t),samlConnections:new rF(t),testingTokens:new rV(t)}}var n_=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}},nb=e=>{let{fetcher:t,sessionToken:r,sessionId:n}=e||{};return async(e={})=>n?e.template?t(n,e.template):r:null},nw={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},nS={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",UnexpectedError:"unexpected-error"};function nk(e,t,r=new Headers,n){let i=function(e,t,r){let{act:n,sid:i,org_id:s,org_role:a,org_slug:o,org_permissions:l,sub:u,fva:c,sts:d}=r,h=nv(e),p=nb({sessionId:i,sessionToken:t,fetcher:async(...e)=>(await h.sessions.getToken(...e)).jwt}),f=c??null;return{actor:n,sessionClaims:r,sessionId:i,sessionStatus:d??null,userId:u,orgId:s,orgRole:a,orgSlug:o,orgPermissions:l,factorVerificationAge:f,getToken:p,has:rc({orgId:s,orgRole:a,orgPermissions:l,userId:u,factorVerificationAge:f}),debug:n_({...e,sessionToken:t})}}(e,n,t);return{status:nw.SignedIn,reason:null,message:null,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!0,toAuth:()=>i,headers:r,token:n}}function nE(e,t,r="",n=new Headers){return nx({status:nw.SignedOut,reason:t,message:r,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>({sessionClaims:null,sessionId:null,sessionStatus:null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:n_({...e,status:nw.SignedOut,reason:t,message:r})}),token:null})}var nx=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(ry.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(ry.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(ry.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},nT=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},nR=(...e)=>new nT(...e),nC=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(ry.Headers.ForwardedProto),n=e.headers.get(ry.Headers.ForwardedHost),i=e.headers.get(ry.Headers.Host),s=t.protocol,a=this.getFirstValueFromHeader(n)??i,o=this.getFirstValueFromHeader(r)??s?.replace(/[:/]/,""),l=a&&o?`${o}://${a}`:t.origin;return l===t.origin?nR(t):nR(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,rd.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},nO=(...e)=>e[0]instanceof nC?e[0]:new nC(...e),nI={},nP=0;function nA(e,t=!0){nI[e.kid]=e,nP=t?Date.now():-1}var nN="local";function nU(e){if(!nI[nN]){if(!e)throw new tm({action:tg.SetClerkJWTKey,message:"Missing local JWK.",reason:tf.LocalJWKMissing});nA({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return nI[nN]}async function nj({secretKey:e,apiUrl:t=rh,apiVersion:r="v1",kid:n,skipJwksCache:i}){if(i||function(){if(-1===nP)return!1;let e=Date.now()-nP>=3e5;return e&&(nI={}),e}()||!nI[n]){if(!e)throw new tm({action:tg.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:tf.RemoteJWKFailedToLoad});let{keys:n}=await tB(()=>nL(t,e,r));if(!n||!n.length)throw new tm({action:tg.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:tf.RemoteJWKFailedToLoad});n.forEach(e=>nA(e))}let s=nI[n];if(!s){let e=Object.values(nI).map(e=>e.kid).sort().join(", ");throw new tm({action:`Go to your Dashboard and validate your secret and public keys are correct. ${tg.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${n}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:tf.JWKKidMismatch})}return s}async function nL(e,t,r){if(!t)throw new tm({action:tg.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:tf.RemoteJWKFailedToLoad});let n=new URL(e);n.pathname=r_(n.pathname,r,"/jwks");let i=await tb.fetch(n.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":rf,"Content-Type":"application/json","User-Agent":rp}});if(!i.ok){let e=await i.json(),t=nM(e?.errors,tp.InvalidSecretKey);if(t){let e=tf.InvalidSecretKey;throw new tm({action:tg.ContactSupport,message:t.message,reason:e})}throw new tm({action:tg.ContactSupport,message:`Error loading Clerk JWKS from ${n.href} with code=${i.status}`,reason:tf.RemoteJWKFailedToLoad})}return i.json()}var nM=(e,t)=>e?e.find(e=>e.code===t):null;async function nD(e,t){let{data:r,errors:n}=tM(e);if(n)return{errors:n};let{header:i}=r,{kid:s}=i;try{let r;if(t.jwtKey)r=nU(t.jwtKey);else{if(!t.secretKey)return{errors:[new tm({action:tg.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:tf.JWKFailedToResolve})]};r=await nj({...t,kid:s})}return await tD(e,{...t,key:r})}catch(e){return{errors:[e]}}}var nq=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.sessionTokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(ry.Cookies.ClientUat),t=this.getCookie(ry.Cookies.ClientUat),r=this.getSuffixedCookie(ry.Cookies.Session)||"",n=this.getCookie(ry.Cookies.Session)||"";if(n&&!this.tokenHasIssuer(n))return!1;if(n&&!this.tokenBelongsToInstance(n))return!0;if(!e&&!r)return!1;let{data:i}=tM(n),s=i?.payload.iat||0,{data:a}=tM(r),o=a?.payload.iat||0;if("0"!==e&&"0"!==t&&s>o||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(a);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}initPublishableKeyValues(e){tY(e.publishableKey,{fatal:!0}),this.publishableKey=e.publishableKey;let t=tY(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain});this.instanceType=t.instanceType,this.frontendApi=t.frontendApi}initHeaderValues(){this.sessionTokenInHeader=this.parseAuthorizationHeader(this.getHeader(ry.Headers.Authorization)),this.origin=this.getHeader(ry.Headers.Origin),this.host=this.getHeader(ry.Headers.Host),this.forwardedHost=this.getHeader(ry.Headers.ForwardedHost),this.forwardedProto=this.getHeader(ry.Headers.CloudFrontForwardedProto)||this.getHeader(ry.Headers.ForwardedProto),this.referrer=this.getHeader(ry.Headers.Referrer),this.userAgent=this.getHeader(ry.Headers.UserAgent),this.secFetchDest=this.getHeader(ry.Headers.SecFetchDest),this.accept=this.getHeader(ry.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(ry.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(ry.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(ry.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(ry.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(ry.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(ry.QueryParameters.Handshake)||this.getCookie(ry.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(ry.Cookies.RedirectCount))||0}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie(t1(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=tM(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=tM(e);if(r)return!1;let n=t.payload.iss.replace(/https?:\/\//gi,"");return this.frontendApi===n}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},nz=async(e,t)=>new nq(t.publishableKey?await t0(t.publishableKey,tb.crypto.subtle):"",e,t),n$=e=>e.split(";")[0]?.split("=")[0],nH=e=>e.split(";")[0]?.split("=")[1];async function nB(e,{key:t}){let{data:r,errors:n}=tM(e);if(n)throw n[0];let{header:i,payload:s}=r,{typ:a,alg:o}=i;tO(a),tI(o);let{data:l,errors:u}=await tL(r,t);if(u)throw new tm({reason:tf.TokenVerificationFailed,message:`Error verifying handshake token. ${u[0]}`});if(!l)throw new tm({reason:tf.TokenInvalidSignature,message:"Handshake signature is invalid."});return s}async function nK(e,t){let r,{secretKey:n,apiUrl:i,apiVersion:s,jwksCacheTtlInMs:a,jwtKey:o,skipJwksCache:l}=t,{data:u,errors:c}=tM(e);if(c)throw c[0];let{kid:d}=u.header;if(o)r=nU(o);else if(n)r=await nj({secretKey:n,apiUrl:i,apiVersion:s,kid:d,jwksCacheTtlInMs:a,skipJwksCache:l});else throw new tm({action:tg.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:tf.JWKFailedToResolve});return await nB(e,{key:r})}var nW={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};async function nJ(e,t){let r=await nz(nO(e),t);if(rX(r.secretKey),r.isSatellite){var n=r.signInUrl,i=r.secretKey;if(!n&&tZ(i))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let s=function(e){let t=null;if(e?.personalAccountPatterns)try{t=te(e.personalAccountPatterns)}catch(t){throw Error(`Invalid personal account pattern "${e.personalAccountPatterns}": "${t}"`)}let r=null;if(e?.organizationPatterns)try{r=te(e.organizationPatterns)}catch(t){throw Error(`Clerk: Invalid organization pattern "${e.organizationPatterns}": "${t}"`)}return{OrganizationMatcher:r,PersonalAccountMatcher:t}}(t.organizationSyncOptions);async function a(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=(await nK(r.handshakeToken,r)).handshake,n="";if(t.forEach(t=>{e.append("Set-Cookie",t),n$(t).startsWith(ry.Cookies.Session)&&(n=nH(t))}),"development"===r.instanceType){let t=new URL(r.clerkUrl);t.searchParams.delete(ry.QueryParameters.Handshake),t.searchParams.delete(ry.QueryParameters.HandshakeHelp),e.append(ry.Headers.Location,t.toString()),e.set(ry.Headers.CacheControl,"no-store")}if(""===n)return nE(r,nS.SessionTokenMissing,"",e);let{data:i,errors:[s]=[]}=await nD(n,r);if(i)return nk(r,i,e,n);if("development"===r.instanceType&&(s?.reason===tf.TokenExpired||s?.reason===tf.TokenNotActiveYet||s?.reason===tf.TokenIatInTheFuture)){s.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${s.getFullMessage()}`);let{data:t,errors:[i]=[]}=await nD(n,{...r,clockSkewInMs:864e5});if(t)return nk(r,t,e,n);throw Error(i?.message||"Clerk: Handshake retry failed.")}throw Error(s?.message||"Clerk: Handshake failed.")}async function o(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:nW.MissingApiClient}}};let{sessionToken:n,refreshTokenInCookie:i}=r;if(!n)return{data:null,error:{message:"Session token must be provided.",cause:{reason:nW.MissingSessionToken}}};if(!i)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:nW.MissingRefreshToken}}};let{data:s,errors:a}=tM(n);if(!s||a)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:nW.ExpiredSessionTokenDecodeFailed,errors:a}}};if(!s?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:nW.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(s.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:n||"",refresh_token:i||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:nW.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:nW.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function l(e){let{data:t,error:r}=await o(e);if(!t||0===t.length)return{data:null,error:r};let n=new Headers,i="";t.forEach(e=>{n.append("Set-Cookie",e),n$(e).startsWith(ry.Cookies.Session)&&(i=nH(e))});let{data:s,errors:a}=await nD(i,e);return a?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:nW.InvalidSessionToken,errors:a}}}:{data:{jwtPayload:s,sessionToken:i,headers:n},error:null}}function u(e,n,i,a){if(function(e){let{accept:t,secFetchDest:r}=e;return!!("document"===r||"iframe"===r||!r&&t?.startsWith("text/html"))}(e)){let o=a??function({handshakeReason:e}){let n=function(e){let t=new URL(e);return t.searchParams.delete(ry.QueryParameters.DevBrowser),t.searchParams.delete(ry.QueryParameters.LegacyDevBrowser),t}(r.clerkUrl),i=r.frontendApi.replace(/http(s)?:\/\//,""),a=new URL(`https://${i}/v1/client/handshake`);a.searchParams.append("redirect_url",n?.href||""),a.searchParams.append(ry.QueryParameters.SuffixedCookies,r.usesSuffixedCookies().toString()),a.searchParams.append(ry.QueryParameters.HandshakeReason,e),"development"===r.instanceType&&r.devBrowserToken&&a.searchParams.append(ry.QueryParameters.DevBrowser,r.devBrowserToken);let o=nV(r.clerkUrl,t.organizationSyncOptions,s);return o&&(function(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t})(o).forEach((e,t)=>{a.searchParams.append(t,e)}),new Headers({[ry.Headers.Location]:a.href})}({handshakeReason:n});return(o.get(ry.Headers.Location)&&o.set(ry.Headers.CacheControl,"no-store"),function(e){if(3===r.handshakeRedirectLoopCounter)return!0;let t=r.handshakeRedirectLoopCounter+1,n=ry.Cookies.RedirectCount;return e.append("Set-Cookie",`${n}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}(o))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),nE(e,n,i)):function(e,t,r="",n){return nx({status:nw.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>null,token:null})}(e,n,i,o)}return nE(e,n,i)}async function c(){let{sessionTokenInHeader:e}=r;try{let{data:t,errors:n}=await nD(e,r);if(n)throw n[0];return nk(r,t,void 0,e)}catch(e){return h(e,"header")}}async function d(){let e=r.clientUat,n=!!r.sessionTokenInCookie,i=!!r.devBrowserToken;if(r.handshakeToken)try{return await a()}catch(e){if(e instanceof tm&&"development"===r.instanceType){if(e.reason===tf.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(ry.QueryParameters.DevBrowser))return u(r,nS.DevBrowserSync,"");let o=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&o)return u(r,nS.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&o&&!r.clerkUrl.searchParams.has(ry.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(ry.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[ry.Headers.Location]:e.toString()});return u(r,nS.SatelliteCookieNeedsSyncing,"",t)}let l=new URL(r.clerkUrl).searchParams.get(ry.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&l){let e=new URL(l);r.devBrowserToken&&e.searchParams.append(ry.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(ry.QueryParameters.ClerkSynced,"true");let t=new Headers({[ry.Headers.Location]:e.toString()});return u(r,nS.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!i)return u(r,nS.DevBrowserMissing,"");if(!e&&!n)return nE(r,nS.SessionTokenAndUATMissing,"");if(!e&&n)return u(r,nS.SessionTokenWithoutClientUAT,"");if(e&&!n)return u(r,nS.ClientUATWithoutSessionToken,"");let{data:c,errors:d}=tM(r.sessionTokenInCookie);if(d)return h(d[0],"cookie");if(c.payload.iat<r.clientUat)return u(r,nS.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:n}=await nD(r.sessionTokenInCookie,r);if(n)throw n[0];let i=nk(r,e,void 0,r.sessionTokenInCookie),a=function(e,r){let n=nV(e.clerkUrl,t.organizationSyncOptions,s);if(!n)return null;let i=!1;if("organization"===n.type&&(n.organizationSlug&&n.organizationSlug!==r.orgSlug&&(i=!0),n.organizationId&&n.organizationId!==r.orgId&&(i=!0)),"personalAccount"===n.type&&r.orgId&&(i=!0),!i)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let a=u(e,nS.ActiveOrganizationMismatch,"");return"handshake"!==a.status?null:a}(r,i.toAuth());if(a)return a;return i}catch(e){return h(e,"cookie")}}async function h(t,n){let i;if(!(t instanceof tm))return nE(r,nS.UnexpectedError);if(t.reason===tf.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await l(r);if(e)return nk(r,e.jwtPayload,e.headers,e.sessionToken);i=t?.cause?.reason?t.cause.reason:nW.UnexpectedSDKError}else i="GET"!==e.method?nW.NonEligibleNonGet:r.refreshTokenInCookie?null:nW.NonEligibleNoCookie;return(t.tokenCarrier=n,[tf.TokenExpired,tf.TokenNotActiveYet,tf.TokenIatInTheFuture].includes(t.reason))?u(r,nX({tokenError:t.reason,refreshError:i}),t.getFullMessage()):nE(r,t.reason,t.getFullMessage())}return r.sessionTokenInHeader?c():d()}var nF=e=>{let{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:s,isSatellite:a,domain:o}=e;return{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:s,isSatellite:a,domain:o}};function nV(e,t,r){if(!t)return null;if(r.OrganizationMatcher){let n;try{n=r.OrganizationMatcher(e.pathname)}catch(e){return console.error(`Clerk: Failed to apply organization pattern "${t.organizationPatterns}" to a path`,e),null}if(n&&"params"in n){let e=n.params;if("id"in e&&"string"==typeof e.id)return{type:"organization",organizationId:e.id};if("slug"in e&&"string"==typeof e.slug)return{type:"organization",organizationSlug:e.slug};console.warn("Clerk: Detected an organization pattern match, but no organization ID or slug was found in the URL. Does the pattern include `:id` or `:slug`?")}}if(r.PersonalAccountMatcher){let n;try{n=r.PersonalAccountMatcher(e.pathname)}catch(e){return console.error(`Failed to apply personal account pattern "${t.personalAccountPatterns}" to a path`,e),null}if(n)return{type:"personalAccount"}}return null}var nX=({tokenError:e,refreshError:t})=>{switch(e){case tf.TokenExpired:return`${nS.SessionTokenExpired}-refresh-${t}`;case tf.TokenNotActiveYet:return nS.SessionTokenNBF;case tf.TokenIatInTheFuture:return nS.SessionTokenIatInTheFuture;default:return nS.UnexpectedError}};function nG(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}var nY={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""},nQ=(e,t,r,n)=>{if(""===e)return nZ(t.toString(),r?.toString());let i=new URL(e),s=r?new URL(r,i):void 0,a=new URL(t,i);return s&&a.searchParams.set("redirect_url",s.toString()),n&&i.hostname!==a.hostname&&a.searchParams.set(ry.QueryParameters.DevBrowser,n),a.toString()},nZ=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let n=new URL(t);r=new URL(e,n.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},n0=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:n,signUpUrl:i,baseUrl:s}=e,a=tY(t),o=a?.frontendApi,l=a?.instanceType==="development",u=function(e){if(!e)return"";let t=e.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return`https://${t}`}(o);return{redirectToSignUp:({returnBackUrl:t}={})=>{i||u||rH.throwMissingPublishableKeyError();let n=`${u}/sign-up`;return r(nQ(s,i||n,t,l?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{n||u||rH.throwMissingPublishableKeyError();let i=`${u}/sign-in`;return r(nQ(s,n||i,t,l?e.devBrowserToken:null))}}},n1=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function n2(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}n1(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),n1(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""});var n4=class{constructor(){tc(this,iD),tc(this,iL,"clerk_telemetry_throttler"),tc(this,iM,864e5)}isEventThrottled(e){if(!tu(this,iD,i$))return!1;let t=Date.now(),r=th(this,iD,iq).call(this,e),n=tu(this,iD,iz)?.[r];if(!n){let e={...tu(this,iD,iz),[r]:t};localStorage.setItem(tu(this,iL),JSON.stringify(e))}if(n&&t-n>tu(this,iM)){let e=tu(this,iD,iz);delete e[r],localStorage.setItem(tu(this,iL),JSON.stringify(e))}return!!n}};iL=new WeakMap,iM=new WeakMap,iD=new WeakSet,iq=function(e){let{sk:t,pk:r,payload:n,...i}=e,s={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>s[e]))},iz=function(){let e=localStorage.getItem(tu(this,iL));return e?JSON.parse(e):{}},i$=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem(tu(this,iL)),!1}};var n3={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},n5=class{constructor(e){tc(this,iF),tc(this,iH),tc(this,iB),tc(this,iK,{}),tc(this,iW,[]),tc(this,iJ),td(this,iH,{maxBufferSize:e.maxBufferSize??n3.maxBufferSize,samplingRate:e.samplingRate??n3.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:n3.endpoint}),e.clerkVersion||"undefined"!=typeof window?tu(this,iK).clerkVersion=e.clerkVersion??"":tu(this,iK).clerkVersion="",tu(this,iK).sdk=e.sdk,tu(this,iK).sdkVersion=e.sdkVersion,tu(this,iK).publishableKey=e.publishableKey??"";let t=tY(e.publishableKey);t&&(tu(this,iK).instanceType=t.instanceType),e.secretKey&&(tu(this,iK).secretKey=e.secretKey.substring(0,16)),td(this,iB,new n4)}get isEnabled(){return!("development"!==tu(this,iK).instanceType||tu(this,iH).disabled||"undefined"!=typeof process&&n2(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return tu(this,iH).debug||"undefined"!=typeof process&&n2(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=th(this,iF,i0).call(this,e.event,e.payload);th(this,iF,iQ).call(this,t.event,t),th(this,iF,iV).call(this,t,e.eventSamplingRate)&&(tu(this,iW).push(t),th(this,iF,iG).call(this))}};iH=new WeakMap,iB=new WeakMap,iK=new WeakMap,iW=new WeakMap,iJ=new WeakMap,iF=new WeakSet,iV=function(e,t){return this.isEnabled&&!this.isDebug&&th(this,iF,iX).call(this,e,t)},iX=function(e,t){let r=Math.random();return!!(r<=tu(this,iH).samplingRate&&(void 0===t||r<=t))&&!tu(this,iB).isEventThrottled(e)},iG=function(){if("undefined"==typeof window)return void th(this,iF,iY).call(this);if(tu(this,iW).length>=tu(this,iH).maxBufferSize){tu(this,iJ)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)(tu(this,iJ)),th(this,iF,iY).call(this);return}tu(this,iJ)||("requestIdleCallback"in window?td(this,iJ,requestIdleCallback(()=>{th(this,iF,iY).call(this)})):td(this,iJ,setTimeout(()=>{th(this,iF,iY).call(this)},0)))},iY=function(){fetch(new URL("/v1/event",tu(this,iH).endpoint),{method:"POST",body:JSON.stringify({events:tu(this,iW)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{td(this,iW,[])}).catch(()=>void 0)},iQ=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},iZ=function(){let e={name:tu(this,iK).sdk,version:tu(this,iK).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e},i0=function(e,t){let r=th(this,iF,iZ).call(this);return{event:e,cv:tu(this,iK).clerkVersion??"",it:tu(this,iK).instanceType??"",sdk:r.name,sdkv:r.version,...tu(this,iK).publishableKey?{pk:tu(this,iK).publishableKey}:{},...tu(this,iK).secretKey?{sk:tu(this,iK).secretKey}:{},payload:t}};r(689),r(483),r(484).s;var n6=r(627);let n8=""+n6.s8+";404";n6.s8,n6.s8,r(826).X,r(644),"undefined"==typeof URLPattern||URLPattern,r(601),r(606),r(797),r(400),new WeakMap;let n9={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},n7=e=>e.headers.get(n9.Headers.NextRedirect),ie=(e,t,r)=>(e.headers.set(t,r),e);var it="__clerk_db_jwt",ir=e=>{let t=new URL(e);return t.searchParams.delete(it),t},ii=e=>{let t=new URL(e);return t.searchParams.delete("__dev_session"),t.hash=decodeURI(t.hash).replace(/__clerk_db_jwt\[(.*)\]/,""),t.href.endsWith("#")&&(t.hash=""),t};let is=(e,t,r)=>{let n=t.headers.get("location");if("true"===t.headers.get(ry.Headers.ClerkRedirectTo)&&n&&tZ(r.secretKey)&&e.clerkUrl.isCrossOrigin(n)){let r=e.cookies.get(it)||"",i=function(e,t){let r=new URL(e),n=r.searchParams.get(it);r.searchParams.delete(it);let i=n||t;return i&&r.searchParams.set(it,i),r}(new URL(n),r);return W.redirect(i.href,t)}return t},ia={rE:"15.3.2"},io=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},il=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?io(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,io(t)])),null,2)).join(", "),iu=(e,t)=>()=>{let r=[],n=!1;return{enable:()=>{n=!0},debug:(...e)=>{n&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(n){var i,s;for(let n of(console.log((i=e,`[clerk debug start: ${i}]`)),r)){let e=t(n);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,n=new TextDecoder("utf-8"),i=r.encode(e).slice(0,4096);return n.decode(i).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((s=e,`[clerk debug end: ${s}] (@clerk/nextjs=6.12.12,next=${ia.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},ic=(e,t)=>(...r)=>{let n=("string"==typeof e?iu(e,il):e)(),i=t(n);try{let e=i(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(n.commit(),e)).catch(e=>{throw n.commit(),e});return n.commit(),e}catch(e){throw n.commit(),e}};function id(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let ih=process.env.CLERK_API_VERSION||"v1",ip=process.env.CLERK_SECRET_KEY||"",ig="pk_test_d2VsY29tZWQtZmVsaW5lLTgyLmNsZXJrLmFjY291bnRzLmRldiQ",im=process.env.CLERK_ENCRYPTION_KEY||"",iy=process.env.CLERK_API_URL||(e=>{let t=tY(e)?.frontendApi;return t?.startsWith("clerk.")&&tW.some(e=>t?.endsWith(e))?tX:tF.some(e=>t?.endsWith(e))?"https://api.lclclerk.com":tV.some(e=>t?.endsWith(e))?"https://api.clerkstage.dev":tX})(ig),iv=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",i_=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",ib=n2(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,iw="/auth/sign-in",iS=n2(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),ik=n2(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),iE=n2(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1,ix=!(ia.rE.startsWith("13.")||ia.rE.startsWith("14.0"))&&!1,iT=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),n=r.includes("dynamic server usage"),i=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||n||i};async function iR(){try{let{headers:e}=await Promise.resolve().then(r.bind(r,505)),t=await e();return new z("https://placeholder.com",{headers:t})}catch(e){if(e&&iT(e))throw e;throw Error(`Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}let iC={secretKey:ip,publishableKey:ig,apiUrl:iy,apiVersion:ih,userAgent:"@clerk/nextjs@6.12.12",proxyUrl:i_,domain:iv,isSatellite:ib,sdkMetadata:{name:"@clerk/nextjs",version:"6.12.12",environment:"production"},telemetry:{disabled:iS,debug:ik}},iO=e=>(function(e){let t={...e},r=nv(t),n=function(e){let t=nG(nY,e.options),r=e.apiClient;return{authenticateRequest:(e,n={})=>{let{apiUrl:i,apiVersion:s}=t,a=nG(t,n);return nJ(e,{...n,...a,apiUrl:i,apiVersion:s,apiClient:r})},debugRequestState:nF}}({options:t,apiClient:r}),i=new n5({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...n,telemetry:i}})({...iC,...e});var iI=r(521);let iP=new Map,iA=new iI.AsyncLocalStorage;var iN=new Set,iU={warnOnce:e=>{iN.has(e)||(iN.add(e),console.warn(e))}};function ij(e){return/^http(s)?:\/\//.test(e||"")}var iL,iM,iD,iq,iz,i$,iH,iB,iK,iW,iJ,iF,iV,iX,iG,iY,iQ,iZ,i0,i1,i2,i4,i3,i5,i6,i8,i9=Object.defineProperty,i7=(e,t,r)=>t in e?i9(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,se=(null==(i1="undefined"!=typeof globalThis?globalThis:void 0)?void 0:i1.crypto)||(null==(i2=void 0!==r.g?r.g:void 0)?void 0:i2.crypto)||(null==(i4="undefined"!=typeof window?window:void 0)?void 0:i4.crypto)||(null==(i3="undefined"!=typeof self?self:void 0)?void 0:i3.crypto)||(null==(i6=null==(i5="undefined"!=typeof frames?frames:void 0)?void 0:i5[0])?void 0:i6.crypto);i8=se?e=>{let t=[];for(let r=0;r<e;r+=4)t.push(se.getRandomValues(new Uint32Array(1))[0]);return new sr(t,e)}:e=>{let t=[],r=e=>{let t=e,r=0x3ade68b1;return()=>{let e=((r=36969*(65535&r)+(r>>16)|0)<<16)+(t=18e3*(65535&t)+(t>>16)|0)|0;return e/=0x100000000,(e+=.5)*(Math.random()>.5?1:-1)}};for(let n=0,i;n<e;n+=4){let e=r(0x100000000*(i||Math.random()));i=0x3ade67b7*e(),t.push(0x100000000*e()|0)}return new sr(t,e)};var st=class{static create(...e){return new this(...e)}mixIn(e){return Object.assign(this,e)}clone(){let e=new this.constructor;return Object.assign(e,this),e}},sr=class extends st{constructor(e=[],t=4*e.length){super();let r=e;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){let e=r.byteLength,t=[];for(let n=0;n<e;n+=1)t[n>>>2]|=r[n]<<24-n%4*8;this.words=t,this.sigBytes=e}else this.words=e,this.sigBytes=t}toString(e=sn){return e.stringify(this)}concat(e){let t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(let e=0;e<i;e+=1){let i=r[e>>>2]>>>24-e%4*8&255;t[n+e>>>2]|=i<<24-(n+e)%4*8}else for(let e=0;e<i;e+=4)t[n+e>>>2]=r[e>>>2];return this.sigBytes+=i,this}clamp(){let{words:e,sigBytes:t}=this;e[t>>>2]&=0xffffffff<<32-t%4*8,e.length=Math.ceil(t/4)}clone(){let e=super.clone.call(this);return e.words=this.words.slice(0),e}};((e,t,r)=>i7(e,"symbol"!=typeof t?t+"":t,r))(sr,"random",i8);var sn={stringify(e){let{words:t,sigBytes:r}=e,n=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;n.push((r>>>4).toString(16)),n.push((15&r).toString(16))}return n.join("")},parse(e){let t=e.length,r=[];for(let n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new sr(r,t/2)}},si={stringify(e){let{words:t,sigBytes:r}=e,n=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;n.push(String.fromCharCode(r))}return n.join("")},parse(e){let t=e.length,r=[];for(let n=0;n<t;n+=1)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new sr(r,t)}},ss={stringify(e){try{return decodeURIComponent(escape(si.stringify(e)))}catch{throw Error("Malformed UTF-8 data")}},parse:e=>si.parse(unescape(encodeURIComponent(e)))},sa=class extends st{constructor(){super(),this._minBufferSize=0}reset(){this._data=new sr,this._nDataBytes=0}_append(e){let t=e;"string"==typeof t&&(t=ss.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(e){let t,{_data:r,blockSize:n}=this,i=r.words,s=r.sigBytes,a=s/(4*n),o=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*n,l=Math.min(4*o,s);if(o){for(let e=0;e<o;e+=n)this._doProcessBlock(i,e);t=i.splice(0,o),r.sigBytes-=l}return new sr(t,l)}clone(){let e=super.clone.call(this);return e._data=this._data.clone(),e}},so=class extends sa{constructor(e){super(),this.blockSize=16,this.cfg=Object.assign(new st,e),this.reset()}static _createHelper(e){return(t,r)=>new e(r).finalize(t)}static _createHmacHelper(e){return(t,r)=>new sl(e,r).finalize(t)}reset(){super.reset.call(this),this._doReset()}update(e){return this._append(e),this._process(),this}finalize(e){return e&&this._append(e),this._doFinalize()}},sl=class extends st{constructor(e,t){super();let r=new e;this._hasher=r;let n=t;"string"==typeof n&&(n=ss.parse(n));let i=r.blockSize,s=4*i;n.sigBytes>s&&(n=r.finalize(t)),n.clamp();let a=n.clone();this._oKey=a;let o=n.clone();this._iKey=o;let l=a.words,u=o.words;for(let e=0;e<i;e+=1)l[e]^=0x5c5c5c5c,u[e]^=0x36363636;a.sigBytes=s,o.sigBytes=s,this.reset()}reset(){let e=this._hasher;e.reset(),e.update(this._iKey)}update(e){return this._hasher.update(e),this}finalize(e){let t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}},su=(e,t,r)=>{let n=[],i=0;for(let s=0;s<t;s+=1)if(s%4){let t=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;n[i>>>2]|=t<<24-i%4*8,i+=1}return sr.create(n,i)},sc={stringify(e){let{words:t,sigBytes:r}=e,n=this._map;e.clamp();let i=[];for(let e=0;e<r;e+=3){let s=(t[e>>>2]>>>24-e%4*8&255)<<16|(t[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|t[e+2>>>2]>>>24-(e+2)%4*8&255;for(let t=0;t<4&&e+.75*t<r;t+=1)i.push(n.charAt(s>>>6*(3-t)&63))}let s=n.charAt(64);if(s)for(;i.length%4;)i.push(s);return i.join("")},parse(e){let t=e.length,r=this._map,n=this._reverseMap;if(!n){this._reverseMap=[],n=this._reverseMap;for(let e=0;e<r.length;e+=1)n[r.charCodeAt(e)]=e}let i=r.charAt(64);if(i){let r=e.indexOf(i);-1!==r&&(t=r)}return su(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},sd=[];for(let e=0;e<64;e+=1)sd[e]=0x100000000*Math.abs(Math.sin(e+1))|0;var sh=(e,t,r,n,i,s,a)=>{let o=e+(t&r|~t&n)+i+a;return(o<<s|o>>>32-s)+t},sp=(e,t,r,n,i,s,a)=>{let o=e+(t&n|r&~n)+i+a;return(o<<s|o>>>32-s)+t},sf=(e,t,r,n,i,s,a)=>{let o=e+(t^r^n)+i+a;return(o<<s|o>>>32-s)+t},sg=(e,t,r,n,i,s,a)=>{let o=e+(r^(t|~n))+i+a;return(o<<s|o>>>32-s)+t},sm=class extends so{_doReset(){this._hash=new sr([0x67452301,0xefcdab89,0x98badcfe,0x10325476])}_doProcessBlock(e,t){for(let r=0;r<16;r+=1){let n=t+r,i=e[n];e[n]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00}let r=this._hash.words,n=e[t+0],i=e[t+1],s=e[t+2],a=e[t+3],o=e[t+4],l=e[t+5],u=e[t+6],c=e[t+7],d=e[t+8],h=e[t+9],p=e[t+10],f=e[t+11],g=e[t+12],m=e[t+13],y=e[t+14],v=e[t+15],_=r[0],b=r[1],w=r[2],S=r[3];_=sh(_,b,w,S,n,7,sd[0]),S=sh(S,_,b,w,i,12,sd[1]),w=sh(w,S,_,b,s,17,sd[2]),b=sh(b,w,S,_,a,22,sd[3]),_=sh(_,b,w,S,o,7,sd[4]),S=sh(S,_,b,w,l,12,sd[5]),w=sh(w,S,_,b,u,17,sd[6]),b=sh(b,w,S,_,c,22,sd[7]),_=sh(_,b,w,S,d,7,sd[8]),S=sh(S,_,b,w,h,12,sd[9]),w=sh(w,S,_,b,p,17,sd[10]),b=sh(b,w,S,_,f,22,sd[11]),_=sh(_,b,w,S,g,7,sd[12]),S=sh(S,_,b,w,m,12,sd[13]),w=sh(w,S,_,b,y,17,sd[14]),b=sh(b,w,S,_,v,22,sd[15]),_=sp(_,b,w,S,i,5,sd[16]),S=sp(S,_,b,w,u,9,sd[17]),w=sp(w,S,_,b,f,14,sd[18]),b=sp(b,w,S,_,n,20,sd[19]),_=sp(_,b,w,S,l,5,sd[20]),S=sp(S,_,b,w,p,9,sd[21]),w=sp(w,S,_,b,v,14,sd[22]),b=sp(b,w,S,_,o,20,sd[23]),_=sp(_,b,w,S,h,5,sd[24]),S=sp(S,_,b,w,y,9,sd[25]),w=sp(w,S,_,b,a,14,sd[26]),b=sp(b,w,S,_,d,20,sd[27]),_=sp(_,b,w,S,m,5,sd[28]),S=sp(S,_,b,w,s,9,sd[29]),w=sp(w,S,_,b,c,14,sd[30]),b=sp(b,w,S,_,g,20,sd[31]),_=sf(_,b,w,S,l,4,sd[32]),S=sf(S,_,b,w,d,11,sd[33]),w=sf(w,S,_,b,f,16,sd[34]),b=sf(b,w,S,_,y,23,sd[35]),_=sf(_,b,w,S,i,4,sd[36]),S=sf(S,_,b,w,o,11,sd[37]),w=sf(w,S,_,b,c,16,sd[38]),b=sf(b,w,S,_,p,23,sd[39]),_=sf(_,b,w,S,m,4,sd[40]),S=sf(S,_,b,w,n,11,sd[41]),w=sf(w,S,_,b,a,16,sd[42]),b=sf(b,w,S,_,u,23,sd[43]),_=sf(_,b,w,S,h,4,sd[44]),S=sf(S,_,b,w,g,11,sd[45]),w=sf(w,S,_,b,v,16,sd[46]),b=sf(b,w,S,_,s,23,sd[47]),_=sg(_,b,w,S,n,6,sd[48]),S=sg(S,_,b,w,c,10,sd[49]),w=sg(w,S,_,b,y,15,sd[50]),b=sg(b,w,S,_,l,21,sd[51]),_=sg(_,b,w,S,g,6,sd[52]),S=sg(S,_,b,w,a,10,sd[53]),w=sg(w,S,_,b,p,15,sd[54]),b=sg(b,w,S,_,i,21,sd[55]),_=sg(_,b,w,S,d,6,sd[56]),S=sg(S,_,b,w,v,10,sd[57]),w=sg(w,S,_,b,u,15,sd[58]),b=sg(b,w,S,_,m,21,sd[59]),_=sg(_,b,w,S,o,6,sd[60]),S=sg(S,_,b,w,f,10,sd[61]),w=sg(w,S,_,b,s,15,sd[62]),b=sg(b,w,S,_,h,21,sd[63]),r[0]=r[0]+_|0,r[1]=r[1]+b|0,r[2]=r[2]+w|0,r[3]=r[3]+S|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32;let i=Math.floor(r/0x100000000);t[(n+64>>>9<<4)+15]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,t[(n+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();let s=this._hash,a=s.words;for(let e=0;e<4;e+=1){let t=a[e];a[e]=(t<<8|t>>>24)&0xff00ff|(t<<24|t>>>8)&0xff00ff00}return s}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}};so._createHelper(sm),so._createHmacHelper(sm);var sy=class extends st{constructor(e){super(),this.cfg=Object.assign(new st,{keySize:4,hasher:sm,iterations:1},e)}compute(e,t){let r,{cfg:n}=this,i=n.hasher.create(),s=sr.create(),a=s.words,{keySize:o,iterations:l}=n;for(;a.length<o;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(let e=1;e<l;e+=1)r=i.finalize(r),i.reset();s.concat(r)}return s.sigBytes=4*o,s}},sv=class extends sa{constructor(e,t,r){super(),this.cfg=Object.assign(new st,r),this._xformMode=e,this._key=t,this.reset()}static createEncryptor(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}static createDecryptor(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}static _createHelper(e){let t=e=>"string"==typeof e?sT:sx;return{encrypt:(r,n,i)=>t(n).encrypt(e,r,n,i),decrypt:(r,n,i)=>t(n).decrypt(e,r,n,i)}}reset(){super.reset.call(this),this._doReset()}process(e){return this._append(e),this._process()}finalize(e){return e&&this._append(e),this._doFinalize()}};sv._ENC_XFORM_MODE=1,sv._DEC_XFORM_MODE=2,sv.keySize=4,sv.ivSize=4;var s_=class extends st{constructor(e,t){super(),this._cipher=e,this._iv=t}static createEncryptor(e,t){return this.Encryptor.create(e,t)}static createDecryptor(e,t){return this.Decryptor.create(e,t)}};function sb(e,t,r){let n,i=this._iv;i?(n=i,this._iv=void 0):n=this._prevBlock;for(let i=0;i<r;i+=1)e[t+i]^=n[i]}var sw=class extends s_{};sw.Encryptor=class extends sw{processBlock(e,t){let r=this._cipher,{blockSize:n}=r;sb.call(this,e,t,n),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}},sw.Decryptor=class extends sw{processBlock(e,t){let r=this._cipher,{blockSize:n}=r,i=e.slice(t,t+n);r.decryptBlock(e,t),sb.call(this,e,t,n),this._prevBlock=i}};var sS={pad(e,t){let r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[];for(let e=0;e<n;e+=4)s.push(i);let a=sr.create(s,n);e.concat(a)},unpad(e){let t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},sk=class extends sv{constructor(e,t,r){super(e,t,Object.assign({mode:sw,padding:sS},r)),this.blockSize=4}reset(){let e;super.reset.call(this);let{cfg:t}=this,{iv:r,mode:n}=t;this._xformMode===this.constructor._ENC_XFORM_MODE?e=n.createEncryptor:(e=n.createDecryptor,this._minBufferSize=1),this._mode=e.call(n,this,r&&r.words),this._mode.__creator=e}_doProcessBlock(e,t){this._mode.processBlock(e,t)}_doFinalize(){let e,{padding:t}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}},sE=class extends st{constructor(e){super(),this.mixIn(e)}toString(e){return(e||this.formatter).stringify(this)}},sx=class extends st{static encrypt(e,t,r,n){let i=Object.assign(new st,this.cfg,n),s=e.createEncryptor(r,i),a=s.finalize(t),o=s.cfg;return sE.create({ciphertext:a,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:s.blockSize,formatter:i.format})}static decrypt(e,t,r,n){let i=t,s=Object.assign(new st,this.cfg,n);return i=this._parse(i,s.format),e.createDecryptor(r,s).finalize(i.ciphertext)}static _parse(e,t){return"string"==typeof e?t.parse(e,this):e}};sx.cfg=Object.assign(new st,{format:{stringify(e){let t,{ciphertext:r,salt:n}=e;return(n?sr.create([0x53616c74,0x65645f5f]).concat(n).concat(r):r).toString(sc)},parse(e){let t,r=sc.parse(e),n=r.words;return 0x53616c74===n[0]&&0x65645f5f===n[1]&&(t=sr.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),sE.create({ciphertext:r,salt:t})}}});var sT=class extends sx{static encrypt(e,t,r,n){let i=Object.assign(new st,this.cfg,n),s=i.kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=s.iv;let a=sx.encrypt.call(this,e,t,s.key,i);return a.mixIn(s),a}static decrypt(e,t,r,n){let i=t,s=Object.assign(new st,this.cfg,n);i=this._parse(i,s.format);let a=s.kdf.execute(r,e.keySize,e.ivSize,i.salt,s.hasher);return s.iv=a.iv,sx.decrypt.call(this,e,i,a.key,s)}};sT.cfg=Object.assign(sx.cfg,{kdf:{execute(e,t,r,n,i){let s,a=n;a||(a=sr.random(8)),s=i?sy.create({keySize:t+r,hasher:i}).compute(e,a):sy.create({keySize:t+r}).compute(e,a);let o=sr.create(s.words.slice(t),4*r);return s.sigBytes=4*t,sE.create({key:s,iv:o,salt:a})}}});var sR=[],sC=[],sO=[],sI=[],sP=[],sA=[],sN=[],sU=[],sj=[],sL=[],sM=[];for(let e=0;e<256;e+=1)e<128?sM[e]=e<<1:sM[e]=e<<1^283;var sD=0,sq=0;for(let e=0;e<256;e+=1){let e=sq^sq<<1^sq<<2^sq<<3^sq<<4;e=e>>>8^255&e^99,sR[sD]=e,sC[e]=sD;let t=sM[sD],r=sM[t],n=sM[r],i=257*sM[e]^0x1010100*e;sO[sD]=i<<24|i>>>8,sI[sD]=i<<16|i>>>16,sP[sD]=i<<8|i>>>24,sA[sD]=i,i=0x1010101*n^65537*r^257*t^0x1010100*sD,sN[e]=i<<24|i>>>8,sU[e]=i<<16|i>>>16,sj[e]=i<<8|i>>>24,sL[e]=i,sD?(sD=t^sM[sM[sM[n^t]]],sq^=sM[sM[sq]]):sD=sq=1}var sz=[0,1,2,4,8,16,32,64,128,27,54],s$=class extends sk{_doReset(){let e;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let t=this._keyPriorReset,r=t.words,n=t.sigBytes/4;this._nRounds=n+6;let i=(this._nRounds+1)*4;this._keySchedule=[];let s=this._keySchedule;for(let t=0;t<i;t+=1)t<n?s[t]=r[t]:(e=s[t-1],t%n?n>6&&t%n==4&&(e=sR[e>>>24]<<24|sR[e>>>16&255]<<16|sR[e>>>8&255]<<8|sR[255&e]):e=(sR[(e=e<<8|e>>>24)>>>24]<<24|sR[e>>>16&255]<<16|sR[e>>>8&255]<<8|sR[255&e])^sz[t/n|0]<<24,s[t]=s[t-n]^e);this._invKeySchedule=[];let a=this._invKeySchedule;for(let t=0;t<i;t+=1){let r=i-t;e=t%4?s[r]:s[r-4],t<4||r<=4?a[t]=e:a[t]=sN[sR[e>>>24]]^sU[sR[e>>>16&255]]^sj[sR[e>>>8&255]]^sL[sR[255&e]]}}encryptBlock(e,t){this._doCryptBlock(e,t,this._keySchedule,sO,sI,sP,sA,sR)}decryptBlock(e,t){let r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,sN,sU,sj,sL,sC),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r}_doCryptBlock(e,t,r,n,i,s,a,o){let l=this._nRounds,u=e[t]^r[0],c=e[t+1]^r[1],d=e[t+2]^r[2],h=e[t+3]^r[3],p=4;for(let e=1;e<l;e+=1){let e=n[u>>>24]^i[c>>>16&255]^s[d>>>8&255]^a[255&h]^r[p];p+=1;let t=n[c>>>24]^i[d>>>16&255]^s[h>>>8&255]^a[255&u]^r[p];p+=1;let o=n[d>>>24]^i[h>>>16&255]^s[u>>>8&255]^a[255&c]^r[p];p+=1;let l=n[h>>>24]^i[u>>>16&255]^s[c>>>8&255]^a[255&d]^r[p];p+=1,u=e,c=t,d=o,h=l}let f=(o[u>>>24]<<24|o[c>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[p];p+=1;let g=(o[c>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&u])^r[p];p+=1;let m=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[u>>>8&255]<<8|o[255&c])^r[p];p+=1;let y=(o[h>>>24]<<24|o[u>>>16&255]<<16|o[c>>>8&255]<<8|o[255&d])^r[p];p+=1,e[t]=f,e[t+1]=g,e[t+2]=m,e[t+3]=y}};s$.keySize=8;var sH=sk._createHelper(s$),sB=[],sK=class extends so{_doReset(){this._hash=new sr([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])}_doProcessBlock(e,t){let r=this._hash.words,n=r[0],i=r[1],s=r[2],a=r[3],o=r[4];for(let r=0;r<80;r+=1){if(r<16)sB[r]=0|e[t+r];else{let e=sB[r-3]^sB[r-8]^sB[r-14]^sB[r-16];sB[r]=e<<1|e>>>31}let l=(n<<5|n>>>27)+o+sB[r];r<20?l+=(i&s|~i&a)+0x5a827999:r<40?l+=(i^s^a)+0x6ed9eba1:r<60?l+=(i&s|i&a|s&a)-0x70e44324:l+=(i^s^a)-0x359d3e2a,o=a,a=s,s=i<<30|i>>>2,i=n,n=l}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[(n+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(n+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}},sW=(so._createHelper(sK),so._createHmacHelper(sK));let sJ=`
Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.

1) With middleware
   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'
   `,sF=`
Invalid signInUrl. A satellite application requires a signInUrl for development instances.
Check if signInUrl is missing from your configuration or if it is not an absolute URL

1) With middleware
   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`,sV=`Clerk: Unable to decrypt request data.

Refresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.

For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`,sX=t7({packageName:"@clerk/nextjs"}),sG="x-middleware-override-headers",sY="x-middleware-request",sQ=(e,t,r)=>{e.headers.get(sG)||(e.headers.set(sG,[...t.headers.keys()]),t.headers.forEach((t,r)=>{e.headers.set(`${sY}-${r}`,t)})),Object.entries(r).forEach(([t,r])=>{e.headers.set(sG,`${e.headers.get(sG)},${t}`),e.headers.set(`${sY}-${t}`,r)})},sZ=(e,t)=>{let r,n=id(null==t?void 0:t.proxyUrl,e.clerkUrl,i_);r=n&&!ij(n)?new URL(n,e.clerkUrl).toString():n;let i=id(t.isSatellite,new URL(e.url),ib),s=id(t.domain,new URL(e.url),iv),a=(null==t?void 0:t.signInUrl)||iw;if(i&&!r&&!s)throw Error(sJ);if(i&&!ij(a)&&tZ(t.secretKey||ip))throw Error(sF);return{proxyUrl:r,isSatellite:i,domain:s,signInUrl:a}},s0=e=>W.redirect(e,{headers:{[ry.Headers.ClerkRedirectTo]:"true"}}),s1="clerk_keyless_dummy_key";function s2(){if(t4())throw Error("Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)");throw Error(sV)}function s4(e,t){return JSON.parse(sH.decrypt(e,t).toString(ss))}let s3=async()=>{var e,t;let r;try{let e=await iR(),t=function(e,t){var r,n;return function(e){try{let{headers:t,nextUrl:r,cookies:n}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==n?void 0:n.get)}catch{return!1}}(e)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||(null==(n=null==(r=e.socket)?void 0:r._httpMessage)?void 0:n.getHeader(t))}(e,ry.Headers.ClerkRequestData);r=function(e){if(!e)return{};let t=t4()?im||ip:im||ip||s1;try{return s4(e,t)}catch{if(ix)try{return s4(e,s1)}catch{s2()}s2()}}(t)}catch(e){if(e&&iT(e))throw e}let n=null!=(t=null==(e=iA.getStore())?void 0:e.get("requestData"))?t:r;return(null==n?void 0:n.secretKey)||(null==n?void 0:n.publishableKey)?iO(n):iO({})},s5="__clerk_keys_";async function s6(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function s8(){let e=process.env.PWD;if(!e)return`${s5}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),r=await s6(t);return`${s5}${r}`}async function s9(e){let t;if(!ix)return;let r=await s8();try{r&&(t=JSON.parse(e(r)||"{}"))}catch{t=void 0}return t}let s7={REDIRECT_TO_URL:"CLERK_PROTECT_REDIRECT_TO_URL",REDIRECT_TO_SIGN_IN:"CLERK_PROTECT_REDIRECT_TO_SIGN_IN"},ae={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},at=new Set(Object.values(ae)),ar="NEXT_REDIRECT";function an(e,t,r="replace",n=307){let i=Error(ar);throw i.digest=`${ar};${r};${e};${n};`,i.clerk_digest=s7.REDIRECT_TO_URL,Object.assign(i,t),i}function ai(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,n]=t,i=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===ar&&("replace"===n||"push"===n)&&"string"==typeof i&&!isNaN(s)&&307===s}let as=e=>{var t,r;return!!e.headers.get(n9.Headers.NextUrl)&&((null==(t=e.headers.get(ry.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(ry.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(n9.Headers.NextAction))},aa=e=>{var t;return"document"===e.headers.get(ry.Headers.SecFetchDest)||"iframe"===e.headers.get(ry.Headers.SecFetchDest)||(null==(t=e.headers.get(ry.Headers.Accept))?void 0:t.includes("text/html"))||ao(e)||au(e)},ao=e=>!!e.headers.get(n9.Headers.NextUrl)&&!as(e)||al(),al=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},au=e=>!!e.headers.get(n9.Headers.NextjsData),ac=e=>[e[0]instanceof Request?e[0]:void 0,e[0]instanceof Request?e[1]:void 0],ad=e=>["function"==typeof e[0]?e[0]:void 0,(2===e.length?e[1]:"function"==typeof e[0]?{}:e[0])||{}],ah=e=>"/clerk-sync-keyless"===e.nextUrl.pathname,ap=e=>{let t=e.nextUrl.searchParams.get("returnUrl"),r=new URL(e.url);return r.pathname="",W.redirect(t||r.toString())},af=(e,t)=>({...t,...sZ(e,t)}),ag=e=>(t={})=>{!function(e,t){an(e,{clerk_digest:s7.REDIRECT_TO_SIGN_IN,returnBackUrl:null===t?"":t||e})}(e.clerkUrl.toString(),t.returnBackUrl)},am=(e,t,r)=>async(n,i)=>(function(e){let{redirectToSignIn:t,authObject:r,redirect:n,notFound:i,request:s}=e;return async(...e)=>{var a,o,l,u,c,d;let h=(null==(a=e[0])?void 0:a.unauthenticatedUrl)||(null==(o=e[0])?void 0:o.unauthorizedUrl)?void 0:e[0],p=(null==(l=e[0])?void 0:l.unauthenticatedUrl)||(null==(u=e[1])?void 0:u.unauthenticatedUrl),f=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(d=e[1])?void 0:d.unauthorizedUrl),g=()=>f?n(f):i();return r.userId?h?"function"==typeof h?h(r.has)?r:g():r.has(h)?r:g():r:p?n(p):aa(s)?t():i()}})({request:e,redirect:e=>an(e,{redirectUrl:e}),notFound:()=>(function(){let e=Object.defineProperty(Error(n8),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n8,e})(),authObject:t,redirectToSignIn:r})(n,i),ay=(e,t,r,n)=>{if(function(e){return"object"==typeof e&&null!==e&&"digest"in e&&"NEXT_NOT_FOUND"===e.digest||function(e){if(!function(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return"NEXT_HTTP_ERROR_FALLBACK"===t&&at.has(Number(r))}(e))return;let[,t]=e.digest.split(";");return Number(t)}(e)===ae.NOT_FOUND}(e))return ie(W.rewrite(new URL(`/clerk_${Date.now()}`,r.url)),ry.Headers.AuthReason,"protect-rewrite");if(function(e){return!!ai(e)&&"clerk_digest"in e&&e.clerk_digest===s7.REDIRECT_TO_SIGN_IN}(e))return n0({redirectAdapter:s0,baseUrl:t.clerkUrl,signInUrl:n.signInUrl,signUpUrl:n.signUpUrl,publishableKey:n.publishableKey}).redirectToSignIn({returnBackUrl:e.returnBackUrl});if(ai(e))return s0(e.redirectUrl);throw e},av="9.19.0",a_=globalThis;function ab(){return aw(a_),a_}function aw(e){let t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||av,t[av]=t[av]||{}}function aS(e,t,r=a_){let n=r.__SENTRY__=r.__SENTRY__||{},i=n[av]=n[av]||{};return i[e]||(i[e]=t())}function ak(e=a_.crypto||a_.msCrypto){let t=()=>16*Math.random();try{if(e?.randomUUID)return e.randomUUID().replace(/-/g,"");e?.getRandomValues&&(t=()=>{let t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(e){}return"10000000100040008000100000000000".replace(/[018]/g,e=>(e^(15&t())>>e/4).toString(16))}function aE(){return Date.now()/1e3}let ax=function(){let{performance:e}=a_;if(!e?.now)return aE;let t=Date.now()-e.now(),r=void 0==e.timeOrigin?t:e.timeOrigin;return()=>(r+e.now())/1e3}(),aT=["debug","info","warn","error","log","assert","trace"],aR={},aC=aS("logger",function(){let e=!1,t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return aT.forEach(e=>{t[e]=()=>void 0}),t});function aO(e,t,r){try{Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0})}catch(e){}}let aI="_sentrySpan";function aP(e,t){t?aO(e,aI,t):delete e[aI]}let aA=Object.prototype.toString;function aN(e,t){return aA.call(e)===`[object ${t}]`}function aU(e){return!!(e?.then&&"function"==typeof e.then)}function aj(){return ak().substring(16)}class aL{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:ak(),sampleRand:Math.random()}}clone(){let e=new aL;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,aP(e,this[aI]),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&function(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||ax(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:ak()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{let t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let t="function"==typeof e?e(this):e,{tags:r,extra:n,user:i,contexts:s,level:a,fingerprint:o=[],propagationContext:l}=(t instanceof aL?t.getScopeData():aN(t,"Object")?e:void 0)||{};return this._tags={...this._tags,...r},this._extra={...this._extra,...n},this._contexts={...this._contexts,...s},i&&Object.keys(i).length&&(this._user=i),a&&(this._level=a),o.length&&(this._fingerprint=o),l&&(this._propagationContext=l),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,aP(this,void 0),this._attachments=[],this.setPropagationContext({traceId:ak(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){let r="number"==typeof t?t:100;if(r<=0)return this;let n={timestamp:aE(),...e,message:e.message?function(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}(e.message,2048):e.message};return this._breadcrumbs.push(n),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),this._client?.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:this[aI]}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=function e(t,r,n=2){if(!r||"object"!=typeof r||n<=0)return r;if(t&&0===Object.keys(r).length)return t;let i={...t};for(let t in r)Object.prototype.hasOwnProperty.call(r,t)&&(i[t]=e(i[t],r[t],n-1));return i}(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,t){let r=t?.event_id||ak();if(!this._client)return aC.warn("No client configured on scope - will not capture exception!"),r;let n=Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:n,...t,event_id:r},this),r}captureMessage(e,t,r){let n=r?.event_id||ak();if(!this._client)return aC.warn("No client configured on scope - will not capture message!"),n;let i=Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:i,...r,event_id:n},this),n}captureEvent(e,t){let r=t?.event_id||ak();return this._client?this._client.captureEvent(e,{...t,event_id:r},this):aC.warn("No client configured on scope - will not capture event!"),r}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}class aM{constructor(e,t){let r,n;r=e||new aL,n=t||new aL,this._stack=[{scope:r}],this._isolationScope=n}withScope(e){let t,r=this._pushScope();try{t=e(r)}catch(e){throw this._popScope(),e}return aU(t)?t.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),t)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function aD(){let e=aw(ab());return e.stack=e.stack||new aM(aS("defaultCurrentScope",()=>new aL),aS("defaultIsolationScope",()=>new aL))}function aq(e){return aD().withScope(e)}function az(e,t){let r=aD();return r.withScope(()=>(r.getStackTop().scope=e,t(e)))}function a$(e){return aD().withScope(()=>e(aD().getIsolationScope()))}function aH(e){let t=aw(e);return t.acs?t.acs:{withIsolationScope:a$,withScope:aq,withSetScope:az,withSetIsolationScope:(e,t)=>a$(t),getCurrentScope:()=>aD().getScope(),getIsolationScope:()=>aD().getIsolationScope()}}function aB(){return aH(ab()).getCurrentScope()}function aK(...e){let t=aH(ab());if(2===e.length){let[r,n]=e;return r?t.withSetScope(r,n):t.withScope(n)}return t.withScope(e[0])}function aW(){return aB().getClient()}let aJ="sentry.source",aF="sentry.sample_rate",aV="sentry.op",aX="sentry.origin",aG="sentry.custom_span_name",aY=!1;function aQ(e){return e&&e.length>0?e.map(({context:{spanId:e,traceId:t,traceFlags:r,...n},attributes:i})=>({span_id:e,trace_id:t,sampled:1===r,attributes:i,...n})):void 0}function aZ(e){return"number"==typeof e?a0(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?a0(e.getTime()):ax()}function a0(e){return e>0x2540be3ff?e/1e3:e}function a1(e){var t;if("function"==typeof e.getSpanJSON)return e.getSpanJSON();let{spanId:r,traceId:n}=e.spanContext();if((t=e).attributes&&t.startTime&&t.name&&t.endTime&&t.status){let{attributes:t,startTime:i,name:s,endTime:a,status:o,links:l}=e;return{span_id:r,trace_id:n,data:t,description:s,parent_span_id:"parentSpanId"in e?e.parentSpanId:"parentSpanContext"in e?e.parentSpanContext?.spanId:void 0,start_timestamp:aZ(i),timestamp:aZ(a)||void 0,status:a4(o),op:t[aV],origin:t[aX],links:aQ(l)}}return{span_id:r,trace_id:n,start_timestamp:0,data:{}}}function a2(e){let{traceFlags:t}=e.spanContext();return 1===t}function a4(e){if(e&&0!==e.code)return 1===e.code?"ok":e.message||"unknown_error"}let a3="_sentryChildSpans",a5="_sentryRootSpan";function a6(e,t){let r=e[a5]||e;aO(t,a5,r),e[a3]?e[a3].add(t):aO(e,a3,new Set([t]))}function a8(e){return e[a5]||e}let a9="_sentryScope",a7="_sentryIsolationScope";function oe(e,t,r){e&&(aO(e,a7,r),aO(e,a9,t))}function ot(e){return{scope:e[a9],isolationScope:e[a7]}}function or(e,t,r=()=>{}){var n,i,s;let a;try{a=e()}catch(e){throw t(e),r(),e}return n=a,i=t,s=r,aU(n)?n.then(e=>(s(),e),e=>{throw i(e),s(),e}):(s(),n)}function on(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let t=e||aW()?.getOptions();return!!t&&(null!=t.tracesSampleRate||!!t.tracesSampler)}function oi(e){if("boolean"==typeof e)return Number(e);let t="string"==typeof e?parseFloat(e):e;if(!("number"!=typeof t||isNaN(t))&&!(t<0)&&!(t>1))return t}let os=/^sentry-/;function oa(e){return e.split(",").map(e=>e.split("=").map(e=>{try{return decodeURIComponent(e.trim())}catch{return}})).reduce((e,[t,r])=>(t&&r&&(e[t]=r),e),{})}let oo="_frozenDsc";function ol(e){let t=aW();if(!t)return{};let r=a8(e),n=a1(r),i=n.data,s=r.spanContext().traceState,a=s?.get("sentry.sample_rate")??i[aF]??i["sentry.previous_trace_sample_rate"];function o(e){return("number"==typeof a||"string"==typeof a)&&(e.sample_rate=`${a}`),e}let l=r[oo];if(l)return o(l);let u=s?.get("sentry.dsc"),c=u&&function(e){let t=function(e){if(e&&(aN(e,"String")||Array.isArray(e)))return Array.isArray(e)?e.reduce((e,t)=>(Object.entries(oa(t)).forEach(([t,r])=>{e[t]=r}),e),{}):oa(e)}(e);if(!t)return;let r=Object.entries(t).reduce((e,[t,r])=>(t.match(os)&&(e[t.slice(7)]=r),e),{});return Object.keys(r).length>0?r:void 0}(u);if(c)return o(c);let d=function(e,t){let r=t.getOptions(),{publicKey:n}=t.getDsn()||{},i={environment:r.environment||"production",release:r.release,public_key:n,trace_id:e};return t.emit("createDsc",i),i}(e.spanContext().traceId,t),h=i[aJ],p=n.description;return"url"!==h&&p&&(d.transaction=p),on()&&(d.sampled=String(a2(r)),d.sample_rand=s?.get("sentry.sample_rand")??ot(r).scope?.getPropagationContext().sampleRand.toString()),o(d),t.emit("createDsc",d,r),d}class ou{constructor(e={}){this._traceId=e.traceId||ak(),this._spanId=e.spanId||aj()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(e){}setAttribute(e,t){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,t,r){return this}addLink(e){return this}addLinks(e){return this}recordException(e,t){}}function oc(e){if(!e||0===e.length)return;let t={};return e.forEach(e=>{let r=e.attributes||{},n=r["sentry.measurement_unit"],i=r["sentry.measurement_value"];"string"==typeof n&&"number"==typeof i&&(t[e.name]={value:i,unit:n})}),t}class od{constructor(e={}){this._traceId=e.traceId||ak(),this._spanId=e.spanId||aj(),this._startTime=e.startTimestamp||ax(),this._links=e.links,this._attributes={},this.setAttributes({[aX]:"manual",[aV]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,t){}spanContext(){let{_spanId:e,_traceId:t,_sampled:r}=this;return{spanId:e,traceId:t,traceFlags:+!!r}}setAttribute(e,t){return void 0===t?delete this._attributes[e]:this._attributes[e]=t,this}setAttributes(e){return Object.keys(e).forEach(t=>this.setAttribute(t,e[t])),this}updateStartTime(e){this._startTime=aZ(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(aJ,"custom"),this}end(e){var t;this._endTime||(this._endTime=aZ(e),t=0,this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[aV],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:a4(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[aX],profile_id:this._attributes["sentry.profile_id"],exclusive_time:this._attributes["sentry.exclusive_time"],measurements:oc(this._events),is_segment:this._isStandaloneSpan&&a8(this)===this||void 0,segment_id:this._isStandaloneSpan?a8(this).spanContext().spanId:void 0,links:aQ(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,t,r){let n=oh(t)?t:r||ax(),i=oh(t)?{}:t||{},s={name:e,time:aZ(n),attributes:i};return this._events.push(s),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){let e=aW();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===a8(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(e){let t=aW();if(!t)return;let r=e[1];if(!r||0===r.length)return t.recordDroppedEvent("before_send","span");t.sendEnvelope(e)}(function(e,t){let r=ol(e[0]),n=t?.getDsn(),i=t?.getOptions().tunnel,s={sent_at:new Date().toISOString(),...!!r.trace_id&&!!r.public_key&&{trace:r},...!!i&&n&&{dsn:function(e,t=!1){let{host:r,path:n,pass:i,port:s,projectId:a,protocol:o,publicKey:l}=e;return`${o}://${l}${t&&i?`:${i}`:""}@${r}${s?`:${s}`:""}/${n?`${n}/`:n}${a}`}(n)}},a=t?.getOptions().beforeSendSpan,o=a?e=>{let t=a1(e),r=a(t);return r||(aY||(function(e){if(!("console"in a_))return e();let t=a_.console,r={},n=Object.keys(aR);n.forEach(e=>{let n=aR[e];r[e]=t[e],t[e]=n});try{return e()}finally{n.forEach(e=>{t[e]=r[e]})}}(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),aY=!0),t)}:a1,l=[];for(let t of e){let e=o(t);e&&l.push([{type:"span"},e])}return function(e,t=[]){return[e,t]}(s,l)}([this],e)):e&&e.recordDroppedEvent("sample_rate","span"));let t=this._convertSpanToTransaction();t&&(ot(this).scope||aB()).captureEvent(t)}_convertSpanToTransaction(){if(!op(a1(this)))return;this._name||(this._name="<unlabeled transaction>");let{scope:e,isolationScope:t}=ot(this);if(!0!==this._sampled)return;let r=(function(e){let t=new Set;return!function e(r){if(!t.has(r)&&a2(r))for(let n of(t.add(r),r[a3]?Array.from(r[a3]):[]))e(n)}(e),Array.from(t)})(this).filter(e=>{var t;return e!==this&&!((t=e)instanceof od&&t.isStandaloneSpan())}).map(e=>a1(e)).filter(op),n=this._attributes[aJ];delete this._attributes[aG],r.forEach(e=>{delete e.data[aG]});let i={contexts:{trace:function(e){let{spanId:t,traceId:r}=e.spanContext(),{data:n,op:i,parent_span_id:s,status:a,origin:o,links:l}=a1(e);return{parent_span_id:s,span_id:t,trace_id:r,data:n,op:i,status:a,origin:o,links:l}}(this)},spans:r.length>1e3?r.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):r,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:t,dynamicSamplingContext:ol(this)},...n&&{transaction_info:{source:n}}},s=oc(this._events);return s&&Object.keys(s).length&&(i.measurements=s),i}}function oh(e){return e&&"number"==typeof e||e instanceof Date||Array.isArray(e)}function op(e){return!!e.start_timestamp&&!!e.timestamp&&!!e.span_id&&!!e.trace_id}let of="__SENTRY_SUPPRESS_TRACING__";function og(){return aH(ab())}function om(e,t,r){let n=aW(),i=n?.getOptions()||{},{name:s=""}=e,a={spanAttributes:{...e.attributes},spanName:s,parentSampled:r};n?.emit("beforeSampling",a,{decision:!1});let o=a.parentSampled??r,l=a.spanAttributes,u=t.getPropagationContext(),[c,d,h]=t.getScopeData().sdkProcessingMetadata[of]?[!1]:function(e,t,r){let n,i;if(!on(e))return[!1];"function"==typeof e.tracesSampler?(n=e.tracesSampler({...t,inheritOrSampleWith:e=>"number"==typeof t.parentSampleRate?t.parentSampleRate:"boolean"==typeof t.parentSampled?Number(t.parentSampled):e}),i=!0):void 0!==t.parentSampled?n=t.parentSampled:void 0!==e.tracesSampleRate&&(n=e.tracesSampleRate,i=!0);let s=oi(n);if(void 0===s)return[!1];if(!s)return[!1,s,i];let a=r<s;return[a,s,i]}(i,{name:s,parentSampled:o,attributes:l,parentSampleRate:oi(u.dsc?.sample_rate)},u.sampleRand),p=new od({...e,attributes:{[aJ]:"custom",[aF]:void 0!==d&&h?d:void 0,...l},sampled:c});return!c&&n&&n.recordDroppedEvent("sample_rate","transaction"),n&&n.emit("spanStart",p),p}let oy=["user","level","extra","contexts","tags","fingerprint","propagationContext"];async function ov(e){let t=aW();return t?t.flush(e):Promise.resolve(!1)}async function o_(){try{await ov(2e3)}catch(e){}}function ob(e){return new Proxy(e,{apply:async(e,t,r)=>(function(...e){let t=aH(ab());if(2===e.length){let[r,n]=e;return r?t.withSetIsolationScope(r,n):t.withIsolationScope(n)}return t.withIsolationScope(e[0])})(n=>{let i,s,a=r[0],o=aB();a instanceof Request?(n.setSDKProcessingMetadata({normalizedRequest:function(e){let t=function(e){let t={};try{e.forEach((e,r)=>{"string"==typeof e&&(t[r]=e)})}catch{}return t}(e.headers);return{method:e.method,url:e.url,query_string:function(e){if(e)try{let t=new URL(e,"http://s.io").search.slice(1);return t.length?t:void 0}catch{return}}(e.url),headers:t}}(a)}),i=`middleware ${a.method} ${new URL(a.url).pathname}`,s="url"):(i="middleware",s="component"),o.setTransactionName(i);let l=function(){let e=aH(ab());return e.getActiveSpan?e.getActiveSpan():aB()[aI]}();if(l){i="middleware",s="component";let e=a8(l);e&&oe(e,o,n)}return function(e,t){let r=aH(ab());if(r.startSpan)return r.startSpan(e,t);let n=function(e){let t={isStandalone:(e.experimental||{}).standalone,...e};if(e.startTime){let r={...t};return r.startTimestamp=aZ(e.startTime),delete r.startTime,r}return t}(e),{forceTransaction:i,parentSpan:s,scope:a}=e;return aK(a?.clone(),()=>{var r;return(void 0!==(r=s)?e=>(function(e,t){let r=function(){return aH(ab())}();return r.withActiveSpan?r.withActiveSpan(e,t):aK(r=>(aP(r,e||void 0),t(r)))})(r,e):e=>e())(()=>{let r=aB(),s=function(e){let t=e[aI];if(!t)return;let r=aW();return(r?r.getOptions():{}).parentSpanIsAlwaysRootSpan?a8(t):t}(r),a=e.onlyIfParent&&!s?new ou:function({parentSpan:e,spanArguments:t,forceTransaction:r,scope:n}){var i,s,a;let o;if(!on()){let n=new ou;if(r||!e){let e={sampled:"false",sample_rate:"0",transaction:t.name,...ol(n)};aO(n,oo,e)}return n}let l=aH(ab()).getIsolationScope();if(e&&!r)o=function(e,t,r){let{spanId:n,traceId:i}=e.spanContext(),s=!t.getScopeData().sdkProcessingMetadata[of]&&a2(e),a=s?new od({...r,parentSpanId:n,traceId:i,sampled:s}):new ou({traceId:i});a6(e,a);let o=aW();return o&&(o.emit("spanStart",a),r.endTimestamp&&o.emit("spanEnd",a)),a}(e,n,t),a6(e,o);else if(e){let r=ol(e),{traceId:i,spanId:s}=e.spanContext(),a=a2(e);aO(o=om({traceId:i,parentSpanId:s,...t},n,a),oo,r)}else{let{traceId:e,dsc:r,parentSpanId:i,sampled:s}={...l.getPropagationContext(),...n.getPropagationContext()};o=om({traceId:e,parentSpanId:i,...t},n,s),r&&aO(o,oo,r)}return a=0,oe(o,n,l),o}({parentSpan:s,spanArguments:n,forceTransaction:i,scope:r});return aP(r,a),or(()=>t(a),()=>{let{status:e}=a1(a);a.isRecording()&&(!e||"ok"===e)&&a.setStatus({code:2,message:"internal_error"})},()=>{a.end()})})})}({name:i,op:"http.server.middleware",attributes:{[aJ]:s,[aX]:"auto.function.nextjs.wrapMiddlewareWithSentry"}},()=>or(()=>e.apply(t,r),e=>{aB().captureException(e,function(e){if(e){var t;return(t=e)instanceof aL||"function"==typeof t||Object.keys(e).some(e=>oy.includes(e))?{captureContext:e}:e}}({mechanism:{type:"instrument",handled:!1}}))},()=>{var e=o_();let t=a_[Symbol.for("@vercel/request-context")],r=t?.get&&t.get()?t.get():{};r?.waitUntil&&r.waitUntil(e)}))})})}let ow=(e=>{if("function"==typeof e)return t=>e(t);let t=tr(e);return e=>t(e.nextUrl.pathname)})(["/dashboard(.*)"]);var oS=((...e)=>{let[t,r]=ac(e),[n,i]=ad(e);return iA.run(iP,()=>{let e=ic("clerkMiddleware",e=>async(t,r)=>{let s="function"==typeof i?await i(t):i,a=await s9(e=>{var r;return null==(r=t.cookies.get(e))?void 0:r.value}),o=function(e,t){return e||t(),e}(s.publishableKey||ig||(null==a?void 0:a.publishableKey),()=>sX.throwMissingPublishableKeyError()),l=function(e,t){return e||t(),e}(s.secretKey||ip||(null==a?void 0:a.secretKey),()=>sX.throwMissingSecretKeyError()),u={publishableKey:o,secretKey:l,signInUrl:s.signInUrl||iw,signUpUrl:s.signUpUrl||"/auth/sign-up",...s};iP.set("requestData",u);let c=await s3();c.telemetry.record({event:"METHOD_CALLED",payload:{method:"clerkMiddleware",...{handler:!!n,satellite:!!u.isSatellite,proxy:!!u.proxyUrl}}}),u.debug&&e.enable();let d=nO(t);e.debug("options",u),e.debug("url",()=>d.toJSON());let h=t.headers.get("authorization");h&&h.startsWith("Basic ")&&e.debug("Basic Auth detected");let p=await c.authenticateRequest(d,af(d,u));if(e.debug("requestState",()=>({status:p.status,headers:JSON.stringify(Object.fromEntries(p.headers)),reason:p.reason})),p.headers.get(ry.Headers.Location))return new Response(null,{status:307,headers:p.headers});if(p.status===nw.Handshake)throw Error("Clerk: handshake status without redirect");let f=p.toAuth();e.debug("auth",()=>({auth:f,debug:f.debug()}));let g=ag(d),m=await am(d,f,g),y=Object.assign(f,{redirectToSignIn:g}),v=()=>Promise.resolve(y);v.protect=m;let _=W.next();try{_=await iA.run(iP,async()=>null==n?void 0:n(v,t,r))||_}catch(e){_=ay(e,d,t,p)}if(p.headers&&p.headers.forEach((e,t)=>{_.headers.append(t,e)}),n7(_))return e.debug("handlerResult is redirect"),is(d,_,u);u.debug&&sQ(_,d,{[ry.Headers.EnableDebug]:"true"});let b=l===(null==a?void 0:a.secretKey)?{publishableKey:null==a?void 0:a.publishableKey,secretKey:null==a?void 0:a.secretKey}:{};return!function(e,t,r,n,i){let s,{reason:a,message:o,status:l,token:u}=r;if(t||(t=W.next()),t.headers.get(n9.Headers.NextRedirect))return;"1"===t.headers.get(n9.Headers.NextResume)&&(t.headers.delete(n9.Headers.NextResume),s=new URL(e.url));let c=t.headers.get(n9.Headers.NextRewrite);if(c){let t=new URL(e.url);if((s=new URL(c)).origin!==t.origin)return}if(s){let r=function(e,t){var r;let n=e=>!e||!Object.values(e).some(e=>void 0!==e);if(n(e)&&n(t))return;if(e.secretKey&&!im)return void iU.warnOnce("Clerk: Missing `CLERK_ENCRYPTION_KEY`. Required for propagating `secretKey` middleware option. See docs: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys");let i=t4()?im||(r=()=>sX.throwMissingSecretKeyError(),ip||r(),ip):im||ip||s1;return sH.encrypt(JSON.stringify({...t,...e}),i).toString()}(n,i);sQ(t,e,{[ry.Headers.AuthStatus]:l,[ry.Headers.AuthToken]:u||"",[ry.Headers.AuthSignature]:u?sW(u,(null==n?void 0:n.secretKey)||ip||i.secretKey||"").toString():"",[ry.Headers.AuthMessage]:o||"",[ry.Headers.AuthReason]:a||"",[ry.Headers.ClerkUrl]:e.clerkUrl.toString(),...r?{[ry.Headers.ClerkRequestData]:r}:{}}),t.headers.set(n9.Headers.NextRewrite,s.href)}}(d,_,p,s,b),_}),s=async(t,r)=>{if(ah(t))return ap(t);let n="function"==typeof i?await i(t):i,s=await s9(e=>{var r;return null==(r=t.cookies.get(e))?void 0:r.value});if(!(n.publishableKey||ig||(null==s?void 0:s.publishableKey))){let e=W.next();return sQ(e,t,{[ry.Headers.AuthStatus]:"signed-out"}),e}return e(t,r)},a=async(t,r)=>ix?s(t,r):e(t,r);return t&&r?a(t,r):a})})(async(e,t)=>{ow(t)&&await e.protect()});let ok={matcher:["/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)","/(api|trpc)(.*)"]};var oE=Object.freeze({__proto__:null,config:ok,default:oS});"middleware"in oE&&"function"==typeof oE.middleware?i=oE.middleware:"default"in oE&&"function"==typeof oE.default?s=oE.default:"function"==typeof oE&&(s=oE);let ox=i?ob(i):void 0,oT=s?ob(s):void 0;r(931);let oR={...a},oC=oR.middleware||oR.default,oO="/src/middleware";if("function"!=typeof oC)throw Object.defineProperty(Error(`The Middleware "${oO}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function oI(e){return e5({...e,page:oO,handler:async(...e)=>{try{return await oC(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await c(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},930:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DiagConsoleLogger:()=>U,DiagLogLevel:()=>n,INVALID_SPANID:()=>ed,INVALID_SPAN_CONTEXT:()=>ep,INVALID_TRACEID:()=>eh,ProxyTracer:()=>eP,ProxyTracerProvider:()=>eN,ROOT_CONTEXT:()=>A,SamplingDecision:()=>a,SpanKind:()=>o,SpanStatusCode:()=>l,TraceFlags:()=>s,ValueType:()=>i,baggageEntryMetadataFromString:()=>I,context:()=>ez,createContextKey:()=>P,createNoopMeter:()=>ee,createTraceState:()=>eq,default:()=>e2,defaultTextMapGetter:()=>et,defaultTextMapSetter:()=>er,diag:()=>e$,isSpanContextValid:()=>eT,isValidSpanId:()=>ex,isValidTraceId:()=>eE,metrics:()=>eK,propagation:()=>eZ,trace:()=>e1});var n,i,s,a,o,l,u="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof r.g?r.g:{},c="1.9.0",d=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/,h=function(e){var t=new Set([e]),r=new Set,n=e.match(d);if(!n)return function(){return!1};var i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=i.prerelease)return function(t){return t===e};function s(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;var n=e.match(d);if(!n)return s(e);var a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=a.prerelease||i.major!==a.major)return s(e);if(0===i.major)return i.minor===a.minor&&i.patch<=a.patch?(t.add(e),!0):s(e);return i.minor<=a.minor?(t.add(e),!0):s(e)}}(c),p=Symbol.for("opentelemetry.js.api."+c.split(".")[0]);function f(e,t,r,n){void 0===n&&(n=!1);var i,s=u[p]=null!=(i=u[p])?i:{version:c};if(!n&&s[e]){var a=Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return r.error(a.stack||a.message),!1}if(s.version!==c){var a=Error("@opentelemetry/api: Registration of version v"+s.version+" for "+e+" does not match previously registered API v"+c);return r.error(a.stack||a.message),!1}return s[e]=t,r.debug("@opentelemetry/api: Registered a global for "+e+" v"+c+"."),!0}function g(e){var t,r,n=null==(t=u[p])?void 0:t.version;if(n&&h(n))return null==(r=u[p])?void 0:r[e]}function m(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+c+".");var r=u[p];r&&delete r[e]}var y=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},v=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},_=function(){function e(e){this._namespace=e.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("debug",this._namespace,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("error",this._namespace,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("info",this._namespace,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("warn",this._namespace,e)},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("verbose",this._namespace,e)},e}();function b(e,t,r){var n=g("diag");if(n)return r.unshift(t),n[e].apply(n,v([],y(r),!1))}!function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(n||(n={}));var w=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},S=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},k=function(){function e(){function e(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=g("diag");if(n)return n[e].apply(n,S([],w(t),!1))}}var t=this;t.setLogger=function(e,r){if(void 0===r&&(r={logLevel:n.INFO}),e===t){var i,s,a,o=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(i=o.stack)?i:o.message),!1}"number"==typeof r&&(r={logLevel:r});var l=g("diag"),u=function(e,t){function r(r,n){var i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.NONE?e=n.NONE:e>n.ALL&&(e=n.ALL),t=t||{},{error:r("error",n.ERROR),warn:r("warn",n.WARN),info:r("info",n.INFO),debug:r("debug",n.DEBUG),verbose:r("verbose",n.VERBOSE)}}(null!=(s=r.logLevel)?s:n.INFO,e);if(l&&!r.suppressOverrideMessage){var c=null!=(a=Error().stack)?a:"<failed to generate stacktrace>";l.warn("Current logger will be overwritten from "+c),u.warn("Current logger will overwrite one already registered from "+c)}return f("diag",u,t,!0)},t.disable=function(){m("diag",t)},t.createComponentLogger=function(e){return new _(e)},t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}(),E=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},x=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},T=function(){function e(e){this._entries=e?new Map(e):new Map}return e.prototype.getEntry=function(e){var t=this._entries.get(e);if(t)return Object.assign({},t)},e.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(e){var t=E(e,2);return[t[0],t[1]]})},e.prototype.setEntry=function(t,r){var n=new e(this._entries);return n._entries.set(t,r),n},e.prototype.removeEntry=function(t){var r=new e(this._entries);return r._entries.delete(t),r},e.prototype.removeEntries=function(){for(var t,r,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var s=new e(this._entries);try{for(var a=x(n),o=a.next();!o.done;o=a.next()){var l=o.value;s._entries.delete(l)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return s},e.prototype.clear=function(){return new e},e}(),R=Symbol("BaggageEntryMetadata"),C=k.instance();function O(e){return void 0===e&&(e={}),new T(new Map(Object.entries(e)))}function I(e){return"string"!=typeof e&&(C.error("Cannot create baggage metadata from unknown type: "+typeof e),e=""),{__TYPE__:R,toString:function(){return e}}}function P(e){return Symbol.for(e)}var A=new function e(t){var r=this;r._currentContext=t?new Map(t):new Map,r.getValue=function(e){return r._currentContext.get(e)},r.setValue=function(t,n){var i=new e(r._currentContext);return i._currentContext.set(t,n),i},r.deleteValue=function(t){var n=new e(r._currentContext);return n._currentContext.delete(t),n}},N=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}],U=function(){for(var e=0;e<N.length;e++)this[N[e].n]=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(console){var n=console[e];if("function"!=typeof n&&(n=console.log),"function"==typeof n)return n.apply(console,t)}}}(N[e].c)},j=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),L=function(){function e(){}return e.prototype.createGauge=function(e,t){return V},e.prototype.createHistogram=function(e,t){return X},e.prototype.createCounter=function(e,t){return F},e.prototype.createUpDownCounter=function(e,t){return G},e.prototype.createObservableGauge=function(e,t){return Q},e.prototype.createObservableCounter=function(e,t){return Y},e.prototype.createObservableUpDownCounter=function(e,t){return Z},e.prototype.addBatchObservableCallback=function(e,t){},e.prototype.removeBatchObservableCallback=function(e){},e}(),M=function(){},D=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t.prototype.add=function(e,t){},t}(M),q=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t.prototype.add=function(e,t){},t}(M),z=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t.prototype.record=function(e,t){},t}(M),$=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t.prototype.record=function(e,t){},t}(M),H=function(){function e(){}return e.prototype.addCallback=function(e){},e.prototype.removeCallback=function(e){},e}(),B=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t}(H),K=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t}(H),W=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return j(t,e),t}(H),J=new L,F=new D,V=new z,X=new $,G=new q,Y=new B,Q=new K,Z=new W;function ee(){return J}!function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(i||(i={}));var et={get:function(e,t){if(null!=e)return e[t]},keys:function(e){return null==e?[]:Object.keys(e)}},er={set:function(e,t,r){null!=e&&(e[t]=r)}},en=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},ei=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},es=function(){function e(){}return e.prototype.active=function(){return A},e.prototype.with=function(e,t,r){for(var n=[],i=3;i<arguments.length;i++)n[i-3]=arguments[i];return t.call.apply(t,ei([r],en(n),!1))},e.prototype.bind=function(e,t){return t},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),ea=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(i)throw i.error}}return a},eo=function(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))},el="context",eu=new es,ec=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(e){return f(el,e,k.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(e,t,r){for(var n,i=[],s=3;s<arguments.length;s++)i[s-3]=arguments[s];return(n=this._getContextManager()).with.apply(n,eo([e,t,r],ea(i),!1))},e.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},e.prototype._getContextManager=function(){return g(el)||eu},e.prototype.disable=function(){this._getContextManager().disable(),m(el,k.instance())},e}();!function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(s||(s={}));var ed="0000000000000000",eh="00000000000000000000000000000000",ep={traceId:eh,spanId:ed,traceFlags:s.NONE},ef=function(){function e(e){void 0===e&&(e=ep),this._spanContext=e}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return this},e.prototype.setAttributes=function(e){return this},e.prototype.addEvent=function(e,t){return this},e.prototype.addLink=function(e){return this},e.prototype.addLinks=function(e){return this},e.prototype.setStatus=function(e){return this},e.prototype.updateName=function(e){return this},e.prototype.end=function(e){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(e,t){},e}(),eg=P("OpenTelemetry Context Key SPAN");function em(e){return e.getValue(eg)||void 0}function ey(){return em(ec.getInstance().active())}function ev(e,t){return e.setValue(eg,t)}function e_(e){return e.deleteValue(eg)}function eb(e,t){return ev(e,new ef(t))}function ew(e){var t;return null==(t=em(e))?void 0:t.spanContext()}var eS=/^([0-9a-f]{32})$/i,ek=/^[0-9a-f]{16}$/i;function eE(e){return eS.test(e)&&e!==eh}function ex(e){return ek.test(e)&&e!==ed}function eT(e){return eE(e.traceId)&&ex(e.spanId)}function eR(e){return new ef(e)}var eC=ec.getInstance(),eO=function(){function e(){}return e.prototype.startSpan=function(e,t,r){if(void 0===r&&(r=eC.active()),null==t?void 0:t.root)return new ef;var n,i=r&&ew(r);return"object"==typeof(n=i)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&eT(i)?new ef(i):new ef},e.prototype.startActiveSpan=function(e,t,r,n){if(!(arguments.length<2)){2==arguments.length?a=t:3==arguments.length?(i=t,a=r):(i=t,s=r,a=n);var i,s,a,o=null!=s?s:eC.active(),l=this.startSpan(e,i,o),u=ev(o,l);return eC.with(u,a,void 0,l)}},e}(),eI=new eO,eP=function(){function e(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}return e.prototype.startSpan=function(e,t,r){return this._getTracer().startSpan(e,t,r)},e.prototype.startActiveSpan=function(e,t,r,n){var i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):eI},e}(),eA=new(function(){function e(){}return e.prototype.getTracer=function(e,t,r){return new eO},e}()),eN=function(){function e(){}return e.prototype.getTracer=function(e,t,r){var n;return null!=(n=this.getDelegateTracer(e,t,r))?n:new eP(this,e,t,r)},e.prototype.getDelegate=function(){var e;return null!=(e=this._delegate)?e:eA},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateTracer=function(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)},e}();!function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(a||(a={})),function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(o||(o={})),function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(l||(l={}));var eU="[_0-9a-z-*/]",ej=RegExp("^(?:[a-z]"+eU+"{0,255}|"+("[a-z0-9]"+eU+"{0,240}@[a-z]")+eU+"{0,13})$"),eL=/^[ -~]{0,255}[!-~]$/,eM=/,|=/,eD=function(){function e(e){this._internalState=new Map,e&&this._parse(e)}return e.prototype.set=function(e,t){var r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r},e.prototype.unset=function(e){var t=this._clone();return t._internalState.delete(e),t},e.prototype.get=function(e){return this._internalState.get(e)},e.prototype.serialize=function(){var e=this;return this._keys().reduce(function(t,r){return t.push(r+"="+e.get(r)),t},[]).join(",")},e.prototype._parse=function(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce(function(e,t){var r=t.trim(),n=r.indexOf("=");if(-1!==n){var i=r.slice(0,n),s=r.slice(n+1,t.length);ej.test(i)&&eL.test(s)&&!eM.test(s)&&e.set(i,s)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},e.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},e.prototype._clone=function(){var t=new e;return t._internalState=new Map(this._internalState),t},e}();function eq(e){return new eD(e)}var ez=ec.getInstance(),e$=k.instance(),eH=new(function(){function e(){}return e.prototype.getMeter=function(e,t,r){return J},e}()),eB="metrics",eK=(function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalMeterProvider=function(e){return f(eB,e,k.instance())},e.prototype.getMeterProvider=function(){return g(eB)||eH},e.prototype.getMeter=function(e,t,r){return this.getMeterProvider().getMeter(e,t,r)},e.prototype.disable=function(){m(eB,k.instance())},e})().getInstance(),eW=function(){function e(){}return e.prototype.inject=function(e,t){},e.prototype.extract=function(e,t){return e},e.prototype.fields=function(){return[]},e}(),eJ=P("OpenTelemetry Baggage Key");function eF(e){return e.getValue(eJ)||void 0}function eV(){return eF(ec.getInstance().active())}function eX(e,t){return e.setValue(eJ,t)}function eG(e){return e.deleteValue(eJ)}var eY="propagation",eQ=new eW,eZ=(function(){function e(){this.createBaggage=O,this.getBaggage=eF,this.getActiveBaggage=eV,this.setBaggage=eX,this.deleteBaggage=eG}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalPropagator=function(e){return f(eY,e,k.instance())},e.prototype.inject=function(e,t,r){return void 0===r&&(r=er),this._getGlobalPropagator().inject(e,t,r)},e.prototype.extract=function(e,t,r){return void 0===r&&(r=et),this._getGlobalPropagator().extract(e,t,r)},e.prototype.fields=function(){return this._getGlobalPropagator().fields()},e.prototype.disable=function(){m(eY,k.instance())},e.prototype._getGlobalPropagator=function(){return g(eY)||eQ},e})().getInstance(),e0="trace",e1=(function(){function e(){this._proxyTracerProvider=new eN,this.wrapSpanContext=eR,this.isSpanContextValid=eT,this.deleteSpan=e_,this.getSpan=em,this.getActiveSpan=ey,this.getSpanContext=ew,this.setSpan=ev,this.setSpanContext=eb}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(e){var t=f(e0,this._proxyTracerProvider,k.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},e.prototype.getTracerProvider=function(){return g(e0)||this._proxyTracerProvider},e.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},e.prototype.disable=function(){m(e0,k.instance()),this._proxyTracerProvider=new eN},e})().getInstance();let e2={context:ez,diag:e$,metrics:eK,propagation:eZ,trace:e1}},931:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var n=r(627),i=r(483);function s(e){return(0,i.nJ)(e)||(0,n.RM)(e)}},944:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){if(!e)return;let[[t,r],...n]=o(e),{domain:i,expires:s,httponly:a,maxage:l,path:d,samesite:h,secure:p,partitioned:f,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var m,y,v={name:t,value:decodeURIComponent(r),domain:i,...s&&{expires:new Date(s)},...a&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:d,...h&&{sameSite:u.includes(m=(m=h).toLowerCase())?m:void 0},...p&&{secure:!0},...g&&{priority:c.includes(y=(y=g).toLowerCase())?y:void 0},...f&&{partitioned:!0}};let e={};for(let t in v)v[t]&&(e[t]=v[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>d,ResponseCookies:()=>h,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,s,a,o)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let l of n(s))i.call(e,l)||l===a||t(e,l,{get:()=>s[l],enumerable:!(o=r(s,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),s);var u=["strict","lax","none"],c=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},h=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},945:e=>{"use strict";let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),n=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),i=(e,t,s,a=new WeakMap)=>{if(s={deep:!1,target:{},...s},a.has(e))return a.get(e);a.set(e,s.target);let{target:o}=s;delete s.target;let l=e=>e.map(e=>n(e)?i(e,t,s,a):e);if(Array.isArray(e))return l(e);for(let[u,c]of Object.entries(e)){let d=t(u,c,e);if(d===r)continue;let[h,p,{shouldRecurse:f=!0}={}]=d;"__proto__"!==h&&(s.deep&&f&&n(p)&&(p=Array.isArray(p)?l(p):i(p,t,s,a)),o[h]=p)}return o};e.exports=(e,r,n)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return i(e,r,n)},e.exports.mapObjectSkip=r},968:(e,t,r)=>{"use strict";r.d(t,{cg:()=>o,xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}function o(e){return s?s.bind(e):i.bind(e)}},995:(e,t,r)=>{"use strict";e.exports=r(87)}},e=>{var t=e(e.s=843);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=t}]);
//# sourceMappingURL=middleware.js.map