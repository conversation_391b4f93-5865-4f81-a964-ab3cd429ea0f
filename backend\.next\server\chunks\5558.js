"use strict";exports.id=5558,exports.ids=[5558],exports.modules={11603:e=>{e.exports={rE:"3.840.0"}},65558:(e,t,n)=>{n.d(t,{getDefaultRoleAssumer:()=>t3,getDefaultRoleAssumerWithWebIdentity:()=>t7});var i=n(79083),r=n(49720),o=n(48871),s=n(10921),a=n(40229),l=n(35166),c=n(44716),d=n(38353),u=n(55288),p=n(72734),g=n(72587),h=n(10456);let f=async(e,t,n)=>({operation:(0,h.getSmithyContext)(t).operation,region:await (0,h.normalizeProvider)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),m=e=>{let t=[];if("AssumeRoleWithWebIdentity"===e.operation)t.push({schemeId:"smithy.api#noAuth"});else t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})});return t},S=e=>Object.assign(e,{stsClientCtor:ev}),y=e=>{let t=S(e);return Object.assign((0,g.h)(t),{authSchemePreference:(0,h.normalizeProvider)(e.authSchemePreference??[])})},E=e=>Object.assign(e,{useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,useGlobalEndpoint:e.useGlobalEndpoint??!1,defaultSigningName:"sts"}),P={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};var I=n(11603),x=n(82132),C=n(74088),v=n(86473),b=n(63551),w=n(99752),A=n(21612),R=n(13214),D=n(45927),T=n(42184),O=n(49168),k=n(21678),$=n(60052),N=n(50559),_=n(49163);let U="required",j="type",F="argv",W="booleanEquals",z="stringEquals",K="sigv4",H="us-east-1",q="endpoint",G="https://sts.{Region}.{PartitionResult#dnsSuffix}",L="tree",V="error",M="getAttr",B={[U]:!1,[j]:"String"},Y={[U]:!0,default:!1,[j]:"Boolean"},J={ref:"Endpoint"},Q={fn:"isSet",[F]:[{ref:"Region"}]},X={ref:"Region"},Z={fn:"aws.partition",[F]:[X],assign:"PartitionResult"},ee={ref:"UseFIPS"},et={ref:"UseDualStack"},en={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:K,signingName:"sts",signingRegion:H}]},headers:{}},ei={},er={conditions:[{fn:z,[F]:[X,"aws-global"]}],[q]:en,[j]:q},eo={fn:W,[F]:[ee,!0]},es={fn:W,[F]:[et,!0]},ea={fn:M,[F]:[{ref:"PartitionResult"},"supportsFIPS"]},el={ref:"PartitionResult"},ec={fn:W,[F]:[!0,{fn:M,[F]:[el,"supportsDualStack"]}]},ed=[{fn:"isSet",[F]:[J]}],eu=[eo],ep=[es],eg={version:"1.0",parameters:{Region:B,UseDualStack:Y,UseFIPS:Y,Endpoint:B,UseGlobalEndpoint:Y},rules:[{conditions:[{fn:W,[F]:[{ref:"UseGlobalEndpoint"},!0]},{fn:"not",[F]:ed},Q,Z,{fn:W,[F]:[ee,!1]},{fn:W,[F]:[et,!1]}],rules:[{conditions:[{fn:z,[F]:[X,"ap-northeast-1"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"ap-south-1"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"ap-southeast-1"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"ap-southeast-2"]}],endpoint:en,[j]:q},er,{conditions:[{fn:z,[F]:[X,"ca-central-1"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"eu-central-1"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"eu-north-1"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"eu-west-1"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"eu-west-2"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"eu-west-3"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"sa-east-1"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,H]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"us-east-2"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"us-west-1"]}],endpoint:en,[j]:q},{conditions:[{fn:z,[F]:[X,"us-west-2"]}],endpoint:en,[j]:q},{endpoint:{url:G,properties:{authSchemes:[{name:K,signingName:"sts",signingRegion:"{Region}"}]},headers:ei},[j]:q}],[j]:L},{conditions:ed,rules:[{conditions:eu,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[j]:V},{conditions:ep,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[j]:V},{endpoint:{url:J,properties:ei,headers:ei},[j]:q}],[j]:L},{conditions:[Q],rules:[{conditions:[Z],rules:[{conditions:[eo,es],rules:[{conditions:[{fn:W,[F]:[!0,ea]},ec],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:ei,headers:ei},[j]:q}],[j]:L},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[j]:V}],[j]:L},{conditions:eu,rules:[{conditions:[{fn:W,[F]:[ea,!0]}],rules:[{conditions:[{fn:z,[F]:[{fn:M,[F]:[el,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:ei,headers:ei},[j]:q},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:ei,headers:ei},[j]:q}],[j]:L},{error:"FIPS is enabled but this partition does not support FIPS",[j]:V}],[j]:L},{conditions:ep,rules:[{conditions:[ec],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:ei,headers:ei},[j]:q}],[j]:L},{error:"DualStack is enabled but this partition does not support DualStack",[j]:V}],[j]:L},er,{endpoint:{url:G,properties:ei,headers:ei},[j]:q}],[j]:L}],[j]:L},{error:"Invalid Configuration: Missing Region",[j]:V}]},eh=new _.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),ef=(e,t={})=>eh.get(e,()=>(0,_.resolveEndpoint)(eg,{endpointParams:e,logger:t.logger}));_.customEndpointFunctions.aws=N.awsEndpointFunctions;let em=e=>({apiVersion:"2011-06-15",base64Decoder:e?.base64Decoder??k.fromBase64,base64Encoder:e?.base64Encoder??k.toBase64,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??ef,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??m,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new v.f2},{schemeId:"smithy.api#noAuth",identityProvider:e=>e.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new l.NoAuthSigner}],logger:e?.logger??new p.NoOpLogger,serviceId:e?.serviceId??"STS",urlParser:e?.urlParser??O.parseUrl,utf8Decoder:e?.utf8Decoder??$.fromUtf8,utf8Encoder:e?.utf8Encoder??$.toUtf8});var eS=n(15807);let ey=e=>{(0,p.emitWarningIfUnsupportedVersion)(process.version);let t=(0,eS.I)(e),n=()=>t().then(p.loadConfigsForDefaultMode),i=em(e);(0,x.I)(process.version);let r={profile:e?.profile,logger:i.logger};return{...i,...e,runtime:"node",defaultsMode:t,authSchemePreference:e?.authSchemePreference??(0,A.loadConfig)(C.$,r),bodyLengthChecker:e?.bodyLengthChecker??D.n,defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,b.pf)({serviceId:i.serviceId,clientVersion:I.rE}),httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:t=>t.getIdentityProvider("aws.auth#sigv4")||(async t=>await e.credentialDefaultProvider(t?.__config||{})()),signer:new v.f2},{schemeId:"smithy.api#noAuth",identityProvider:e=>e.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new l.NoAuthSigner}],maxAttempts:e?.maxAttempts??(0,A.loadConfig)(u.qs,e),region:e?.region??(0,A.loadConfig)(a.NODE_REGION_CONFIG_OPTIONS,{...a.NODE_REGION_CONFIG_FILE_OPTIONS,...r}),requestHandler:R.NodeHttpHandler.create(e?.requestHandler??n),retryMode:e?.retryMode??(0,A.loadConfig)({...u.kN,default:async()=>(await n()).retryMode||T.DEFAULT_RETRY_MODE},e),sha256:e?.sha256??w.V.bind(null,"sha256"),streamCollector:e?.streamCollector??R.streamCollector,useDualstackEndpoint:e?.useDualstackEndpoint??(0,A.loadConfig)(a.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,r),useFipsEndpoint:e?.useFipsEndpoint??(0,A.loadConfig)(a.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,r),userAgentAppId:e?.userAgentAppId??(0,A.loadConfig)(b.hV,r)}};var eE=n(30208),eP=n(96126);let eI=e=>{let t=e.httpAuthSchemes,n=e.httpAuthSchemeProvider,i=e.credentials;return{setHttpAuthScheme(e){let n=t.findIndex(t=>t.schemeId===e.schemeId);-1===n?t.push(e):t.splice(n,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){n=e},httpAuthSchemeProvider:()=>n,setCredentials(e){i=e},credentials:()=>i}},ex=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),eC=(e,t)=>{let n=Object.assign((0,eE.Rq)(e),(0,p.getDefaultExtensionConfiguration)(e),(0,eP.getHttpHandlerExtensionConfiguration)(e),eI(e));return t.forEach(e=>e.configure(n)),Object.assign(e,(0,eE.$3)(n),(0,p.resolveDefaultRuntimeConfig)(n),(0,eP.resolveHttpHandlerRuntimeConfig)(n),ex(n))};class ev extends p.Client{config;constructor(...[e]){let t=ey(e||{});super(t),this.initConfig=t;let n=E(t),p=(0,s.resolveUserAgentConfig)(n),g=(0,u.$z)(p),h=(0,a.resolveRegionConfig)(g),m=(0,i.OV)(h),S=eC(y((0,d.Co)(m)),e?.extensions||[]);this.config=S,this.middlewareStack.use((0,s.getUserAgentPlugin)(this.config)),this.middlewareStack.use((0,u.ey)(this.config)),this.middlewareStack.use((0,c.vK)(this.config)),this.middlewareStack.use((0,i.TC)(this.config)),this.middlewareStack.use((0,r.Y7)(this.config)),this.middlewareStack.use((0,o.n4)(this.config)),this.middlewareStack.use((0,l.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config,{httpAuthSchemeParametersProvider:f,identityProviderConfigProvider:async e=>new l.DefaultIdentityProviderConfig({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,l.getHttpSigningPlugin)(this.config))}destroy(){super.destroy()}}var eb=n(17972);class ew extends p.ServiceException{constructor(e){super(e),Object.setPrototypeOf(this,ew.prototype)}}let eA=e=>({...e,...e.SecretAccessKey&&{SecretAccessKey:p.SENSITIVE_STRING}}),eR=e=>({...e,...e.Credentials&&{Credentials:eA(e.Credentials)}});class eD extends ew{name="ExpiredTokenException";$fault="client";constructor(e){super({name:"ExpiredTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,eD.prototype)}}class eT extends ew{name="MalformedPolicyDocumentException";$fault="client";constructor(e){super({name:"MalformedPolicyDocumentException",$fault:"client",...e}),Object.setPrototypeOf(this,eT.prototype)}}class eO extends ew{name="PackedPolicyTooLargeException";$fault="client";constructor(e){super({name:"PackedPolicyTooLargeException",$fault:"client",...e}),Object.setPrototypeOf(this,eO.prototype)}}class ek extends ew{name="RegionDisabledException";$fault="client";constructor(e){super({name:"RegionDisabledException",$fault:"client",...e}),Object.setPrototypeOf(this,ek.prototype)}}class e$ extends ew{name="IDPRejectedClaimException";$fault="client";constructor(e){super({name:"IDPRejectedClaimException",$fault:"client",...e}),Object.setPrototypeOf(this,e$.prototype)}}class eN extends ew{name="InvalidIdentityTokenException";$fault="client";constructor(e){super({name:"InvalidIdentityTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,eN.prototype)}}let e_=e=>({...e,...e.WebIdentityToken&&{WebIdentityToken:p.SENSITIVE_STRING}}),eU=e=>({...e,...e.Credentials&&{Credentials:eA(e.Credentials)}});class ej extends ew{name="IDPCommunicationErrorException";$fault="client";constructor(e){super({name:"IDPCommunicationErrorException",$fault:"client",...e}),Object.setPrototypeOf(this,ej.prototype)}}var eF=n(8523);let eW=async(e,t)=>{let n;return n=tB({...eQ(e,t),[tp]:th,[tq]:tu}),tc(t,td,"/",void 0,n)},ez=async(e,t)=>{let n;return n=tB({...eX(e,t),[tp]:tS,[tq]:tu}),tc(t,td,"/",void 0,n)},eK=async(e,t)=>{if(e.statusCode>=300)return eq(e,t);let n=await (0,eF.t_)(e.body,t),i={};return i=e3(n.AssumeRoleResult,t),{$metadata:ta(e),...i}},eH=async(e,t)=>{if(e.statusCode>=300)return eq(e,t);let n=await (0,eF.t_)(e.body,t),i={};return i=e7(n.AssumeRoleWithWebIdentityResult,t),{$metadata:ta(e),...i}},eq=async(e,t)=>{let n={...e,body:await (0,eF.FI)(e.body,t)},i=tY(e,n.body);switch(i){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await eG(n,t);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await eB(n,t);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await eY(n,t);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await eJ(n,t);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await eL(n,t);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await eV(n,t);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await eM(n,t);default:return tl({output:e,parsedBody:n.body.Error,errorCode:i})}},eG=async(e,t)=>{let n=e.body,i=te(n.Error,t),r=new eD({$metadata:ta(e),...i});return(0,p.decorateServiceException)(r,n)},eL=async(e,t)=>{let n=e.body,i=tt(n.Error,t),r=new ej({$metadata:ta(e),...i});return(0,p.decorateServiceException)(r,n)},eV=async(e,t)=>{let n=e.body,i=tn(n.Error,t),r=new e$({$metadata:ta(e),...i});return(0,p.decorateServiceException)(r,n)},eM=async(e,t)=>{let n=e.body,i=ti(n.Error,t),r=new eN({$metadata:ta(e),...i});return(0,p.decorateServiceException)(r,n)},eB=async(e,t)=>{let n=e.body,i=tr(n.Error,t),r=new eT({$metadata:ta(e),...i});return(0,p.decorateServiceException)(r,n)},eY=async(e,t)=>{let n=e.body,i=to(n.Error,t),r=new eO({$metadata:ta(e),...i});return(0,p.decorateServiceException)(r,n)},eJ=async(e,t)=>{let n=e.body,i=ts(n.Error,t),r=new ek({$metadata:ta(e),...i});return(0,p.decorateServiceException)(r,n)},eQ=(e,t)=>{let n={};if(null!=e[t$]&&(n[t$]=e[t$]),null!=e[tN]&&(n[tN]=e[tN]),null!=e[tA]){let i=eZ(e[tA],t);e[tA]?.length===0&&(n.PolicyArns=[]),Object.entries(i).forEach(([e,t])=>{n[`PolicyArns.${e}`]=t})}if(null!=e[tw]&&(n[tw]=e[tw]),null!=e[tx]&&(n[tx]=e[tx]),null!=e[tz]){let i=e6(e[tz],t);e[tz]?.length===0&&(n.Tags=[]),Object.entries(i).forEach(([e,t])=>{n[`Tags.${e}`]=t})}if(null!=e[tH]){let i=e5(e[tH],t);e[tH]?.length===0&&(n.TransitiveTagKeys=[]),Object.entries(i).forEach(([e,t])=>{n[`TransitiveTagKeys.${e}`]=t})}if(null!=e[tv]&&(n[tv]=e[tv]),null!=e[tF]&&(n[tF]=e[tF]),null!=e[tK]&&(n[tK]=e[tK]),null!=e[tj]&&(n[tj]=e[tj]),null!=e[tD]){let i=e2(e[tD],t);e[tD]?.length===0&&(n.ProvidedContexts=[]),Object.entries(i).forEach(([e,t])=>{n[`ProvidedContexts.${e}`]=t})}return n},eX=(e,t)=>{let n={};if(null!=e[t$]&&(n[t$]=e[t$]),null!=e[tN]&&(n[tN]=e[tN]),null!=e[tL]&&(n[tL]=e[tL]),null!=e[tT]&&(n[tT]=e[tT]),null!=e[tA]){let i=eZ(e[tA],t);e[tA]?.length===0&&(n.PolicyArns=[]),Object.entries(i).forEach(([e,t])=>{n[`PolicyArns.${e}`]=t})}return null!=e[tw]&&(n[tw]=e[tw]),null!=e[tx]&&(n[tx]=e[tx]),n},eZ=(e,t)=>{let n={},i=1;for(let r of e)null!==r&&(Object.entries(e0(r,t)).forEach(([e,t])=>{n[`member.${i}.${e}`]=t}),i++);return n},e0=(e,t)=>{let n={};return null!=e[tV]&&(n[tV]=e[tV]),n},e1=(e,t)=>{let n={};return null!=e[tR]&&(n[tR]=e[tR]),null!=e[tI]&&(n[tI]=e[tI]),n},e2=(e,t)=>{let n={},i=1;for(let r of e)null!==r&&(Object.entries(e1(r,t)).forEach(([e,t])=>{n[`member.${i}.${e}`]=t}),i++);return n},e4=(e,t)=>{let n={};return null!=e[tb]&&(n[tb]=e[tb]),null!=e[tG]&&(n[tG]=e[tG]),n},e5=(e,t)=>{let n={},i=1;for(let t of e)null!==t&&(n[`member.${i}`]=t,i++);return n},e6=(e,t)=>{let n={},i=1;for(let r of e)null!==r&&(Object.entries(e4(r,t)).forEach(([e,t])=>{n[`member.${i}.${e}`]=t}),i++);return n},e8=(e,t)=>{let n={};return null!=e[tf]&&(n[tf]=(0,p.expectString)(e[tf])),null!=e[ty]&&(n[ty]=(0,p.expectString)(e[ty])),n},e3=(e,t)=>{let n={};return null!=e[tP]&&(n[tP]=e9(e[tP],t)),null!=e[tm]&&(n[tm]=e8(e[tm],t)),null!=e[tO]&&(n[tO]=(0,p.strictParseInt32)(e[tO])),null!=e[tj]&&(n[tj]=(0,p.expectString)(e[tj])),n},e7=(e,t)=>{let n={};return null!=e[tP]&&(n[tP]=e9(e[tP],t)),null!=e[tU]&&(n[tU]=(0,p.expectString)(e[tU])),null!=e[tm]&&(n[tm]=e8(e[tm],t)),null!=e[tO]&&(n[tO]=(0,p.strictParseInt32)(e[tO])),null!=e[tk]&&(n[tk]=(0,p.expectString)(e[tk])),null!=e[tE]&&(n[tE]=(0,p.expectString)(e[tE])),null!=e[tj]&&(n[tj]=(0,p.expectString)(e[tj])),n},e9=(e,t)=>{let n={};return null!=e[tg]&&(n[tg]=(0,p.expectString)(e[tg])),null!=e[t_]&&(n[t_]=(0,p.expectString)(e[t_])),null!=e[tW]&&(n[tW]=(0,p.expectString)(e[tW])),null!=e[tC]&&(n[tC]=(0,p.expectNonNull)((0,p.parseRfc3339DateTimeWithOffset)(e[tC]))),n},te=(e,t)=>{let n={};return null!=e[tM]&&(n[tM]=(0,p.expectString)(e[tM])),n},tt=(e,t)=>{let n={};return null!=e[tM]&&(n[tM]=(0,p.expectString)(e[tM])),n},tn=(e,t)=>{let n={};return null!=e[tM]&&(n[tM]=(0,p.expectString)(e[tM])),n},ti=(e,t)=>{let n={};return null!=e[tM]&&(n[tM]=(0,p.expectString)(e[tM])),n},tr=(e,t)=>{let n={};return null!=e[tM]&&(n[tM]=(0,p.expectString)(e[tM])),n},to=(e,t)=>{let n={};return null!=e[tM]&&(n[tM]=(0,p.expectString)(e[tM])),n},ts=(e,t)=>{let n={};return null!=e[tM]&&(n[tM]=(0,p.expectString)(e[tM])),n},ta=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),tl=(0,p.withBaseException)(ew),tc=async(e,t,n,i,r)=>{let{hostname:o,protocol:s="https",port:a,path:l}=await e.endpoint(),c={protocol:s,hostname:o,port:a,method:"POST",path:l.endsWith("/")?l.slice(0,-1)+n:l+n,headers:t};return void 0!==i&&(c.hostname=i),void 0!==r&&(c.body=r),new eP.HttpRequest(c)},td={"content-type":"application/x-www-form-urlencoded"},tu="2011-06-15",tp="Action",tg="AccessKeyId",th="AssumeRole",tf="AssumedRoleId",tm="AssumedRoleUser",tS="AssumeRoleWithWebIdentity",ty="Arn",tE="Audience",tP="Credentials",tI="ContextAssertion",tx="DurationSeconds",tC="Expiration",tv="ExternalId",tb="Key",tw="Policy",tA="PolicyArns",tR="ProviderArn",tD="ProvidedContexts",tT="ProviderId",tO="PackedPolicySize",tk="Provider",t$="RoleArn",tN="RoleSessionName",t_="SecretAccessKey",tU="SubjectFromWebIdentityToken",tj="SourceIdentity",tF="SerialNumber",tW="SessionToken",tz="Tags",tK="TokenCode",tH="TransitiveTagKeys",tq="Version",tG="Value",tL="WebIdentityToken",tV="arn",tM="message",tB=e=>Object.entries(e).map(([e,t])=>(0,p.extendedEncodeURIComponent)(e)+"="+(0,p.extendedEncodeURIComponent)(t)).join("&"),tY=(e,t)=>t.Error?.Code!==void 0?t.Error.Code:404==e.statusCode?"NotFound":void 0;class tJ extends p.Command.classBuilder().ep(P).m(function(e,t,n,i){return[(0,eb.getSerdePlugin)(n,this.serialize,this.deserialize),(0,d.rD)(n,e.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,eR).ser(eW).de(eK).build(){}class tQ extends p.Command.classBuilder().ep(P).m(function(e,t,n,i){return[(0,eb.getSerdePlugin)(n,this.serialize,this.deserialize),(0,d.rD)(n,e.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(e_,eU).ser(ez).de(eH).build(){}class tX extends ev{}(0,p.createAggregatedClient)({AssumeRoleCommand:tJ,AssumeRoleWithWebIdentityCommand:tQ},tX);var tZ=n(36666);let t0="us-east-1",t1=e=>{if("string"==typeof e?.Arn){let t=e.Arn.split(":");if(t.length>4&&""!==t[4])return t[4]}},t2=async(e,t,n)=>{let i="function"==typeof e?await e():e,r="function"==typeof t?await t():t;return n?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${i} (provider)`,`${r} (parent client)`,`${t0} (STS default)`),i??r??t0},t4=(e,t)=>{let n,i;return async(r,o)=>{if(i=r,!n){let{logger:r=e?.parentClientConfig?.logger,region:o,requestHandler:s=e?.parentClientConfig?.requestHandler,credentialProviderLogger:a}=e,l=await t2(o,e?.parentClientConfig?.region,a),c=!t6(s);n=new t({profile:e?.parentClientConfig?.profile,credentialDefaultProvider:()=>async()=>i,region:l,requestHandler:c?s:void 0,logger:r})}let{Credentials:s,AssumedRoleUser:a}=await n.send(new tJ(o));if(!s||!s.AccessKeyId||!s.SecretAccessKey)throw Error(`Invalid response from STS.assumeRole call with role ${o.RoleArn}`);let l=t1(a),c={accessKeyId:s.AccessKeyId,secretAccessKey:s.SecretAccessKey,sessionToken:s.SessionToken,expiration:s.Expiration,...s.CredentialScope&&{credentialScope:s.CredentialScope},...l&&{accountId:l}};return(0,tZ.g)(c,"CREDENTIALS_STS_ASSUME_ROLE","i"),c}},t5=(e,t)=>{let n;return async i=>{if(!n){let{logger:i=e?.parentClientConfig?.logger,region:r,requestHandler:o=e?.parentClientConfig?.requestHandler,credentialProviderLogger:s}=e,a=await t2(r,e?.parentClientConfig?.region,s),l=!t6(o);n=new t({profile:e?.parentClientConfig?.profile,region:a,requestHandler:l?o:void 0,logger:i})}let{Credentials:r,AssumedRoleUser:o}=await n.send(new tQ(i));if(!r||!r.AccessKeyId||!r.SecretAccessKey)throw Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${i.RoleArn}`);let s=t1(o),a={accessKeyId:r.AccessKeyId,secretAccessKey:r.SecretAccessKey,sessionToken:r.SessionToken,expiration:r.Expiration,...r.CredentialScope&&{credentialScope:r.CredentialScope},...s&&{accountId:s}};return s&&(0,tZ.g)(a,"RESOLVED_ACCOUNT_ID","T"),(0,tZ.g)(a,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),a}},t6=e=>e?.metadata?.handlerProtocol==="h2",t8=(e,t)=>t?class extends e{constructor(e){for(let n of(super(e),t))this.middlewareStack.use(n)}}:e,t3=(e={},t)=>t4(e,t8(ev,t)),t7=(e={},t)=>t5(e,t8(ev,t))}};