try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="31ed34e8-2c70-4aa3-a40e-22cd03e90097",e._sentryDebugIdIdentifier="sentry-dbid-31ed34e8-2c70-4aa3-a40e-22cd03e90097")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1769],{14840:(e,t,n)=>{n.d(t,{Hz:()=>tK,Vv:()=>tC});var r=n(41677),o=n(8595),a=n(14360),s=n(47297),i=n(45447),l=n(4656),c=n(98073),u=n(30708),d=n(56291),f=n(99004),h=n(89295),m=n(84586),v=n.n(m),p=n(45393),g=n.n(p),y=n(56102),b=n(9419),w=n(90576),E=n(48853),D=n.n(E),A=n(54094),S=n(99746),k=n(35140),R=n(46282),_=n(30012),O=n.n(_),M=n(35550),N=n(57365),T=n(37456),x=n(86277),z=n(29195),C=n(64497),P=n.n(C),I=n(11485),L=n.n(I),F=n(60796),W=n(96603),H=n(74017),K=n.n(H),j=n(92632),V=n(26869),q=n(38686),G=n(28807),Y=n(1722),X=n.n(Y),U=n(83572),B=n.n(U),Z=n(92425),J=n.n(Z),Q=n(7356),$=n.n(Q);function ee(e){return e.children}n(85044),n(95791),n(75892),n(52414),n(56130),n(90682),n(76114),n(25735);var et={PREVIOUS:"PREV",NEXT:"NEXT",TODAY:"TODAY",DATE:"DATE"},en={MONTH:"month",WEEK:"week",WORK_WEEK:"work_week",DAY:"day",AGENDA:"agenda"},er=Object.keys(en).map(function(e){return en[e]});v().oneOfType([v().string,v().func]),v().any,v().func,v().oneOfType([v().arrayOf(v().oneOf(er)),v().objectOf(function(e,t){if(-1!==er.indexOf(t)&&"boolean"==typeof e[t])return null;for(var n=arguments.length,r=Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];return v().elementType.apply(v(),[e,t].concat(r))})]),v().oneOfType([v().oneOf(["overlap","no-overlap"]),v().func]);var eo={seconds:1e3,minutes:6e4,hours:36e5,day:864e5};function ea(e,t){var n=y.startOf(e,"month");return y.startOf(n,"week",t.startOfWeek())}function es(e,t){var n=y.endOf(e,"month");return y.endOf(n,"week",t.startOfWeek())}function ei(e,t){for(var n=ea(e,t),r=es(e,t),o=[];y.lte(n,r,"day");)o.push(n),n=y.add(n,1,"day");return o}function el(e,t){var n=y.startOf(e,t);return y.eq(n,e)?n:y.add(n,1,t)}function ec(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day",r=e,o=[];y.lte(r,t,n);)o.push(r),r=y.add(r,1,n);return o}function eu(e,t){return null==t&&null==e?null:(null==t&&(t=new Date),null==e&&(e=new Date),e=y.startOf(e,"day"),e=y.hours(e,y.hours(t)),e=y.minutes(e,y.minutes(t)),e=y.seconds(e,y.seconds(t)),y.milliseconds(e,y.milliseconds(t)))}function ed(e){return 0===y.hours(e)&&0===y.minutes(e)&&0===y.seconds(e)&&0===y.milliseconds(e)}function ef(e,t,n){return n&&"milliseconds"!==n?Math.round(Math.abs(y.startOf(e,n)/eo[n]-y.startOf(t,n)/eo[n])):Math.abs(e-t)}var eh=v().oneOfType([v().string,v().func]);function em(e,t,n,r,o){var a="function"==typeof r?r(n,o,e):t.call(e,n,r,o);return g()(null==a||"string"==typeof a,"`localizer format(..)` must return a string, null, or undefined"),a}function ev(e,t,n){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,t+n,0,0)}function ep(e,t){return e.getTimezoneOffset()-t.getTimezoneOffset()}function eg(e,t){return ef(e,t,"minutes")+ep(e,t)}function ey(e){var t=(0,y.startOf)(e,"day");return ef(t,e,"minutes")+ep(t,e)}function eb(e,t){return(0,y.lt)(e,t,"day")}function ew(e,t,n){return(0,y.eq)(e,t,"minutes")?(0,y.gte)(t,n,"minutes"):(0,y.gt)(t,n,"minutes")}function eE(e,t){var n,r;return"day"==(n="day")&&(n="date"),Math.abs(y[n](e,void 0,void 0)-y[n](t,void 0,r))}function eD(e){var t=e.evtA,n=t.start,r=t.end,o=t.allDay,a=e.evtB,s=a.start,i=a.end,l=a.allDay,c=(0,y.startOf)(n,"day")-(0,y.startOf)(s,"day"),u=eE(n,r),d=eE(s,i);return c||d-u||!!l-!!o||n-s||r-i}function eA(e){var t=e.event,n=t.start,r=t.end,o=e.range,a=o.start,s=o.end,i=(0,y.startOf)(n,"day"),l=(0,y.lte)(i,s,"day"),c=(0,y.neq)(i,r,"minutes")?(0,y.gt)(r,a,"minutes"):(0,y.gte)(r,a,"minutes");return l&&c}function eS(e,t){return(0,y.eq)(e,t,"day")}function ek(e,t){return ed(e)&&ed(t)}var eR=(0,i.A)(function e(t){var n=this;(0,s.A)(this,e),g()("function"==typeof t.format,"date localizer `format(..)` must be a function"),g()("function"==typeof t.firstOfWeek,"date localizer `firstOfWeek(..)` must be a function"),this.propType=t.propType||eh,this.formats=t.formats,this.format=function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return em.apply(void 0,[n,t.format].concat(r))},this.startOfWeek=t.firstOfWeek,this.merge=t.merge||eu,this.inRange=t.inRange||y.inRange,this.lt=t.lt||y.lt,this.lte=t.lte||y.lte,this.gt=t.gt||y.gt,this.gte=t.gte||y.gte,this.eq=t.eq||y.eq,this.neq=t.neq||y.neq,this.startOf=t.startOf||y.startOf,this.endOf=t.endOf||y.endOf,this.add=t.add||y.add,this.range=t.range||ec,this.diff=t.diff||ef,this.ceil=t.ceil||el,this.min=t.min||y.min,this.max=t.max||y.max,this.minutes=t.minutes||y.minutes,this.daySpan=t.daySpan||eE,this.firstVisibleDay=t.firstVisibleDay||ea,this.lastVisibleDay=t.lastVisibleDay||es,this.visibleDays=t.visibleDays||ei,this.getSlotDate=t.getSlotDate||ev,this.getTimezoneOffset=t.getTimezoneOffset||function(e){return e.getTimezoneOffset()},this.getDstOffset=t.getDstOffset||ep,this.getTotalMin=t.getTotalMin||eg,this.getMinutesFromMidnight=t.getMinutesFromMidnight||ey,this.continuesPrior=t.continuesPrior||eb,this.continuesAfter=t.continuesAfter||ew,this.sortEvents=t.sortEvents||eD,this.inEventRange=t.inEventRange||eA,this.isSameDate=t.isSameDate||eS,this.startAndEndAreDateOnly=t.startAndEndAreDateOnly||ek,this.segmentOffset=t.browserTZOffset?t.browserTZOffset():0}),e_=function(e){function t(){var e;(0,s.A)(this,t);for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=(0,l.A)(this,t,[].concat(r))).navigate=function(t){e.props.onNavigate(t)},e.view=function(t){e.props.onView(t)},e}return(0,c.A)(t,e),(0,i.A)(t,[{key:"render",value:function(){var e=this.props,t=e.localizer.messages,n=e.label;return f.createElement("div",{className:"rbc-toolbar"},f.createElement("span",{className:"rbc-btn-group"},f.createElement("button",{type:"button",onClick:this.navigate.bind(null,et.TODAY)},t.today),f.createElement("button",{type:"button",onClick:this.navigate.bind(null,et.PREVIOUS)},t.previous),f.createElement("button",{type:"button",onClick:this.navigate.bind(null,et.NEXT)},t.next)),f.createElement("span",{className:"rbc-toolbar-label"},n),f.createElement("span",{className:"rbc-btn-group"},this.viewNamesGroup(t)))}},{key:"viewNamesGroup",value:function(e){var t=this,n=this.props.views,r=this.props.view;if(n.length>1)return n.map(function(n){return f.createElement("button",{type:"button",key:n,className:(0,d.A)({"rbc-active":r===n}),onClick:t.view.bind(null,n)},e[n])})}}])}(f.Component);function eO(e,t){e&&e.apply(null,[].concat(t))}var eM={date:"Date",time:"Time",event:"Event",allDay:"All Day",week:"Week",work_week:"Work Week",day:"Day",month:"Month",previous:"Back",next:"Next",yesterday:"Yesterday",tomorrow:"Tomorrow",today:"Today",agenda:"Agenda",noEventsInRange:"There are no events in this range.",showMore:function(e){return"+".concat(e," more")}},eN=["style","className","event","selected","isAllDay","onSelect","onDoubleClick","onKeyPress","localizer","continuesPrior","continuesAfter","accessors","getters","children","components","slotStart","slotEnd"],eT=function(e){function t(){return(0,s.A)(this,t),(0,l.A)(this,t,arguments)}return(0,c.A)(t,e),(0,i.A)(t,[{key:"render",value:function(){var e=this.props,t=e.style,n=e.className,a=e.event,s=e.selected,i=e.isAllDay,l=e.onSelect,c=e.onDoubleClick,u=e.onKeyPress,h=e.localizer,m=e.continuesPrior,v=e.continuesAfter,p=e.accessors,g=e.getters,y=e.children,b=e.components,w=b.event,E=b.eventWrapper,D=e.slotStart,A=e.slotEnd,S=(0,o.A)(e,eN);delete S.resizable;var k=p.title(a),R=p.tooltip(a),_=p.end(a),O=p.start(a),M=p.allDay(a),N=i||M||h.diff(O,h.ceil(_,"day"),"day")>1,T=g.eventProp(a,O,_,s),x=f.createElement("div",{className:"rbc-event-content",title:R||void 0},w?f.createElement(w,{event:a,continuesPrior:m,continuesAfter:v,title:k,isAllDay:M,localizer:h,slotStart:D,slotEnd:A}):k);return f.createElement(E,Object.assign({},this.props,{type:"date"}),f.createElement("div",Object.assign({},S,{style:(0,r.A)((0,r.A)({},T.style),t),className:(0,d.A)("rbc-event",n,T.className,{"rbc-selected":s,"rbc-event-allday":N,"rbc-event-continues-prior":m,"rbc-event-continues-after":v}),onClick:function(e){return l&&l(a,e)},onDoubleClick:function(e){return c&&c(a,e)},onKeyDown:function(e){return u&&u(a,e)}}),"function"==typeof y?y(x):x))}}])}(f.Component);function ex(e,t){return!!e&&null!=t&&O()(e,t)}function ez(e,t){return(e.right-e.left)/t}function eC(e,t,n,r){var o=ez(e,r);return n?r-1-Math.floor((t-e.left)/o):Math.floor((t-e.left)/o)}function eP(e){var t,n,r,o=e.containerRef,a=e.accessors,s=e.getters,i=e.selected,l=e.components,c=e.localizer,u=e.position,d=e.show,h=e.events,m=e.slotStart,v=e.slotEnd,p=e.onSelect,g=e.onDoubleClick,y=e.onKeyPress,b=e.handleDragStart,w=e.popperRef,E=e.target,D=e.offset;n=(t={ref:w,callback:d}).ref,r=t.callback,(0,f.useEffect)(function(){var e=function(e){n.current&&!n.current.contains(e.target)&&r()};return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}},[n,r]),(0,f.useLayoutEffect)(function(){var e,t,n,r,a,s,i,l,c,u,d,f,h,m,v,p,g,y,b,A,S=(t=(e={target:E,offset:D,container:o.current,box:w.current}).target,n=e.offset,r=e.container,a=e.box,i=(s=(0,R.A)(t)).top,l=s.left,c=s.width,u=s.height,f=(d=(0,R.A)(r)).top,h=d.left,m=d.width,v=d.height,g=(p=(0,R.A)(a)).width,y=p.height,b=n.x,A=n.y,{topOffset:i+y>f+v?i-y-A:i+A+u,leftOffset:l+g>h+m?l+b-g+c:l+b}),k=S.topOffset,_=S.leftOffset;w.current.style.top="".concat(k,"px"),w.current.style.left="".concat(_,"px")},[D.x,D.y,E]);var A=u.width;return f.createElement("div",{style:{minWidth:A+A/2},className:"rbc-overlay",ref:w},f.createElement("div",{className:"rbc-overlay-header"},c.format(m,"dayHeaderFormat")),h.map(function(e,t){return f.createElement(eT,{key:t,type:"popup",localizer:c,event:e,getters:s,onSelect:p,accessors:a,components:l,onDoubleClick:g,onKeyPress:y,continuesPrior:c.lt(a.end(e),m,"day"),continuesAfter:c.gte(a.start(e),v,"day"),slotStart:m,slotEnd:v,selected:ex(e,i),draggable:!0,onDragStart:function(){return b(e)},onDragEnd:function(){return d()}})}))}var eI=f.forwardRef(function(e,t){return f.createElement(eP,Object.assign({},e,{popperRef:t}))});function eL(e){var t=e.containerRef,n=e.popupOffset,r=void 0===n?5:n,o=e.overlay,a=e.accessors,s=e.localizer,i=e.components,l=e.getters,c=e.selected,u=e.handleSelectEvent,d=e.handleDoubleClickEvent,h=e.handleKeyPressEvent,m=e.handleDragStart,v=e.onHide,p=e.overlayDisplay,g=(0,f.useRef)(null);if(!o.position)return null;var y=r;isNaN(r)||(y={x:r,y:r});var b=o.position,w=o.events,E=o.date,D=o.end;return f.createElement(k.A,{rootClose:!0,flip:!0,show:!0,placement:"bottom",onHide:v,target:o.target},function(e){var n=e.props;return f.createElement(eI,Object.assign({},n,{containerRef:t,ref:g,target:o.target,offset:y,accessors:a,getters:l,selected:c,components:i,localizer:s,position:b,show:p,events:w,slotStart:E,slotEnd:D,onSelect:u,onDoubleClick:d,onKeyPress:h,handleDragStart:m}))})}eI.propTypes={accessors:v().object.isRequired,getters:v().object.isRequired,selected:v().object,components:v().object.isRequired,localizer:v().object.isRequired,position:v().object.isRequired,show:v().func.isRequired,events:v().array.isRequired,slotStart:v().instanceOf(Date).isRequired,slotEnd:v().instanceOf(Date),onSelect:v().func,onDoubleClick:v().func,onKeyPress:v().func,handleDragStart:v().func,style:v().object,offset:v().shape({x:v().number,y:v().number})};var eF=f.forwardRef(function(e,t){return f.createElement(eL,Object.assign({},e,{containerRef:t}))});function eW(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:document;return(0,z.A)(n,e,t,{passive:!1})}function eH(e,t){var n,r,o;return n=t.clientX,r=t.clientY,o=document.elementFromPoint(n,r),!!(0,x.A)(o,".rbc-event",e)}function eK(e){var t=e;return e.touches&&e.touches.length&&(t=e.touches[0]),{clientX:t.clientX,clientY:t.clientY,pageX:t.pageX,pageY:t.pageY}}eF.propTypes={popupOffset:v().oneOfType([v().number,v().shape({x:v().number,y:v().number})]),overlay:v().shape({position:v().object,events:v().array,date:v().instanceOf(Date),end:v().instanceOf(Date)}),accessors:v().object.isRequired,localizer:v().object.isRequired,components:v().object.isRequired,getters:v().object.isRequired,selected:v().object,handleSelectEvent:v().func,handleDoubleClickEvent:v().func,handleKeyPressEvent:v().func,handleDragStart:v().func,onHide:v().func,overlayDisplay:v().func};var ej=(0,i.A)(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.global,o=n.longPressThreshold,a=n.validContainers;(0,s.A)(this,e),this._initialEvent=null,this.selecting=!1,this.isDetached=!1,this.container=t,this.globalMouse=!t||void 0!==r&&r,this.longPressThreshold=void 0===o?250:o,this.validContainers=void 0===a?[]:a,this._listeners=Object.create(null),this._handleInitialEvent=this._handleInitialEvent.bind(this),this._handleMoveEvent=this._handleMoveEvent.bind(this),this._handleTerminatingEvent=this._handleTerminatingEvent.bind(this),this._keyListener=this._keyListener.bind(this),this._dropFromOutsideListener=this._dropFromOutsideListener.bind(this),this._dragOverFromOutsideListener=this._dragOverFromOutsideListener.bind(this),this._removeTouchMoveWindowListener=eW("touchmove",function(){},window),this._removeKeyDownListener=eW("keydown",this._keyListener),this._removeKeyUpListener=eW("keyup",this._keyListener),this._removeDropFromOutsideListener=eW("drop",this._dropFromOutsideListener),this._removeDragOverFromOutsideListener=eW("dragover",this._dragOverFromOutsideListener),this._addInitialEventListener()},[{key:"on",value:function(e,t){var n=this._listeners[e]||(this._listeners[e]=[]);return n.push(t),{remove:function(){var e=n.indexOf(t);-1!==e&&n.splice(e,1)}}}},{key:"emit",value:function(e){for(var t,n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return(this._listeners[e]||[]).forEach(function(e){void 0===t&&(t=e.apply(void 0,r))}),t}},{key:"teardown",value:function(){this._initialEvent=null,this._initialEventData=null,this._selectRect=null,this.selecting=!1,this._lastClickData=null,this.isDetached=!0,this._listeners=Object.create(null),this._removeTouchMoveWindowListener&&this._removeTouchMoveWindowListener(),this._removeInitialEventListener&&this._removeInitialEventListener(),this._removeEndListener&&this._removeEndListener(),this._onEscListener&&this._onEscListener(),this._removeMoveListener&&this._removeMoveListener(),this._removeKeyUpListener&&this._removeKeyUpListener(),this._removeKeyDownListener&&this._removeKeyDownListener(),this._removeDropFromOutsideListener&&this._removeDropFromOutsideListener(),this._removeDragOverFromOutsideListener&&this._removeDragOverFromOutsideListener()}},{key:"isSelected",value:function(e){var t=this._selectRect;return!!t&&!!this.selecting&&eV(t,eq(e))}},{key:"filter",value:function(e){return this._selectRect&&this.selecting?e.filter(this.isSelected,this):[]}},{key:"_addLongPressListener",value:function(e,t){var n=this,r=null,o=null,a=null,s=function(t){r=setTimeout(function(){l(),e(t)},n.longPressThreshold),o=eW("touchmove",function(){return l()}),a=eW("touchend",function(){return l()})},i=eW("touchstart",s),l=function(){r&&clearTimeout(r),o&&o(),a&&a(),r=null,o=null,a=null};return t&&s(t),function(){l(),i()}}},{key:"_addInitialEventListener",value:function(){var e=this,t=eW("mousedown",function(t){e._removeInitialEventListener(),e._handleInitialEvent(t),e._removeInitialEventListener=eW("mousedown",e._handleInitialEvent)}),n=eW("touchstart",function(t){e._removeInitialEventListener(),e._removeInitialEventListener=e._addLongPressListener(e._handleInitialEvent,t)});this._removeInitialEventListener=function(){t(),n()}}},{key:"_dropFromOutsideListener",value:function(e){var t=eK(e),n=t.pageX,r=t.pageY,o=t.clientX,a=t.clientY;this.emit("dropFromOutside",{x:n,y:r,clientX:o,clientY:a}),e.preventDefault()}},{key:"_dragOverFromOutsideListener",value:function(e){var t=eK(e),n=t.pageX,r=t.pageY,o=t.clientX,a=t.clientY;this.emit("dragOverFromOutside",{x:n,y:r,clientX:o,clientY:a}),e.preventDefault()}},{key:"_handleInitialEvent",value:function(e){if(this._initialEvent=e,!this.isDetached){var t,n=eK(e),r=n.clientX,o=n.clientY,s=n.pageX,i=n.pageY,l=this.container();if(3!==e.which&&2!==e.button&&(!l||(0,T.A)(l,document.elementFromPoint(r,o)))){if(!this.globalMouse&&l&&!(0,T.A)(l,e.target)){var c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return"object"!==(0,a.A)(e)&&(e={top:e,left:e,right:e,bottom:e}),e}(0),u=c.top,d=c.left,f=c.bottom,h=c.right;if(!eV({top:(t=eq(l)).top-u,left:t.left-d,bottom:t.bottom+f,right:t.right+h},{top:i,left:s}))return}if(!1!==this.emit("beforeSelect",this._initialEventData={isTouch:/^touch/.test(e.type),x:s,y:i,clientX:r,clientY:o}))switch(e.type){case"mousedown":this._removeEndListener=eW("mouseup",this._handleTerminatingEvent),this._onEscListener=eW("keydown",this._handleTerminatingEvent),this._removeMoveListener=eW("mousemove",this._handleMoveEvent);break;case"touchstart":this._handleMoveEvent(e),this._removeEndListener=eW("touchend",this._handleTerminatingEvent),this._removeMoveListener=eW("touchmove",this._handleMoveEvent)}}}}},{key:"_isWithinValidContainer",value:function(e){var t=e.target,n=this.validContainers;return!n||!n.length||!t||n.some(function(e){return!!t.closest(e)})}},{key:"_handleTerminatingEvent",value:function(e){var t=this.selecting,n=this._selectRect;if(!t&&e.type.includes("key")&&(e=this._initialEvent),this.selecting=!1,this._removeEndListener&&this._removeEndListener(),this._removeMoveListener&&this._removeMoveListener(),this._selectRect=null,this._initialEvent=null,this._initialEventData=null,e){var r=!this.container||(0,T.A)(this.container(),e.target),o=this._isWithinValidContainer(e);return"Escape"!==e.key&&o?!t&&r?this._handleClickEvent(e):t?this.emit("select",n):this.emit("reset"):this.emit("reset")}}},{key:"_handleClickEvent",value:function(e){var t=eK(e),n=t.pageX,r=t.pageY,o=t.clientX,a=t.clientY,s=new Date().getTime();return this._lastClickData&&s-this._lastClickData.timestamp<250?(this._lastClickData=null,this.emit("doubleClick",{x:n,y:r,clientX:o,clientY:a})):(this._lastClickData={timestamp:s},this.emit("click",{x:n,y:r,clientX:o,clientY:a}))}},{key:"_handleMoveEvent",value:function(e){if(null!==this._initialEventData&&!this.isDetached){var t=this._initialEventData,n=t.x,r=t.y,o=eK(e),a=o.pageX,s=o.pageY,i=Math.abs(n-a),l=Math.abs(r-s),c=Math.min(a,n),u=Math.min(s,r),d=this.selecting,f=this.isClick(a,s);(!f||d||i||l)&&(d||f||this.emit("selectStart",this._initialEventData),f||(this.selecting=!0,this._selectRect={top:u,left:c,x:a,y:s,right:c+i,bottom:u+l},this.emit("selecting",this._selectRect)),e.preventDefault())}}},{key:"_keyListener",value:function(e){this.ctrl=e.metaKey||e.ctrlKey}},{key:"isClick",value:function(e,t){var n=this._initialEventData,r=n.x,o=n.y;return!n.isTouch&&5>=Math.abs(e-r)&&5>=Math.abs(t-o)}}]);function eV(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=eq(e),o=r.top,a=r.left,s=r.right,i=r.bottom,l=eq(t),c=l.top,u=l.left,d=l.right,f=l.bottom;return!((void 0===i?o:i)-n<c||o+n>(void 0===f?c:f)||(void 0===s?a:s)-n<u||a+n>(void 0===d?u:d))}function eq(e){if(!e.getBoundingClientRect)return e;var t=e.getBoundingClientRect(),n=t.left+eG("left"),r=t.top+eG("top");return{top:r,left:n,right:(e.offsetWidth||0)+n,bottom:(e.offsetHeight||0)+r}}function eG(e){return"left"===e?window.pageXOffset||document.body.scrollLeft||0:"top"===e?window.pageYOffset||document.body.scrollTop||0:void 0}var eY=function(e){function t(e,n){var r;return(0,s.A)(this,t),(r=(0,l.A)(this,t,[e,n])).state={selecting:!1},r.containerRef=(0,f.createRef)(),r}return(0,c.A)(t,e),(0,i.A)(t,[{key:"componentDidMount",value:function(){this.props.selectable&&this._selectable()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable()}},{key:"componentDidUpdate",value:function(e){!e.selectable&&this.props.selectable&&this._selectable(),e.selectable&&!this.props.selectable&&this._teardownSelectable()}},{key:"render",value:function(){var e=this.props,t=e.range,n=e.getNow,r=e.getters,o=e.date,a=e.components.dateCellWrapper,s=e.localizer,i=this.state,l=i.selecting,c=i.startIdx,u=i.endIdx,h=n();return f.createElement("div",{className:"rbc-row-bg",ref:this.containerRef},t.map(function(e,n){var i=r.dayProp(e),m=i.className,v=i.style;return f.createElement(a,{key:n,value:e,range:t},f.createElement("div",{style:v,className:(0,d.A)("rbc-day-bg",m,l&&n>=c&&n<=u&&"rbc-selected-cell",s.isSameDate(e,h)&&"rbc-today",o&&s.neq(o,e,"month")&&"rbc-off-range-bg")}))}))}},{key:"_selectable",value:function(){var e=this,t=this.containerRef.current,n=this._selector=new ej(this.props.container,{longPressThreshold:this.props.longPressThreshold}),o=function(n,r){if(!eH(t,n)&&(o=n.clientX,a=n.clientY,s=document.elementFromPoint(o,a),!(0,x.A)(s,".rbc-show-more",t))){var o,a,s,i,l,c=eq(t),u=e.props,d=u.range,f=u.rtl;if(i=n.x,(l=n.y)>=c.top&&l<=c.bottom&&i>=c.left&&i<=c.right){var h=eC(c,n.x,f,d.length);e._selectSlot({startIdx:h,endIdx:h,action:r,box:n})}}e._initial={},e.setState({selecting:!1})};n.on("selecting",function(r){var o=e.props,a=o.range,s=o.rtl,i=-1,l=-1;if(e.state.selecting||(eO(e.props.onSelectStart,[r]),e._initial={x:r.x,y:r.y}),n.isSelected(t)){var c,u,d,f,h,m,v,p,g,y,b,w=eq(t),E=(c=e._initial,u=a.length,d=-1,f=-1,h=u-1,m=ez(w,u),v=eC(w,r.x,s,u),p=w.top<r.y&&w.bottom>r.y,g=w.top<c.y&&w.bottom>c.y,y=c.y>w.bottom,b=w.top>c.y,r.top<w.top&&r.bottom>w.bottom&&(d=0,f=h),p&&(b?(d=0,f=v):y&&(d=v,f=h)),g&&(d=f=s?h-Math.floor((c.x-w.left)/m):Math.floor((c.x-w.left)/m),p?v<d?d=v:f=v:c.y<r.y?f=h:d=0),{startIdx:d,endIdx:f});i=E.startIdx,l=E.endIdx}e.setState({selecting:!0,startIdx:i,endIdx:l})}),n.on("beforeSelect",function(t){if("ignoreEvents"===e.props.selectable)return!eH(e.containerRef.current,t)}),n.on("click",function(e){return o(e,"click")}),n.on("doubleClick",function(e){return o(e,"doubleClick")}),n.on("select",function(t){e._selectSlot((0,r.A)((0,r.A)({},e.state),{},{action:"select",bounds:t})),e._initial={},e.setState({selecting:!1}),eO(e.props.onSelectEnd,[e.state])})}},{key:"_teardownSelectable",value:function(){this._selector&&(this._selector.teardown(),this._selector=null)}},{key:"_selectSlot",value:function(e){var t=e.endIdx,n=e.startIdx,r=e.action,o=e.bounds,a=e.box;-1!==t&&-1!==n&&this.props.onSelectSlot&&this.props.onSelectSlot({start:n,end:t,action:r,bounds:o,box:a,resourceId:this.props.resourceId})}}])}(f.Component),eX={propTypes:{slotMetrics:v().object.isRequired,selected:v().object,isAllDay:v().bool,accessors:v().object.isRequired,localizer:v().object.isRequired,components:v().object.isRequired,getters:v().object.isRequired,onSelect:v().func,onDoubleClick:v().func,onKeyPress:v().func},defaultProps:{segments:[],selected:{}},renderEvent:function(e,t){var n=e.selected;e.isAllDay;var r=e.accessors,o=e.getters,a=e.onSelect,s=e.onDoubleClick,i=e.onKeyPress,l=e.localizer,c=e.slotMetrics,u=e.components,d=e.resizable,h=c.continuesPrior(t),m=c.continuesAfter(t);return f.createElement(eT,{event:t,getters:o,localizer:l,accessors:r,components:u,onSelect:a,onDoubleClick:s,onKeyPress:i,continuesPrior:h,continuesAfter:m,slotStart:c.first,slotEnd:c.last,selected:ex(t,n),resizable:d})},renderSpan:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:" ",o=Math.abs(t)/e*100+"%";return f.createElement("div",{key:n,className:"rbc-row-segment",style:{WebkitFlexBasis:o,flexBasis:o,maxWidth:o}},r)}},eU=function(e){function t(){return(0,s.A)(this,t),(0,l.A)(this,t,arguments)}return(0,c.A)(t,e),(0,i.A)(t,[{key:"render",value:function(){var e=this,t=this.props,n=t.segments,r=t.slotMetrics.slots,o=t.className,a=1;return f.createElement("div",{className:(0,d.A)(o,"rbc-row")},n.reduce(function(t,n,o){var s=n.event,i=n.left,l=n.right,c=n.span,u="_lvl_"+o,d=i-a,f=eX.renderEvent(e.props,s);return d&&t.push(eX.renderSpan(r,d,"".concat(u,"_gap"))),t.push(eX.renderSpan(r,c,u,f)),a=l+1,t},[]))}}])}(f.Component);function eB(e){var t=e.dateRange,n=e.unit,r=e.localizer;return{first:t[0],last:r.add(t[t.length-1],1,void 0===n?"day":n)}}function eZ(e){var t,n,r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1/0,a=[],s=[];for(t=0;t<e.length;t++){for(n=0,r=e[t];n<a.length&&function(e,t){return t.some(function(t){return t.left<=e.right&&t.right>=e.left})}(r,a[n]);n++);n>=o?s.push(r):(a[n]||(a[n]=[])).push(r)}for(t=0;t<a.length;t++)a[t].sort(function(e,t){return e.left-t.left});return{levels:a,extra:s}}function eJ(e,t,n,r,o){var a={start:r.start(e),end:r.end(e)};return o.inEventRange({event:a,range:{start:t,end:n}})}function eQ(e,t,n,r){var o={start:n.start(e),end:n.end(e),allDay:n.allDay(e)},a={start:n.start(t),end:n.end(t),allDay:n.allDay(t)};return r.sortEvents({evtA:o,evtB:a})}eU.defaultProps=(0,r.A)({},eX.defaultProps);var e$=function(e,t){return e.left<=t&&e.right>=t},e0=function(e,t){return e.filter(function(e){return e$(e,t)}).map(function(e){return e.event})},e1=function(e){function t(){return(0,s.A)(this,t),(0,l.A)(this,t,arguments)}return(0,c.A)(t,e),(0,i.A)(t,[{key:"render",value:function(){for(var e=this.props,t=e.segments,n=e.slotMetrics.slots,r=eZ(t).levels[0],o=1,a=1,s=[];o<=n;){var i="_lvl_"+o,l=r.filter(function(e){return e$(e,o)})[0]||{},c=l.event,u=l.left,d=l.right,h=l.span;if(!c){if(this.getHiddenEventsForSlot(t,o).length>0){var m=o-a;m&&s.push(eX.renderSpan(n,m,i+"_gap")),s.push(eX.renderSpan(n,1,i,this.renderShowMore(t,o))),a=o+=1;continue}o++;continue}var v=Math.max(0,u-a);if(this.canRenderSlotEvent(u,h)){var p=eX.renderEvent(this.props,c);v&&s.push(eX.renderSpan(n,v,i+"_gap")),s.push(eX.renderSpan(n,h,i,p)),a=o=d+1}else v&&s.push(eX.renderSpan(n,v,i+"_gap")),s.push(eX.renderSpan(n,1,i,this.renderShowMore(t,o))),a=o+=1}return f.createElement("div",{className:"rbc-row"},s)}},{key:"getHiddenEventsForSlot",value:function(e,t){var n=e0(e,t),r=eZ(e).levels[0].filter(function(e){return e$(e,t)}).map(function(e){return e.event});return n.filter(function(e){return!r.some(function(t){return t===e})})}},{key:"canRenderSlotEvent",value:function(e,t){var n=this.props.segments;return L()(e,e+t).every(function(e){return 1===e0(n,e).length})}},{key:"renderShowMore",value:function(e,t){var n=this,r=this.props,o=r.localizer,a=r.slotMetrics,s=r.components,i=a.getEventsForSlot(t),l=e0(e,t),c=l.length;if(null!=s&&s.showMore){var u=s.showMore,h=a.getDateForSlot(t-1);return!!c&&f.createElement(u,{localizer:o,slotDate:h,slot:t,count:c,events:i,remainingEvents:l})}return!!c&&f.createElement("button",{type:"button",key:"sm_"+t,className:(0,d.A)("rbc-button-link","rbc-show-more"),onClick:function(e){return n.showMore(t,e)}},o.messages.showMore(c,l,i))}},{key:"showMore",value:function(e,t){t.preventDefault(),t.stopPropagation(),this.props.onShowMore(e,t.target)}}])}(f.Component);e1.defaultProps=(0,r.A)({},eX.defaultProps);var e2=function(e){var t=e.children;return f.createElement("div",{className:"rbc-row-content-scroll-container"},t)},e3=function(e,t){return e[0].range===t[0].range&&e[0].events===t[0].events},e5=function(e){function t(){var e;(0,s.A)(this,t);for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return(e=(0,l.A)(this,t,[].concat(o))).handleSelectSlot=function(t){var n=e.props,r=n.range;(0,n.onSelectSlot)(r.slice(t.start,t.end+1),t)},e.handleShowMore=function(t,n){var r,o=e.props,a=o.range,s=o.onShowMore,i=e.slotMetrics(e.props),l=(0,N.A)(e.containerRef.current,".rbc-row-bg")[0];l&&(r=l.children[t-1]),s(i.getEventsForSlot(t),a[t-1],r,t,n)},e.getContainer=function(){var t=e.props.container;return t?t():e.containerRef.current},e.renderHeadingCell=function(t,n){var r=e.props,o=r.renderHeader,a=r.getNow,s=r.localizer;return o({date:t,key:"header_".concat(n),className:(0,d.A)("rbc-date-cell",s.isSameDate(t,a())&&"rbc-now")})},e.renderDummy=function(){var t=e.props,n=t.className,r=t.range,o=t.renderHeader,a=t.showAllEvents;return f.createElement("div",{className:n,ref:e.containerRef},f.createElement("div",{className:(0,d.A)("rbc-row-content",a&&"rbc-row-content-scrollable")},o&&f.createElement("div",{className:"rbc-row",ref:e.headingRowRef},r.map(e.renderHeadingCell)),f.createElement("div",{className:"rbc-row",ref:e.eventRowRef},f.createElement("div",{className:"rbc-row-segment"},f.createElement("div",{className:"rbc-event"},f.createElement("div",{className:"rbc-event-content"},"\xa0"))))))},e.containerRef=(0,f.createRef)(),e.headingRowRef=(0,f.createRef)(),e.eventRowRef=(0,f.createRef)(),e.slotMetrics=function e(){return(0,F.A)(function(t){for(var n=t.range,o=t.events,a=t.maxRows,s=t.minRows,i=t.accessors,l=t.localizer,c=eB({dateRange:n,localizer:l}),u=c.first,d=c.last,f=o.map(function(e){var t,r,o,a,s,c,u,d;return r=(t=eB({dateRange:n,localizer:l})).first,o=t.last,a=l.diff(r,o,"day"),s=l.max(l.startOf(i.start(e),"day"),r),c=l.min(l.ceil(i.end(e),"day"),o),u=P()(n,function(e){return l.isSameDate(e,s)}),{event:e,span:d=Math.max((d=Math.min(d=l.diff(s,c,"day"),a))-l.segmentOffset,1),left:u+1,right:Math.max(u+d,1)}}),h=eZ(f,Math.max(a-1,1)),m=h.levels,v=h.extra,p=v.length>0?s-1:s;m.length<p;)m.push([]);return{first:u,last:d,levels:m,extra:v,range:n,slots:n.length,clone:function(n){return e()((0,r.A)((0,r.A)({},t),n))},getDateForSlot:function(e){return n[e]},getSlotForDate:function(e){return n.find(function(t){return l.isSameDate(t,e)})},getEventsForSlot:function(e){return f.filter(function(t){return t.left<=e&&t.right>=e}).map(function(e){return e.event})},continuesPrior:function(e){return l.continuesPrior(i.start(e),u)},continuesAfter:function(e){var t=i.start(e),n=i.end(e);return l.continuesAfter(t,n,d)}}},e3)}(),e}return(0,c.A)(t,e),(0,i.A)(t,[{key:"getRowLimit",value:function(){var e,t=(0,M.A)(this.eventRowRef.current),n=null!=(e=this.headingRowRef)&&e.current?(0,M.A)(this.headingRowRef.current):0;return Math.max(Math.floor(((0,M.A)(this.containerRef.current)-n)/t),1)}},{key:"render",value:function(){var e=this.props,t=e.date,n=e.rtl,r=e.range,o=e.className,a=e.selected,s=e.selectable,i=e.renderForMeasure,l=e.accessors,c=e.getters,u=e.components,h=e.getNow,m=e.renderHeader,v=e.onSelect,p=e.localizer,g=e.onSelectStart,y=e.onSelectEnd,b=e.onDoubleClick,w=e.onKeyPress,E=e.resourceId,D=e.longPressThreshold,A=e.isAllDay,S=e.resizable,k=e.showAllEvents;if(i)return this.renderDummy();var R=this.slotMetrics(this.props),_=R.levels,O=R.extra,M=k?e2:ee,N=u.weekWrapper,T={selected:a,accessors:l,getters:c,localizer:p,components:u,onSelect:v,onDoubleClick:b,onKeyPress:w,resourceId:E,slotMetrics:R,resizable:S};return f.createElement("div",{className:o,role:"rowgroup",ref:this.containerRef},f.createElement(eY,{localizer:p,date:t,getNow:h,rtl:n,range:r,selectable:s,container:this.getContainer,getters:c,onSelectStart:g,onSelectEnd:y,onSelectSlot:this.handleSelectSlot,components:u,longPressThreshold:D,resourceId:E}),f.createElement("div",{className:(0,d.A)("rbc-row-content",k&&"rbc-row-content-scrollable"),role:"row"},m&&f.createElement("div",{className:"rbc-row ",ref:this.headingRowRef},r.map(this.renderHeadingCell)),f.createElement(M,null,f.createElement(N,Object.assign({isAllDay:A},T,{rtl:this.props.rtl}),_.map(function(e,t){return f.createElement(eU,Object.assign({key:t,segments:e},T))}),!!O.length&&f.createElement(e1,Object.assign({segments:O,onShowMore:this.handleShowMore},T))))))}}])}(f.Component);e5.defaultProps={minRows:0,maxRows:1/0};var e4=function(e){var t=e.label;return f.createElement("span",{role:"columnheader","aria-sort":"none"},t)},e6=function(e){var t=e.label,n=e.drilldownView,r=e.onDrillDown;return n?f.createElement("button",{type:"button",className:"rbc-button-link",onClick:r},t):f.createElement("span",null,t)},e7=["date","className"],e9=function(e){function t(){var e;(0,s.A)(this,t);for(var n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return(e=(0,l.A)(this,t,[].concat(r))).getContainer=function(){return e.containerRef.current},e.renderWeek=function(t,n){var r,o,a,s,i,l,c,u,d,h=e.props,m=h.events,v=h.components,p=h.selectable,g=h.getNow,y=h.selected,b=h.date,E=h.localizer,D=h.longPressThreshold,A=h.accessors,S=h.getters,k=h.showAllEvents,R=e.state,_=R.needLimitMeasure,O=R.rowLimit,M=(r=(0,w.A)(m),o=t[0],a=t[t.length-1],s=r.filter(function(e){return eJ(e,o,a,A,E)}),i=(0,w.A)(s),l=[],c=[],i.forEach(function(e){var t=A.start(e),n=A.end(e);E.daySpan(t,n)>1?l.push(e):c.push(e)}),u=l.sort(function(e,t){return eQ(e,t,A,E)}),d=c.sort(function(e,t){return eQ(e,t,A,E)}),[].concat((0,w.A)(u),(0,w.A)(d)));return f.createElement(e5,{key:n,ref:0===n?e.slotRowRef:void 0,container:e.getContainer,className:"rbc-month-row",getNow:g,date:b,range:t,events:M,maxRows:k?1/0:O,selected:y,selectable:p,components:v,accessors:A,getters:S,localizer:E,renderHeader:e.readerDateHeading,renderForMeasure:_,onShowMore:e.handleShowMore,onSelect:e.handleSelectEvent,onDoubleClick:e.handleDoubleClickEvent,onKeyPress:e.handleKeyPressEvent,onSelectSlot:e.handleSelectSlot,longPressThreshold:D,rtl:e.props.rtl,resizable:e.props.resizable,showAllEvents:k})},e.readerDateHeading=function(t){var n=t.date,r=t.className,a=(0,o.A)(t,e7),s=e.props,i=s.date,l=s.getDrilldownView,c=s.localizer,u=c.neq(i,n,"month"),h=c.isSameDate(n,i),m=l(n),v=c.format(n,"dateFormat"),p=e.props.components.dateHeader||e6;return f.createElement("div",Object.assign({},a,{className:(0,d.A)(r,u&&"rbc-off-range",h&&"rbc-current"),role:"cell"}),f.createElement(p,{label:v,date:n,drilldownView:m,isOffRange:u,onDrillDown:function(t){return e.handleHeadingClick(n,m,t)}}))},e.handleSelectSlot=function(t,n){e._pendingSelection=e._pendingSelection.concat(t),clearTimeout(e._selectTimer),e._selectTimer=setTimeout(function(){return e.selectDates(n)})},e.handleHeadingClick=function(t,n,r){r.preventDefault(),e.clearSelection(),eO(e.props.onDrillDown,[t,n])},e.handleSelectEvent=function(){e.clearSelection();for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];eO(e.props.onSelectEvent,n)},e.handleDoubleClickEvent=function(){e.clearSelection();for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];eO(e.props.onDoubleClickEvent,n)},e.handleKeyPressEvent=function(){e.clearSelection();for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];eO(e.props.onKeyPressEvent,n)},e.handleShowMore=function(t,n,r,o,a){var s=e.props,i=s.popup,l=s.onDrillDown,c=s.onShowMore,u=s.getDrilldownView,d=s.doShowMoreDrillDown;if(e.clearSelection(),i){var f=(0,A.A)(r,e.containerRef.current);e.setState({overlay:{date:n,events:t,position:f,target:a}})}else d&&eO(l,[n,u(n)||en.DAY]);eO(c,[t,n,o])},e.overlayDisplay=function(){e.setState({overlay:null})},e.state={rowLimit:5,needLimitMeasure:!0,date:null},e.containerRef=(0,f.createRef)(),e.slotRowRef=(0,f.createRef)(),e._bgRows=[],e._pendingSelection=[],e}return(0,c.A)(t,e),(0,i.A)(t,[{key:"componentDidMount",value:function(){var e,t=this;this.state.needLimitMeasure&&this.measureRowLimit(this.props),window.addEventListener("resize",this._resizeListener=function(){e||S.E(function(){e=!1,t.setState({needLimitMeasure:!0})})},!1)}},{key:"componentDidUpdate",value:function(){this.state.needLimitMeasure&&this.measureRowLimit(this.props)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this._resizeListener,!1)}},{key:"render",value:function(){var e=this.props,t=e.date,n=e.localizer,r=e.className,o=n.visibleDays(t,n),a=D()(o,7);return this._weekCount=a.length,f.createElement("div",{className:(0,d.A)("rbc-month-view",r),role:"table","aria-label":"Month View",ref:this.containerRef},f.createElement("div",{className:"rbc-row rbc-month-header",role:"row"},this.renderHeaders(a[0])),a.map(this.renderWeek),this.props.popup&&this.renderOverlay())}},{key:"renderHeaders",value:function(e){var t=this.props,n=t.localizer,r=t.components,o=e[0],a=e[e.length-1],s=r.header||e4;return n.range(o,a,"day").map(function(e,t){return f.createElement("div",{key:"header_"+t,className:"rbc-header"},f.createElement(s,{date:e,localizer:n,label:n.format(e,"weekdayFormat")}))})}},{key:"renderOverlay",value:function(){var e,t,n=this,r=null!=(e=null==(t=this.state)?void 0:t.overlay)?e:{},o=this.props,a=o.accessors,s=o.localizer,i=o.components,l=o.getters,c=o.selected,u=o.popupOffset,d=o.handleDragStart;return f.createElement(eF,{overlay:r,accessors:a,localizer:s,components:i,getters:l,selected:c,popupOffset:u,ref:this.containerRef,handleKeyPressEvent:this.handleKeyPressEvent,handleSelectEvent:this.handleSelectEvent,handleDoubleClickEvent:this.handleDoubleClickEvent,handleDragStart:d,show:!!r.position,overlayDisplay:this.overlayDisplay,onHide:function(){return n.setState({overlay:null})}})}},{key:"measureRowLimit",value:function(){this.setState({needLimitMeasure:!1,rowLimit:this.slotRowRef.current.getRowLimit()})}},{key:"selectDates",value:function(e){var t=this._pendingSelection.slice();this._pendingSelection=[],t.sort(function(e,t){return e-t});var n=new Date(t[0]),r=new Date(t[t.length-1]);r.setDate(t[t.length-1].getDate()+1),eO(this.props.onSelectSlot,{slots:t,start:n,end:r,action:e.action,bounds:e.bounds,box:e.box})}},{key:"clearSelection",value:function(){clearTimeout(this._selectTimer),this._pendingSelection=[]}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.date;return{date:n,needLimitMeasure:e.localizer.neq(n,t.date,"month")}}}])}(f.Component);e9.range=function(e,t){var n=t.localizer;return{start:n.firstVisibleDay(e,n),end:n.lastVisibleDay(e,n)}},e9.navigate=function(e,t,n){var r=n.localizer;switch(t){case et.PREVIOUS:return r.add(e,-1,"month");case et.NEXT:return r.add(e,1,"month");default:return e}},e9.title=function(e,t){return t.localizer.format(e,"monthHeaderFormat")};var e8=function(e){var t=e.min,n=e.max,r=e.step,o=e.slots,a=e.localizer;return"".concat(+a.startOf(t,"minutes"))+"".concat(+a.startOf(n,"minutes"))+"".concat(r,"-").concat(o)};function te(e){for(var t=e.min,n=e.max,r=e.step,o=e.timeslots,a=e.localizer,s=e8({start:t,end:n,step:r,timeslots:o,localizer:a}),i=1+a.getTotalMin(t,n),l=a.getMinutesFromMidnight(t),c=Math.ceil((i-1)/(r*o)),u=c*o,d=Array(c),f=Array(u),h=0;h<c;h++){d[h]=Array(o);for(var m=0;m<o;m++){var v=h*o+m,p=v*r;f[v]=d[h][m]=a.getSlotDate(t,l,p)}}var g=f.length*r;function y(e){return Math.min(a.diff(t,e,"minutes")+a.getDstOffset(t,e),i)}return f.push(a.getSlotDate(t,l,g)),{groups:d,update:function(e){return e8(e)!==s?te(e):this},dateIsInGroup:function(e,t){var r=d[t+1];return a.inRange(e,d[t][0],r?r[0]:n,"minutes")},nextSlot:function(e){var t=f[Math.min(f.findIndex(function(t){return t===e||a.eq(t,e)})+1,f.length-1)];return a.eq(t,e)&&(t=a.add(e,r,"minutes")),t},closestSlotToPosition:function(e){var t=Math.min(f.length-1,Math.max(0,Math.floor(e*u)));return f[t]},closestSlotFromPoint:function(e,t){var n=Math.abs(t.top-t.bottom);return this.closestSlotToPosition((e.y-t.top)/n)},closestSlotFromDate:function(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(a.lt(e,t,"minutes"))return f[0];if(a.gt(e,n,"minutes"))return f[f.length-1];var s=a.diff(t,e,"minutes");return f[(s-s%r)/r+o]},startsBeforeDay:function(e){return a.lt(e,t,"day")},startsAfterDay:function(e){return a.gt(e,n,"day")},startsBefore:function(e){return a.lt(a.merge(t,e),t,"minutes")},startsAfter:function(e){return a.gt(a.merge(n,e),n,"minutes")},getRange:function(e,o,s,i){s||(e=a.min(n,a.max(t,e))),i||(o=a.min(n,a.max(t,o)));var l=y(e),c=y(o),d=c>r*u&&!a.eq(n,o)?(l-r)/(r*u)*100:l/(r*u)*100;return{top:d,height:c/(r*u)*100-d,start:y(e),startDate:e,end:y(o),endDate:o}},getCurrentTimePosition:function(e){return y(e)/(r*u)*100}}}var tt=(0,i.A)(function e(t,n){var r=n.accessors,o=n.slotMetrics;(0,s.A)(this,e);var a=o.getRange(r.start(t),r.end(t)),i=a.start,l=a.startDate,c=a.end,u=a.endDate,d=a.top,f=a.height;this.start=i,this.end=c,this.startMs=+l,this.endMs=+u,this.top=d,this.height=f,this.data=t},[{key:"_width",get:function(){return this.rows?100/(this.rows.reduce(function(e,t){return Math.max(e,t.leaves.length+1)},0)+1):this.leaves?(100-this.container._width)/(this.leaves.length+1):this.row._width}},{key:"width",get:function(){var e=this._width,t=Math.min(100,1.7*this._width);if(this.rows)return t;if(this.leaves)return this.leaves.length>0?t:e;var n=this.row.leaves;return n.indexOf(this)===n.length-1?e:t}},{key:"xOffset",get:function(){if(this.rows)return 0;if(this.leaves)return this.container._width;var e=this.row,t=e.leaves,n=e.xOffset,r=e._width;return n+(t.indexOf(this)+1)*r}}]);function tn(e){for(var t=e.events,n=e.minimumStartDifference,r=e.slotMetrics,o=e.accessors,a=function(e){for(var t=K()(e,["startMs",function(e){return-e.endMs}]),n=[];t.length>0;){var r=t.shift();n.push(r);for(var o=0;o<t.length;o++){var a=t[o];if(!(r.endMs>a.startMs)){if(o>0){var s=t.splice(o,1)[0];n.push(s)}break}}}return n}(t.map(function(e){return new tt(e,{slotMetrics:r,accessors:o})})),s=[],i=0;i<a.length;i++)if(function(){var e,t=a[i],r=s.find(function(e){return e.end>t.start||Math.abs(t.start-e.start)<n});if(!r)return t.rows=[],s.push(t),1;t.container=r;for(var o=null,l=r.rows.length-1;!o&&l>=0;l--)e=r.rows[l],(Math.abs(t.start-e.start)<n||t.start>e.start&&t.start<e.end)&&(o=r.rows[l]);o?(o.leaves.push(t),t.row=o):(t.leaves=[],r.rows.push(t))}())continue;return a.map(function(e){return{event:e.data,style:{top:e.top,height:e.height,width:e.width,xOffset:Math.max(0,e.xOffset)}}})}var tr={overlap:tn,"no-overlap":function(e){var t=tn({events:e.events,minimumStartDifference:e.minimumStartDifference,slotMetrics:e.slotMetrics,accessors:e.accessors});t.sort(function(e,t){return(e=e.style,t=t.style,e.top!==t.top)?e.top>t.top?1:-1:e.height!==t.height?e.top+e.height<t.top+t.height?1:-1:0});for(var n=0;n<t.length;++n)t[n].friends=[],delete t[n].style.left,delete t[n].style.left,delete t[n].idx,delete t[n].size;for(var r=0;r<t.length-1;++r)for(var o=t[r],a=o.style.top,s=o.style.top+o.style.height,i=r+1;i<t.length;++i){var l=t[i],c=l.style.top,u=l.style.top+l.style.height;(c>=a&&u<=s||u>a&&u<=s||c>=a&&c<s)&&(o.friends.push(l),l.friends.push(o))}for(var d=0;d<t.length;++d){for(var f=t[d],h=[],m=0;m<100;++m)h.push(1);for(var v=0;v<f.friends.length;++v)void 0!==f.friends[v].idx&&(h[f.friends[v].idx]=0);f.idx=h.indexOf(1)}for(var p=0;p<t.length;++p){var g=0;if(!t[p].size){var y=[];g=100/(function e(t,n,r){for(var o=0;o<t.friends.length;++o)if(!(r.indexOf(t.friends[o])>-1)){n=n>t.friends[o].idx?n:t.friends[o].idx,r.push(t.friends[o]);var a=e(t.friends[o],n,r);n=n>a?n:a}return n}(t[p],0,y)+1),t[p].size=g;for(var b=0;b<y.length;++b)y[b].size=g}}for(var w=0;w<t.length;++w){var E=t[w];E.style.left=E.idx*E.size;for(var D=0,A=0;A<E.friends.length;++A){var S=E.friends[A].idx;D=D>S?D:S}D<=E.idx&&(E.size=100-E.idx*E.size);var k=3*(0!==E.idx);E.style.width="calc(".concat(E.size,"% - ").concat(k,"px)"),E.style.height="calc(".concat(E.style.height,"% - 2px)"),E.style.xOffset="calc(".concat(E.style.left,"% + ").concat(k,"px)")}return t}},to=function(e){function t(){return(0,s.A)(this,t),(0,l.A)(this,t,arguments)}return(0,c.A)(t,e),(0,i.A)(t,[{key:"render",value:function(){var e=this.props,t=e.renderSlot,n=e.resource,r=e.group,o=e.getters,a=e.components,s=(void 0===a?{}:a).timeSlotWrapper,i=void 0===s?ee:s,l=o?o.slotGroupProp(r):{};return f.createElement("div",Object.assign({className:"rbc-timeslot-group"},l),r.map(function(e,r){var a=o?o.slotProp(e,n):{};return f.createElement(i,{key:r,value:e,resource:n},f.createElement("div",Object.assign({},a,{className:(0,d.A)("rbc-time-slot",a.className)}),t&&t(e,r)))}))}}])}(f.Component);function ta(e){return"string"==typeof e?e:e+"%"}function ts(e){var t=e.style,n=e.className,o=e.event,a=e.accessors,s=e.rtl,i=e.selected,l=e.label,c=e.continuesPrior,u=e.continuesAfter,h=e.getters,m=e.onClick,v=e.onDoubleClick,p=e.isBackgroundEvent,g=e.onKeyPress,y=e.components,w=y.event,E=y.eventWrapper,D=a.title(o),A=a.tooltip(o),S=a.end(o),k=a.start(o),R=h.eventProp(o,k,S,i),_=[f.createElement("div",{key:"1",className:"rbc-event-label"},l),f.createElement("div",{key:"2",className:"rbc-event-content"},w?f.createElement(w,{event:o,title:D}):D)],O=t.height,M=t.top,N=t.width,T=t.xOffset,x=(0,r.A)((0,r.A)({},R.style),{},(0,b.A)({top:ta(M),height:ta(O),width:ta(N)},s?"right":"left",ta(T)));return f.createElement(E,Object.assign({type:"time"},e),f.createElement("div",{role:"button",tabIndex:0,onClick:m,onDoubleClick:v,style:x,onKeyDown:g,title:A?("string"==typeof l?l+": ":"")+A:void 0,className:(0,d.A)(p?"rbc-background-event":"rbc-event",n,R.className,{"rbc-selected":i,"rbc-event-continues-earlier":c,"rbc-event-continues-later":u})},_))}var ti=function(e){var t=e.children,n=e.className,r=e.style,o=e.innerRef;return f.createElement("div",{className:n,style:r,ref:o},t)},tl=f.forwardRef(function(e,t){return f.createElement(ti,Object.assign({},e,{innerRef:t}))}),tc=["dayProp"],tu=["eventContainerWrapper","timeIndicatorWrapper"],td=function(e){function t(){var e;(0,s.A)(this,t);for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return(e=(0,l.A)(this,t,[].concat(o))).state={selecting:!1,timeIndicatorPosition:null},e.intervalTriggered=!1,e.renderEvents=function(t){var n=t.events,o=t.isBackgroundEvent,a=e.props,s=a.rtl,i=a.selected,l=a.accessors,c=a.localizer,u=a.getters,d=a.components,h=a.step,m=a.timeslots,v=a.dayLayoutAlgorithm,p=a.resizable,g=e.slotMetrics,y=c.messages;return(function(e){e.events,e.minimumStartDifference,e.slotMetrics,e.accessors;var t,n=e.dayLayoutAlgorithm,r=n;return(n in tr&&(r=tr[n]),(t=r)&&t.constructor&&t.call&&t.apply)?r.apply(this,arguments):[]})({events:n,accessors:l,slotMetrics:g,minimumStartDifference:Math.ceil(h*m/2),dayLayoutAlgorithm:v}).map(function(t,n){var a,h,m=t.event,v=t.style,b=l.end(m),w=l.start(m),E=null!=(a=l.eventId(m))?a:"evt_"+n,D="eventTimeRangeFormat",A=g.startsBeforeDay(w),S=g.startsAfterDay(b);A?D="eventTimeRangeEndFormat":S&&(D="eventTimeRangeStartFormat"),h=A&&S?y.allDay:c.format({start:w,end:b},D);var k=A||g.startsBefore(w),R=S||g.startsAfter(b);return f.createElement(ts,{style:v,event:m,label:h,key:E,getters:u,rtl:s,components:d,continuesPrior:k,continuesAfter:R,accessors:l,resource:e.props.resource,selected:ex(m,i),onClick:function(t){return e._select((0,r.A)((0,r.A)((0,r.A)({},m),e.props.resource&&{sourceResource:e.props.resource}),o&&{isBackgroundEvent:!0}),t)},onDoubleClick:function(t){return e._doubleClick(m,t)},isBackgroundEvent:o,onKeyPress:function(t){return e._keyPress(m,t)},resizable:p})})},e._selectable=function(){var t=e.containerRef.current,n=e.props,o=n.longPressThreshold,a=n.localizer,s=e._selector=new ej(function(){return t},{longPressThreshold:o}),i=function(t){var n=e.props.onSelecting,r=e.state||{},o=l(t),s=o.startDate,i=o.endDate;n&&(a.eq(r.startDate,s,"minutes")&&a.eq(r.endDate,i,"minutes")||!1===n({start:s,end:i,resourceId:e.props.resource}))||(e.state.start!==o.start||e.state.end!==o.end||e.state.selecting!==o.selecting)&&e.setState(o)},l=function(n){var o=e.slotMetrics.closestSlotFromPoint(n,eq(t));e.state.selecting||(e._initialSlot=o);var s=e._initialSlot;a.lte(s,o)?o=e.slotMetrics.nextSlot(o):a.gt(s,o)&&(s=e.slotMetrics.nextSlot(s));var i=e.slotMetrics.getRange(a.min(s,o),a.max(s,o));return(0,r.A)((0,r.A)({},i),{},{selecting:!0,top:"".concat(i.top,"%"),height:"".concat(i.height,"%")})},c=function(t,n){if(!eH(e.containerRef.current,t)){var r=l(t),o=r.startDate,a=r.endDate;e._selectSlot({startDate:o,endDate:a,action:n,box:t})}e.setState({selecting:!1})};s.on("selecting",i),s.on("selectStart",i),s.on("beforeSelect",function(t){if("ignoreEvents"===e.props.selectable)return!eH(e.containerRef.current,t)}),s.on("click",function(e){return c(e,"click")}),s.on("doubleClick",function(e){return c(e,"doubleClick")}),s.on("select",function(t){e.state.selecting&&(e._selectSlot((0,r.A)((0,r.A)({},e.state),{},{action:"select",bounds:t})),e.setState({selecting:!1}))}),s.on("reset",function(){e.state.selecting&&e.setState({selecting:!1})})},e._teardownSelectable=function(){e._selector&&(e._selector.teardown(),e._selector=null)},e._selectSlot=function(t){for(var n=t.startDate,r=t.endDate,o=t.action,a=t.bounds,s=t.box,i=n,l=[];e.props.localizer.lte(i,r);)l.push(i),i=new Date(+i+60*e.props.step*1e3);eO(e.props.onSelectSlot,{slots:l,start:n,end:r,resourceId:e.props.resource,action:o,bounds:a,box:s})},e._select=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];eO(e.props.onSelectEvent,n)},e._doubleClick=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];eO(e.props.onDoubleClickEvent,n)},e._keyPress=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];eO(e.props.onKeyPressEvent,n)},e.slotMetrics=te(e.props),e.containerRef=(0,f.createRef)(),e}return(0,c.A)(t,e),(0,i.A)(t,[{key:"componentDidMount",value:function(){this.props.selectable&&this._selectable(),this.props.isNow&&this.setTimeIndicatorPositionUpdateInterval()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable(),this.clearTimeIndicatorInterval()}},{key:"componentDidUpdate",value:function(e,t){this.props.selectable&&!e.selectable&&this._selectable(),!this.props.selectable&&e.selectable&&this._teardownSelectable();var n=this.props,r=n.getNow,o=n.isNow,a=n.localizer,s=n.date,i=n.min,l=n.max,c=a.neq(e.getNow(),r(),"minutes");if(e.isNow!==o||c){if(this.clearTimeIndicatorInterval(),o){var u=!c&&a.eq(e.date,s,"minutes")&&t.timeIndicatorPosition===this.state.timeIndicatorPosition;this.setTimeIndicatorPositionUpdateInterval(u)}}else o&&(a.neq(e.min,i,"minutes")||a.neq(e.max,l,"minutes"))&&this.positionTimeIndicator()}},{key:"setTimeIndicatorPositionUpdateInterval",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.intervalTriggered||t||this.positionTimeIndicator(),this._timeIndicatorTimeout=window.setTimeout(function(){e.intervalTriggered=!0,e.positionTimeIndicator(),e.setTimeIndicatorPositionUpdateInterval()},6e4)}},{key:"clearTimeIndicatorInterval",value:function(){this.intervalTriggered=!1,window.clearTimeout(this._timeIndicatorTimeout)}},{key:"positionTimeIndicator",value:function(){var e=this.props,t=e.min,n=e.max,r=(0,e.getNow)();if(r>=t&&r<=n){var o=this.slotMetrics.getCurrentTimePosition(r);this.intervalTriggered=!0,this.setState({timeIndicatorPosition:o})}else this.clearTimeIndicatorInterval()}},{key:"render",value:function(){var e=this.props,t=e.date,n=e.max,r=e.rtl,a=e.isNow,s=e.resource,i=e.accessors,l=e.localizer,c=e.getters,u=c.dayProp,h=(0,o.A)(c,tc),m=e.components,v=m.eventContainerWrapper,p=m.timeIndicatorWrapper,g=(0,o.A)(m,tu);this.slotMetrics=this.slotMetrics.update(this.props);var y=this.slotMetrics,b=this.state,w=b.selecting,E=b.top,D=b.height,A=b.startDate,S=b.endDate,k=u(n,s),R=k.className,_=k.style,O={className:"rbc-current-time-indicator",style:{top:"".concat(this.state.timeIndicatorPosition,"%")}},M=g.dayColumnWrapper||tl;return f.createElement(M,{ref:this.containerRef,date:t,style:_,className:(0,d.A)(R,"rbc-day-slot","rbc-time-column",a&&"rbc-now",a&&"rbc-today",w&&"rbc-slot-selecting"),slotMetrics:y,resource:s},y.groups.map(function(e,t){return f.createElement(to,{key:t,group:e,resource:s,getters:h,components:g})}),f.createElement(v,{localizer:l,resource:s,accessors:i,getters:h,components:g,slotMetrics:y},f.createElement("div",{className:(0,d.A)("rbc-events-container",r&&"rtl")},this.renderEvents({events:this.props.backgroundEvents,isBackgroundEvent:!0}),this.renderEvents({events:this.props.events}))),w&&f.createElement("div",{className:"rbc-slot-selection",style:{top:E,height:D}},f.createElement("span",null,l.format({start:A,end:S},"selectRangeFormat"))),a&&this.intervalTriggered&&f.createElement(p,O,f.createElement("div",O)))}}])}(f.Component);td.defaultProps={dragThroughEvents:!0,timeslots:2};var tf=function(e){var t=e.label;return f.createElement(f.Fragment,null,t)},th=function(e){function t(){var e;(0,s.A)(this,t);for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=(0,l.A)(this,t,[].concat(r))).handleHeaderClick=function(t,n,r){r.preventDefault(),eO(e.props.onDrillDown,[t,n])},e.renderRow=function(t){var n=e.props,r=n.events,o=n.rtl,a=n.selectable,s=n.getNow,i=n.range,l=n.getters,c=n.localizer,u=n.accessors,d=n.components,h=n.resizable,m=u.resourceId(t),v=t?r.filter(function(e){return u.resource(e)===m}):r;return f.createElement(e5,{isAllDay:!0,rtl:o,getNow:s,minRows:2,maxRows:e.props.allDayMaxRows+1,range:i,events:v,resourceId:m,className:"rbc-allday-cell",selectable:a,selected:e.props.selected,components:d,accessors:u,getters:l,localizer:c,onSelect:e.props.onSelectEvent,onShowMore:e.props.onShowMore,onDoubleClick:e.props.onDoubleClickEvent,onKeyPress:e.props.onKeyPressEvent,onSelectSlot:e.props.onSelectSlot,longPressThreshold:e.props.longPressThreshold,resizable:h})},e}return(0,c.A)(t,e),(0,i.A)(t,[{key:"renderHeaderCells",value:function(e){var t=this,n=this.props,r=n.localizer,o=n.getDrilldownView,a=n.getNow,s=n.getters.dayProp,i=n.components.header,l=void 0===i?e4:i,c=a();return e.map(function(e,n){var a=o(e),i=r.format(e,"dayFormat"),u=s(e),h=u.className,m=u.style,v=f.createElement(l,{date:e,label:i,localizer:r});return f.createElement("div",{key:n,style:m,className:(0,d.A)("rbc-header",h,r.isSameDate(e,c)&&"rbc-today")},a?f.createElement("button",{type:"button",className:"rbc-button-link",onClick:function(n){return t.handleHeaderClick(e,a,n)}},v):f.createElement("span",null,v))})}},{key:"render",value:function(){var e=this,t=this.props,n=t.width,r=t.rtl,o=t.resources,a=t.range,s=t.events,i=t.getNow,l=t.accessors,c=t.selectable,h=t.components,m=t.getters,v=t.scrollRef,p=t.localizer,g=t.isOverflowing,y=t.components,b=y.timeGutterHeader,w=y.resourceHeader,E=void 0===w?tf:w,D=t.resizable,A={};g&&(A[r?"marginLeft":"marginRight"]="".concat((0,j.A)()-1,"px"));var S=o.groupEvents(s);return f.createElement("div",{style:A,ref:v,className:(0,d.A)("rbc-time-header",g&&"rbc-overflowing")},f.createElement("div",{className:"rbc-label rbc-time-header-gutter",style:{width:n,minWidth:n,maxWidth:n}},b&&f.createElement(b,null)),o.map(function(t,n){var o=(0,u.A)(t,2),s=o[0],d=o[1];return f.createElement("div",{className:"rbc-time-header-content",key:s||n},d&&f.createElement("div",{className:"rbc-row rbc-row-resource",key:"resource_".concat(n)},f.createElement("div",{className:"rbc-header"},f.createElement(E,{index:n,label:l.resourceTitle(d),resource:d}))),f.createElement("div",{className:"rbc-row rbc-time-header-cell".concat(a.length<=1?" rbc-time-header-cell-single-day":"")},e.renderHeaderCells(a)),f.createElement(e5,{isAllDay:!0,rtl:r,getNow:i,minRows:2,maxRows:e.props.allDayMaxRows+1,range:a,events:S.get(s)||[],resourceId:d&&s,className:"rbc-allday-cell",selectable:c,selected:e.props.selected,components:h,accessors:l,getters:m,localizer:p,onSelect:e.props.onSelectEvent,onShowMore:e.props.onShowMore,onDoubleClick:e.props.onDoubleClickEvent,onKeyDown:e.props.onKeyPressEvent,onSelectSlot:e.props.onSelectSlot,longPressThreshold:e.props.longPressThreshold,resizable:D}))}))}}])}(f.Component),tm=function(e){function t(){var e;(0,s.A)(this,t);for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return(e=(0,l.A)(this,t,[].concat(r))).handleHeaderClick=function(t,n,r){r.preventDefault(),eO(e.props.onDrillDown,[t,n])},e}return(0,c.A)(t,e),(0,i.A)(t,[{key:"renderHeaderCells",value:function(e){var t=this,n=this.props,r=n.localizer,o=n.getDrilldownView,a=n.getNow,s=n.getters.dayProp,i=n.components,l=i.header,c=void 0===l?e4:l,h=i.resourceHeader,m=void 0===h?tf:h,v=n.resources,p=n.accessors,g=n.events,y=n.rtl,b=n.selectable,w=n.components,E=n.getters,D=n.resizable,A=a(),S=v.groupEvents(g);return e.map(function(n,i){var l=o(n),h=r.format(n,"dayFormat"),g=s(n),k=g.className,R=g.style,_=f.createElement(c,{date:n,label:h,localizer:r});return f.createElement("div",{key:i,className:"rbc-time-header-content rbc-resource-grouping"},f.createElement("div",{className:"rbc-row rbc-time-header-cell".concat(e.length<=1?" rbc-time-header-cell-single-day":"")},f.createElement("div",{style:R,className:(0,d.A)("rbc-header",k,r.isSameDate(n,A)&&"rbc-today")},l?f.createElement("button",{type:"button",className:"rbc-button-link",onClick:function(e){return t.handleHeaderClick(n,l,e)}},_):f.createElement("span",null,_))),f.createElement("div",{className:"rbc-row"},v.map(function(e,t){var o=(0,u.A)(e,2),a=o[0],s=o[1];return f.createElement("div",{key:"resource_".concat(a,"_").concat(t),className:(0,d.A)("rbc-header",k,r.isSameDate(n,A)&&"rbc-today")},f.createElement(m,{index:t,label:p.resourceTitle(s),resource:s}))})),f.createElement("div",{className:"rbc-row rbc-m-b-negative-3 rbc-h-full"},v.map(function(e,o){var s=(0,u.A)(e,2),i=s[0],l=s[1],c=(S.get(i)||[]).filter(function(e){return r.isSameDate(e.start,n)||r.isSameDate(e.end,n)});return f.createElement(e5,{key:"resource_".concat(i,"_").concat(o),isAllDay:!0,rtl:y,getNow:a,minRows:2,maxRows:t.props.allDayMaxRows+1,range:[n],events:c,resourceId:l&&i,className:"rbc-allday-cell",selectable:b,selected:t.props.selected,components:w,accessors:p,getters:E,localizer:r,onSelect:t.props.onSelectEvent,onShowMore:t.props.onShowMore,onDoubleClick:t.props.onDoubleClickEvent,onKeyDown:t.props.onKeyPressEvent,onSelectSlot:t.props.onSelectSlot,longPressThreshold:t.props.longPressThreshold,resizable:D})})))})}},{key:"render",value:function(){var e=this.props,t=e.width,n=e.rtl,r=e.range,o=e.scrollRef,a=e.isOverflowing,s=e.components.timeGutterHeader,i={};return a&&(i[n?"marginLeft":"marginRight"]="".concat((0,j.A)()-1,"px")),f.createElement("div",{style:i,ref:o,className:(0,d.A)("rbc-time-header",a&&"rbc-overflowing")},f.createElement("div",{className:"rbc-label rbc-time-header-gutter",style:{width:t,minWidth:t,maxWidth:t}},s&&f.createElement(s,null)),this.renderHeaderCells(r))}}])}(f.Component),tv=function(e){var t=e.min,n=e.max,r=e.timeslots,o=e.step,a=e.localizer,s=e.getNow,i=e.resource,l=e.components,c=e.getters,h=e.gutterRef,m=l.timeGutterWrapper,v=(0,f.useMemo)(function(){var e,r,o,s;return r=(e={min:t,max:n,localizer:a}).min,o=e.max,(s=e.localizer).getTimezoneOffset(r)!==s.getTimezoneOffset(o)?{start:s.add(r,-1,"day"),end:s.add(o,-1,"day")}:{start:r,end:o}},[null==t?void 0:t.toISOString(),null==n?void 0:n.toISOString(),a]),p=v.start,g=v.end,y=(0,f.useState)(te({min:p,max:g,timeslots:r,step:o,localizer:a})),b=(0,u.A)(y,2),w=b[0],E=b[1];(0,f.useEffect)(function(){w&&E(w.update({min:p,max:g,timeslots:r,step:o,localizer:a}))},[null==p?void 0:p.toISOString(),null==g?void 0:g.toISOString(),r,o]);var D=(0,f.useCallback)(function(e,t){if(t)return null;var n=w.dateIsInGroup(s(),t);return f.createElement("span",{className:(0,d.A)("rbc-label",n&&"rbc-now")},a.format(e,"timeGutterFormat"))},[w,a,s]);return f.createElement(m,{slotMetrics:w},f.createElement("div",{className:"rbc-time-gutter rbc-time-column",ref:h},w.groups.map(function(e,t){return f.createElement(to,{key:t,group:e,resource:i,components:l,renderSlot:D,getters:c})})))},tp=f.forwardRef(function(e,t){return f.createElement(tv,Object.assign({gutterRef:t},e))}),tg={},ty=function(e){function t(e){var n;return(0,s.A)(this,t),(n=(0,l.A)(this,t,[e])).handleScroll=function(e){n.scrollRef.current&&(n.scrollRef.current.scrollLeft=e.target.scrollLeft)},n.handleResize=function(){S.Z(n.rafHandle),n.rafHandle=S.E(n.checkOverflow)},n.handleKeyPressEvent=function(){n.clearSelection();for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];eO(n.props.onKeyPressEvent,t)},n.handleSelectEvent=function(){n.clearSelection();for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];eO(n.props.onSelectEvent,t)},n.handleDoubleClickEvent=function(){n.clearSelection();for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];eO(n.props.onDoubleClickEvent,t)},n.handleShowMore=function(e,t,o,a,s){var i=n.props,l=i.popup,c=i.onDrillDown,u=i.onShowMore,d=i.getDrilldownView,f=i.doShowMoreDrillDown;if(n.clearSelection(),l){var h=(0,A.A)(o,n.containerRef.current);n.setState({overlay:{date:t,events:e,position:(0,r.A)((0,r.A)({},h),{},{width:"200px"}),target:s}})}else f&&eO(c,[t,d(t)||en.DAY]);eO(u,[e,t,a])},n.handleSelectAllDaySlot=function(e,t){var r=n.props.onSelectSlot,o=new Date(e[0]),a=new Date(e[e.length-1]);a.setDate(e[e.length-1].getDate()+1),eO(r,{slots:e,start:o,end:a,action:t.action,resourceId:t.resourceId})},n.overlayDisplay=function(){n.setState({overlay:null})},n.checkOverflow=function(){if(!n._updatingOverflow){var e=n.contentRef.current;if(null!=e&&e.scrollHeight){var t=e.scrollHeight>e.clientHeight;n.state.isOverflowing!==t&&(n._updatingOverflow=!0,n.setState({isOverflowing:t},function(){n._updatingOverflow=!1}))}}},n.memoizedResources=(0,F.A)(function(e,t){return{map:function(n){return e?e.map(function(e,r){return n([t.resourceId(e),e],r)}):[n([tg,null],0)]},groupEvents:function(n){var r=new Map;return e?n.forEach(function(e){var n=t.resource(e)||tg;if(Array.isArray(n))n.forEach(function(t){var n=r.get(t)||[];n.push(e),r.set(t,n)});else{var o=r.get(n)||[];o.push(e),r.set(n,o)}}):r.set(tg,n),r}}}),n.state={gutterWidth:void 0,isOverflowing:null},n.scrollRef=f.createRef(),n.contentRef=f.createRef(),n.containerRef=f.createRef(),n._scrollRatio=null,n.gutterRef=(0,f.createRef)(),n}return(0,c.A)(t,e),(0,i.A)(t,[{key:"getSnapshotBeforeUpdate",value:function(){return this.checkOverflow(),null}},{key:"componentDidMount",value:function(){null==this.props.width&&this.measureGutter(),this.calculateScroll(),this.applyScroll(),window.addEventListener("resize",this.handleResize)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.handleResize),S.Z(this.rafHandle),this.measureGutterAnimationFrameRequest&&window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest)}},{key:"componentDidUpdate",value:function(){this.applyScroll()}},{key:"renderDayColumn",value:function(e,t,n,r,o,a,s,i,l,c){var u=this.props,d=u.min,h=u.max,m=(r.get(t)||[]).filter(function(t){return a.inRange(e,s.start(t),s.end(t),"day")}),v=(o.get(t)||[]).filter(function(t){return a.inRange(e,s.start(t),s.end(t),"day")});return f.createElement(td,Object.assign({},this.props,{localizer:a,min:a.merge(e,d),max:a.merge(e,h),resource:n&&t,components:i,isNow:a.isSameDate(e,c),key:"".concat(t,"-").concat(e),date:e,events:m,backgroundEvents:v,dayLayoutAlgorithm:l}))}},{key:"renderResourcesFirst",value:function(e,t,n,r,o,a,s,i,l){var c=this;return t.map(function(t){var d=(0,u.A)(t,2),f=d[0],h=d[1];return e.map(function(e){return c.renderDayColumn(e,f,h,n,r,o,a,i,l,s)})})}},{key:"renderRangeFirst",value:function(e,t,n,r,o,a,s,i,l){var c=this;return e.map(function(e){return f.createElement("div",{style:{display:"flex",minHeight:"100%",flex:1},key:e},t.map(function(t){var d=(0,u.A)(t,2),h=d[0],m=d[1];return f.createElement("div",{style:{flex:1},key:a.resourceId(m)},c.renderDayColumn(e,h,m,n,r,o,a,i,l,s))}))})}},{key:"renderEvents",value:function(e,t,n,r){var o=this.props,a=o.accessors,s=o.localizer,i=o.resourceGroupingLayout,l=o.components,c=o.dayLayoutAlgorithm,u=this.memoizedResources(this.props.resources,a),d=u.groupEvents(t),f=u.groupEvents(n);return i?this.renderRangeFirst(e,u,d,f,s,a,r,l,c):this.renderResourcesFirst(e,u,d,f,s,a,r,l,c)}},{key:"render",value:function(){var e,t=this.props,n=t.events,r=t.backgroundEvents,o=t.range,a=t.width,s=t.rtl,i=t.selected,l=t.getNow,c=t.resources,u=t.components,h=t.accessors,m=t.getters,v=t.localizer,p=t.min,g=t.max,y=t.showMultiDayTimes,b=t.longPressThreshold,w=t.resizable,E=t.resourceGroupingLayout;a=a||this.state.gutterWidth;var D=o[0],A=o[o.length-1];this.slots=o.length;var S=[],k=[],R=[];n.forEach(function(e){if(eJ(e,D,A,h,v)){var t=h.start(e),n=h.end(e);h.allDay(e)||v.startAndEndAreDateOnly(t,n)||!y&&!v.isSameDate(t,n)?S.push(e):k.push(e)}}),r.forEach(function(e){eJ(e,D,A,h,v)&&R.push(e)}),S.sort(function(e,t){return eQ(e,t,h,v)});var _={range:o,events:S,width:a,rtl:s,getNow:l,localizer:v,selected:i,allDayMaxRows:this.props.showAllEvents?1/0:null!=(e=this.props.allDayMaxRows)?e:1/0,resources:this.memoizedResources(c,h),selectable:this.props.selectable,accessors:h,getters:m,components:u,scrollRef:this.scrollRef,isOverflowing:this.state.isOverflowing,longPressThreshold:b,onSelectSlot:this.handleSelectAllDaySlot,onSelectEvent:this.handleSelectEvent,onShowMore:this.handleShowMore,onDoubleClickEvent:this.props.onDoubleClickEvent,onKeyPressEvent:this.props.onKeyPressEvent,onDrillDown:this.props.onDrillDown,getDrilldownView:this.props.getDrilldownView,resizable:w};return f.createElement("div",{className:(0,d.A)("rbc-time-view",c&&"rbc-time-view-resources"),ref:this.containerRef},c&&c.length>1&&E?f.createElement(tm,_):f.createElement(th,_),this.props.popup&&this.renderOverlay(),f.createElement("div",{ref:this.contentRef,className:"rbc-time-content",onScroll:this.handleScroll},f.createElement(tp,{date:D,ref:this.gutterRef,localizer:v,min:v.merge(D,p),max:v.merge(D,g),step:this.props.step,getNow:this.props.getNow,timeslots:this.props.timeslots,components:u,className:"rbc-time-gutter",getters:m}),this.renderEvents(o,k,R,l())))}},{key:"renderOverlay",value:function(){var e,t,n=this,r=null!=(e=null==(t=this.state)?void 0:t.overlay)?e:{},o=this.props,a=o.accessors,s=o.localizer,i=o.components,l=o.getters,c=o.selected,u=o.popupOffset,d=o.handleDragStart;return f.createElement(eF,{overlay:r,accessors:a,localizer:s,components:i,getters:l,selected:c,popupOffset:u,ref:this.containerRef,handleKeyPressEvent:this.handleKeyPressEvent,handleSelectEvent:this.handleSelectEvent,handleDoubleClickEvent:this.handleDoubleClickEvent,handleDragStart:d,show:!!r.position,overlayDisplay:this.overlayDisplay,onHide:function(){return n.setState({overlay:null})}})}},{key:"clearSelection",value:function(){clearTimeout(this._selectTimer),this._pendingSelection=[]}},{key:"measureGutter",value:function(){var e=this;this.measureGutterAnimationFrameRequest&&window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest),this.measureGutterAnimationFrameRequest=window.requestAnimationFrame(function(){var t,n=null!=(t=e.gutterRef)&&t.current?(0,W.A)(e.gutterRef.current):void 0;n&&e.state.gutterWidth!==n&&e.setState({gutterWidth:n})})}},{key:"applyScroll",value:function(){if(null!=this._scrollRatio&&!0===this.props.enableAutoScroll){var e=this.contentRef.current;e.scrollTop=e.scrollHeight*this._scrollRatio,this._scrollRatio=null}}},{key:"calculateScroll",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=e.min,n=e.max,r=e.scrollToTime,o=e.localizer,a=o.diff(o.merge(r,t),r,"milliseconds"),s=o.diff(t,n,"milliseconds");this._scrollRatio=a/s}}])}(f.Component);ty.defaultProps={step:30,timeslots:2,resourceGroupingLayout:!1};var tb=["date","localizer","min","max","scrollToTime","enableAutoScroll"],tw=function(e){function t(){return(0,s.A)(this,t),(0,l.A)(this,t,arguments)}return(0,c.A)(t,e),(0,i.A)(t,[{key:"render",value:function(){var e=this.props,n=e.date,r=e.localizer,a=e.min,s=void 0===a?r.startOf(new Date,"day"):a,i=e.max,l=void 0===i?r.endOf(new Date,"day"):i,c=e.scrollToTime,u=void 0===c?r.startOf(new Date,"day"):c,d=e.enableAutoScroll,h=(0,o.A)(e,tb),m=t.range(n,{localizer:r});return f.createElement(ty,Object.assign({},h,{range:m,eventOffset:10,localizer:r,min:s,max:l,scrollToTime:u,enableAutoScroll:void 0===d||d}))}}])}(f.Component);tw.range=function(e,t){return[t.localizer.startOf(e,"day")]},tw.navigate=function(e,t,n){var r=n.localizer;switch(t){case et.PREVIOUS:return r.add(e,-1,"day");case et.NEXT:return r.add(e,1,"day");default:return e}},tw.title=function(e,t){return t.localizer.format(e,"dayHeaderFormat")};var tE=["date","localizer","min","max","scrollToTime","enableAutoScroll"],tD=function(e){function t(){return(0,s.A)(this,t),(0,l.A)(this,t,arguments)}return(0,c.A)(t,e),(0,i.A)(t,[{key:"render",value:function(){var e=this.props,n=e.date,r=e.localizer,a=e.min,s=void 0===a?r.startOf(new Date,"day"):a,i=e.max,l=void 0===i?r.endOf(new Date,"day"):i,c=e.scrollToTime,u=void 0===c?r.startOf(new Date,"day"):c,d=e.enableAutoScroll,h=(0,o.A)(e,tE),m=t.range(n,this.props);return f.createElement(ty,Object.assign({},h,{range:m,eventOffset:15,localizer:r,min:s,max:l,scrollToTime:u,enableAutoScroll:void 0===d||d}))}}])}(f.Component);tD.defaultProps=ty.defaultProps,tD.navigate=function(e,t,n){var r=n.localizer;switch(t){case et.PREVIOUS:return r.add(e,-1,"week");case et.NEXT:return r.add(e,1,"week");default:return e}},tD.range=function(e,t){var n=t.localizer,r=n.startOfWeek(),o=n.startOf(e,"week",r),a=n.endOf(e,"week",r);return n.range(o,a)},tD.title=function(e,t){var n=t.localizer,r=tD.range(e,{localizer:n}),o=(0,V.A)(r),a=o[0],s=o.slice(1);return n.format({start:a,end:s.pop()},"dayRangeHeaderFormat")};var tA=["date","localizer","min","max","scrollToTime","enableAutoScroll"];function tS(e,t){return tD.range(e,t).filter(function(e){return -1===[6,0].indexOf(e.getDay())})}var tk=function(e){function t(){return(0,s.A)(this,t),(0,l.A)(this,t,arguments)}return(0,c.A)(t,e),(0,i.A)(t,[{key:"render",value:function(){var e=this.props,t=e.date,n=e.localizer,r=e.min,a=void 0===r?n.startOf(new Date,"day"):r,s=e.max,i=void 0===s?n.endOf(new Date,"day"):s,l=e.scrollToTime,c=void 0===l?n.startOf(new Date,"day"):l,u=e.enableAutoScroll,d=(0,o.A)(e,tA),h=tS(t,this.props);return f.createElement(ty,Object.assign({},d,{range:h,eventOffset:15,localizer:n,min:a,max:i,scrollToTime:c,enableAutoScroll:void 0===u||u}))}}])}(f.Component);function tR(e){var t=e.accessors,n=e.components,r=e.date,o=e.events,a=e.getters,s=e.length,i=e.localizer,l=e.onDoubleClickEvent,c=e.onSelectEvent,u=e.selected,d=(0,f.useRef)(null),h=(0,f.useRef)(null),m=(0,f.useRef)(null),v=(0,f.useRef)(null),p=(0,f.useRef)(null);(0,f.useEffect)(function(){b()});var g=function(e,r,o){var s=n.event,d=n.date;return(r=r.filter(function(n){return eJ(n,i.startOf(e,"day"),i.endOf(e,"day"),t,i)})).map(function(n,h){var m=t.title(n),v=t.end(n),p=t.start(n),g=a.eventProp(n,p,v,ex(n,u)),b=0===h&&i.format(e,"agendaDateFormat"),w=0===h&&f.createElement("td",{rowSpan:r.length,className:"rbc-agenda-date-cell"},d?f.createElement(d,{day:e,label:b}):b);return f.createElement("tr",{key:o+"_"+h,className:g.className,style:g.style},w,f.createElement("td",{className:"rbc-agenda-time-cell"},y(e,n)),f.createElement("td",{className:"rbc-agenda-event-cell",onClick:function(e){return c&&c(n,e)},onDoubleClick:function(e){return l&&l(n,e)}},s?f.createElement(s,{event:n,title:m}):m))},[])},y=function(e,r){var o="",a=n.time,s=i.messages.allDay,l=t.end(r),c=t.start(r);return!t.allDay(r)&&(i.eq(c,l)?s=i.format(c,"agendaTimeFormat"):i.isSameDate(c,l)?s=i.format({start:c,end:l},"agendaTimeRangeFormat"):i.isSameDate(e,c)?s=i.format(c,"agendaTimeFormat"):i.isSameDate(e,l)&&(s=i.format(l,"agendaTimeFormat"))),i.gt(e,c,"day")&&(o="rbc-continues-prior"),i.lt(e,l,"day")&&(o+=" rbc-continues-after"),f.createElement("span",{className:o.trim()},a?f.createElement(a,{event:r,day:e,label:s}):s)},b=function(){if(p.current){var e=d.current,t=p.current.firstChild;if(t){var n=v.current.scrollHeight>v.current.clientHeight,r=[],o=r;r=[(0,W.A)(t.children[0]),(0,W.A)(t.children[1])],(o[0]!==r[0]||o[1]!==r[1])&&(h.current.style.width=r[0]+"px",m.current.style.width=r[1]+"px"),n?((0,q.A)(e,"rbc-header-overflowing"),e.style.marginRight=(0,j.A)()+"px"):(0,G.A)(e,"rbc-header-overflowing")}}},w=i.messages,E=i.add(r,void 0===s?30:s,"day"),D=i.range(r,E,"day");return(o=o.filter(function(e){return eJ(e,i.startOf(r,"day"),i.endOf(E,"day"),t,i)})).sort(function(e,n){return t.start(e)-t.start(n)}),f.createElement("div",{className:"rbc-agenda-view"},0!==o.length?f.createElement(f.Fragment,null,f.createElement("table",{ref:d,className:"rbc-agenda-table"},f.createElement("thead",null,f.createElement("tr",null,f.createElement("th",{className:"rbc-header",ref:h},w.date),f.createElement("th",{className:"rbc-header",ref:m},w.time),f.createElement("th",{className:"rbc-header"},w.event)))),f.createElement("div",{className:"rbc-agenda-content",ref:v},f.createElement("table",{className:"rbc-agenda-table"},f.createElement("tbody",{ref:p},D.map(function(e,t){return g(e,o,t)}))))):f.createElement("span",{className:"rbc-agenda-empty"},w.noEventsInRange))}tk.defaultProps=ty.defaultProps,tk.range=tS,tk.navigate=tD.navigate,tk.title=function(e,t){var n=t.localizer,r=tS(e,{localizer:n}),o=(0,V.A)(r),a=o[0],s=o.slice(1);return n.format({start:a,end:s.pop()},"dayRangeHeaderFormat")},tR.range=function(e,t){var n=t.length,r=t.localizer.add(e,void 0===n?30:n,"day");return{start:e,end:r}},tR.navigate=function(e,t,n){var r=n.length,o=void 0===r?30:r,a=n.localizer;switch(t){case et.PREVIOUS:return a.add(e,-o,"day");case et.NEXT:return a.add(e,o,"day");default:return e}},tR.title=function(e,t){var n=t.length,r=t.localizer,o=r.add(e,void 0===n?30:n,"day");return r.format({start:e,end:o},"agendaHeaderFormat")};var t_=(0,b.A)((0,b.A)((0,b.A)((0,b.A)((0,b.A)({},en.MONTH,e9),en.WEEK,tD),en.WORK_WEEK,tk),en.DAY,tw),en.AGENDA,tR),tO=["action","date","today"],tM=function(e){return function(t){var n;return n=null,"function"==typeof e?n=e(t):"string"==typeof e&&"object"===(0,a.A)(t)&&null!=t&&e in t&&(n=t[e]),n}},tN=["view","date","getNow","onNavigate"],tT=["view","toolbar","events","backgroundEvents","resourceGroupingLayout","style","className","elementProps","date","getNow","length","showMultiDayTimes","onShowMore","doShowMoreDrillDown","components","formats","messages","culture"];function tx(e){if(Array.isArray(e))return e;for(var t=[],n=0,r=Object.entries(e);n<r.length;n++){var o=(0,u.A)(r[n],2),a=o[0];o[1]&&t.push(a)}return t}var tz=function(e){function t(){var e;(0,s.A)(this,t);for(var n=arguments.length,i=Array(n),c=0;c<n;c++)i[c]=arguments[c];return(e=(0,l.A)(this,t,[].concat(i))).getViews=function(){var t=e.props.views;return Array.isArray(t)?$()(t,function(e,t){return e[t]=t_[t]},{}):"object"===(0,a.A)(t)?B()(t,function(e,t){return!0===e?t_[t]:e}):t_},e.getView=function(){return e.getViews()[e.props.view]},e.getDrilldownView=function(t){var n=e.props,r=n.view,o=n.drilldownView,a=n.getDrilldownView;return a?a(t,r,Object.keys(e.getViews())):o},e.handleRangeChange=function(t,n,r){var o=e.props,a=o.onRangeChange,s=o.localizer;a&&n.range&&a(n.range(t,{localizer:s}),r)},e.handleNavigate=function(t,n){var a=e.props,s=a.view,i=a.date,l=a.getNow,c=a.onNavigate,u=(0,o.A)(a,tN),d=e.getView(),f=l();c(i=function(e,t){var n=t.action,r=t.date,a=t.today,s=(0,o.A)(t,tO);switch(e="string"==typeof e?t_[e]:e,n){case et.TODAY:r=a||new Date;break;case et.DATE:break;default:g()(e&&"function"==typeof e.navigate,"Calendar View components must implement a static `.navigate(date, action)` method.s"),r=e.navigate(r,n,s)}return r}(d,(0,r.A)((0,r.A)({},u),{},{action:t,date:n||i||f,today:f})),s,t),e.handleRangeChange(i,d)},e.handleViewChange=function(t){t!==e.props.view&&-1!==tx(e.props.views).indexOf(t)&&e.props.onView(t);var n=e.getViews();e.handleRangeChange(e.props.date||e.props.getNow(),n[t],t)},e.handleSelectEvent=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];eO(e.props.onSelectEvent,n)},e.handleDoubleClickEvent=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];eO(e.props.onDoubleClickEvent,n)},e.handleKeyPressEvent=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];eO(e.props.onKeyPressEvent,n)},e.handleSelectSlot=function(t){eO(e.props.onSelectSlot,t)},e.handleDrillDown=function(t,n){var r=e.props.onDrillDown;if(r)return void r(t,n,e.drilldownView);n&&e.handleViewChange(n),e.handleNavigate(et.DATE,t)},e.state={context:t.getContext(e.props)},e}return(0,c.A)(t,e),(0,i.A)(t,[{key:"render",value:function(){var e=this.props,t=e.view,n=e.toolbar,r=e.events,a=e.backgroundEvents,s=e.resourceGroupingLayout,i=e.style,l=e.className,c=e.elementProps,u=e.date,h=e.getNow,m=e.length,v=e.showMultiDayTimes,p=e.onShowMore,g=e.doShowMoreDrillDown;e.components,e.formats,e.messages,e.culture;var y=(0,o.A)(e,tT);u=u||h();var b=this.getView(),w=this.state.context,E=w.accessors,D=w.components,A=w.getters,S=w.localizer,k=w.viewNames,R=D.toolbar||e_,_=b.title(u,{localizer:S,length:m});return f.createElement("div",Object.assign({},c,{className:(0,d.A)(l,"rbc-calendar",y.rtl&&"rbc-rtl"),style:i}),n&&f.createElement(R,{date:u,view:t,views:k,label:_,onView:this.handleViewChange,onNavigate:this.handleNavigate,localizer:S}),f.createElement(b,Object.assign({},y,{events:r,backgroundEvents:a,date:u,getNow:h,length:m,localizer:S,getters:A,components:D,accessors:E,showMultiDayTimes:v,getDrilldownView:this.getDrilldownView,onNavigate:this.handleNavigate,onDrillDown:this.handleDrillDown,onSelectEvent:this.handleSelectEvent,onDoubleClickEvent:this.handleDoubleClickEvent,onKeyPressEvent:this.handleKeyPressEvent,onSelectSlot:this.handleSelectSlot,onShowMore:p,doShowMoreDrillDown:g,resourceGroupingLayout:s})))}}],[{key:"getDerivedStateFromProps",value:function(e){return{context:t.getContext(e)}}},{key:"getContext",value:function(e){var t,n,o,a=e.startAccessor,s=e.endAccessor,i=e.allDayAccessor,l=e.tooltipAccessor,c=e.titleAccessor,u=e.resourceAccessor,d=e.resourceIdAccessor,f=e.resourceTitleAccessor,h=e.eventIdAccessor,m=e.eventPropGetter,v=e.backgroundEventPropGetter,p=e.slotPropGetter,g=e.slotGroupPropGetter,y=e.dayPropGetter,b=e.view,w=e.views,E=e.localizer,D=e.culture,A=e.messages,S=e.components,k=void 0===S?{}:S,R=e.formats,_=tx(w),O=(t=void 0===A?{}:A,(0,r.A)((0,r.A)({},eM),t));return{viewNames:_,localizer:(n=void 0===R?{}:R,o=(0,r.A)((0,r.A)({},E.formats),n),(0,r.A)((0,r.A)({},E),{},{messages:O,startOfWeek:function(){return E.startOfWeek(D)},format:function(e,t){return E.format(e,o[t]||t,D)}})),getters:{eventProp:function(){return m&&m.apply(void 0,arguments)||{}},backgroundEventProp:function(){return v&&v.apply(void 0,arguments)||{}},slotProp:function(){return p&&p.apply(void 0,arguments)||{}},slotGroupProp:function(){return g&&g.apply(void 0,arguments)||{}},dayProp:function(){return y&&y.apply(void 0,arguments)||{}}},components:X()(k[b]||{},J()(k,_),{eventWrapper:ee,backgroundEventWrapper:ee,eventContainerWrapper:ee,dateCellWrapper:ee,weekWrapper:ee,timeSlotWrapper:ee,timeGutterWrapper:ee,timeIndicatorWrapper:ee}),accessors:{start:tM(a),end:tM(s),allDay:tM(i),tooltip:tM(l),title:tM(c),resource:tM(u),resourceId:tM(d),resourceTitle:tM(f),eventId:tM(h)}}}}])}(f.Component);tz.defaultProps={events:[],backgroundEvents:[],elementProps:{},popup:!1,toolbar:!0,view:en.MONTH,views:[en.MONTH,en.WEEK,en.DAY,en.AGENDA],step:30,length:30,allDayMaxRows:1/0,doShowMoreDrillDown:!0,drilldownView:en.DAY,titleAccessor:"title",tooltipAccessor:"title",allDayAccessor:"allDay",startAccessor:"start",endAccessor:"end",resourceAccessor:"resourceId",resourceIdAccessor:"id",resourceTitleAccessor:"title",eventIdAccessor:"id",longPressThreshold:250,getNow:function(){return new Date},dayLayoutAlgorithm:"overlap"};var tC=(0,h.Pd)(tz,{view:"onView",date:"onNavigate",selected:"onSelectEvent"}),tP=function(e,t,n){var r=e.start,o=e.end;return n.format(r,"LT",t)+" – "+n.format(o,"LT",t)},tI=function(e,t,n){var r=e.start,o=e.end;return n.format(r,"t",t)+" – "+n.format(o,"t",t)},tL=function(e,t,n){var r=e.start,o=e.end;return n.format(r,"t",t)+" – "+n.format(o,"t",t)},tF=function(e,t,n){var r=e.start,o=e.end;return n.format(r,{time:"short"},t)+" – "+n.format(o,{time:"short"},t)},tW=function(e,t,n){var r=e.start,o=e.end;return"".concat(n.format(r,"p",t)," – ").concat(n.format(o,"p",t))},tH={dateFormat:"dd",dayFormat:"dd eee",weekdayFormat:"ccc",selectRangeFormat:tW,eventTimeRangeFormat:tW,eventTimeRangeStartFormat:function(e,t,n){var r=e.start;return"".concat(n.format(r,"h:mma",t)," – ")},eventTimeRangeEndFormat:function(e,t,n){var r=e.end;return" – ".concat(n.format(r,"h:mma",t))},timeGutterFormat:"p",monthHeaderFormat:"MMMM yyyy",dayHeaderFormat:"cccc MMM dd",dayRangeHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return"".concat(n.format(r,"MMMM dd",t)," – ").concat(n.format(o,(0,y.eq)(r,o,"month")?"dd":"MMMM dd",t))},agendaHeaderFormat:function(e,t,n){var r=e.start,o=e.end;return"".concat(n.format(r,"P",t)," – ").concat(n.format(o,"P",t))},agendaDateFormat:"ccc MMM dd",agendaTimeFormat:"p",agendaTimeRangeFormat:tW},tK=function(e){var t=e.startOfWeek,n=e.getDay,r=e.format,o=e.locales;return new eR({formats:tH,firstOfWeek:function(e){return n(t(new Date,{locale:o[e]}))},format:function(e,t,n){return r(new Date(e),t,{locale:o[n]})}})},tj=function(e,t,n){var r=e.start,o=e.end;return n.format(r,"LT",t)+" – "+n.format(o,"LT",t)}}}]);