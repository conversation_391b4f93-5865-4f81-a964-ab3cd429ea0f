try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="021106b0-f5b5-4528-8924-86ec422d36bd",t._sentryDebugIdIdentifier="sentry-dbid-021106b0-f5b5-4528-8924-86ec422d36bd")}catch(t){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8855],{29256:(t,e,r)=>{Promise.resolve().then(r.bind(r,32564))},32564:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>s});var n=r(52880),a=r(40548),l=r(92708);function s(t){let{error:e}=t;return(0,n.jsxs)(a.Fc,{variant:"destructive","data-sentry-element":"Alert","data-sentry-component":"SalesError","data-sentry-source-file":"error.tsx",children:[(0,n.jsx)(l.A,{className:"h-4 w-4","data-sentry-element":"IconAlertCircle","data-sentry-source-file":"error.tsx"}),(0,n.jsx)(a.XL,{"data-sentry-element":"AlertTitle","data-sentry-source-file":"error.tsx",children:"Error"}),(0,n.jsxs)(a.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"error.tsx",children:["Failed to load sales data: ",e.message]})]})}},40548:(t,e,r)=>{"use strict";r.d(e,{Fc:()=>i,TN:()=>c,XL:()=>o});var n=r(52880);r(99004);var a=r(85017),l=r(54651);let s=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(t){let{className:e,variant:r,...a}=t;return(0,n.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,l.cn)(s({variant:r}),e),...a,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function o(t){let{className:e,...r}=t;return(0,n.jsx)("div",{"data-slot":"alert-title",className:(0,l.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...r,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function c(t){let{className:e,...r}=t;return(0,n.jsx)("div",{"data-slot":"alert-description",className:(0,l.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},49202:(t,e,r)=>{"use strict";r.d(e,{A:()=>l});var n=r(99004),a={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let l=(t,e,r,l)=>{let s=(0,n.forwardRef)((r,s)=>{let{color:i="currentColor",size:o=24,stroke:c=2,title:d,className:u,children:f,...g}=r;return(0,n.createElement)("svg",{ref:s,...a[t],width:o,height:o,className:["tabler-icon","tabler-icon-".concat(e),u].join(" "),..."filled"===t?{fill:i}:{strokeWidth:c,stroke:i},...g},[d&&(0,n.createElement)("title",{key:"svg-title"},d),...l.map(t=>{let[e,r]=t;return(0,n.createElement)(e,r)}),...Array.isArray(f)?f:[f]])});return s.displayName="".concat(r),s}},54651:(t,e,r)=>{"use strict";r.d(e,{Jv:()=>c,cn:()=>l,fw:()=>o,r6:()=>i,z3:()=>s});var n=r(97921),a=r(56309);function l(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return(0,a.QP)((0,n.$)(e))}function s(t){var e,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:l="normal"}=n;if(0===t)return"0 Byte";let s=Math.floor(Math.log(t)/Math.log(1024));return"".concat((t/Math.pow(1024,s)).toFixed(a)," ").concat("accurate"===l?null!=(e=["Bytes","KiB","MiB","GiB","TiB"][s])?e:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][s])?r:"Bytes")}function i(t){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(t)}function o(t){let e=Math.floor((new Date().getTime()-t.getTime())/1e3);if(e<60)return"刚刚";let r=Math.floor(e/60);if(r<60)return"".concat(r,"分钟前");let n=Math.floor(r/60);if(n<24)return"".concat(n,"小时前");let a=Math.floor(n/24);if(a<7)return"".concat(a,"天前");let l=Math.floor(a/7);if(l<4)return"".concat(l,"周前");let s=Math.floor(a/30);if(s<12)return"".concat(s,"个月前");let i=Math.floor(a/365);return"".concat(i,"年前")}function c(t){return("string"==typeof t?new Date(t):t)<new Date}},85017:(t,e,r)=>{"use strict";r.d(e,{F:()=>s});var n=r(97921);let a=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,l=n.$,s=(t,e)=>r=>{var n;if((null==e?void 0:e.variants)==null)return l(t,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:i}=e,o=Object.keys(s).map(t=>{let e=null==r?void 0:r[t],n=null==i?void 0:i[t];if(null===e)return null;let l=a(e)||a(n);return s[t][l]}),c=r&&Object.entries(r).reduce((t,e)=>{let[r,n]=e;return void 0===n||(t[r]=n),t},{});return l(t,o,null==e||null==(n=e.compoundVariants)?void 0:n.reduce((t,e)=>{let{class:r,className:n,...a}=e;return Object.entries(a).every(t=>{let[e,r]=t;return Array.isArray(r)?r.includes({...i,...c}[e]):({...i,...c})[e]===r})?[...t,r,n]:t},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},92708:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var n=(0,r(49202).A)("outline","alert-circle","IconAlertCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])}},t=>{var e=e=>t(t.s=e);t.O(0,[6677,9442,4579,9253,7358],()=>e(29256)),_N_E=t.O()}]);