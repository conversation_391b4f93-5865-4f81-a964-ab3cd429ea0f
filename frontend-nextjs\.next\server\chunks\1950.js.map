{"version": 3, "file": "1950.js", "mappings": "gcAsBO,SAASA,IAEZ,MAAM,qBAEL,CAFK,MACH,+GADG,+DA<PERSON>,EAOJ,yFAXgBA,qCAAAA,KAFEC,EAjBX,OAiBWA,8BAA8B,GAAC,kRCpBjD,4BACA,kCACA,6BACA,kCACA,MACA,kBACA,EAcA,oCACA,mEACA,oHACA,0EACA,sICvBA,mDAEA,GACA,WACA,WACA,gBACA,gBACA,oBACA,oBACA,yBACA,oBACA,eACA,CACA,uEACA,yBACA,6BACA,+BACA,sICXaC,qCAAAA,KAAN,IAAMA,EAGLC,EAAAA,OAAAA,QAFN,KAA6B,GAEvBA,CACgB,GAEhBA,CACgB,oXEbxB,OACA,iBACA,yBACA,SACA,uBACA,oBACA,SACA,EAEA,2CACA,SACA,wBAEA,MACA,QACA,OAGA,MAFA,eAEA,SADA,SACA,GAEA,OADA,gBACA,uCACA,EACA,iBACA,aACA,GACA,CACA,EACA,cAAyC,IACzC,QACA,aAAU,gFAAsF,CAChG,KACA,IACA,EACA,KACA,eACA,yBACA,SACA,QACA,CAAG,EACH,OACA,IACA,gBACA,CAAM,SAEN,aACA,OAEA,UACA,UAxCA,IAwCA,IAEA,SAEA,CAEA,wBEjDA,UACA,YACA,MAAsB,QAAiB,IAAM,QAAuB,GACpE,OACA,cAGA,SACA,aACA,iCAAqC,EAAO;AAC5C,EAAE,EAAQ,GAEV,wBEjBA,GACA,YACA,gBACA,oBACA,CAAG,CACH,QACA,gBACA,qBACA,CAAG,CACH,UACA,gBACA,qBACA,CAAG,CACH,KACA,kBACA,qBACA,CACA,EACA,2DACA,oDACA,6BACA,cACA,cACA,UACA,IAAU,oCAAiC,QAC3C,wBAGA,QAGA,aACA,yBAEA,OACA,WAEA,KAXA,IAYA,EACA,MACA,MACA,SAQA,+BACA,0DACA,YARA,IACA,mBACA,KAEA,CACA,EAIA,YAGA,EACA,4BAAkD,EAAuB,IACzE,yBACA,YAEA,0BACA,MACA,YAEA,UAAU,kBAAsB,IAChC,QACA,kBACA,kBACA,UACA,mBACA,QACA,qBACA,iBACA,oBACA,oBACA,CACA,EACA,KACA,IACA,aACA,SAEA,aACA,eACA,wBACA,sBAEA,sBACA,aEuFA,cACA,+BAAiC,qBACjC,CACA,cACA,4BACA,CAoEA,gBACA,QApGA,EAGA,MAEA,IAgGA,OArGA,KAGA,EAgFA,sBAtCA,EAuCA,oCApDA,KACA,eACA,2DAAuE,GAAG,EAC1E,MACA,eACA,UACA,UACA,YACA,UACA,CAAK,qBACL,QACA,EAyCA,uBAvCA,EAuCA,EAvCA,gBACA,WAsCA,KAtCA,OACG,EACH,2CAoCA,KAjCA,SAEA,OACA,kBAAyB,CACzB,yFACA,QACA,EAAI,mHAFJ,SAEI,WAAuL,EAAvL,EAAuL,OAAc,KACzM,MADI,CACJ,IACA,qCACA,CACA,sCACA,aACA,qBACA,uCACA,8BACA,6HACA,EAAY,sFACZ,CACA,sCACA,mFACA,+CACA,MACA,mDACA,CACA,CACA,GArBA,cAqBA,mEACA,CACA,+EACA,wFACA,CACA,yBACA,EA/BA,SA9GA,KACA,kBAAyB,CACzB,UA5FA,YACA,iBAA0B,YAAc,CACxC,WACA,8BACA,QACA,gBACA,QACA,aACO,EACP,QACA,CACA,aACA,QACA,oBACA,UACA,aACO,EACP,QACA,CACA,KAAgB,IAAhB,EAAgB,CAChB,QACA,YACA,QACA,aACO,EACP,QACA,CACA,KAAgB,IAAhB,EAAgB,CAChB,QACA,aACA,QACA,YACA,CAAO,EACP,QACA,CACA,YACA,mBAAkC,YAAc,CAChD,sBACA,sDACA,UACA,QACA,CACA,KACA,CACA,6DACA,SACA,YACA,QACA,OACA,CAAO,MACP,QACA,CACA,YACA,mBACA,6EACA,KAAa,YAAc,CAC3B,gBACA,iBACA,QACA,CACA,cACA,YACA,IACA,KACA,OACU,kCACV,kEACA,UAEA,yDACA,sDACA,SACA,eACA,QACA,OACA,CAAO,MACP,QACA,CACA,QACA,YACA,QACA,YACA,CAAK,CACL,CACA,eACA,WACA,QACA,QACA,CAAG,GACH,EAGA,uGACA,iDACG,eACH,WACA,uBACA,oCACA,2EACA,CAAG,cACH,eAAwB,gCAAoC,KAC5D,QACA,CAAG,eACH,YAA2B,EAA3B,EAA2B,OAAc,KACzC,MADA,CACA,IACA,2BACA,CACA,QACA,CAAG,eACH,sDACA,SACA,kGACA,sFACA,EAAK,YAAc,CACnB,2CACA,SACA,WACA,4DACA,YACA,SACA,UACA,gBACA,0BACA,CAAO,EACP,QACA,CACA,2BACA,MACA,KACA,QACA,CAGA,GAFA,oBACA,UACA,CACA,kDACA,oBACA,mBACA,qBACA,SACA,SACA,0BACA,CAAO,EACP,QACA,CACA,QACA,CACA,QACA,EAsFA,SACA,EAgBA,EArGA,EAqGA,GAlGA,EAFA,EAEA,EAkGA,EAjGA,kBAAyB,CACzB,oCACA,QACA,EAAI,EACJ,YACA,gBACA,eACA,+CAMK,IAAS,WAAc,KAN5B,YACA,kBACA,aACA,2FACA,aACA,CAAO,sBACP,CAAK,CACL,GACA,OACA,OACA,QACA,QACA,CACA,CA8EA,CAAI,SACJ,YACA;AACA,EAAE,UAAU,EAEZ,CACA,CElPA,8BAEA,KAAoB,iBAAiB,GAAG,CAExC,QAFiD,OAWjD,GACA,oBACA,oBACA,yBACA,8BACA,4BACA,sCACA,EACA,GACA,6BACA,mCACA,wCAEA,wBACA,sBACA,6BACA,iCACA,mCACA,EA6BA,GACA,WAxDA,CACA,6BACA,qCACA,+BACA,+BACA,iCACA,qBACA,EAkDA,UACA,QA/BA,CACA,+BACA,uCACA,iCACA,iCACA,mCACA,6BACA,4BACA,wCACA,sCACA,sDACA,8BACA,iCACA,mCACA,iCACA,gBACA,mBACA,uBACA,gBACA,YACA,2BACA,8BACA,oBACA,4BACA,EAQA,aAPA,CACA,uBACA,EAMA,iBACA,EAIA,uBAAsE,KACtE,MADmE,GAAG,EACtE,MACA,2BAHA,KAGA,UAHA,IAIA,CAGA,YACA,eACA,cACA,CACA,aACA,MACA,+CAEA,CACA,EAGA,8BACA,kBACA,qCACA,qBACA,cACA,MACA,CAAK,CACL,CACA,iDACA,qBACA,cACA,oBACA,CAAK,CACL,CACA,EAGA,2BACA,kBACA,mCACA,qBACA,aACA,OACA,aAAqB,aACrB,CAAK,CACL,CACA,mCACA,qBACA,cACA,OACA,YACA,CAAK,CACL,CACA,mCAEA,OADA,kBACA,cACA,gBACA,WACA,CAAK,CACL,CACA,EAGA,aACA,kBACA,wBAAiC,EACjC,qBACA,aACA,OACA,aAAqB,kBACrB,CAAK,CACL,CACA,mBAEA,OADA,kBACA,cACA,aACA,WACA,CAAK,CACL,CACA,gBACA,qBACA,cACA,mBACA,kBAAoB,EACpB,CAAK,CACL,CACA,EAIA,kBACA,sBACA,qBACA,gBACA,OALA,WAKA,EACA,CAAK,CACL,CACA,EAGA,qBACA,kBACA,yBAEA,OADA,kBACA,cACA,aACA,WACA,CAAK,CACL,CACA,4BACA,qBACA,cACA,OACA,YACA,CAAK,CACL,CACA,+BAAsD,EAEtD,OADA,kBACA,cACA,eACA,YACA,YACA,CAAK,CACL,CACA,4BAEA,OADA,kBACA,cACA,gBACA,WACA,CAAK,CACL,CACA,EAGA,iBACA,kBACA,4BAAqC,EACrC,qBACA,aACA,OACA,aAAqB,kBACrB,CAAK,CACL,CACA,0BACA,qBACA,cACA,OACA,YACA,CAAK,CACL,CACA,0BAEA,OADA,kBACA,cACA,cACA,oBACA,CAAK,CACL,CACA,EAGA,mBACA,kBACA,6BACA,qBACA,aACA,OACA,aACA,CAAK,CACL,CACA,4BACA,qBACA,cACA,OACA,YACA,CAAK,CACL,CACA,yBACA,wBAAY,GAAsB,EAClC,+CAEA,OADA,kBACA,cACA,aACA,YACA,aACA,qBACA,CACA,CAAK,CACL,CACA,8BAEA,OADA,kBACA,cACA,eACA,YACA,YACA,CAAK,CACL,CACA,kCACA,kBACA,UAAyB,IAAO,UAKhC,OAJA,yBACA,mBACA,+CAEA,cACA,aACA,mBACA,UACA,CAAK,CACL,CACA,gCAEA,OADA,kBACA,cACA,gBACA,kBACA,CAAK,CACL,CACA,sCAEA,OADA,kBACA,cACA,eACA,uBACA,YACA,CAAK,CACL,CACA,4BACA,qBACA,gBACA,WACA,CAAK,CACL,CACA,uCACA,mBAAY,QAAiC,EAE7C,OADA,kBACA,cACA,aACA,0BACA,aACA,CAAK,CACL,CACA,sCACA,mBAAY,QAAgC,EAE5C,OADA,kBACA,cACA,cACA,0BACA,YACA,CAAK,CACL,CACA,sCACA,mBAAY,iBAAwC,EAEpD,OADA,kBACA,cACA,eACA,4BACA,YACA,CAAK,CACL,CACA,8CACA,mBAAY,iBAAwC,EACpD,qBACA,eACA,uCACA,YACA,CAAK,CACL,CACA,sCACA,mBAAY,YAAyB,EAErC,OADA,kBACA,cACA,gBACA,2BACA,CAAK,CACL,CACA,uCACA,mBAAY,QAAiC,EAE7C,OADA,kBACA,cACA,aACA,0BACA,aACA,CAAK,CACL,CACA,sCACA,mBAAY,QAAgC,EAE5C,OADA,kBACA,cACA,cACA,0BACA,YACA,CAAK,CACL,CACA,mCACA,mBAAY,kBAA+B,EAG3C,OAFA,kBACA,kBACA,cACA,aACA,2BACA,CAAK,CACL,CACA,sCACA,mBAAY,uBAA8C,EAE1D,OADA,kBACA,cACA,cACA,qCACA,YACA,CAAK,CACL,CACA,mCACA,mBAAY,QAAiC,EAE7C,OADA,kBACA,cACA,aACA,sBACA,aACA,CAAK,CACL,CACA,kCACA,mBAAY,QAAgC,EAE5C,OADA,kBACA,cACA,cACA,sBACA,YACA,KACA,uBACA,CACA,CAAK,CACL,CACA,kCACA,mBAAY,mBAA0C,EAGtD,OAFA,kBACA,kBACA,cACA,eACA,wBACA,YACA,CAAK,CACL,CACA,kCACA,mBAAY,cAA2B,EAGvC,OAFA,kBACA,kBACA,cACA,gBACA,uBACA,CAAK,CACL,CACA,EAGA,mBACA,kBACA,wBAEA,OADA,kBACA,cACA,aACA,WACA,CAAK,CACL,CACA,2BACA,qBACA,cACA,OACA,YACA,CAAK,CACL,CACA,8BAAoD,EAEpD,OADA,kBACA,cACA,eACA,YACA,YACA,CAAK,CACL,CACA,2BAEA,OADA,kBACA,cACA,gBACA,WACA,CAAK,CACL,CACA,EAGA,oBACA,mBACA,2BACA,qBACA,aACA,QACA,aAAqB,aACrB,CAAK,CACL,CACA,wBAEA,OADA,kBACA,cACA,aACA,YACA,CAAK,CACL,CACA,2BACA,qBACA,cACA,QACA,YACA,CAAK,CACL,CACA,2BAEA,OADA,kBACA,cACA,gBACA,YACA,CAAK,CACL,CACA,EAGA,eACA,mBACA,yBAAkC,EAClC,qBACA,aACA,QACA,aAAqB,kBACrB,CAAK,CACL,CACA,oBAEA,OADA,kBACA,cACA,aACA,YACA,CAAK,CACL,CACA,uBAEA,OADA,kBACA,cACA,cACA,qBACA,CAAK,CACL,CACA,yBAEA,OADA,kBACA,cACA,cACA,sBACA,kBAAoB,EACpB,CAAK,CACL,CACA,oBAEA,OADA,kBACA,cACA,cACA,2BACA,CAAK,CACL,CACA,0BACA,kBACA,qBAAY,QAAkC,EAC9C,qBACA,cACA,uBACA,aACA,8BAAqB,EACrB,CAAK,CACL,CACA,EAGA,qBACA,mBACA,2BACA,qBACA,cACA,QACA,YACA,CAAK,CACL,CACA,2BAEA,OADA,kBACA,cACA,cACA,qBACA,CAAK,CACL,CACA,EAeA,GAAmB,QAAiB,EAAG,6BAA+B,EACtE,mBAAM,IAAoB,CAAE,OAA0B,GAGtD,YACA,mBACA,sBAA+B,EAC/B,IAAY,iCAA6C,EACzD,yBACA,cACA,aACA,QACA,aACA,CAAO,EACP,iBACA,EACA,YAAa,eACb,CACA,iBAEA,OADA,kBACA,cACA,aACA,YACA,CAAK,CACL,CACA,oBACA,qBACA,cACA,QACA,YACA,CAAK,CACL,CACA,uBAAsC,EAEtC,OADA,kBACA,cACA,eACA,aACA,YACA,CAAK,CACL,CACA,kCACA,kBACA,UAAyB,IAAO,UAEhC,OADA,yBACA,cACA,cACA,6BACA,UACA,CAAK,CACL,CACA,8BAEA,OADA,kBACA,cACA,eACA,wBACA,YACA,CAAK,CACL,CACA,oBAEA,OADA,kBACA,cACA,gBACA,YACA,CAAK,CACL,CACA,mBAA4B,EAC5B,qBACA,aACA,mBACA,aACA,CAAK,CACL,CACA,mCACA,kBACA,6BACA,eAAsD,EAAS,EAO/D,OANA,GACM,EACN,QADgB,oCAEhB,4DAGA,cACA,aACA,qCACA,aAAqB,aACrB,CAAK,CACL,CACA,wBAEA,OADA,kBACA,cACA,gBACA,kBACA,CAAK,CACL,CACA,uCACA,WAAY,oBAAwB,EAEpC,OADA,kBACA,cACA,aACA,wCACA,mBAAqB,WACrB,CAAK,CACL,CACA,wBACA,WAAY,cAAmB,EAE/B,OADA,kBACA,cACA,cACA,+BACA,YAAoB,WACpB,CAAK,CACL,CACA,oBACA,IAAY,iBAAe,EAE3B,OADA,kBACA,cACA,cACA,2BACA,iBAAoB,EACpB,CAAK,CACL,CACA,iBAEA,OADA,kBACA,cACA,cACA,kBACA,CAAK,CACL,CACA,mBAEA,OADA,kBACA,cACA,cACA,oBACA,CAAK,CACL,CACA,kBAEA,OADA,kBACA,cACA,cACA,mBACA,CAAK,CACL,CACA,oBAEA,OADA,kBACA,cACA,cACA,qBACA,CAAK,CACL,CACA,gCAEA,OADA,kBACA,cACA,gBACA,4BACA,CAAK,CACL,CACA,EAGA,uBACA,mBACA,gCAAyC,EACzC,qBACA,aACA,QACA,aACA,CAAK,CACL,CACA,8BACA,qBACA,cACA,QACA,YACA,CAAK,CACL,CACA,2BAEA,OADA,kBACA,cACA,aACA,YACA,CAAK,CACL,CACA,iCAA0D,EAE1D,OADA,kBACA,cACA,eACA,aACA,YACA,CAAK,CACL,CACA,8BAEA,OADA,kBACA,cACA,gBACA,YACA,CAAK,CACL,CACA,EAIA,mBACA,2BACA,qBACA,cACA,KALA,iBAMA,CAAK,CACL,CACA,EAOA,eACA,0BACA,8GAEA,CAMA,eACA,qBACA,sBACA,iBACA,gBACA,iBACA,CACA,mBACA,uEACA,CACA,EAGA,WACA,uBACA,UACA,kBACA,iBACA,iBACA,mBACA,CACA,mBACA,yEACA,CACA,EAGA,WACA,6BACA,UACA,gBACA,iBACA,YACA,eACA,sBACA,mBACA,iBACA,CACA,mBACA,aACA,KACA,YACA,aACA,OACA,UACA,kBACA,eACA,cAEA,CACA,EACA,WACA,0CACA,UACA,gBACA,cACA,cACA,oBACA,gBACA,iBACA,iBACA,iBACA,gCACA,sBACA,YACA,CACA,mBACA,aACA,KACA,YACA,UACA,SACA,iBACA,YACA,aACA,aACA,aACA,8BACA,kDACA,QAEA,CACA,EAGA,WACA,6BACA,UACA,kBACA,gBACA,gBACA,gBACA,2BACA,iBACA,gBACA,CACA,mBACA,aACA,KACA,cACA,kCACA,aACA,aACA,yBACA,aACA,aAEA,CACA,EAGA,WACA,eACA,cACA,CACA,mBACA,uBACA,CACA,EAGA,WACA,qBACA,cACA,UACA,YACA,cACA,CACA,mBACA,wDACA,CACA,EAGA,WACA,mCACA,UACA,qBACA,sBACA,sBACA,eACA,YACA,iBACA,cACA,YACA,YACA,uBACA,CACA,mBACA,aACA,KACA,kBACA,mBACA,mBACA,UACA,OACA,aACA,SACA,OACA,OACA,qBAEA,CACA,EAGA,WACA,iBACA,UACA,WACA,CACA,mBACA,yBACA,CACA,EAGA,WACA,oDACA,cACA,gBACA,uCACA,gBACA,gBACA,aACA,cACA,CACA,mBACA,aACA,SACA,WACA,wFACA,WACA,YACA,QAEA,CACA,EAGA,WACA,qBACA,UACA,oBACA,oBACA,eACA,CACA,mBACA,aACA,KACA,gBACA,4CACA,mCAEA,CACA,EAGA,WACA,oCAAoJ,MACpJ,UACA,gBACA,wBACA,kBACA,sBACA,oBACA,iBACA,gBACA,gBACA,gBACA,sBACA,aACA,mBACA,CACA,mBACA,aACA,KACA,WACA,oBACA,mBACA,kBACA,gBACA,aACA,YACA,gBACA,WACA,kBACA,QACA,4CAEA,CACA,EAGA,WACA,6BACA,UACA,oBACA,sBACA,iBACA,iBACA,cACA,WACA,eACA,cACA,CACA,UACA,iBAEA,mBACA,YACA,KACA,gBACA,kBACA,aACA,aACA,SACA,MACA,WAGA,OADA,SACA,CACA,CACA,EAGA,IACA,iDACA,2CACA,gBACA,kBACA,cACA,6BAIA,wBACA,sCACA,4BAEA,iDACA,iDACA,2BACA,2BAEA,kBAEA,4BAEA,yBACA,YAGA,cACA,wBAIA,EAGA,WACA,sBAAqE,QACrE,yBACA,gBACA,aACA,sBACA,aACA,cACA,kBACA,CACA,mBACA,aACA,sBACA,WACA,QACA,kBACA,YACA,SACA,eAEA,CACA,EAGA,WACA,8BAA2F,KAAsB,UACjH,UACA,YACA,YACA,gBACA,gBACA,iBACA,iBACA,sBACA,uBACA,6BACA,0BACA,oBACA,iBACA,cACA,CACA,UACA,iBAEA,mBACA,YACA,KACA,OACA,OACA,gBACA,YACA,aACA,aACA,kBACA,mBACA,0BACA,uBACA,gBACA,cAGA,OADA,SACA,CACA,CACA,EAGA,WACA,8BAAuG,KAAsB,EAC7H,UACA,oBACA,YACA,sBACA,iBACA,iBACA,cACA,sBACA,uBACA,cACA,CACA,UACA,iBAEA,mBACA,YACA,KACA,gBACA,OACA,kBACA,aACA,aACA,SACA,kBACA,oBAGA,OADA,SACA,CACA,CACA,EAGA,WACA,sBAAwD,KAAsB,UAC9E,UACA,YACA,mBACA,sBACA,uBACA,iBACA,iBACA,oBACA,sBACA,cACA,CACA,UACA,iBAEA,mBACA,YACA,KACA,OACA,cACA,kBACA,mBACA,aACA,aACA,4BACA,iCAGA,OADA,SACA,CACA,CACA,EACA,WACA,yBACA,kBACA,iBACA,gBACA,gBACA,gBACA,aACA,CACA,mBACA,aACA,aACA,aACA,YACA,YACA,YACA,UAEA,CACA,EAGA,WACA,yBACA,UACA,mBACA,+BACA,2BACA,oBACA,eACA,CACA,mBACA,aACA,KACA,eACA,6BACA,wBACA,4CACA,mCAEA,CACA,EAGA,WACA,qBACA,UACA,WACA,iBACA,gBACA,CACA,mBACA,kDACA,CACA,EAGA,WACA,2BACA,UACA,cACA,aACA,cACA,WACA,iBACA,gBACA,CACA,mBACA,6EACA,CACA,EAGA,WACA,2BACA,UACA,uBACA,qBACA,eACA,cACA,qBACA,WACA,CACA,mBACA,aACA,KACA,oBACA,kBACA,UACA,SACA,kBACA,OAEA,CACA,EAGA,WACA,eACA,UACA,CACA,mBACA,mBACA,CACA,EAGA,WACA,iCACA,UACA,YACA,cACA,cACA,gBACA,0BACA,uBACA,yBACA,iBACA,gBACA,CACA,mBACA,aACA,KACA,OACA,SACA,SACA,WACA,uBACA,mBACA,sBACA,aACA,aAEA,CACA,EAGA,WACA,+BACA,UACA,gBACA,sBACA,cACA,oBACA,iBACA,gBACA,oBACA,qBACA,CACA,mBACA,aACA,KACA,WACA,mBACA,SACA,gBACA,aACA,YACA,4CACA,kDAEA,CACA,EAGA,WACA,mBACA,UACA,kBACA,mBACA,CACA,mBACA,4EACA,CACA,EAGA,WACA,sDAA6R,KAAsB,KAAqB,0CACxU,UACA,uBACA,mBACA,yBACA,wBACA,cACA,cACA,iBACA,iBACA,gBACA,gBACA,6BACA,4BACA,2BACA,oBACA,kBACA,gBACA,iBACA,gBACA,sBACA,uBACA,sBACA,sBACA,oBACA,mBACA,wBACA,oBACA,oBACA,iCACA,gCACA,yBACA,uBACA,cACA,CACA,UACA,iBAEA,mBACA,YACA,KACA,mBACA,eACA,sBACA,qBACA,SACA,SACA,aACA,aACA,YACA,YACA,2BACA,0BACA,yBACA,kBACA,cACA,WACA,aACA,YACA,kBACA,mBACA,kBACA,+CACA,6CACA,4CACA,iDACA,6CACA,iBACA,8BACA,6BACA,sBACA,qBAGA,OADA,SACA,CACA,CACA,0BACA,qCAAuC,EAAI,wCAC3C,CACA,yBACA,mCAAqC,EAAI,uCACzC,CACA,wBACA,kCAAoC,EAAI,sCACxC,CACA,eACA,2DACA,CACA,EAyBA,eACA,mDACA,sBAEA,iBACA,+BACA,qBACA,6BACA,qBACA,gBACA,qBACA,iBACA,qBACA,sBACA,qBACA,eACA,qBACA,oBACA,qBACA,0BACA,qBACA,sBACA,qBACA,gCACA,qBACA,gCACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,qBACA,iBACA,qBACA,oBACA,qBACA,eACA,qBACA,oBACA,OA1CA,mBA2CA,QACA,qBACA,SACA,QACA,CACA,CAGA,mBAsGA,EAhBA,OAgBA,EArGA,UACA,IA6BA,EA7BA,CACA,YACA,sBACA,WACA,aAzjDA,IAyjDA,CACA,cACA,CAAM,EACN,MAAY,iEAAgE,CAC5E,IACA,MAGA,cADA,UAEA,KAEA,8BADoC,EAAa,CAAG,KAAgB,GAEpE,GACA,kDAIA,OACA,sBACA,eACA,KAEA,IACA,2BAAwC,GAAU,EAGlD,QA1FA,EA2FA,KACA,QAAoB,IAAO,eAC3B,SACA,UACA,MACA,CAAS,MACD,CACR,qCAEA,MADA,sCACA,CAAiC,oBAAqB,EAAa,GAAe,QAAa,CAA5B,CAA4B,EAAK,KACpG,QAAoB,IAAO,eAC3B,SACA,UACA,IACA,CAAS,CACT,CACA,8EACA,8BACA,SACA,OACA,UACA,aACA,iBACA,yBACA,6BACA,EAEA,OAxHA,GATA,cAkIA,GAhIA,CAAa,KAgIb,EAjIA,aACa,EAUb,CADA,EAuHA,IAtHA,gCAGA,wCATa,KA4Hb,EA9HA,mBAEa,WADb,eAGA,CAAa,QA0Hb,EA1Ha,CA0Hb,CACA,WACA,CACA,CAAM,SACN,sBACA,OACA,UACA,QACA,CACA,wBACA,qCACA,EACA,CACA,6BACA,EAEA,OACA,UACA,aACA,iBACA,yBACA,6BACA,CACA,CACA,EAkBA,cACA,SAAY,8DAA6D,cACzE,MACA,UAAwB,IAAqB,QAC7C,QACA,SACA,cACA,CAAO,CAEP,OADA,WACA,CACA,QACA,WACA,MAAe,gBAEf,CACA,CA/BA,CACA,wBACA,+EACA,iBAEA,kBACA,EACA,CACA,eACA,wCACA,eACA,wBAA0C,IAAU,KAEpD,SAsBA,eACA,YACA,OACA,6CACA,IAAqB,yBAAqC,GAE1D,8BACA,iBACA,wBACA,qBACA,uBACA,sBACA,uBACA,mBACA,uBACA,gBACA,iBACA,0BACA,uBACA,CACA,CAIA,UACA,KACA,OAAkB,MAGlB,OAFA,6CACA,uCACA,CAAa,KACb,EAEA,mBACA,IACA,MACA,MACA,SACA,WACA,WACA,kBACA,MACA,MACA,MACA,CAAI,EACJ,QACA,MACA,YACA,eACA,2DACG,EACH,UAEA,OACA,QACA,gBACA,YACA,cALA,QAMA,SACA,QACA,UACA,UACA,iBACA,wBACA,WACA,IAAS,EAAwB,OAAG,eAAH,YAAG,mCAA+D,EACnG,UAAyB,oBAAsC,CAC/D,CACA,CACA,eACA,OACA,mBACA,eACA,mBACA,YACA,WACA,WACA,aACA,aACA,oBACA,2BACA,mCACA,WACA,WACA,CACA,CAKA,WACA,YAAU,8BAAmC,MAC7C,iBAA4B,GAC5B,EAGA,WACA,gBAEA,EALA,IAOA,EAGA,IACA,qBACA,uBACA,qBACA,EACA,IACA,+DACA,wCACA,kCACA,uDACA,sDACA,0DACA,4CACA,4CACA,qEACA,oCACA,6DACA,+DACA,0DACA,kCACA,EACA,iCACA,gBACA,OACA,mBACA,YACA,aACA,wBACA,oCACA,8BACA,oBACA,0BACA,0BACA,oCACA,oCACA,cACA,aACA,UACA,OACA,CACA,CACA,oCACA,WACA,oBACA,SACA,UACA,wBACA,oCACA,8BACA,oBACA,0BACA,0BACA,oCACA,oCACA,cACA,UACA,eAAwC,4CAAuE,EAC/G,UACA,CAAG,CACH,CAoBA,WACA,+BAAwD,EACxD,aACA,IACA,sCACA,CAAM,MACN,CAEA,YACA,IACA,oCACA,CAAM,MACN,CAEA,YACA,IACA,oCACA,CAAM,MACN,CAGA,OADA,YACA,CACA,EAMA,qBACA,iBACA,kDAEA,EACA,WACA,aAIA,yBACA,iBAEA,MADA,8CACA,gCACA,8CACA,oCACA,CACA,SACA,OACA,uBACA,mBACA,yDACA,kCACA,wDACA,CACA,CAKA,wBACA,qBACA,0CACA,yCACA,gCACA,aACA,qCACA,yDACA,UAAyD,EAAiB,KAAK,EAAa,kBAC5F,aACA,MAEA,yBACA,CACA,2BACA,wBAEA,gBAEA,8BAD0B,QAAK,uDAE/B,CACA,qBACA,+BAAyC,EAAE,0BAC3C,CACA,EACA,WACA,oCAIA,MACA,KAOA,oBACA,YACA,kBACA,CACA,eAKA,eACA,IAfA,GAeA,GAfA,CAeA,CACA,MACA,UAAgB,IAAsB,EACtC,OAAgB,GAA4B,gBAC5C,6BACA,OAAgB,IAA4B,iBACrC,EAGP,GACA,CACA,YACA,UACA,YACA,EANA,oCAbA,6BAaA,YAZA,2BAYA,YAXA,+CAWA,YAVA,SAUA,0CAOA,QACA,CAAO,CACP,GAGA,CACA,OApCA,GAoCA,GApCA,CAsCA,mBACA,YACA,WACA,aAv+DA,IAu+DA,CACA,MACA,gBACC,EACD,iBAqEA,WACA,SAEA,yBAIA,OAHA,GACA,QAEA,CACA,KA7EA,CA7CA,GA6CA,EA7CA,CA6CA,CACA,MACA,UAAgB,IAAsB,EACtC,OAAgB,GAA4B,gBAC5C,kEACA,OAAgB,IAA4B,uBACrC,EAGP,SAAY,GAAO,MAAQ,EAD3B,GACgC,CADhC,WAEA,iBACA,UAAgB,IAAsB,EACtC,OAAgB,GAA4B,gBAC5C,yFACA,OAAgB,IAA4B,uBACrC,EAEP,mBACA,CACA,MAhEA,GAgEA,EAhEA,CAiEA,OAEA,aAhEA,WAgEA,+BACA,WAAc,IAAsB,EACpC,qFAA4F,GAA4B,iBAAiB,wBACzI,sEAA6E,EAAI,sLAAsL,EAAQ,EAC/Q,OAAc,IAA4B,gBACrC,CACL,CACA,QACA,CACA,yBACA,MACA,UAAc,IAAsB,EACpC,OAAc,GAA4B,mBAC1C,qHACA,OAAc,IAA4B,uBACrC,EAEL,gBACA,oCACA,YAAyB,IAAO,eAChC,SACA,wBAA+B,EAAI,EACnC,sBACA,kCACA,cACA,CACA,CAAG,EACH,UACA,qBACA,eAAqE,IAA0B,mBAC/F,MACA,MAAqB,IAA4B,wBACjD,IAAgB,IAAsB,EACtC,OAAgB,GAA4B,gBAC5C,kBACA,QACA,CAAO,CACP,CACA,UAAc,IAAsB,EACpC,OAAc,GAA4B,gBAC1C,yCAAgD,QAAU,YAAY,SAAgB,EACtF,OAAc,IAA4B,uBACrC,CACL,CACA,eACA,CAWA,cACA,EAGA,sBAFA,KAMA,uBACA,IAAU,iBAAgC,QAAS,IACnD,KACA,cAAa,GAEb,WAAU,GAAS,EACnB,KAAU,GAAM,EAChB,IACA,MACA,YACA,oBACM,gBAGN,OACA,QACA,IAAc,IAAsB,EACpC,OAAoB,GAA4B,gBAChD,qDACA,OAAoB,IAA4B,oBACrC,EACX,EATA,YAA2C,WAAiB,EAY5D,aAAiB,QAAS,IAAU,WAAiB,CACrD,CAAI,SACJ,OAAa,WACb,CACA,CAMA,aACA,mBACA,oBACA,oBACA,iCACA,wBACA,wBACA,2BACA,sBACA,yCAOA,mBACA,4DAEA,sBACA,kDACA,sCACA,gDACA,wCACA,8BACA,SAEA,sCACA,SAEA,UACA,SAEA,IAAY,QAAsB,QAAS,IAC3C,oBACA,CAAY,QAA4B,CAAE,OAAS,IACnD,oBACA,0BAGA,iBAFA,SAKA,qCACA,6BACA,uBACA,QAEA,OACA,OAIA,CACA,gCAp4CA,IAq4CA,iBAp4CE,QAAmB,IAAQ,SAAa,EAq4C1C,qCACA,MAAe,QAAmB,sBAClC,SACA,oBACA,gBACK,CACL,kCACA,+BAEA,mBACA,iGACA,6CACA,yCACA,2DACA,iHACA,iDACA,mDACA,yDACA,4CACA,CACA,mBACA,gFACA,oEACA,8FACA,CACA,sBACA,gIACA,yGACA,oFACA,CACA,iBACA,qDACA,CACA,aACA,+CACA,CACA,aACA,+CACA,CACA,qBACA,sBAA0B,QAAqB,8BAC/C,CACA,wCACA,2BACA,0BAEA,iBACA,CACA,4BACA,MACA,OAEA,+BACA,EAGA,aACA,SAHA,CAMA,CACA,kBACA,SAAY,YAAe,CAAE,OAAS,UACtC,IAGA,eACA,CACA,0BACA,MACA,SAEA,SAAY,YAAe,CAAE,OAAS,IACtC,KACA,SAEA,gDACA,2BACA,CACA,kBACA,6CACA,CACA,EACA,eAEA,OADA,uBAAsD,QAAe,kBAAyB,IAAO,mBACrG,KAIA,MACA,UAAiC,qBAEjC,MACA,UAAiC,qBAIjC,yBAA2C,EAAK,EAChD,IAAU,iBAAwB,CAAE,OAAS,IAC7C,KACA,WAEA,WAAU,aAAkB,EAC5B,KAAU,SAAW,EACnB,QAAgB,IAChB,QAAqB,IACvB,IAAU,iBAAgD,MAAQ,QAAiB,MACnF,KACA,UAAc,IAAsB,EACpC,OAAc,IAA4B,yBAC1C,4CAAmD,KAAmB,EACjE,EAEL,MACA,UAAc,IAAsB,EACpC,OAAc,IAA4B,uBAC1C,yCACA,CAAK,EAEL,QACA,CACA,uBACA,IAMA,EANA,WAAU,qEAAyE,EACnF,MAAU,YAAe,CAAE,OAAS,IACpC,KACA,WAEA,QAAU,GAAM,SAEhB,KACA,aACI,KACJ,sBAAyC,iEAAqE,OAE9G,UAAc,IAAsB,EACpC,OAAc,GAA4B,gBAC1C,+DACA,OAAc,IAA4B,oBACrC,EAEL,mBACA,KACA,CAAG,CACH,CAGA,QACA,qDACA,yCACA,4CACA,sCACA,4CACA,4CACA,sEACA,6EACA,yBACA,0CACA,2CACA,EAmCA,uBACA,wBAEA,GADA,gBACA,mBArCA,EAsCA,YAtCA,EAsCA,YArCA,OAAoB,QAA0B,IAC9C,4FAIA,GAiCA,uBA7BA,cACA,MACA,IACA,YACA,CAAI,MACJ,iEACA,CACA,gBACA,+FAEA,EAoBA,sBAlCA,CAoCA,uBAnCA,2GAoCA,CACA,eAkaA,GACA,WACA,8BACA,IACA,EAA+B,EAAK,0BACpC,CAAM,SACN,iDAA2D,0BAAgC,MAAM,EAAE,GACnG,CAEA,WACA,2BACA,IACA,EAA4B,EAAK,uBACjC,CAAM,SACN,oDAA8D,uBAA6B,MAAM,EAAE,GACnG,CAEA,OACA,sBACA,wBACA,CACA,EAvbA,2BAiCA,mBACA,mBACA,qCACA,yCACA,CAAK,EAEL,EADA,+BACA,UACA,KAOA,GANA,cACA,yBACA,qCACA,SAEA,CAAK,EACL,gCACA,0BACA,mDACA,uDACA,0CACA,wCACA,CACA,UACA,yCAEA,SAAY,iBAA6B,cACzC,KACA,mBAEA,gDAAiF,IAA4B,2BAAmC,IAA4B,gCAAwC,IAA4B,uBAChP,wBACA,cACA;;AAEA;;AAEA;;AAEA,EAAE,mBAAuB,GAEzB,IAAc,sBAA+C,YAC7D,KACA,mBACA,CAAO,EACP,KACA,kBAEA,0DACA,CACA,mDACA,CACA,oBACA,gBACA,OACA,UACA,OACA,2DACA,OAAmB,2BACnB,CACA,EAEA,IAAY,uCAAyE,EACrF,MACA,OACA,UACA,OACA,0CACA,OAAmB,8BACnB,CACA,EAEA,MACA,OACA,UACA,OACA,0CACA,OAAmB,8BACnB,CACA,EAEA,IAAY,iBAA4C,CAAE,OAAS,IACnE,SACA,OACA,UACA,OACA,sDACA,OAAmB,mDACnB,CACA,EAEA,oBACA,OACA,UACA,OACA,4DACA,OAAmB,6CACnB,CACA,EAEA,IAUA,OAAe,KATf,0DACA,gBACA,yCACA,oBACA,oBACA,iCAEA,yFACA,EAAO,EACQ,mBACf,CAAM,SACN,sBAkBA,OACA,UACA,OACA,uCACA,OAAqB,yCACrB,CACA,EAvBA,yCACA,OACA,UACA,OACA,iCACA,OAAuB,qCACvB,CACA,EAEA,OACA,UACA,OACA,yBACA,OAAqB,wCACrB,CACA,CAUA,CACA,CACA,oBACA,IAAY,gBAA4B,WACxC,oBACA,OAAe,mBAEf,kBACA,KACA,cACA,yBACA,qCACA,SAEA,CAAK,EACL,IAAY,iBAA2B,qBACvC,EACA,CACA,UACA,OACA,2DACA,OAAmB,uCACnB,CACA,EAEA,CAAa,iBAAQ,2BAAmC,YACxD,CACA,oBACA,YA5NA,GACA,WAAU,kBAAuB,WACjC,8BAGA,+BAIA,EAmNA,IACA,kBA/LA,iBAAsC,EAAiB,EACvD,eAPA,GACA,iBAGA,OAFA,oDACA,0DACA,CACA,EAEA,YACA,4CACA,qBAAmC,EAAsB,uBACzD,kDACA,sBACA,kCACA,oCAEA,2DACA,mDACA,sEAEA,SACA,WACA,0BACA,GAQA,OANA,GACA,CAqcA,YACA,cAYA,MAXA,4BACA,4BAEA,0BACA,kBACA,0CAEA,oBACA,6CAGA,EACA,EAndA,GACA,gBACA,0BACA,CAAO,EAEP,aAAyB,4BAAwC,CACjE,EAsKA,CAAqE,kBAAyB,QAK9F,CAJA,2BACA,yCAEA,SA8DA,GACA,sCACA,SAEA,uCACA,0BAEA,OADA,yBAAoC,EAAW,GAAG,IAAkB,cAAc,UAAU,YAC5F,EACA,EAtEA,KAGA,YADA,kOAEA,WAEA,SAtuBA,YACA,WACA,oBACA,SACA,UACA,oCACA,8BACA,oBACA,wBACA,0BACA,0BACA,oCACA,oCACA,cACA,UACA,gBACA,UACA,CAAG,CACH,EAotBA,QACA,CACA,gBACA,CAyCA,mBACA,yBAAY,GAAuB,EACnC,IACA,SAAc,YAAe,cAC7B,KACA,WAEA,uBACA,CAAM,SACN,oBACA,CACA,CAiBA,mBACA,kBACA,2BACA,sBACA,oBACA,IACA,gBACA,CAAQ,SACR,gBAA6B,IAAsB,iCACnD,CAfA,GAeA,EAfA,SAAyB,IAA4B,uBAErD,YADA,qJAGA,4DAAmE,mBAAuB,GAW1F,CAEA,sDAEA,CAEA,6FACA,iCAEA,iDACA,oCACA,8CAEA,mGACA,2BACA,sBACA,mCACA,uBAEA,mBAAoC,kCAAsD,EAC1F,+CACA,CACA,2CACA,oCAEA,sDACA,gBACA,oBACA,sBACA,6BACA,mBAGA,4DACA,mBAAoC,kCAAqE,EACzG,4CACA,CACA,sCACA,oCAEA,UACA,6CAEA,SACA,+CAEA,SACA,+CAEA,IAAY,iBAA4C,CAAE,OAAS,yBACnE,KACA,wBAEA,6BACA,iDAEA,IACA,SAAc,YAAe,mCAC7B,KACA,WAEA,SACA,EACA,EACA,OAEA,wBAEA,WAjJA,KACA,SACA,WACA,0BACA,GAEA,MACA,YAEA,SAYA,GAXA,0BACA,oDACA,OAEA,8CACA,QAGA,qCACA,OAEA,GACA,YAEA,oCAIA,OAHA,aACA,uJAEA,KAEA,QACA,EACA,8BACA,UAEA,uBACA,KAEA,CACA,EA2GA,EACA,YAEA,KACA,SAEA,QACA,CAAM,SACN,oBACA,CAEA,CACA,0BAIA,EAHA,kBAAyB,MACzB,gCAGA,GAnYA,WAAwB,IAA4B,eAmYpD,EAnYoD,uCAmYpD,CACA,SAAc,WAAc,WAC5B,KACA,mDAGA,EADA,iBACA,eAEA,uBAEM,IAEN,EADA,iBACA,qBACQ,uBAGR,KAFA,6BAWA,CANA,iBACA,CACM,IAA4B,cAC5B,IAA4B,mBAC5B,IAA4B,qBAClC,qBAEA,EACA,EACA,IAA+D,mCAAsC,EACrG,oBAGA,iCACA,QACA,uBACA,IAEA,GACA,CACA,WACA,eAAU,yEAA6E,EACvF,kBAAW,wEACX,EAuBA,mBACA,MACA,YAEA,0BACA,MACA,IACA,mCACA,CAAM,SAEN,OADA,8DAAoE,uBAA6B,gBACjG,IACA,CACA,oBACA,eACA,mCACA,OAAiB,yCAEjB,uCACA,OAAiB,6CAEjB,aACA,iJAEA,CACA,CACA,6BACA,MACA,IACA,sCACA,CAAM,SAEN,OADA,2DAAiE,0BAAgC,gBACjG,IACA,CACA,KACA,OAAe,uBAEf,CACA,WACA,CAgBA,SACA,aACA,eACC,IACD,UACA,KAAS,IAA4B,cACrC,SAAgB,uBAAoC,WAAW,EAAa,OACnE,IAA4B,mBACrC,+BACS,IAA4B,qBACrC,6CAEA,0BAEA,EAGA,iBACA,6BACA,OACA,EAAe,sBAEf,CAAM,MAEN,CAGA,IAAI,GAAc,CAClB,aACA,UACA,EAHkB,KAGlB,OACA,kBACA,YACA,kBACA,eACA,UACA,WACA,EACA,eACA,SAAkD,GAAc,WAChE,cAcA,CAfgE,KAehE,CACA,oBAdA,OAAqD,IACrD,WAAY,gBAAqB,EACjC,UACA,aACA,KACA,KAGA,SACA,aACA,WACA,CAAK,CACL,EAGA,oBACA,CACA,0IG74FA,kBACA,UACA,qCAEA,iBACA,wBACA,eAOA,OANA,GACA,gDAEA,4BACA,mBAAyB,IAAS,+BAElC,YACA,EACA,UACA,MACA,wBAOA,iBAPA,CACA,6BACA,kFAEA,iBACA,qBACA,CAMA,CANI,MAGJ,GACA,qCAEA,YACA,EACA,MACA,mBAAU,uDAAiE,EAC3E,EAA+B,QAAmB,IAClD,iBACA,kCACA,EFnDA,YACA,MACA,EEiD8C,IFjD9C,GAEA,4GACA,iBAAoB,EAAgB,CACpC,EE6C8C,GAmB9C,OAAW,iBAlBX,gBAA8B,GAAgB,EAAI,IAClD,MACM,IAAY,mCAElB,SAAiC,EAAgB,UACjD,SACA,qCAEA,EAUW,iBATX,gBAA8B,GAAgB,EAAI,IAClD,MACM,IAAY,mCAElB,SAAiC,EAAgB,UACjD,SACA,qCAEA,CACW,CACX,mBCrDO,SAASC,IAEZ,MAAM,qBAEL,CAFK,MACH,+GADG,+DAEN,EAOJ,sFAXgBA,qCAAAA,KAFEH,EAhBX,OAgBWA,8BAA8B,GAAC,4ZGhBjD,0CACA,qCACA,0CACA,mCACA,EAAwB,6DAA6C,CACrE,GADyE,CAAE,MAC3E,6BACA,6BFIA,KACA,MAAsB,QAAmB,wBACzC,yBAA2C,IAA4B,yBAC5D,IAAY,CAEjB,IAAkB,yBACb,IAAa,CAElB,IAAoB,yBACf,IAAe,CAEjB,IAAY,CACrB,EEhBqE,GACrE,2CACA,8CACA,EAAqB,QAAQ,iDAC7B,EAAoB,eAAyC,CAC7D,EAAoB,CAD6C,CAAE,aACN,CAC7D,GADiE,CAAE,IAEnE,gBACA,kBACA,YAAe,YAAoB,EAEnC,EAA2B,QAAQ,mDACnC,EAAwB,QAAQ,gDAChC,EAAyB,QAAQ,iHClBjC,OAAuB,UAA+B,EACtD,QAAwB,KAAO,IAAgB,uECO/C,IAcA,MACA,UACA,MACA,SAEA,oBACA,gBACA,qCACA,KAEA,GAGA,OAAmB,MAEnB,aADA,eACA,CACA,sBACA,QACA,UACA,aAEA,uBACA,cAEA,CACA,QACA,EACA,QACA,EAGA,cACA,uBACA,SAEA,WACA,SAEA,uBACA,4BACA,SAEA,6BACA,QAEA,CACA,2BACA,WAGA,GAIA,CAzBA,EAhCA,YACA,mCAAqD,gBAAqB,KAC1E,GA+BA,EApCA,YACA,0EACA,oDCNA,IAeO,aAQP,MAPA,8BACA,iCAA+C,IAAO,IAEtD,aADA,eACA,uDAEA,QACA,GACA,qBACA,CAgHO,eC5GA,cACP,sBACA,CDqNA,cAoEA,oDErUA,oDAEA,iBAqBA,yBACA,oBACA,eACA,uBAA4C,sBAAkC,GAC9E,CE3BO,oBDAA,ECEP,OADA,OACkB,EADlB,IAA8B,MDA9B,UADO,ECEmB,EAAQ,CAAG,KAAH,KAAG,IAAgB,ODDvB,MACnB,MAAM,GDIV,KACP,aAA8B,MAM9B,IALA,kDAA2O,cAC3O,QADA,eACA,UADA,eACA,MACA,IACA,WAEA,oBACA,IACA,2BACA,IAEA,oCAVA,WAAkO,EAAS,GAU3O,IAV2O,kBAW3O,EEjBkB,EDAO,EAAQ,CAAG,KAAH,KAAG,IAAgB,ICCpD,mIEAA,oBAHA,EAIA,MAHA,EADA,EAI4C,IAAS,kBAArD,EAHA,cAGqD,IAAoC,IAAS,YAClG,CACA,gBACA,eASA,YACA,IACA,YAAY,uBAA4B,MACxC,+IACA,CAAI,MACJ,QACA,CACA,EAfA,aAgBA,GACA,IACA,YAAY,GAAU,MACtB,8CACA,CAAI,MACJ,QACA,CACA,EAvBA,GACA,iBAEA,oHACA,CACA,cACA,yBACA,+GCVgBC,qCAAAA,SAAAA,EAAiBG,CAAc,EAC7C,GACEC,GAAAA,EAAAA,iBAAAA,EAAkBD,IAClBE,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBF,IACpBG,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBH,IACrBI,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBJ,IAClBK,CAAAA,EAAAA,EAAAA,UAAAA,EAAWL,IACXM,CAAAA,EAAAA,EAAAA,8BAAAA,EAA+BN,GAE/B,KADA,CACMA,EAGJA,aAAiBO,OAAS,UAAWP,GACvCH,EAAiBG,EAD6B,KAClB,CAEhC,aAtB+C,WACpB,WACS,WACF,WACA,WACG,mRCJrC,UAKA,GAEA,MAKA,GAEA,OACA,IACA,OAAW,CACX,CAAI,MACJ,CACA,GAH+B,GAG/B,EACA,yZGoBA,cACA,OACA,YACA,kBACA,2BACA,MACA,8BACA,8BACA,wCACA,iCACA,sBACA,CACA,CACA,CAeA,4BACA,eAAyB,+BAA4B,EACrD,SACA,mBACA,UAAwB,UAAU;AAClC,UAAU;AACV,SAAS;AACT,qBAAqB,gBACrB,sBACQ,EAKR,OAJA,mBACA;AACA,kBAAkB,kBAAkB,GAEpC,CACA,EACA,wCACA,cACA,eACA,oBACA,mBACA,qBApDA,MACA,+BAmDA,EACA,CACA,EAiDA,iBACA,uKAA0K,KAAK,GAC/K,oLAAuL,KAAK,GAC5L,2IACA,iIACA,wBAA2B,SAAS,sHACpC,CAAC,EACD,wBAA6B,mBAA6B,EAC1D,QACA,GACA,KACA,IACA,EACA,gBACA,MACA,SAAgB,EAAI,IAAI,EAAW,EAEnC,QAEA,aADA,cAA2C,kBAAkB,IAC7D,CACA,+BACA,eAA2B,EAAE,OAAW,IACxC,CACA,SAAc,EAAI,IAAI,EAAI,EAE1B,OACA,gBAAqB,cAA2B,EAIhD,MAHA,oBACA,MAEA,KACK,CACL,aAAkB,iBAAiC,EAEnD,OADA,qBAAmD,EACnD,KACK,CACL,mCACA,qDACA,CAAK,CACL,wBACA,+CACA,CAAK,CACL,kCACA,mDACA,CAAK,CACL,6BACA,8CACA,CAAK,CACL,kCACA,wCACA,CAAK,CACL,SACA,iBACA,CACA,CACA,mIGjMA,QADiB,EAAe,oBACoB,IAAe,+BCDnE,sCACA,iBAGA,KACA,MACA,wBACA,uBACA,qBAEA,0BAOA,GANA,GACA,QACA,SAAY,CACZ,IACA,EAEA,SACA,gBAGA,kBAEA,IAAQ,UAAQ,CAChB,iBAEA,qCACA,oBACA,YAGA,kCACA,eAEA,SACA,SAGA,uBAA0B,MAAsB,EAAI,GAGpD,mBAIA,iBACA,oBACA,KACA,YAGA,OACA,CAEA,QACA,CAEA,qBACA,SACA,6CAAmD,EAAO,MAAM,SAAc,IAG9E,eACA,EAEA,uBAA4B,oBCjE5B,MAAY,EAAQ,KAAS,EAC7B,CAAQ,SADW,EACX,EAAY,EAAU,KAAY,EAE1C,IAAiC,MAFJ,MAEI,CAEjC,wBACA,oBACA,iCACA,iDACA,MAEA,qBACA,2CAMA,6BASA,IAQA,MAhBA,OACA,CAOA,EAPA,UAOA,EAPA,EAQA,mBACA,yBACA,MACA,SACA,CAAG,GAZH,wBACA,GAcA,EAbA,EAaA,EAbA,EAcA,CADA,EAbA,GAcA,cACA,CAAQ,oCACR,QAfA,CACG,CARH,iBAA4B,qCAA6C,IASzE,0JCOSQ,uBAAuB,mBAAvBA,GALAC,YAAY,mBAAZA,EAAAA,YAAY,EAEZV,SAAS,mBAATA,EAAAA,SAAS,EADTW,QAAQ,mBAARA,EAAAA,QAAQ,EAFEC,iBAAiB,mBAAjBA,EAAAA,iBAAiB,EAA3BC,QAAQ,mBAARA,EAAAA,QAAQ,EAIRjB,YAAY,mBAAZA,EAAAA,YAAY,EACZE,gBAAgB,mBAAhBA,EAAAA,gBAAgB,YALmB,WACf,WACJ,WACC,WACG,UACI,KAhCjC,OAAMgB,UAAqCN,MACzCO,aAAc,CACZ,KAAK,CACH,0JAEJ,CACF,CAEA,MAAMN,UAAgCO,gBAEpCC,QAAS,CACP,MAAM,IAAIH,CACZ,CAEAI,QAAS,CACP,MAAM,IAAIJ,CACZ,CAEAK,KAAM,CACJ,MAAM,IAAIL,CACZ,CAEAM,MAAO,CACL,MAAM,IAAIN,CACZ,CACF,8RCzBA,OACA,oCACA,EACA,GACA,6BACA,6BACA,gDACA,iEACA,gDACA,yCACA,8CACA,oDACA,sCACA,oCACA,kDACA,sCACA,sCACA,2CACA,iCACA,EACA,GACA,2CACA,oEACA,6DACA,mEACA,+GACA,EACA,wBACA,aACA,SACA,UACA,SACG,EACH,SACA,wCACA,cACA,eACA,aACA,CACA,iBACA,SAAc,mDAAwD,UAAU,YAAY,kBAAkB,kBAAkB,GAEhI,EACA,sBACA,iBC3CA,IAAa,CA0Eb,cACA,YACA,WAEA,OACA,SACA,mBACA,IACA,GACA,uBACA,UACA,MACA,CADmB,GACnB,cAAuC,KACvC,aACA,QAEA,kBAAsC,SACtC,QACA,CACA,eACA,WACA,eAEA,kBACA,iBACA,WACA,iBACA,OACA,CACA,KACA,EAAM,UACN,QACA,EA5CA,0BA9CA,IA+CA,QACA,mBAEA,OADA,gCACA,CACA,CAAC,IAwCD,kBACA,GACA,sBACA,iBACA,QACA,EAAM,YACN,QACA,CACA,kBACA,WACA,wBACA,iBACA,UACA,CACA,QACA,CAkGA,cACA,uBACA,SACA,IACA,4BACA,CACA,SACA,QACA,CACA,0JCxNgBO,gBAAgB,mBAAhBA,GA6EAC,8BAA8B,mBAA9BA,GARAC,wBAAwB,mBAAxBA,GARAC,uBAAuB,mBAAvBA,GAhBAZ,iBAAiB,mBAAjBA,GAvBAC,QAAQ,mBAARA,aArCmB,UAM5B,OAEDY,EAGE1B,EAAAA,OAAAA,UAFN,KAA6B,GAEvBA,CACkB,GACpB2B,CAASA,KAECL,EACdM,CAAW,CACXC,CAAkB,CAClBC,CAAqE,EAArEA,KAAAA,IAAAA,IAAAA,EAAiCC,EAAAA,kBAAkB,CAACC,iBAAAA,EAEpD,IAAM9B,EAAQ,qBAA8B,CAA9B,MAAU+B,EAAAA,mBAAmB,EAA7B,+DAA6B,GAE3C,OADA/B,EAAMgC,MAAM,CAAMD,EAAAA,mBAAmB,CAAC,IAAGJ,EAAK,IAAGD,EAAI,IAAGE,EAAW,IAC5D5B,CACT,CAcO,SAASY,EAEdc,CAAW,CACXC,CAAmB,IAFnB,EAISH,CAIT,OAJAG,MAAAA,CAAAA,GAAAA,EAASH,CAAAA,IAJkB,EAIlBA,CAAAA,EAAAA,GAAAA,IAAAA,EAAAA,EAAoBS,QAAQ,WAA5BT,EAAgCU,QAAAA,EACrCzB,EAAAA,YAAY,CAAC0B,IAAI,CACjB1B,EAAAA,YAAY,CAAC2B,OAAAA,EAEXhB,EAAiBM,EAAKC,EAAME,EAAAA,kBAAkB,CAACC,iBAAiB,CACxE,CAaO,SAASnB,EAEde,CAAW,CACXC,CAAyC,EAEzC,MAFAA,KAFA,IAEAA,IAAAA,EAAqBlB,EAAAA,YAAY,CAAC2B,EAFP,KAEOA,EAE5BhB,EAAiBM,EAAKC,EAAME,EAAAA,kBAAkB,CAACQ,iBAAiB,CACxE,CAUO,SAASd,EAAwBvB,CAAc,QACpD,CAAKsC,EAAAA,CAAD,CAACA,eAAAA,EAAgBtC,GAIdA,EAAMgC,GAJgB,GAIV,CAACO,KAAK,CAAC,KAAKC,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,KAJb,IAKtC,CAEO,SAASnB,EAAyBtB,CAAoB,EAC3D,GAAI,CAACsC,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBtC,GACnB,KAD2B,CACrB,qBAAiC,CAAjC,MAAU,wBAAV,+DAAgC,GAGxC,OAAOA,EAAMgC,MAAM,CAACO,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAG/B,SAASlB,EAA+BrB,CAAoB,EACjE,GAAI,CAACsC,GAAAA,EAAAA,eAAAA,EAAgBtC,GACnB,KAD2B,CACrB,qBAAiC,CAAjC,MAAU,wBAAV,+DAAgC,GAGxC,OAAO0C,OAAO1C,EAAMgC,MAAM,CAACO,KAAK,CAAC,KAAKI,EAAE,CAAC,CAAC,GAC5C,0PCjGA,SACA,kDACA,QACI,0CACJ,yCAEA,0JGCA,6BACA,GACA,MAAQ,aACR,YACA,OAAW,CACX,CAAG,CACH,GAF0C,GAAG,CAAK,SAElD,2BACA,qBACA,6BACA,2BACA,2BACA,8BAIA,GACA,aACA,CAUA,iBAA0C,EAC1C,aACA,WACA,YAAoB,iBAA2B,IAC/C,qBAEA,CACA,+BACA,qCAEA,eACA,mBAEA,GADA,IACA,mCACA,qCAGA,2CACA,IACA,IACA,IACA,YAAkB,IAAS,KAC3B,oBACA,cACA,6CAEA,cACA,aACA,IACA,KACA,gBAEA,CACA,yBACA,4CAEA,SACA,EA/CA,OAEA,iBACA,CA6CA,iBAA4C,EAC5C,QAAU,MAAa,EACvB,gBACA,KACA,IACA,IACA,YAAkB,WAAiB,IAGnC,IAFA,gBACA,KACA,UACA,UACA,mBAMA,GAHA,GACA,4BAEA,EACA,wBACA,OAGA,SACA,EApEA,MAEA,EACA,GACA,yEACA,MACA,EAiEA,GACA,gBACA,gBACA,eACA,EACA,sBACA,GACA,QACA,QACA,OACA,EACA,iBACA,cACA,WACA,OACA,UACA,qCAA6C,EAAc,oBAAoB,YAAe,IAE9F,OACA,MAAY,UAAgC,CAC5C,UAEA,CAGA,SACA,6DAEA,UACA,gCACA,4BAEA,GADA,uBAIA,uBACA,mBACA,UAAgB,IAAsB,EACtC,OAAgB,GAA4B,gBAC5C,OAAgB,IAA4B,yBAC5C,4CAAqD,kBAAoB,wBAAwB,eACjG,GACU,IACH,CACP,MACI,SACJ,0BACA,UAAgB,IAAsB,EACtC,OAAgB,GAA4B,gBAC5C,OAAgB,IAA4B,yBAC5C,kDAA2D,kBAAoB,wBAAwB,eACvG,GACU,IACH,CAEP,CACA,EACA,MACA,eAGA,UACA,UAAc,IAAsB,EACpC,OAAc,GAA4B,gBAC1C,OAAc,IAA4B,cAC1C,4BAAmC,kBAAoB,mBAClD,CAEL,EACA,MACA,kBACA,UAAc,IAAsB,EACpC,OAAc,GAA4B,gBAC1C,OAAc,IAA4B,uBAC1C,iCAAwC,kBAAoB,eAAe,EAAK,GAC3E,CAEL,EACA,MACA,sBACA,UAAc,IAAsB,EACpC,OAAc,GAA4B,gBAC1C,OAAc,IAA4B,yBAC1C,0EAAiF,kBAAoB,GAChG,CAEL,EACA,UACA,uBAGA,eACA,UAAc,IAAsB,EACpC,OAAc,IAA4B,+BAC1C,oDAA2D,kBAAoB,cAAc,EAAkB,IAC1G,CAEL,EACA,UACA,sBACA,UAAc,IAAsB,EACpC,OAAc,GAA4B,gBAC1C,OAAc,IAA4B,yBAC1C,+CAAsD,kBAAoB,oBACrE,EAEL,2BACA,cAGA,GAFA,mBACA,2BAEA,UAAc,IAAsB,EACpC,OAAc,IAA4B,cAC1C,wCAA+C,gBAAyB,kBAAkB,gBAA0B,GAC/G,CAEL,EACA,UACA,cACA,OAEA,sBACA,UAAc,IAAsB,EACpC,OAAc,GAA4B,gBAC1C,OAAc,IAA4B,yBAC1C,mDAA0D,kBAAoB,oBACzE,EAEL,2BACA,cAGA,GAFA,mBACA,0BAEA,UAAc,IAAsB,EACpC,OAAc,IAA4B,mBAC1C,qFAA4F,kBAA8B,gBAAgB,iBAA2B,EAChK,CAEL,EACA,UACA,cACA,OAEA,sBACA,UAAc,IAAsB,EACpC,OAAc,GAA4B,gBAC1C,OAAc,IAA4B,yBAC1C,kDAAyD,kBAAoB,mBAC7E,CAAK,EAEL,2BACA,cAGA,GAFA,mBACA,0BAEA,UAAc,IAAsB,EACpC,OAAc,IAA4B,qBAC1C,4EAAmF,kBAA6B,gBAAgB,iBAA2B,CAC3J,CAAK,CAEL,EAcA,kBACA,sBACA,mDAEA,eAdA,GACA,4FACA,EAAkB,OAAc,IAEhC,iBADA,2BAEA,uBAA2C,IAAY,IACvD,qBAEA,QACA,EAKA,GACA,4BACA,8CACA,CAIA,sBACA,IAAU,4BAAyB,EAEnC,EADA,kBACA,uCACA,WACA,IACA,4BAEA,OAAa,KADb,0CACa,CACb,CAAI,SACJ,OACA,QACA,IAAY,IAAsB,EAClC,OAAkB,IAA4B,uBAC9C,kBACA,CAAS,EACT,CAEA,CACA,CACA,cACA,oCACA,gBACA,OACA,QACA,IAAY,IAAsB,EAClC,OAAkB,IAA4B,cAC9C,4EACA,CAAS,EACT,EAGA,aACA,kBACA,iCAAwE,SAAa,IACrF,iCAA0E,SAAa,IAavF,OAAW,KAXX,CACA,SACA,UACA,UAJA,WAAoD,SAAa,EAKjE,KACA,SACA,UACA,YACA,MACA,CACA,CACW,CACX,CACA,sBACA,aAAU,6CAAkD,EAC5D,KArDA,IAsDA,CAAU,iBAAwB,KAClC,KACA,cAAa,GAEb,WAAU,aAAkB,EAC5B,IACA,IAAY,aAAW,EACvB,KACA,KACA,QAAY,iCAA+B,EAC3C,KACA,WACA,OACA,OACA,OACA,MACA,CAAI,SACJ,OAAa,WACb,CACA,IAAU,iBAAgD,oBAC1D,EACA,CACA,QACA,IAAY,IAAsB,EAClC,OAAkB,GAA4B,gBAC9C,OAAkB,IAA4B,yBAC9C,0CAAqD,KAAmB,EAC/D,EAET,EAEA,EAUA,CAAW,QATX,CACA,QACA,IAAY,IAAsB,EAClC,OAAkB,IAA4B,uBAC9C,mCACA,CAAS,EACT,CAIA,uGCrWgBjC,qCAAAA,KAFhB,IAAMkC,EAAU,GAAEhD,EAjBX,OAiBWA,8BAA8B,CAAC,OAE1C,SAASc,IAEd,IAAMV,EAAQ,qBAAiB,CAAjB,MAAU4C,GAAV,+DAAgB,EAG9B,OAFE5C,EAAkCgC,MAAM,CAAGY,EAEvC5C,CACR,8SC3BI,EAAc,GAClB,wBADkB,EAClB,wBACA,QACI,0CACJ,wCAEA,YCKA,aAQA,iBAA8C,EAE9C,IADA,WACA,OACA,eACA,YACA,+NAGA,kBACA,0CAEA,WACA,CACA,iDACA,EAAoB,OAAc,kBAOlC,OANA,gBACA,WACA,aACI,6BACJ,YAA2B,UAAe,EAE1C,CACA,eACA,aACA,CACA,CACA,iBACA,IACA,oCAnCA,YAoCA,EAAuC,OAAc,oCACrD,WACA,CAAI,MACJ,QACA,CACA,CACA,aACA,cACA,OACA,sBACA,MACA,SAEA,sCACA,WAKA,OAJA,aACA,EAAc,IAAuB,wBACrC,YAEA,CACA,CACA,CACA,CAOA,cACA,sDACA,CAIA,+CACA,kCAGA,OAAS,EADT,sCADA,6BAEuB,sDACvB,CACA,aACA,GAAY,EAAW,GAAG,EAAa", "sources": ["webpack://next-shadcn-dashboard-starter/../../../src/client/components/unauthorized.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-7ELT755Q.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-I6MTSTOF.mjs", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/unstable-rethrow.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/url.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-BUNBAIZO.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/retry.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-UEY4AZIP.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/deprecated.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-E3NYSYOB.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/authorization.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-JJHTUJGL.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/pathToRegexp.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/chunk-H5XWF6TY.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/buildAccountsBaseUrl.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/authorization-errors.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/internal.mjs", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/forbidden.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-NNO3XJ5E.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/apiUrlFromPublishableKey.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/constants.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/utils/feature-flags.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-RWYTRAIK.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/lower-case@2.0.2/node_modules/lower-case/dist.es2015/index.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/no-case@3.0.4/node_modules/no-case/dist.es2015/index.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/dot-case@3.0.4/node_modules/dot-case/dist.es2015/index.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/snake-case@3.0.4/node_modules/snake-case/dist.es2015/index.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/underscore.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/headers-utils.js", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/unstable-rethrow.server.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-7HPDNZ3R.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/api/navigation.react-server.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/keys.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-3EORDU4Z.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/error.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/utils/index.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/utils/sdk-versions.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/map-obj@4.3.0/node_modules/map-obj/index.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/snakecase-keys@8.0.1/node_modules/snakecase-keys/index.js", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/navigation.react-server.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/chunk-5JS2VYLU.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/redirect.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-TETGTEI2.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/runtime/node/crypto.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/isomorphicAtob.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+backend@1.25.8_react_f4d84c1a2e512a789c906b9cc5b5d317/node_modules/@clerk/backend/dist/chunk-AT3FJU3M.mjs", "webpack://next-shadcn-dashboard-starter/../../../src/client/components/not-found.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-KOH7GTJO.mjs", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+shared@3.2.3_react-d_de67e44da943cf54d0431710a6f74b2d/node_modules/@clerk/shared/dist/chunk-G3VP5PJE.mjs"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n};\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg);\nvar __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj));\nvar __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);\nvar __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), setter ? setter.call(obj, value) : member.set(obj, value), value);\nvar __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method);\n\nexport {\n  __export,\n  __reExport,\n  __privateGet,\n  __privateAdd,\n  __privateSet,\n  __privateMethod\n};\n//# sourceMappingURL=chunk-7ELT755Q.mjs.map", "// src/constants.ts\nvar LEGACY_DEV_INSTANCE_SUFFIXES = [\".lcl.dev\", \".lclstage.dev\", \".lclclerk.com\"];\nvar CURRENT_DEV_INSTANCE_SUFFIXES = [\".accounts.dev\", \".accountsstage.dev\", \".accounts.lclclerk.com\"];\nvar DEV_OR_STAGING_SUFFIXES = [\n  \".lcl.dev\",\n  \".stg.dev\",\n  \".lclstage.dev\",\n  \".stgstage.dev\",\n  \".dev.lclclerk.com\",\n  \".stg.lclclerk.com\",\n  \".accounts.lclclerk.com\",\n  \"accountsstage.dev\",\n  \"accounts.dev\"\n];\nvar LOCAL_ENV_SUFFIXES = [\".lcl.dev\", \"lclstage.dev\", \".lclclerk.com\", \".accounts.lclclerk.com\"];\nvar STAGING_ENV_SUFFIXES = [\".accountsstage.dev\"];\nvar LOCAL_API_URL = \"https://api.lclclerk.com\";\nvar STAGING_API_URL = \"https://api.clerkstage.dev\";\nvar PROD_API_URL = \"https://api.clerk.com\";\nfunction iconImageUrl(id, format = \"svg\") {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n\nexport {\n  LEGACY_DEV_INSTANCE_SUFFIXES,\n  CURRENT_DEV_INSTANCE_SUFFIXES,\n  DEV_OR_STAGING_SUFFIXES,\n  LOCAL_ENV_SUFFIXES,\n  STAGING_ENV_SUFFIXES,\n  LOCAL_API_URL,\n  STAGING_API_URL,\n  PROD_API_URL,\n  iconImageUrl\n};\n//# sourceMappingURL=chunk-I6MTSTOF.mjs.map", "/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n", "import {\n  addClerkPrefix,\n  cleanDoubleSlashes,\n  getClerkJsMajorVersionOrTag,\n  getScriptUrl,\n  hasLeadingSlash,\n  hasTrailingSlash,\n  isAbsoluteUrl,\n  isCurrentDevAccountPortalOrigin,\n  isLegacyDevAccountPortalOrigin,\n  isNonEmptyURL,\n  joinURL,\n  parseSearchParams,\n  stripScheme,\n  withLeadingSlash,\n  withTrailingSlash,\n  withoutLeadingSlash,\n  withoutTrailingSlash\n} from \"./chunk-IFTVZ2LQ.mjs\";\nimport \"./chunk-3TMSNP4L.mjs\";\nimport \"./chunk-I6MTSTOF.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  addClerkPrefix,\n  cleanDoubleSlashes,\n  getClerkJsMajorVersionOrTag,\n  getScriptUrl,\n  hasLeadingSlash,\n  hasTrailingSlash,\n  isAbsoluteUrl,\n  isCurrentDevAccountPortalOrigin,\n  isLegacyDevAccountPortalOrigin,\n  isNonEmptyURL,\n  joinURL,\n  parseSearchParams,\n  stripScheme,\n  withLeadingSlash,\n  withTrailingSlash,\n  withoutLeadingSlash,\n  withoutTrailingSlash\n};\n//# sourceMappingURL=url.mjs.map", "// src/retry.ts\nvar defaultOptions = {\n  initialDelay: 125,\n  maxDelayBetweenRetries: 0,\n  factor: 2,\n  shouldRetry: (_, iteration) => iteration < 5,\n  retryImmediately: true,\n  jitter: true\n};\nvar RETRY_IMMEDIATELY_DELAY = 100;\nvar sleep = async (ms) => new Promise((s) => setTimeout(s, ms));\nvar applyJitter = (delay, jitter) => {\n  return jitter ? delay * (1 + Math.random()) : delay;\n};\nvar createExponentialDelayAsyncFn = (opts) => {\n  let timesCalled = 0;\n  const calculateDelayInMs = () => {\n    const constant = opts.initialDelay;\n    const base = opts.factor;\n    let delay = constant * Math.pow(base, timesCalled);\n    delay = applyJitter(delay, opts.jitter);\n    return Math.min(opts.maxDelayBetweenRetries || delay, delay);\n  };\n  return async () => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\nvar retry = async (callback, options = {}) => {\n  let iterations = 0;\n  const { shouldRetry, initialDelay, maxDelayBetweenRetries, factor, retryImmediately, jitter } = {\n    ...defaultOptions,\n    ...options\n  };\n  const delay = createExponentialDelayAsyncFn({\n    initialDelay,\n    maxDelayBetweenRetries,\n    factor,\n    jitter\n  });\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterations++;\n      if (!shouldRetry(e, iterations)) {\n        throw e;\n      }\n      if (retryImmediately && iterations === 1) {\n        await sleep(applyJitter(RETRY_IMMEDIATELY_DELAY, jitter));\n      } else {\n        await delay();\n      }\n    }\n  }\n};\n\nexport {\n  retry\n};\n//# sourceMappingURL=chunk-BUNBAIZO.mjs.map", "import {\n  retry\n} from \"./chunk-BUNBAIZO.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  retry\n};\n//# sourceMappingURL=retry.mjs.map", "import {\n  isProductionEnvironment,\n  isTestEnvironment\n} from \"./chunk-7HPDNZ3R.mjs\";\n\n// src/deprecated.ts\nvar displayedWarnings = /* @__PURE__ */ new Set();\nvar deprecated = (fnName, warning, key) => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\n${warning}`\n  );\n};\nvar deprecatedProperty = (cls, propName, warning, isStatic = false) => {\n  const target = isStatic ? cls : cls.prototype;\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v) {\n      value = v;\n    }\n  });\n};\nvar deprecatedObjectProperty = (obj, propName, warning, key) => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v) {\n      value = v;\n    }\n  });\n};\n\nexport {\n  deprecated,\n  deprecatedProperty,\n  deprecatedObjectProperty\n};\n//# sourceMappingURL=chunk-UEY4AZIP.mjs.map", "import {\n  deprecated,\n  deprecatedObjectProperty,\n  deprecatedProperty\n} from \"./chunk-UEY4AZIP.mjs\";\nimport \"./chunk-7HPDNZ3R.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  deprecated,\n  deprecatedObjectProperty,\n  deprecatedProperty\n};\n//# sourceMappingURL=deprecated.mjs.map", "// src/authorization.ts\nvar TYPES_TO_OBJECTS = {\n  strict_mfa: {\n    afterMinutes: 10,\n    level: \"multi_factor\"\n  },\n  strict: {\n    afterMinutes: 10,\n    level: \"second_factor\"\n  },\n  moderate: {\n    afterMinutes: 60,\n    level: \"second_factor\"\n  },\n  lax: {\n    afterMinutes: 1440,\n    level: \"second_factor\"\n  }\n};\nvar ALLOWED_LEVELS = /* @__PURE__ */ new Set([\"first_factor\", \"second_factor\", \"multi_factor\"]);\nvar ALLOWED_TYPES = /* @__PURE__ */ new Set([\"strict_mfa\", \"strict\", \"moderate\", \"lax\"]);\nvar isValidMaxAge = (maxAge) => typeof maxAge === \"number\" && maxAge > 0;\nvar isValidLevel = (level) => ALLOWED_LEVELS.has(level);\nvar isValidVerificationType = (type) => ALLOWED_TYPES.has(type);\nvar checkOrgAuthorization = (params, options) => {\n  const { orgId, orgRole, orgPermissions } = options;\n  if (!params.role && !params.permission) {\n    return null;\n  }\n  if (!orgId || !orgRole || !orgPermissions) {\n    return null;\n  }\n  if (params.permission) {\n    return orgPermissions.includes(params.permission);\n  }\n  if (params.role) {\n    return orgRole === params.role;\n  }\n  return null;\n};\nvar validateReverificationConfig = (config) => {\n  if (!config) {\n    return false;\n  }\n  const convertConfigToObject = (config2) => {\n    if (typeof config2 === \"string\") {\n      return TYPES_TO_OBJECTS[config2];\n    }\n    return config2;\n  };\n  const isValidStringValue = typeof config === \"string\" && isValidVerificationType(config);\n  const isValidObjectValue = typeof config === \"object\" && isValidLevel(config.level) && isValidMaxAge(config.afterMinutes);\n  if (isValidStringValue || isValidObjectValue) {\n    return convertConfigToObject.bind(null, config);\n  }\n  return false;\n};\nvar checkReverificationAuthorization = (params, { factorVerificationAge }) => {\n  if (!params.reverification || !factorVerificationAge) {\n    return null;\n  }\n  const isValidReverification = validateReverificationConfig(params.reverification);\n  if (!isValidReverification) {\n    return null;\n  }\n  const { level, afterMinutes } = isValidReverification();\n  const [factor1Age, factor2Age] = factorVerificationAge;\n  const isValidFactor1 = factor1Age !== -1 ? afterMinutes > factor1Age : null;\n  const isValidFactor2 = factor2Age !== -1 ? afterMinutes > factor2Age : null;\n  switch (level) {\n    case \"first_factor\":\n      return isValidFactor1;\n    case \"second_factor\":\n      return factor2Age !== -1 ? isValidFactor2 : isValidFactor1;\n    case \"multi_factor\":\n      return factor2Age === -1 ? isValidFactor1 : isValidFactor1 && isValidFactor2;\n  }\n};\nvar createCheckAuthorization = (options) => {\n  return (params) => {\n    if (!options.userId) {\n      return false;\n    }\n    const orgAuthorization = checkOrgAuthorization(params, options);\n    const reverificationAuthorization = checkReverificationAuthorization(params, options);\n    if ([orgAuthorization, reverificationAuthorization].some((a) => a === null)) {\n      return [orgAuthorization, reverificationAuthorization].some((a) => a === true);\n    }\n    return [orgAuthorization, reverificationAuthorization].every((a) => a === true);\n  };\n};\n\nexport {\n  validateReverificationConfig,\n  createCheckAuthorization\n};\n//# sourceMappingURL=chunk-E3NYSYOB.mjs.map", "import {\n  createCheckAuthorization,\n  validateReverificationConfig\n} from \"./chunk-E3NYSYOB.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  createCheckAuthorization,\n  validateReverificationConfig\n};\n//# sourceMappingURL=authorization.mjs.map", "// src/compiled/path-to-regexp/index.js\nfunction _(r) {\n  for (var n = [], e = 0; e < r.length; ) {\n    var a = r[e];\n    if (a === \"*\" || a === \"+\" || a === \"?\") {\n      n.push({\n        type: \"MODIFIER\",\n        index: e,\n        value: r[e++]\n      });\n      continue;\n    }\n    if (a === \"\\\\\") {\n      n.push({\n        type: \"ESCAPED_CHAR\",\n        index: e++,\n        value: r[e++]\n      });\n      continue;\n    }\n    if (a === \"{\") {\n      n.push({\n        type: \"OPEN\",\n        index: e,\n        value: r[e++]\n      });\n      continue;\n    }\n    if (a === \"}\") {\n      n.push({\n        type: \"CLOSE\",\n        index: e,\n        value: r[e++]\n      });\n      continue;\n    }\n    if (a === \":\") {\n      for (var u = \"\", t = e + 1; t < r.length; ) {\n        var c = r.charCodeAt(t);\n        if (c >= 48 && c <= 57 || c >= 65 && c <= 90 || c >= 97 && c <= 122 || c === 95) {\n          u += r[t++];\n          continue;\n        }\n        break;\n      }\n      if (!u) throw new TypeError(\"Missing parameter name at \".concat(e));\n      n.push({\n        type: \"NAME\",\n        index: e,\n        value: u\n      }), e = t;\n      continue;\n    }\n    if (a === \"(\") {\n      var o = 1, m = \"\", t = e + 1;\n      if (r[t] === \"?\") throw new TypeError('Pattern cannot start with \"?\" at '.concat(t));\n      for (; t < r.length; ) {\n        if (r[t] === \"\\\\\") {\n          m += r[t++] + r[t++];\n          continue;\n        }\n        if (r[t] === \")\") {\n          if (o--, o === 0) {\n            t++;\n            break;\n          }\n        } else if (r[t] === \"(\" && (o++, r[t + 1] !== \"?\"))\n          throw new TypeError(\"Capturing groups are not allowed at \".concat(t));\n        m += r[t++];\n      }\n      if (o) throw new TypeError(\"Unbalanced pattern at \".concat(e));\n      if (!m) throw new TypeError(\"Missing pattern at \".concat(e));\n      n.push({\n        type: \"PATTERN\",\n        index: e,\n        value: m\n      }), e = t;\n      continue;\n    }\n    n.push({\n      type: \"CHAR\",\n      index: e,\n      value: r[e++]\n    });\n  }\n  return n.push({\n    type: \"END\",\n    index: e,\n    value: \"\"\n  }), n;\n}\nfunction F(r, n) {\n  n === void 0 && (n = {});\n  for (var e = _(r), a = n.prefixes, u = a === void 0 ? \"./\" : a, t = n.delimiter, c = t === void 0 ? \"/#?\" : t, o = [], m = 0, h = 0, p = \"\", f = function(l) {\n    if (h < e.length && e[h].type === l) return e[h++].value;\n  }, w = function(l) {\n    var v = f(l);\n    if (v !== void 0) return v;\n    var E = e[h], N = E.type, S = E.index;\n    throw new TypeError(\"Unexpected \".concat(N, \" at \").concat(S, \", expected \").concat(l));\n  }, d = function() {\n    for (var l = \"\", v; v = f(\"CHAR\") || f(\"ESCAPED_CHAR\"); ) l += v;\n    return l;\n  }, M = function(l) {\n    for (var v = 0, E = c; v < E.length; v++) {\n      var N = E[v];\n      if (l.indexOf(N) > -1) return true;\n    }\n    return false;\n  }, A = function(l) {\n    var v = o[o.length - 1], E = l || (v && typeof v == \"string\" ? v : \"\");\n    if (v && !E)\n      throw new TypeError('Must have text between two parameters, missing text after \"'.concat(v.name, '\"'));\n    return !E || M(E) ? \"[^\".concat(s(c), \"]+?\") : \"(?:(?!\".concat(s(E), \")[^\").concat(s(c), \"])+?\");\n  }; h < e.length; ) {\n    var T = f(\"CHAR\"), x = f(\"NAME\"), C = f(\"PATTERN\");\n    if (x || C) {\n      var g = T || \"\";\n      u.indexOf(g) === -1 && (p += g, g = \"\"), p && (o.push(p), p = \"\"), o.push({\n        name: x || m++,\n        prefix: g,\n        suffix: \"\",\n        pattern: C || A(g),\n        modifier: f(\"MODIFIER\") || \"\"\n      });\n      continue;\n    }\n    var i = T || f(\"ESCAPED_CHAR\");\n    if (i) {\n      p += i;\n      continue;\n    }\n    p && (o.push(p), p = \"\");\n    var R = f(\"OPEN\");\n    if (R) {\n      var g = d(), y = f(\"NAME\") || \"\", O = f(\"PATTERN\") || \"\", b = d();\n      w(\"CLOSE\"), o.push({\n        name: y || (O ? m++ : \"\"),\n        pattern: y && !O ? A(g) : O,\n        prefix: g,\n        suffix: b,\n        modifier: f(\"MODIFIER\") || \"\"\n      });\n      continue;\n    }\n    w(\"END\");\n  }\n  return o;\n}\nfunction H(r, n) {\n  var e = [], a = P(r, e, n);\n  return I(a, e, n);\n}\nfunction I(r, n, e) {\n  e === void 0 && (e = {});\n  var a = e.decode, u = a === void 0 ? function(t) {\n    return t;\n  } : a;\n  return function(t) {\n    var c = r.exec(t);\n    if (!c) return false;\n    for (var o = c[0], m = c.index, h = /* @__PURE__ */ Object.create(null), p = function(w) {\n      if (c[w] === void 0) return \"continue\";\n      var d = n[w - 1];\n      d.modifier === \"*\" || d.modifier === \"+\" ? h[d.name] = c[w].split(d.prefix + d.suffix).map(function(M) {\n        return u(M, d);\n      }) : h[d.name] = u(c[w], d);\n    }, f = 1; f < c.length; f++)\n      p(f);\n    return {\n      path: o,\n      index: m,\n      params: h\n    };\n  };\n}\nfunction s(r) {\n  return r.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, \"\\\\$1\");\n}\nfunction D(r) {\n  return r && r.sensitive ? \"\" : \"i\";\n}\nfunction $(r, n) {\n  if (!n) return r;\n  for (var e = /\\((?:\\?<(.*?)>)?(?!\\?)/g, a = 0, u = e.exec(r.source); u; )\n    n.push({\n      name: u[1] || a++,\n      prefix: \"\",\n      suffix: \"\",\n      modifier: \"\",\n      pattern: \"\"\n    }), u = e.exec(r.source);\n  return r;\n}\nfunction W(r, n, e) {\n  var a = r.map(function(u) {\n    return P(u, n, e).source;\n  });\n  return new RegExp(\"(?:\".concat(a.join(\"|\"), \")\"), D(e));\n}\nfunction L(r, n, e) {\n  return U(F(r, e), n, e);\n}\nfunction U(r, n, e) {\n  e === void 0 && (e = {});\n  for (var a = e.strict, u = a === void 0 ? false : a, t = e.start, c = t === void 0 ? true : t, o = e.end, m = o === void 0 ? true : o, h = e.encode, p = h === void 0 ? function(v) {\n    return v;\n  } : h, f = e.delimiter, w = f === void 0 ? \"/#?\" : f, d = e.endsWith, M = d === void 0 ? \"\" : d, A = \"[\".concat(s(M), \"]|$\"), T = \"[\".concat(s(w), \"]\"), x = c ? \"^\" : \"\", C = 0, g = r; C < g.length; C++) {\n    var i = g[C];\n    if (typeof i == \"string\") x += s(p(i));\n    else {\n      var R = s(p(i.prefix)), y = s(p(i.suffix));\n      if (i.pattern)\n        if (n && n.push(i), R || y)\n          if (i.modifier === \"+\" || i.modifier === \"*\") {\n            var O = i.modifier === \"*\" ? \"?\" : \"\";\n            x += \"(?:\".concat(R, \"((?:\").concat(i.pattern, \")(?:\").concat(y).concat(R, \"(?:\").concat(i.pattern, \"))*)\").concat(y, \")\").concat(O);\n          } else x += \"(?:\".concat(R, \"(\").concat(i.pattern, \")\").concat(y, \")\").concat(i.modifier);\n        else {\n          if (i.modifier === \"+\" || i.modifier === \"*\")\n            throw new TypeError('Can not repeat \"'.concat(i.name, '\" without a prefix and suffix'));\n          x += \"(\".concat(i.pattern, \")\").concat(i.modifier);\n        }\n      else x += \"(?:\".concat(R).concat(y, \")\").concat(i.modifier);\n    }\n  }\n  if (m) u || (x += \"\".concat(T, \"?\")), x += e.endsWith ? \"(?=\".concat(A, \")\") : \"$\";\n  else {\n    var b = r[r.length - 1], l = typeof b == \"string\" ? T.indexOf(b[b.length - 1]) > -1 : b === void 0;\n    u || (x += \"(?:\".concat(T, \"(?=\").concat(A, \"))?\")), l || (x += \"(?=\".concat(T, \"|\").concat(A, \")\"));\n  }\n  return new RegExp(x, D(e));\n}\nfunction P(r, n, e) {\n  return r instanceof RegExp ? $(r, n) : Array.isArray(r) ? W(r, n, e) : L(r, n, e);\n}\n\n// src/pathToRegexp.ts\nvar pathToRegexp = (path) => {\n  try {\n    return P(path);\n  } catch (e) {\n    throw new Error(\n      `Invalid path: ${path}.\nConsult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\n${e.message}`\n    );\n  }\n};\nfunction match(str, options) {\n  try {\n    return H(str, options);\n  } catch (e) {\n    throw new Error(\n      `Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x\n${e.message}`\n    );\n  }\n}\n\nexport {\n  pathToRegexp,\n  match\n};\n//# sourceMappingURL=chunk-JJHTUJGL.mjs.map", "import {\n  match,\n  pathToRegexp\n} from \"./chunk-JJHTUJGL.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  match,\n  pathToRegexp\n};\n//# sourceMappingURL=pathToRegexp.mjs.map", "import {\n  assertHeaderAlgorithm,\n  assertHeaderType,\n  decodeJwt,\n  hasValidSignature,\n  runtime,\n  verifyJwt\n} from \"./chunk-AT3FJU3M.mjs\";\nimport {\n  TokenVerificationError,\n  TokenVerificationErrorAction,\n  TokenVerificationErrorCode,\n  TokenVerificationErrorReason\n} from \"./chunk-5JS2VYLU.mjs\";\n\n// src/constants.ts\nvar API_URL = \"https://api.clerk.com\";\nvar API_VERSION = \"v1\";\nvar USER_AGENT = `${\"@clerk/backend\"}@${\"1.25.8\"}`;\nvar MAX_CACHE_LAST_UPDATED_AT_SECONDS = 5 * 60;\nvar SUPPORTED_BAPI_VERSION = \"2024-10-01\";\nvar Attributes = {\n  AuthToken: \"__clerkAuthToken\",\n  AuthSignature: \"__clerkAuthSignature\",\n  AuthStatus: \"__clerkAuthStatus\",\n  AuthReason: \"__clerkAuthReason\",\n  AuthMessage: \"__clerkAuthMessage\",\n  ClerkUrl: \"__clerkUrl\"\n};\nvar Cookies = {\n  Session: \"__session\",\n  Refresh: \"__refresh\",\n  ClientUat: \"__client_uat\",\n  Handshake: \"__clerk_handshake\",\n  DevBrowser: \"__clerk_db_jwt\",\n  RedirectCount: \"__clerk_redirect_count\"\n};\nvar QueryParameters = {\n  ClerkSynced: \"__clerk_synced\",\n  SuffixedCookies: \"suffixed_cookies\",\n  ClerkRedirectUrl: \"__clerk_redirect_url\",\n  // use the reference to Cookies to indicate that it's the same value\n  DevBrowser: Cookies.DevBrowser,\n  Handshake: Cookies.Handshake,\n  HandshakeHelp: \"__clerk_help\",\n  LegacyDevBrowser: \"__dev_session\",\n  HandshakeReason: \"__clerk_hs_reason\"\n};\nvar Headers2 = {\n  AuthToken: \"x-clerk-auth-token\",\n  AuthSignature: \"x-clerk-auth-signature\",\n  AuthStatus: \"x-clerk-auth-status\",\n  AuthReason: \"x-clerk-auth-reason\",\n  AuthMessage: \"x-clerk-auth-message\",\n  ClerkUrl: \"x-clerk-clerk-url\",\n  EnableDebug: \"x-clerk-debug\",\n  ClerkRequestData: \"x-clerk-request-data\",\n  ClerkRedirectTo: \"x-clerk-redirect-to\",\n  CloudFrontForwardedProto: \"cloudfront-forwarded-proto\",\n  Authorization: \"authorization\",\n  ForwardedPort: \"x-forwarded-port\",\n  ForwardedProto: \"x-forwarded-proto\",\n  ForwardedHost: \"x-forwarded-host\",\n  Accept: \"accept\",\n  Referrer: \"referer\",\n  UserAgent: \"user-agent\",\n  Origin: \"origin\",\n  Host: \"host\",\n  ContentType: \"content-type\",\n  SecFetchDest: \"sec-fetch-dest\",\n  Location: \"location\",\n  CacheControl: \"cache-control\"\n};\nvar ContentTypes = {\n  Json: \"application/json\"\n};\nvar constants = {\n  Attributes,\n  Cookies,\n  Headers: Headers2,\n  ContentTypes,\n  QueryParameters\n};\n\n// src/util/path.ts\nvar SEPARATOR = \"/\";\nvar MULTIPLE_SEPARATOR_REGEX = new RegExp(\"(?<!:)\" + SEPARATOR + \"{1,}\", \"g\");\nfunction joinPaths(...args) {\n  return args.filter((p) => p).join(SEPARATOR).replace(MULTIPLE_SEPARATOR_REGEX, SEPARATOR);\n}\n\n// src/api/endpoints/AbstractApi.ts\nvar AbstractAPI = class {\n  constructor(request) {\n    this.request = request;\n  }\n  requireId(id) {\n    if (!id) {\n      throw new Error(\"A valid resource ID is required.\");\n    }\n  }\n};\n\n// src/api/endpoints/AccountlessApplicationsAPI.ts\nvar basePath = \"/accountless_applications\";\nvar AccountlessApplicationAPI = class extends AbstractAPI {\n  async createAccountlessApplication() {\n    return this.request({\n      method: \"POST\",\n      path: basePath\n    });\n  }\n  async completeAccountlessApplicationOnboarding() {\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath, \"complete\")\n    });\n  }\n};\n\n// src/api/endpoints/AllowlistIdentifierApi.ts\nvar basePath2 = \"/allowlist_identifiers\";\nvar AllowlistIdentifierAPI = class extends AbstractAPI {\n  async getAllowlistIdentifierList() {\n    return this.request({\n      method: \"GET\",\n      path: basePath2,\n      queryParams: { paginated: true }\n    });\n  }\n  async createAllowlistIdentifier(params) {\n    return this.request({\n      method: \"POST\",\n      path: basePath2,\n      bodyParams: params\n    });\n  }\n  async deleteAllowlistIdentifier(allowlistIdentifierId) {\n    this.requireId(allowlistIdentifierId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath2, allowlistIdentifierId)\n    });\n  }\n};\n\n// src/api/endpoints/ClientApi.ts\nvar basePath3 = \"/clients\";\nvar ClientAPI = class extends AbstractAPI {\n  async getClientList(params = {}) {\n    return this.request({\n      method: \"GET\",\n      path: basePath3,\n      queryParams: { ...params, paginated: true }\n    });\n  }\n  async getClient(clientId) {\n    this.requireId(clientId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath3, clientId)\n    });\n  }\n  verifyClient(token) {\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath3, \"verify\"),\n      bodyParams: { token }\n    });\n  }\n};\n\n// src/api/endpoints/DomainApi.ts\nvar basePath4 = \"/domains\";\nvar DomainAPI = class extends AbstractAPI {\n  async deleteDomain(id) {\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath4, id)\n    });\n  }\n};\n\n// src/api/endpoints/EmailAddressApi.ts\nvar basePath5 = \"/email_addresses\";\nvar EmailAddressAPI = class extends AbstractAPI {\n  async getEmailAddress(emailAddressId) {\n    this.requireId(emailAddressId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath5, emailAddressId)\n    });\n  }\n  async createEmailAddress(params) {\n    return this.request({\n      method: \"POST\",\n      path: basePath5,\n      bodyParams: params\n    });\n  }\n  async updateEmailAddress(emailAddressId, params = {}) {\n    this.requireId(emailAddressId);\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath5, emailAddressId),\n      bodyParams: params\n    });\n  }\n  async deleteEmailAddress(emailAddressId) {\n    this.requireId(emailAddressId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath5, emailAddressId)\n    });\n  }\n};\n\n// src/api/endpoints/InvitationApi.ts\nvar basePath6 = \"/invitations\";\nvar InvitationAPI = class extends AbstractAPI {\n  async getInvitationList(params = {}) {\n    return this.request({\n      method: \"GET\",\n      path: basePath6,\n      queryParams: { ...params, paginated: true }\n    });\n  }\n  async createInvitation(params) {\n    return this.request({\n      method: \"POST\",\n      path: basePath6,\n      bodyParams: params\n    });\n  }\n  async revokeInvitation(invitationId) {\n    this.requireId(invitationId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath6, invitationId, \"revoke\")\n    });\n  }\n};\n\n// src/api/endpoints/OrganizationApi.ts\nvar basePath7 = \"/organizations\";\nvar OrganizationAPI = class extends AbstractAPI {\n  async getOrganizationList(params) {\n    return this.request({\n      method: \"GET\",\n      path: basePath7,\n      queryParams: params\n    });\n  }\n  async createOrganization(params) {\n    return this.request({\n      method: \"POST\",\n      path: basePath7,\n      bodyParams: params\n    });\n  }\n  async getOrganization(params) {\n    const { includeMembersCount } = params;\n    const organizationIdOrSlug = \"organizationId\" in params ? params.organizationId : params.slug;\n    this.requireId(organizationIdOrSlug);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath7, organizationIdOrSlug),\n      queryParams: {\n        includeMembersCount\n      }\n    });\n  }\n  async updateOrganization(organizationId, params) {\n    this.requireId(organizationId);\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath7, organizationId),\n      bodyParams: params\n    });\n  }\n  async updateOrganizationLogo(organizationId, params) {\n    this.requireId(organizationId);\n    const formData = new runtime.FormData();\n    formData.append(\"file\", params?.file);\n    if (params?.uploaderUserId) {\n      formData.append(\"uploader_user_id\", params?.uploaderUserId);\n    }\n    return this.request({\n      method: \"PUT\",\n      path: joinPaths(basePath7, organizationId, \"logo\"),\n      formData\n    });\n  }\n  async deleteOrganizationLogo(organizationId) {\n    this.requireId(organizationId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath7, organizationId, \"logo\")\n    });\n  }\n  async updateOrganizationMetadata(organizationId, params) {\n    this.requireId(organizationId);\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath7, organizationId, \"metadata\"),\n      bodyParams: params\n    });\n  }\n  async deleteOrganization(organizationId) {\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath7, organizationId)\n    });\n  }\n  async getOrganizationMembershipList(params) {\n    const { organizationId, ...queryParams } = params;\n    this.requireId(organizationId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath7, organizationId, \"memberships\"),\n      queryParams\n    });\n  }\n  async createOrganizationMembership(params) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath7, organizationId, \"memberships\"),\n      bodyParams\n    });\n  }\n  async updateOrganizationMembership(params) {\n    const { organizationId, userId, ...bodyParams } = params;\n    this.requireId(organizationId);\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath7, organizationId, \"memberships\", userId),\n      bodyParams\n    });\n  }\n  async updateOrganizationMembershipMetadata(params) {\n    const { organizationId, userId, ...bodyParams } = params;\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath7, organizationId, \"memberships\", userId, \"metadata\"),\n      bodyParams\n    });\n  }\n  async deleteOrganizationMembership(params) {\n    const { organizationId, userId } = params;\n    this.requireId(organizationId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath7, organizationId, \"memberships\", userId)\n    });\n  }\n  async getOrganizationInvitationList(params) {\n    const { organizationId, ...queryParams } = params;\n    this.requireId(organizationId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath7, organizationId, \"invitations\"),\n      queryParams\n    });\n  }\n  async createOrganizationInvitation(params) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath7, organizationId, \"invitations\"),\n      bodyParams\n    });\n  }\n  async getOrganizationInvitation(params) {\n    const { organizationId, invitationId } = params;\n    this.requireId(organizationId);\n    this.requireId(invitationId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath7, organizationId, \"invitations\", invitationId)\n    });\n  }\n  async revokeOrganizationInvitation(params) {\n    const { organizationId, invitationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath7, organizationId, \"invitations\", invitationId, \"revoke\"),\n      bodyParams\n    });\n  }\n  async getOrganizationDomainList(params) {\n    const { organizationId, ...queryParams } = params;\n    this.requireId(organizationId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath7, organizationId, \"domains\"),\n      queryParams\n    });\n  }\n  async createOrganizationDomain(params) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath7, organizationId, \"domains\"),\n      bodyParams: {\n        ...bodyParams,\n        verified: bodyParams.verified ?? true\n      }\n    });\n  }\n  async updateOrganizationDomain(params) {\n    const { organizationId, domainId, ...bodyParams } = params;\n    this.requireId(organizationId);\n    this.requireId(domainId);\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath7, organizationId, \"domains\", domainId),\n      bodyParams\n    });\n  }\n  async deleteOrganizationDomain(params) {\n    const { organizationId, domainId } = params;\n    this.requireId(organizationId);\n    this.requireId(domainId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath7, organizationId, \"domains\", domainId)\n    });\n  }\n};\n\n// src/api/endpoints/PhoneNumberApi.ts\nvar basePath8 = \"/phone_numbers\";\nvar PhoneNumberAPI = class extends AbstractAPI {\n  async getPhoneNumber(phoneNumberId) {\n    this.requireId(phoneNumberId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath8, phoneNumberId)\n    });\n  }\n  async createPhoneNumber(params) {\n    return this.request({\n      method: \"POST\",\n      path: basePath8,\n      bodyParams: params\n    });\n  }\n  async updatePhoneNumber(phoneNumberId, params = {}) {\n    this.requireId(phoneNumberId);\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath8, phoneNumberId),\n      bodyParams: params\n    });\n  }\n  async deletePhoneNumber(phoneNumberId) {\n    this.requireId(phoneNumberId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath8, phoneNumberId)\n    });\n  }\n};\n\n// src/api/endpoints/RedirectUrlApi.ts\nvar basePath9 = \"/redirect_urls\";\nvar RedirectUrlAPI = class extends AbstractAPI {\n  async getRedirectUrlList() {\n    return this.request({\n      method: \"GET\",\n      path: basePath9,\n      queryParams: { paginated: true }\n    });\n  }\n  async getRedirectUrl(redirectUrlId) {\n    this.requireId(redirectUrlId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath9, redirectUrlId)\n    });\n  }\n  async createRedirectUrl(params) {\n    return this.request({\n      method: \"POST\",\n      path: basePath9,\n      bodyParams: params\n    });\n  }\n  async deleteRedirectUrl(redirectUrlId) {\n    this.requireId(redirectUrlId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath9, redirectUrlId)\n    });\n  }\n};\n\n// src/api/endpoints/SessionApi.ts\nvar basePath10 = \"/sessions\";\nvar SessionAPI = class extends AbstractAPI {\n  async getSessionList(params = {}) {\n    return this.request({\n      method: \"GET\",\n      path: basePath10,\n      queryParams: { ...params, paginated: true }\n    });\n  }\n  async getSession(sessionId) {\n    this.requireId(sessionId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath10, sessionId)\n    });\n  }\n  async revokeSession(sessionId) {\n    this.requireId(sessionId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath10, sessionId, \"revoke\")\n    });\n  }\n  async verifySession(sessionId, token) {\n    this.requireId(sessionId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath10, sessionId, \"verify\"),\n      bodyParams: { token }\n    });\n  }\n  async getToken(sessionId, template) {\n    this.requireId(sessionId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath10, sessionId, \"tokens\", template || \"\")\n    });\n  }\n  async refreshSession(sessionId, params) {\n    this.requireId(sessionId);\n    const { suffixed_cookies, ...restParams } = params;\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath10, sessionId, \"refresh\"),\n      bodyParams: restParams,\n      queryParams: { suffixed_cookies }\n    });\n  }\n};\n\n// src/api/endpoints/SignInTokenApi.ts\nvar basePath11 = \"/sign_in_tokens\";\nvar SignInTokenAPI = class extends AbstractAPI {\n  async createSignInToken(params) {\n    return this.request({\n      method: \"POST\",\n      path: basePath11,\n      bodyParams: params\n    });\n  }\n  async revokeSignInToken(signInTokenId) {\n    this.requireId(signInTokenId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath11, signInTokenId, \"revoke\")\n    });\n  }\n};\n\n// src/util/shared.ts\nimport { addClerkPrefix, getScriptUrl, getClerkJsMajorVersionOrTag } from \"@clerk/shared/url\";\nimport { retry } from \"@clerk/shared/retry\";\nimport {\n  isDevelopmentFromSecretKey,\n  isProductionFromSecretKey,\n  parsePublishableKey,\n  getCookieSuffix,\n  getSuffixedCookieName\n} from \"@clerk/shared/keys\";\nimport { deprecated, deprecatedProperty } from \"@clerk/shared/deprecated\";\nimport { buildErrorThrower } from \"@clerk/shared/error\";\nimport { createDevOrStagingUrlCache } from \"@clerk/shared/keys\";\nvar errorThrower = buildErrorThrower({ packageName: \"@clerk/backend\" });\nvar { isDevOrStagingUrl } = createDevOrStagingUrlCache();\n\n// src/api/endpoints/UserApi.ts\nvar basePath12 = \"/users\";\nvar UserAPI = class extends AbstractAPI {\n  async getUserList(params = {}) {\n    const { limit, offset, orderBy, ...userCountParams } = params;\n    const [data, totalCount] = await Promise.all([\n      this.request({\n        method: \"GET\",\n        path: basePath12,\n        queryParams: params\n      }),\n      this.getCount(userCountParams)\n    ]);\n    return { data, totalCount };\n  }\n  async getUser(userId) {\n    this.requireId(userId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath12, userId)\n    });\n  }\n  async createUser(params) {\n    return this.request({\n      method: \"POST\",\n      path: basePath12,\n      bodyParams: params\n    });\n  }\n  async updateUser(userId, params = {}) {\n    this.requireId(userId);\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath12, userId),\n      bodyParams: params\n    });\n  }\n  async updateUserProfileImage(userId, params) {\n    this.requireId(userId);\n    const formData = new runtime.FormData();\n    formData.append(\"file\", params?.file);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath12, userId, \"profile_image\"),\n      formData\n    });\n  }\n  async updateUserMetadata(userId, params) {\n    this.requireId(userId);\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath12, userId, \"metadata\"),\n      bodyParams: params\n    });\n  }\n  async deleteUser(userId) {\n    this.requireId(userId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath12, userId)\n    });\n  }\n  async getCount(params = {}) {\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath12, \"count\"),\n      queryParams: params\n    });\n  }\n  async getUserOauthAccessToken(userId, provider) {\n    this.requireId(userId);\n    const hasPrefix = provider.startsWith(\"oauth_\");\n    const _provider = hasPrefix ? provider : `oauth_${provider}`;\n    if (hasPrefix) {\n      deprecated(\n        \"getUserOauthAccessToken(userId, provider)\",\n        \"Remove the `oauth_` prefix from the `provider` argument.\"\n      );\n    }\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath12, userId, \"oauth_access_tokens\", _provider),\n      queryParams: { paginated: true }\n    });\n  }\n  async disableUserMFA(userId) {\n    this.requireId(userId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath12, userId, \"mfa\")\n    });\n  }\n  async getOrganizationMembershipList(params) {\n    const { userId, limit, offset } = params;\n    this.requireId(userId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath12, userId, \"organization_memberships\"),\n      queryParams: { limit, offset }\n    });\n  }\n  async verifyPassword(params) {\n    const { userId, password } = params;\n    this.requireId(userId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath12, userId, \"verify_password\"),\n      bodyParams: { password }\n    });\n  }\n  async verifyTOTP(params) {\n    const { userId, code } = params;\n    this.requireId(userId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath12, userId, \"verify_totp\"),\n      bodyParams: { code }\n    });\n  }\n  async banUser(userId) {\n    this.requireId(userId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath12, userId, \"ban\")\n    });\n  }\n  async unbanUser(userId) {\n    this.requireId(userId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath12, userId, \"unban\")\n    });\n  }\n  async lockUser(userId) {\n    this.requireId(userId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath12, userId, \"lock\")\n    });\n  }\n  async unlockUser(userId) {\n    this.requireId(userId);\n    return this.request({\n      method: \"POST\",\n      path: joinPaths(basePath12, userId, \"unlock\")\n    });\n  }\n  async deleteUserProfileImage(userId) {\n    this.requireId(userId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath12, userId, \"profile_image\")\n    });\n  }\n};\n\n// src/api/endpoints/SamlConnectionApi.ts\nvar basePath13 = \"/saml_connections\";\nvar SamlConnectionAPI = class extends AbstractAPI {\n  async getSamlConnectionList(params = {}) {\n    return this.request({\n      method: \"GET\",\n      path: basePath13,\n      queryParams: params\n    });\n  }\n  async createSamlConnection(params) {\n    return this.request({\n      method: \"POST\",\n      path: basePath13,\n      bodyParams: params\n    });\n  }\n  async getSamlConnection(samlConnectionId) {\n    this.requireId(samlConnectionId);\n    return this.request({\n      method: \"GET\",\n      path: joinPaths(basePath13, samlConnectionId)\n    });\n  }\n  async updateSamlConnection(samlConnectionId, params = {}) {\n    this.requireId(samlConnectionId);\n    return this.request({\n      method: \"PATCH\",\n      path: joinPaths(basePath13, samlConnectionId),\n      bodyParams: params\n    });\n  }\n  async deleteSamlConnection(samlConnectionId) {\n    this.requireId(samlConnectionId);\n    return this.request({\n      method: \"DELETE\",\n      path: joinPaths(basePath13, samlConnectionId)\n    });\n  }\n};\n\n// src/api/endpoints/TestingTokenApi.ts\nvar basePath14 = \"/testing_tokens\";\nvar TestingTokenAPI = class extends AbstractAPI {\n  async createTestingToken() {\n    return this.request({\n      method: \"POST\",\n      path: basePath14\n    });\n  }\n};\n\n// src/api/request.ts\nimport { ClerkAPIResponseError, parseError } from \"@clerk/shared/error\";\nimport snakecaseKeys from \"snakecase-keys\";\n\n// src/util/optionsAssertions.ts\nfunction assertValidSecretKey(val) {\n  if (!val || typeof val !== \"string\") {\n    throw Error(\"Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.\");\n  }\n}\nfunction assertValidPublishableKey(val) {\n  parsePublishableKey(val, { fatal: true });\n}\n\n// src/api/resources/AccountlessApplication.ts\nvar AccountlessApplication = class _AccountlessApplication {\n  constructor(publishableKey, secretKey, claimUrl, apiKeysUrl) {\n    this.publishableKey = publishableKey;\n    this.secretKey = secretKey;\n    this.claimUrl = claimUrl;\n    this.apiKeysUrl = apiKeysUrl;\n  }\n  static fromJSON(data) {\n    return new _AccountlessApplication(data.publishable_key, data.secret_key, data.claim_url, data.api_keys_url);\n  }\n};\n\n// src/api/resources/AllowlistIdentifier.ts\nvar AllowlistIdentifier = class _AllowlistIdentifier {\n  constructor(id, identifier, createdAt, updatedAt, invitationId) {\n    this.id = id;\n    this.identifier = identifier;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n    this.invitationId = invitationId;\n  }\n  static fromJSON(data) {\n    return new _AllowlistIdentifier(data.id, data.identifier, data.created_at, data.updated_at, data.invitation_id);\n  }\n};\n\n// src/api/resources/Session.ts\nvar SessionActivity = class _SessionActivity {\n  constructor(id, isMobile, ipAddress, city, country, browserVersion, browserName, deviceType) {\n    this.id = id;\n    this.isMobile = isMobile;\n    this.ipAddress = ipAddress;\n    this.city = city;\n    this.country = country;\n    this.browserVersion = browserVersion;\n    this.browserName = browserName;\n    this.deviceType = deviceType;\n  }\n  static fromJSON(data) {\n    return new _SessionActivity(\n      data.id,\n      data.is_mobile,\n      data.ip_address,\n      data.city,\n      data.country,\n      data.browser_version,\n      data.browser_name,\n      data.device_type\n    );\n  }\n};\nvar Session = class _Session {\n  constructor(id, clientId, userId, status, lastActiveAt, expireAt, abandonAt, createdAt, updatedAt, lastActiveOrganizationId, latestActivity, actor = null) {\n    this.id = id;\n    this.clientId = clientId;\n    this.userId = userId;\n    this.status = status;\n    this.lastActiveAt = lastActiveAt;\n    this.expireAt = expireAt;\n    this.abandonAt = abandonAt;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n    this.lastActiveOrganizationId = lastActiveOrganizationId;\n    this.latestActivity = latestActivity;\n    this.actor = actor;\n  }\n  static fromJSON(data) {\n    return new _Session(\n      data.id,\n      data.client_id,\n      data.user_id,\n      data.status,\n      data.last_active_at,\n      data.expire_at,\n      data.abandon_at,\n      data.created_at,\n      data.updated_at,\n      data.last_active_organization_id,\n      data.latest_activity && SessionActivity.fromJSON(data.latest_activity),\n      data.actor\n    );\n  }\n};\n\n// src/api/resources/Client.ts\nvar Client = class _Client {\n  constructor(id, sessionIds, sessions, signInId, signUpId, lastActiveSessionId, createdAt, updatedAt) {\n    this.id = id;\n    this.sessionIds = sessionIds;\n    this.sessions = sessions;\n    this.signInId = signInId;\n    this.signUpId = signUpId;\n    this.lastActiveSessionId = lastActiveSessionId;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n  }\n  static fromJSON(data) {\n    return new _Client(\n      data.id,\n      data.session_ids,\n      data.sessions.map((x) => Session.fromJSON(x)),\n      data.sign_in_id,\n      data.sign_up_id,\n      data.last_active_session_id,\n      data.created_at,\n      data.updated_at\n    );\n  }\n};\n\n// src/api/resources/Cookies.ts\nvar Cookies2 = class _Cookies {\n  constructor(cookies) {\n    this.cookies = cookies;\n  }\n  static fromJSON(data) {\n    return new _Cookies(data.cookies);\n  }\n};\n\n// src/api/resources/DeletedObject.ts\nvar DeletedObject = class _DeletedObject {\n  constructor(object, id, slug, deleted) {\n    this.object = object;\n    this.id = id;\n    this.slug = slug;\n    this.deleted = deleted;\n  }\n  static fromJSON(data) {\n    return new _DeletedObject(data.object, data.id || null, data.slug || null, data.deleted);\n  }\n};\n\n// src/api/resources/Email.ts\nvar Email = class _Email {\n  constructor(id, fromEmailName, emailAddressId, toEmailAddress, subject, body, bodyPlain, status, slug, data, deliveredByClerk) {\n    this.id = id;\n    this.fromEmailName = fromEmailName;\n    this.emailAddressId = emailAddressId;\n    this.toEmailAddress = toEmailAddress;\n    this.subject = subject;\n    this.body = body;\n    this.bodyPlain = bodyPlain;\n    this.status = status;\n    this.slug = slug;\n    this.data = data;\n    this.deliveredByClerk = deliveredByClerk;\n  }\n  static fromJSON(data) {\n    return new _Email(\n      data.id,\n      data.from_email_name,\n      data.email_address_id,\n      data.to_email_address,\n      data.subject,\n      data.body,\n      data.body_plain,\n      data.status,\n      data.slug,\n      data.data,\n      data.delivered_by_clerk\n    );\n  }\n};\n\n// src/api/resources/IdentificationLink.ts\nvar IdentificationLink = class _IdentificationLink {\n  constructor(id, type) {\n    this.id = id;\n    this.type = type;\n  }\n  static fromJSON(data) {\n    return new _IdentificationLink(data.id, data.type);\n  }\n};\n\n// src/api/resources/Verification.ts\nvar Verification = class _Verification {\n  constructor(status, strategy, externalVerificationRedirectURL = null, attempts = null, expireAt = null, nonce = null, message = null) {\n    this.status = status;\n    this.strategy = strategy;\n    this.externalVerificationRedirectURL = externalVerificationRedirectURL;\n    this.attempts = attempts;\n    this.expireAt = expireAt;\n    this.nonce = nonce;\n    this.message = message;\n  }\n  static fromJSON(data) {\n    return new _Verification(\n      data.status,\n      data.strategy,\n      data.external_verification_redirect_url ? new URL(data.external_verification_redirect_url) : null,\n      data.attempts,\n      data.expire_at,\n      data.nonce\n    );\n  }\n};\n\n// src/api/resources/EmailAddress.ts\nvar EmailAddress = class _EmailAddress {\n  constructor(id, emailAddress, verification, linkedTo) {\n    this.id = id;\n    this.emailAddress = emailAddress;\n    this.verification = verification;\n    this.linkedTo = linkedTo;\n  }\n  static fromJSON(data) {\n    return new _EmailAddress(\n      data.id,\n      data.email_address,\n      data.verification && Verification.fromJSON(data.verification),\n      data.linked_to.map((link) => IdentificationLink.fromJSON(link))\n    );\n  }\n};\n\n// src/api/resources/ExternalAccount.ts\nvar ExternalAccount = class _ExternalAccount {\n  constructor(id, provider, identificationId, externalId, approvedScopes, emailAddress, firstName, lastName, imageUrl, username, publicMetadata = {}, label, verification) {\n    this.id = id;\n    this.provider = provider;\n    this.identificationId = identificationId;\n    this.externalId = externalId;\n    this.approvedScopes = approvedScopes;\n    this.emailAddress = emailAddress;\n    this.firstName = firstName;\n    this.lastName = lastName;\n    this.imageUrl = imageUrl;\n    this.username = username;\n    this.publicMetadata = publicMetadata;\n    this.label = label;\n    this.verification = verification;\n  }\n  static fromJSON(data) {\n    return new _ExternalAccount(\n      data.id,\n      data.provider,\n      data.identification_id,\n      data.provider_user_id,\n      data.approved_scopes,\n      data.email_address,\n      data.first_name,\n      data.last_name,\n      data.image_url || \"\",\n      data.username,\n      data.public_metadata,\n      data.label,\n      data.verification && Verification.fromJSON(data.verification)\n    );\n  }\n};\n\n// src/api/resources/Invitation.ts\nvar Invitation = class _Invitation {\n  constructor(id, emailAddress, publicMetadata, createdAt, updatedAt, status, url, revoked) {\n    this.id = id;\n    this.emailAddress = emailAddress;\n    this.publicMetadata = publicMetadata;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n    this.status = status;\n    this.url = url;\n    this.revoked = revoked;\n    this._raw = null;\n  }\n  get raw() {\n    return this._raw;\n  }\n  static fromJSON(data) {\n    const res = new _Invitation(\n      data.id,\n      data.email_address,\n      data.public_metadata,\n      data.created_at,\n      data.updated_at,\n      data.status,\n      data.url,\n      data.revoked\n    );\n    res._raw = data;\n    return res;\n  }\n};\n\n// src/api/resources/JSON.ts\nvar ObjectType = {\n  AccountlessApplication: \"accountless_application\",\n  AllowlistIdentifier: \"allowlist_identifier\",\n  Client: \"client\",\n  Cookies: \"cookies\",\n  Email: \"email\",\n  EmailAddress: \"email_address\",\n  ExternalAccount: \"external_account\",\n  FacebookAccount: \"facebook_account\",\n  GoogleAccount: \"google_account\",\n  Invitation: \"invitation\",\n  OauthAccessToken: \"oauth_access_token\",\n  Organization: \"organization\",\n  OrganizationDomain: \"organization_domain\",\n  OrganizationInvitation: \"organization_invitation\",\n  OrganizationMembership: \"organization_membership\",\n  PhoneNumber: \"phone_number\",\n  RedirectUrl: \"redirect_url\",\n  SamlAccount: \"saml_account\",\n  Session: \"session\",\n  SignInAttempt: \"sign_in_attempt\",\n  SignInToken: \"sign_in_token\",\n  SignUpAttempt: \"sign_up_attempt\",\n  SmsMessage: \"sms_message\",\n  User: \"user\",\n  WaitlistEntry: \"waitlist_entry\",\n  Web3Wallet: \"web3_wallet\",\n  Token: \"token\",\n  TotalCount: \"total_count\",\n  TestingToken: \"testing_token\",\n  Role: \"role\",\n  Permission: \"permission\"\n};\n\n// src/api/resources/OauthAccessToken.ts\nvar OauthAccessToken = class _OauthAccessToken {\n  constructor(externalAccountId, provider, token, publicMetadata = {}, label, scopes, tokenSecret) {\n    this.externalAccountId = externalAccountId;\n    this.provider = provider;\n    this.token = token;\n    this.publicMetadata = publicMetadata;\n    this.label = label;\n    this.scopes = scopes;\n    this.tokenSecret = tokenSecret;\n  }\n  static fromJSON(data) {\n    return new _OauthAccessToken(\n      data.external_account_id,\n      data.provider,\n      data.token,\n      data.public_metadata,\n      data.label || \"\",\n      data.scopes,\n      data.token_secret\n    );\n  }\n};\n\n// src/api/resources/Organization.ts\nvar Organization = class _Organization {\n  constructor(id, name, slug, imageUrl, hasImage, createdAt, updatedAt, publicMetadata = {}, privateMetadata = {}, maxAllowedMemberships, adminDeleteEnabled, membersCount, createdBy) {\n    this.id = id;\n    this.name = name;\n    this.slug = slug;\n    this.imageUrl = imageUrl;\n    this.hasImage = hasImage;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n    this.publicMetadata = publicMetadata;\n    this.privateMetadata = privateMetadata;\n    this.maxAllowedMemberships = maxAllowedMemberships;\n    this.adminDeleteEnabled = adminDeleteEnabled;\n    this.membersCount = membersCount;\n    this.createdBy = createdBy;\n    this._raw = null;\n  }\n  get raw() {\n    return this._raw;\n  }\n  static fromJSON(data) {\n    const res = new _Organization(\n      data.id,\n      data.name,\n      data.slug,\n      data.image_url || \"\",\n      data.has_image,\n      data.created_at,\n      data.updated_at,\n      data.public_metadata,\n      data.private_metadata,\n      data.max_allowed_memberships,\n      data.admin_delete_enabled,\n      data.members_count,\n      data.created_by\n    );\n    res._raw = data;\n    return res;\n  }\n};\n\n// src/api/resources/OrganizationInvitation.ts\nvar OrganizationInvitation = class _OrganizationInvitation {\n  constructor(id, emailAddress, role, organizationId, createdAt, updatedAt, status, publicMetadata = {}, privateMetadata = {}) {\n    this.id = id;\n    this.emailAddress = emailAddress;\n    this.role = role;\n    this.organizationId = organizationId;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n    this.status = status;\n    this.publicMetadata = publicMetadata;\n    this.privateMetadata = privateMetadata;\n    this._raw = null;\n  }\n  get raw() {\n    return this._raw;\n  }\n  static fromJSON(data) {\n    const res = new _OrganizationInvitation(\n      data.id,\n      data.email_address,\n      data.role,\n      data.organization_id,\n      data.created_at,\n      data.updated_at,\n      data.status,\n      data.public_metadata,\n      data.private_metadata\n    );\n    res._raw = data;\n    return res;\n  }\n};\n\n// src/api/resources/OrganizationMembership.ts\nvar OrganizationMembership = class _OrganizationMembership {\n  constructor(id, role, permissions, publicMetadata = {}, privateMetadata = {}, createdAt, updatedAt, organization, publicUserData) {\n    this.id = id;\n    this.role = role;\n    this.permissions = permissions;\n    this.publicMetadata = publicMetadata;\n    this.privateMetadata = privateMetadata;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n    this.organization = organization;\n    this.publicUserData = publicUserData;\n    this._raw = null;\n  }\n  get raw() {\n    return this._raw;\n  }\n  static fromJSON(data) {\n    const res = new _OrganizationMembership(\n      data.id,\n      data.role,\n      data.permissions,\n      data.public_metadata,\n      data.private_metadata,\n      data.created_at,\n      data.updated_at,\n      Organization.fromJSON(data.organization),\n      OrganizationMembershipPublicUserData.fromJSON(data.public_user_data)\n    );\n    res._raw = data;\n    return res;\n  }\n};\nvar OrganizationMembershipPublicUserData = class _OrganizationMembershipPublicUserData {\n  constructor(identifier, firstName, lastName, imageUrl, hasImage, userId) {\n    this.identifier = identifier;\n    this.firstName = firstName;\n    this.lastName = lastName;\n    this.imageUrl = imageUrl;\n    this.hasImage = hasImage;\n    this.userId = userId;\n  }\n  static fromJSON(data) {\n    return new _OrganizationMembershipPublicUserData(\n      data.identifier,\n      data.first_name,\n      data.last_name,\n      data.image_url,\n      data.has_image,\n      data.user_id\n    );\n  }\n};\n\n// src/api/resources/PhoneNumber.ts\nvar PhoneNumber = class _PhoneNumber {\n  constructor(id, phoneNumber, reservedForSecondFactor, defaultSecondFactor, verification, linkedTo) {\n    this.id = id;\n    this.phoneNumber = phoneNumber;\n    this.reservedForSecondFactor = reservedForSecondFactor;\n    this.defaultSecondFactor = defaultSecondFactor;\n    this.verification = verification;\n    this.linkedTo = linkedTo;\n  }\n  static fromJSON(data) {\n    return new _PhoneNumber(\n      data.id,\n      data.phone_number,\n      data.reserved_for_second_factor,\n      data.default_second_factor,\n      data.verification && Verification.fromJSON(data.verification),\n      data.linked_to.map((link) => IdentificationLink.fromJSON(link))\n    );\n  }\n};\n\n// src/api/resources/RedirectUrl.ts\nvar RedirectUrl = class _RedirectUrl {\n  constructor(id, url, createdAt, updatedAt) {\n    this.id = id;\n    this.url = url;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n  }\n  static fromJSON(data) {\n    return new _RedirectUrl(data.id, data.url, data.created_at, data.updated_at);\n  }\n};\n\n// src/api/resources/SignInTokens.ts\nvar SignInToken = class _SignInToken {\n  constructor(id, userId, token, status, url, createdAt, updatedAt) {\n    this.id = id;\n    this.userId = userId;\n    this.token = token;\n    this.status = status;\n    this.url = url;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n  }\n  static fromJSON(data) {\n    return new _SignInToken(data.id, data.user_id, data.token, data.status, data.url, data.created_at, data.updated_at);\n  }\n};\n\n// src/api/resources/SMSMessage.ts\nvar SMSMessage = class _SMSMessage {\n  constructor(id, fromPhoneNumber, toPhoneNumber, message, status, phoneNumberId, data) {\n    this.id = id;\n    this.fromPhoneNumber = fromPhoneNumber;\n    this.toPhoneNumber = toPhoneNumber;\n    this.message = message;\n    this.status = status;\n    this.phoneNumberId = phoneNumberId;\n    this.data = data;\n  }\n  static fromJSON(data) {\n    return new _SMSMessage(\n      data.id,\n      data.from_phone_number,\n      data.to_phone_number,\n      data.message,\n      data.status,\n      data.phone_number_id,\n      data.data\n    );\n  }\n};\n\n// src/api/resources/Token.ts\nvar Token = class _Token {\n  constructor(jwt) {\n    this.jwt = jwt;\n  }\n  static fromJSON(data) {\n    return new _Token(data.jwt);\n  }\n};\n\n// src/api/resources/SamlConnection.ts\nvar SamlAccountConnection = class _SamlAccountConnection {\n  constructor(id, name, domain, active, provider, syncUserAttributes, allowSubdomains, allowIdpInitiated, createdAt, updatedAt) {\n    this.id = id;\n    this.name = name;\n    this.domain = domain;\n    this.active = active;\n    this.provider = provider;\n    this.syncUserAttributes = syncUserAttributes;\n    this.allowSubdomains = allowSubdomains;\n    this.allowIdpInitiated = allowIdpInitiated;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n  }\n  static fromJSON(data) {\n    return new _SamlAccountConnection(\n      data.id,\n      data.name,\n      data.domain,\n      data.active,\n      data.provider,\n      data.sync_user_attributes,\n      data.allow_subdomains,\n      data.allow_idp_initiated,\n      data.created_at,\n      data.updated_at\n    );\n  }\n};\n\n// src/api/resources/SamlAccount.ts\nvar SamlAccount = class _SamlAccount {\n  constructor(id, provider, providerUserId, active, emailAddress, firstName, lastName, verification, samlConnection) {\n    this.id = id;\n    this.provider = provider;\n    this.providerUserId = providerUserId;\n    this.active = active;\n    this.emailAddress = emailAddress;\n    this.firstName = firstName;\n    this.lastName = lastName;\n    this.verification = verification;\n    this.samlConnection = samlConnection;\n  }\n  static fromJSON(data) {\n    return new _SamlAccount(\n      data.id,\n      data.provider,\n      data.provider_user_id,\n      data.active,\n      data.email_address,\n      data.first_name,\n      data.last_name,\n      data.verification && Verification.fromJSON(data.verification),\n      data.saml_connection && SamlAccountConnection.fromJSON(data.saml_connection)\n    );\n  }\n};\n\n// src/api/resources/Web3Wallet.ts\nvar Web3Wallet = class _Web3Wallet {\n  constructor(id, web3Wallet, verification) {\n    this.id = id;\n    this.web3Wallet = web3Wallet;\n    this.verification = verification;\n  }\n  static fromJSON(data) {\n    return new _Web3Wallet(data.id, data.web3_wallet, data.verification && Verification.fromJSON(data.verification));\n  }\n};\n\n// src/api/resources/User.ts\nvar User = class _User {\n  constructor(id, passwordEnabled, totpEnabled, backupCodeEnabled, twoFactorEnabled, banned, locked, createdAt, updatedAt, imageUrl, hasImage, primaryEmailAddressId, primaryPhoneNumberId, primaryWeb3WalletId, lastSignInAt, externalId, username, firstName, lastName, publicMetadata = {}, privateMetadata = {}, unsafeMetadata = {}, emailAddresses = [], phoneNumbers = [], web3Wallets = [], externalAccounts = [], samlAccounts = [], lastActiveAt, createOrganizationEnabled, createOrganizationsLimit = null, deleteSelfEnabled, legalAcceptedAt) {\n    this.id = id;\n    this.passwordEnabled = passwordEnabled;\n    this.totpEnabled = totpEnabled;\n    this.backupCodeEnabled = backupCodeEnabled;\n    this.twoFactorEnabled = twoFactorEnabled;\n    this.banned = banned;\n    this.locked = locked;\n    this.createdAt = createdAt;\n    this.updatedAt = updatedAt;\n    this.imageUrl = imageUrl;\n    this.hasImage = hasImage;\n    this.primaryEmailAddressId = primaryEmailAddressId;\n    this.primaryPhoneNumberId = primaryPhoneNumberId;\n    this.primaryWeb3WalletId = primaryWeb3WalletId;\n    this.lastSignInAt = lastSignInAt;\n    this.externalId = externalId;\n    this.username = username;\n    this.firstName = firstName;\n    this.lastName = lastName;\n    this.publicMetadata = publicMetadata;\n    this.privateMetadata = privateMetadata;\n    this.unsafeMetadata = unsafeMetadata;\n    this.emailAddresses = emailAddresses;\n    this.phoneNumbers = phoneNumbers;\n    this.web3Wallets = web3Wallets;\n    this.externalAccounts = externalAccounts;\n    this.samlAccounts = samlAccounts;\n    this.lastActiveAt = lastActiveAt;\n    this.createOrganizationEnabled = createOrganizationEnabled;\n    this.createOrganizationsLimit = createOrganizationsLimit;\n    this.deleteSelfEnabled = deleteSelfEnabled;\n    this.legalAcceptedAt = legalAcceptedAt;\n    this._raw = null;\n  }\n  get raw() {\n    return this._raw;\n  }\n  static fromJSON(data) {\n    const res = new _User(\n      data.id,\n      data.password_enabled,\n      data.totp_enabled,\n      data.backup_code_enabled,\n      data.two_factor_enabled,\n      data.banned,\n      data.locked,\n      data.created_at,\n      data.updated_at,\n      data.image_url,\n      data.has_image,\n      data.primary_email_address_id,\n      data.primary_phone_number_id,\n      data.primary_web3_wallet_id,\n      data.last_sign_in_at,\n      data.external_id,\n      data.username,\n      data.first_name,\n      data.last_name,\n      data.public_metadata,\n      data.private_metadata,\n      data.unsafe_metadata,\n      (data.email_addresses || []).map((x) => EmailAddress.fromJSON(x)),\n      (data.phone_numbers || []).map((x) => PhoneNumber.fromJSON(x)),\n      (data.web3_wallets || []).map((x) => Web3Wallet.fromJSON(x)),\n      (data.external_accounts || []).map((x) => ExternalAccount.fromJSON(x)),\n      (data.saml_accounts || []).map((x) => SamlAccount.fromJSON(x)),\n      data.last_active_at,\n      data.create_organization_enabled,\n      data.create_organizations_limit,\n      data.delete_self_enabled,\n      data.legal_accepted_at\n    );\n    res._raw = data;\n    return res;\n  }\n  get primaryEmailAddress() {\n    return this.emailAddresses.find(({ id }) => id === this.primaryEmailAddressId) ?? null;\n  }\n  get primaryPhoneNumber() {\n    return this.phoneNumbers.find(({ id }) => id === this.primaryPhoneNumberId) ?? null;\n  }\n  get primaryWeb3Wallet() {\n    return this.web3Wallets.find(({ id }) => id === this.primaryWeb3WalletId) ?? null;\n  }\n  get fullName() {\n    return [this.firstName, this.lastName].join(\" \").trim() || null;\n  }\n};\n\n// src/api/resources/Deserializer.ts\nfunction deserialize(payload) {\n  let data, totalCount;\n  if (Array.isArray(payload)) {\n    const data2 = payload.map((item) => jsonToObject(item));\n    return { data: data2 };\n  } else if (isPaginated(payload)) {\n    data = payload.data.map((item) => jsonToObject(item));\n    totalCount = payload.total_count;\n    return { data, totalCount };\n  } else {\n    return { data: jsonToObject(payload) };\n  }\n}\nfunction isPaginated(payload) {\n  if (!payload || typeof payload !== \"object\" || !(\"data\" in payload)) {\n    return false;\n  }\n  return Array.isArray(payload.data) && payload.data !== void 0;\n}\nfunction getCount(item) {\n  return item.total_count;\n}\nfunction jsonToObject(item) {\n  if (typeof item !== \"string\" && \"object\" in item && \"deleted\" in item) {\n    return DeletedObject.fromJSON(item);\n  }\n  switch (item.object) {\n    case ObjectType.AccountlessApplication:\n      return AccountlessApplication.fromJSON(item);\n    case ObjectType.AllowlistIdentifier:\n      return AllowlistIdentifier.fromJSON(item);\n    case ObjectType.Client:\n      return Client.fromJSON(item);\n    case ObjectType.Cookies:\n      return Cookies2.fromJSON(item);\n    case ObjectType.EmailAddress:\n      return EmailAddress.fromJSON(item);\n    case ObjectType.Email:\n      return Email.fromJSON(item);\n    case ObjectType.Invitation:\n      return Invitation.fromJSON(item);\n    case ObjectType.OauthAccessToken:\n      return OauthAccessToken.fromJSON(item);\n    case ObjectType.Organization:\n      return Organization.fromJSON(item);\n    case ObjectType.OrganizationInvitation:\n      return OrganizationInvitation.fromJSON(item);\n    case ObjectType.OrganizationMembership:\n      return OrganizationMembership.fromJSON(item);\n    case ObjectType.PhoneNumber:\n      return PhoneNumber.fromJSON(item);\n    case ObjectType.RedirectUrl:\n      return RedirectUrl.fromJSON(item);\n    case ObjectType.SignInToken:\n      return SignInToken.fromJSON(item);\n    case ObjectType.Session:\n      return Session.fromJSON(item);\n    case ObjectType.SmsMessage:\n      return SMSMessage.fromJSON(item);\n    case ObjectType.Token:\n      return Token.fromJSON(item);\n    case ObjectType.TotalCount:\n      return getCount(item);\n    case ObjectType.User:\n      return User.fromJSON(item);\n    default:\n      return item;\n  }\n}\n\n// src/api/request.ts\nfunction buildRequest(options) {\n  const requestFn = async (requestOptions) => {\n    const {\n      secretKey,\n      requireSecretKey = true,\n      apiUrl = API_URL,\n      apiVersion = API_VERSION,\n      userAgent = USER_AGENT\n    } = options;\n    const { path, method, queryParams, headerParams, bodyParams, formData } = requestOptions;\n    if (requireSecretKey) {\n      assertValidSecretKey(secretKey);\n    }\n    const url = joinPaths(apiUrl, apiVersion, path);\n    const finalUrl = new URL(url);\n    if (queryParams) {\n      const snakecasedQueryParams = snakecaseKeys({ ...queryParams });\n      for (const [key, val] of Object.entries(snakecasedQueryParams)) {\n        if (val) {\n          [val].flat().forEach((v) => finalUrl.searchParams.append(key, v));\n        }\n      }\n    }\n    const headers = {\n      \"Clerk-API-Version\": SUPPORTED_BAPI_VERSION,\n      \"User-Agent\": userAgent,\n      ...headerParams\n    };\n    if (secretKey) {\n      headers.Authorization = `Bearer ${secretKey}`;\n    }\n    let res;\n    try {\n      if (formData) {\n        res = await runtime.fetch(finalUrl.href, {\n          method,\n          headers,\n          body: formData\n        });\n      } else {\n        headers[\"Content-Type\"] = \"application/json\";\n        const hasBody = method !== \"GET\" && bodyParams && Object.keys(bodyParams).length > 0;\n        const body = hasBody ? { body: JSON.stringify(snakecaseKeys(bodyParams, { deep: false })) } : null;\n        res = await runtime.fetch(finalUrl.href, {\n          method,\n          headers,\n          ...body\n        });\n      }\n      const isJSONResponse = res?.headers && res.headers?.get(constants.Headers.ContentType) === constants.ContentTypes.Json;\n      const responseBody = await (isJSONResponse ? res.json() : res.text());\n      if (!res.ok) {\n        return {\n          data: null,\n          errors: parseErrors(responseBody),\n          status: res?.status,\n          statusText: res?.statusText,\n          clerkTraceId: getTraceId(responseBody, res?.headers)\n        };\n      }\n      return {\n        ...deserialize(responseBody),\n        errors: null\n      };\n    } catch (err) {\n      if (err instanceof Error) {\n        return {\n          data: null,\n          errors: [\n            {\n              code: \"unexpected_error\",\n              message: err.message || \"Unexpected error\"\n            }\n          ],\n          clerkTraceId: getTraceId(err, res?.headers)\n        };\n      }\n      return {\n        data: null,\n        errors: parseErrors(err),\n        status: res?.status,\n        statusText: res?.statusText,\n        clerkTraceId: getTraceId(err, res?.headers)\n      };\n    }\n  };\n  return withLegacyRequestReturn(requestFn);\n}\nfunction getTraceId(data, headers) {\n  if (data && typeof data === \"object\" && \"clerk_trace_id\" in data && typeof data.clerk_trace_id === \"string\") {\n    return data.clerk_trace_id;\n  }\n  const cfRay = headers?.get(\"cf-ray\");\n  return cfRay || \"\";\n}\nfunction parseErrors(data) {\n  if (!!data && typeof data === \"object\" && \"errors\" in data) {\n    const errors = data.errors;\n    return errors.length > 0 ? errors.map(parseError) : [];\n  }\n  return [];\n}\nfunction withLegacyRequestReturn(cb) {\n  return async (...args) => {\n    const { data, errors, totalCount, status, statusText, clerkTraceId } = await cb(...args);\n    if (errors) {\n      const error = new ClerkAPIResponseError(statusText || \"\", {\n        data: [],\n        status,\n        clerkTraceId\n      });\n      error.errors = errors;\n      throw error;\n    }\n    if (typeof totalCount !== \"undefined\") {\n      return { data, totalCount };\n    }\n    return data;\n  };\n}\n\n// src/api/factory.ts\nfunction createBackendApiClient(options) {\n  const request = buildRequest(options);\n  return {\n    __experimental_accountlessApplications: new AccountlessApplicationAPI(\n      buildRequest({ ...options, requireSecretKey: false })\n    ),\n    allowlistIdentifiers: new AllowlistIdentifierAPI(request),\n    clients: new ClientAPI(request),\n    emailAddresses: new EmailAddressAPI(request),\n    invitations: new InvitationAPI(request),\n    organizations: new OrganizationAPI(request),\n    phoneNumbers: new PhoneNumberAPI(request),\n    redirectUrls: new RedirectUrlAPI(request),\n    sessions: new SessionAPI(request),\n    signInTokens: new SignInTokenAPI(request),\n    users: new UserAPI(request),\n    domains: new DomainAPI(request),\n    samlConnections: new SamlConnectionAPI(request),\n    testingTokens: new TestingTokenAPI(request)\n  };\n}\n\n// src/tokens/authObjects.ts\nimport { createCheckAuthorization } from \"@clerk/shared/authorization\";\nvar createDebug = (data) => {\n  return () => {\n    const res = { ...data };\n    res.secretKey = (res.secretKey || \"\").substring(0, 7);\n    res.jwtKey = (res.jwtKey || \"\").substring(0, 7);\n    return { ...res };\n  };\n};\nfunction signedInAuthObject(authenticateContext, sessionToken, sessionClaims) {\n  const {\n    act: actor,\n    sid: sessionId,\n    org_id: orgId,\n    org_role: orgRole,\n    org_slug: orgSlug,\n    org_permissions: orgPermissions,\n    sub: userId,\n    fva,\n    sts\n  } = sessionClaims;\n  const apiClient = createBackendApiClient(authenticateContext);\n  const getToken = createGetToken({\n    sessionId,\n    sessionToken,\n    fetcher: async (...args) => (await apiClient.sessions.getToken(...args)).jwt\n  });\n  const factorVerificationAge = fva ?? null;\n  const sessionStatus = sts ?? null;\n  return {\n    actor,\n    sessionClaims,\n    sessionId,\n    sessionStatus,\n    userId,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n    getToken,\n    has: createCheckAuthorization({ orgId, orgRole, orgPermissions, userId, factorVerificationAge }),\n    debug: createDebug({ ...authenticateContext, sessionToken })\n  };\n}\nfunction signedOutAuthObject(debugData) {\n  return {\n    sessionClaims: null,\n    sessionId: null,\n    sessionStatus: null,\n    userId: null,\n    actor: null,\n    orgId: null,\n    orgRole: null,\n    orgSlug: null,\n    orgPermissions: null,\n    factorVerificationAge: null,\n    getToken: () => Promise.resolve(null),\n    has: () => false,\n    debug: createDebug(debugData)\n  };\n}\nvar makeAuthObjectSerializable = (obj) => {\n  const { debug, getToken, has, ...rest } = obj;\n  return rest;\n};\nvar createGetToken = (params) => {\n  const { fetcher, sessionToken, sessionId } = params || {};\n  return async (options = {}) => {\n    if (!sessionId) {\n      return null;\n    }\n    if (options.template) {\n      return fetcher(sessionId, options.template);\n    }\n    return sessionToken;\n  };\n};\n\n// src/tokens/authStatus.ts\nvar AuthStatus = {\n  SignedIn: \"signed-in\",\n  SignedOut: \"signed-out\",\n  Handshake: \"handshake\"\n};\nvar AuthErrorReason = {\n  ClientUATWithoutSessionToken: \"client-uat-but-no-session-token\",\n  DevBrowserMissing: \"dev-browser-missing\",\n  DevBrowserSync: \"dev-browser-sync\",\n  PrimaryRespondsToSyncing: \"primary-responds-to-syncing\",\n  SatelliteCookieNeedsSyncing: \"satellite-needs-syncing\",\n  SessionTokenAndUATMissing: \"session-token-and-uat-missing\",\n  SessionTokenMissing: \"session-token-missing\",\n  SessionTokenExpired: \"session-token-expired\",\n  SessionTokenIATBeforeClientUAT: \"session-token-iat-before-client-uat\",\n  SessionTokenNBF: \"session-token-nbf\",\n  SessionTokenIatInTheFuture: \"session-token-iat-in-the-future\",\n  SessionTokenWithoutClientUAT: \"session-token-but-no-client-uat\",\n  ActiveOrganizationMismatch: \"active-organization-mismatch\",\n  UnexpectedError: \"unexpected-error\"\n};\nfunction signedIn(authenticateContext, sessionClaims, headers = new Headers(), token) {\n  const authObject = signedInAuthObject(authenticateContext, token, sessionClaims);\n  return {\n    status: AuthStatus.SignedIn,\n    reason: null,\n    message: null,\n    proxyUrl: authenticateContext.proxyUrl || \"\",\n    publishableKey: authenticateContext.publishableKey || \"\",\n    isSatellite: authenticateContext.isSatellite || false,\n    domain: authenticateContext.domain || \"\",\n    signInUrl: authenticateContext.signInUrl || \"\",\n    signUpUrl: authenticateContext.signUpUrl || \"\",\n    afterSignInUrl: authenticateContext.afterSignInUrl || \"\",\n    afterSignUpUrl: authenticateContext.afterSignUpUrl || \"\",\n    isSignedIn: true,\n    toAuth: () => authObject,\n    headers,\n    token\n  };\n}\nfunction signedOut(authenticateContext, reason, message = \"\", headers = new Headers()) {\n  return withDebugHeaders({\n    status: AuthStatus.SignedOut,\n    reason,\n    message,\n    proxyUrl: authenticateContext.proxyUrl || \"\",\n    publishableKey: authenticateContext.publishableKey || \"\",\n    isSatellite: authenticateContext.isSatellite || false,\n    domain: authenticateContext.domain || \"\",\n    signInUrl: authenticateContext.signInUrl || \"\",\n    signUpUrl: authenticateContext.signUpUrl || \"\",\n    afterSignInUrl: authenticateContext.afterSignInUrl || \"\",\n    afterSignUpUrl: authenticateContext.afterSignUpUrl || \"\",\n    isSignedIn: false,\n    headers,\n    toAuth: () => signedOutAuthObject({ ...authenticateContext, status: AuthStatus.SignedOut, reason, message }),\n    token: null\n  });\n}\nfunction handshake(authenticateContext, reason, message = \"\", headers) {\n  return withDebugHeaders({\n    status: AuthStatus.Handshake,\n    reason,\n    message,\n    publishableKey: authenticateContext.publishableKey || \"\",\n    isSatellite: authenticateContext.isSatellite || false,\n    domain: authenticateContext.domain || \"\",\n    proxyUrl: authenticateContext.proxyUrl || \"\",\n    signInUrl: authenticateContext.signInUrl || \"\",\n    signUpUrl: authenticateContext.signUpUrl || \"\",\n    afterSignInUrl: authenticateContext.afterSignInUrl || \"\",\n    afterSignUpUrl: authenticateContext.afterSignUpUrl || \"\",\n    isSignedIn: false,\n    headers,\n    toAuth: () => null,\n    token: null\n  });\n}\nvar withDebugHeaders = (requestState) => {\n  const headers = new Headers(requestState.headers || {});\n  if (requestState.message) {\n    try {\n      headers.set(constants.Headers.AuthMessage, requestState.message);\n    } catch {\n    }\n  }\n  if (requestState.reason) {\n    try {\n      headers.set(constants.Headers.AuthReason, requestState.reason);\n    } catch {\n    }\n  }\n  if (requestState.status) {\n    try {\n      headers.set(constants.Headers.AuthStatus, requestState.status);\n    } catch {\n    }\n  }\n  requestState.headers = headers;\n  return requestState;\n};\n\n// src/tokens/clerkRequest.ts\nimport { parse } from \"cookie\";\n\n// src/tokens/clerkUrl.ts\nvar ClerkUrl = class extends URL {\n  isCrossOrigin(other) {\n    return this.origin !== new URL(other.toString()).origin;\n  }\n};\nvar createClerkUrl = (...args) => {\n  return new ClerkUrl(...args);\n};\n\n// src/tokens/clerkRequest.ts\nvar ClerkRequest = class extends Request {\n  constructor(input, init) {\n    const url = typeof input !== \"string\" && \"url\" in input ? input.url : String(input);\n    super(url, init || typeof input === \"string\" ? void 0 : input);\n    this.clerkUrl = this.deriveUrlFromHeaders(this);\n    this.cookies = this.parseCookies(this);\n  }\n  toJSON() {\n    return {\n      url: this.clerkUrl.href,\n      method: this.method,\n      headers: JSON.stringify(Object.fromEntries(this.headers)),\n      clerkUrl: this.clerkUrl.toString(),\n      cookies: JSON.stringify(Object.fromEntries(this.cookies))\n    };\n  }\n  /**\n   * Used to fix request.url using the x-forwarded-* headers\n   * TODO add detailed description of the issues this solves\n   */\n  deriveUrlFromHeaders(req) {\n    const initialUrl = new URL(req.url);\n    const forwardedProto = req.headers.get(constants.Headers.ForwardedProto);\n    const forwardedHost = req.headers.get(constants.Headers.ForwardedHost);\n    const host = req.headers.get(constants.Headers.Host);\n    const protocol = initialUrl.protocol;\n    const resolvedHost = this.getFirstValueFromHeader(forwardedHost) ?? host;\n    const resolvedProtocol = this.getFirstValueFromHeader(forwardedProto) ?? protocol?.replace(/[:/]/, \"\");\n    const origin = resolvedHost && resolvedProtocol ? `${resolvedProtocol}://${resolvedHost}` : initialUrl.origin;\n    if (origin === initialUrl.origin) {\n      return createClerkUrl(initialUrl);\n    }\n    return createClerkUrl(initialUrl.pathname + initialUrl.search, origin);\n  }\n  getFirstValueFromHeader(value) {\n    return value?.split(\",\")[0];\n  }\n  parseCookies(req) {\n    const cookiesRecord = parse(this.decodeCookieValue(req.headers.get(\"cookie\") || \"\"));\n    return new Map(Object.entries(cookiesRecord));\n  }\n  decodeCookieValue(str) {\n    return str ? str.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent) : str;\n  }\n};\nvar createClerkRequest = (...args) => {\n  return args[0] instanceof ClerkRequest ? args[0] : new ClerkRequest(...args);\n};\n\n// src/tokens/keys.ts\nvar cache = {};\nvar lastUpdatedAt = 0;\nfunction getFromCache(kid) {\n  return cache[kid];\n}\nfunction getCacheValues() {\n  return Object.values(cache);\n}\nfunction setInCache(jwk, shouldExpire = true) {\n  cache[jwk.kid] = jwk;\n  lastUpdatedAt = shouldExpire ? Date.now() : -1;\n}\nvar LocalJwkKid = \"local\";\nvar PEM_HEADER = \"-----BEGIN PUBLIC KEY-----\";\nvar PEM_TRAILER = \"-----END PUBLIC KEY-----\";\nvar RSA_PREFIX = \"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA\";\nvar RSA_SUFFIX = \"IDAQAB\";\nfunction loadClerkJWKFromLocal(localKey) {\n  if (!getFromCache(LocalJwkKid)) {\n    if (!localKey) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.SetClerkJWTKey,\n        message: \"Missing local JWK.\",\n        reason: TokenVerificationErrorReason.LocalJWKMissing\n      });\n    }\n    const modulus = localKey.replace(/\\r\\n|\\n|\\r/g, \"\").replace(PEM_HEADER, \"\").replace(PEM_TRAILER, \"\").replace(RSA_PREFIX, \"\").replace(RSA_SUFFIX, \"\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\");\n    setInCache(\n      {\n        kid: \"local\",\n        kty: \"RSA\",\n        alg: \"RS256\",\n        n: modulus,\n        e: \"AQAB\"\n      },\n      false\n      // local key never expires in cache\n    );\n  }\n  return getFromCache(LocalJwkKid);\n}\nasync function loadClerkJWKFromRemote({\n  secretKey,\n  apiUrl = API_URL,\n  apiVersion = API_VERSION,\n  kid,\n  skipJwksCache\n}) {\n  if (skipJwksCache || cacheHasExpired() || !getFromCache(kid)) {\n    if (!secretKey) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: \"Failed to load JWKS from Clerk Backend or Frontend API.\",\n        reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad\n      });\n    }\n    const fetcher = () => fetchJWKSFromBAPI(apiUrl, secretKey, apiVersion);\n    const { keys } = await retry(fetcher);\n    if (!keys || !keys.length) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: \"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.\",\n        reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad\n      });\n    }\n    keys.forEach((key) => setInCache(key));\n  }\n  const jwk = getFromCache(kid);\n  if (!jwk) {\n    const cacheValues = getCacheValues();\n    const jwkKeys = cacheValues.map((jwk2) => jwk2.kid).sort().join(\", \");\n    throw new TokenVerificationError({\n      action: `Go to your Dashboard and validate your secret and public keys are correct. ${TokenVerificationErrorAction.ContactSupport} if the issue persists.`,\n      message: `Unable to find a signing key in JWKS that matches the kid='${kid}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${jwkKeys}`,\n      reason: TokenVerificationErrorReason.JWKKidMismatch\n    });\n  }\n  return jwk;\n}\nasync function fetchJWKSFromBAPI(apiUrl, key, apiVersion) {\n  if (!key) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.SetClerkSecretKey,\n      message: \"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.\",\n      reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad\n    });\n  }\n  const url = new URL(apiUrl);\n  url.pathname = joinPaths(url.pathname, apiVersion, \"/jwks\");\n  const response = await runtime.fetch(url.href, {\n    headers: {\n      Authorization: `Bearer ${key}`,\n      \"Clerk-API-Version\": SUPPORTED_BAPI_VERSION,\n      \"Content-Type\": \"application/json\",\n      \"User-Agent\": USER_AGENT\n    }\n  });\n  if (!response.ok) {\n    const json = await response.json();\n    const invalidSecretKeyError = getErrorObjectByCode(json?.errors, TokenVerificationErrorCode.InvalidSecretKey);\n    if (invalidSecretKeyError) {\n      const reason = TokenVerificationErrorReason.InvalidSecretKey;\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: invalidSecretKeyError.message,\n        reason\n      });\n    }\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.ContactSupport,\n      message: `Error loading Clerk JWKS from ${url.href} with code=${response.status}`,\n      reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad\n    });\n  }\n  return response.json();\n}\nfunction cacheHasExpired() {\n  if (lastUpdatedAt === -1) {\n    return false;\n  }\n  const isExpired = Date.now() - lastUpdatedAt >= MAX_CACHE_LAST_UPDATED_AT_SECONDS * 1e3;\n  if (isExpired) {\n    cache = {};\n  }\n  return isExpired;\n}\nvar getErrorObjectByCode = (errors, code) => {\n  if (!errors) {\n    return null;\n  }\n  return errors.find((err) => err.code === code);\n};\n\n// src/tokens/verify.ts\nasync function verifyToken(token, options) {\n  const { data: decodedResult, errors } = decodeJwt(token);\n  if (errors) {\n    return { errors };\n  }\n  const { header } = decodedResult;\n  const { kid } = header;\n  try {\n    let key;\n    if (options.jwtKey) {\n      key = loadClerkJWKFromLocal(options.jwtKey);\n    } else if (options.secretKey) {\n      key = await loadClerkJWKFromRemote({ ...options, kid });\n    } else {\n      return {\n        errors: [\n          new TokenVerificationError({\n            action: TokenVerificationErrorAction.SetClerkJWTKey,\n            message: \"Failed to resolve JWK during verification.\",\n            reason: TokenVerificationErrorReason.JWKFailedToResolve\n          })\n        ]\n      };\n    }\n    return await verifyJwt(token, { ...options, key });\n  } catch (error) {\n    return { errors: [error] };\n  }\n}\n\n// src/tokens/request.ts\nimport { match } from \"@clerk/shared/pathToRegexp\";\n\n// src/tokens/authenticateContext.ts\nvar AuthenticateContext = class {\n  constructor(cookieSuffix, clerkRequest, options) {\n    this.cookieSuffix = cookieSuffix;\n    this.clerkRequest = clerkRequest;\n    this.initPublishableKeyValues(options);\n    this.initHeaderValues();\n    this.initCookieValues();\n    this.initHandshakeValues();\n    Object.assign(this, options);\n    this.clerkUrl = this.clerkRequest.clerkUrl;\n  }\n  /**\n   * Retrieves the session token from either the cookie or the header.\n   *\n   * @returns {string | undefined} The session token if available, otherwise undefined.\n   */\n  get sessionToken() {\n    return this.sessionTokenInCookie || this.sessionTokenInHeader;\n  }\n  usesSuffixedCookies() {\n    const suffixedClientUat = this.getSuffixedCookie(constants.Cookies.ClientUat);\n    const clientUat = this.getCookie(constants.Cookies.ClientUat);\n    const suffixedSession = this.getSuffixedCookie(constants.Cookies.Session) || \"\";\n    const session = this.getCookie(constants.Cookies.Session) || \"\";\n    if (session && !this.tokenHasIssuer(session)) {\n      return false;\n    }\n    if (session && !this.tokenBelongsToInstance(session)) {\n      return true;\n    }\n    if (!suffixedClientUat && !suffixedSession) {\n      return false;\n    }\n    const { data: sessionData } = decodeJwt(session);\n    const sessionIat = sessionData?.payload.iat || 0;\n    const { data: suffixedSessionData } = decodeJwt(suffixedSession);\n    const suffixedSessionIat = suffixedSessionData?.payload.iat || 0;\n    if (suffixedClientUat !== \"0\" && clientUat !== \"0\" && sessionIat > suffixedSessionIat) {\n      return false;\n    }\n    if (suffixedClientUat === \"0\" && clientUat !== \"0\") {\n      return false;\n    }\n    if (this.instanceType !== \"production\") {\n      const isSuffixedSessionExpired = this.sessionExpired(suffixedSessionData);\n      if (suffixedClientUat !== \"0\" && clientUat === \"0\" && isSuffixedSessionExpired) {\n        return false;\n      }\n    }\n    if (!suffixedClientUat && suffixedSession) {\n      return false;\n    }\n    return true;\n  }\n  initPublishableKeyValues(options) {\n    assertValidPublishableKey(options.publishableKey);\n    this.publishableKey = options.publishableKey;\n    const pk = parsePublishableKey(this.publishableKey, {\n      fatal: true,\n      proxyUrl: options.proxyUrl,\n      domain: options.domain\n    });\n    this.instanceType = pk.instanceType;\n    this.frontendApi = pk.frontendApi;\n  }\n  initHeaderValues() {\n    this.sessionTokenInHeader = this.parseAuthorizationHeader(this.getHeader(constants.Headers.Authorization));\n    this.origin = this.getHeader(constants.Headers.Origin);\n    this.host = this.getHeader(constants.Headers.Host);\n    this.forwardedHost = this.getHeader(constants.Headers.ForwardedHost);\n    this.forwardedProto = this.getHeader(constants.Headers.CloudFrontForwardedProto) || this.getHeader(constants.Headers.ForwardedProto);\n    this.referrer = this.getHeader(constants.Headers.Referrer);\n    this.userAgent = this.getHeader(constants.Headers.UserAgent);\n    this.secFetchDest = this.getHeader(constants.Headers.SecFetchDest);\n    this.accept = this.getHeader(constants.Headers.Accept);\n  }\n  initCookieValues() {\n    this.sessionTokenInCookie = this.getSuffixedOrUnSuffixedCookie(constants.Cookies.Session);\n    this.refreshTokenInCookie = this.getSuffixedCookie(constants.Cookies.Refresh);\n    this.clientUat = Number.parseInt(this.getSuffixedOrUnSuffixedCookie(constants.Cookies.ClientUat) || \"\") || 0;\n  }\n  initHandshakeValues() {\n    this.devBrowserToken = this.getQueryParam(constants.QueryParameters.DevBrowser) || this.getSuffixedOrUnSuffixedCookie(constants.Cookies.DevBrowser);\n    this.handshakeToken = this.getQueryParam(constants.QueryParameters.Handshake) || this.getCookie(constants.Cookies.Handshake);\n    this.handshakeRedirectLoopCounter = Number(this.getCookie(constants.Cookies.RedirectCount)) || 0;\n  }\n  getQueryParam(name) {\n    return this.clerkRequest.clerkUrl.searchParams.get(name);\n  }\n  getHeader(name) {\n    return this.clerkRequest.headers.get(name) || void 0;\n  }\n  getCookie(name) {\n    return this.clerkRequest.cookies.get(name) || void 0;\n  }\n  getSuffixedCookie(name) {\n    return this.getCookie(getSuffixedCookieName(name, this.cookieSuffix)) || void 0;\n  }\n  getSuffixedOrUnSuffixedCookie(cookieName) {\n    if (this.usesSuffixedCookies()) {\n      return this.getSuffixedCookie(cookieName);\n    }\n    return this.getCookie(cookieName);\n  }\n  parseAuthorizationHeader(authorizationHeader) {\n    if (!authorizationHeader) {\n      return void 0;\n    }\n    const [scheme, token] = authorizationHeader.split(\" \", 2);\n    if (!token) {\n      return scheme;\n    }\n    if (scheme === \"Bearer\") {\n      return token;\n    }\n    return void 0;\n  }\n  tokenHasIssuer(token) {\n    const { data, errors } = decodeJwt(token);\n    if (errors) {\n      return false;\n    }\n    return !!data.payload.iss;\n  }\n  tokenBelongsToInstance(token) {\n    if (!token) {\n      return false;\n    }\n    const { data, errors } = decodeJwt(token);\n    if (errors) {\n      return false;\n    }\n    const tokenIssuer = data.payload.iss.replace(/https?:\\/\\//gi, \"\");\n    return this.frontendApi === tokenIssuer;\n  }\n  sessionExpired(jwt) {\n    return !!jwt && jwt?.payload.exp <= Date.now() / 1e3 >> 0;\n  }\n};\nvar createAuthenticateContext = async (clerkRequest, options) => {\n  const cookieSuffix = options.publishableKey ? await getCookieSuffix(options.publishableKey, runtime.crypto.subtle) : \"\";\n  return new AuthenticateContext(cookieSuffix, clerkRequest, options);\n};\n\n// src/tokens/cookie.ts\nvar getCookieName = (cookieDirective) => {\n  return cookieDirective.split(\";\")[0]?.split(\"=\")[0];\n};\nvar getCookieValue = (cookieDirective) => {\n  return cookieDirective.split(\";\")[0]?.split(\"=\")[1];\n};\n\n// src/tokens/handshake.ts\nasync function verifyHandshakeJwt(token, { key }) {\n  const { data: decoded, errors } = decodeJwt(token);\n  if (errors) {\n    throw errors[0];\n  }\n  const { header, payload } = decoded;\n  const { typ, alg } = header;\n  assertHeaderType(typ);\n  assertHeaderAlgorithm(alg);\n  const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);\n  if (signatureErrors) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Error verifying handshake token. ${signatureErrors[0]}`\n    });\n  }\n  if (!signatureValid) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidSignature,\n      message: \"Handshake signature is invalid.\"\n    });\n  }\n  return payload;\n}\nasync function verifyHandshakeToken(token, options) {\n  const { secretKey, apiUrl, apiVersion, jwksCacheTtlInMs, jwtKey, skipJwksCache } = options;\n  const { data, errors } = decodeJwt(token);\n  if (errors) {\n    throw errors[0];\n  }\n  const { kid } = data.header;\n  let key;\n  if (jwtKey) {\n    key = loadClerkJWKFromLocal(jwtKey);\n  } else if (secretKey) {\n    key = await loadClerkJWKFromRemote({ secretKey, apiUrl, apiVersion, kid, jwksCacheTtlInMs, skipJwksCache });\n  } else {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.SetClerkJWTKey,\n      message: \"Failed to resolve JWK during handshake verification.\",\n      reason: TokenVerificationErrorReason.JWKFailedToResolve\n    });\n  }\n  return await verifyHandshakeJwt(token, {\n    key\n  });\n}\n\n// src/tokens/request.ts\nvar RefreshTokenErrorReason = {\n  NonEligibleNoCookie: \"non-eligible-no-refresh-cookie\",\n  NonEligibleNonGet: \"non-eligible-non-get\",\n  InvalidSessionToken: \"invalid-session-token\",\n  MissingApiClient: \"missing-api-client\",\n  MissingSessionToken: \"missing-session-token\",\n  MissingRefreshToken: \"missing-refresh-token\",\n  ExpiredSessionTokenDecodeFailed: \"expired-session-token-decode-failed\",\n  ExpiredSessionTokenMissingSidClaim: \"expired-session-token-missing-sid-claim\",\n  FetchError: \"fetch-error\",\n  UnexpectedSDKError: \"unexpected-sdk-error\",\n  UnexpectedBAPIError: \"unexpected-bapi-error\"\n};\nfunction assertSignInUrlExists(signInUrl, key) {\n  if (!signInUrl && isDevelopmentFromSecretKey(key)) {\n    throw new Error(`Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite`);\n  }\n}\nfunction assertProxyUrlOrDomain(proxyUrlOrDomain) {\n  if (!proxyUrlOrDomain) {\n    throw new Error(`Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl`);\n  }\n}\nfunction assertSignInUrlFormatAndOrigin(_signInUrl, origin) {\n  let signInUrl;\n  try {\n    signInUrl = new URL(_signInUrl);\n  } catch {\n    throw new Error(`The signInUrl needs to have a absolute url format.`);\n  }\n  if (signInUrl.origin === origin) {\n    throw new Error(`The signInUrl needs to be on a different origin than your satellite application.`);\n  }\n}\nfunction isRequestEligibleForHandshake(authenticateContext) {\n  const { accept, secFetchDest } = authenticateContext;\n  if (secFetchDest === \"document\" || secFetchDest === \"iframe\") {\n    return true;\n  }\n  if (!secFetchDest && accept?.startsWith(\"text/html\")) {\n    return true;\n  }\n  return false;\n}\nfunction isRequestEligibleForRefresh(err, authenticateContext, request) {\n  return err.reason === TokenVerificationErrorReason.TokenExpired && !!authenticateContext.refreshTokenInCookie && request.method === \"GET\";\n}\nasync function authenticateRequest(request, options) {\n  const authenticateContext = await createAuthenticateContext(createClerkRequest(request), options);\n  assertValidSecretKey(authenticateContext.secretKey);\n  if (authenticateContext.isSatellite) {\n    assertSignInUrlExists(authenticateContext.signInUrl, authenticateContext.secretKey);\n    if (authenticateContext.signInUrl && authenticateContext.origin) {\n      assertSignInUrlFormatAndOrigin(authenticateContext.signInUrl, authenticateContext.origin);\n    }\n    assertProxyUrlOrDomain(authenticateContext.proxyUrl || authenticateContext.domain);\n  }\n  const organizationSyncTargetMatchers = computeOrganizationSyncTargetMatchers(options.organizationSyncOptions);\n  function removeDevBrowserFromURL(url) {\n    const updatedURL = new URL(url);\n    updatedURL.searchParams.delete(constants.QueryParameters.DevBrowser);\n    updatedURL.searchParams.delete(constants.QueryParameters.LegacyDevBrowser);\n    return updatedURL;\n  }\n  function buildRedirectToHandshake({ handshakeReason }) {\n    const redirectUrl = removeDevBrowserFromURL(authenticateContext.clerkUrl);\n    const frontendApiNoProtocol = authenticateContext.frontendApi.replace(/http(s)?:\\/\\//, \"\");\n    const url = new URL(`https://${frontendApiNoProtocol}/v1/client/handshake`);\n    url.searchParams.append(\"redirect_url\", redirectUrl?.href || \"\");\n    url.searchParams.append(\n      constants.QueryParameters.SuffixedCookies,\n      authenticateContext.usesSuffixedCookies().toString()\n    );\n    url.searchParams.append(constants.QueryParameters.HandshakeReason, handshakeReason);\n    if (authenticateContext.instanceType === \"development\" && authenticateContext.devBrowserToken) {\n      url.searchParams.append(constants.QueryParameters.DevBrowser, authenticateContext.devBrowserToken);\n    }\n    const toActivate = getOrganizationSyncTarget(\n      authenticateContext.clerkUrl,\n      options.organizationSyncOptions,\n      organizationSyncTargetMatchers\n    );\n    if (toActivate) {\n      const params = getOrganizationSyncQueryParams(toActivate);\n      params.forEach((value, key) => {\n        url.searchParams.append(key, value);\n      });\n    }\n    return new Headers({ [constants.Headers.Location]: url.href });\n  }\n  async function resolveHandshake() {\n    const headers = new Headers({\n      \"Access-Control-Allow-Origin\": \"null\",\n      \"Access-Control-Allow-Credentials\": \"true\"\n    });\n    const handshakePayload = await verifyHandshakeToken(authenticateContext.handshakeToken, authenticateContext);\n    const cookiesToSet = handshakePayload.handshake;\n    let sessionToken = \"\";\n    cookiesToSet.forEach((x) => {\n      headers.append(\"Set-Cookie\", x);\n      if (getCookieName(x).startsWith(constants.Cookies.Session)) {\n        sessionToken = getCookieValue(x);\n      }\n    });\n    if (authenticateContext.instanceType === \"development\") {\n      const newUrl = new URL(authenticateContext.clerkUrl);\n      newUrl.searchParams.delete(constants.QueryParameters.Handshake);\n      newUrl.searchParams.delete(constants.QueryParameters.HandshakeHelp);\n      headers.append(constants.Headers.Location, newUrl.toString());\n      headers.set(constants.Headers.CacheControl, \"no-store\");\n    }\n    if (sessionToken === \"\") {\n      return signedOut(authenticateContext, AuthErrorReason.SessionTokenMissing, \"\", headers);\n    }\n    const { data, errors: [error] = [] } = await verifyToken(sessionToken, authenticateContext);\n    if (data) {\n      return signedIn(authenticateContext, data, headers, sessionToken);\n    }\n    if (authenticateContext.instanceType === \"development\" && (error?.reason === TokenVerificationErrorReason.TokenExpired || error?.reason === TokenVerificationErrorReason.TokenNotActiveYet || error?.reason === TokenVerificationErrorReason.TokenIatInTheFuture)) {\n      error.tokenCarrier = \"cookie\";\n      console.error(\n        `Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.\n\nTo resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).\n\n---\n\n${error.getFullMessage()}`\n      );\n      const { data: retryResult, errors: [retryError] = [] } = await verifyToken(sessionToken, {\n        ...authenticateContext,\n        clockSkewInMs: 864e5\n      });\n      if (retryResult) {\n        return signedIn(authenticateContext, retryResult, headers, sessionToken);\n      }\n      throw new Error(retryError?.message || \"Clerk: Handshake retry failed.\");\n    }\n    throw new Error(error?.message || \"Clerk: Handshake failed.\");\n  }\n  async function refreshToken(authenticateContext2) {\n    if (!options.apiClient) {\n      return {\n        data: null,\n        error: {\n          message: \"An apiClient is needed to perform token refresh.\",\n          cause: { reason: RefreshTokenErrorReason.MissingApiClient }\n        }\n      };\n    }\n    const { sessionToken: expiredSessionToken, refreshTokenInCookie: refreshToken2 } = authenticateContext2;\n    if (!expiredSessionToken) {\n      return {\n        data: null,\n        error: {\n          message: \"Session token must be provided.\",\n          cause: { reason: RefreshTokenErrorReason.MissingSessionToken }\n        }\n      };\n    }\n    if (!refreshToken2) {\n      return {\n        data: null,\n        error: {\n          message: \"Refresh token must be provided.\",\n          cause: { reason: RefreshTokenErrorReason.MissingRefreshToken }\n        }\n      };\n    }\n    const { data: decodeResult, errors: decodedErrors } = decodeJwt(expiredSessionToken);\n    if (!decodeResult || decodedErrors) {\n      return {\n        data: null,\n        error: {\n          message: \"Unable to decode the expired session token.\",\n          cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenDecodeFailed, errors: decodedErrors }\n        }\n      };\n    }\n    if (!decodeResult?.payload?.sid) {\n      return {\n        data: null,\n        error: {\n          message: \"Expired session token is missing the `sid` claim.\",\n          cause: { reason: RefreshTokenErrorReason.ExpiredSessionTokenMissingSidClaim }\n        }\n      };\n    }\n    try {\n      const response = await options.apiClient.sessions.refreshSession(decodeResult.payload.sid, {\n        format: \"cookie\",\n        suffixed_cookies: authenticateContext2.usesSuffixedCookies(),\n        expired_token: expiredSessionToken || \"\",\n        refresh_token: refreshToken2 || \"\",\n        request_origin: authenticateContext2.clerkUrl.origin,\n        // The refresh endpoint expects headers as Record<string, string[]>, so we need to transform it.\n        request_headers: Object.fromEntries(Array.from(request.headers.entries()).map(([k, v]) => [k, [v]]))\n      });\n      return { data: response.cookies, error: null };\n    } catch (err) {\n      if (err?.errors?.length) {\n        if (err.errors[0].code === \"unexpected_error\") {\n          return {\n            data: null,\n            error: {\n              message: `Fetch unexpected error`,\n              cause: { reason: RefreshTokenErrorReason.FetchError, errors: err.errors }\n            }\n          };\n        }\n        return {\n          data: null,\n          error: {\n            message: err.errors[0].code,\n            cause: { reason: err.errors[0].code, errors: err.errors }\n          }\n        };\n      } else {\n        return {\n          data: null,\n          error: {\n            message: `Unexpected Server/BAPI error`,\n            cause: { reason: RefreshTokenErrorReason.UnexpectedBAPIError, errors: [err] }\n          }\n        };\n      }\n    }\n  }\n  async function attemptRefresh(authenticateContext2) {\n    const { data: cookiesToSet, error } = await refreshToken(authenticateContext2);\n    if (!cookiesToSet || cookiesToSet.length === 0) {\n      return { data: null, error };\n    }\n    const headers = new Headers();\n    let sessionToken = \"\";\n    cookiesToSet.forEach((x) => {\n      headers.append(\"Set-Cookie\", x);\n      if (getCookieName(x).startsWith(constants.Cookies.Session)) {\n        sessionToken = getCookieValue(x);\n      }\n    });\n    const { data: jwtPayload, errors } = await verifyToken(sessionToken, authenticateContext2);\n    if (errors) {\n      return {\n        data: null,\n        error: {\n          message: `Clerk: unable to verify refreshed session token.`,\n          cause: { reason: RefreshTokenErrorReason.InvalidSessionToken, errors }\n        }\n      };\n    }\n    return { data: { jwtPayload, sessionToken, headers }, error: null };\n  }\n  function handleMaybeHandshakeStatus(authenticateContext2, reason, message, headers) {\n    if (isRequestEligibleForHandshake(authenticateContext2)) {\n      const handshakeHeaders = headers ?? buildRedirectToHandshake({ handshakeReason: reason });\n      if (handshakeHeaders.get(constants.Headers.Location)) {\n        handshakeHeaders.set(constants.Headers.CacheControl, \"no-store\");\n      }\n      const isRedirectLoop = setHandshakeInfiniteRedirectionLoopHeaders(handshakeHeaders);\n      if (isRedirectLoop) {\n        const msg = `Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard.`;\n        console.log(msg);\n        return signedOut(authenticateContext2, reason, message);\n      }\n      return handshake(authenticateContext2, reason, message, handshakeHeaders);\n    }\n    return signedOut(authenticateContext2, reason, message);\n  }\n  function handleMaybeOrganizationSyncHandshake(authenticateContext2, auth) {\n    const organizationSyncTarget = getOrganizationSyncTarget(\n      authenticateContext2.clerkUrl,\n      options.organizationSyncOptions,\n      organizationSyncTargetMatchers\n    );\n    if (!organizationSyncTarget) {\n      return null;\n    }\n    let mustActivate = false;\n    if (organizationSyncTarget.type === \"organization\") {\n      if (organizationSyncTarget.organizationSlug && organizationSyncTarget.organizationSlug !== auth.orgSlug) {\n        mustActivate = true;\n      }\n      if (organizationSyncTarget.organizationId && organizationSyncTarget.organizationId !== auth.orgId) {\n        mustActivate = true;\n      }\n    }\n    if (organizationSyncTarget.type === \"personalAccount\" && auth.orgId) {\n      mustActivate = true;\n    }\n    if (!mustActivate) {\n      return null;\n    }\n    if (authenticateContext2.handshakeRedirectLoopCounter > 0) {\n      console.warn(\n        \"Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation.\"\n      );\n      return null;\n    }\n    const handshakeState = handleMaybeHandshakeStatus(\n      authenticateContext2,\n      AuthErrorReason.ActiveOrganizationMismatch,\n      \"\"\n    );\n    if (handshakeState.status !== \"handshake\") {\n      return null;\n    }\n    return handshakeState;\n  }\n  async function authenticateRequestWithTokenInHeader() {\n    const { sessionTokenInHeader } = authenticateContext;\n    try {\n      const { data, errors } = await verifyToken(sessionTokenInHeader, authenticateContext);\n      if (errors) {\n        throw errors[0];\n      }\n      return signedIn(authenticateContext, data, void 0, sessionTokenInHeader);\n    } catch (err) {\n      return handleError(err, \"header\");\n    }\n  }\n  function setHandshakeInfiniteRedirectionLoopHeaders(headers) {\n    if (authenticateContext.handshakeRedirectLoopCounter === 3) {\n      return true;\n    }\n    const newCounterValue = authenticateContext.handshakeRedirectLoopCounter + 1;\n    const cookieName = constants.Cookies.RedirectCount;\n    headers.append(\"Set-Cookie\", `${cookieName}=${newCounterValue}; SameSite=Lax; HttpOnly; Max-Age=3`);\n    return false;\n  }\n  function handleHandshakeTokenVerificationErrorInDevelopment(error) {\n    if (error.reason === TokenVerificationErrorReason.TokenInvalidSignature) {\n      const msg = `Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.`;\n      throw new Error(msg);\n    }\n    throw new Error(`Clerk: Handshake token verification failed: ${error.getFullMessage()}.`);\n  }\n  async function authenticateRequestWithTokenInCookie() {\n    const hasActiveClient = authenticateContext.clientUat;\n    const hasSessionToken = !!authenticateContext.sessionTokenInCookie;\n    const hasDevBrowserToken = !!authenticateContext.devBrowserToken;\n    if (authenticateContext.handshakeToken) {\n      try {\n        return await resolveHandshake();\n      } catch (error) {\n        if (error instanceof TokenVerificationError && authenticateContext.instanceType === \"development\") {\n          handleHandshakeTokenVerificationErrorInDevelopment(error);\n        } else {\n          console.error(\"Clerk: unable to resolve handshake:\", error);\n        }\n      }\n    }\n    if (authenticateContext.instanceType === \"development\" && authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.DevBrowser)) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserSync, \"\");\n    }\n    const isRequestEligibleForMultiDomainSync = authenticateContext.isSatellite && authenticateContext.secFetchDest === \"document\";\n    if (authenticateContext.instanceType === \"production\" && isRequestEligibleForMultiDomainSync) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, \"\");\n    }\n    if (authenticateContext.instanceType === \"development\" && isRequestEligibleForMultiDomainSync && !authenticateContext.clerkUrl.searchParams.has(constants.QueryParameters.ClerkSynced)) {\n      const redirectURL = new URL(authenticateContext.signInUrl);\n      redirectURL.searchParams.append(\n        constants.QueryParameters.ClerkRedirectUrl,\n        authenticateContext.clerkUrl.toString()\n      );\n      const headers = new Headers({ [constants.Headers.Location]: redirectURL.toString() });\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SatelliteCookieNeedsSyncing, \"\", headers);\n    }\n    const redirectUrl = new URL(authenticateContext.clerkUrl).searchParams.get(\n      constants.QueryParameters.ClerkRedirectUrl\n    );\n    if (authenticateContext.instanceType === \"development\" && !authenticateContext.isSatellite && redirectUrl) {\n      const redirectBackToSatelliteUrl = new URL(redirectUrl);\n      if (authenticateContext.devBrowserToken) {\n        redirectBackToSatelliteUrl.searchParams.append(\n          constants.QueryParameters.DevBrowser,\n          authenticateContext.devBrowserToken\n        );\n      }\n      redirectBackToSatelliteUrl.searchParams.append(constants.QueryParameters.ClerkSynced, \"true\");\n      const headers = new Headers({ [constants.Headers.Location]: redirectBackToSatelliteUrl.toString() });\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.PrimaryRespondsToSyncing, \"\", headers);\n    }\n    if (authenticateContext.instanceType === \"development\" && !hasDevBrowserToken) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.DevBrowserMissing, \"\");\n    }\n    if (!hasActiveClient && !hasSessionToken) {\n      return signedOut(authenticateContext, AuthErrorReason.SessionTokenAndUATMissing, \"\");\n    }\n    if (!hasActiveClient && hasSessionToken) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenWithoutClientUAT, \"\");\n    }\n    if (hasActiveClient && !hasSessionToken) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.ClientUATWithoutSessionToken, \"\");\n    }\n    const { data: decodeResult, errors: decodedErrors } = decodeJwt(authenticateContext.sessionTokenInCookie);\n    if (decodedErrors) {\n      return handleError(decodedErrors[0], \"cookie\");\n    }\n    if (decodeResult.payload.iat < authenticateContext.clientUat) {\n      return handleMaybeHandshakeStatus(authenticateContext, AuthErrorReason.SessionTokenIATBeforeClientUAT, \"\");\n    }\n    try {\n      const { data, errors } = await verifyToken(authenticateContext.sessionTokenInCookie, authenticateContext);\n      if (errors) {\n        throw errors[0];\n      }\n      const signedInRequestState = signedIn(\n        authenticateContext,\n        data,\n        void 0,\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        authenticateContext.sessionTokenInCookie\n      );\n      const handshakeRequestState = handleMaybeOrganizationSyncHandshake(\n        authenticateContext,\n        signedInRequestState.toAuth()\n      );\n      if (handshakeRequestState) {\n        return handshakeRequestState;\n      }\n      return signedInRequestState;\n    } catch (err) {\n      return handleError(err, \"cookie\");\n    }\n    return signedOut(authenticateContext, AuthErrorReason.UnexpectedError);\n  }\n  async function handleError(err, tokenCarrier) {\n    if (!(err instanceof TokenVerificationError)) {\n      return signedOut(authenticateContext, AuthErrorReason.UnexpectedError);\n    }\n    let refreshError;\n    if (isRequestEligibleForRefresh(err, authenticateContext, request)) {\n      const { data, error } = await attemptRefresh(authenticateContext);\n      if (data) {\n        return signedIn(authenticateContext, data.jwtPayload, data.headers, data.sessionToken);\n      }\n      if (error?.cause?.reason) {\n        refreshError = error.cause.reason;\n      } else {\n        refreshError = RefreshTokenErrorReason.UnexpectedSDKError;\n      }\n    } else {\n      if (request.method !== \"GET\") {\n        refreshError = RefreshTokenErrorReason.NonEligibleNonGet;\n      } else if (!authenticateContext.refreshTokenInCookie) {\n        refreshError = RefreshTokenErrorReason.NonEligibleNoCookie;\n      } else {\n        refreshError = null;\n      }\n    }\n    err.tokenCarrier = tokenCarrier;\n    const reasonToHandshake = [\n      TokenVerificationErrorReason.TokenExpired,\n      TokenVerificationErrorReason.TokenNotActiveYet,\n      TokenVerificationErrorReason.TokenIatInTheFuture\n    ].includes(err.reason);\n    if (reasonToHandshake) {\n      return handleMaybeHandshakeStatus(\n        authenticateContext,\n        convertTokenVerificationErrorReasonToAuthErrorReason({ tokenError: err.reason, refreshError }),\n        err.getFullMessage()\n      );\n    }\n    return signedOut(authenticateContext, err.reason, err.getFullMessage());\n  }\n  if (authenticateContext.sessionTokenInHeader) {\n    return authenticateRequestWithTokenInHeader();\n  }\n  return authenticateRequestWithTokenInCookie();\n}\nvar debugRequestState = (params) => {\n  const { isSignedIn, proxyUrl, reason, message, publishableKey, isSatellite, domain } = params;\n  return { isSignedIn, proxyUrl, reason, message, publishableKey, isSatellite, domain };\n};\nfunction computeOrganizationSyncTargetMatchers(options) {\n  let personalAccountMatcher = null;\n  if (options?.personalAccountPatterns) {\n    try {\n      personalAccountMatcher = match(options.personalAccountPatterns);\n    } catch (e) {\n      throw new Error(`Invalid personal account pattern \"${options.personalAccountPatterns}\": \"${e}\"`);\n    }\n  }\n  let organizationMatcher = null;\n  if (options?.organizationPatterns) {\n    try {\n      organizationMatcher = match(options.organizationPatterns);\n    } catch (e) {\n      throw new Error(`Clerk: Invalid organization pattern \"${options.organizationPatterns}\": \"${e}\"`);\n    }\n  }\n  return {\n    OrganizationMatcher: organizationMatcher,\n    PersonalAccountMatcher: personalAccountMatcher\n  };\n}\nfunction getOrganizationSyncTarget(url, options, matchers) {\n  if (!options) {\n    return null;\n  }\n  if (matchers.OrganizationMatcher) {\n    let orgResult;\n    try {\n      orgResult = matchers.OrganizationMatcher(url.pathname);\n    } catch (e) {\n      console.error(`Clerk: Failed to apply organization pattern \"${options.organizationPatterns}\" to a path`, e);\n      return null;\n    }\n    if (orgResult && \"params\" in orgResult) {\n      const params = orgResult.params;\n      if (\"id\" in params && typeof params.id === \"string\") {\n        return { type: \"organization\", organizationId: params.id };\n      }\n      if (\"slug\" in params && typeof params.slug === \"string\") {\n        return { type: \"organization\", organizationSlug: params.slug };\n      }\n      console.warn(\n        \"Clerk: Detected an organization pattern match, but no organization ID or slug was found in the URL. Does the pattern include `:id` or `:slug`?\"\n      );\n    }\n  }\n  if (matchers.PersonalAccountMatcher) {\n    let personalResult;\n    try {\n      personalResult = matchers.PersonalAccountMatcher(url.pathname);\n    } catch (e) {\n      console.error(`Failed to apply personal account pattern \"${options.personalAccountPatterns}\" to a path`, e);\n      return null;\n    }\n    if (personalResult) {\n      return { type: \"personalAccount\" };\n    }\n  }\n  return null;\n}\nfunction getOrganizationSyncQueryParams(toActivate) {\n  const ret = /* @__PURE__ */ new Map();\n  if (toActivate.type === \"personalAccount\") {\n    ret.set(\"organization_id\", \"\");\n  }\n  if (toActivate.type === \"organization\") {\n    if (toActivate.organizationId) {\n      ret.set(\"organization_id\", toActivate.organizationId);\n    }\n    if (toActivate.organizationSlug) {\n      ret.set(\"organization_id\", toActivate.organizationSlug);\n    }\n  }\n  return ret;\n}\nvar convertTokenVerificationErrorReasonToAuthErrorReason = ({\n  tokenError,\n  refreshError\n}) => {\n  switch (tokenError) {\n    case TokenVerificationErrorReason.TokenExpired:\n      return `${AuthErrorReason.SessionTokenExpired}-refresh-${refreshError}`;\n    case TokenVerificationErrorReason.TokenNotActiveYet:\n      return AuthErrorReason.SessionTokenNBF;\n    case TokenVerificationErrorReason.TokenIatInTheFuture:\n      return AuthErrorReason.SessionTokenIatInTheFuture;\n    default:\n      return AuthErrorReason.UnexpectedError;\n  }\n};\n\n// src/util/mergePreDefinedOptions.ts\nfunction mergePreDefinedOptions(preDefinedOptions, options) {\n  return Object.keys(preDefinedOptions).reduce(\n    (obj, key) => {\n      return { ...obj, [key]: options[key] || obj[key] };\n    },\n    { ...preDefinedOptions }\n  );\n}\n\n// src/tokens/factory.ts\nvar defaultOptions = {\n  secretKey: \"\",\n  jwtKey: \"\",\n  apiUrl: void 0,\n  apiVersion: void 0,\n  proxyUrl: \"\",\n  publishableKey: \"\",\n  isSatellite: false,\n  domain: \"\",\n  audience: \"\"\n};\nfunction createAuthenticateRequest(params) {\n  const buildTimeOptions = mergePreDefinedOptions(defaultOptions, params.options);\n  const apiClient = params.apiClient;\n  const authenticateRequest2 = (request, options = {}) => {\n    const { apiUrl, apiVersion } = buildTimeOptions;\n    const runTimeOptions = mergePreDefinedOptions(buildTimeOptions, options);\n    return authenticateRequest(request, {\n      ...options,\n      ...runTimeOptions,\n      // We should add all the omitted props from options here (eg apiUrl / apiVersion)\n      // to avoid runtime options override them.\n      apiUrl,\n      apiVersion,\n      apiClient\n    });\n  };\n  return {\n    authenticateRequest: authenticateRequest2,\n    debugRequestState\n  };\n}\n\nexport {\n  errorThrower,\n  parsePublishableKey,\n  constants,\n  createBackendApiClient,\n  signedInAuthObject,\n  signedOutAuthObject,\n  makeAuthObjectSerializable,\n  AuthStatus,\n  createClerkRequest,\n  verifyToken,\n  debugRequestState,\n  createAuthenticateRequest\n};\n//# sourceMappingURL=chunk-H5XWF6TY.mjs.map", "import \"./chunk-7ELT755Q.mjs\";\n\n// src/buildAccountsBaseUrl.ts\nfunction buildAccountsBaseUrl(frontendApi) {\n  if (!frontendApi) {\n    return \"\";\n  }\n  const accountsBaseUrl = frontendApi.replace(/clerk\\.accountsstage\\./, \"accountsstage.\").replace(/clerk\\.accounts\\.|clerk\\./, \"accounts.\");\n  return `https://${accountsBaseUrl}`;\n}\nexport {\n  buildAccountsBaseUrl\n};\n//# sourceMappingURL=buildAccountsBaseUrl.mjs.map", "import {\n  isReverificationHint,\n  reverificationError,\n  reverificationErrorResponse\n} from \"./chunk-43A5F2IE.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  isReverificationHint,\n  reverificationError,\n  reverificationErrorResponse\n};\n//# sourceMappingURL=authorization-errors.mjs.map", "import {\n  AuthStatus,\n  constants,\n  createAuthenticateRequest,\n  createBackendApiClient,\n  createClerkRequest,\n  debugRequestState,\n  errorThrower,\n  makeAuthObjectSerializable,\n  parsePublishableKey,\n  signedInAuthObject,\n  signedOutAuthObject\n} from \"./chunk-H5XWF6TY.mjs\";\nimport \"./chunk-AT3FJU3M.mjs\";\nimport \"./chunk-5JS2VYLU.mjs\";\n\n// src/createRedirect.ts\nimport { buildAccountsBaseUrl } from \"@clerk/shared/buildAccountsBaseUrl\";\nvar buildUrl = (_baseUrl, _targetUrl, _returnBackUrl, _devBrowserToken) => {\n  if (_baseUrl === \"\") {\n    return legacyBuildUrl(_targetUrl.toString(), _returnBackUrl?.toString());\n  }\n  const baseUrl = new URL(_baseUrl);\n  const returnBackUrl = _returnBackUrl ? new URL(_returnBackUrl, baseUrl) : void 0;\n  const res = new URL(_targetUrl, baseUrl);\n  if (returnBackUrl) {\n    res.searchParams.set(\"redirect_url\", returnBackUrl.toString());\n  }\n  if (_devBrowserToken && baseUrl.hostname !== res.hostname) {\n    res.searchParams.set(constants.QueryParameters.DevBrowser, _devBrowserToken);\n  }\n  return res.toString();\n};\nvar legacyBuildUrl = (targetUrl, redirectUrl) => {\n  let url;\n  if (!targetUrl.startsWith(\"http\")) {\n    if (!redirectUrl || !redirectUrl.startsWith(\"http\")) {\n      throw new Error(\"destination url or return back url should be an absolute path url!\");\n    }\n    const baseURL = new URL(redirectUrl);\n    url = new URL(targetUrl, baseURL.origin);\n  } else {\n    url = new URL(targetUrl);\n  }\n  if (redirectUrl) {\n    url.searchParams.set(\"redirect_url\", redirectUrl);\n  }\n  return url.toString();\n};\nvar createRedirect = (params) => {\n  const { publishableKey, redirectAdapter, signInUrl, signUpUrl, baseUrl } = params;\n  const parsedPublishableKey = parsePublishableKey(publishableKey);\n  const frontendApi = parsedPublishableKey?.frontendApi;\n  const isDevelopment = parsedPublishableKey?.instanceType === \"development\";\n  const accountsBaseUrl = buildAccountsBaseUrl(frontendApi);\n  const redirectToSignUp = ({ returnBackUrl } = {}) => {\n    if (!signUpUrl && !accountsBaseUrl) {\n      errorThrower.throwMissingPublishableKeyError();\n    }\n    const accountsSignUpUrl = `${accountsBaseUrl}/sign-up`;\n    return redirectAdapter(\n      buildUrl(baseUrl, signUpUrl || accountsSignUpUrl, returnBackUrl, isDevelopment ? params.devBrowserToken : null)\n    );\n  };\n  const redirectToSignIn = ({ returnBackUrl } = {}) => {\n    if (!signInUrl && !accountsBaseUrl) {\n      errorThrower.throwMissingPublishableKeyError();\n    }\n    const accountsSignInUrl = `${accountsBaseUrl}/sign-in`;\n    return redirectAdapter(\n      buildUrl(baseUrl, signInUrl || accountsSignInUrl, returnBackUrl, isDevelopment ? params.devBrowserToken : null)\n    );\n  };\n  return { redirectToSignUp, redirectToSignIn };\n};\n\n// src/util/decorateObjectWithResources.ts\nvar decorateObjectWithResources = async (obj, authObj, opts) => {\n  const { loadSession, loadUser, loadOrganization } = opts || {};\n  const { userId, sessionId, orgId } = authObj;\n  const { sessions, users, organizations } = createBackendApiClient({ ...opts });\n  const [sessionResp, userResp, organizationResp] = await Promise.all([\n    loadSession && sessionId ? sessions.getSession(sessionId) : Promise.resolve(void 0),\n    loadUser && userId ? users.getUser(userId) : Promise.resolve(void 0),\n    loadOrganization && orgId ? organizations.getOrganization({ organizationId: orgId }) : Promise.resolve(void 0)\n  ]);\n  const resources = stripPrivateDataFromObject({\n    session: sessionResp,\n    user: userResp,\n    organization: organizationResp\n  });\n  return Object.assign(obj, resources);\n};\nfunction stripPrivateDataFromObject(authObject) {\n  const user = authObject.user ? { ...authObject.user } : authObject.user;\n  const organization = authObject.organization ? { ...authObject.organization } : authObject.organization;\n  prunePrivateMetadata(user);\n  prunePrivateMetadata(organization);\n  return { ...authObject, user, organization };\n}\nfunction prunePrivateMetadata(resource) {\n  if (resource) {\n    if (\"privateMetadata\" in resource) {\n      delete resource[\"privateMetadata\"];\n    }\n    if (\"private_metadata\" in resource) {\n      delete resource[\"private_metadata\"];\n    }\n  }\n  return resource;\n}\n\n// src/internal.ts\nimport { reverificationError, reverificationErrorResponse } from \"@clerk/shared/authorization-errors\";\nexport {\n  AuthStatus,\n  constants,\n  createAuthenticateRequest,\n  createClerkRequest,\n  createRedirect,\n  debugRequestState,\n  decorateObjectWithResources,\n  makeAuthObjectSerializable,\n  reverificationError,\n  reverificationErrorResponse,\n  signedInAuthObject,\n  signedOutAuthObject,\n  stripPrivateDataFromObject\n};\n//# sourceMappingURL=internal.mjs.map", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "import {\n  parsePublishableKey\n} from \"./chunk-G3VP5PJE.mjs\";\nimport {\n  LEGACY_DEV_INSTANCE_SUFFIXES,\n  LOCAL_API_URL,\n  LOCAL_ENV_SUFFIXES,\n  PROD_API_URL,\n  STAGING_API_URL,\n  STAGING_ENV_SUFFIXES\n} from \"./chunk-I6MTSTOF.mjs\";\n\n// src/apiUrlFromPublishableKey.ts\nvar apiUrlFromPublishableKey = (publishableKey) => {\n  const frontendApi = parsePublishableKey(publishableKey)?.frontendApi;\n  if (frontendApi?.startsWith(\"clerk.\") && LEGACY_DEV_INSTANCE_SUFFIXES.some((suffix) => frontendApi?.endsWith(suffix))) {\n    return PROD_API_URL;\n  }\n  if (LOCAL_ENV_SUFFIXES.some((suffix) => frontendApi?.endsWith(suffix))) {\n    return LOCAL_API_URL;\n  }\n  if (STAGING_ENV_SUFFIXES.some((suffix) => frontendApi?.endsWith(suffix))) {\n    return STAGING_API_URL;\n  }\n  return PROD_API_URL;\n};\n\nexport {\n  apiUrlFromPublishableKey\n};\n//# sourceMappingURL=chunk-NNO3XJ5E.mjs.map", "import {\n  apiUrlFromPublishableKey\n} from \"./chunk-NNO3XJ5E.mjs\";\nimport \"./chunk-G3VP5PJE.mjs\";\nimport \"./chunk-TETGTEI2.mjs\";\nimport \"./chunk-KOH7GTJO.mjs\";\nimport \"./chunk-I6MTSTOF.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  apiUrlFromPublishableKey\n};\n//# sourceMappingURL=apiUrlFromPublishableKey.mjs.map", "import \"../chunk-BUSYA2B4.js\";\nimport { apiUrlFromPublishableKey } from \"@clerk/shared/apiUrlFromPublishableKey\";\nimport { isTruthy } from \"@clerk/shared/underscore\";\nconst CLERK_JS_VERSION = process.env.NEXT_PUBLIC_CLERK_JS_VERSION || \"\";\nconst CLERK_JS_URL = process.env.NEXT_PUBLIC_CLERK_JS_URL || \"\";\nconst API_VERSION = process.env.CLERK_API_VERSION || \"v1\";\nconst SECRET_KEY = process.env.CLERK_SECRET_KEY || \"\";\nconst PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || \"\";\nconst ENCRYPTION_KEY = process.env.CLERK_ENCRYPTION_KEY || \"\";\nconst API_URL = process.env.CLERK_API_URL || apiUrlFromPublishableKey(PUBLISHABLE_KEY);\nconst DOMAIN = process.env.NEXT_PUBLIC_CLERK_DOMAIN || \"\";\nconst PROXY_URL = process.env.NEXT_PUBLIC_CLERK_PROXY_URL || \"\";\nconst IS_SATELLITE = isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE) || false;\nconst SIGN_IN_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || \"\";\nconst SIGN_UP_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || \"\";\nconst SDK_METADATA = {\n  name: \"@clerk/nextjs\",\n  version: \"6.12.12\",\n  environment: process.env.NODE_ENV\n};\nconst TELEMETRY_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED);\nconst TELEMETRY_DEBUG = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);\nconst KEYLESS_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED) || false;\nexport {\n  API_URL,\n  API_VERSION,\n  CLERK_JS_URL,\n  CLERK_JS_VERSION,\n  DOMAIN,\n  ENCRYPTION_KEY,\n  IS_SATELLITE,\n  KEYLESS_DISABLED,\n  PROXY_URL,\n  PUBLISHABLE_KEY,\n  SDK_METADATA,\n  SECRET_KEY,\n  SIGN_IN_URL,\n  SIGN_UP_URL,\n  TELEMETRY_DEBUG,\n  TELEMETRY_DISABLED\n};\n//# sourceMappingURL=constants.js.map", "import \"../chunk-BUSYA2B4.js\";\nimport { isDevelopmentEnvironment } from \"@clerk/shared/utils\";\nimport { KEYLESS_DISABLED } from \"../server/constants\";\nimport { isNextWithUnstableServerActions } from \"./sdk-versions\";\nconst canUseKeyless = !isNextWithUnstableServerActions && // Next.js will inline the value of 'development' or 'production' on the client bundle, so this is client-safe.\nisDevelopmentEnvironment() && !KEYLESS_DISABLED;\nexport {\n  canUseKeyless\n};\n//# sourceMappingURL=feature-flags.js.map", "// src/underscore.ts\nvar toSentence = (items) => {\n  if (items.length == 0) {\n    return \"\";\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(\", \");\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\nvar IP_V4_ADDRESS_REGEX = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\nfunction isIPV4Address(str) {\n  return IP_V4_ADDRESS_REGEX.test(str || \"\");\n}\nfunction titleize(str) {\n  const s = str || \"\";\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\nfunction snakeToCamel(str) {\n  return str ? str.replace(/([-_][a-z])/g, (match) => match.toUpperCase().replace(/-|_/, \"\")) : \"\";\n}\nfunction camelToSnake(str) {\n  return str ? str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`) : \"\";\n}\nvar createDeepObjectTransformer = (transform) => {\n  const deepTransform = (obj) => {\n    if (!obj) {\n      return obj;\n    }\n    if (Array.isArray(obj)) {\n      return obj.map((el) => {\n        if (typeof el === \"object\" || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === \"object\") {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n  return deepTransform;\n};\nvar deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\nvar deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\nfunction isTruthy(value) {\n  if (typeof value === `boolean`) {\n    return value;\n  }\n  if (value === void 0 || value === null) {\n    return false;\n  }\n  if (typeof value === `string`) {\n    if (value.toLowerCase() === `true`) {\n      return true;\n    }\n    if (value.toLowerCase() === `false`) {\n      return false;\n    }\n  }\n  const number = parseInt(value, 10);\n  if (isNaN(number)) {\n    return false;\n  }\n  if (number > 0) {\n    return true;\n  }\n  return false;\n}\nfunction getNonUndefinedValues(obj) {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== void 0) {\n      acc[key] = value;\n    }\n    return acc;\n  }, {});\n}\n\nexport {\n  toSentence,\n  isIPV4Address,\n  titleize,\n  snakeToCamel,\n  camelToSnake,\n  deepCamelToSnake,\n  deepSnakeToCamel,\n  isTruthy,\n  getNonUndefinedValues\n};\n//# sourceMappingURL=chunk-RWYTRAIK.mjs.map", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nvar SUPPORTED_LOCALE = {\n    tr: {\n        regexp: /\\u0130|\\u0049|\\u0049\\u0307/g,\n        map: {\n            İ: \"\\u0069\",\n            I: \"\\u0131\",\n            İ: \"\\u0069\",\n        },\n    },\n    az: {\n        regexp: /\\u0130/g,\n        map: {\n            İ: \"\\u0069\",\n            I: \"\\u0131\",\n            İ: \"\\u0069\",\n        },\n    },\n    lt: {\n        regexp: /\\u0049|\\u004A|\\u012E|\\u00CC|\\u00CD|\\u0128/g,\n        map: {\n            I: \"\\u0069\\u0307\",\n            J: \"\\u006A\\u0307\",\n            Į: \"\\u012F\\u0307\",\n            Ì: \"\\u0069\\u0307\\u0300\",\n            Í: \"\\u0069\\u0307\\u0301\",\n            Ĩ: \"\\u0069\\u0307\\u0303\",\n        },\n    },\n};\n/**\n * Localized lower case.\n */\nexport function localeLowerCase(str, locale) {\n    var lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n    if (lang)\n        return lowerCase(str.replace(lang.regexp, function (m) { return lang.map[m]; }));\n    return lowerCase(str);\n}\n/**\n * Lower case as a function.\n */\nexport function lowerCase(str) {\n    return str.toLowerCase();\n}\n//# sourceMappingURL=index.js.map", "import { lowerCase } from \"lower-case\";\n// Support camel case (\"camelCase\" -> \"camel Case\" and \"CAMELCase\" -> \"CAMEL Case\").\nvar DEFAULT_SPLIT_REGEXP = [/([a-z0-9])([A-Z])/g, /([A-Z])([A-Z][a-z])/g];\n// Remove all non-word characters.\nvar DEFAULT_STRIP_REGEXP = /[^A-Z0-9]+/gi;\n/**\n * Normalize the string into something other libraries can manipulate easier.\n */\nexport function noCase(input, options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.splitRegexp, splitRegexp = _a === void 0 ? DEFAULT_SPLIT_REGEXP : _a, _b = options.stripRegexp, stripRegexp = _b === void 0 ? DEFAULT_STRIP_REGEXP : _b, _c = options.transform, transform = _c === void 0 ? lowerCase : _c, _d = options.delimiter, delimiter = _d === void 0 ? \" \" : _d;\n    var result = replace(replace(input, splitRegexp, \"$1\\0$2\"), stripRegexp, \"\\0\");\n    var start = 0;\n    var end = result.length;\n    // Trim the delimiter from around the output string.\n    while (result.charAt(start) === \"\\0\")\n        start++;\n    while (result.charAt(end - 1) === \"\\0\")\n        end--;\n    // Transform each token independently.\n    return result.slice(start, end).split(\"\\0\").map(transform).join(delimiter);\n}\n/**\n * Replace `re` in the input string with the replacement value.\n */\nfunction replace(input, re, value) {\n    if (re instanceof RegExp)\n        return input.replace(re, value);\n    return re.reduce(function (input, re) { return input.replace(re, value); }, input);\n}\n//# sourceMappingURL=index.js.map", "import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nexport function dotCase(input, options) {\n    if (options === void 0) { options = {}; }\n    return noCase(input, __assign({ delimiter: \".\" }, options));\n}\n//# sourceMappingURL=index.js.map", "import { __assign } from \"tslib\";\nimport { dotCase } from \"dot-case\";\nexport function snakeCase(input, options) {\n    if (options === void 0) { options = {}; }\n    return dotCase(input, __assign({ delimiter: \"_\" }, options));\n}\n//# sourceMappingURL=index.js.map", "import {\n  camelToSnake,\n  deepCamelToSnake,\n  deepSnakeToCamel,\n  getNonUndefinedValues,\n  isIPV4Address,\n  isTruthy,\n  snakeToCamel,\n  titleize,\n  toSentence\n} from \"./chunk-RWYTRAIK.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  camelToSnake,\n  deepCamelToSnake,\n  deepSnakeToCamel,\n  getNonUndefinedValues,\n  isIPV4Address,\n  isTruthy,\n  snakeToCamel,\n  titleize,\n  toSentence\n};\n//# sourceMappingURL=underscore.mjs.map", "import \"../chunk-BUSYA2B4.js\";\nimport { constants } from \"@clerk/backend/internal\";\nfunction getCustomAttributeFromRequest(req, key) {\n  return key in req ? req[key] : void 0;\n}\nfunction getAuthKeyFromRequest(req, key) {\n  return getCustomAttributeFromRequest(req, constants.Attributes[key]) || getHeader(req, constants.Headers[key]);\n}\nfunction getHeader(req, name) {\n  var _a, _b;\n  if (isNextRequest(req) || isRequestWebAPI(req)) {\n    return req.headers.get(name);\n  }\n  return req.headers[name] || req.headers[name.toLowerCase()] || ((_b = (_a = req.socket) == null ? void 0 : _a._httpMessage) == null ? void 0 : _b.getHeader(name));\n}\nfunction detectClerkMiddleware(req) {\n  return Boolean(getAuthKeyFromRequest(req, \"AuthStatus\"));\n}\nfunction isNextRequest(val) {\n  try {\n    const { headers, nextUrl, cookies } = val || {};\n    return typeof (headers == null ? void 0 : headers.get) === \"function\" && typeof (nextUrl == null ? void 0 : nextUrl.searchParams.get) === \"function\" && typeof (cookies == null ? void 0 : cookies.get) === \"function\";\n  } catch {\n    return false;\n  }\n}\nfunction isRequestWebAPI(val) {\n  try {\n    const { headers } = val || {};\n    return typeof (headers == null ? void 0 : headers.get) === \"function\";\n  } catch {\n    return false;\n  }\n}\nexport {\n  detectClerkMiddleware,\n  getAuthKeyFromRequest,\n  getCustomAttributeFromRequest,\n  getHeader,\n  isNextRequest,\n  isRequestWebAPI\n};\n//# sourceMappingURL=headers-utils.js.map", "import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n", "// src/utils/runtimeEnvironment.ts\nvar isDevelopmentEnvironment = () => {\n  try {\n    return process.env.NODE_ENV === \"development\";\n  } catch {\n  }\n  return false;\n};\nvar isTestEnvironment = () => {\n  try {\n    return process.env.NODE_ENV === \"test\";\n  } catch {\n  }\n  return false;\n};\nvar isProductionEnvironment = () => {\n  try {\n    return process.env.NODE_ENV === \"production\";\n  } catch {\n  }\n  return false;\n};\n\nexport {\n  isDevelopmentEnvironment,\n  isTestEnvironment,\n  isProductionEnvironment\n};\n//# sourceMappingURL=chunk-7HPDNZ3R.mjs.map", "export * from '../client/components/navigation.react-server';\n\n//# sourceMappingURL=navigation.react-server.js.map", "import {\n  buildPublishable<PERSON>ey,\n  createDevOrStagingUrlCache,\n  getCookieSuffix,\n  getSuffixedCookieName,\n  isDevelopmentFromPublishableKey,\n  isDevelopmentFromSecretKey,\n  isProductionFromPublishableKey,\n  isProductionFromSecretKey,\n  isPublishable<PERSON>ey,\n  parsePublishableKey\n} from \"./chunk-G3VP5PJE.mjs\";\nimport \"./chunk-TETGTEI2.mjs\";\nimport \"./chunk-KOH7GTJO.mjs\";\nimport \"./chunk-I6MTSTOF.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  buildPublishableKey,\n  createDevOrStagingUrlCache,\n  getCookieSuffix,\n  getSuffixedCookieName,\n  isDevelopmentFromPublishableKey,\n  isDevelopmentFromSecretKey,\n  isProductionFromPublishableKey,\n  isProductionFromSecretKey,\n  isPublishableKey,\n  parsePublishableKey\n};\n//# sourceMappingURL=keys.mjs.map", "// src/error.ts\nfunction isUnauthorizedError(e) {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === \"authentication_invalid\" && status === 401;\n}\nfunction isCaptchaError(e) {\n  return [\"captcha_invalid\", \"captcha_not_enabled\", \"captcha_missing_token\"].includes(e.errors[0].code);\n}\nfunction is4xxError(e) {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\nfunction isNetworkError(e) {\n  const message = (`${e.message}${e.name}` || \"\").toLowerCase().replace(/\\s+/g, \"\");\n  return message.includes(\"networkerror\");\n}\nfunction isKnownError(error) {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\nfunction isClerkAPIResponseError(err) {\n  return \"clerkError\" in err;\n}\nfunction isClerkRuntimeError(err) {\n  return \"clerkRuntimeError\" in err;\n}\nfunction isReverificationCancelledError(err) {\n  return isClerkRuntimeError(err) && err.code === \"reverification_cancelled\";\n}\nfunction isMetamaskError(err) {\n  return \"code\" in err && [4001, 32602, 32603].includes(err.code) && \"message\" in err;\n}\nfunction isUserLockedError(err) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === \"user_locked\";\n}\nfunction isPasswordPwnedError(err) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === \"form_password_pwned\";\n}\nfunction parseErrors(data = []) {\n  return data.length > 0 ? data.map(parseError) : [];\n}\nfunction parseError(error) {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn\n    }\n  };\n}\nfunction errorToJSON(error) {\n  return {\n    code: error?.code || \"\",\n    message: error?.message || \"\",\n    long_message: error?.longMessage,\n    meta: {\n      param_name: error?.meta?.paramName,\n      session_id: error?.meta?.sessionId,\n      email_addresses: error?.meta?.emailAddresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn\n    }\n  };\n}\nvar ClerkAPIResponseError = class _ClerkAPIResponseError extends Error {\n  constructor(message, { data, status, clerkTraceId }) {\n    super(message);\n    this.toString = () => {\n      let message = `[${this.name}]\nMessage:${this.message}\nStatus:${this.status}\nSerialized errors: ${this.errors.map(\n        (e) => JSON.stringify(e)\n      )}`;\n      if (this.clerkTraceId) {\n        message += `\nClerk Trace ID: ${this.clerkTraceId}`;\n      }\n      return message;\n    };\n    Object.setPrototypeOf(this, _ClerkAPIResponseError.prototype);\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n};\nvar ClerkRuntimeError = class _ClerkRuntimeError extends Error {\n  constructor(message, { code }) {\n    const prefix = \"\\u{1F512} Clerk:\";\n    const regex = new RegExp(prefix.replace(\" \", \"\\\\s*\"), \"i\");\n    const sanitized = message.replace(regex, \"\");\n    const _message = `${prefix} ${sanitized.trim()}\n\n(code=\"${code}\")\n`;\n    super(_message);\n    /**\n     * Returns a string representation of the error.\n     *\n     * @returns {string} A formatted string with the error name and message.\n     * @memberof ClerkRuntimeError\n     */\n    this.toString = () => {\n      return `[${this.name}]\nMessage:${this.message}`;\n    };\n    Object.setPrototypeOf(this, _ClerkRuntimeError.prototype);\n    this.code = code;\n    this.message = _message;\n    this.clerkRuntimeError = true;\n    this.name = \"ClerkRuntimeError\";\n  }\n};\nvar EmailLinkError = class _EmailLinkError extends Error {\n  constructor(code) {\n    super(code);\n    this.code = code;\n    this.name = \"EmailLinkError\";\n    Object.setPrototypeOf(this, _EmailLinkError.prototype);\n  }\n};\nfunction isEmailLinkError(err) {\n  return err.name === \"EmailLinkError\";\n}\nvar EmailLinkErrorCode = {\n  Expired: \"expired\",\n  Failed: \"failed\",\n  ClientMismatch: \"client_mismatch\"\n};\nvar EmailLinkErrorCodeStatus = {\n  Expired: \"expired\",\n  Failed: \"failed\",\n  ClientMismatch: \"client_mismatch\"\n};\nvar DefaultMessages = Object.freeze({\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`\n});\nfunction buildErrorThrower({ packageName, customMessages }) {\n  let pkg = packageName;\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages\n  };\n  function buildMessage(rawMessage, replacements) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || \"\").toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n    return `${pkg}: ${msg}`;\n  }\n  return {\n    setPackageName({ packageName: packageName2 }) {\n      if (typeof packageName2 === \"string\") {\n        pkg = packageName2;\n      }\n      return this;\n    },\n    setMessages({ customMessages: customMessages2 }) {\n      Object.assign(messages, customMessages2 || {});\n      return this;\n    },\n    throwInvalidPublishableKeyError(params) {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n    throwInvalidProxyUrl(params) {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n    throwMissingPublishableKeyError() {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n    throwMissingSecretKeyError() {\n      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));\n    },\n    throwMissingClerkProviderError(params) {\n      throw new Error(buildMessage(messages.MissingClerkProvider, params));\n    },\n    throw(message) {\n      throw new Error(buildMessage(message));\n    }\n  };\n}\nvar ClerkWebAuthnError = class extends ClerkRuntimeError {\n  constructor(message, { code }) {\n    super(message, { code });\n    this.code = code;\n  }\n};\n\nexport {\n  isUnauthorizedError,\n  isCaptchaError,\n  is4xxError,\n  isNetworkError,\n  isKnownError,\n  isClerkAPIResponseError,\n  isClerkRuntimeError,\n  isReverificationCancelledError,\n  isMetamaskError,\n  isUserLockedError,\n  isPasswordPwnedError,\n  parseErrors,\n  parseError,\n  errorToJSON,\n  ClerkAPIResponseError,\n  ClerkRuntimeError,\n  EmailLinkError,\n  isEmailLinkError,\n  EmailLinkErrorCode,\n  EmailLinkErrorCodeStatus,\n  buildErrorThrower,\n  ClerkWebAuthnError\n};\n//# sourceMappingURL=chunk-3EORDU4Z.mjs.map", "import {\n  Clerk<PERSON><PERSON><PERSON>po<PERSON><PERSON>rror,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  ClerkWebAuthnError,\n  EmailLinkError,\n  EmailLinkErrorCode,\n  EmailLinkErrorCodeStatus,\n  buildErrorThrower,\n  errorToJSON,\n  is4xxError,\n  isCaptchaError,\n  isClerkAPIResponseError,\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isMetamaskError,\n  isNetworkError,\n  isPasswordPwnedError,\n  isReverificationCancelledError,\n  isUnauthorizedError,\n  isUserLockedError,\n  parseError,\n  parseErrors\n} from \"./chunk-3EORDU4Z.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  ClerkAPIResponseError,\n  ClerkRuntimeError,\n  ClerkWebAuthnError,\n  EmailLinkError,\n  EmailLinkErrorCode,\n  EmailLinkErrorCodeStatus,\n  buildErrorThrower,\n  errorToJSON,\n  is4xxError,\n  isC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  is<PERSON><PERSON>kAPIResponseError,\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isMetamaskError,\n  isNetworkError,\n  isPasswordPwnedError,\n  isReverificationCancelledError,\n  isUnauthorizedError,\n  isUserLockedError,\n  parseError,\n  parseErrors\n};\n//# sourceMappingURL=error.mjs.map", "import {\n  fastDeepMergeAndKeep,\n  fastDeepMergeAndReplace,\n  logErrorInDevMode\n} from \"../chunk-3QKZJFQO.mjs\";\nimport {\n  createDeferredPromise\n} from \"../chunk-4S4AJ26F.mjs\";\nimport {\n  noop\n} from \"../chunk-7FNX7RWY.mjs\";\nimport {\n  handleValueOrFn\n} from \"../chunk-O32JQBM6.mjs\";\nimport {\n  isStaging\n} from \"../chunk-3TMSNP4L.mjs\";\nimport {\n  isDevelopmentEnvironment,\n  isProductionEnvironment,\n  isTestEnvironment\n} from \"../chunk-7HPDNZ3R.mjs\";\nimport \"../chunk-7ELT755Q.mjs\";\nexport {\n  createDeferredPromise,\n  fastDeepMergeAndKeep,\n  fastDeepMergeAndReplace,\n  handleValueOrFn,\n  isDevelopmentEnvironment,\n  isProductionEnvironment,\n  isStaging,\n  isTestEnvironment,\n  logErrorInDevMode,\n  noop\n};\n//# sourceMappingURL=index.mjs.map", "import \"../chunk-BUSYA2B4.js\";\nimport nextPkg from \"next/package.json\";\nconst isNext13 = nextPkg.version.startsWith(\"13.\");\nconst isNextWithUnstableServerActions = isNext13 || nextPkg.version.startsWith(\"14.0\");\nexport {\n  isNext13,\n  isNextWithUnstableServerActions\n};\n//# sourceMappingURL=sdk-versions.js.map", "'use strict';\n\nconst isObject = value => typeof value === 'object' && value !== null;\nconst mapObjectSkip = Symbol('skip');\n\n// Customized for this use-case\nconst isObjectCustom = value =>\n\tisObject(value) &&\n\t!(value instanceof RegExp) &&\n\t!(value instanceof Error) &&\n\t!(value instanceof Date);\n\nconst mapObject = (object, mapper, options, isSeen = new WeakMap()) => {\n\toptions = {\n\t\tdeep: false,\n\t\ttarget: {},\n\t\t...options\n\t};\n\n\tif (isSeen.has(object)) {\n\t\treturn isSeen.get(object);\n\t}\n\n\tisSeen.set(object, options.target);\n\n\tconst {target} = options;\n\tdelete options.target;\n\n\tconst mapArray = array => array.map(element => isObjectCustom(element) ? mapObject(element, mapper, options, isSeen) : element);\n\tif (Array.isArray(object)) {\n\t\treturn mapArray(object);\n\t}\n\n\tfor (const [key, value] of Object.entries(object)) {\n\t\tconst mapResult = mapper(key, value, object);\n\n\t\tif (mapResult === mapObjectSkip) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet [newKey, newValue, {shouldRecurse = true} = {}] = mapResult;\n\n\t\t// Drop `__proto__` keys.\n\t\tif (newKey === '__proto__') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (options.deep && shouldRecurse && isObjectCustom(newValue)) {\n\t\t\tnewValue = Array.isArray(newValue) ?\n\t\t\t\tmapArray(newValue) :\n\t\t\t\tmapObject(newValue, mapper, options, isSeen);\n\t\t}\n\n\t\ttarget[newKey] = newValue;\n\t}\n\n\treturn target;\n};\n\nmodule.exports = (object, mapper, options) => {\n\tif (!isObject(object)) {\n\t\tthrow new TypeError(`Expected an object, got \\`${object}\\` (${typeof object})`);\n\t}\n\n\treturn mapObject(object, mapper, options);\n};\n\nmodule.exports.mapObjectSkip = mapObjectSkip;\n", "'use strict'\n\nconst map = require('map-obj')\nconst { snakeCase } = require('snake-case')\n\nconst PlainObjectConstructor = {}.constructor\n\nmodule.exports = function (obj, options) {\n  if (Array.isArray(obj)) {\n    if (obj.some(item => item.constructor !== PlainObjectConstructor)) {\n      throw new Error('obj must be array of plain objects')\n    }\n  } else {\n    if (obj.constructor !== PlainObjectConstructor) {\n      throw new Error('obj must be an plain object')\n    }\n  }\n\n  options = Object.assign({ deep: true, exclude: [], parsingOptions: {} }, options)\n\n  return map(obj, function (key, val) {\n    return [\n      matches(options.exclude, key) ? key : snakeCase(key, options.parsingOptions),\n      val,\n      mapperOptions(key, val, options)\n    ]\n  }, options)\n}\n\nfunction matches (patterns, value) {\n  return patterns.some(function (pattern) {\n    return typeof pattern === 'string'\n      ? pattern === value\n      : pattern.test(value)\n  })\n}\n\nfunction mapperOptions (key, val, options) {\n  return options.shouldRecurse\n    ? { shouldRecurse: options.shouldRecurse(key, val) }\n    : undefined\n}\n", "/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n", "// src/errors.ts\nvar TokenVerificationErrorCode = {\n  InvalidSecretKey: \"clerk_key_invalid\"\n};\nvar TokenVerificationErrorReason = {\n  TokenExpired: \"token-expired\",\n  TokenInvalid: \"token-invalid\",\n  TokenInvalidAlgorithm: \"token-invalid-algorithm\",\n  TokenInvalidAuthorizedParties: \"token-invalid-authorized-parties\",\n  TokenInvalidSignature: \"token-invalid-signature\",\n  TokenNotActiveYet: \"token-not-active-yet\",\n  TokenIatInTheFuture: \"token-iat-in-the-future\",\n  TokenVerificationFailed: \"token-verification-failed\",\n  InvalidSecretKey: \"secret-key-invalid\",\n  LocalJWKMissing: \"jwk-local-missing\",\n  RemoteJWKFailedToLoad: \"jwk-remote-failed-to-load\",\n  RemoteJWKInvalid: \"jwk-remote-invalid\",\n  RemoteJWKMissing: \"jwk-remote-missing\",\n  JWKFailedToResolve: \"jwk-failed-to-resolve\",\n  JWKKidMismatch: \"jwk-kid-mismatch\"\n};\nvar TokenVerificationErrorAction = {\n  ContactSupport: \"Contact <EMAIL>\",\n  EnsureClerkJWT: \"Make sure that this is a valid Clerk generate JWT.\",\n  SetClerkJWTKey: \"Set the CLERK_JWT_KEY environment variable.\",\n  SetClerkSecretKey: \"Set the CLERK_SECRET_KEY environment variable.\",\n  EnsureClockSync: \"Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization).\"\n};\nvar TokenVerificationError = class _TokenVerificationError extends Error {\n  constructor({\n    action,\n    message,\n    reason\n  }) {\n    super(message);\n    Object.setPrototypeOf(this, _TokenVerificationError.prototype);\n    this.reason = reason;\n    this.message = message;\n    this.action = action;\n  }\n  getFullMessage() {\n    return `${[this.message, this.action].filter((m) => m).join(\" \")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`;\n  }\n};\nvar SignJWTError = class extends Error {\n};\n\nexport {\n  TokenVerificationErrorCode,\n  TokenVerificationErrorReason,\n  TokenVerificationErrorAction,\n  TokenVerificationError,\n  SignJWTError\n};\n//# sourceMappingURL=chunk-5JS2VYLU.mjs.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parse = parse;\nexports.serialize = serialize;\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\nconst __toString = Object.prototype.toString;\nconst NullObject = /* @__PURE__ */ (() => {\n    const C = function () { };\n    C.prototype = Object.create(null);\n    return C;\n})();\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nfunction parse(str, options) {\n    const obj = new NullObject();\n    const len = str.length;\n    // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n    if (len < 2)\n        return obj;\n    const dec = options?.decode || decode;\n    let index = 0;\n    do {\n        const eqIdx = str.indexOf(\"=\", index);\n        if (eqIdx === -1)\n            break; // No more cookie pairs.\n        const colonIdx = str.indexOf(\";\", index);\n        const endIdx = colonIdx === -1 ? len : colonIdx;\n        if (eqIdx > endIdx) {\n            // backtrack on prior semicolon\n            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n            continue;\n        }\n        const keyStartIdx = startIndex(str, index, eqIdx);\n        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n        const key = str.slice(keyStartIdx, keyEndIdx);\n        // only assign once\n        if (obj[key] === undefined) {\n            let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n            let valEndIdx = endIndex(str, endIdx, valStartIdx);\n            const value = dec(str.slice(valStartIdx, valEndIdx));\n            obj[key] = value;\n        }\n        index = endIdx + 1;\n    } while (index < len);\n    return obj;\n}\nfunction startIndex(str, index, max) {\n    do {\n        const code = str.charCodeAt(index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index;\n    } while (++index < max);\n    return max;\n}\nfunction endIndex(str, index, min) {\n    while (index > min) {\n        const code = str.charCodeAt(--index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index + 1;\n    }\n    return min;\n}\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nfunction serialize(name, val, options) {\n    const enc = options?.encode || encodeURIComponent;\n    if (!cookieNameRegExp.test(name)) {\n        throw new TypeError(`argument name is invalid: ${name}`);\n    }\n    const value = enc(val);\n    if (!cookieValueRegExp.test(value)) {\n        throw new TypeError(`argument val is invalid: ${val}`);\n    }\n    let str = name + \"=\" + value;\n    if (!options)\n        return str;\n    if (options.maxAge !== undefined) {\n        if (!Number.isInteger(options.maxAge)) {\n            throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n        }\n        str += \"; Max-Age=\" + options.maxAge;\n    }\n    if (options.domain) {\n        if (!domainValueRegExp.test(options.domain)) {\n            throw new TypeError(`option domain is invalid: ${options.domain}`);\n        }\n        str += \"; Domain=\" + options.domain;\n    }\n    if (options.path) {\n        if (!pathValueRegExp.test(options.path)) {\n            throw new TypeError(`option path is invalid: ${options.path}`);\n        }\n        str += \"; Path=\" + options.path;\n    }\n    if (options.expires) {\n        if (!isDate(options.expires) ||\n            !Number.isFinite(options.expires.valueOf())) {\n            throw new TypeError(`option expires is invalid: ${options.expires}`);\n        }\n        str += \"; Expires=\" + options.expires.toUTCString();\n    }\n    if (options.httpOnly) {\n        str += \"; HttpOnly\";\n    }\n    if (options.secure) {\n        str += \"; Secure\";\n    }\n    if (options.partitioned) {\n        str += \"; Partitioned\";\n    }\n    if (options.priority) {\n        const priority = typeof options.priority === \"string\"\n            ? options.priority.toLowerCase()\n            : undefined;\n        switch (priority) {\n            case \"low\":\n                str += \"; Priority=Low\";\n                break;\n            case \"medium\":\n                str += \"; Priority=Medium\";\n                break;\n            case \"high\":\n                str += \"; Priority=High\";\n                break;\n            default:\n                throw new TypeError(`option priority is invalid: ${options.priority}`);\n        }\n    }\n    if (options.sameSite) {\n        const sameSite = typeof options.sameSite === \"string\"\n            ? options.sameSite.toLowerCase()\n            : options.sameSite;\n        switch (sameSite) {\n            case true:\n            case \"strict\":\n                str += \"; SameSite=Strict\";\n                break;\n            case \"lax\":\n                str += \"; SameSite=Lax\";\n                break;\n            case \"none\":\n                str += \"; SameSite=None\";\n                break;\n            default:\n                throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n        }\n    }\n    return str;\n}\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str) {\n    if (str.indexOf(\"%\") === -1)\n        return str;\n    try {\n        return decodeURIComponent(str);\n    }\n    catch (e) {\n        return str;\n    }\n}\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val) {\n    return __toString.call(val) === \"[object Date]\";\n}\n//# sourceMappingURL=index.js.map", "import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n", "// src/isomorphicAtob.ts\nvar isomorphicAtob = (data) => {\n  if (typeof atob !== \"undefined\" && typeof atob === \"function\") {\n    return atob(data);\n  } else if (typeof global !== \"undefined\" && global.Buffer) {\n    return new global.Buffer(data, \"base64\").toString();\n  }\n  return data;\n};\n\nexport {\n  isomorphicAtob\n};\n//# sourceMappingURL=chunk-TETGTEI2.mjs.map", "export { webcrypto } from 'node:crypto';\n", "import {\n  isomorphicAtob\n} from \"./chunk-TETGTEI2.mjs\";\nimport \"./chunk-7ELT755Q.mjs\";\nexport {\n  isomorphicAtob\n};\n//# sourceMappingURL=isomorphicAtob.mjs.map", "import {\n  TokenVerificationError,\n  TokenVerificationErrorAction,\n  TokenVerificationErrorReason\n} from \"./chunk-5JS2VYLU.mjs\";\n\n// src/runtime.ts\nimport { webcrypto as crypto } from \"#crypto\";\nvar globalFetch = fetch.bind(globalThis);\nvar runtime = {\n  crypto,\n  get fetch() {\n    return process.env.NODE_ENV === \"test\" ? fetch : globalFetch;\n  },\n  AbortController: globalThis.AbortController,\n  Blob: globalThis.Blob,\n  FormData: globalThis.FormData,\n  Headers: globalThis.Headers,\n  Request: globalThis.Request,\n  Response: globalThis.Response\n};\n\n// src/util/rfc4648.ts\nvar base64url = {\n  parse(string, opts) {\n    return parse(string, base64UrlEncoding, opts);\n  },\n  stringify(data, opts) {\n    return stringify(data, base64UrlEncoding, opts);\n  }\n};\nvar base64UrlEncoding = {\n  chars: \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\",\n  bits: 6\n};\nfunction parse(string, encoding, opts = {}) {\n  if (!encoding.codes) {\n    encoding.codes = {};\n    for (let i = 0; i < encoding.chars.length; ++i) {\n      encoding.codes[encoding.chars[i]] = i;\n    }\n  }\n  if (!opts.loose && string.length * encoding.bits & 7) {\n    throw new SyntaxError(\"Invalid padding\");\n  }\n  let end = string.length;\n  while (string[end - 1] === \"=\") {\n    --end;\n    if (!opts.loose && !((string.length - end) * encoding.bits & 7)) {\n      throw new SyntaxError(\"Invalid padding\");\n    }\n  }\n  const out = new (opts.out ?? Uint8Array)(end * encoding.bits / 8 | 0);\n  let bits = 0;\n  let buffer = 0;\n  let written = 0;\n  for (let i = 0; i < end; ++i) {\n    const value = encoding.codes[string[i]];\n    if (value === void 0) {\n      throw new SyntaxError(\"Invalid character \" + string[i]);\n    }\n    buffer = buffer << encoding.bits | value;\n    bits += encoding.bits;\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 255 & buffer >> bits;\n    }\n  }\n  if (bits >= encoding.bits || 255 & buffer << 8 - bits) {\n    throw new SyntaxError(\"Unexpected end of data\");\n  }\n  return out;\n}\nfunction stringify(data, encoding, opts = {}) {\n  const { pad = true } = opts;\n  const mask = (1 << encoding.bits) - 1;\n  let out = \"\";\n  let bits = 0;\n  let buffer = 0;\n  for (let i = 0; i < data.length; ++i) {\n    buffer = buffer << 8 | 255 & data[i];\n    bits += 8;\n    while (bits > encoding.bits) {\n      bits -= encoding.bits;\n      out += encoding.chars[mask & buffer >> bits];\n    }\n  }\n  if (bits) {\n    out += encoding.chars[mask & buffer << encoding.bits - bits];\n  }\n  if (pad) {\n    while (out.length * encoding.bits & 7) {\n      out += \"=\";\n    }\n  }\n  return out;\n}\n\n// src/jwt/algorithms.ts\nvar algToHash = {\n  RS256: \"SHA-256\",\n  RS384: \"SHA-384\",\n  RS512: \"SHA-512\"\n};\nvar RSA_ALGORITHM_NAME = \"RSASSA-PKCS1-v1_5\";\nvar jwksAlgToCryptoAlg = {\n  RS256: RSA_ALGORITHM_NAME,\n  RS384: RSA_ALGORITHM_NAME,\n  RS512: RSA_ALGORITHM_NAME\n};\nvar algs = Object.keys(algToHash);\nfunction getCryptoAlgorithm(algorithmName) {\n  const hash = algToHash[algorithmName];\n  const name = jwksAlgToCryptoAlg[algorithmName];\n  if (!hash || !name) {\n    throw new Error(`Unsupported algorithm ${algorithmName}, expected one of ${algs.join(\",\")}.`);\n  }\n  return {\n    hash: { name: algToHash[algorithmName] },\n    name: jwksAlgToCryptoAlg[algorithmName]\n  };\n}\n\n// src/jwt/assertions.ts\nvar isArrayString = (s) => {\n  return Array.isArray(s) && s.length > 0 && s.every((a) => typeof a === \"string\");\n};\nvar assertAudienceClaim = (aud, audience) => {\n  const audienceList = [audience].flat().filter((a) => !!a);\n  const audList = [aud].flat().filter((a) => !!a);\n  const shouldVerifyAudience = audienceList.length > 0 && audList.length > 0;\n  if (!shouldVerifyAudience) {\n    return;\n  }\n  if (typeof aud === \"string\") {\n    if (!audienceList.includes(aud)) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.EnsureClerkJWT,\n        reason: TokenVerificationErrorReason.TokenVerificationFailed,\n        message: `Invalid JWT audience claim (aud) ${JSON.stringify(aud)}. Is not included in \"${JSON.stringify(\n          audienceList\n        )}\".`\n      });\n    }\n  } else if (isArrayString(aud)) {\n    if (!aud.some((a) => audienceList.includes(a))) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.EnsureClerkJWT,\n        reason: TokenVerificationErrorReason.TokenVerificationFailed,\n        message: `Invalid JWT audience claim array (aud) ${JSON.stringify(aud)}. Is not included in \"${JSON.stringify(\n          audienceList\n        )}\".`\n      });\n    }\n  }\n};\nvar assertHeaderType = (typ) => {\n  if (typeof typ === \"undefined\") {\n    return;\n  }\n  if (typ !== \"JWT\") {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenInvalid,\n      message: `Invalid JWT type ${JSON.stringify(typ)}. Expected \"JWT\".`\n    });\n  }\n};\nvar assertHeaderAlgorithm = (alg) => {\n  if (!algs.includes(alg)) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenInvalidAlgorithm,\n      message: `Invalid JWT algorithm ${JSON.stringify(alg)}. Supported: ${algs}.`\n    });\n  }\n};\nvar assertSubClaim = (sub) => {\n  if (typeof sub !== \"string\") {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Subject claim (sub) is required and must be a string. Received ${JSON.stringify(sub)}.`\n    });\n  }\n};\nvar assertAuthorizedPartiesClaim = (azp, authorizedParties) => {\n  if (!azp || !authorizedParties || authorizedParties.length === 0) {\n    return;\n  }\n  if (!authorizedParties.includes(azp)) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidAuthorizedParties,\n      message: `Invalid JWT Authorized party claim (azp) ${JSON.stringify(azp)}. Expected \"${authorizedParties}\".`\n    });\n  }\n};\nvar assertExpirationClaim = (exp, clockSkewInMs) => {\n  if (typeof exp !== \"number\") {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT expiry date claim (exp) ${JSON.stringify(exp)}. Expected number.`\n    });\n  }\n  const currentDate = new Date(Date.now());\n  const expiryDate = /* @__PURE__ */ new Date(0);\n  expiryDate.setUTCSeconds(exp);\n  const expired = expiryDate.getTime() <= currentDate.getTime() - clockSkewInMs;\n  if (expired) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenExpired,\n      message: `JWT is expired. Expiry date: ${expiryDate.toUTCString()}, Current date: ${currentDate.toUTCString()}.`\n    });\n  }\n};\nvar assertActivationClaim = (nbf, clockSkewInMs) => {\n  if (typeof nbf === \"undefined\") {\n    return;\n  }\n  if (typeof nbf !== \"number\") {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT not before date claim (nbf) ${JSON.stringify(nbf)}. Expected number.`\n    });\n  }\n  const currentDate = new Date(Date.now());\n  const notBeforeDate = /* @__PURE__ */ new Date(0);\n  notBeforeDate.setUTCSeconds(nbf);\n  const early = notBeforeDate.getTime() > currentDate.getTime() + clockSkewInMs;\n  if (early) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenNotActiveYet,\n      message: `JWT cannot be used prior to not before date claim (nbf). Not before date: ${notBeforeDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`\n    });\n  }\n};\nvar assertIssuedAtClaim = (iat, clockSkewInMs) => {\n  if (typeof iat === \"undefined\") {\n    return;\n  }\n  if (typeof iat !== \"number\") {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT issued at date claim (iat) ${JSON.stringify(iat)}. Expected number.`\n    });\n  }\n  const currentDate = new Date(Date.now());\n  const issuedAtDate = /* @__PURE__ */ new Date(0);\n  issuedAtDate.setUTCSeconds(iat);\n  const postIssued = issuedAtDate.getTime() > currentDate.getTime() + clockSkewInMs;\n  if (postIssued) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenIatInTheFuture,\n      message: `JWT issued at date claim (iat) is in the future. Issued at date: ${issuedAtDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`\n    });\n  }\n};\n\n// src/jwt/cryptoKeys.ts\nimport { isomorphicAtob } from \"@clerk/shared/isomorphicAtob\";\nfunction pemToBuffer(secret) {\n  const trimmed = secret.replace(/-----BEGIN.*?-----/g, \"\").replace(/-----END.*?-----/g, \"\").replace(/\\s/g, \"\");\n  const decoded = isomorphicAtob(trimmed);\n  const buffer = new ArrayBuffer(decoded.length);\n  const bufView = new Uint8Array(buffer);\n  for (let i = 0, strLen = decoded.length; i < strLen; i++) {\n    bufView[i] = decoded.charCodeAt(i);\n  }\n  return bufView;\n}\nfunction importKey(key, algorithm, keyUsage) {\n  if (typeof key === \"object\") {\n    return runtime.crypto.subtle.importKey(\"jwk\", key, algorithm, false, [keyUsage]);\n  }\n  const keyData = pemToBuffer(key);\n  const format = keyUsage === \"sign\" ? \"pkcs8\" : \"spki\";\n  return runtime.crypto.subtle.importKey(format, keyData, algorithm, false, [keyUsage]);\n}\n\n// src/jwt/verifyJwt.ts\nvar DEFAULT_CLOCK_SKEW_IN_SECONDS = 5 * 1e3;\nasync function hasValidSignature(jwt, key) {\n  const { header, signature, raw } = jwt;\n  const encoder = new TextEncoder();\n  const data = encoder.encode([raw.header, raw.payload].join(\".\"));\n  const algorithm = getCryptoAlgorithm(header.alg);\n  try {\n    const cryptoKey = await importKey(key, algorithm, \"verify\");\n    const verified = await runtime.crypto.subtle.verify(algorithm.name, cryptoKey, signature, data);\n    return { data: verified };\n  } catch (error) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          reason: TokenVerificationErrorReason.TokenInvalidSignature,\n          message: error?.message\n        })\n      ]\n    };\n  }\n}\nfunction decodeJwt(token) {\n  const tokenParts = (token || \"\").toString().split(\".\");\n  if (tokenParts.length !== 3) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          reason: TokenVerificationErrorReason.TokenInvalid,\n          message: `Invalid JWT form. A JWT consists of three parts separated by dots.`\n        })\n      ]\n    };\n  }\n  const [rawHeader, rawPayload, rawSignature] = tokenParts;\n  const decoder = new TextDecoder();\n  const header = JSON.parse(decoder.decode(base64url.parse(rawHeader, { loose: true })));\n  const payload = JSON.parse(decoder.decode(base64url.parse(rawPayload, { loose: true })));\n  const signature = base64url.parse(rawSignature, { loose: true });\n  const data = {\n    header,\n    payload,\n    signature,\n    raw: {\n      header: rawHeader,\n      payload: rawPayload,\n      signature: rawSignature,\n      text: token\n    }\n  };\n  return { data };\n}\nasync function verifyJwt(token, options) {\n  const { audience, authorizedParties, clockSkewInMs, key } = options;\n  const clockSkew = clockSkewInMs || DEFAULT_CLOCK_SKEW_IN_SECONDS;\n  const { data: decoded, errors } = decodeJwt(token);\n  if (errors) {\n    return { errors };\n  }\n  const { header, payload } = decoded;\n  try {\n    const { typ, alg } = header;\n    assertHeaderType(typ);\n    assertHeaderAlgorithm(alg);\n    const { azp, sub, aud, iat, exp, nbf } = payload;\n    assertSubClaim(sub);\n    assertAudienceClaim([aud], [audience]);\n    assertAuthorizedPartiesClaim(azp, authorizedParties);\n    assertExpirationClaim(exp, clockSkew);\n    assertActivationClaim(nbf, clockSkew);\n    assertIssuedAtClaim(iat, clockSkew);\n  } catch (err) {\n    return { errors: [err] };\n  }\n  const { data: signatureValid, errors: signatureErrors } = await hasValidSignature(decoded, key);\n  if (signatureErrors) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          action: TokenVerificationErrorAction.EnsureClerkJWT,\n          reason: TokenVerificationErrorReason.TokenVerificationFailed,\n          message: `Error verifying JWT signature. ${signatureErrors[0]}`\n        })\n      ]\n    };\n  }\n  if (!signatureValid) {\n    return {\n      errors: [\n        new TokenVerificationError({\n          reason: TokenVerificationErrorReason.TokenInvalidSignature,\n          message: \"JWT signature is invalid.\"\n        })\n      ]\n    };\n  }\n  return { data: payload };\n}\n\nexport {\n  runtime,\n  base64url,\n  getCryptoAlgorithm,\n  assertHeaderType,\n  assertHeaderAlgorithm,\n  importKey,\n  hasValidSignature,\n  decodeJwt,\n  verifyJwt\n};\n//# sourceMappingURL=chunk-AT3FJU3M.mjs.map", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n", "// src/isomorphicBtoa.ts\nvar isomorphicBtoa = (data) => {\n  if (typeof btoa !== \"undefined\" && typeof btoa === \"function\") {\n    return btoa(data);\n  } else if (typeof global !== \"undefined\" && global.Buffer) {\n    return new global.Buffer(data).toString(\"base64\");\n  }\n  return data;\n};\n\nexport {\n  isomorphicBtoa\n};\n//# sourceMappingURL=chunk-KOH7GTJO.mjs.map", "import {\n  isomorphicAtob\n} from \"./chunk-TETGTEI2.mjs\";\nimport {\n  isomorphicBtoa\n} from \"./chunk-KOH7GTJO.mjs\";\nimport {\n  DEV_OR_STAGING_SUFFIXES,\n  LEGACY_DEV_INSTANCE_SUFFIXES\n} from \"./chunk-I6MTSTOF.mjs\";\n\n// src/keys.ts\nvar PUBLISHABLE_KEY_LIVE_PREFIX = \"pk_live_\";\nvar PUBLISHABLE_KEY_TEST_PREFIX = \"pk_test_\";\nvar PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\nfunction buildPublishableKey(frontendApi) {\n  const isDevKey = PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) || frontendApi.startsWith(\"clerk.\") && LEGACY_DEV_INSTANCE_SUFFIXES.some((s) => frontendApi.endsWith(s));\n  const keyPrefix = isDevKey ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${isomorphicBtoa(`${frontendApi}$`)}`;\n}\nfunction parsePublishableKey(key, options = {}) {\n  key = key || \"\";\n  if (!key || !isPublishableKey(key)) {\n    if (options.fatal && !key) {\n      throw new Error(\n        \"Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys\"\n      );\n    }\n    if (options.fatal && !isPublishableKey(key)) {\n      throw new Error(\"Publishable key not valid.\");\n    }\n    return null;\n  }\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? \"production\" : \"development\";\n  let frontendApi = isomorphicAtob(key.split(\"_\")[2]);\n  frontendApi = frontendApi.slice(0, -1);\n  if (options.proxyUrl) {\n    frontendApi = options.proxyUrl;\n  } else if (instanceType !== \"development\" && options.domain) {\n    frontendApi = `clerk.${options.domain}`;\n  }\n  return {\n    instanceType,\n    frontendApi\n  };\n}\nfunction isPublishableKey(key = \"\") {\n  try {\n    const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n    const hasValidFrontendApiPostfix = isomorphicAtob(key.split(\"_\")[2] || \"\").endsWith(\"$\");\n    return hasValidPrefix && hasValidFrontendApiPostfix;\n  } catch {\n    return false;\n  }\n}\nfunction createDevOrStagingUrlCache() {\n  const devOrStagingUrlCache = /* @__PURE__ */ new Map();\n  return {\n    isDevOrStagingUrl: (url) => {\n      if (!url) {\n        return false;\n      }\n      const hostname = typeof url === \"string\" ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === void 0) {\n        res = DEV_OR_STAGING_SUFFIXES.some((s) => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    }\n  };\n}\nfunction isDevelopmentFromPublishableKey(apiKey) {\n  return apiKey.startsWith(\"test_\") || apiKey.startsWith(\"pk_test_\");\n}\nfunction isProductionFromPublishableKey(apiKey) {\n  return apiKey.startsWith(\"live_\") || apiKey.startsWith(\"pk_live_\");\n}\nfunction isDevelopmentFromSecretKey(apiKey) {\n  return apiKey.startsWith(\"test_\") || apiKey.startsWith(\"sk_test_\");\n}\nfunction isProductionFromSecretKey(apiKey) {\n  return apiKey.startsWith(\"live_\") || apiKey.startsWith(\"sk_live_\");\n}\nasync function getCookieSuffix(publishableKey, subtle = globalThis.crypto.subtle) {\n  const data = new TextEncoder().encode(publishableKey);\n  const digest = await subtle.digest(\"sha-1\", data);\n  const stringDigest = String.fromCharCode(...new Uint8Array(digest));\n  return isomorphicBtoa(stringDigest).replace(/\\+/gi, \"-\").replace(/\\//gi, \"_\").substring(0, 8);\n}\nvar getSuffixedCookieName = (cookieName, cookieSuffix) => {\n  return `${cookieName}_${cookieSuffix}`;\n};\n\nexport {\n  buildPublishableKey,\n  parsePublishableKey,\n  isPublishableKey,\n  createDevOrStagingUrlCache,\n  isDevelopmentFromPublishableKey,\n  isProductionFromPublishableKey,\n  isDevelopmentFromSecretKey,\n  isProductionFromSecretKey,\n  getCookieSuffix,\n  getSuffixedCookieName\n};\n//# sourceMappingURL=chunk-G3VP5PJE.mjs.map"], "names": ["unauthorized", "HTTP_ERROR_FALLBACK_ERROR_CODE", "unstable_rethrow", "require", "forbidden", "error", "isNextRouterError", "isBailoutToCSRError", "isDynamicServerError", "isDynamicPostpone", "isPostpone", "isHangingPromiseRejectionError", "Error", "ReadonlyURLSearchParams", "RedirectType", "notFound", "permanentRedirect", "redirect", "ReadonlyURLSearchParamsError", "constructor", "URLSearchParams", "append", "delete", "set", "sort", "getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "actionAsyncStorage", "undefined", "url", "type", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "REDIRECT_ERROR_CODE", "digest", "getStore", "isAction", "push", "replace", "PermanentRedirect", "isRedirectError", "split", "slice", "join", "Number", "at", "DIGEST"], "sourceRoot": ""}