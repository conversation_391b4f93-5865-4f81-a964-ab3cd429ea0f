try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="cbe29e74-35cd-4c9b-9873-b4bdbabd9658",e._sentryDebugIdIdentifier="sentry-dbid-cbe29e74-35cd-4c9b-9873-b4bdbabd9658")}catch(e){}"use strict";exports.id=2723,exports.ids=[2723],exports.modules={85104:(e,t,s)=>{s.d(t,{clerkDevelopmentCache:()=>l,createKeylessModeMessage:()=>a});var n=s(72016);let a=e=>`
\x1b[35m
[Clerk]:\x1b[0m You are running in keyless mode.
You can \x1b[35mclaim your keys\x1b[0m by visiting ${e.claimUrl}
`,l=function(){if((0,n.b_)())return global.__clerk_internal_keyless_logger||(global.__clerk_internal_keyless_logger={__cache:new Map,log:function({cacheKey:e,msg:t}){var s;this.__cache.has(e)&&Date.now()<((null==(s=this.__cache.get(e))?void 0:s.expiresAt)||0)||(console.log(t),this.__cache.set(e,{expiresAt:Date.now()+6e5}))},run:async function(e,{cacheKey:t,onSuccessStale:s=6e5,onErrorStale:n=6e5}){var a,l;if(this.__cache.has(t)&&Date.now()<((null==(a=this.__cache.get(t))?void 0:a.expiresAt)||0))return null==(l=this.__cache.get(t))?void 0:l.data;try{let n=await e();return this.__cache.set(t,{expiresAt:Date.now()+s,data:n}),n}catch(e){throw this.__cache.set(t,{expiresAt:Date.now()+n}),e}}}),globalThis.__clerk_internal_keyless_logger}()}};
//# sourceMappingURL=2723.js.map