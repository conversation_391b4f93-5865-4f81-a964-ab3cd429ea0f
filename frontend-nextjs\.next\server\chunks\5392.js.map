{"version": 3, "file": "5392.js", "mappings": "shBAKA,SAASA,EAAQ,CACf,GAAGC,EACgD,EACnD,MAAO,UAACC,EAAAA,EAAqB,EAACC,YAAU,UAAW,GAAGF,CAAK,CAAEG,sBAAoB,wBAAwBC,wBAAsB,UAAUC,0BAAwB,eACnK,CACA,SAASC,EAAe,CACtB,GAAGN,EACmD,EACtD,MAAO,UAACC,EAAAA,EAAwB,EAACC,YAAU,kBAAmB,GAAGF,CAAK,CAAEG,sBAAoB,2BAA2BC,wBAAsB,iBAAiBC,0BAAwB,eACxL,CACA,SAASE,EAAe,CACtBC,WAAS,OACTC,EAAQ,QAAQ,YAChBC,EAAa,CAAC,CACd,GAAGV,EACmD,EACtD,MAAO,UAACC,EAAAA,EAAuB,EAACE,sBAAoB,0BAA0BC,wBAAsB,iBAAiBC,0BAAwB,uBACzI,UAACJ,EAAAA,EAAwB,EAACC,YAAU,kBAAkBO,MAAOA,EAAOC,WAAYA,EAAYF,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,ieAAkeH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,2BAA2BE,0BAAwB,iBAEhrB,iGCpBA,IAAMO,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRC,QAAS,CACPC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfL,QAAS,SACX,CACF,GACA,SAASM,EAAM,WACbb,CAAS,CACTO,SAAO,SACPO,GAAU,CAAK,CACf,GAAGtB,EAGJ,EACC,IAAMuB,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAKrB,YAAU,QAAQM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAc,CACzDG,SACF,GAAIP,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,OAAOC,wBAAsB,QAAQC,0BAAwB,aAC9G,iKCrBA,IAAMoB,EAAOC,EAAAA,EAAYA,CAInBC,EAAmBC,EAAAA,aAAmB,CAAwB,CAAC,GAC/DC,EAAY,CAAkH,CAClI,GAAG7B,EACkC,GAC9B,UAAC2B,EAAiBG,QAAQ,EAACC,MAAO,CACvCC,KAAMhC,EAAMgC,IAAI,EACf7B,sBAAoB,4BAA4BC,wBAAsB,YAAYC,0BAAwB,oBACzG,UAAC4B,EAAAA,EAAUA,CAAAA,CAAE,GAAGjC,CAAK,CAAEG,sBAAoB,aAAaE,0BAAwB,eAGhF6B,EAAe,KACnB,IAAMC,EAAeP,EAAAA,UAAgB,CAACD,GAChCS,EAAcR,EAAAA,UAAgB,CAACS,GAC/B,CACJC,eAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,GACZC,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAYA,CAAC,CAC7BT,KAAMG,EAAaH,IAAI,GAEnBU,EAAaJ,EAAcH,EAAaH,IAAI,CAAEQ,GACpD,GAAI,CAACL,EACH,MAAUQ,MAAM,kDAElB,GAAM,IACJC,CAAE,CACH,CAAGR,EACJ,MAAO,IACLQ,EACAZ,KAAMG,EAAaH,IAAI,CACvBa,WAAY,GAAGD,EAAG,UAAU,CAAC,CAC7BE,kBAAmB,GAAGF,EAAG,sBAAsB,CAAC,CAChDG,cAAe,GAAGH,EAAG,kBAAkB,CAAC,CACxC,GAAGF,CAAU,CAEjB,EAIML,EAAkBT,EAAAA,aAAmB,CAAuB,CAAC,GACnE,SAASoB,EAAS,WAChBxC,CAAS,CACT,GAAGR,EACyB,EAC5B,IAAM4C,EAAKhB,EAAAA,KAAW,GACtB,MAAO,UAACS,EAAgBP,QAAQ,EAACC,MAAO,IACtCa,CACF,EAAGzC,sBAAoB,2BAA2BC,wBAAsB,WAAWC,0BAAwB,oBACvG,UAAC4C,MAAAA,CAAI/C,YAAU,YAAYM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,aAAcH,GAAa,GAAGR,CAAK,IAElF,CACA,SAASkD,EAAU,WACjB1C,CAAS,CACT,GAAGR,EAC8C,EACjD,GAAM,OACJmD,CAAK,YACLN,CAAU,CACX,CAAGX,IACJ,MAAO,UAACkB,EAAAA,CAAKA,CAAAA,CAAClD,YAAU,aAAamD,aAAY,CAAC,CAACF,EAAO3C,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCH,GAAY8C,QAAST,EAAa,GAAG7C,CAAK,CAAEG,sBAAoB,QAAQC,wBAAsB,YAAYC,0BAAwB,YAClP,CACA,SAASkD,EAAY,CACnB,GAAGvD,EAC+B,EAClC,GAAM,OACJmD,CAAK,YACLN,CAAU,mBACVC,CAAiB,eACjBC,CAAa,CACd,CAAGb,IACJ,MAAO,UAACV,EAAAA,EAAIA,CAAAA,CAACtB,YAAU,eAAe0C,GAAIC,EAAYW,mBAAkB,EAAkC,GAAGV,EAAkB,CAAC,EAAEC,EAAAA,CAAe,CAAhE,GAAGD,EAAAA,CAAmB,CAA4CW,eAAc,CAAC,CAACN,EAAQ,GAAGnD,CAAK,CAAEG,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,YAC9Q,CACA,SAASqD,EAAgB,WACvBlD,CAAS,CACT,GAAGR,EACuB,EAC1B,GAAM,mBACJ8C,CAAiB,CAClB,CAAGZ,IACJ,MAAO,UAACyB,IAAAA,CAAEzD,YAAU,mBAAmB0C,GAAIE,EAAmBtC,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,YACtM,CACA,SAASuD,EAAY,WACnBpD,CAAS,CACT,GAAGR,EACuB,EAC1B,GAAM,OACJmD,CAAK,eACLJ,CAAa,CACd,CAAGb,IACE2B,EAAOV,EAAQW,OAAOX,GAAOY,SAAW,IAAM/D,EAAMgE,QAAQ,QAClE,EAGO,EAHH,CAGG,CAHI,CAGJ,KAACL,IAAAA,CAAEzD,YAAU,eAAe0C,GAAIG,EAAevC,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2BAA4BH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,oBAC9KwD,IAHI,IAKX,kICzGA,SAASI,EAAK,WACZzD,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAACiD,MAAAA,CAAI/C,YAAU,OAAOM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAAS6D,EAAW,WAClB1D,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAACiD,MAAAA,CAAI/C,YAAU,cAAcM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAAS8D,EAAU,WACjB3D,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAACiD,MAAAA,CAAI/C,YAAU,aAAaM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAAS+D,EAAgB,WACvB5D,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAACiD,MAAAA,CAAI/C,YAAU,mBAAmBM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,YACjL,CACA,SAASgE,EAAW,WAClB7D,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAACiD,MAAAA,CAAI/C,YAAU,cAAcM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iEAAkEH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACxM,CACA,SAASiE,EAAY,WACnB9D,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAACiD,MAAAA,CAAI/C,YAAU,eAAeM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASkE,EAAW,WAClB/D,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAACiD,MAAAA,CAAI/C,YAAU,cAAcM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACjL,gLC1BM,EAAY,OAGZ,CAAC,EAAmB,EAAe,CAAI,OAAkB,CAAC,EAAW,CACzE,CADuC,CACvC,EAA2B,CAC5B,EACK,EAA2B,QAA2B,CAAC,EAWvD,CAAC,EAAc,EAAc,CAAI,EAAoC,GA6BrE,EAAa,IA7BiE,QA6BjE,CACjB,CAAC,EAA+B,KAC9B,GAAM,aACJ,EACA,MAAO,gBACP,EACA,eACA,cAAc,iBACd,iBACA,EAAiB,YACjB,GAAG,EACL,CAAI,EACE,EAAY,QAAY,CAAC,GACzB,CAAC,EAAO,EAAQ,CAAI,OAAoB,CAAC,CAC7C,KAAM,EACN,SAAU,EACV,YAAa,CACf,CAAC,EAED,MACE,UAAC,GACC,MAAO,EACP,OAAQ,OAAK,CAAC,QACd,EACA,cAAe,EACf,cACA,IAAK,iBACL,EAEA,mBAAC,IAAS,CAAC,IAAV,CACC,IAAK,EACL,mBAAkB,EACjB,GAAG,EACJ,IAAK,GACP,EAGN,GAGF,EAAK,YAAc,EAMnB,IAAM,EAAgB,WAOhB,EAAiB,aACrB,CAAC,EAAmC,KAClC,GAAM,aAAE,OAAa,EAAO,GAAM,GAAG,EAAU,CAAI,EAC7C,EAAU,EAAe,CADgB,CACD,GACxC,EAAwB,EAAyB,GACvD,CAFyD,KAGvD,EAFgE,CAEhE,OAAkB,KAAjB,CACC,SAAO,EACN,GAAG,EACJ,YAAa,EAAQ,YACrB,IAAK,EAAQ,SACb,EAEA,mBAAC,IAAS,CAAC,IAAV,CACC,KAAK,UACL,mBAAkB,EAAQ,YACzB,GAAG,EACJ,IAAK,GACP,EAGN,GAGF,EAAS,YAAc,EAMvB,IAAM,EAAe,cAQf,EAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,aAAE,QAAa,WAAO,GAAW,EAAO,GAAG,EAAa,CAAI,EAC5D,EAAU,EAAe,EAAc,EADiB,CAExD,EAAwB,EAAyB,GACjD,CAFkD,CAEtC,EAAc,EAAQ,EAD0B,IAC1B,CAAQ,GAC1C,EAD+C,EACrB,EAAQ,OAAQ,GAC1C,EAD+C,IACxB,EAAQ,MACrC,MACE,UAAkB,KAAjB,CACC,SAAO,EACN,GAAG,EACJ,UAAW,CAAC,EACZ,OAAQ,EAER,mBAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACL,KAAK,MACL,gBAAe,EACf,gBAAe,EACf,aAAY,EAAa,SAAW,WACpC,gBAAe,EAAW,GAAK,gBAC/B,EACA,GAAI,EACH,GAAG,EACJ,IAAK,EACL,YAAa,OAAoB,CAAC,EAAM,YAAc,IAG/C,GAA6B,IAAjB,EAAM,QAAgB,OAAM,QAI3C,EAAM,eAAe,EAHrB,EAAQ,cAAc,EAK1B,CAAC,EAL8B,UAMpB,OAAoB,CAAC,EAAM,UAAW,IAC3C,CAAC,IAAK,OAAO,EAAE,SAAS,EAAM,GAAG,EAAG,GAAQ,cAAc,EAChE,CAAC,EACD,QAAS,OAAoB,CAAC,EAAM,QAAS,KAG3C,IAAM,EAAmD,WAA3B,EAAQ,cAClC,CAAC,GAAe,IAAY,GAC9B,EAAQ,EADS,WACT,CAAc,EAE1B,CAAC,CAHwD,CAC1B,CAGjC,EAGN,GAGF,EAAY,YAAc,EAM1B,IAAM,EAAe,cAaf,EAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,CAAE,oBAAa,aAAO,WAAY,EAAU,GAAG,EAAa,CAAI,EAChE,EAAU,EAAe,EAAc,EADqB,CAE5D,EAAY,EAAc,EAAQ,EADgB,IAChB,CAAQ,GAC1C,EAD+C,EACrB,EAAQ,OAAQ,GAC1C,EAAa,IAAU,EAAQ,MAC/B,EAAqC,SAAO,GAOlD,OAP4D,EAEtD,UAAU,KACd,IAAM,EAAM,sBAAsB,IAAO,EAA6B,QAAU,IAChF,CADsF,KAC/E,IAAM,qBAAqB,EACpC,CADuC,CACpC,CAAC,CAAC,EAGH,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAC9B,UAAC,SAAE,EAAQ,GACV,UAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAa,SAAW,WACpC,mBAAkB,EAAQ,YAC1B,KAAK,WACL,kBAAiB,EACjB,OAAQ,CAAC,EACT,GAAI,EACJ,SAAU,EACT,GAAG,EACJ,IAAK,EACL,MAAO,CACL,GAAG,EAAM,MACT,kBAAmB,EAA6B,QAAU,KAAO,MACnE,EAEC,YAAW,GACd,CAEJ,CAEJ,GAOF,SAAS,EAAc,EAAgB,GACrC,MAAO,GAAG,EAAM,WAAY,EAAK,EAGnC,CAHmC,QAG1B,EAAc,EAAgB,GAAe,MAC7C,GAAG,EAAM,WAAY,EAAK,EATnC,CASmC,CATvB,YAAc,iBCrQ1B,SAASmE,EAAK,CACZhE,MADWgE,KACF,CACT,GAAGxE,EAC6C,EAChD,MAAO,UAACyE,EAAkB,CAACvE,EAAD,UAAW,OAAOM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,sBAAuBH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,qBAAqBC,wBAAsB,OAAOC,0BAAwB,YACxM,CACA,SAASqE,EAAS,WAADA,CACN,CACT,GAAG1E,EAC6C,EAChD,MAAO,UDwQI,ECxQe,CAACE,CAAD,WAAW,YAAYM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,sGAAuGH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,qBAAqBC,wBAAsB,WAAWC,0BAAwB,YACjS,CACA,SAASsE,EAAY,WACnBnE,CAAS,CACT,CAFkBmE,EAEf3E,EACgD,EACnD,MAAO,UDmQO,ECnQe,CAACE,IAAD,QAAW,eAAeM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kqBAAmqBH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,YACz2B,CACA,SAASuE,EAAY,WACnBpE,CAAS,CACT,CAFkBoE,EAEf5E,EACgD,EACnD,MAAO,UD8PO,EC9Pe,CAACE,IAAD,QAAW,eAAeM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,sBAAuBH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,YAC7N", "sources": ["webpack://next-shadcn-dashboard-starter/./src/components/ui/popover.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/badge.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/form.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/card.tsx", "webpack://next-shadcn-dashboard-starter/../src/tabs.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/tabs.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\nimport { cn } from '@/lib/utils';\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot='popover' {...props} data-sentry-element=\"PopoverPrimitive.Root\" data-sentry-component=\"Popover\" data-sentry-source-file=\"popover.tsx\" />;\n}\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot='popover-trigger' {...props} data-sentry-element=\"PopoverPrimitive.Trigger\" data-sentry-component=\"PopoverTrigger\" data-sentry-source-file=\"popover.tsx\" />;\n}\nfunction PopoverContent({\n  className,\n  align = 'center',\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return <PopoverPrimitive.Portal data-sentry-element=\"PopoverPrimitive.Portal\" data-sentry-component=\"PopoverContent\" data-sentry-source-file=\"popover.tsx\">\r\n      <PopoverPrimitive.Content data-slot='popover-content' align={align} sideOffset={sideOffset} className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden', className)} {...props} data-sentry-element=\"PopoverPrimitive.Content\" data-sentry-source-file=\"popover.tsx\" />\r\n    </PopoverPrimitive.Portal>;\n}\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot='popover-anchor' {...props} data-sentry-element=\"PopoverPrimitive.Anchor\" data-sentry-component=\"PopoverAnchor\" data-sentry-source-file=\"popover.tsx\" />;\n}\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport { Controller, FormProvider, useFormContext, useFormState, type ControllerProps, type FieldPath, type FieldValues } from 'react-hook-form';\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\nconst Form = FormProvider;\ntype FormFieldContextValue<TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>> = {\n  name: TName;\n};\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\nconst FormField = <TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return <FormFieldContext.Provider value={{\n    name: props.name\n  }} data-sentry-element=\"FormFieldContext.Provider\" data-sentry-component=\"FormField\" data-sentry-source-file=\"form.tsx\">\r\n      <Controller {...props} data-sentry-element=\"Controller\" data-sentry-source-file=\"form.tsx\" />\r\n    </FormFieldContext.Provider>;\n};\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const {\n    getFieldState\n  } = useFormContext();\n  const formState = useFormState({\n    name: fieldContext.name\n  });\n  const fieldState = getFieldState(fieldContext.name, formState);\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n  const {\n    id\n  } = itemContext;\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\ntype FormItemContextValue = {\n  id: string;\n};\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\nfunction FormItem({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  const id = React.useId();\n  return <FormItemContext.Provider value={{\n    id\n  }} data-sentry-element=\"FormItemContext.Provider\" data-sentry-component=\"FormItem\" data-sentry-source-file=\"form.tsx\">\r\n      <div data-slot='form-item' className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>;\n}\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const {\n    error,\n    formItemId\n  } = useFormField();\n  return <Label data-slot='form-label' data-error={!!error} className={cn('data-[error=true]:text-destructive', className)} htmlFor={formItemId} {...props} data-sentry-element=\"Label\" data-sentry-component=\"FormLabel\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormControl({\n  ...props\n}: React.ComponentProps<typeof Slot>) {\n  const {\n    error,\n    formItemId,\n    formDescriptionId,\n    formMessageId\n  } = useFormField();\n  return <Slot data-slot='form-control' id={formItemId} aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`} aria-invalid={!!error} {...props} data-sentry-element=\"Slot\" data-sentry-component=\"FormControl\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormDescription({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    formDescriptionId\n  } = useFormField();\n  return <p data-slot='form-description' id={formDescriptionId} className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"FormDescription\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormMessage({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    error,\n    formMessageId\n  } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n  if (!body) {\n    return null;\n  }\n  return <p data-slot='form-message' id={formMessageId} className={cn('text-destructive text-sm', className)} {...props} data-sentry-component=\"FormMessage\" data-sentry-source-file=\"form.tsx\">\r\n      {body}\r\n    </p>;\n}\nexport { useFormField, Form, FormItem, FormLabel, FormControl, FormDescription, FormMessage, FormField };", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value?: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ElementRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n", "'use client';\n\nimport * as React from 'react';\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\nimport { cn } from '@/lib/utils';\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return <TabsPrimitive.Root data-slot='tabs' className={cn('flex flex-col gap-2', className)} {...props} data-sentry-element=\"TabsPrimitive.Root\" data-sentry-component=\"Tabs\" data-sentry-source-file=\"tabs.tsx\" />;\n}\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return <TabsPrimitive.List data-slot='tabs-list' className={cn('bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]', className)} {...props} data-sentry-element=\"TabsPrimitive.List\" data-sentry-component=\"TabsList\" data-sentry-source-file=\"tabs.tsx\" />;\n}\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return <TabsPrimitive.Trigger data-slot='tabs-trigger' className={cn(\"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} {...props} data-sentry-element=\"TabsPrimitive.Trigger\" data-sentry-component=\"TabsTrigger\" data-sentry-source-file=\"tabs.tsx\" />;\n}\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return <TabsPrimitive.Content data-slot='tabs-content' className={cn('flex-1 outline-none', className)} {...props} data-sentry-element=\"TabsPrimitive.Content\" data-sentry-component=\"TabsContent\" data-sentry-source-file=\"tabs.tsx\" />;\n}\nexport { Tabs, TabsList, TabsTrigger, TabsContent };"], "names": ["Popover", "props", "PopoverPrimitive", "data-slot", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "PopoverTrigger", "PopoverC<PERSON>nt", "className", "align", "sideOffset", "cn", "badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot", "Form", "FormProvider", "FormFieldContext", "React", "FormField", "Provider", "value", "name", "Controller", "useFormField", "fieldContext", "itemContext", "FormItemContext", "getFieldState", "useFormContext", "formState", "useFormState", "fieldState", "Error", "id", "formItemId", "formDescriptionId", "formMessageId", "FormItem", "div", "FormLabel", "error", "Label", "data-error", "htmlFor", "FormControl", "aria-<PERSON><PERSON>", "aria-invalid", "FormDescription", "p", "FormMessage", "body", "String", "message", "children", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "CardAction", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "Tabs", "TabsPrimitive", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}