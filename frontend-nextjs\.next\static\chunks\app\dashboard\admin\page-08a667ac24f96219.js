try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c1857195-79da-4a02-bbcd-0429423f893a",e._sentryDebugIdIdentifier="sentry-dbid-c1857195-79da-4a02-bbcd-0429423f893a")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1833],{984:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useRouter",{enumerable:!0,get:function(){return n}});let s=a(99004),r=a(64971);function n(){return(0,s.useContext)(r.RouterContext)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2757:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var s=a(52880);a(99004);var r=a(90917);function n(e){let{children:t,scrollable:a=!0}=e;return(0,s.jsx)(s.Fragment,{children:a?(0,s.jsx)(r.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:t})}):(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:t})})}},12138:(e,t,a)=>{Promise.resolve().then(a.bind(a,66168))},18580:(e,t,a)=>{"use strict";a.d(t,{o:()=>r});class s{async makeRequest(e){let t,a,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:r="GET",body:n,params:i}=s;if(t="/api".concat(e),i){let e=new URLSearchParams;Object.entries(i).forEach(t=>{let[a,s]=t;null!=s&&e.append(a,s.toString())}),e.toString()&&(t+="?".concat(e.toString()))}a={method:r,headers:{"Content-Type":"application/json"}},n&&"GET"!==r&&(a.body=JSON.stringify(n));try{let e=await fetch(t,a);if(!e.ok){let t=await e.text();throw Error("API request failed: ".concat(e.status," ").concat(e.statusText," - ").concat(t))}return await e.json()}catch(t){throw console.error("API request failed for ".concat(e,":"),t),t}}async getAppointments(e){return this.makeRequest("/appointments",{params:{depth:"2",...e}})}async getAppointment(e){return this.makeRequest("/appointments/".concat(e),{params:{depth:"2"}})}async createAppointment(e){return this.makeRequest("/appointments",{method:"POST",body:e})}async updateAppointment(e,t){return this.makeRequest("/appointments/".concat(e),{method:"PATCH",body:t})}async deleteAppointment(e){return this.makeRequest("/appointments/".concat(e),{method:"DELETE"})}async getPatients(e){let t={depth:"1",...e};return(null==e?void 0:e.search)&&(t["where[or][0][fullName][contains]"]=e.search,t["where[or][1][phone][contains]"]=e.search,t["where[or][2][email][contains]"]=e.search,delete t.search),this.makeRequest("/patients",{params:t})}async getPatient(e){return this.makeRequest("/patients/".concat(e),{params:{depth:"1"}})}async createPatient(e){return this.makeRequest("/patients",{method:"POST",body:e})}async updatePatient(e,t){return this.makeRequest("/patients/".concat(e),{method:"PATCH",body:t})}async deletePatient(e){return this.makeRequest("/patients/".concat(e),{method:"DELETE"})}async getTreatments(e){return this.makeRequest("/treatments",{params:{depth:"1",...e}})}async getTreatment(e){return this.makeRequest("/treatments/".concat(e))}async createTreatment(e){return this.makeRequest("/treatments",{method:"POST",body:e})}async updateTreatment(e,t){return this.makeRequest("/treatments/".concat(e),{method:"PATCH",body:t})}async deleteTreatment(e){return this.makeRequest("/treatments/".concat(e),{method:"DELETE"})}async getUsers(e){return this.makeRequest("/users",{params:{depth:"1",...e}})}async updateUser(e,t){return this.makeRequest("/users/".concat(e),{method:"PATCH",body:t})}async syncCurrentUser(){return this.makeRequest("/users/sync",{method:"POST",body:{clerkId:this.user.clerkId,email:this.user.email,firstName:this.user.firstName,lastName:this.user.lastName}})}async syncUser(e){try{let t=await this.makeRequest("/users",{params:{where:JSON.stringify({clerkId:{equals:e.clerkId}}),limit:1}});if(!t.docs||!(t.docs.length>0))return await this.makeRequest("/users",{method:"POST",body:{email:e.email,clerkId:e.clerkId,firstName:e.firstName,lastName:e.lastName,role:"front-desk",lastLogin:new Date().toISOString()}});{let a=t.docs[0];return await this.makeRequest("/users/".concat(a.id),{method:"PATCH",body:{email:e.email,firstName:e.firstName,lastName:e.lastName,lastLogin:new Date().toISOString()}})}}catch(t){return console.error("Error syncing user with Payload:",t),{id:"temp-id",email:e.email,clerkId:e.clerkId,role:"front-desk",firstName:e.firstName,lastName:e.lastName}}}async getPatientInteractions(e){return this.makeRequest("/patient-interactions",{params:{depth:"2",...e}})}async getPatientInteraction(e){return this.makeRequest("/patient-interactions/".concat(e),{params:{depth:"2"}})}async createPatientInteraction(e){return this.makeRequest("/patient-interactions",{method:"POST",body:e})}async updatePatientInteraction(e,t){return this.makeRequest("/patient-interactions/".concat(e),{method:"PATCH",body:t})}async deletePatientInteraction(e){return this.makeRequest("/patient-interactions/".concat(e),{method:"DELETE"})}async getPatientTasks(e){return this.makeRequest("/patient-tasks",{params:{depth:"2",...e}})}async getPatientTask(e){return this.makeRequest("/patient-tasks/".concat(e),{params:{depth:"2"}})}async createPatientTask(e){return this.makeRequest("/patient-tasks",{method:"POST",body:e})}async updatePatientTask(e,t){return this.makeRequest("/patient-tasks/".concat(e),{method:"PATCH",body:t})}async deletePatientTask(e){return this.makeRequest("/patient-tasks/".concat(e),{method:"DELETE"})}async getPatientInteractionsByPatient(e,t){return this.makeRequest("/patients/".concat(e,"/interactions"),{params:{depth:"2",...t}})}async getPatientTasksByPatient(e,t){return this.makeRequest("/patients/".concat(e,"/tasks"),{params:{depth:"2",...t}})}async getPatientTimeline(e,t){return this.makeRequest("/patients/".concat(e,"/timeline"),{params:{depth:"2",...t}})}constructor(e){this.user=e}}function r(e){return new s(e)}},26230:(e,t,a)=>{"use strict";a.d(t,{t:()=>r});let s={nav:{dashboard:"仪表板",appointments:"预约管理",patients:"患者管理",treatments:"治疗项目",admin:"系统管理",account:"账户",profile:"个人资料",login:"登录",overview:"概览"},dashboard:{title:"诊所控制台 \uD83C\uDFE5",subtitle:"欢迎使用您的诊所管理系统",metrics:{todayAppointments:"今日预约",recentPatients:"近期患者",totalPatients:"患者总数",activetreatments:"可用治疗",scheduledForToday:"今日安排",appointmentsScheduledForToday:"今日安排的预约",newPatientsThisWeek:"本周新患者",patientsRegisteredInLast7Days:"过去7天注册的患者",totalRegisteredPatients:"注册患者总数",completePatientDatabase:"完整患者数据库",treatmentOptionsAvailable:"可用治疗选项",fullServiceCatalog:"完整服务目录",active:"活跃",last7Days:"过去7天",allTime:"全部时间",available:"可用"},errors:{loadingDashboard:"加载仪表板时出错",failedToLoadMetrics:"无法加载仪表板数据"}},appointments:{title:"预约管理",subtitle:"管理患者预约和排程",newAppointment:"新建预约",editAppointment:"编辑预约",appointmentDetails:"预约详情",appointmentsCount:"个预约",loadingAppointments:"加载预约中...",noAppointments:"暂无预约",filters:{all:"全部",today:"今天",thisWeek:"本周",thisMonth:"本月",status:"状态",dateRange:"日期范围"},status:{scheduled:"已安排",confirmed:"已确认",inProgress:"进行中",completed:"已完成",cancelled:"已取消",noShow:"未到场"},form:{patient:"患者",selectPatient:"选择患者",treatment:"治疗项目",selectTreatment:"选择治疗项目",date:"日期",time:"时间",notes:"备注",notesPlaceholder:"预约备注（可选）",status:"状态"}},patients:{title:"患者管理",subtitle:"管理患者信息和病历",newPatient:"新建患者",editPatient:"编辑患者",patientDetails:"患者详情",patientsCount:"位患者",loadingPatients:"加载患者中...",noPatients:"暂无患者",searchPlaceholder:"按姓名、电话或邮箱搜索患者",form:{fullName:"姓名",fullNamePlaceholder:"请输入患者姓名",phone:"电话",phonePlaceholder:"请输入电话号码",email:"邮箱",emailPlaceholder:"请输入邮箱地址（可选）",medicalNotes:"病历备注",medicalNotesPlaceholder:"请输入病历备注（可选）"}},treatments:{title:"治疗项目",subtitle:"管理诊所治疗服务",newTreatment:"新建治疗",editTreatment:"编辑治疗",treatmentDetails:"治疗详情",treatmentsCount:"个治疗项目",loadingTreatments:"加载治疗项目中...",noTreatments:"暂无治疗项目",form:{name:"治疗名称",namePlaceholder:"请输入治疗名称",description:"治疗描述",descriptionPlaceholder:"请输入治疗描述",duration:"治疗时长",durationPlaceholder:"请输入治疗时长（分钟）",price:"价格",pricePlaceholder:"请输入价格"}},admin:{title:"系统管理",subtitle:"管理用户权限和系统设置",userManagement:"用户管理",roleManagement:"角色管理",systemSettings:"系统设置",users:"用户",roles:{admin:"管理员",doctor:"医生",frontDesk:"前台"}},common:{actions:{save:"保存",cancel:"取消",edit:"编辑",delete:"删除",view:"查看",search:"搜索",filter:"筛选",reset:"重置",submit:"提交",close:"关闭",confirm:"确认",back:"返回",next:"下一步",previous:"上一步",add:"添加",remove:"移除",update:"更新",create:"创建"},status:{loading:"加载中...",success:"成功",error:"错误",warning:"警告",info:"信息",pending:"待处理",active:"活跃",inactive:"非活跃",enabled:"已启用",disabled:"已禁用"},time:{today:"今天",yesterday:"昨天",tomorrow:"明天",thisWeek:"本周",lastWeek:"上周",nextWeek:"下周",thisMonth:"本月",lastMonth:"上月",nextMonth:"下月",thisYear:"今年",lastYear:"去年",nextYear:"明年"},confirmDialog:{title:"确认操作",deleteTitle:"确认删除",deleteMessage:"您确定要删除这个项目吗？此操作无法撤销。",cancelTitle:"确认取消",cancelMessage:"您确定要取消吗？未保存的更改将丢失。",saveTitle:"确认保存",saveMessage:"您确定要保存这些更改吗？"}},validation:{required:"此字段为必填项",email:"请输入有效的邮箱地址",phone:"请输入有效的电话号码",minLength:"至少需要 {min} 个字符",maxLength:"最多允许 {max} 个字符",number:"请输入有效的数字",positive:"请输入正数",date:"请选择有效的日期",time:"请选择有效的时间"},errors:{general:"发生了未知错误，请稍后重试",network:"网络连接错误，请检查您的网络连接",unauthorized:"您没有权限执行此操作",notFound:"请求的资源未找到",serverError:"服务器错误，请稍后重试",validationError:"输入数据验证失败",loadFailed:"加载数据失败",saveFailed:"保存数据失败",deleteFailed:"删除数据失败",updateFailed:"更新数据失败",createFailed:"创建数据失败"},success:{saved:"保存成功",deleted:"删除成功",updated:"更新成功",created:"创建成功",sent:"发送成功",uploaded:"上传成功",downloaded:"下载成功"}};function r(e,t){let a=e.split("."),r=s;for(let t of a)if(!r||"object"!=typeof r||!(t in r))return console.warn("Translation key not found: ".concat(e)),e;else r=r[t];return"string"!=typeof r?(console.warn("Translation value is not a string: ".concat(e)),e):t?r.replace(/\{(\w+)\}/g,(e,a)=>{var s;return(null==(s=t[a])?void 0:s.toString())||e}):r}},45450:(e,t,a)=>{"use strict";a.d(t,{TR:()=>h,bq:()=>m,eb:()=>f,gC:()=>p,l6:()=>c,mi:()=>x,s3:()=>d,yv:()=>u});var s=a(52880);a(99004);var r=a(60795),n=a(5908),i=a(90502),l=a(29205),o=a(54651);function c(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function d(e){let{...t}=e;return(0,s.jsx)(r.YJ,{"data-slot":"select-group",...t,"data-sentry-element":"SelectPrimitive.Group","data-sentry-component":"SelectGroup","data-sentry-source-file":"select.tsx"})}function u(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function m(e){let{className:t,size:a="default",children:i,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[i,(0,s.jsx)(r.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(n.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function p(e){let{className:t,children:a,position:n="popper",...i}=e;return(0,s.jsx)(r.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,s.jsx)(y,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:a}),(0,s.jsx)(g,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.JU,{"data-slot":"select-label",className:(0,o.cn)("text-muted-foreground px-2 py-1.5 text-xs",t),...a,"data-sentry-element":"SelectPrimitive.Label","data-sentry-component":"SelectLabel","data-sentry-source-file":"select.tsx"})}function f(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(i.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,s.jsx)(r.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:a})]})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.wv,{"data-slot":"select-separator",className:(0,o.cn)("bg-border pointer-events-none -mx-1 my-1 h-px",t),...a,"data-sentry-element":"SelectPrimitive.Separator","data-sentry-component":"SelectSeparator","data-sentry-source-file":"select.tsx"})}function y(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(l.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(n.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},47959:(e,t,a)=>{"use strict";a.d(t,{T5:()=>r.T5,hP:()=>s.hP,kX:()=>r.kX,nO:()=>r.nO,wV:()=>s.wV,yC:()=>n});var s=a(33116),r=a(34921);function n(e,t,a){let r=t.path||(null==a?void 0:a.path);return"path"===(t.routing||(null==a?void 0:a.routing)||"path")?r?{...a,...t,routing:"path"}:s.sb.throw((0,s.kd)(e)):t.path?s.sb.throw((0,s.s7)(e)):{...a,...t,path:void 0}}},66168:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var s=a(52880),r=a(99004),n=a(83405),i=a(29980),l=a(95181),o=a(2757),c=a(62054),d=a(88151),u=a(86540),m=a(45450),p=a(49202),h=(0,p.A)("outline","shield","IconShield",[["path",{d:"M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3",key:"svg-0"}]]),f=a(68290),x=(0,p.A)("outline","user-check","IconUserCheck",[["path",{d:"M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0",key:"svg-0"}],["path",{d:"M6 21v-2a4 4 0 0 1 4 -4h4",key:"svg-1"}],["path",{d:"M15 19l2 2l4 -4",key:"svg-2"}]]),y=a(18580),g=a(39426),v=a(4629),b=a(26230);function j(){let{userId:e,isLoaded:t}=(0,n.d)(),{user:a,refreshUser:p}=(0,i.It)(),[j,P]=(0,r.useState)([]),[k,N]=(0,r.useState)(!0),[w,S]=(0,r.useState)(null),[T,A]=(0,r.useState)(null);t&&!e&&(0,l.redirect)("/auth/sign-in");let R=async()=>{if(a)try{N(!0);let e=(0,y.o)(a),t=await e.getUsers({limit:100});P(t.docs||[]),S(null)}catch(e){console.error("Failed to fetch users:",e),S("Failed to load users. Please try again later.")}finally{N(!1)}};(0,r.useEffect)(()=>{a&&"admin"===a.role&&R()},[a]);let C=async(e,t)=>{if(a)try{A(e);let s=(0,y.o)(a);await s.updateUser(e,{role:t}),P(j.map(a=>a.id===e?{...a,role:t}:a)),v.toast.success("User role updated successfully"),a.payloadUserId===e&&await p()}catch(e){console.error("Failed to update user role:",e),v.toast.error("Failed to update user role")}finally{A(null)}};return!t||k?(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"加载管理面板中..."})]})})}):(0,s.jsx)(i.Y0,{roles:"admin",fallback:(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(h,{className:"size-12 mx-auto text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"访问被拒绝"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"您需要管理员权限才能访问此页面。"})]})})}),"data-sentry-element":"RoleGate","data-sentry-component":"AdminPage","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(o.A,{"data-sentry-element":"PageContainer","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"flex flex-1 flex-col space-y-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"size-6","data-sentry-element":"IconUsers","data-sentry-source-file":"page.tsx"}),(0,b.t)("admin.title")]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,b.t)("admin.subtitle")})]})}),(0,s.jsxs)(u.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(u.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(u.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(x,{className:"size-5","data-sentry-element":"IconUserCheck","data-sentry-source-file":"page.tsx"}),"当前用户"]}),(0,s.jsx)(u.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"您当前的角色和权限"})]}),(0,s.jsx)(u.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:null==a?void 0:a.email}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[null==a?void 0:a.firstName," ",null==a?void 0:a.lastName]})]}),(0,s.jsx)(d.E,{className:(0,g.Pj)((null==a?void 0:a.role)||"front-desk"),"data-sentry-element":"Badge","data-sentry-source-file":"page.tsx",children:(0,g.cb)((null==a?void 0:a.role)||"front-desk")})]})})]}),w?(0,s.jsx)(u.Zp,{children:(0,s.jsx)(u.Wu,{className:"pt-6",children:(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:w}),(0,s.jsx)(c.$,{onClick:R,children:"Try Again"})]})})}):(0,s.jsxs)(u.Zp,{children:[(0,s.jsxs)(u.aR,{children:[(0,s.jsxs)(u.ZB,{children:["All Users (",j.length,")"]}),(0,s.jsx)(u.BT,{children:"Manage roles for all system users"})]}),(0,s.jsx)(u.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[j.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.email}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.firstName," ",e.lastName]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Joined: ",new Date(e.createdAt).toLocaleDateString()]})]})})}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(d.E,{className:(0,g.Pj)(e.role),children:(0,g.cb)(e.role)}),(0,s.jsxs)(m.l6,{value:e.role,onValueChange:t=>C(e.id,t),disabled:T===e.id,children:[(0,s.jsx)(m.bq,{className:"w-32",children:(0,s.jsx)(m.yv,{})}),(0,s.jsxs)(m.gC,{children:[(0,s.jsx)(m.eb,{value:"admin",children:"Administrator"}),(0,s.jsx)(m.eb,{value:"doctor",children:"Doctor"}),(0,s.jsx)(m.eb,{value:"front-desk",children:"Front Desk"})]})]})]})]},e.id)),0===j.length&&(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(f.A,{className:"size-12 mx-auto text-muted-foreground mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"No users found"})]})]})})]})]})})})}},67637:(e,t,a)=>{e.exports=a(984)},68290:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var s=(0,a(49202).A)("outline","users","IconUsers",[["path",{d:"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"svg-2"}],["path",{d:"M21 21v-2a4 4 0 0 0 -3 -3.85",key:"svg-3"}]])},83405:(e,t,a)=>{"use strict";a.d(t,{PromisifiedAuthProvider:()=>o,d:()=>c});var s=a(87905),r=a(47959),n=a(67637),i=a(99004);let l=i.createContext(null);function o(e){let{authPromise:t,children:a}=e;return i.createElement(l.Provider,{value:t},a)}function c(){let e=(0,n.useRouter)(),t=i.useContext(l),a=t;return(t&&"then"in t&&(a=i.use(t)),"undefined"!=typeof window)?(0,s.As)(a):e?(0,s.As)():(0,r.hP)(a)}},86540:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>d,X9:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>u});var s=a(52880);a(99004);var r=a(54651);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,r.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...a,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function u(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},88151:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var s=a(52880);a(99004);var r=a(50516),n=a(85017),i=a(54651);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:n=!1,...o}=e,c=n?r.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(l({variant:a}),t),...o,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},90917:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,ScrollArea:()=>i});var s=a(52880);a(99004);var r=a(71359),n=a(54651);function i(e){let{className:t,children:a,...i}=e;return(0,s.jsxs)(r.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",t),...i,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,s.jsx)(r.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:a}),(0,s.jsx)(l,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,s.jsx)(r.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function l(e){let{className:t,orientation:a="vertical",...i}=e;return(0,s.jsx)(r.VM,{"data-slot":"scroll-area-scrollbar",orientation:a,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===a&&"h-full w-2.5 border-l border-l-transparent","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent",t),...i,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,s.jsx)(r.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},95181:(e,t,a)=>{"use strict";var s=a(4377);a.o(s,"redirect")&&a.d(t,{redirect:function(){return s.redirect}}),a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}}),a.o(s,"useSelectedLayoutSegments")&&a.d(t,{useSelectedLayoutSegments:function(){return s.useSelectedLayoutSegments}})}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,7905,1359,4089,4629,7131,3530,229,9442,4579,9253,7358],()=>t(12138)),_N_E=e.O()}]);