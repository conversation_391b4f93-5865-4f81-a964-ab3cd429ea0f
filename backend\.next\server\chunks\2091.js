"use strict";exports.id=2091,exports.ids=[2091],exports.modules={72091:(e,t,s)=>{s.d(t,{GetRoleCredentialsCommand:()=>F,SSOClient:()=>ej});var n=s(38353),o=s(17972),r=s(72734);let i=e=>Object.assign(e,{useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"}),a={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};class d extends r.ServiceException{constructor(e){super(e),Object.setPrototypeOf(this,d.prototype)}}class c extends d{name="InvalidRequestException";$fault="client";constructor(e){super({name:"InvalidRequestException",$fault:"client",...e}),Object.setPrototypeOf(this,c.prototype)}}class u extends d{name="ResourceNotFoundException";$fault="client";constructor(e){super({name:"ResourceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,u.prototype)}}class l extends d{name="TooManyRequestsException";$fault="client";constructor(e){super({name:"TooManyRequestsException",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}class p extends d{name="UnauthorizedException";$fault="client";constructor(e){super({name:"UnauthorizedException",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype)}}let h=e=>({...e,...e.accessToken&&{accessToken:r.SENSITIVE_STRING}}),g=e=>({...e,...e.secretAccessKey&&{secretAccessKey:r.SENSITIVE_STRING},...e.sessionToken&&{sessionToken:r.SENSITIVE_STRING}}),m=e=>({...e,...e.roleCredentials&&{roleCredentials:g(e.roleCredentials)}});var f=s(90841),S=s(35166);let y=async(e,t)=>{let s,n=(0,S.requestBuilder)(e,t),o=(0,r.map)({},r.isSerializableHeaderValue,{[D]:e[R]});n.bp("/federation/credentials");let i=(0,r.map)({[A]:[,(0,r.expectNonNull)(e[k],"roleName")],[N]:[,(0,r.expectNonNull)(e[O],"accountId")]});return n.m("GET").h(o).q(i).b(s),n.build()},E=async(e,t)=>{if(200!==e.statusCode&&e.statusCode>=300)return P(e,t);let s=(0,r.map)({$metadata:w(e)}),n=(0,r.expectNonNull)((0,r.expectObject)(await (0,f.Y2)(e.body,t)),"body");return Object.assign(s,(0,r.take)(n,{roleCredentials:r._json})),s},P=async(e,t)=>{let s={...e,body:await (0,f.CG)(e.body,t)},n=(0,f.cJ)(e,s.body);switch(n){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await x(s,t);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await v(s,t);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await b(s,t);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await C(s,t);default:return I({output:e,parsedBody:s.body,errorCode:n})}},I=(0,r.withBaseException)(d),x=async(e,t)=>{let s=(0,r.map)({}),n=e.body;Object.assign(s,(0,r.take)(n,{message:r.expectString}));let o=new c({$metadata:w(e),...s});return(0,r.decorateServiceException)(o,e.body)},v=async(e,t)=>{let s=(0,r.map)({}),n=e.body;Object.assign(s,(0,r.take)(n,{message:r.expectString}));let o=new u({$metadata:w(e),...s});return(0,r.decorateServiceException)(o,e.body)},b=async(e,t)=>{let s=(0,r.map)({}),n=e.body;Object.assign(s,(0,r.take)(n,{message:r.expectString}));let o=new l({$metadata:w(e),...s});return(0,r.decorateServiceException)(o,e.body)},C=async(e,t)=>{let s=(0,r.map)({}),n=e.body;Object.assign(s,(0,r.take)(n,{message:r.expectString}));let o=new p({$metadata:w(e),...s});return(0,r.decorateServiceException)(o,e.body)},w=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),O="accountId",R="accessToken",N="account_id",k="roleName",A="role_name",D="x-amz-sso_bearer_token";class F extends r.Command.classBuilder().ep(a).m(function(e,t,s,r){return[(0,o.getSerdePlugin)(s,this.serialize,this.deserialize),(0,n.rD)(s,e.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(h,m).ser(y).de(E).build(){}var _=s(79083),T=s(49720),U=s(48871),q=s(10921),z=s(40229),j=s(44716),$=s(55288),G=s(72587),H=s(10456);let M=async(e,t,s)=>({operation:(0,H.getSmithyContext)(t).operation,region:await (0,H.normalizeProvider)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),V=e=>{let t=[];switch(e.operation){case"GetRoleCredentials":case"ListAccountRoles":case"ListAccounts":case"Logout":t.push({schemeId:"smithy.api#noAuth"});break;default:t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})})}return t},L=e=>Object.assign((0,G.h)(e),{authSchemePreference:(0,H.normalizeProvider)(e.authSchemePreference??[])}),B={rE:"3.840.0"};var K=s(82132),Y=s(74088),W=s(63551),J=s(99752),Q=s(21612),X=s(13214),Z=s(45927),ee=s(42184),et=s(86473),es=s(49168),en=s(21678),eo=s(60052),er=s(50559),ei=s(49163);let ea="required",ed="argv",ec="isSet",eu="booleanEquals",el="error",ep="endpoint",eh="tree",eg="PartitionResult",em="getAttr",ef={[ea]:!1,type:"String"},eS={[ea]:!0,default:!1,type:"Boolean"},ey={ref:"Endpoint"},eE={fn:eu,[ed]:[{ref:"UseFIPS"},!0]},eP={fn:eu,[ed]:[{ref:"UseDualStack"},!0]},eI={},ex={fn:em,[ed]:[{ref:eg},"supportsFIPS"]},ev={ref:eg},eb={fn:eu,[ed]:[!0,{fn:em,[ed]:[ev,"supportsDualStack"]}]},eC=[eE],ew=[eP],eO=[{ref:"Region"}],eR={version:"1.0",parameters:{Region:ef,UseDualStack:eS,UseFIPS:eS,Endpoint:ef},rules:[{conditions:[{fn:ec,[ed]:[ey]}],rules:[{conditions:eC,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:el},{conditions:ew,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:el},{endpoint:{url:ey,properties:eI,headers:eI},type:ep}],type:eh},{conditions:[{fn:ec,[ed]:eO}],rules:[{conditions:[{fn:"aws.partition",[ed]:eO,assign:eg}],rules:[{conditions:[eE,eP],rules:[{conditions:[{fn:eu,[ed]:[!0,ex]},eb],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:eI,headers:eI},type:ep}],type:eh},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:el}],type:eh},{conditions:eC,rules:[{conditions:[{fn:eu,[ed]:[ex,!0]}],rules:[{conditions:[{fn:"stringEquals",[ed]:[{fn:em,[ed]:[ev,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:eI,headers:eI},type:ep},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:eI,headers:eI},type:ep}],type:eh},{error:"FIPS is enabled but this partition does not support FIPS",type:el}],type:eh},{conditions:ew,rules:[{conditions:[eb],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:eI,headers:eI},type:ep}],type:eh},{error:"DualStack is enabled but this partition does not support DualStack",type:el}],type:eh},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:eI,headers:eI},type:ep}],type:eh}],type:eh},{error:"Invalid Configuration: Missing Region",type:el}]},eN=new ei.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),ek=(e,t={})=>eN.get(e,()=>(0,ei.resolveEndpoint)(eR,{endpointParams:e,logger:t.logger}));ei.customEndpointFunctions.aws=er.awsEndpointFunctions;let eA=e=>({apiVersion:"2019-06-10",base64Decoder:e?.base64Decoder??en.fromBase64,base64Encoder:e?.base64Encoder??en.toBase64,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??ek,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??V,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new et.f2},{schemeId:"smithy.api#noAuth",identityProvider:e=>e.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new S.NoAuthSigner}],logger:e?.logger??new r.NoOpLogger,serviceId:e?.serviceId??"SSO",urlParser:e?.urlParser??es.parseUrl,utf8Decoder:e?.utf8Decoder??eo.fromUtf8,utf8Encoder:e?.utf8Encoder??eo.toUtf8});var eD=s(15807);let eF=e=>{(0,r.emitWarningIfUnsupportedVersion)(process.version);let t=(0,eD.I)(e),s=()=>t().then(r.loadConfigsForDefaultMode),n=eA(e);(0,K.I)(process.version);let o={profile:e?.profile,logger:n.logger};return{...n,...e,runtime:"node",defaultsMode:t,authSchemePreference:e?.authSchemePreference??(0,Q.loadConfig)(Y.$,o),bodyLengthChecker:e?.bodyLengthChecker??Z.n,defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,W.pf)({serviceId:n.serviceId,clientVersion:B.rE}),maxAttempts:e?.maxAttempts??(0,Q.loadConfig)($.qs,e),region:e?.region??(0,Q.loadConfig)(z.NODE_REGION_CONFIG_OPTIONS,{...z.NODE_REGION_CONFIG_FILE_OPTIONS,...o}),requestHandler:X.NodeHttpHandler.create(e?.requestHandler??s),retryMode:e?.retryMode??(0,Q.loadConfig)({...$.kN,default:async()=>(await s()).retryMode||ee.DEFAULT_RETRY_MODE},e),sha256:e?.sha256??J.V.bind(null,"sha256"),streamCollector:e?.streamCollector??X.streamCollector,useDualstackEndpoint:e?.useDualstackEndpoint??(0,Q.loadConfig)(z.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,o),useFipsEndpoint:e?.useFipsEndpoint??(0,Q.loadConfig)(z.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,o),userAgentAppId:e?.userAgentAppId??(0,Q.loadConfig)(W.hV,o)}};var e_=s(30208),eT=s(96126);let eU=e=>{let t=e.httpAuthSchemes,s=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){let s=t.findIndex(t=>t.schemeId===e.schemeId);-1===s?t.push(e):t.splice(s,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){s=e},httpAuthSchemeProvider:()=>s,setCredentials(e){n=e},credentials:()=>n}},eq=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),ez=(e,t)=>{let s=Object.assign((0,e_.Rq)(e),(0,r.getDefaultExtensionConfiguration)(e),(0,eT.getHttpHandlerExtensionConfiguration)(e),eU(e));return t.forEach(e=>e.configure(s)),Object.assign(e,(0,e_.$3)(s),(0,r.resolveDefaultRuntimeConfig)(s),(0,eT.resolveHttpHandlerRuntimeConfig)(s),eq(s))};class ej extends r.Client{config;constructor(...[e]){let t=eF(e||{});super(t),this.initConfig=t;let s=i(t),o=(0,q.resolveUserAgentConfig)(s),r=(0,$.$z)(o),a=(0,z.resolveRegionConfig)(r),d=(0,_.OV)(a),c=ez(L((0,n.Co)(d)),e?.extensions||[]);this.config=c,this.middlewareStack.use((0,q.getUserAgentPlugin)(this.config)),this.middlewareStack.use((0,$.ey)(this.config)),this.middlewareStack.use((0,j.vK)(this.config)),this.middlewareStack.use((0,_.TC)(this.config)),this.middlewareStack.use((0,T.Y7)(this.config)),this.middlewareStack.use((0,U.n4)(this.config)),this.middlewareStack.use((0,S.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config,{httpAuthSchemeParametersProvider:M,identityProviderConfigProvider:async e=>new S.DefaultIdentityProviderConfig({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,S.getHttpSigningPlugin)(this.config))}destroy(){super.destroy()}}}};