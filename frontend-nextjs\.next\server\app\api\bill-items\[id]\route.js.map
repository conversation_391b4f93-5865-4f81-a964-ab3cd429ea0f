{"version": 3, "file": "../app/api/bill-items/[id]/route.js", "mappings": "ubAAA,yHEAA,oDCAA,qGCAA,mECAA,0GCAA,qDCAA,gDCAA,kDCAA,gDCAA,uGCAA,iECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,wDCAA,yFCAA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,4VCLA,IAAMA,EAAcC,SAAAA,cAA+B,CAK5C,GALgD,YAKjCC,EACpBC,CAAoB,CACpB,CAFoBD,OAElBE,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEC,QAAM,CAAE,CAAG,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAEzB,GAAI,CAACD,EACH,IADW,GACJE,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAO,2BACT,EAAEC,MAAQ,IAAI,GAIlB,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMP,EAGfQ,EAAO,EAHQR,IAGFS,KAAAA,CAAM,CAAC,+BAA+B,EAAER,EAAAA,CAAQ,CAAE,CACnES,OAAS,EACPC,aAAAA,CAAe,CAAC,OAAO,EAAEd,QAAQe,GAAG,CAACC,gBAAgB,CAAE,EAE3D,GAAGC,IAAI,CAACC,GAAAA,EAAWX,IAAI,IAGjBY,EAAa,GAAGpB,EAAY,GAAf,MAAe,OAAgB,EAAEW,EAAI,EAElDU,EAAW,MAAXA,KAAiBR,CAAMO,EAAY,CACvCE,MAAQ,CAD+B,MAEvCR,OAAS,EACP,cAAgB,oBAChB,iBAAmBT,CAAAA,EACnB,IADmBA,WACHO,EAAKW,EAAAA,aAAe,CAAC,EAAE,EAAEC,aAAiB,IAC5D,CACF,GAEMC,EAAO,EAAPA,IAAaJ,EAASb,IAAI,EAAba,CAEnB,GAAI,CAACA,EAASK,EAAE,CACd,CADgB,EAAJA,IACLnB,EAAAA,YAAAA,CAAaC,IAAI,CAACiB,EAAM,CAAEf,CAAF,KAAEA,CAAQW,EAASX,MAAAA,GAGpD,OAAOH,EAAAA,YAAAA,CAAaC,IAAI,CAACiB,EAC3B,CAAE,CADyBA,CAAAA,IAClBhB,EAAO,CAEd,EAFc,KACdkB,OAAQlB,CAAAA,KAAK,CAAC,oCAAsCA,CAAAA,GAC7CF,EAD6CE,CAAAA,WAC7CF,CAAaC,IAAI,CACtB,CAAEC,KAAO,yBACT,EAAEC,MAAQ,IAAI,EAElB,CACF,CAKO,eAAekB,EACpBzB,CAAoB,CACpB,GAFoByB,KAElBxB,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,QAAEC,CAAM,CAAE,CAAG,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAEzB,GAAI,CAACD,EACH,IADW,GACJE,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAO,2BACT,EAAEC,MAAQ,IAAI,GAIlB,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMP,EAGfQ,EAAO,EAHQR,IAGFS,KAAAA,CAAM,CAAC,+BAA+B,EAAER,EAAAA,CAAQ,CAAE,CACnES,OAAS,EACPC,aAAAA,CAAe,CAAC,OAAO,EAAEd,QAAQe,GAAG,CAACC,gBAAgB,CAAE,EAE3D,GAAGC,IAAI,CAACC,GAAAA,EAAWX,IAAI,IAGjBqB,EAAO,EAAPA,IAAa1B,EAAQK,IAAI,CAAZL,EAGbiB,EAAa,GAAGpB,EAAY,GAAf,MAAe,OAAgB,EAAEW,EAAI,EAElDU,EAAW,MAAMR,KAAAA,CAAMO,EAAY,CACvCE,MAAQ,CAD+B,QAEvCR,OAAS,EACP,cAAgB,oBAChB,iBAAmBT,CAAAA,EACnB,IADmBA,WACHO,EAAKW,EAAAA,aAAe,CAAC,EAAE,EAAEC,aAAiB,IAC5D,EACAK,IAAMC,CAAAA,IAAAA,CAAKC,SAAS,CAACF,EACvB,EADuBA,CAGjBJ,EAAO,EAAPA,IAAaJ,EAASb,IAAI,EAAba,CAEnB,GAAI,CAACA,EAASK,EAAE,CACd,CADgB,EAAJA,IACLnB,EAAAA,YAAAA,CAAaC,IAAI,CAACiB,EAAM,CAAEf,CAAF,KAAEA,CAAQW,EAASX,MAAO,GAG3D,OAAOH,EAAAA,YAAAA,CAAaC,IAAI,CAACiB,EAC3B,CAAE,CADyBA,CAAAA,IAClBhB,EAAO,CAEd,EAFc,KACdkB,OAAQlB,CAAAA,KAAK,CAAC,oCAAsCA,CAAAA,GAC7CF,EAD6CE,CAAAA,WAC7CF,CAAaC,IAAI,CACtB,CAAEC,KAAO,yBACT,EAAEC,MAAQ,IAAI,EAElB,CACF,CAKO,eAAesB,EACpB7B,CAAoB,CACpB,IAFoB6B,IAElB5B,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,QAAEC,CAAM,CAAE,CAAG,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAEzB,GAAI,CAACD,EACH,IADW,GACJE,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAO,2BACT,EAAEC,MAAQ,IAAI,GAIlB,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMP,EAGfQ,EAAO,EAHQR,IAGFS,KAAAA,CAAM,CAAC,+BAA+B,EAAER,EAAAA,CAAQ,CAAE,CACnES,OAAS,EACPC,aAAAA,CAAe,CAAC,OAAO,EAAEd,QAAQe,GAAG,CAACC,gBAAgB,CAAE,EAE3D,GAAGC,IAAI,CAACC,GAAAA,EAAWX,IAAI,IAGjBY,EAAa,GAAGpB,EAAY,GAAf,MAAe,OAAgB,EAAEW,EAAI,EAElDU,EAAW,MAAXA,KAAiBR,CAAMO,EAAY,CACvCE,MAAQ,CAD+B,SAEvCR,OAAS,EACP,cAAgB,oBAChB,iBAAmBT,CAAAA,EACnB,IADmBA,WACHO,EAAKW,EAAAA,aAAe,CAAC,EAAE,EAAEC,aAAiB,IAC5D,CACF,GAEMC,EAAO,EAAPA,IAAaJ,EAASb,IAAI,EAAba,CAEnB,GAAI,CAACA,EAASK,EAAE,CACd,CADgB,EAAJA,IACLnB,EAAAA,YAAAA,CAAaC,IAAI,CAACiB,EAAM,CAAEf,CAAF,KAAEA,CAAQW,EAASX,MAAAA,GAGpD,OAAOH,EAAAA,YAAAA,CAAaC,IAAI,CAACiB,EAC3B,CAAE,CADyBA,CAAAA,IAClBhB,EAAO,CAEd,EAFc,KACdkB,OAAQlB,CAAAA,KAAK,CAAC,oCAAsCA,CAAAA,GAC7CF,EAAAA,CAD6CE,WAC7CF,CAAaC,IAAI,CACtB,CAAEC,KAAO,yBACT,EAAEC,MAAQ,IAAI,EAElB,CACF,CChKA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAItB,UAA6B,EAAE,OAAxB,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,sBAAsB,SAC1C,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAeuB,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,EAAYC,CAAf,CAA6C,KAAH,EAA5B,EAEnB,EAAS,EAAYC,EAA+B,MAAH,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,EAAYC,GAAf,IAA+C,EAAH,OAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,kCACA,gCACA,iBACA,0CACA,CAAK,CACL,qHACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,0BC5BA,kEEAA,sECAA,oDCAA,kECAA,yDCAA,sDCAA,8GCAA,qDCAA,2DCAA,yDCAA,iECAA,uDCAA,sDCAA,yDCAA,iDCAA,2DCAA,2DCAA,iDCAA,+DC6BgB,kBAAoF,yBC7BpG,4DCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://next-shadcn-dashboard-starter/src/app/api/bill-items/[id]/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/?2a70", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/?e6b9", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b/node_modules/next/dist/server/route-modules/app-route/module.compiled.js", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", null, "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 45962;\nmodule.exports = webpackEmptyContext;", "import { NextRequest, NextResponse } from 'next/server';\nimport { auth } from '@clerk/nextjs/server';\n\nconst BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n\n/**\n * GET /api/bill-items/[id] - Proxy to backend bill-items API\n */\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { userId } = await auth();\n    \n    if (!userId) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    const { id } = await params;\n\n    // Get user info from Clerk\n    const user = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    }).then(res => res.json());\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/bill-items/${id}`;\n    \n    const response = await fetch(backendUrl, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': user.email_addresses[0]?.email_address || '',\n      },\n    });\n\n    const data = await response.json();\n    \n    if (!response.ok) {\n      return NextResponse.json(data, { status: response.status });\n    }\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error('Error proxying bill-items request:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * PATCH /api/bill-items/[id] - Proxy to backend bill-items API\n */\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { userId } = await auth();\n    \n    if (!userId) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    const { id } = await params;\n\n    // Get user info from Clerk\n    const user = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    }).then(res => res.json());\n\n    // Get request body\n    const body = await request.json();\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/bill-items/${id}`;\n    \n    const response = await fetch(backendUrl, {\n      method: 'PATCH',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': user.email_addresses[0]?.email_address || '',\n      },\n      body: JSON.stringify(body),\n    });\n\n    const data = await response.json();\n    \n    if (!response.ok) {\n      return NextResponse.json(data, { status: response.status });\n    }\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error('Error proxying bill-items request:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * DELETE /api/bill-items/[id] - Proxy to backend bill-items API\n */\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { userId } = await auth();\n    \n    if (!userId) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    const { id } = await params;\n\n    // Get user info from Clerk\n    const user = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    }).then(res => res.json());\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/bill-items/${id}`;\n    \n    const response = await fetch(backendUrl, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': user.email_addresses[0]?.email_address || '',\n      },\n    });\n\n    const data = await response.json();\n    \n    if (!response.ok) {\n      return NextResponse.json(data, { status: response.status });\n    }\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error('Error proxying bill-items request:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/bill-items/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\bill-items\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/bill-items/[id]/route\",\n        pathname: \"/api/bill-items/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/bill-items/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\bill-items\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:os\");", null, "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "\"use strict\";\nif (process.env.NEXT_RUNTIME === 'edge') {\n    module.exports = require('next/dist/server/route-modules/app-route/module.js');\n} else {\n    if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n        if (process.env.NODE_ENV === 'development') {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo-experimental.runtime.dev.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.dev.js');\n            }\n        } else {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo-experimental.runtime.prod.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.prod.js');\n            }\n        }\n    } else {\n        if (process.env.NODE_ENV === 'development') {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo.runtime.dev.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route.runtime.dev.js');\n            }\n        } else {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo.runtime.prod.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route.runtime.prod.js');\n            }\n        }\n    }\n}\n\n//# sourceMappingURL=module.compiled.js.map", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["BACKEND_URL", "process", "GET", "request", "params", "userId", "auth", "NextResponse", "json", "error", "status", "id", "user", "fetch", "headers", "Authorization", "env", "CLERK_SECRET_KEY", "then", "res", "backendUrl", "response", "method", "email_addresses", "email_address", "data", "ok", "console", "PATCH", "body", "JSON", "stringify", "DELETE", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}