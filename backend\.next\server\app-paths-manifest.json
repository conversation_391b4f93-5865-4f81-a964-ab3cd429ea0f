{"/_not-found/page": "app/_not-found/page.js", "/api/appointments/[id]/route": "app/api/appointments/[id]/route.js", "/api/appointments/route": "app/api/appointments/route.js", "/api/appointments-direct/route": "app/api/appointments-direct/route.js", "/api/bill-items/route": "app/api/bill-items/route.js", "/api/bills/[id]/route": "app/api/bills/[id]/route.js", "/api/bill-items/[id]/route": "app/api/bill-items/[id]/route.js", "/api/bills/bulk-operations/route": "app/api/bills/bulk-operations/route.js", "/api/bills/generate-from-appointment/route": "app/api/bills/generate-from-appointment/route.js", "/api/bills/route": "app/api/bills/route.js", "/api/db-test/route": "app/api/db-test/route.js", "/api/deposits/apply-to-bill/route": "app/api/deposits/apply-to-bill/route.js", "/api/deposits/[id]/route": "app/api/deposits/[id]/route.js", "/api/deposits/refund/route": "app/api/deposits/refund/route.js", "/api/patients/[id]/route": "app/api/patients/[id]/route.js", "/api/deposits/route": "app/api/deposits/route.js", "/api/patients/convert-to-patient/route": "app/api/patients/convert-to-patient/route.js", "/api/patients/route": "app/api/patients/route.js", "/api/payments/[id]/receipt/route": "app/api/payments/[id]/receipt/route.js", "/api/payments/[id]/route": "app/api/payments/[id]/route.js", "/api/payments/process-payment/route": "app/api/payments/process-payment/route.js", "/api/payments/route": "app/api/payments/route.js", "/api/reports/analytics/route": "app/api/reports/analytics/route.js", "/api/reports/financial/route": "app/api/reports/financial/route.js", "/api/reports/monthly-revenue/route": "app/api/reports/monthly-revenue/route.js", "/api/reports/daily-revenue/route": "app/api/reports/daily-revenue/route.js", "/api/reports/outstanding-balances/route": "app/api/reports/outstanding-balances/route.js", "/api/reset-db/route": "app/api/reset-db/route.js", "/api/test/route": "app/api/test/route.js", "/api/treatments/[id]/route": "app/api/treatments/[id]/route.js", "/api/treatments/route": "app/api/treatments/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/api/users/route": "app/api/users/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/(payload)/api/graphql-playground/route": "app/(payload)/api/graphql-playground/route.js", "/(payload)/api/graphql/route": "app/(payload)/api/graphql/route.js", "/(payload)/api/[...slug]/route": "app/(payload)/api/[...slug]/route.js", "/my-route/route": "app/my-route/route.js", "/(payload)/admin/[[...segments]]/page": "app/(payload)/admin/[[...segments]]/page.js", "/(frontend)/page": "app/(frontend)/page.js"}