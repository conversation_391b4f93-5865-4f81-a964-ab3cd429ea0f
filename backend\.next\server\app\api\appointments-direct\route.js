(()=>{var e={};e.id=6562,e.ids=[6562],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34869:()=>{},44468:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>c,routeModule:()=>u,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var a=r(70293),n=r(32498),o=r(83889),i=r(96602),p=e([i]);i=(p.then?(await p)():p)[0];let u=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/appointments-direct/route",pathname:"/api/appointments-direct",filename:"route",bundlePath:"app/api/appointments-direct/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\backend\\src\\app\\api\\appointments-direct\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=u;function c(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}s()}catch(e){s(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64939:e=>{"use strict";e.exports=import("pg")},93077:()=>{},96602:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>i});var a=r(27492),n=r(64939),o=e([n]);async function i(){try{let e=new n.Pool({connectionString:process.env.DATABASE_URI}),t=await e.connect();if(!(await t.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'appointments'
      );
    `)).rows[0].exists)return t.release(),await e.end(),a.NextResponse.json({message:"Appointments table does not exist",tables_exist:!1});let r=await t.query(`
      SELECT id, created_at, updated_at 
      FROM appointments 
      LIMIT 10
    `);return t.release(),await e.end(),a.NextResponse.json({message:"Appointments fetched successfully",count:r.rows.length,appointments:r.rows,tables_exist:!0})}catch(e){return console.error("Database query error:",e),a.NextResponse.json({message:"Database query failed",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}n=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[3889,9556],()=>r(44468));module.exports=s})();