{"version": 3, "file": "9293.js", "mappings": "qeAAA,iCCmBA,MDlBA,UCEuC,SDDvC,KCiBuC,EDjBvC,sBAA2D,aAAa,qBACxE,ECAuC,CACvC,0CACA,eAAY,wEAA+E,EAAQ,KAAS,CAW5G,YAAuB,GATvB,CACA,aACA,gBACA,eACA,iBACA,YACA,QACA,EAEuB,KAVN,EAAQ,KAAW,EAUb,IADvB,MATwB,MASxB,KACuB,CACvB,CACA,CAAC,IChBD,MACA,sBAA4B,GAAQ,wEACpC,EACA,OACO,EAAW,IAClB,QADkB,EAGE,IAEpB,OACA,CAHoB,CAGF,MAClB,MADkB,IAGT,EAAW,MAEpB,MAFoB,CAGpB,EAAkB,KAClB,OADkB,EAGT,EAAW,wGCnBpB,aACA,eAAU,GAAa,CAAE,OAAa,GACtC,EAAe,QAAe,GAC9B,EAAc,QAAc,GAE5B,UADA,wBAEA,CACA,aACA,kBACA,4HAAsK,EAAG,aAAa,EAAU,2BAA2B,EAAK,aAAa,EAAU,EACvP,CAAU,cAAa,CAAE,OAAa,GACtC,EAAe,QAAe,GAC9B,EAAc,QAAc,GAC5B,0BACA,oBACA,YACA,eACA,4BAAuD,EAAc,IACrE,eAIA,SACA,KACA,yCAEA,KACA,qBAGA", "sources": ["webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/chunk-BUSYA2B4.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/runtime/node/safe-node-apis.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/fs/utils.js", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb/node_modules/@clerk/nextjs/dist/esm/server/fs/middleware-location.js"], "sourcesContent": ["var __getOwnPropNames = Object.getOwnPropertyNames;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\n\nexport {\n  __commonJS\n};\n//# sourceMappingURL=chunk-BUSYA2B4.js.map", "import {\n  __commonJS\n} from \"../../chunk-BUSYA2B4.js\";\nvar require_safe_node_apis = __commonJS({\n  \"src/runtime/node/safe-node-apis.js\"(exports, module) {\n    const { existsSync, writeFileSync, readFileSync, appendFileSync, mkdirSync, rmSync } = require(\"node:fs\");\n    const path = require(\"node:path\");\n    const fs = {\n      existsSync,\n      writeFileSync,\n      readFileSync,\n      appendFileSync,\n      mkdirSync,\n      rmSync\n    };\n    const cwd = () => process.cwd();\n    module.exports = { fs, path, cwd };\n  }\n});\nexport default require_safe_node_apis();\n//# sourceMappingURL=safe-node-apis.js.map", "import \"../../chunk-BUSYA2B4.js\";\nimport nodeRuntime from \"#safe-node-apis\";\nconst throwMissingFsModule = (module) => {\n  throw new Error(`Clerk: ${module} is missing. This is an internal error. Please contact Clerk's support.`);\n};\nconst nodeFsOrThrow = () => {\n  if (!nodeRuntime.fs) {\n    throwMissingFsModule(\"fs\");\n  }\n  return nodeRuntime.fs;\n};\nconst nodePathOrThrow = () => {\n  if (!nodeRuntime.path) {\n    throwMissingFsModule(\"path\");\n  }\n  return nodeRuntime.path;\n};\nconst nodeCwdOrThrow = () => {\n  if (!nodeRuntime.cwd) {\n    throwMissingFsModule(\"cwd\");\n  }\n  return nodeRuntime.cwd;\n};\nexport {\n  nodeCwdOrThrow,\n  nodeFsOrThrow,\n  nodePathOrThrow\n};\n//# sourceMappingURL=utils.js.map", "import \"../../chunk-BUSYA2B4.js\";\nimport { nodeCwdOrThrow, nodeFsOrThrow, nodePathOrThrow } from \"./utils\";\nfunction hasSrcAppDir() {\n  const { existsSync } = nodeFsOrThrow();\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  const projectWithAppSrc = path.join(cwd(), \"src\", \"app\");\n  return !!existsSync(projectWithAppSrc);\n}\nfunction suggestMiddlewareLocation() {\n  const fileExtensions = [\"ts\", \"js\"];\n  const suggestionMessage = (extension, to, from) => `Clerk: clerkMiddleware() was not run, your middleware file might be misplaced. Move your middleware file to ./${to}middleware.${extension}. Currently located at ./${from}middleware.${extension}`;\n  const { existsSync } = nodeFsOrThrow();\n  const path = nodePathOrThrow();\n  const cwd = nodeCwdOrThrow();\n  const projectWithAppSrcPath = path.join(cwd(), \"src\", \"app\");\n  const projectWithAppPath = path.join(cwd(), \"app\");\n  const checkMiddlewareLocation = (basePath, to, from) => {\n    for (const fileExtension of fileExtensions) {\n      if (existsSync(path.join(basePath, `middleware.${fileExtension}`))) {\n        return suggestionMessage(fileExtension, to, from);\n      }\n    }\n    return void 0;\n  };\n  if (existsSync(projectWithAppSrcPath)) {\n    return checkMiddlewareLocation(projectWithAppSrcPath, \"src/\", \"src/app/\") || checkMiddlewareLocation(cwd(), \"src/\", \"\");\n  }\n  if (existsSync(projectWithAppPath)) {\n    return checkMiddlewareLocation(projectWithAppPath, \"\", \"app/\");\n  }\n  return void 0;\n}\nexport {\n  hasSrcAppDir,\n  suggestMiddlewareLocation\n};\n//# sourceMappingURL=middleware-location.js.map"], "names": [], "sourceRoot": ""}