try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="df46e07f-a4be-424b-9130-9da6d752401a",e._sentryDebugIdIdentifier="sentry-dbid-df46e07f-a4be-424b-9130-9da6d752401a")}catch(e){}"use strict";exports.id=1822,exports.ids=[1822],exports.modules={20105:(e,t)=>{function i(e){var t;let{config:i,src:r,width:o,quality:n}=e,l=n||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+o+"&q="+l+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),i.__next_img_default=!0;let r=i},31822:(e,t,i)=>{i.d(t,{default:()=>o.a});var r=i(98849),o=i.n(r)},44347:(e,t,i)=>{e.exports=i(42989).vendored.contexts.ImageConfigContext},50304:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return r}});let i=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},77117:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),i(32952);let r=i(97852),o=i(50304),n=["-moz-initial","fill","none","scale-down",void 0];function l(e){return void 0!==e.default}function s(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var i,a;let d,u,f,{src:c,sizes:g,unoptimized:m=!1,priority:p=!1,loading:h,className:b,quality:v,width:y,height:_,fill:w=!1,style:E,overrideSrc:S,onLoad:x,onLoadingComplete:j,placeholder:C="empty",blurDataURL:P,fetchPriority:O,decoding:R="async",layout:z,objectFit:I,objectPosition:D,lazyBoundary:M,lazyRoot:T,...A}=e,{imgConf:N,showAltText:k,blurComplete:F,defaultLoader:L}=t,G=N||o.imageConfigDefault;if("allSizes"in G)d=G;else{let e=[...G.deviceSizes,...G.imageSizes].sort((e,t)=>e-t),t=G.deviceSizes.sort((e,t)=>e-t),r=null==(i=G.qualities)?void 0:i.sort((e,t)=>e-t);d={...G,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=A.loader||L;delete A.loader,delete A.srcSet;let q="__next_img_default"in B;if(q){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:i,...r}=t;return e(r)}}if(z){"fill"===z&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[z];e&&(E={...E,...e});let t={responsive:"100vw",fill:"100vw"}[z];t&&!g&&(g=t)}let W="",V=s(y),X=s(_);if((a=c)&&"object"==typeof a&&(l(a)||void 0!==a.src)){let e=l(c)?c.default:c;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(u=e.blurWidth,f=e.blurHeight,P=P||e.blurDataURL,W=e.src,!w)if(V||X){if(V&&!X){let t=V/e.width;X=Math.round(e.height*t)}else if(!V&&X){let t=X/e.height;V=Math.round(e.width*t)}}else V=e.width,X=e.height}let U=!p&&("lazy"===h||void 0===h);(!(c="string"==typeof c?c:W)||c.startsWith("data:")||c.startsWith("blob:"))&&(m=!0,U=!1),d.unoptimized&&(m=!0),q&&!d.dangerouslyAllowSVG&&c.split("?",1)[0].endsWith(".svg")&&(m=!0);let J=s(v),Y=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:I,objectPosition:D}:{},k?{}:{color:"transparent"},E),H=F||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:V,heightInt:X,blurWidth:u,blurHeight:f,blurDataURL:P||"",objectFit:Y.objectFit})+'")':'url("'+C+'")',$=n.includes(Y.objectFit)?"fill"===Y.objectFit?"100% 100%":"cover":Y.objectFit,K=H?{backgroundSize:$,backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:H}:{},Q=function(e){let{config:t,src:i,unoptimized:r,width:o,quality:n,sizes:l,loader:s}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:a,kind:d}=function(e,t,i){let{deviceSizes:r,allSizes:o}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,l),u=a.length-1;return{sizes:l||"w"!==d?l:"100vw",srcSet:a.map((e,r)=>s({config:t,src:i,quality:n,width:e})+" "+("w"===d?e:r+1)+d).join(", "),src:s({config:t,src:i,quality:n,width:a[u]})}}({config:d,src:c,unoptimized:m,width:V,quality:J,sizes:g,loader:B});return{props:{...A,loading:U?"lazy":h,fetchPriority:O,width:V,height:X,decoding:R,className:b,style:{...Y,...K},sizes:Q.sizes,srcSet:Q.srcSet,src:S||Q.src},meta:{unoptimized:m,priority:p,placeholder:C,fill:w}}}},77681:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return _}});let r=i(9538),o=i(70293),n=i(24443),l=o._(i(60222)),s=r._(i(89859)),a=r._(i(11900)),d=i(77117),u=i(50304),f=i(44347);i(32952);let c=i(6280),g=r._(i(20105)),m=i(50994),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function h(e,t,i,r,o,n,l){let s=null==e?void 0:e.src;e&&e["data-loaded-src"]!==s&&(e["data-loaded-src"]=s,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,o=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function b(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,l.forwardRef)((e,t)=>{let{src:i,srcSet:r,sizes:o,height:s,width:a,decoding:d,className:u,style:f,fetchPriority:c,placeholder:g,loading:p,unoptimized:v,fill:y,onLoadRef:_,onLoadingCompleteRef:w,setBlurComplete:E,setShowAltText:S,sizesInput:x,onLoad:j,onError:C,...P}=e,O=(0,l.useCallback)(e=>{e&&(C&&(e.src=e.src),e.complete&&h(e,g,_,w,E,v,x))},[i,g,_,w,E,C,v,x]),R=(0,m.useMergedRef)(t,O);return(0,n.jsx)("img",{...P,...b(c),loading:p,width:a,height:s,decoding:d,"data-nimg":y?"fill":"1",className:u,style:f,sizes:o,srcSet:r,src:i,ref:R,onLoad:e=>{h(e.currentTarget,g,_,w,E,v,x)},onError:e=>{S(!0),"empty"!==g&&E(!0),C&&C(e)}})});function y(e){let{isAppRouter:t,imgAttributes:i}=e,r={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...b(i.fetchPriority)};return t&&s.default.preload?(s.default.preload(i.src,r),null):(0,n.jsx)(a.default,{children:(0,n.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...r},"__nimg-"+i.src+i.srcSet+i.sizes)})}let _=(0,l.forwardRef)((e,t)=>{let i=(0,l.useContext)(c.RouterContext),r=(0,l.useContext)(f.ImageConfigContext),o=(0,l.useMemo)(()=>{var e;let t=p||r||u.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),n=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:o,qualities:n}},[r]),{onLoad:s,onLoadingComplete:a}=e,m=(0,l.useRef)(s);(0,l.useEffect)(()=>{m.current=s},[s]);let h=(0,l.useRef)(a);(0,l.useEffect)(()=>{h.current=a},[a]);let[b,_]=(0,l.useState)(!1),[w,E]=(0,l.useState)(!1),{props:S,meta:x}=(0,d.getImgProps)(e,{defaultLoader:g.default,imgConf:o,blurComplete:b,showAltText:w});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(v,{...S,unoptimized:x.unoptimized,placeholder:x.placeholder,fill:x.fill,onLoadRef:m,onLoadingCompleteRef:h,setBlurComplete:_,setShowAltText:E,sizesInput:e.sizes,ref:t}),x.priority?(0,n.jsx)(y,{isAppRouter:!i,imgAttributes:S}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97852:(e,t)=>{function i(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:o,blurDataURL:n,objectFit:l}=e,s=r?40*r:t,a=o?40*o:i,d=s&&a?"viewBox='0 0 "+s+" "+a+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},98849:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return a},getImageProps:function(){return s}});let r=i(9538),o=i(77117),n=i(77681),l=r._(i(20105));function s(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let a=n.Image}};
//# sourceMappingURL=1822.js.map