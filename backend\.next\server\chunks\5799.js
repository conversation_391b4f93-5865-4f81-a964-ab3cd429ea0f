"use strict";exports.id=5799,exports.ids=[5799],exports.modules={22584:(e,t,a)=>{function n(e){return(t,a={})=>{let n,i=a.width,o=i&&e.matchPatterns[i]||e.matchPatterns[e.defaultMatchWidth],r=t.match(o);if(!r)return null;let u=r[0],s=i&&e.parsePatterns[i]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(s)?function(e,t){for(let a=0;a<e.length;a++)if(t(e[a]))return a}(s,e=>e.test(u)):function(e,t){for(let a in e)if(Object.prototype.hasOwnProperty.call(e,a)&&t(e[a]))return a}(s,e=>e.test(u));return n=e.valueCallback?e.valueCallback(l):l,{value:n=a.valueCallback?a.valueCallback(n):n,rest:t.slice(u.length)}}}a.d(t,{A:()=>n})},43416:(e,t,a)=>{a.d(t,{K:()=>n});function n(e){return(t,a={})=>{let n=t.match(e.matchPattern);if(!n)return null;let i=n[0],o=t.match(e.parsePattern);if(!o)return null;let r=e.valueCallback?e.valueCallback(o[0]):o[0];return{value:r=a.valueCallback?a.valueCallback(r):r,rest:t.slice(i.length)}}}},84246:(e,t,a)=>{a.d(t,{o:()=>n});function n(e){return(t,a)=>{let n;if("formatting"===(a?.context?String(a.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,i=a?.width?String(a.width):t;n=e.formattingValues[i]||e.formattingValues[t]}else{let t=e.defaultWidth,i=a?.width?String(a.width):e.defaultWidth;n=e.values[i]||e.values[t]}return n[e.argumentCallback?e.argumentCallback(t):t]}}},89500:(e,t,a)=>{a.d(t,{k:()=>n});function n(e){return (t={})=>{let a=t.width?String(t.width):e.defaultWidth;return e.formats[a]||e.formats[e.defaultWidth]}}},95799:(e,t,a)=>{a.r(t),a.d(t,{default:()=>k,et:()=>h});let n={lessThanXSeconds:{standalone:{one:"v\xe4hem kui \xfcks sekund",other:"v\xe4hem kui {{count}} sekundit"},withPreposition:{one:"v\xe4hem kui \xfche sekundi",other:"v\xe4hem kui {{count}} sekundi"}},xSeconds:{standalone:{one:"\xfcks sekund",other:"{{count}} sekundit"},withPreposition:{one:"\xfche sekundi",other:"{{count}} sekundi"}},halfAMinute:{standalone:"pool minutit",withPreposition:"poole minuti"},lessThanXMinutes:{standalone:{one:"v\xe4hem kui \xfcks minut",other:"v\xe4hem kui {{count}} minutit"},withPreposition:{one:"v\xe4hem kui \xfche minuti",other:"v\xe4hem kui {{count}} minuti"}},xMinutes:{standalone:{one:"\xfcks minut",other:"{{count}} minutit"},withPreposition:{one:"\xfche minuti",other:"{{count}} minuti"}},aboutXHours:{standalone:{one:"umbes \xfcks tund",other:"umbes {{count}} tundi"},withPreposition:{one:"umbes \xfche tunni",other:"umbes {{count}} tunni"}},xHours:{standalone:{one:"\xfcks tund",other:"{{count}} tundi"},withPreposition:{one:"\xfche tunni",other:"{{count}} tunni"}},xDays:{standalone:{one:"\xfcks p\xe4ev",other:"{{count}} p\xe4eva"},withPreposition:{one:"\xfche p\xe4eva",other:"{{count}} p\xe4eva"}},aboutXWeeks:{standalone:{one:"umbes \xfcks n\xe4dal",other:"umbes {{count}} n\xe4dalat"},withPreposition:{one:"umbes \xfche n\xe4dala",other:"umbes {{count}} n\xe4dala"}},xWeeks:{standalone:{one:"\xfcks n\xe4dal",other:"{{count}} n\xe4dalat"},withPreposition:{one:"\xfche n\xe4dala",other:"{{count}} n\xe4dala"}},aboutXMonths:{standalone:{one:"umbes \xfcks kuu",other:"umbes {{count}} kuud"},withPreposition:{one:"umbes \xfche kuu",other:"umbes {{count}} kuu"}},xMonths:{standalone:{one:"\xfcks kuu",other:"{{count}} kuud"},withPreposition:{one:"\xfche kuu",other:"{{count}} kuu"}},aboutXYears:{standalone:{one:"umbes \xfcks aasta",other:"umbes {{count}} aastat"},withPreposition:{one:"umbes \xfche aasta",other:"umbes {{count}} aasta"}},xYears:{standalone:{one:"\xfcks aasta",other:"{{count}} aastat"},withPreposition:{one:"\xfche aasta",other:"{{count}} aasta"}},overXYears:{standalone:{one:"rohkem kui \xfcks aasta",other:"rohkem kui {{count}} aastat"},withPreposition:{one:"rohkem kui \xfche aasta",other:"rohkem kui {{count}} aasta"}},almostXYears:{standalone:{one:"peaaegu \xfcks aasta",other:"peaaegu {{count}} aastat"},withPreposition:{one:"peaaegu \xfche aasta",other:"peaaegu {{count}} aasta"}}};var i=a(89500);let o={date:(0,i.k)({formats:{full:"EEEE, d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"dd.MM.y"},defaultWidth:"full"}),time:(0,i.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,i.k)({formats:{full:"{{date}} 'kell' {{time}}",long:"{{date}} 'kell' {{time}}",medium:"{{date}}. {{time}}",short:"{{date}}. {{time}}"},defaultWidth:"full"})},r={lastWeek:"'eelmine' eeee 'kell' p",yesterday:"'eile kell' p",today:"'t\xe4na kell' p",tomorrow:"'homme kell' p",nextWeek:"'j\xe4rgmine' eeee 'kell' p",other:"P"};var u=a(84246);let s={narrow:["J","V","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jaan","veebr","m\xe4rts","apr","mai","juuni","juuli","aug","sept","okt","nov","dets"],wide:["jaanuar","veebruar","m\xe4rts","aprill","mai","juuni","juuli","august","september","oktoober","november","detsember"]},l={narrow:["P","E","T","K","N","R","L"],short:["P","E","T","K","N","R","L"],abbreviated:["p\xfchap.","esmasp.","teisip.","kolmap.","neljap.","reede.","laup."],wide:["p\xfchap\xe4ev","esmasp\xe4ev","teisip\xe4ev","kolmap\xe4ev","neljap\xe4ev","reede","laup\xe4ev"]},m={ordinalNumber:(e,t)=>Number(e)+".",era:(0,u.o)({values:{narrow:["e.m.a","m.a.j"],abbreviated:["e.m.a","m.a.j"],wide:["enne meie ajaarvamist","meie ajaarvamise j\xe4rgi"]},defaultWidth:"wide"}),quarter:(0,u.o)({values:{narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,u.o)({values:s,defaultWidth:"wide",formattingValues:s,defaultFormattingWidth:"wide"}),day:(0,u.o)({values:l,defaultWidth:"wide",formattingValues:l,defaultFormattingWidth:"wide"}),dayPeriod:(0,u.o)({values:{narrow:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6",noon:"keskp\xe4ev",morning:"hommik",afternoon:"p\xe4rastl\xf5una",evening:"\xf5htu",night:"\xf6\xf6"},abbreviated:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6",noon:"keskp\xe4ev",morning:"hommik",afternoon:"p\xe4rastl\xf5una",evening:"\xf5htu",night:"\xf6\xf6"},wide:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6",noon:"keskp\xe4ev",morning:"hommik",afternoon:"p\xe4rastl\xf5una",evening:"\xf5htu",night:"\xf6\xf6"}},defaultWidth:"wide",formattingValues:{narrow:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6l",noon:"keskp\xe4eval",morning:"hommikul",afternoon:"p\xe4rastl\xf5unal",evening:"\xf5htul",night:"\xf6\xf6sel"},abbreviated:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6l",noon:"keskp\xe4eval",morning:"hommikul",afternoon:"p\xe4rastl\xf5unal",evening:"\xf5htul",night:"\xf6\xf6sel"},wide:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6l",noon:"keskp\xe4eval",morning:"hommikul",afternoon:"p\xe4rastl\xf5unal",evening:"\xf5htul",night:"\xf6\xf6sel"}},defaultFormattingWidth:"wide"})};var d=a(22584);let h={code:"et",formatDistance:(e,t,a)=>{let i,o=a?.addSuffix?n[e].withPreposition:n[e].standalone;if(i="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",String(t)),a?.addSuffix)if(a.comparison&&a.comparison>0)return i+" p\xe4rast";else return i+" eest";return i},formatLong:o,formatRelative:(e,t,a,n)=>r[e],localize:m,match:{ordinalNumber:(0,a(43416).K)({matchPattern:/^\d+\./i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,d.A)({matchPatterns:{narrow:/^(e\.m\.a|m\.a\.j|eKr|pKr)/i,abbreviated:/^(e\.m\.a|m\.a\.j|eKr|pKr)/i,wide:/^(enne meie ajaarvamist|meie ajaarvamise järgi|enne Kristust|pärast Kristust)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^e/i,/^(m|p)/i]},defaultParseWidth:"any"}),quarter:(0,d.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^K[1234]/i,wide:/^[1234](\.)? kvartal/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,d.A)({matchPatterns:{narrow:/^[jvmasond]/i,abbreviated:/^(jaan|veebr|märts|apr|mai|juuni|juuli|aug|sept|okt|nov|dets)/i,wide:/^(jaanuar|veebruar|märts|aprill|mai|juuni|juuli|august|september|oktoober|november|detsember)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^v/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^v/i,/^mär/i,/^ap/i,/^mai/i,/^juun/i,/^juul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,d.A)({matchPatterns:{narrow:/^[petknrl]/i,short:/^[petknrl]/i,abbreviated:/^(püh?|esm?|tei?|kolm?|nel?|ree?|laup?)\.?/i,wide:/^(pühapäev|esmaspäev|teisipäev|kolmapäev|neljapäev|reede|laupäev)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^p/i,/^e/i,/^t/i,/^k/i,/^n/i,/^r/i,/^l/i]},defaultParseWidth:"any"}),dayPeriod:(0,d.A)({matchPatterns:{any:/^(am|pm|keskööl?|keskpäev(al)?|hommik(ul)?|pärastlõunal?|õhtul?|öö(sel)?)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^keskö/i,noon:/^keskp/i,morning:/hommik/i,afternoon:/pärastlõuna/i,evening:/õhtu/i,night:/öö/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}},k=h}};