try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="bf0b1822-b4bc-4645-b75f-d261b96fc569",e._sentryDebugIdIdentifier="sentry-dbid-bf0b1822-b4bc-4645-b75f-d261b96fc569")}catch(e){}"use strict";exports.id=9616,exports.ids=[9616],exports.modules={13932:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(23457).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19022:(e,t,r)=>{r.d(t,{H$:()=>u,mG:()=>s,V2:()=>d,fS:()=>h,ev:()=>g,Rg:()=>c,At:()=>l,tm:()=>f,rB:()=>o,Mh:()=>m,nN:()=>p});var n=r(59144),i=r(26124);r(67814);var a=r(76099);process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let s=process.env.CLERK_API_VERSION||"v1",o=process.env.CLERK_SECRET_KEY||"",l="pk_test_d2VsY29tZWQtZmVsaW5lLTgyLmNsZXJrLmFjY291bnRzLmRldiQ";process.env.CLERK_ENCRYPTION_KEY;let u=process.env.CLERK_API_URL||(e=>{let t=(0,n.q5)(e)?.frontendApi;return t?.startsWith("clerk.")&&i.iM.some(e=>t?.endsWith(e))?i.FW:i.mG.some(e=>t?.endsWith(e))?i.Vc:i.ub.some(e=>t?.endsWith(e))?i.HG:i.FW})(l),d=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",c=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",h=(0,a.zz)(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,f={name:"@clerk/nextjs",version:"6.12.12",environment:"production"},p=(0,a.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),m=(0,a.zz)(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),g=(0,a.zz)(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1},21254:(e,t,r)=>{r.d(t,{Fj:()=>a,MC:()=>i,b_:()=>n});var n=()=>!1,i=()=>!1,a=()=>{try{return!0}catch{}return!1}},23457:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return i},getAccessFallbackErrorTypeByStatus:function(){return o},getAccessFallbackHTTPStatus:function(){return s},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),i="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}function s(e){return Number(e.digest.split(";")[1])}function o(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26124:(e,t,r)=>{r.d(t,{FW:()=>u,HG:()=>l,Vc:()=>o,gE:()=>i,iM:()=>n,mG:()=>a,ub:()=>s});var n=[".lcl.dev",".lclstage.dev",".lclclerk.com"],i=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],a=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],s=[".accountsstage.dev"],o="https://api.lclclerk.com",l="https://api.clerkstage.dev",u="https://api.clerk.com"},26604:(e,t,r)=>{r.d(t,{jn:()=>i,qu:()=>n,z:()=>a,zF:()=>s});var n={InvalidSecretKey:"clerk_key_invalid"},i={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",RemoteJWKInvalid:"jwk-remote-invalid",RemoteJWKMissing:"jwk-remote-missing",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},a={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable.",EnsureClockSync:"Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization)."},s=class e extends Error{constructor({action:t,message:r,reason:n}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=n,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}}},27205:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},27235:(e,t,r)=>{r.d(t,{ai:()=>k,at:()=>b,ot:()=>y});var n=r(19439);r(37271);var i=r(61648),a=r(84041);let s=(0,r(33634)._r)({packageName:"@clerk/nextjs"});var o=r(36461);r(77875),r(26604),r(67814);var l=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let n=new URL(t);r=new URL(e,n.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},u=r(72016),d=r(19022);let c={rE:"15.3.2"},h=!(c.rE.startsWith("13.")||c.rE.startsWith("14.0"))&&(0,u.b_)()&&!d.ev,f="__clerk_keys_";async function p(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function m(){let e=process.env.PWD;if(!e)return`${f}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),r=await p(t);return`${f}${r}`}async function g(e){let t;if(!h)return;let r=await m();try{r&&(t=JSON.parse(e(r)||"{}"))}catch{t=void 0}return t}async function y(e){let{claimUrl:t,publishableKey:r,secretKey:n,returnUrl:s}=e,l=await (0,i.UL)(),u=new Request("https://placeholder.com",{headers:await (0,i.b3)()}),d=await g(e=>{var t;return null==(t=l.get(e))?void 0:t.value}),c=(null==d?void 0:d.publishableKey)===r,h=(null==d?void 0:d.secretKey)===n;if(!c||!h){var f,p,y,k,b,_;if(l.set(await m(),JSON.stringify({claimUrl:t,publishableKey:r,secretKey:n}),{secure:!0,httpOnly:!0}),f="AuthStatus",((p=o.AA.Attributes[f])in u?u[p]:void 0)||(y=u,k=o.AA.Headers[f],function(e){try{let{headers:t,nextUrl:r,cookies:n}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==n?void 0:n.get)}catch{return!1}}(y)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(y)?y.headers.get(k):y.headers[k]||y.headers[k.toLowerCase()]||(null==(_=null==(b=y.socket)?void 0:b._httpMessage)?void 0:_.getHeader(k))))return void(0,a.redirect)(`/clerk-sync-keyless?returnUrl=${s}`,a.RedirectType.replace)}}async function k(){if(!h)return null;let e=await r.e(5896).then(r.bind(r,95896)).then(e=>e.createOrReadKeyless()).catch(()=>null);if(!e)return s.throwMissingPublishableKeyError(),null;let{clerkDevelopmentCache:t,createKeylessModeMessage:n}=await r.e(2723).then(r.bind(r,85104));null==t||t.log({cacheKey:e.publishableKey,msg:n(e)});let{claimUrl:a,publishableKey:o,secretKey:l,apiKeysUrl:u}=e;return(await (0,i.UL)()).set(await m(),JSON.stringify({claimUrl:a,publishableKey:o,secretKey:l}),{secure:!1,httpOnly:!1}),{claimUrl:a,publishableKey:o,apiKeysUrl:u}}async function b(){h&&await r.e(5896).then(r.bind(r,95896)).then(e=>e.removeKeyless()).catch(()=>{})}(0,r(73283).D)([k,b,y]),(0,n.A)(k,"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261",null),(0,n.A)(b,"7f7b45347fd50452ee6e2850ded1018991a7b086f0",null),(0,n.A)(y,"7f909588461cb83e855875f4939d6f26e4ae81b49e",null)},29520:(e,t)=>{t.qg=function(e,t){let s=new r,o=e.length;if(o<2)return s;let l=t?.decode||a,u=0;do{let t=e.indexOf("=",u);if(-1===t)break;let r=e.indexOf(";",u),a=-1===r?o:r;if(t>a){u=e.lastIndexOf(";",t-1)+1;continue}let d=n(e,u,t),c=i(e,t,d),h=e.slice(d,c);if(void 0===s[h]){let r=n(e,t+1,a),o=i(e,a,r),u=l(e.slice(r,o));s[h]=u}u=a+1}while(u<o);return s},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function n(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function i(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function a(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},33186:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(23457).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33634:(e,t,r)=>{r.d(t,{LR:()=>i,_r:()=>s,u$:()=>n});function n(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn}}}var i=class e extends Error{constructor(t,{data:r,status:i,clerkTraceId:a}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=i,this.message=t,this.clerkTraceId=a,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(n):[]}(r)}},a=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function s({packageName:e,customMessages:t}){let r=e,n={...a,...t};function i(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(n,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(i(n.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(i(n.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(i(n.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(i(n.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(i(n.MissingClerkProvider,e))},throw(e){throw Error(i(e))}}}r(67814)},36461:(e,t,r)=>{r.d(t,{AA:()=>N,Bs:()=>tm,y3:()=>eW,nr:()=>tt});var n=r(77875),i=r(26604);r(67814);var a={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!0,jitter:!0},s=async e=>new Promise(t=>setTimeout(t,e)),o=(e,t)=>t?e*(1+Math.random()):e,l=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=o(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await s(r()),t++}},u=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:u,factor:d,retryImmediately:c,jitter:h}={...a,...t},f=l({initialDelay:i,maxDelayBetweenRetries:u,factor:d,jitter:h});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;c&&1===r?await s(o(100,h)):await f()}},d=r(59144),c=r(21254),h=new Set,f=(e,t,r)=>{let n=(0,c.MC)()||(0,c.Fj)(),i=r??e;h.has(i)||n||(h.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},p=r(33634),m=r(76849),g={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},y=new Set(["first_factor","second_factor","multi_factor"]),k=new Set(["strict_mfa","strict","moderate","lax"]),b=e=>"number"==typeof e&&e>0,_=e=>y.has(e),v=e=>k.has(e),w=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(e.permission):e.role?n===e.role:null:null},T=e=>{if(!e)return!1;let t="string"==typeof e&&v(e),r="object"==typeof e&&_(e.level)&&b(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?g[e]:e).bind(null,e)},S=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=T(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[a,s]=t,o=-1!==a?i>a:null,l=-1!==s?i>s:null;switch(n){case"first_factor":return o;case"second_factor":return -1!==s?l:o;case"multi_factor":return -1===s?o:o&&l}},E=e=>t=>{if(!e.userId)return!1;let r=w(t,e),n=S(t,e);return[r,n].some(e=>null===e)?[r,n].some(e=>!0===e):[r,n].every(e=>!0===e)},O=r(29520);function C(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function I(e){return e&&e.sensitive?"":"i"}function A(e,t){try{var r,n,i,a,s,o;return r=[],n=function e(t,r,n){var i;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,i=r.exec(e.source);i;)t.push({name:i[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(t,r):Array.isArray(t)?(i=t.map(function(t){return e(t,r,n).source}),new RegExp("(?:".concat(i.join("|"),")"),I(n))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,a=r.start,s=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,u=r.delimiter,d=r.endsWith,c="[".concat(C(void 0===d?"":d),"]|$"),h="[".concat(C(void 0===u?"/#?":u),"]"),f=void 0===a||a?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)f+=C(l(m));else{var g=C(l(m.prefix)),y=C(l(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var k="*"===m.modifier?"?":"";f+="(?:".concat(g,"((?:").concat(m.pattern,")(?:").concat(y).concat(g,"(?:").concat(m.pattern,"))*)").concat(y,")").concat(k)}else f+="(?:".concat(g,"(").concat(m.pattern,")").concat(y,")").concat(m.modifier);else{if("+"===m.modifier||"*"===m.modifier)throw TypeError('Can not repeat "'.concat(m.name,'" without a prefix and suffix'));f+="(".concat(m.pattern,")").concat(m.modifier)}else f+="(?:".concat(g).concat(y,")").concat(m.modifier)}}if(void 0===s||s)i||(f+="".concat(h,"?")),f+=r.endsWith?"(?=".concat(c,")"):"$";else{var b=e[e.length-1],_="string"==typeof b?h.indexOf(b[b.length-1])>-1:void 0===b;i||(f+="(?:".concat(h,"(?=").concat(c,"))?")),_||(f+="(?=".concat(h,"|").concat(c,")"))}return new RegExp(f,I(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",a=r+1;a<e.length;){var s=e.charCodeAt(a);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){i+=e[a++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=a;continue}if("("===n){var o=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '.concat(a));for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--o){a++;break}}else if("("===e[a]&&(o++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at ".concat(a));l+=e[a++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,a=t.delimiter,s=void 0===a?"/#?":a,o=[],l=0,u=0,d="",c=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=c(e);if(void 0!==t)return t;var n=r[u],i=n.type,a=n.index;throw TypeError("Unexpected ".concat(i," at ").concat(a,", expected ").concat(e))},f=function(){for(var e,t="";e=c("CHAR")||c("ESCAPED_CHAR");)t+=e;return t},p=function(e){for(var t=0;t<s.length;t++){var r=s[t];if(e.indexOf(r)>-1)return!0}return!1},m=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||p(r)?"[^".concat(C(s),"]+?"):"(?:(?!".concat(C(r),")[^").concat(C(s),"])+?")};u<r.length;){var g=c("CHAR"),y=c("NAME"),k=c("PATTERN");if(y||k){var b=g||"";-1===i.indexOf(b)&&(d+=b,b=""),d&&(o.push(d),d=""),o.push({name:y||l++,prefix:b,suffix:"",pattern:k||m(b),modifier:c("MODIFIER")||""});continue}var _=g||c("ESCAPED_CHAR");if(_){d+=_;continue}if(d&&(o.push(d),d=""),c("OPEN")){var b=f(),v=c("NAME")||"",w=c("PATTERN")||"",T=f();h("CLOSE"),o.push({name:v||(w?l++:""),pattern:v&&!w?m(b):w,prefix:b,suffix:T,modifier:c("MODIFIER")||""});continue}h("END")}return o}(t,n),r,n)}(e,r,t),i=r,a=t,void 0===a&&(a={}),s=a.decode,o=void 0===s?function(e){return e}:s,function(e){var t=n.exec(e);if(!t)return!1;for(var r=t[0],a=t.index,s=Object.create(null),l=1;l<t.length;l++)!function(e){if(void 0!==t[e]){var r=i[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return o(e,r)}):s[r.name]=o(t[e],r)}}(l);return{path:r,index:a,params:s}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}var P="https://api.clerk.com",R="@clerk/backend@1.25.8",x="2024-10-01",U={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count"},j={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:U.DevBrowser,Handshake:U.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason"},N={Attributes:{AuthToken:"__clerkAuthToken",AuthSignature:"__clerkAuthSignature",AuthStatus:"__clerkAuthStatus",AuthReason:"__clerkAuthReason",AuthMessage:"__clerkAuthMessage",ClerkUrl:"__clerkUrl"},Cookies:U,Headers:{AuthToken:"x-clerk-auth-token",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthReason:"x-clerk-auth-reason",AuthMessage:"x-clerk-auth-message",ClerkUrl:"x-clerk-clerk-url",EnableDebug:"x-clerk-debug",ClerkRequestData:"x-clerk-request-data",ClerkRedirectTo:"x-clerk-redirect-to",CloudFrontForwardedProto:"cloudfront-forwarded-proto",Authorization:"authorization",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",ForwardedHost:"x-forwarded-host",Accept:"accept",Referrer:"referer",UserAgent:"user-agent",Origin:"origin",Host:"host",ContentType:"content-type",SecFetchDest:"sec-fetch-dest",Location:"location",CacheControl:"cache-control"},ContentTypes:{Json:"application/json"},QueryParameters:j},q=RegExp("(?<!:)/{1,}","g");function z(...e){return e.filter(e=>e).join("/").replace(q,"/")}var M=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},J="/accountless_applications",L=class extends M{async createAccountlessApplication(){return this.request({method:"POST",path:J})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:z(J,"complete")})}},F="/allowlist_identifiers",D=class extends M{async getAllowlistIdentifierList(){return this.request({method:"GET",path:F,queryParams:{paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:F,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:z(F,e)})}},H="/clients",K=class extends M{async getClientList(e={}){return this.request({method:"GET",path:H,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:z(H,e)})}verifyClient(e){return this.request({method:"POST",path:z(H,"verify"),bodyParams:{token:e}})}},W=class extends M{async deleteDomain(e){return this.request({method:"DELETE",path:z("/domains",e)})}},$="/email_addresses",B=class extends M{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:z($,e)})}async createEmailAddress(e){return this.request({method:"POST",path:$,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z($,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:z($,e)})}},G="/invitations",V=class extends M{async getInvitationList(e={}){return this.request({method:"GET",path:G,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:G,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:z(G,e,"revoke")})}},Q="/organizations",Y=class extends M{async getOrganizationList(e){return this.request({method:"GET",path:Q,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:Q,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:z(Q,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:z(Q,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new n.fA.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:z(Q,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:z(Q,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:z(Q,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:z(Q,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(Q,t,"memberships"),queryParams:r})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z(Q,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...n}=e;return this.requireId(t),this.request({method:"PATCH",path:z(Q,t,"memberships",r),bodyParams:n})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...n}=e;return this.request({method:"PATCH",path:z(Q,t,"memberships",r,"metadata"),bodyParams:n})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:z(Q,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(Q,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z(Q,t,"invitations"),bodyParams:r})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:z(Q,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...n}=e;return this.requireId(t),this.request({method:"POST",path:z(Q,t,"invitations",r,"revoke"),bodyParams:n})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:z(Q,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:z(Q,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...n}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:z(Q,t,"domains",r),bodyParams:n})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:z(Q,t,"domains",r)})}},X="/phone_numbers",Z=class extends M{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:z(X,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:X,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(X,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:z(X,e)})}},ee="/redirect_urls",et=class extends M{async getRedirectUrlList(){return this.request({method:"GET",path:ee,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:z(ee,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:ee,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:z(ee,e)})}},er="/sessions",en=class extends M{async getSessionList(e={}){return this.request({method:"GET",path:er,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:z(er,e)})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:z(er,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:z(er,e,"verify"),bodyParams:{token:t}})}async getToken(e,t){return this.requireId(e),this.request({method:"POST",path:z(er,e,"tokens",t||"")})}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...n}=t;return this.request({method:"POST",path:z(er,e,"refresh"),bodyParams:n,queryParams:{suffixed_cookies:r}})}},ei="/sign_in_tokens",ea=class extends M{async createSignInToken(e){return this.request({method:"POST",path:ei,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:z(ei,e,"revoke")})}};(0,p._r)({packageName:"@clerk/backend"});var{isDevOrStagingUrl:es}=(0,d.RZ)(),eo="/users",el=class extends M{async getUserList(e={}){let{limit:t,offset:r,orderBy:n,...i}=e,[a,s]=await Promise.all([this.request({method:"GET",path:eo,queryParams:e}),this.getCount(i)]);return{data:a,totalCount:s}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:z(eo,e)})}async createUser(e){return this.request({method:"POST",path:eo,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(eo,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new n.fA.FormData;return r.append("file",t?.file),this.request({method:"POST",path:z(eo,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:z(eo,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:z(eo,e)})}async getCount(e={}){return this.request({method:"GET",path:z(eo,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),n=r?t:`oauth_${t}`;return r&&f("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:z(eo,e,"oauth_access_tokens",n),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:z(eo,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:z(eo,t,"organization_memberships"),queryParams:{limit:r,offset:n}})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:z(eo,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:z(eo,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:z(eo,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:z(eo,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:z(eo,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:z(eo,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:z(eo,e,"profile_image")})}},eu="/saml_connections",ed=class extends M{async getSamlConnectionList(e={}){return this.request({method:"GET",path:eu,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:eu,bodyParams:e})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:z(eu,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:z(eu,e),bodyParams:t})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:z(eu,e)})}},ec=class extends M{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}};function eh(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var ef=class e{constructor(e,t,r,n){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=n}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},ep=class e{constructor(e,t,r,n,i){this.id=e,this.identifier=t,this.createdAt=r,this.updatedAt=n,this.invitationId=i}static fromJSON(t){return new e(t.id,t.identifier,t.created_at,t.updated_at,t.invitation_id)}},em=class e{constructor(e,t,r,n,i,a,s,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=n,this.country=i,this.browserVersion=a,this.browserName=s,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},eg=class e{constructor(e,t,r,n,i,a,s,o,l,u,d,c=null){this.id=e,this.clientId=t,this.userId=r,this.status=n,this.lastActiveAt=i,this.expireAt=a,this.abandonAt=s,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=u,this.latestActivity=d,this.actor=c}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&em.fromJSON(t.latest_activity),t.actor)}},ey=class e{constructor(e,t,r,n,i,a,s,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=n,this.signUpId=i,this.lastActiveSessionId=a,this.createdAt=s,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>eg.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},ek=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},eb=class e{constructor(e,t,r,n){this.object=e,this.id=t,this.slug=r,this.deleted=n}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},e_=class e{constructor(e,t,r,n,i,a,s,o,l,u,d){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=n,this.subject=i,this.body=a,this.bodyPlain=s,this.status=o,this.slug=l,this.data=u,this.deliveredByClerk=d}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},ev=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},ew=class e{constructor(e,t,r=null,n=null,i=null,a=null,s=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=n,this.expireAt=i,this.nonce=a,this.message=s}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},eT=class e{constructor(e,t,r,n){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&ew.fromJSON(t.verification),t.linked_to.map(e=>ev.fromJSON(e)))}},eS=class e{constructor(e,t,r,n,i,a,s,o,l,u,d={},c,h){this.id=e,this.provider=t,this.identificationId=r,this.externalId=n,this.approvedScopes=i,this.emailAddress=a,this.firstName=s,this.lastName=o,this.imageUrl=l,this.username=u,this.publicMetadata=d,this.label=c,this.verification=h}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.public_metadata,t.label,t.verification&&ew.fromJSON(t.verification))}},eE=class e{constructor(e,t,r,n,i,a,s,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=n,this.updatedAt=i,this.status=a,this.url=s,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},eO={AccountlessApplication:"accountless_application",AllowlistIdentifier:"allowlist_identifier",Client:"client",Cookies:"cookies",Email:"email",EmailAddress:"email_address",Invitation:"invitation",OauthAccessToken:"oauth_access_token",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",PhoneNumber:"phone_number",RedirectUrl:"redirect_url",Session:"session",SignInToken:"sign_in_token",SmsMessage:"sms_message",User:"user",Token:"token",TotalCount:"total_count"},eC=class e{constructor(e,t,r,n={},i,a,s){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=n,this.label=i,this.scopes=a,this.tokenSecret=s}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret)}},eI=class e{constructor(e,t,r,n,i,a,s,o={},l={},u,d,c,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=n,this.hasImage=i,this.createdAt=a,this.updatedAt=s,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=u,this.adminDeleteEnabled=d,this.membersCount=c,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},eA=class e{constructor(e,t,r,n,i,a,s,o={},l={}){this.id=e,this.emailAddress=t,this.role=r,this.organizationId=n,this.createdAt=i,this.updatedAt=a,this.status=s,this.publicMetadata=o,this.privateMetadata=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.organization_id,t.created_at,t.updated_at,t.status,t.public_metadata,t.private_metadata);return r._raw=t,r}},eP=class e{constructor(e,t,r,n={},i={},a,s,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=n,this.privateMetadata=i,this.createdAt=a,this.updatedAt=s,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,eI.fromJSON(t.organization),eR.fromJSON(t.public_user_data));return r._raw=t,r}},eR=class e{constructor(e,t,r,n,i,a){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=n,this.hasImage=i,this.userId=a}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},ex=class e{constructor(e,t,r,n,i,a){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=n,this.verification=i,this.linkedTo=a}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&ew.fromJSON(t.verification),t.linked_to.map(e=>ev.fromJSON(e)))}},eU=class e{constructor(e,t,r,n){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},ej=class e{constructor(e,t,r,n,i,a,s){this.id=e,this.userId=t,this.token=r,this.status=n,this.url=i,this.createdAt=a,this.updatedAt=s}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},eN=class e{constructor(e,t,r,n,i,a,s){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=n,this.status=i,this.phoneNumberId=a,this.data=s}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},eq=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},ez=class e{constructor(e,t,r,n,i,a,s,o,l,u){this.id=e,this.name=t,this.domain=r,this.active=n,this.provider=i,this.syncUserAttributes=a,this.allowSubdomains=s,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=u}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},eM=class e{constructor(e,t,r,n,i,a,s,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=n,this.emailAddress=i,this.firstName=a,this.lastName=s,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&ew.fromJSON(t.verification),t.saml_connection&&ez.fromJSON(t.saml_connection))}},eJ=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&ew.fromJSON(t.verification))}},eL=class e{constructor(e,t,r,n,i,a,s,o,l,u,d,c,h,f,p,m,g,y,k,b={},_={},v={},w=[],T=[],S=[],E=[],O=[],C,I,A=null,P,R){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=n,this.twoFactorEnabled=i,this.banned=a,this.locked=s,this.createdAt=o,this.updatedAt=l,this.imageUrl=u,this.hasImage=d,this.primaryEmailAddressId=c,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=f,this.lastSignInAt=p,this.externalId=m,this.username=g,this.firstName=y,this.lastName=k,this.publicMetadata=b,this.privateMetadata=_,this.unsafeMetadata=v,this.emailAddresses=w,this.phoneNumbers=T,this.web3Wallets=S,this.externalAccounts=E,this.samlAccounts=O,this.lastActiveAt=C,this.createOrganizationEnabled=I,this.createOrganizationsLimit=A,this.deleteSelfEnabled=P,this.legalAcceptedAt=R,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>eT.fromJSON(e)),(t.phone_numbers||[]).map(e=>ex.fromJSON(e)),(t.web3_wallets||[]).map(e=>eJ.fromJSON(e)),(t.external_accounts||[]).map(e=>eS.fromJSON(e)),(t.saml_accounts||[]).map(e=>eM.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}};function eF(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return eb.fromJSON(e);switch(e.object){case eO.AccountlessApplication:return ef.fromJSON(e);case eO.AllowlistIdentifier:return ep.fromJSON(e);case eO.Client:return ey.fromJSON(e);case eO.Cookies:return ek.fromJSON(e);case eO.EmailAddress:return eT.fromJSON(e);case eO.Email:return e_.fromJSON(e);case eO.Invitation:return eE.fromJSON(e);case eO.OauthAccessToken:return eC.fromJSON(e);case eO.Organization:return eI.fromJSON(e);case eO.OrganizationInvitation:return eA.fromJSON(e);case eO.OrganizationMembership:return eP.fromJSON(e);case eO.PhoneNumber:return ex.fromJSON(e);case eO.RedirectUrl:return eU.fromJSON(e);case eO.SignInToken:return ej.fromJSON(e);case eO.Session:return eg.fromJSON(e);case eO.SmsMessage:return eN.fromJSON(e);case eO.Token:return eq.fromJSON(e);case eO.TotalCount:return e.total_count;case eO.User:return eL.fromJSON(e);default:return e}}function eD(e){var t;return t=async t=>{let r,{secretKey:i,requireSecretKey:a=!0,apiUrl:s=P,apiVersion:o="v1",userAgent:l=R}=e,{path:u,method:d,queryParams:c,headerParams:h,bodyParams:f,formData:p}=t;a&&eh(i);let g=new URL(z(s,o,u));if(c)for(let[e,t]of Object.entries(m({...c})))t&&[t].flat().forEach(t=>g.searchParams.append(e,t));let y={"Clerk-API-Version":x,"User-Agent":l,...h};i&&(y.Authorization=`Bearer ${i}`);try{var k;if(p)r=await n.fA.fetch(g.href,{method:d,headers:y,body:p});else{y["Content-Type"]="application/json";let e="GET"!==d&&f&&Object.keys(f).length>0?{body:JSON.stringify(m(f,{deep:!1}))}:null;r=await n.fA.fetch(g.href,{method:d,headers:y,...e})}let e=r?.headers&&r.headers?.get(N.Headers.ContentType)===N.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:eK(t),status:r?.status,statusText:r?.statusText,clerkTraceId:eH(t,r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>eF(e))}:(k=t)&&"object"==typeof k&&"data"in k&&Array.isArray(k.data)&&void 0!==k.data?{data:t.data.map(e=>eF(e)),totalCount:t.total_count}:{data:eF(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:eH(e,r?.headers)};return{data:null,errors:eK(e),status:r?.status,statusText:r?.statusText,clerkTraceId:eH(e,r?.headers)}}},async(...e)=>{let{data:r,errors:n,totalCount:i,status:a,statusText:s,clerkTraceId:o}=await t(...e);if(n){let e=new p.LR(s||"",{data:[],status:a,clerkTraceId:o});throw e.errors=n,e}return void 0!==i?{data:r,totalCount:i}:r}}function eH(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function eK(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(p.u$):[]}return[]}function eW(e){let t=eD(e);return{__experimental_accountlessApplications:new L(eD({...e,requireSecretKey:!1})),allowlistIdentifiers:new D(t),clients:new K(t),emailAddresses:new B(t),invitations:new V(t),organizations:new Y(t),phoneNumbers:new Z(t),redirectUrls:new et(t),sessions:new en(t),signInTokens:new ea(t),users:new el(t),domains:new W(t),samlConnections:new ed(t),testingTokens:new ec(t)}}var e$=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}},eB=e=>{let{fetcher:t,sessionToken:r,sessionId:n}=e||{};return async(e={})=>n?e.template?t(n,e.template):r:null},eG={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},eV={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",UnexpectedError:"unexpected-error"};function eQ(e,t,r=new Headers,n){let i=function(e,t,r){let{act:n,sid:i,org_id:a,org_role:s,org_slug:o,org_permissions:l,sub:u,fva:d,sts:c}=r,h=eW(e),f=eB({sessionId:i,sessionToken:t,fetcher:async(...e)=>(await h.sessions.getToken(...e)).jwt}),p=d??null;return{actor:n,sessionClaims:r,sessionId:i,sessionStatus:c??null,userId:u,orgId:a,orgRole:s,orgSlug:o,orgPermissions:l,factorVerificationAge:p,getToken:f,has:E({orgId:a,orgRole:s,orgPermissions:l,userId:u,factorVerificationAge:p}),debug:e$({...e,sessionToken:t})}}(e,n,t);return{status:eG.SignedIn,reason:null,message:null,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!0,toAuth:()=>i,headers:r,token:n}}function eY(e,t,r="",n=new Headers){return eX({status:eG.SignedOut,reason:t,message:r,proxyUrl:e.proxyUrl||"",publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>({sessionClaims:null,sessionId:null,sessionStatus:null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:e$({...e,status:eG.SignedOut,reason:t,message:r})}),token:null})}var eX=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(N.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(N.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(N.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},eZ=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},e0=(...e)=>new eZ(...e),e1=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(N.Headers.ForwardedProto),n=e.headers.get(N.Headers.ForwardedHost),i=e.headers.get(N.Headers.Host),a=t.protocol,s=this.getFirstValueFromHeader(n)??i,o=this.getFirstValueFromHeader(r)??a?.replace(/[:/]/,""),l=s&&o?`${o}://${s}`:t.origin;return l===t.origin?e0(t):e0(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,O.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},e2=(...e)=>e[0]instanceof e1?e[0]:new e1(...e),e4={},e6=0;function e5(e,t=!0){e4[e.kid]=e,e6=t?Date.now():-1}var e9="local";function e3(e){if(!e4[e9]){if(!e)throw new i.zF({action:i.z.SetClerkJWTKey,message:"Missing local JWK.",reason:i.jn.LocalJWKMissing});e5({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return e4[e9]}async function e8({secretKey:e,apiUrl:t=P,apiVersion:r="v1",kid:n,skipJwksCache:a}){if(a||function(){if(-1===e6)return!1;let e=Date.now()-e6>=3e5;return e&&(e4={}),e}()||!e4[n]){if(!e)throw new i.zF({action:i.z.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:i.jn.RemoteJWKFailedToLoad});let{keys:n}=await u(()=>e7(t,e,r));if(!n||!n.length)throw new i.zF({action:i.z.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:i.jn.RemoteJWKFailedToLoad});n.forEach(e=>e5(e))}let s=e4[n];if(!s){let e=Object.values(e4).map(e=>e.kid).sort().join(", ");throw new i.zF({action:`Go to your Dashboard and validate your secret and public keys are correct. ${i.z.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${n}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:i.jn.JWKKidMismatch})}return s}async function e7(e,t,r){if(!t)throw new i.zF({action:i.z.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:i.jn.RemoteJWKFailedToLoad});let a=new URL(e);a.pathname=z(a.pathname,r,"/jwks");let s=await n.fA.fetch(a.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":x,"Content-Type":"application/json","User-Agent":R}});if(!s.ok){let e=await s.json(),t=te(e?.errors,i.qu.InvalidSecretKey);if(t){let e=i.jn.InvalidSecretKey;throw new i.zF({action:i.z.ContactSupport,message:t.message,reason:e})}throw new i.zF({action:i.z.ContactSupport,message:`Error loading Clerk JWKS from ${a.href} with code=${s.status}`,reason:i.jn.RemoteJWKFailedToLoad})}return s.json()}var te=(e,t)=>e?e.find(e=>e.code===t):null;async function tt(e,t){let{data:r,errors:a}=(0,n.iU)(e);if(a)return{errors:a};let{header:s}=r,{kid:o}=s;try{let r;if(t.jwtKey)r=e3(t.jwtKey);else{if(!t.secretKey)return{errors:[new i.zF({action:i.z.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:i.jn.JWKFailedToResolve})]};r=await e8({...t,kid:o})}return await (0,n.J0)(e,{...t,key:r})}catch(e){return{errors:[e]}}}var tr=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.sessionTokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(N.Cookies.ClientUat),t=this.getCookie(N.Cookies.ClientUat),r=this.getSuffixedCookie(N.Cookies.Session)||"",i=this.getCookie(N.Cookies.Session)||"";if(i&&!this.tokenHasIssuer(i))return!1;if(i&&!this.tokenBelongsToInstance(i))return!0;if(!e&&!r)return!1;let{data:a}=(0,n.iU)(i),s=a?.payload.iat||0,{data:o}=(0,n.iU)(r),l=o?.payload.iat||0;if("0"!==e&&"0"!==t&&s>l||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(o);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}initPublishableKeyValues(e){var t;t=e.publishableKey,(0,d.q5)(t,{fatal:!0}),this.publishableKey=e.publishableKey;let r=(0,d.q5)(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain});this.instanceType=r.instanceType,this.frontendApi=r.frontendApi}initHeaderValues(){this.sessionTokenInHeader=this.parseAuthorizationHeader(this.getHeader(N.Headers.Authorization)),this.origin=this.getHeader(N.Headers.Origin),this.host=this.getHeader(N.Headers.Host),this.forwardedHost=this.getHeader(N.Headers.ForwardedHost),this.forwardedProto=this.getHeader(N.Headers.CloudFrontForwardedProto)||this.getHeader(N.Headers.ForwardedProto),this.referrer=this.getHeader(N.Headers.Referrer),this.userAgent=this.getHeader(N.Headers.UserAgent),this.secFetchDest=this.getHeader(N.Headers.SecFetchDest),this.accept=this.getHeader(N.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(N.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(N.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(N.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(N.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(N.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(N.QueryParameters.Handshake)||this.getCookie(N.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(N.Cookies.RedirectCount))||0}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie((0,d.ky)(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=(0,n.iU)(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=(0,n.iU)(e);if(r)return!1;let i=t.payload.iss.replace(/https?:\/\//gi,"");return this.frontendApi===i}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},tn=async(e,t)=>new tr(t.publishableKey?await (0,d.qS)(t.publishableKey,n.fA.crypto.subtle):"",e,t),ti=e=>e.split(";")[0]?.split("=")[0],ta=e=>e.split(";")[0]?.split("=")[1];async function ts(e,{key:t}){let{data:r,errors:a}=(0,n.iU)(e);if(a)throw a[0];let{header:s,payload:o}=r,{typ:l,alg:u}=s;(0,n.qf)(l),(0,n.l3)(u);let{data:d,errors:c}=await (0,n.nk)(r,t);if(c)throw new i.zF({reason:i.jn.TokenVerificationFailed,message:`Error verifying handshake token. ${c[0]}`});if(!d)throw new i.zF({reason:i.jn.TokenInvalidSignature,message:"Handshake signature is invalid."});return o}async function to(e,t){let r,{secretKey:a,apiUrl:s,apiVersion:o,jwksCacheTtlInMs:l,jwtKey:u,skipJwksCache:d}=t,{data:c,errors:h}=(0,n.iU)(e);if(h)throw h[0];let{kid:f}=c.header;if(u)r=e3(u);else if(a)r=await e8({secretKey:a,apiUrl:s,apiVersion:o,kid:f,jwksCacheTtlInMs:l,skipJwksCache:d});else throw new i.zF({action:i.z.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:i.jn.JWKFailedToResolve});return await ts(e,{key:r})}var tl={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};async function tu(e,t){let r=await tn(e2(e),t);if(eh(r.secretKey),r.isSatellite){var a=r.signInUrl,s=r.secretKey;if(!a&&(0,d.mC)(s))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let o=function(e){let t=null;if(e?.personalAccountPatterns)try{t=A(e.personalAccountPatterns)}catch(t){throw Error(`Invalid personal account pattern "${e.personalAccountPatterns}": "${t}"`)}let r=null;if(e?.organizationPatterns)try{r=A(e.organizationPatterns)}catch(t){throw Error(`Clerk: Invalid organization pattern "${e.organizationPatterns}": "${t}"`)}return{OrganizationMatcher:r,PersonalAccountMatcher:t}}(t.organizationSyncOptions);async function l(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=(await to(r.handshakeToken,r)).handshake,n="";if(t.forEach(t=>{e.append("Set-Cookie",t),ti(t).startsWith(N.Cookies.Session)&&(n=ta(t))}),"development"===r.instanceType){let t=new URL(r.clerkUrl);t.searchParams.delete(N.QueryParameters.Handshake),t.searchParams.delete(N.QueryParameters.HandshakeHelp),e.append(N.Headers.Location,t.toString()),e.set(N.Headers.CacheControl,"no-store")}if(""===n)return eY(r,eV.SessionTokenMissing,"",e);let{data:a,errors:[s]=[]}=await tt(n,r);if(a)return eQ(r,a,e,n);if("development"===r.instanceType&&(s?.reason===i.jn.TokenExpired||s?.reason===i.jn.TokenNotActiveYet||s?.reason===i.jn.TokenIatInTheFuture)){s.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${s.getFullMessage()}`);let{data:t,errors:[i]=[]}=await tt(n,{...r,clockSkewInMs:864e5});if(t)return eQ(r,t,e,n);throw Error(i?.message||"Clerk: Handshake retry failed.")}throw Error(s?.message||"Clerk: Handshake failed.")}async function u(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:tl.MissingApiClient}}};let{sessionToken:i,refreshTokenInCookie:a}=r;if(!i)return{data:null,error:{message:"Session token must be provided.",cause:{reason:tl.MissingSessionToken}}};if(!a)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:tl.MissingRefreshToken}}};let{data:s,errors:o}=(0,n.iU)(i);if(!s||o)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:tl.ExpiredSessionTokenDecodeFailed,errors:o}}};if(!s?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:tl.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(s.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:i||"",refresh_token:a||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:tl.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:tl.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function c(e){let{data:t,error:r}=await u(e);if(!t||0===t.length)return{data:null,error:r};let n=new Headers,i="";t.forEach(e=>{n.append("Set-Cookie",e),ti(e).startsWith(N.Cookies.Session)&&(i=ta(e))});let{data:a,errors:s}=await tt(i,e);return s?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:tl.InvalidSessionToken,errors:s}}}:{data:{jwtPayload:a,sessionToken:i,headers:n},error:null}}function h(e,n,i,a){if(function(e){let{accept:t,secFetchDest:r}=e;return!!("document"===r||"iframe"===r||!r&&t?.startsWith("text/html"))}(e)){let s=a??function({handshakeReason:e}){let n=function(e){let t=new URL(e);return t.searchParams.delete(N.QueryParameters.DevBrowser),t.searchParams.delete(N.QueryParameters.LegacyDevBrowser),t}(r.clerkUrl),i=r.frontendApi.replace(/http(s)?:\/\//,""),a=new URL(`https://${i}/v1/client/handshake`);a.searchParams.append("redirect_url",n?.href||""),a.searchParams.append(N.QueryParameters.SuffixedCookies,r.usesSuffixedCookies().toString()),a.searchParams.append(N.QueryParameters.HandshakeReason,e),"development"===r.instanceType&&r.devBrowserToken&&a.searchParams.append(N.QueryParameters.DevBrowser,r.devBrowserToken);let s=tc(r.clerkUrl,t.organizationSyncOptions,o);return s&&(function(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t})(s).forEach((e,t)=>{a.searchParams.append(t,e)}),new Headers({[N.Headers.Location]:a.href})}({handshakeReason:n});return(s.get(N.Headers.Location)&&s.set(N.Headers.CacheControl,"no-store"),function(e){if(3===r.handshakeRedirectLoopCounter)return!0;let t=r.handshakeRedirectLoopCounter+1,n=N.Cookies.RedirectCount;return e.append("Set-Cookie",`${n}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}(s))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),eY(e,n,i)):function(e,t,r="",n){return eX({status:eG.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,headers:n,toAuth:()=>null,token:null})}(e,n,i,s)}return eY(e,n,i)}async function f(){let{sessionTokenInHeader:e}=r;try{let{data:t,errors:n}=await tt(e,r);if(n)throw n[0];return eQ(r,t,void 0,e)}catch(e){return m(e,"header")}}async function p(){let e=r.clientUat,a=!!r.sessionTokenInCookie,s=!!r.devBrowserToken;if(r.handshakeToken)try{return await l()}catch(e){if(e instanceof i.zF&&"development"===r.instanceType){if(e.reason===i.jn.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(N.QueryParameters.DevBrowser))return h(r,eV.DevBrowserSync,"");let u=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&u)return h(r,eV.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&u&&!r.clerkUrl.searchParams.has(N.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(N.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[N.Headers.Location]:e.toString()});return h(r,eV.SatelliteCookieNeedsSyncing,"",t)}let d=new URL(r.clerkUrl).searchParams.get(N.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&d){let e=new URL(d);r.devBrowserToken&&e.searchParams.append(N.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(N.QueryParameters.ClerkSynced,"true");let t=new Headers({[N.Headers.Location]:e.toString()});return h(r,eV.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!s)return h(r,eV.DevBrowserMissing,"");if(!e&&!a)return eY(r,eV.SessionTokenAndUATMissing,"");if(!e&&a)return h(r,eV.SessionTokenWithoutClientUAT,"");if(e&&!a)return h(r,eV.ClientUATWithoutSessionToken,"");let{data:c,errors:f}=(0,n.iU)(r.sessionTokenInCookie);if(f)return m(f[0],"cookie");if(c.payload.iat<r.clientUat)return h(r,eV.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:n}=await tt(r.sessionTokenInCookie,r);if(n)throw n[0];let i=eQ(r,e,void 0,r.sessionTokenInCookie),a=function(e,r){let n=tc(e.clerkUrl,t.organizationSyncOptions,o);if(!n)return null;let i=!1;if("organization"===n.type&&(n.organizationSlug&&n.organizationSlug!==r.orgSlug&&(i=!0),n.organizationId&&n.organizationId!==r.orgId&&(i=!0)),"personalAccount"===n.type&&r.orgId&&(i=!0),!i)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let a=h(e,eV.ActiveOrganizationMismatch,"");return"handshake"!==a.status?null:a}(r,i.toAuth());if(a)return a;return i}catch(e){return m(e,"cookie")}}async function m(t,n){let a;if(!(t instanceof i.zF))return eY(r,eV.UnexpectedError);if(t.reason===i.jn.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await c(r);if(e)return eQ(r,e.jwtPayload,e.headers,e.sessionToken);a=t?.cause?.reason?t.cause.reason:tl.UnexpectedSDKError}else a="GET"!==e.method?tl.NonEligibleNonGet:r.refreshTokenInCookie?null:tl.NonEligibleNoCookie;return(t.tokenCarrier=n,[i.jn.TokenExpired,i.jn.TokenNotActiveYet,i.jn.TokenIatInTheFuture].includes(t.reason))?h(r,th({tokenError:t.reason,refreshError:a}),t.getFullMessage()):eY(r,t.reason,t.getFullMessage())}return r.sessionTokenInHeader?f():p()}var td=e=>{let{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:a,isSatellite:s,domain:o}=e;return{isSignedIn:t,proxyUrl:r,reason:n,message:i,publishableKey:a,isSatellite:s,domain:o}};function tc(e,t,r){if(!t)return null;if(r.OrganizationMatcher){let n;try{n=r.OrganizationMatcher(e.pathname)}catch(e){return console.error(`Clerk: Failed to apply organization pattern "${t.organizationPatterns}" to a path`,e),null}if(n&&"params"in n){let e=n.params;if("id"in e&&"string"==typeof e.id)return{type:"organization",organizationId:e.id};if("slug"in e&&"string"==typeof e.slug)return{type:"organization",organizationSlug:e.slug};console.warn("Clerk: Detected an organization pattern match, but no organization ID or slug was found in the URL. Does the pattern include `:id` or `:slug`?")}}if(r.PersonalAccountMatcher){let n;try{n=r.PersonalAccountMatcher(e.pathname)}catch(e){return console.error(`Failed to apply personal account pattern "${t.personalAccountPatterns}" to a path`,e),null}if(n)return{type:"personalAccount"}}return null}var th=({tokenError:e,refreshError:t})=>{switch(e){case i.jn.TokenExpired:return`${eV.SessionTokenExpired}-refresh-${t}`;case i.jn.TokenNotActiveYet:return eV.SessionTokenNBF;case i.jn.TokenIatInTheFuture:return eV.SessionTokenIatInTheFuture;default:return eV.UnexpectedError}};function tf(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}var tp={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};function tm(e){let t=tf(tp,e.options),r=e.apiClient;return{authenticateRequest:(e,n={})=>{let{apiUrl:i,apiVersion:a}=t,s=tf(t,n);return tu(e,{...n,...s,apiUrl:i,apiVersion:a,apiClient:r})},debugRequestState:td}}},38916:(e,t,r)=>{r.r(t),r.d(t,{snakeCase:()=>l});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function i(e){return e.toLowerCase()}Object.create,"function"==typeof SuppressedError&&SuppressedError;var a=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],s=/[^A-Z0-9]+/gi;function o(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce(function(e,t){return e.replace(t,r)},e)}function l(e,t){var r;return void 0===t&&(t={}),void 0===(r=n({delimiter:"_"},t))&&(r={}),function(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=t.stripRegexp,l=t.transform,u=t.delimiter,d=o(o(e,void 0===r?a:r,"$1\0$2"),void 0===n?s:n,"\0"),c=0,h=d.length;"\0"===d.charAt(c);)c++;for(;"\0"===d.charAt(h-1);)h--;return d.slice(c,h).split("\0").map(void 0===l?i:l).join(void 0===u?" ":u)}(e,n({delimiter:"."},r))}},39043:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},48134:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return s},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return o}});let n=r(27205),i=r(79999),a=r(19121).actionAsyncStorage;function s(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let a=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",a}function o(e,t){var r;throw null!=t||(t=(null==a||null==(r=a.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),s(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=i.RedirectType.replace),s(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,s.isNextRouterError)(t)||(0,a.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(99397),i=r(77426),a=r(39043),s=r(54519),o=r(88740),l=r(91448);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54519:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(23457),i=r(79999);function a(e){return(0,i.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59144:(e,t,r)=>{r.d(t,{RZ:()=>u,qS:()=>c,ky:()=>h,mC:()=>d,q5:()=>o});var n=r(63586),i=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,a=r(26124),s="pk_live_";function o(e,t={}){if(!(e=e||"")||!l(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!l(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(s)?"production":"development",i=(0,n.y)(e.split("_")[2]);return i=i.slice(0,-1),t.proxyUrl?i=t.proxyUrl:"development"!==r&&t.domain&&(i=`clerk.${t.domain}`),{instanceType:r,frontendApi:i}}function l(e=""){try{let t=e.startsWith(s)||e.startsWith("pk_test_"),r=(0,n.y)(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function u(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=a.gE.some(e=>r.endsWith(e)),e.set(r,n)),n}}}function d(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function c(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return i(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var h=(e,t)=>`${e}_${t}`},63586:(e,t,r)=>{r.d(t,{y:()=>n});var n=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e},67814:(e,t,r)=>{r.d(t,{OV:()=>c,S7:()=>u,VK:()=>d,jq:()=>h});var n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,o=e=>{throw TypeError(e)},l=(e,t,r)=>t.has(e)||o("Cannot "+r),u=(e,t,r)=>(l(e,t,"read from private field"),r?r.call(e):t.get(e)),d=(e,t,r)=>t.has(e)?o("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),c=(e,t,r,n)=>(l(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),h=(e,t,r)=>(l(e,t,"access private method"),r)},72016:(e,t,r)=>{r.d(t,{b_:()=>n.b_});var n=r(21254);r(67814)},72081:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(23457).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76099:(e,t,r)=>{r.d(t,{zz:()=>i});var n=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function i(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}n(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),n(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},76849:(e,t,r)=>{let n=r(85805),{snakeCase:i}=r(38916),a={}.constructor;e.exports=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==a))throw Error("obj must be array of plain objects")}else if(e.constructor!==a)throw Error("obj must be an plain object");return n(e,function(e,r){var n,a,s,o,l;return[(n=t.exclude,a=e,n.some(function(e){return"string"==typeof e?e===a:e.test(a)}))?e:i(e,t.parsingOptions),r,(s=e,o=r,(l=t).shouldRecurse?{shouldRecurse:l.shouldRecurse(s,o)}:void 0)]},t=Object.assign({deep:!0,exclude:[],parsingOptions:{}},t))}},77426:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},77875:(e,t,r)=>{r.d(t,{l3:()=>y,qf:()=>g,iU:()=>S,nk:()=>T,fA:()=>o,J0:()=>E});var n=r(26604),i=r(77598),a=r(63586);r(67814);var s=fetch.bind(globalThis),o={crypto:i.webcrypto,get fetch(){return s},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},l={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let n=e.length;for(;"="===e[n-1];)if(--n,!r.loose&&!((e.length-n)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(n*t.bits/8|0),a=0,s=0,o=0;for(let r=0;r<n;++r){let n=t.codes[e[r]];if(void 0===n)throw SyntaxError("Invalid character "+e[r]);s=s<<t.bits|n,(a+=t.bits)>=8&&(a-=8,i[o++]=255&s>>a)}if(a>=t.bits||255&s<<8-a)throw SyntaxError("Unexpected end of data");return i})(e,u,t)},u={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},d={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},c="RSASSA-PKCS1-v1_5",h={RS256:c,RS384:c,RS512:c},f=Object.keys(d),p=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),m=(e,t)=>{let r=[t].flat().filter(e=>!!e),i=[e].flat().filter(e=>!!e);if(r.length>0&&i.length>0){if("string"==typeof e){if(!r.includes(e))throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(p(e)&&!e.some(e=>r.includes(e)))throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},g=e=>{if(void 0!==e&&"JWT"!==e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},y=e=>{if(!f.includes(e))throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${f}.`})},k=e=>{if("string"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},b=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new n.zF({reason:n.jn.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},_=(e,t)=>{if("number"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()<=r.getTime()-t)throw new n.zF({reason:n.jn.TokenExpired,message:`JWT is expired. Expiry date: ${i.toUTCString()}, Current date: ${r.toUTCString()}.`})},v=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new n.zF({reason:n.jn.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})},w=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),i=new Date(0);if(i.setUTCSeconds(e),i.getTime()>r.getTime()+t)throw new n.zF({reason:n.jn.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${i.toUTCString()}; Current date: ${r.toUTCString()};`})};async function T(e,t){let{header:r,signature:i,raw:s}=e,l=new TextEncoder().encode([s.header,s.payload].join(".")),u=function(e){let t=d[e],r=h[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${f.join(",")}.`);return{hash:{name:d[e]},name:h[e]}}(r.alg);try{let e=await function(e,t,r){if("object"==typeof e)return o.crypto.subtle.importKey("jwk",e,t,!1,[r]);let n=function(e){let t=e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,""),r=(0,a.y)(t),n=new Uint8Array(new ArrayBuffer(r.length));for(let e=0,t=r.length;e<t;e++)n[e]=r.charCodeAt(e);return n}(e),i="sign"===r?"pkcs8":"spki";return o.crypto.subtle.importKey(i,n,t,!1,[r])}(t,u,"verify");return{data:await o.crypto.subtle.verify(u.name,e,i,l)}}catch(e){return{errors:[new n.zF({reason:n.jn.TokenInvalidSignature,message:e?.message})]}}}function S(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new n.zF({reason:n.jn.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,i,a]=t,s=new TextDecoder,o=JSON.parse(s.decode(l.parse(r,{loose:!0}))),u=JSON.parse(s.decode(l.parse(i,{loose:!0})));return{data:{header:o,payload:u,signature:l.parse(a,{loose:!0}),raw:{header:r,payload:i,signature:a,text:e}}}}async function E(e,t){let{audience:r,authorizedParties:i,clockSkewInMs:a,key:s}=t,o=a||5e3,{data:l,errors:u}=S(e);if(u)return{errors:u};let{header:d,payload:c}=l;try{let{typ:e,alg:t}=d;g(e),y(t);let{azp:n,sub:a,aud:s,iat:l,exp:u,nbf:h}=c;k(a),m([s],[r]),b(n,i),_(u,o),v(h,o),w(l,o)}catch(e){return{errors:[e]}}let{data:h,errors:f}=await T(l,s);return f?{errors:[new n.zF({action:n.z.EnsureClerkJWT,reason:n.jn.TokenVerificationFailed,message:`Error verifying JWT signature. ${f[0]}`})]}:h?{data:c}:{errors:[new n.zF({reason:n.jn.TokenInvalidSignature,message:"JWT signature is invalid."})]}}},79999:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return i},RedirectType:function(){return a},isRedirectError:function(){return s}});let n=r(27205),i="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,s=t.slice(2,-2).join(";"),o=Number(t.at(-2));return r===i&&("replace"===a||"push"===a)&&"string"==typeof s&&!isNaN(o)&&o in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80860:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(54047).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84041:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return i.RedirectType},forbidden:function(){return s.forbidden},notFound:function(){return a.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(48134),i=r(79999),a=r(33186),s=r(72081),o=r(13932),l=r(80860);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},85805:e=>{let t=e=>"object"==typeof e&&null!==e,r=Symbol("skip"),n=e=>t(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date),i=(e,t,a,s=new WeakMap)=>{if(a={deep:!1,target:{},...a},s.has(e))return s.get(e);s.set(e,a.target);let{target:o}=a;delete a.target;let l=e=>e.map(e=>n(e)?i(e,t,a,s):e);if(Array.isArray(e))return l(e);for(let[u,d]of Object.entries(e)){let c=t(u,d,e);if(c===r)continue;let[h,f,{shouldRecurse:p=!0}={}]=c;"__proto__"!==h&&(a.deep&&p&&n(f)&&(f=Array.isArray(f)?l(f):i(f,t,a,s)),o[h]=f)}return o};e.exports=(e,r,n)=>{if(!t(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);return i(e,r,n)},e.exports.mapObjectSkip=r}};
//# sourceMappingURL=9616.js.map