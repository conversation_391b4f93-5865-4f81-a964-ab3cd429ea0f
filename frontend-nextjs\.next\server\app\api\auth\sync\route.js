try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="45ac3614-dec2-49db-ac20-1fae3b53d87f",e._sentryDebugIdIdentifier="sentry-dbid-45ac3614-dec2-49db-ac20-1fae3b53d87f")}catch(e){}"use strict";(()=>{var e={};e.id=210,e.ids=[210],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21638:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>w,serverHooks:()=>E,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>T});var s={};t.r(s),t.d(s,{DELETE:()=>g,GET:()=>q,HEAD:()=>v,OPTIONS:()=>k,PATCH:()=>f,POST:()=>h,PUT:()=>m});var o=t(86047),n=t(85544),i=t(36135),a=t(63033),p=t(53547),d=t(54360),u=t(19761);let c=(0,p.ZA)(async(e,r)=>{try{let r=(0,d.o)(e),t=await r.syncUser({clerkId:e.clerkId,email:e.email,firstName:e.firstName,lastName:e.lastName});return console.log("User sync completed:",t),(0,p.$y)({message:"User sync completed",user:{clerkId:e.clerkId,email:e.email,firstName:e.firstName,lastName:e.lastName,role:t.role,payloadUserId:t.id,lastLogin:new Date().toISOString()}})}catch(e){return console.error("Error syncing user:",e),(0,p.WX)("Failed to sync user")}}),l={...a},x="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;function y(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=x?.getStore();o=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/auth/sync",headers:o}).apply(t,s)}})}let q=y(void 0,"GET"),h=y(c,"POST"),m=y(void 0,"PUT"),f=y(void 0,"PATCH"),g=y(void 0,"DELETE"),v=y(void 0,"HEAD"),k=y(void 0,"OPTIONS"),w=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/sync/route",pathname:"/api/auth/sync",filename:"route",bundlePath:"app/api/auth/sync/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\auth\\sync\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:T,serverHooks:E}=w;function P(){return(0,i.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:T})}},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[55,3738,1950,5886,9615,125],()=>t(21638));module.exports=s})();
//# sourceMappingURL=route.js.map